﻿using BIMsmithMarket.Domain.Models;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace BIMsmithMarket.Core.Helpers
{
    public static class IISLogsParser
    {
        public static dynamic GetStatsFromFile(string path)
        {
            IISLogRecord[] records = ParseIISLogFile(path);

            double averageTime = records.Average(x => x.TimeTaken);
            int mostExpensiveCount = 1000;
            IISLogRecord[] topExpensiveRequests = records.OrderByDescending(x => x.TimeTaken).Take(mostExpensiveCount).ToArray();
            var grouppedRequests = records.GroupBy(x => x.Uri).Select(x => new
            {
                Uri = x.Key,
                Count = x.Count(),
                AverageTime = x.Average(c => c.TimeTaken),
                MinTime = x.Min(c => c.TimeTaken),
                MaxTime = x.Max(c => c.TimeTaken)
            });

            var mostPopularRequests = grouppedRequests.OrderByDescending(x => x.Count).ToArray();
            var mostExpensiveRequests = grouppedRequests.OrderByDescending(x => x.AverageTime).ToArray();

            return new
            {
                totalCount = records.Length,
                averageTime,
                topExpensiveRequests,
                mostPopularRequests,
                mostExpensiveRequests
            };
        }

        #region private methods
        private static IISLogRecord[] ParseIISLogFile(string path)
        {
            List<string> fileLines = new List<string>(File.ReadAllLines(path));
            List<IISLogRecord> records = new List<IISLogRecord>();
            int headerStringsCount = 4;
            fileLines.RemoveRange(0, headerStringsCount);

            foreach (string line in fileLines)
            {
                string[] parts = line.Split(' ');
                int index = 0;

                if (parts[index].StartsWith("#"))
                    continue;

                IISLogRecord record = new IISLogRecord
                {
                    Date = parts[index++],
                    Time = parts[index++],
                    ServerIp = parts[index++],
                    HttpVerb = parts[index++],
                    Uri = parts[index++],
                    Query = parts[index++],
                    Port = parts[index++],
                    Username = parts[index++],
                    ClientIp = parts[index++],
                    UserAgent = parts[index++],
                    Referrer = parts[index++],
                    StatusCode = parts[index++],
                    SubStatusCode = parts[index++],
                    Win32StatusCode = parts[index++],
                    TimeTaken = int.Parse(parts[index++])
                };
                records.Add(record);
            }

            return records.ToArray();
        }
        #endregion
    }
}