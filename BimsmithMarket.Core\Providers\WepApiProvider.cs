﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Domain.Dto;
using System;
using System.Net.Http;
using System.Threading.Tasks;

namespace BIMsmithMarket.Core.Providers
{
    public static class WepApiProvider
    {
        public static async Task<bool> IsActiveUrlAsync(this string url)
        {
            if (string.IsNullOrWhiteSpace(url))
                return false;

            try
            {
                HttpResponseMessage result = await RequestResourceAsync(url);
                return result.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public static bool IsActiveUrl(this string url)
        {
            if (string.IsNullOrWhiteSpace(url))
                return false;

            try
            {
                HttpResponseMessage result = RequestResource(url);
                return result.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public static async Task<bool> IsActiveImageUrlAsync(this string url)
        {
            try
            {
                HttpResponseMessage result = await RequestResourceAsync(url);
                string mediaType = result?.Content?.Headers?.ContentType?.MediaType;
                return !string.IsNullOrWhiteSpace(mediaType) ? mediaType.ToLower().Contains("image") : false;
            }
            catch
            {
                return false;
            }
        }

        public static bool IsActiveImageUrl(this string url)
        {
            try
            {
                HttpResponseMessage result = RequestResource(url);
                string mediaType = result?.Content?.Headers?.ContentType?.MediaType;
                return !string.IsNullOrWhiteSpace(mediaType) ? mediaType.ToLower().Contains("image") : false;
            }
            catch
            {
                return false;
            }
        }

        public static async Task<HttpResponseHeadersInfo> GetHeadersAsync(string url)
        {
            if (string.IsNullOrWhiteSpace(url))
                return new HttpResponseHeadersInfo
                {
                    MediaType = string.Empty,
                    FileName = string.Empty
                };

            try
            {
                HttpResponseMessage result = await RequestResourceAsync(url);

                return new HttpResponseHeadersInfo
                {
                    FileName = result.Content?.Headers?.ContentDisposition?.FileName?.Replace("\"", string.Empty) ?? string.Empty,
                    MediaType = result.Content?.Headers?.ContentType?.MediaType ?? string.Empty
                };
            }
            catch
            {
                return new HttpResponseHeadersInfo
                {
                    MediaType = string.Empty,
                    FileName = string.Empty
                };
            }
        }

        public static HttpResponseHeadersInfo GetHeaders(string url)
        {
            if (string.IsNullOrWhiteSpace(url))
                return new HttpResponseHeadersInfo
                {
                    MediaType = string.Empty,
                    FileName = string.Empty
                };

            try
            {
                HttpResponseMessage result = RequestResource(url);

                return new HttpResponseHeadersInfo
                {
                    FileName = result.Content?.Headers?.ContentDisposition?.FileName?.Replace("\"", string.Empty) ?? string.Empty,
                    MediaType = result.Content?.Headers?.ContentType?.MediaType ?? string.Empty
                };
            }
            catch
            {
                return new HttpResponseHeadersInfo
                {
                    MediaType = string.Empty,
                    FileName = string.Empty
                };
            }
        }

        #region
        private static async Task<HttpResponseMessage> RequestResourceAsync(string url)
        {
            HttpClient httpClient = HttpClientFactory.GetClient();
            HttpRequestMessage httpRequestMessage = CreateHttpRequestMessage(url, HttpMethod.Head);
            HttpResponseMessage result = await httpClient.SendAsync(httpRequestMessage);

            if (!result.IsSuccessStatusCode)
            {
                httpRequestMessage = CreateHttpRequestMessage(url, HttpMethod.Get);
                result = await httpClient.SendAsync(httpRequestMessage);
            }

            return result;
        }

        private static HttpResponseMessage RequestResource(string url)
        {
            HttpClient httpClient = HttpClientFactory.GetClient();
            HttpRequestMessage httpRequestMessage = CreateHttpRequestMessage(url, HttpMethod.Head);
            HttpResponseMessage result = httpClient.Send(httpRequestMessage);

            if (!result.IsSuccessStatusCode)
            {
                httpRequestMessage = CreateHttpRequestMessage(url, HttpMethod.Get);
                result = httpClient.Send(httpRequestMessage);
            }

            return result;
        }

        private static HttpRequestMessage CreateHttpRequestMessage(string url, HttpMethod httpMethod)
        {
            HttpRequestMessage httpRequestMessage = new HttpRequestMessage();
            httpRequestMessage.Headers.Add("User-Agent", ConfigurationHelper.GetValue("MarketUserAgent"));
            httpRequestMessage.Method = httpMethod;
            httpRequestMessage.RequestUri = new Uri(url);
            return httpRequestMessage;
        }
        #endregion
    }
}