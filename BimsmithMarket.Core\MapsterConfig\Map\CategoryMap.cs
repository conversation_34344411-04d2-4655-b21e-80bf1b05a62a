﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using Mapster;
using System.Linq;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public static class CategoryMap
    {
        public static void Register(TypeAdapterConfig config)
        {
            config.ForType<Category, CategoryExcelDto>()
                .Map(d => d.KeyStatNames, s => s.CategoryKeyStats.OrderBy(x => x.Order).Select(x => x.KeyStat.Name));
        }
    }
}