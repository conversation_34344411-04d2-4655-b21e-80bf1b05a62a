﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Models.ApplicationDetailModels;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;

using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Services
{
    public class DetailApplicationService : IDetailApplicationService
    {
        public async Task<dynamic> AddAsync(AddDetailApplicationViewModel model, string userId)
        {
            using (var unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                DetailApplication duplicatedDetailApplications = await unitOfWork.DetailApplicationRepository.GetAll()
                    .FirstOrDefaultAsync(x => x.VanityUrl == model.VanityUrl);

                if (duplicatedDetailApplications != null)
                    throw new ApplicationException("Vanity url is already existed");

                var detailApplication = model.Adapt<DetailApplication>();
                detailApplication.CreatedById = userId;
                detailApplication.CreatedDate = DateTime.UtcNow;

                unitOfWork.DetailApplicationRepository.Insert(detailApplication);

                await unitOfWork.SaveAsync();

                CacheHelper.ClearSpecificCache("*/api/DetailApplication/List*");

                return await GetAsync(detailApplication.Id);
            }
        }

        public async Task<dynamic> EditAsync(EditDetailApplicationViewModel model, string userId)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                DetailApplication duplicatedDetailApplications = await unitOfWork.DetailApplicationRepository.GetAll()
                    .FirstOrDefaultAsync(x => x.VanityUrl == model.VanityUrl && x.Id != model.Id);

                if (duplicatedDetailApplications != null)
                    throw new ApplicationException("Vanity url is already existed");

                DetailApplication detailApplication = await unitOfWork.DetailApplicationRepository.GetByIdAsync(model.Id);
                if (detailApplication == null)
                    throw new Exception("Detail Application Not Found");

                model.Adapt(detailApplication);
                detailApplication.ModifiedById = userId;
                detailApplication.ModifiedDate = DateTime.UtcNow;

                unitOfWork.DetailApplicationRepository.Edit(detailApplication);

                await unitOfWork.SaveAsync();

                CacheHelper.ClearSpecificCache("*/api/DetailApplication/List*");

                return await GetAsync(detailApplication.Id);
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                unitOfWork.BeginTransaction();
                DetailApplication detailApplication = await unitOfWork.DetailApplicationRepository.GetByIdAsync(id);
                if (detailApplication == null)
                    throw new Exception("Detail Application Not Found");

                var detailApplications = detailApplication.DetailDetailApplications.ToList();
                detailApplications.ForEach(x => unitOfWork.DetailDetailApplicationRepository.Delete(x));

                unitOfWork.DetailApplicationRepository.Delete(detailApplication);

                await unitOfWork.SaveAsync();
                unitOfWork.CommitTransaction();

                CacheHelper.ClearSpecificCache("*/api/DetailApplication/List*");

                return true;
            }
        }

        public async Task<dynamic> GetAsync(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                DetailApplication detailApplication = await unitOfWork.DetailApplicationRepository.GetByIdAsync(id);

                if (detailApplication == null) throw new Exception("Detail Application not found");

                return new
                {
                    id = detailApplication.Id,
                    name = detailApplication.Name,
                    header = detailApplication.Header,
                    pageTitle = detailApplication.PageTitle,
                    metaDescription = detailApplication.MetaDescription,
                    description = detailApplication.Description,
                    vanityUrl = detailApplication.VanityUrl,
                    keywords = detailApplication.Keywords,
                    synonyms = detailApplication.Synonyms
                };
            }
        }

        public async Task<dynamic> GetByVanityUrlAsync(string vanityUrl)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                DetailApplication detailApplication = await unitOfWork.DetailApplicationRepository.GetAll()
                    .FirstOrDefaultAsync(x => x.VanityUrl.Equals(vanityUrl));

                if (detailApplication == null) throw new Exception("Detail Application not found");

                return new
                {
                    id = detailApplication.Id,
                    name = detailApplication.Name,
                    header = detailApplication.Header,
                    pageTitle = detailApplication.PageTitle,
                    metaDescription = detailApplication.MetaDescription,
                    description = detailApplication.Description,
                    vanityUrl = detailApplication.VanityUrl,
                    keywords = detailApplication.Keywords,
                    synonyms = detailApplication.Synonyms
                };
            }
        }

        public async Task<dynamic> ListAsync(string query, int count, int offset)
        {
            using (var unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var detailApplicationQuery = unitOfWork.DetailApplicationRepository.GetAll();

                if (!string.IsNullOrWhiteSpace(query))
                    detailApplicationQuery = detailApplicationQuery.Where(x => x.Name.ToUpper().Contains(query.ToUpper()));

                int totatCount = await detailApplicationQuery.CountAsync();

                var result = await detailApplicationQuery.OrderBy(x => x.Id).Select(x => new
                {
                    id = x.Id,
                    name = x.Name,
                    header = x.Header,
                    pageTitle = x.PageTitle,
                    metaDescription = x.MetaDescription,
                    description = x.Description,
                    vanityUrl = x.VanityUrl,
                    keywords = x.Keywords,
                    synonyms = x.Synonyms
                })
                .Skip(offset)
                .Take(count)
                .AsNoTracking()
                .ToListAsync();

                return new
                {
                    totalCount = totatCount,
                    data = result
                };
            }
        }
    }
}