﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using Dapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;

namespace BIMsmithMarket.Services.Search.FullTextSearch
{
    /// <summary>
    /// Utility class for performing full text searches
    /// </summary>
    public class SearchAlgorithm
    {
        public class FTSSearchResult : IComparable<FTSSearchResult>
        {
            // Market DB search
            public int Id { get; set; }
            public TokenMatchType? PN { get; set; }
            public TokenMatchType? PD { get; set; }
            public TokenMatchType? PK { get; set; }
            public TokenMatchType? CN { get; set; }
            public TokenMatchType? CK { get; set; }
            public TokenMatchType? PCN { get; set; }
            public TokenMatchType? PCK { get; set; }
            public TokenMatchType? LN { get; set; }
            public TokenMatchType? MN { get; set; }
            public TokenMatchType? MK { get; set; }
            public TokenMatchType? PS { get; set; }
            public TokenMatchType? OC { get; set; }
            public TokenMatchType? MCN { get; set; }
            public TokenMatchType? MCK { get; set; }

            // external searches
            public TokenMatchType? PC { get; set; } // cert
            public TokenMatchType? MF { get; set; } // mstfmt

            //public int CategoryId { get; set; }
            //public int? ParentCategoryId { get; set; }
            //public int? ParentParentCategoryId { get; set; }
            public float Weight { get; set; }

            // output
            public float Score { get; private set; }
            public string Explain { get; private set; }

            public FTSSearchResult Rank(QueryTokenType tokenType, string token, WeightsTable weightsTable)
            {
                CalculateScore(tokenType, weightsTable);
                Explain = $"'{token}'->{Explain}";
                return this;
            }

            private static TokenMatchType BestMatch(IEnumerable<TokenMatchType?> hits)
            {
                if (!hits.Any(h => h.HasValue && h.Value != TokenMatchType.None))
                    return TokenMatchType.None;

                return hits.Where(h => h.HasValue && h != TokenMatchType.None).Min(h => h.Value);
            }

            public static FTSSearchResult Aggregate(IEnumerable<FTSSearchResult> results)
            {
                if (results.Count() == 1)
                    return results.Single();

                return new FTSSearchResult
                {
                    Id = results.Max(r => r.Id),
                    CK = BestMatch(results.Select(r => r.CK)),
                    CN = BestMatch(results.Select(r => r.CN)),
                    LN = BestMatch(results.Select(r => r.LN)),
                    MCK = BestMatch(results.Select(r => r.MCK)),
                    MCN = BestMatch(results.Select(r => r.MCN)),
                    MF = BestMatch(results.Select(r => r.MF)),
                    MK = BestMatch(results.Select(r => r.MK)),
                    MN = BestMatch(results.Select(r => r.MN)),
                    OC = BestMatch(results.Select(r => r.OC)),
                    PC = BestMatch(results.Select(r => r.PC)),
                    PCK = BestMatch(results.Select(r => r.PCK)),
                    PCN = BestMatch(results.Select(r => r.PCN)),
                    PD = BestMatch(results.Select(r => r.PD)),
                    PK = BestMatch(results.Select(r => r.PK)),
                    PN = BestMatch(results.Select(r => r.PN)),
                    PS = BestMatch(results.Select(r => r.PS)),
                    Weight = results.Max(r => r.Weight)
                };
            }

            public static FTSSearchResult Average(IEnumerable<FTSSearchResult> results, float divisor)
            {
                return new FTSSearchResult
                {
                    Score = results.Sum(r => r.Score) / divisor,
                    Explain = string.Join(",", results.Select(s => s.Explain).Where(s => s != null).Distinct())
                };
            }

            private void MaxScore(TokenMatchType? value, float[] table, string desc)
            {
                if (value.HasValue && value.Value != TokenMatchType.None && table[(int)value.Value] > Score)
                {
                    // new max
                    Score = table[(int)value.Value];
                    Explain = desc + "." + WeightsTable.Matches[(int)value.Value];
                }
            }

            private void CalculateScore(QueryTokenType tokenType, WeightsTable weightsTable)
            {
                Score = 0;
                Explain = string.Empty;
                switch (tokenType)
                {
                    case QueryTokenType.ModelNumber:
                        MaxScore(PN, weightsTable.PrdModelNum, "PrdModelNum");
                        break;
                    default:
                        MaxScore(PN, weightsTable.PrdName, "PrdName");
                        break;
                }

                MaxScore(PD, weightsTable.PrdDesc, "PrdDesc");
                MaxScore(PK, weightsTable.PrdKwrd, "PrdKwrd");
                MaxScore(CN, weightsTable.CatName, "CatName");
                MaxScore(CK, weightsTable.CatKwrd, "CatKwrd");
                MaxScore(PCN, weightsTable.ParCatName, "ParCatName");
                MaxScore(PCK, weightsTable.ParCatKwrd, "ParCatKwrd");
                MaxScore(LN, weightsTable.PrdLineName, "PrdLineName");
                MaxScore(MN, weightsTable.MfnName, "MfnName");
                MaxScore(MK, weightsTable.MfnKwrd, "MfnKwrd");
                MaxScore(PS, weightsTable.PrdStat, "PrdStat");
                MaxScore(OC, weightsTable.Omncls, "Omncls");
                MaxScore(MCN, weightsTable.MulCatName, "MulCatName");
                MaxScore(MCK, weightsTable.MulCatKwrd, "MulCatKwrd");

                // external search
                MaxScore(MF, weightsTable.Mstfmt, "Mstfmt");
                MaxScore(PC, weightsTable.PrdCert, "PrdCert");

                if (Weight > 0)
                {
                    Score += (float)Math.Log(Weight + 1) / 100.0f;
                    Explain += " +Wt";
                }
            }

            public int CompareTo(FTSSearchResult other)
            {
                return Score.CompareTo(other.Score);
            }
        }

        /// <summary>
        /// Using full-text search
        /// </summary>
        internal static ProductSearchResults SearchProducts(
            ProductSearchOptions options,
            IQueryable<Product> input,
            IUnitOfWork unitOfWork,
            SearchCache searchCache,
            ProductSearch.SearchAlgorithm algo)
        {
            var sw = Stopwatch.StartNew();

            var results = RunStep(() => new SearchResults(algo, options, input, searchCache, unitOfWork), "Initialize", sw);

            // no keyword search at all
            if (!results.HasQuery)
            {
                results.Explain(() => "No query");
                return RunStep(() => NoQueryResults(options, input, results.AllResults, results, unitOfWork, searchCache, algo), "NoQueryResults", sw);
            }

            // check for stop words (synonym == "STOP")
            RunStep(() => FindStopWords(results, searchCache), "FindStopWords", sw);

            // stop words?
            if (results.Tokens.Any(t => t.Type == QueryTokenType.Stopword))
            {
                return new ProductSearchResults(options, new Product[0].AsQueryable(), true, results);
            }

            // check for exact match on masterformat/omniclass if it's all numbers
            RunStep(() => FindCodes(results, unitOfWork), "FindCodes", sw);

            // check for exact match on model number
            RunStep(() => FindKeyStatModels(results, searchCache, unitOfWork), "FindKeyStatModels", sw);

            // hard filter on file types
            RunStep(() => FindProjectTypes(results, unitOfWork), "FindProjectTypes", sw);

            // no query, only file type
            if (results.Tokens.Any() && results.Tokens.All(t => t.Type == QueryTokenType.ProjectType))
            {
                options.ProductFileTypeIds = results.FoundProjectTypes.ToList();
                results.Explain(() => "No tokens remaining, returning results for Project Types only");
                return RunStep(() => NoQueryResults(options, input,
                    results.AllResults.Where(a => a.ProductFiles.Any(c => c.ProjectDataTypeId != null && options.ProductFileTypeIds.Contains(c.ProjectDataTypeId.Value))), results, unitOfWork, searchCache, algo), "NoQueryResults(TypeBeeline)", sw);
            }

            // lookup autocorrected versions of all tokens
            RunStep(() => AutocorrectTokens(results, searchCache, unitOfWork), "AutocorrectTokens", sw);

            // find manufacturer names in query
            RunStep(() => FindManufacturers(results, unitOfWork, algo), "FindManufacturers", sw);

            // find category names in query
            RunStep(() => FindCategories(results, unitOfWork, algo), "FindCategories", sw);

            // beeline to category view if there's 100% match on category name and no other words in the query
            if (options.CategoryId <= 0 && results.FoundCategories.Count(c => c.Value > WeightsTable.CategoryBeelineThreshold) >= 1 && results.Tokens.All(t => t.Type == QueryTokenType.Category || t.Type == QueryTokenType.Noise))
            {
                var beelineCategoryIds = results.FoundCategories.Where(c => c.Value > WeightsTable.CategoryBeelineThreshold).Select(c => c.Key).ToList();
                var noQueryResults = RunStep(() => StaticViews.DefaultView(options, beelineCategoryIds, results.AllResults, input, results, unitOfWork, searchCache, algo), "DefaultCategoryViewBeeline", sw);
                if (noQueryResults.AllResults(unitOfWork).Any())
                {
                    results.Explain(c => $"Beeline hit on categories {string.Join(",", beelineCategoryIds.Select(b => c.Categories[b].Name))} and no other tokens");
                    // only beeline if there are results
                    if (beelineCategoryIds.Count == 1)
                    {
                        options.CategoryId = beelineCategoryIds.Single();
                    }
                    return noQueryResults;
                }
                else
                {
                    results.Explain(c => $"Beeline hit on categories {string.Join(",", beelineCategoryIds.Select(b => c.Categories[b].Name))} but no results found, proceeding");
                }
            }

            // find exatch name matches
            RunStep(() => FindNameMatches(results, unitOfWork, algo), "FindNameMatches", sw);

            // find url matches
            RunStep(() => FindUrlMatches(results, unitOfWork), "FindUrlMatches", sw);

            // find units
            RunStep(() => FindUnits(results, searchCache, unitOfWork), "FindUnits", sw);

            // normalize hyphens
            RunStep(() => NormalizeHyphens(results, unitOfWork), "NormalizeHyphens", sw);

            // normalize tokens for FTS
            RunStep(() => NormalizeForFTS(results), "NormalizeForFTS", sw);

            // mark words suspected to be model numbers
            RunStep(() => FindFreeTextModels(results, unitOfWork), "FindFreeTextModels", sw);

            // FTS full phrase
            RunStep(() => FindPhraseResults(results, searchCache, unitOfWork, algo), "FindPhraseResults", sw);

            // FTS individual tokens
            RunStep(() => FindTokenResults(results, searchCache, unitOfWork, algo), "FindTokenResults", sw);

            // calculate result ranks
            var rankedResults = RunStep(() => RankResults(results, searchCache, unitOfWork, algo), "RankResults", sw);

            // primary and secondary results
            var primaryOrderedResults = RunStep(() => rankedResults.GetPrimaryResults(), "PrimaryResults", sw);
            var secondaryOrderedResults = RunStep(() => rankedResults.GetSecondaryResults(), "SecondaryResults", sw);

            // filter results (FTS results are not filtered by AllResults), only use primary results for this
            var primaryResultsIds = primaryOrderedResults?.Select(o => o.Id).ToList();
            var filteredResults = RunStep(() => results.AllResults.Where(r => primaryResultsIds.Contains(r.Id)), "FilterResults", sw);

            // create explain header
            results.ExplainSummary();

#if (DEBUG)
            Debug.Write(string.Join(Environment.NewLine, results.SearchExplain));
#endif
            if (options.SortType == ProductSortType.Relevant)
            {
                // rotate results with similar scores across manufacturers
                RunStep(() => RotateSimilarResults(primaryOrderedResults, unitOfWork), "RotateSimilarResults", sw);

                return RunStep(() => new ProductSearchResults(options, filteredResults, primaryOrderedResults, secondaryOrderedResults, input, results), "ProductSearchResults (Relevant)", sw);
            }
            else
            {
                return RunStep(() => new ProductSearchResults(options, filteredResults, false, results), "ProductSearchResults (generic)", sw);
            }
        }

        internal static ProductSearchResults NoQueryResults(ProductSearchOptions options, IQueryable<Product> input, IQueryable<Product> allResults, SearchResults results, IUnitOfWork unitOfWork, SearchCache searchCache, ProductSearch.SearchAlgorithm algo)
        {
            if (options.SortType == ProductSortType.Relevant)
            {
                // check if we are looking for a pure category search since we don't have queries
                if (options.CategoryId > 0)
                {
                    return StaticViews.DefaultView(options, new List<int> { options.CategoryId }, allResults, input, results, unitOfWork, searchCache, algo);
                }
            }
            return StaticViews.DefaultView(options, new List<int> { 0 }, allResults, input, results, unitOfWork, searchCache, algo);
        }


        internal class AutocorrectResult
        {
            public string IndexWord { get; set; }
            public string Inflection { get; set; }
            public int Distance { get; set; }
            public string Type { get; set; }
        }

        internal static void AutocorrectTokens(SearchResults results, SearchCache searchCache, IUnitOfWork unitOfWork)
        {
            if (results.Tokens.Any())
            {
                foreach (var token in results.Tokens.Where(t => t.Type == QueryTokenType.Unknown))
                {
                    string correction = null;
                    if (token.Original.All(c => char.IsLetter(c) || c == '-') && token.Original.Length > 0 && !searchCache.IsAcronym(token.Original)) // autocorrect only if all characters are letters
                    {
                        try
                        {
                            var autocorrectToken = QueryToken.Trim(token.Original, 200);
                            if (autocorrectToken.Contains('-') && autocorrectToken.Replace("-", "").Length > 0)
                            {
                                // autocorrect works best on conjoined tokens
                                autocorrectToken = autocorrectToken.Replace("-", "");
                            }

                            var autocorrectSensitivity = searchCache.GetSetting(ProductSearch.sAutocorrectSensitivitySetting, 3);
                            var autocorrectResults = new List<AutocorrectResult>();

                            var sql = "dbo.FTSAutocorrect @Query, @LangCode, 10, @Sensitivity";
                            var parameters = new
                            {
                                Query = autocorrectToken,
                                results.Options.LangCode,
                                Sensitivity = autocorrectSensitivity + 1
                            };
                            autocorrectResults = unitOfWork.CurrentDbContext.Database.GetDbConnection().Query<AutocorrectResult>(sql, parameters).ToList();

                            var topMatch = autocorrectResults.Where(a => a.Distance <= autocorrectSensitivity).FirstOrDefault();
                            if (topMatch != null && topMatch.Type == "Noise")
                            {
                                token.Type = QueryTokenType.Noise;
                                results.Explain(() => $"Token '{token.Original}' classified as Noise");
                            }
                            else if (token.Original.Length > 2)
                            {
                                correction = topMatch?.IndexWord;
                                if (token.Original != correction)
                                {
                                    results.Explain(() => $"Token '{token.Original}' corrected to '{topMatch?.IndexWord}' at distance {topMatch?.Distance}");
                                }
                            }
                        }
                        catch (Exception e)
                        {
                            Trace.TraceError("FTSAutocorrect '{0}' error: {1}", token.Original, e.Message);
                            throw;
                        }
                    }

                    token.Corrected = correction ?? token.Original;
                }
            }
        }

        internal static void FindStopWords(SearchResults results, SearchCache cache)
        {
            if (results.HasQuery)
            {
                foreach (var token in results.Tokens)
                {
                    if (cache.IsStopWord(token.Original))
                    {
                        results.Explain(() => $"Token '{token.Original}' identified as a stop word, aborting search.");
                        token.Type = QueryTokenType.Stopword;
                    }
                }
            }
        }

        internal static void FindCodes(SearchResults state, IUnitOfWork unitOfWork)
        {
            if (state.HasQuery && state.Tokens.Any())
            {
                // check for exact match on masterformat/omniclass if query is all numbers
                var cache = SearchCache.Get(unitOfWork);
                var codeQuery = state.Query.Replace(" ", "");
                if (codeQuery.Length > 3 && !codeQuery.Any(c => char.IsLetter(c)))
                {
                    var nbsp = char.ConvertFromUtf32(160);
                    state.FoundOmniclasses.AddRange(cache.Omniclasses.Where(m => m.Code.Replace(nbsp, "").StartsWith(codeQuery)).Select(m => m.Id).ToList());
                    if (state.FoundOmniclasses.Any())
                    {
                        state.Explain(c => $"Found Omniclass Ids {string.Join(",", state.FoundOmniclasses)} for token {codeQuery}");
                        state.Tokens.ForEach(t => t.Type = QueryTokenType.Omniclass);
                    }

                    state.FoundMasterformats.AddRange(cache.ExternalMasterformats.Where(m => m.Code.Replace(" ", "").StartsWith(codeQuery)).Select(m => m.Id).ToList());
                    if (state.FoundMasterformats.Any())
                    {
                        state.Explain(c => $"Found Masterformat Ids {string.Join(",", state.FoundMasterformats)} for token {codeQuery}");
                        state.Tokens.ForEach(t => t.Type = QueryTokenType.Masterformat);
                    }
                }
            }
        }

        internal static void FindKeyStatModels(SearchResults state, SearchCache searchCache, IUnitOfWork unitOfWork)
        {
            if (state.HasQuery && state.Tokens.Any())
            {
                // check for token matches on model number
                var tokenStrings = state.Tokens.Select(t => t.Original).ToList();
                var modelNumberKeyStat = searchCache.GetSetting(ProductSearch.sModelNumberKeyStatSetting, 280);
                var modelNumberPairs = unitOfWork.KeyStatValueListRepository.GetAll().Where(m => m.ProductStats.KeyStatId == modelNumberKeyStat && tokenStrings.Contains(m.Value))
                    .Select(m => new { m.Value, m.ProductStats.ProductId }).Take(1000).ToList();
                if (modelNumberPairs.Any())
                {
                    var modelNumbers = modelNumberPairs.Select(m => m.Value).Distinct().ToList();
                    foreach (var modelNumber in modelNumbers)
                    {
                        foreach (var matchingToken in state.Tokens.Where(t => t.Original == modelNumber))
                        {
                            matchingToken.HasResults = true;
                            matchingToken.Type = QueryTokenType.ModelNumber;
                        }
                    }
                    state.FoundModels.AddRange(modelNumberPairs.Select(m => m.ProductId));
                    state.Explain(c => $"Found Model numbers of products {string.Join(",", state.FoundModels)} on tokens {string.Join(", ", modelNumbers)}");
                }
            }
        }

        internal static void FindFreeTextModels(SearchResults state, IUnitOfWork unitOfWork)
        {
            if (state.HasQuery && state.Tokens.Any())
            {
                foreach (var token in state.Tokens)
                {
                    if (token.Type == QueryTokenType.Unknown && token.Original.Any(t => char.IsDigit(t)))
                    {
                        token.Type = QueryTokenType.ModelNumber;
                        state.Explain(() => $"Categorised token {token.Original} as model number");
                    }
                }
            }
        }

        internal static void FindProjectTypes(SearchResults state, IUnitOfWork unitOfWork)
        {
            if (state.HasQuery && state.Tokens.Any(t => t.Type == QueryTokenType.Unknown))
            {
                // check for exact file type name
                var cache = SearchCache.Get(unitOfWork);
                foreach (var token in state.Tokens.Where(t => t.Type == QueryTokenType.Unknown && !string.IsNullOrWhiteSpace(t.Original)))
                {
                    var trimmedToken = token.Original.Trim().ToLower();
                    if (cache.ProjectTypes.ContainsKey(trimmedToken))
                    {
                        token.Type = QueryTokenType.ProjectType;
                        state.FoundProjectTypes.Add(cache.ProjectTypes[trimmedToken]);
                        state.Explain(c => $"Found Project Type {trimmedToken} as {c.ProjectTypes[trimmedToken]}");
                    }
                }
            }
        }

        internal static void FindUnits(SearchResults state, SearchCache searchCache, IUnitOfWork unitOfWork)
        {
            // pre-check unit results
            var unitsFound = false;
            var exactUnitResults = UnitSearch.CheckForUnitMatch(state.Tokens, state.AllResults, unitOfWork, out unitsFound);
            if (unitsFound)
            {
                SearchResults.Merge(state.UnitMatches, exactUnitResults);
                state.Explain(() => $"Found {exactUnitResults.Count} exact unit matches");

                // to account for misformed stats in database, add score from free text result on unit tokens, but not mandatory
                // not sure we want this
                if (searchCache.GetSetting(ProductSearch.sSearchTextForUnitsSetting, 1) == 1)
                {
                    var unitTokens = state.Tokens.Where(t => t.Type == QueryTokenType.Unit);
                    var unitTokenQuery = QueryToken.NormalizeToken(string.Join(" ", unitTokens.Select(t => t.Original)));
                    if (!string.IsNullOrWhiteSpace(unitTokenQuery))
                    {
                        var approxUnitResults = new Dictionary<int, float>();
                        foreach (var unitSearchResult in FreetextSearch(QueryToken.DummyFTS(unitTokenQuery), state, searchCache, unitOfWork))
                        {
                            approxUnitResults.Add(unitSearchResult.Id, 0.25f); // smaller confidence
                        }
                        state.Explain(() => $"Found {approxUnitResults.Count} approximate unit matches from query '{unitTokenQuery}'");
                        SearchResults.Merge(state.UnitMatches, approxUnitResults);
                    }
                }
            }
        }

        internal static void FindManufacturers(SearchResults state, IUnitOfWork unitOfWork, ProductSearch.SearchAlgorithm algo)
        {
            var cache = SearchCache.Get(unitOfWork);
            if (cache != null && cache.ManufacturerTokens != null && (state.Options.ManufacturerIds == null || !state.Options.ManufacturerIds.Any()))
            {
                var unknownTokens = state.Tokens.Where(t => t.Type == QueryTokenType.Unknown).ToList();

                if (algo >= ProductSearch.SearchAlgorithm.V8)
                {
                    // new method: exact match on synonyms only
                    var matchedManufacturers = cache.TryMatchExact(unknownTokens, cache.ManufacturerSynonymTokens, state.Options.LangCode, QueryTokenType.Manufacturer, state.Tokens.Count(t => t.Type != QueryTokenType.Noise), state);
                    if (matchedManufacturers.Any())
                    {
                        state.Explain(c => $"Matched Manufacturers by synonyms: {string.Join(",", matchedManufacturers.Select(m => $"{c.Manufacturers[m.Key].Name} ({m.Value:P0})"))}");
                    }
                    SearchResults.Merge(state.FoundManufacturers, matchedManufacturers);
                }
                else
                {
                    var matchedManufacturers = cache.TryMatchWithConfidence(unknownTokens, cache.ManufacturerTokens, state.Options.LangCode, QueryTokenType.Manufacturer, state.Tokens.Count(t => t.Type != QueryTokenType.Noise));
                    SearchResults.Merge(state.FoundManufacturers, matchedManufacturers);

                    if (state.Tokens.Any(t => t.Type == QueryTokenType.Noise))
                    {
                        // only exact hits if we include noise tokens
                        var noiseTokens = state.Tokens.Where(t => t.Type == QueryTokenType.Unknown || t.Type == QueryTokenType.Noise).ToList();
                        var noiseManufacturers = cache.TryMatchWithConfidence(noiseTokens, cache.ManufacturerTokens, state.Options.LangCode, QueryTokenType.Manufacturer, state.Tokens.Count());
                        var relevantManufacturers = noiseManufacturers.Where(m => m.Value >= WeightsTable.ManufacturerBeelineThreshold);
                        if (relevantManufacturers.Any())
                        {
                            state.Explain(c => $"Matched Manufacturers by query: {string.Join(",", relevantManufacturers.Select(m => $"{c.Manufacturers[m.Key].Name} ({m.Value:P0})"))}");
                        }
                        SearchResults.Merge(state.FoundManufacturers, relevantManufacturers.ToDictionary(m => m.Key, m => m.Value));
                    }
                }
            }
        }

        internal static void FindCategories(SearchResults state, IUnitOfWork unitOfWork, ProductSearch.SearchAlgorithm algo)
        {
            // check for category hits
            var cache = SearchCache.Get(unitOfWork);
            if (cache != null && cache.CategoryNameTokens != null && state.Options.CategoryId <= 0)
            {
                var unknownTokens = state.Tokens.Where(t => t.Type == QueryTokenType.Unknown).ToList();

                if (algo >= ProductSearch.SearchAlgorithm.V8)
                {
                    // new method: exact match on synonyms
                    var synonymCategories = cache.TryMatchExact(unknownTokens, cache.CategorySynonymTokens, state.Options.LangCode, QueryTokenType.Category, state.Tokens.Count(t => t.Type != QueryTokenType.Noise), state);

                    if (synonymCategories.Any())
                        state.Explain(c => $"Matched Categories by synonyms: {string.Join(",", synonymCategories.Select(m => $"{c.Categories[m.Key].Name} ({m.Value:P0})"))}");

                    var allCategories = synonymCategories.SelectMany(c => cache.ChildCategories[c.Key].Select(cc => new KeyValuePair<int, float>(cc, c.Value * 0.9f)))
                        .Concat(synonymCategories).GroupBy(c => c.Key).ToDictionary(c => c.Key, c => c.Max(cc => cc.Value));

                    if (allCategories.Count != synonymCategories.Count)
                        state.Explain(c => $"Adding child categories: {string.Join(",", allCategories.Where(cc => !synonymCategories.ContainsKey(cc.Key)).Select(m => $"{c.Categories[m.Key].Name} ({m.Value:P0})"))}");

                    SearchResults.Merge(state.FoundCategories, allCategories);
                }
                else
                {
                    // add all child categories with highest score
                    // old method: prefix fuzzy match on variants
                    var matchedCategories = cache.TryMatchWithConfidence(unknownTokens, cache.CategoryNameTokens, state.Options.LangCode, QueryTokenType.Category, state.Tokens.Count(t => t.Type != QueryTokenType.Noise));

                    if (matchedCategories.Any())
                        state.Explain(c => $"Matched Categories by query: {string.Join(",", matchedCategories.Select(m => $"{c.Categories[m.Key].Name} ({m.Value:P0})"))}");

                    var allCategories = matchedCategories.SelectMany(c => cache.ChildCategories[c.Key].Select(cc => new KeyValuePair<int, float>(cc, c.Value * 0.9f)))
                        .Concat(matchedCategories).GroupBy(c => c.Key).ToDictionary(c => c.Key, c => c.Max(cc => cc.Value));

                    if (matchedCategories.Count != allCategories.Count)
                        state.Explain(c => $"Adding child categories: {string.Join(",", allCategories.Where(cc => !matchedCategories.ContainsKey(cc.Key)).Select(m => $"{c.Categories[m.Key].Name} ({m.Value:P0})"))}");

                    SearchResults.Merge(state.FoundCategories, allCategories);
                }
            }
        }

        internal static void AddClassicResults(string originalQuery, SearchResults state, IUnitOfWork unitOfWork)
        {
            var classicResults = state.AllResults.Where(a =>
                a.Name.ToLower().Contains(originalQuery) ||
                //a.Manufacturer.Name.ToLower().Contains(originalQuery) ||
                //a.ProductLine.Name.ToLower().Contains(originalQuery) ||
                originalQuery.Equals(a.Name) ||
                originalQuery.Equals(a.Manufacturer.Name) ||
                originalQuery.Equals(a.ProductLine.Name))
                .Select(a => new
                {
                    a.Id,
                    ProductName = a.Name.ToLower(),
                    ProductLineName = a.ProductLine.Name.ToLower(),
                    ManufacturerName = a.Manufacturer.Name.ToLower()
                })
                .ToList();

            if (classicResults.Any())
            {
                state.Explain(() => $"Found {classicResults.Count} classic SQL results for query '{originalQuery}'");
                state.HasClassicResults = true;
            }

            state.ProductNamePartials.AddRange(classicResults.Where(r => r.ProductName != null && r.ProductName.Contains(originalQuery)).Select(r => r.Id));
            state.ProductNameMatches.AddRange(classicResults.Where(r => originalQuery.Equals(r.ProductName)).Select(r => r.Id));
            state.ProductLineNameMatches.AddRange(classicResults.Where(r => originalQuery.Equals(r.ProductLineName)).Select(r => r.Id));
            state.ManufacturerNameMatches.AddRange(classicResults.Where(r => originalQuery.Equals(r.ManufacturerName)).Select(r => r.Id));

            if (state.Options.LangCode != SearchCache.DefaultLanguage)
            {
                // search translations
                var translatedProductNames = unitOfWork.DynamicTranslationRepository.GetAll().Where(t => t.LanguageCode == state.Options.LangCode && t.TranslatableEntityField.TranslatableEntity.TypeName == "Products"
                && t.TranslatableEntityField.FieldName == "Name").Where(t => t.Value.ToLower().Contains(originalQuery) || originalQuery.Equals(t.Value))
                    .Select(t => new { ProductId = t.EntityId, ProductName = t.Value.ToLower() }).ToList();
                if (translatedProductNames.Any())
                {
                    state.Explain(() => $"Found {translatedProductNames.Count} classic translated SQL results for query '{originalQuery}' and language '{state.Options.LangCode}'");
                    state.HasClassicResults = true;
                    state.ProductNamePartials.AddRange(translatedProductNames.Where(r => r.ProductName != null && r.ProductName.Contains(originalQuery)).Select(r => r.ProductId));
                    state.ProductNameMatches.AddRange(translatedProductNames.Where(r => r.ProductName != null && originalQuery.Equals(r.ProductName)).Select(r => r.ProductId));
                }
            }
        }

        internal static void FindNameMatches(SearchResults state, IUnitOfWork unitOfWork, ProductSearch.SearchAlgorithm algo)
        {
            // add non-FTS exact name match results
            if (state.HasQuery)
            {
                // use original rather than normalised query (double spaces etc.)
                var originalQuery = state.OriginalQuery.Trim().ToLower();
                if (originalQuery.Length > 1)
                {
                    AddClassicResults(originalQuery, state, unitOfWork);
                    if (algo >= ProductSearch.SearchAlgorithm.V8 && originalQuery.Count(c => c == ' ') == 1)
                    {
                        // try joined words if there are just two
                        AddClassicResults(originalQuery.Replace(" ", ""), state, unitOfWork);
                    }
                }
            }
        }

        internal static void FindUrlMatches(SearchResults state, IUnitOfWork unitOfWork)
        {
            if (!string.IsNullOrWhiteSpace(state.Options.AttachUrl))
            {
                state.UrlMatches.AddRange(state.AllResults.Where(a => a.ProductFiles.Any(pf =>
                    pf.IsAttachment &&
                    pf.File.SyncUrl.Contains(state.Options.AttachUrl)) ||
                    a.ProductLine.ProductLineFiles.Any(pf =>
                    pf.IsAttachment &&
                    pf.File.SyncUrl.Contains(state.Options.AttachUrl)))
                    .Select(a => a.Id).ToList());
            }
        }

        internal static void NormalizeHyphens(SearchResults state, IUnitOfWork unitOfWork)
        {
            // check for phrases that should have been hyphened ("semi gloss" or "semigloss" instead of "semi-gloss")
            try
            {
                var numUnknownTokens = state.Tokens.Count(t => t.Type == QueryTokenType.Unknown);
                if (numUnknownTokens >= 1)
                {
                    var cache = SearchCache.Get(unitOfWork);
                    if (cache != null && cache.HyphenedPhrases != null && cache.ConjoinedPhrases != null)
                    {
                        if (numUnknownTokens >= 2)
                        {
                            // replace "semi gloss" with "semi-gloss"
                            for (int i = 0; i < state.Tokens.Count - 1; i++)
                            {
                                var left = state.Tokens[i];
                                var right = state.Tokens[i + 1];
                                if (left.Type == QueryTokenType.Unknown && right.Type == QueryTokenType.Unknown)
                                {
                                    var phrase = string.Format("{0}-{1}", left.Corrected, right.Corrected);
                                    if (cache.HyphenedPhrases.Contains(phrase.ToLower()))
                                    {
                                        state.Explain(() => $"Replacing '{left.Corrected} {right.Corrected}' with '{phrase}'");
                                        left.FTS = phrase;
                                        right.FTS = string.Empty;
                                        right.Type = QueryTokenType.Noise;
                                        i++;
                                    }
                                    else
                                    {
                                        left.FTS = left.Corrected;
                                        right.FTS = right.Corrected;
                                    }
                                }
                            }
                        }

                        // replace "semigloss" with "semi-gloss"
                        for (int i = 0; i < state.Tokens.Count; i++)
                        {
                            var token = state.Tokens[i];
                            if (token.Type == QueryTokenType.Unknown && cache.ConjoinedPhrases.ContainsKey(token.Corrected.ToLower()))
                            {
                                var conjoinedPhrase = cache.ConjoinedPhrases[token.Corrected.ToLower()];
                                state.Explain(() => $"Replacing '{token.Corrected}' with '{conjoinedPhrase}'");
                                token.Corrected = conjoinedPhrase;
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Trace.TraceError("FTSProductSearch hyphen check error: {0}", e.Message);
                throw;
            }
        }

        internal static void NormalizeForFTS(SearchResults state)
        {
            foreach (var token in state.Tokens)
            {
                token.FTS = QueryToken.NormalizeToken(token.FTS ?? token.Corrected);

                if (string.IsNullOrWhiteSpace(token.FTS) || token.FTS.Length < 2)
                {
                    token.Type = QueryTokenType.Noise;
                    state.Explain(() => $"Categorised token '{token.Original}' as Noise");
                }
                else
                {
                    // remove single-letter phrases
                    var phrases = token.FTS.Split(new char[] { ';', '.', ':' });
                    if (phrases.Any(p => p.Length == 1))
                    {
                        token.Type = QueryTokenType.Noise;
                        state.Explain(() => $"Categorised token '{token.Original}' as Noise");
                    }
                }
            }
        }

        internal static void FindPhraseResults(SearchResults state, SearchCache searchCache, IUnitOfWork unitOfWork, ProductSearch.SearchAlgorithm algo)
        {
            int ftsTokenCount = state.Tokens.Count;
            var weightsTable = searchCache.WeightsTable;
            if (ftsTokenCount > 1)
            {
                // query whole phrase for last-resort results
                var correctedPhrase = QueryToken.NormalizeToken(string.Join(" ", state.Tokens.Select(t => t.FTS)));
                try
                {
                    var phraseResults = FreetextSearch(QueryToken.DummyFTS(correctedPhrase), state, searchCache, unitOfWork);
                    var rankedResults = phraseResults.Select(f => new { f.Id, Result = f.Rank(QueryTokenType.Unknown, correctedPhrase, weightsTable) }).ToList().Where(f => f.Result.Score > 0).ToDictionary(f => f.Id, f => f.Result);

                    SearchResults.Merge(state.PhraseResults, rankedResults);
                    if (rankedResults.Any())
                    {
                        state.Tokens.ForEach(t => t.HasResults = true);
                        state.SimplePhrase = state.Tokens;
                    }

                    state.Explain(() => $"FreePhrase search '{correctedPhrase}' returned {phraseResults.Count} results. Top 10 = {string.Join(",", rankedResults.OrderByDescending(r => r.Value).Take(10).Select(t => t.Key))}");
                }
                catch (Exception e)
                {
                    Trace.TraceError("Freetext search '{0}' error: {1}", correctedPhrase, e.Message);
                    throw;
                }

                if (algo >= ProductSearch.SearchAlgorithm.V8 && ftsTokenCount == 2)
                {
                    // if we have two words, search for them joined together and treat as phrase results as well
                    var conjoinedPhrase = QueryToken.NormalizeToken(string.Join("", state.Tokens.Select(t => t.FTS)));
                    try
                    {
                        var phraseResults = FreetextSearch(QueryToken.DummyFTS(conjoinedPhrase), state, searchCache, unitOfWork);
                        var rankedResults = phraseResults.Select(f => new { f.Id, Result = f.Rank(QueryTokenType.Unknown, conjoinedPhrase, weightsTable) }).ToList().Where(f => f.Result.Score > 0).ToDictionary(f => f.Id, f => f.Result);

                        SearchResults.Merge(state.PhraseResults, rankedResults);
                        if (rankedResults.Any())
                        {
                            state.Tokens.ForEach(t => t.HasResults = true);
                            state.SimplePhrase = state.Tokens;
                        }

                        state.Explain(() => $"FreePhrase search '{conjoinedPhrase}' returned {phraseResults.Count} results. Top 10 = {string.Join(",", rankedResults.OrderByDescending(r => r.Value).Take(10).Select(t => t.Key))}");
                    }
                    catch (Exception e)
                    {
                        Trace.TraceError("Freetext search '{0}' error: {1}", conjoinedPhrase, e.Message);
                        throw;
                    }
                }
            }
        }

        internal static IEnumerable<FTSSearchResult> AddResults(IEnumerable<int> products, TokenMatchType matchType, bool published, IUnitOfWork unitOfWork)
        {
            if (products.Any())
            {
                return
                    unitOfWork.ProductCertificateRepository.GetAll().Where(c => c.ExternalCertificateId != null && products.Contains(c.ExternalCertificateId.Value) && c.Product.Published == published)
                    .Select(r => new FTSSearchResult
                    {
                        Id = r.ProductId,
                        PC = matchType
                    });
            }
            return new FTSSearchResult[0];
        }

        internal static List<FTSSearchResult> FreetextSearch(QueryToken token, SearchResults state, SearchCache searchCache, IUnitOfWork unitOfWork)
        {
            // search using SQL FTS in Market DB
            var results = new List<FTSSearchResult>();

            if (!string.IsNullOrWhiteSpace(token.FTS))
            {
                var sql = "dbo.FTSProductSearch @Query";
                var parameters = new
                {
                    Query = token.FTS
                };
                results = unitOfWork.CurrentDbContext.Database.GetDbConnection().Query<FTSSearchResult>(sql, parameters).ToList();
            }

            // search external data in cache
            if (!string.IsNullOrWhiteSpace(token.Original))
            {
                results.AddRange(AddResults(searchCache.ExternalCertificates.Where(c => c.Synonyms.Contains(token.Original.ToLower())).Select(c => c.Id), TokenMatchType.Exact, state.Options.Published, unitOfWork));
                results.AddRange(AddResults(searchCache.ExternalCertificates.Where(c => c.Synonyms.Any(s => s.Split(' ').Any(ct => ct == token.Original.ToLower()))).Select(c => c.Id), TokenMatchType.Word, state.Options.Published, unitOfWork));
            }

            return results.GroupBy(r => r.Id).Select(r => FTSSearchResult.Aggregate(r)).ToList();
        }

        internal static void FindTokenResults(SearchResults state, SearchCache searchCache, IUnitOfWork unitOfWork, ProductSearch.SearchAlgorithm algo)
        {
            // exclude noise and numerics
            var ftsTokens = state.Tokens.Where(t =>
                t.Type != QueryTokenType.Noise &&
                t.Type != QueryTokenType.Unit &&
                t.Type != QueryTokenType.ProjectType &&
                t.Type != QueryTokenType.Masterformat &&
                t.Type != QueryTokenType.Omniclass).ToList(); // still allow model number, as it could be a cert acronym
            if (algo >= ProductSearch.SearchAlgorithm.V8)
            {
                // include freetext for categories and manufacturers, or not?
                if (searchCache.GetSetting(ProductSearch.sFreetextSearchCategory, 1) == 0)
                    ftsTokens = ftsTokens.Where(t => t.Type != QueryTokenType.Category).ToList();
                if (searchCache.GetSetting(ProductSearch.sFreetextSearchManufacturer, 1) == 0)
                    ftsTokens = ftsTokens.Where(t => t.Type != QueryTokenType.Manufacturer).ToList();
            }
            var ftsTokenCount = ftsTokens.Count();
            if (ftsTokenCount == 0)
            {
                return;
            }

            var weightsTable = searchCache.WeightsTable;
            foreach (var token in ftsTokens)
            {
                var tokenResult = new Dictionary<int, FTSSearchResult>();
                try
                {
                    List<FTSSearchResult> tokenSearchResults = FreetextSearch(token, state, searchCache, unitOfWork).ToList();
                    foreach (var t in tokenSearchResults)
                    {
                        t.Rank(token.Type, token.FTS, weightsTable);
                        if (t.Score > 0)
                            token.HasResults = true;
                    }
                    state.Explain(() => $"FreeToken search '{token.FTS}' returned {tokenSearchResults.Count} results, max score = {(tokenSearchResults.Any() ? tokenSearchResults.Max(c => c.Score) : 0)}.");

                    // if token has a hyphen, search for both hyphened and conjoined instances and choose better match
                    if (token.Type == QueryTokenType.Unknown && token.FTS.Contains('-') && !token.FTS.Any(c => char.IsDigit(c)) && token.FTS.Replace("-", "").Length > 0)
                    {
                        var conjoinedToken = token.FTS.Replace("-", "");
                        var conjoinedSearchResults = FreetextSearch(QueryToken.DummyFTS(conjoinedToken), state, searchCache, unitOfWork);
                        foreach (var cj in conjoinedSearchResults)
                        {
                            cj.Rank(token.Type, conjoinedToken, weightsTable);
                            if (cj.Score > 0)
                                token.HasResults = true;
                        }
                        state.Explain(() => $"FreeToken search '{conjoinedToken}' returned {conjoinedSearchResults.Count} results, max score = {(conjoinedSearchResults.Any() ? conjoinedSearchResults.Max(c => c.Score) : 0)}.");

                        // merge with main results by choosing higher rank on each product
                        tokenSearchResults = tokenSearchResults.Concat(conjoinedSearchResults).ToList().GroupBy(g => g.Id).Select(g => g.OrderByDescending(t => t.Score).First()).ToList();
                    }

                    // if token is mixed numbers combined, look for spaced variant 'cat5' -> 'cat 5'
                    if (token.Type == QueryTokenType.Unknown && token.FTS.Any(c => char.IsDigit(c)) && token.FTS.Any(c => !char.IsDigit(c)))
                    {
                        var sb = new StringBuilder();
                        sb.Append(token.FTS[0]);
                        for (int i = 1; i < token.FTS.Length; i++)
                        {
                            if (char.IsDigit(token.FTS[i]) && char.IsLetter(token.FTS[i - 1]) || char.IsDigit(token.FTS[i - 1]) && char.IsLetter(token.FTS[i]))
                            {
                                sb.Append(' ');
                            }
                            sb.Append(token.FTS[i]);
                        }
                        var splitToken = sb.ToString();
                        var splitTokenSearchResults = FreetextSearch(QueryToken.DummyFTS(splitToken), state, searchCache, unitOfWork);
                        foreach (var st in splitTokenSearchResults)
                        {
                            st.Rank(token.Type, splitToken, weightsTable);
                            if (st.Score > 0)
                                token.HasResults = true;
                        }
                        state.Explain(() => $"FreeToken search '{splitToken}' returned {splitTokenSearchResults.Count} results, max score = {(splitTokenSearchResults.Any() ? splitTokenSearchResults.Max(c => c.Score) : 0)}.");

                        // merge with main results by choosing higher rank on each product
                        tokenSearchResults = tokenSearchResults.Concat(splitTokenSearchResults).ToList().GroupBy(g => g.Id).Select(g => g.OrderByDescending(t => t.Score).First()).ToList();
                    }

                    var rankedResults = tokenSearchResults.ToDictionary(t => t.Id, t => t);

                    state.Explain(() => $"FreeToken search top 10 results for token '{token.FTS}': {string.Join(",", rankedResults.OrderByDescending(r => r.Value).Take(10).Select(t => t.Key))}");
                    foreach (var result in rankedResults.Where(r => r.Value.Score > 0))
                    {
                        tokenResult[result.Key] = result.Value;
                    }
                }
                catch (Exception e)
                {
                    Trace.TraceError("Freetext search '{0}' error: {1}", token.FTS, e.Message);
                    ftsTokenCount--;
                    throw;
                }

                // search results for this token
                state.TokenResults.Add(new TokenResult { Results = tokenResult, Token = token });
            }

            // results that have some score on each individual non-noise phrase from the query
            state.AllTokenScoring = state.TokenResults.SelectMany(r => r.Results).GroupBy(r => r.Key).Where(r => r.Count() == ftsTokenCount).ToDictionary(r => r.Key, r => FTSSearchResult.Average(r.Select(q => q.Value), ftsTokenCount));
            state.AllTokenPhrase = ftsTokens;
            state.Explain(() => $"{state.AllTokenScoring.Count} products have matched all {ftsTokenCount} freetext tokens individually.");
            state.FTSTokenCount = ftsTokenCount;
        }

        internal static RankedResults RankResults(SearchResults state, SearchCache searchCache, IUnitOfWork unitOfWork, ProductSearch.SearchAlgorithm algo)
        {
            var rankedResults = new RankedResults();
            var weightsTable = searchCache.WeightsTable;
            rankedResults.IncludeOnly(state.AllResults.Select(a => a.Id)); // only include within allresults

            // major ranks represent ordered sections of results, results will be ordered by major rank first, then minor rank

            // very top - exact matches (classic results)
            rankedResults.MajorRankAndScore(state.ProductNameMatches, 100, weightsTable.PrdName[1], "ExactProductName", state.Tokens);
            rankedResults.MajorRankAndScore(state.ProductNamePartials, 1/*100*/, weightsTable.PrdName[2], "PartialProductName", state.Tokens);
            rankedResults.MajorRankAndScore(state.ProductLineNameMatches, 100, weightsTable.PrdLineName[1], "ExactLineName", state.Tokens);
            rankedResults.MajorRankAndScore(state.ManufacturerNameMatches, 100, weightsTable.MfnName[1], "ExactManufacturer", state.Tokens);
            rankedResults.MajorRankAndScore(state.UrlMatches, 100, weightsTable.PrdName[1], "Url", state.Tokens);
            if (state.FoundModels.Any())
            {
                rankedResults.MajorRankAndScore(state.FoundModels, 100, weightsTable.PrdStat[1], "Model", state.Tokens.Where(t => t.Type == QueryTokenType.ModelNumber));
            }
            if (state.FoundOmniclasses.Any())
            {
                var omniclassMatches = state.AllResults.Where(e => e.ProductOmniclasses.Any(m => state.FoundOmniclasses.Contains(m.OmniclassId))).Select(e => e.Id).ToList();
                rankedResults.MajorRankAndScore(omniclassMatches, 100, weightsTable.Omncls[1], "Omniclass", state.Tokens.Where(t => t.Type == QueryTokenType.Omniclass));
            }
            if (state.FoundMasterformats.Any())
            {
                var masterformatMatches = state.AllResults.Where(e => e.ProductMasterformats.Any(m => state.FoundMasterformats.Contains(m.ExternalMasterformatId))).Select(e => e.Id).ToList();
                rankedResults.MajorRankAndScore(masterformatMatches, 100, weightsTable.Mstfmt[1], "Masterformat", state.Tokens.Where(t => t.Type == QueryTokenType.Masterformat));
            }

            if (state.UnitMatches.Any())
            {
                bool unitsFound = false;
                var categoryMatches = state.FoundCategories.Any() ? new SortedSet<int>(ProductSearch.FilterByCategories(state.AllResults, state.FoundCategories.Keys, unitOfWork).Select(p => p.Id).ToList()) : null;
                foreach (var match in state.UnitMatches)
                {
                    if (algo >= ProductSearch.SearchAlgorithm.V8 || categoryMatches == null || categoryMatches.Contains(match.Key)) // only if we matched category as well
                    {
                        rankedResults.MajorRankAndScore(match.Key, 4, weightsTable.UnitMatchScore * match.Value, "Unit"); // lower rank based on unit confidence
                        rankedResults.Query(match.Key, state.Tokens.Where(t => t.Type == QueryTokenType.Unit));
                        if (match.Value == 1)
                        {
                            if (algo == ProductSearch.SearchAlgorithm.V7)
                            {
                                rankedResults.SecondaryRankCutoff = 3; // anything below 4 will be secondary result
                            }

                            unitsFound = true;
                        }
                    }
                }
                if (unitsFound)
                    state.Explain(() => $"Rank cut-off for secondary results is {rankedResults.SecondaryRankCutoff} (unit matches found)");
            }

            // middle-tier - partial matches on categories or manufacturers
            if (state.FoundManufacturers.Any())
            {
                var manufacturerMatches = ProductSearch.FilterByManufacturers(state.AllResults, state.FoundManufacturers.Keys, unitOfWork).Select(p => new { p.Id, p.ManufacturerId, p.Weight }).ToList();
                foreach (var manufacturerMatch in manufacturerMatches.GroupBy(m => m.ManufacturerId))
                {
                    var maxWeight = manufacturerMatch.Max(m => m.Weight);
                    var minWeight = manufacturerMatch.Min(m => m.Weight);
                    foreach (var match in manufacturerMatch)
                    {
                        rankedResults.MajorRankAndScore(match.Id, 3, weightsTable.MfnName[1] * state.FoundManufacturers[match.ManufacturerId], "Manufacturer"); // lower rank based on confidence in manufacturer name
                        if (maxWeight != minWeight && weightsTable.WeightScore != 0)
                        {
                            // add weight normalized per manufacturer
                            var normalizedWeight = (match.Weight - minWeight) / (maxWeight - minWeight);
                            rankedResults.MajorRankAndScore(match.Id, 3, weightsTable.WeightScore * normalizedWeight, "Weight");
                        }
                        rankedResults.Query(match.Id, state.Tokens.Where(t => t.Type == QueryTokenType.Manufacturer));
                    }
                }
            }

            if (state.FoundCategories.Any())
            {
                if (algo == ProductSearch.SearchAlgorithm.V9)
                {
                    var categoryIds = state.FoundCategories.Keys.ToList();
                    var categoryResults = state.AllResults.Where(p =>
                            categoryIds.Contains(p.CategoryId) ||
                            (p.Category.ParentCategoryId != null && categoryIds.Contains(p.Category.ParentCategoryId.Value)) ||
                            (p.Category.ParentCategoryId != null && p.Category.ParentCategory.ParentCategoryId != null && categoryIds.Contains(p.Category.ParentCategory.ParentCategoryId.Value)) ||
                            p.ProductCategories.Any(pc => categoryIds.Contains(pc.CategoryId)));
                    var categoryMatches = StaticViews.GetProductDisplayOrderViews(categoryResults, unitOfWork.ProductCategoryRepository.GetAll(), searchCache);

                    int counter = 0;
                    foreach (var match in categoryMatches)
                    {
                        // find highest category match
                        var categoryScore = state.FoundCategories.ContainsKey(match.CategoryId) ? state.FoundCategories[match.CategoryId] : 0;
                        foreach (var pc in new[] { match.CategoryId, match.ParentCategoryId ?? -1 })
                        {
                            if (state.FoundCategories.ContainsKey(pc))
                            {
                                categoryScore = Math.Max(categoryScore, state.FoundCategories[pc]);
                            }
                        }
                        rankedResults.MajorRankAndScore(match.ProductId, 3, weightsTable.CatName[1] * categoryScore - counter++ * 0.0001f, "Category"); // lower rank based on confidence in category name

                        // mark tokens that pertain to all found categories
                        foreach (var categoryId in new[] { match.CategoryId, match.ParentCategoryId ?? -1 }.Intersect(state.FoundCategories.Keys).ToList())
                        {
                            var parentCategoryId = searchCache.Categories[categoryId].ParentCategoryId ?? -1;
                            rankedResults.Query(match.ProductId, state.Tokens.Where(t => t.Type == QueryTokenType.Category && (t.Matches.Contains(categoryId) || t.Matches.Contains(parentCategoryId))));
                        }
                    }
                }
                else
                {
                    var categoryMatches = ProductSearch.FilterByCategories(state.AllResults, state.FoundCategories.Keys, unitOfWork).Select(p => new { p.Id, p.CategoryId, Categories = p.ProductCategories.Select(c => c.CategoryId) }).ToList();
                    foreach (var match in categoryMatches)
                    {
                        // find highest category match
                        var categoryScore = state.FoundCategories.ContainsKey(match.CategoryId) ? state.FoundCategories[match.CategoryId] : 0;
                        foreach (var pc in match.Categories)
                        {
                            if (state.FoundCategories.ContainsKey(pc))
                            {
                                categoryScore = Math.Max(categoryScore, state.FoundCategories[pc]);
                            }
                        }
                        rankedResults.MajorRankAndScore(match.Id, 3, weightsTable.CatName[1] * categoryScore, "Category"); // lower rank based on confidence in category name
                        rankedResults.Query(match.Id, state.Tokens.Where(t => t.Type == QueryTokenType.Category));
                    }
                }
            }

            // low-tier: FTS
            foreach (var p in state.AllTokenScoring)
            {
                rankedResults.MajorRankAndScore(p.Key, 1, p.Value.Score, "Tokens: " + p.Value.Explain); // double score
                rankedResults.Query(p.Key, state.AllTokenPhrase);
            }

            // phrase scoring            
            foreach (var p in state.PhraseResults)
            {
                rankedResults.MajorRankAndScore(p.Key, 1, p.Value.Score, "Phrase: " + p.Value.Explain);
                rankedResults.Query(p.Key, state.SimplePhrase);
            }

            // low-quality (secondary) results with rank of 0 ("fewer words")
            if (state.FTSTokenCount > 1)
            {
                var betterResults = new SortedSet<int>(state.AllTokenScoring.Keys.Concat(state.PhraseResults.Keys).Concat(rankedResults.PrimaryResultIds()));

                // do we have any sub-phrase results (more than one word matched)? if yes, put top 10 at the top
                if (state.FTSTokenCount > 2)
                {
                    var subPhraseResults = state.TokenResults.SelectMany(r => r.Results).GroupBy(r => r.Key).Where(r => r.Count() > 1).ToDictionary(r => r.Key, r => FTSSearchResult.Average(r.Select(q => q.Value), state.FTSTokenCount));
                    int posNo = 0;
                    foreach (var spr in subPhraseResults.Where(s => !betterResults.Contains(s.Key)).OrderByDescending(s => s.Value).Take(10))
                    {
                        rankedResults.MajorRankAndScore(spr.Key, 0, 120 - posNo, "Subphrase");
                        rankedResults.Query(spr.Key, state.TokenResults.Where(t => t.Results.ContainsKey(spr.Key)).Select(t => t.Token));
                        betterResults.Add(spr.Key);
                        posNo++;
                    }
                    if (subPhraseResults.Any())
                    {
                        state.Explain(() => $"Secondary subphrase results at rank {rankedResults.SecondaryRankCutoff} ({subPhraseResults.Count} found with {posNo} non-repeated)");
                    }
                }

                // the rest are each individual token's results rotated
                int tokenNo = 0;
                foreach (var t in state.TokenResults)
                {
                    int posNo = 0;
                    foreach (var r in t.Results.Where(q => !betterResults.Contains(q.Key) && !rankedResults.IsExcluded(q.Key)).OrderByDescending(q => q.Value))
                    {
                        var drift = -tokenNo / 50.0f;
                        var score = Math.Max(100 - posNo, 0);
                        rankedResults.MajorRankAndScore(r.Key, 0, score, "Token");
                        rankedResults.Get(r.Key).Drift = drift;
                        rankedResults.Query(r.Key, new QueryToken[] { t.Token });
                        posNo++;
                    }
                    tokenNo++;
                    state.Explain(() => $"Secondary token results '{t.Token.Original}' at rank {rankedResults.SecondaryRankCutoff} ({t.Results.Count} found with {posNo} non-repeated)");
                }
            }
            else
            {
                state.Explain(() => "No available freetext tokens for secondary results");
            }

            // narrow down to project type matches
            if (state.FoundProjectTypes.Any())
            {
                var projectTypeMatches = state.AllResults.Where(e => e.ProductFiles.Any(f => f.ProjectDataTypeId != null && state.FoundProjectTypes.Contains(f.ProjectDataTypeId.Value))).Select(e => e.Id).ToList();
                state.Explain(() => $"Narrowing down results to project types '{string.Join(",", state.FoundProjectTypes)}' only");
                rankedResults.IncludeOnly(projectTypeMatches);
            }

            // check whether we have relevant results
            if (algo >= ProductSearch.SearchAlgorithm.V8 && searchCache.GetSetting(ProductSearch.sRequireAllTokensToMatch, 1) == 1)
            {
                var expectedTokens = state.Tokens.Count(t => t.Type != QueryTokenType.Noise);
                int foundAllTokens = 0;
                foreach (var o in rankedResults.GetOrderedResults().Where(r => r.Score > 0))
                {
                    // only full matches will be in primary results
                    if (o.MatchedTokens.Count >= expectedTokens)
                    {
                        foundAllTokens++;
                    }
                    else
                    {
                        o.MajorRank = 0;
                    }
                }
                if (foundAllTokens == 0)
                {
                    // no result has matched ALL tokens, so all results are secondary
                    state.Explain(() => $"We have no results that match all {expectedTokens} tokens, returning as secondary results only");
                    rankedResults.SecondaryRankCutoff = 1000;
                }
                else
                {
                    state.Explain(() => $"Found {foundAllTokens} primary results matching all {expectedTokens} tokens, rest are secondary");
                }
            }

            // remember ranks
            state.RankedResults = rankedResults;

            return rankedResults;
        }

        internal static void RotateSimilarResults(IEnumerable<RankedResults.ProductRank> primaryOrderedResults, IUnitOfWork unitOfWork)
        {
            // see if there are any candidate groups for rotating
            if (primaryOrderedResults.Any())
            {
                var clusters = new List<List<RankedResults.ProductRank>>();

                var currentCluster = new List<RankedResults.ProductRank>();
                RankedResults.ProductRank startRank = primaryOrderedResults.First();
                currentCluster.Add(startRank);
                foreach (var nextRank in primaryOrderedResults.Skip(1))
                {
                    if (nextRank.MajorRank == startRank.MajorRank && nextRank.FinalScore > startRank.FinalScore - 1.0f)
                    {
                        // within threshold, add to current cluster
                        currentCluster.Add(nextRank);
                    }
                    else
                    {
                        // cannot add to cluster, start new cluster
                        if (currentCluster.Count > 1)
                        {
                            clusters.Add(currentCluster.ToList());
                        }
                        currentCluster.Clear();
                        startRank = nextRank;
                        currentCluster.Add(nextRank);
                    }
                }
                if (currentCluster.Count > 1)
                {
                    clusters.Add(currentCluster.ToList());
                }

                if (clusters.Any())
                {
                    // get manufacturer information and rotate all the clusters
                    var clusterIds = clusters.SelectMany(c => c.Select(r => r.Id)).ToList();
                    var manufacturerMap = unitOfWork.ProductRepository.GetAll().Where(p => clusterIds.Contains(p.Id)).Select(p => new { p.Id, p.ManufacturerId }).ToList().ToDictionary(p => p.Id, p => p.ManufacturerId);
                    foreach (var cluster in clusters)
                    {
                        var maxScore = cluster.Max(c => c.FinalScore);
                        var minScore = cluster.Min(c => c.FinalScore) - 0.01f; // in case max == min
                        var scoreStep = (minScore - maxScore) / (cluster.Count - 1);
                        var resultsByManufacturer = cluster.GroupBy(c => manufacturerMap[c.Id]).Select(c => c.OrderByDescending(q => q.Score).ToList()).OrderByDescending(c => c.First().FinalScore).ToArray();
                        var currentScore = maxScore;
                        var currentManufacturerIdx = 0;
                        var productsRemaining = cluster.Count;
                        var flipManufacturer = false;
                        while (productsRemaining > 0)
                        {
                            if (resultsByManufacturer[currentManufacturerIdx].Any())
                            {
                                var nextResult = resultsByManufacturer[currentManufacturerIdx].First();
                                nextResult.Drift = currentScore - nextResult.Score;
                                resultsByManufacturer[currentManufacturerIdx].Remove(nextResult);
                                currentScore += scoreStep;
                                productsRemaining--;
                            }
                            // next manufacturer (two products each)
                            if (flipManufacturer)
                            {
                                currentManufacturerIdx++;
                                flipManufacturer = false;
                            }
                            else
                            {
                                flipManufacturer = true;
                            }
                            currentManufacturerIdx %= resultsByManufacturer.Length;
                        }
                    }
                }
            }
        }

        internal static void RunStep(Action stepFunc, string stepName, Stopwatch sw)
        {
            try
            {
                stepFunc();
            }
            catch (Exception ex)
            {
                Trace.TraceError("FullTextSearch::SearchProducts step {0} error: {1}", stepName, ex.Message);
            }

#if (DEBUG)
            Debug.WriteLine(string.Format("FullTextSearch::SearchProducts step {0} finished at {1}ms", stepName, sw.ElapsedMilliseconds));
#endif
        }

        internal static T RunStep<T>(Func<T> stepFunc, string stepName, Stopwatch sw) where T : class
        {
            T ret = null;
            try
            {
                ret = stepFunc();
            }
            catch (Exception ex)
            {
                Trace.TraceError("FullTextSearch::SearchProducts step {0} error: {1}", stepName, ex.Message);
            }

#if (DEBUG)
            Debug.WriteLine(string.Format("FullTextSearch::SearchProducts step {0} finished at {1}ms", stepName, sw.ElapsedMilliseconds));
#endif
            return ret;
        }

        internal static void DumpExplain(ProductSearchOptions options, List<string> searchExplain)
        {
            if (options.Skip == 0)
            {
                Debug.WriteLine("==== Search Query: '" + options.Query + "'");
                foreach (var s in searchExplain)
                {
                    Debug.WriteLine("== " + s);
                }
            }
        }
    }
}