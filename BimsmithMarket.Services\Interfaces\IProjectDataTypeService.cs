﻿using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IProjectDataTypeService
    {
        Task<object> GetAsync(IUnitOfWork unitOfWork, int id);
        Task<object> ListAsync(IUnitOfWork unitOfWork, bool onlyParents);
        Task<object> AddAsync(IUnitOfWork unitOfWork, string userId, AddProjectDataTypeModel model);
        Task<object> EditAsync(IUnitOfWork unitOfWork, string userId, EditProjectDataTypeModel model);
        Task DeleteAsync(IUnitOfWork unitOfWork, int id);
        Task<object> HasRelationsAsync(IUnitOfWork unitOfWork, int id);
        Task<ICollection<ProjectTypePublicListDto>> PublicListAsync(IUnitOfWork unitOfWork);

        Task<ProjectDataTypeDto[]> RevitProcessingListAsync(IUnitOfWork unitOfWork);
    }
}