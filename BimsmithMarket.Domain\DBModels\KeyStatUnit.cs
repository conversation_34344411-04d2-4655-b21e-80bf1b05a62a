﻿using System.Collections.Generic;

namespace BIMsmithMarket.Domain.DBModels
{
    public class KeyStatUnit : BaseEntity
    {
        public string GroupName { get; set; }

        public string AUnitName { get; set; }

        public string BUnitName { get; set; }

        public string Relation { get; set; }

        public string AFormat { get; set; }

        public string BFormat { get; set; }

        public string Description { get; set; }

        public UnitMetricType UnitMetricType { get; set; }

        public int Status { get; set; }

        /// ------------------------------------------
        public virtual ICollection<ProductStats> ProductStats { get; set; }

        public virtual ICollection<ProductStats> ConvertProductStats { get; set; }

        public virtual ICollection<ProductLineStats> ProductLineStats { get; set; }

        public virtual ICollection<ProductLineStats> ConvertProductLineStats { get; set; }

        public virtual ICollection<KeyStatValueList> KeyStatValueList { get; set; }

        public virtual ICollection<KeyStatValueList> ConvertKeyStatValueList { get; set; }

        public virtual ICollection<KeyStatUnitRelation> FromUnitRelations { get; set; }

        public virtual ICollection<KeyStatUnitRelation> ToUnitRelations { get; set; }

        public KeyStatUnit()
        {
            ProductStats = new List<ProductStats>();
            ConvertProductStats = new List<ProductStats>();
            ProductLineStats = new List<ProductLineStats>();
            ConvertProductLineStats = new List<ProductLineStats>();
            KeyStatValueList = new List<KeyStatValueList>();
            ConvertKeyStatValueList = new List<KeyStatValueList>();
            FromUnitRelations = new List<KeyStatUnitRelation>();
            ToUnitRelations = new List<KeyStatUnitRelation>();
        }
    }

    public enum UnitMetricType
    {
        Common = 0,
        Metric = 1,
        Imperial = 2
    }
}