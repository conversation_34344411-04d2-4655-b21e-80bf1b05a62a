﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Domain.Attributes;
using BIMsmithMarket.Domain.Interfaces;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.DataLayer
{
    public class MongoRepository<T> : IMongoRepository<T> where T : IDocument
    {
        private readonly IMongoDatabase _database;
        private readonly MongoClient _client;
        public MongoRepository()
        {
            string connectionString = ConfigurationHelper.GetValue("ConnectionStrings:MongoDb");
            var connection = new MongoUrlBuilder(connectionString);
            _client = new MongoClient(connectionString);
            _database = _client.GetDatabase(connection.DatabaseName);
        }

        public IMongoCollection<T> Collection
        {
            get { return _database.GetCollection<T>(GetCollectionName(typeof(T))); }
        }

        public async Task<IEnumerable<T>> GetAllAsync()
        {
            var builder = new FilterDefinitionBuilder<T>();
            var filter = builder.Empty;

            return await Collection.Find(filter).ToListAsync();
        }

        public async Task<T> GetByIdAsync(int id)
        {
            var filter = Builders<T>.Filter.Eq(c => c.Id, id);
            return await Collection.Find(filter).FirstOrDefaultAsync();
        }

        public async Task InsertAsync(T model)
        {
            await Collection.InsertOneAsync(model);
        }

        public async Task EditAsync(T model)
        {
            var filter = Builders<T>.Filter.Eq(c => c.Id, model.Id);
            await Collection.ReplaceOneAsync(filter, model);
        }

        public async Task DeleteAsync(int id)
        {
            var filter = Builders<T>.Filter.Eq(c => c.Id, id);
            await Collection.DeleteOneAsync(filter);
        }

        private string GetCollectionName(Type documentType)
        {
            return ((BsonCollectionAttribute)documentType.GetCustomAttributes(
                    typeof(BsonCollectionAttribute),
                    true)
                .FirstOrDefault())?.CollectionName;
        }
    }
}