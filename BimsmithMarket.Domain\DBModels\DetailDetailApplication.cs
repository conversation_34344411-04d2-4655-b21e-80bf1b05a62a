﻿using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class DetailDetailApplication : BaseEntity
    {
        public int DetailId { get; set; }

        public int DetailApplicationId { get; set; }

        [ForeignKey("DetailId")]
        public virtual Detail Detail { get; set; }

        [ForeignKey("DetailApplicationId")]
        public virtual DetailApplication DetailApplication { get; set; }
    }
}