﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{

    /// <summary>
    /// BIMsmith controller
    /// </summary>
    [Route("api/[controller]/[action]")]
    public class BIMsmithController : BaseApiController
    {
        /// <summary>
        /// Starter service
        /// </summary>
        private readonly IStarterService _starterService;

        /// <summary>
        /// Starter service
        /// </summary>
        private readonly IAnalyticsService _analyticsService;

        private readonly IBIMsmithService _bIMsmithService;

        private readonly IWebHostEnvironment _webHostEnvironment;

        private readonly UserManager<ApplicationUser> _userManager;

        public BIMsmithController(
            IAnalyticsService analyticsService,
            IBIMsmithService bIMsmithService,
            IStarterService starterService,
            IWebHostEnvironment webHostEnvironment,
            UserManager<ApplicationUser> userManager
            )
        {
            _analyticsService = analyticsService;
            _bIMsmithService = bIMsmithService;
            _starterService = starterService;
            _webHostEnvironment = webHostEnvironment;
            _userManager = userManager;
        }

        /// <summary>
        /// Get product list of user by userId
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="streamId"></param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <param name="token">Auth token</param>
        /// <returns></returns>
        [HttpGet]
        [Route("/api/BIMsmith/UserProduct/List")]
        public async Task<IActionResult> UserProductsList(string token, string userId, string streamId = null, int offset = 0, int count = 10)
        {
            if (token != ConfigurationHelper.GetValue("ApiAccessToken"))
            {
                return BadRequest("API Access token is invalid");
            }

            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _bIMsmithService.UserProductsListAsync(unitOfWork, userId, streamId, offset, count));
            }
        }

        /// <summary>
        /// Get manufacturer list of user by userId
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <param name="token">Auth token</param>
        /// <returns></returns>
        [HttpGet]
        [Route("/api/BIMsmith/UserManufacturer/List")]
        public async Task<IActionResult> UserManufacturersList(string token, string userId, int offset = 0, int count = 10)
        {
            if (token != ConfigurationHelper.GetValue("ApiAccessToken"))
            {
                return BadRequest("API Access token is invalid");
            }

            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _bIMsmithService.UserManufacturersListAsync(unitOfWork, userId, offset, count));
            }
        }

        /// <summary>
        /// Remove product from BIMsmith list.
        /// </summary>
        /// <param name="ownerId">user id</param>
        /// <param name="productId"></param>
        /// <param name="streamId"></param>
        /// <param name="token">Auth token</param>
        /// <returns></returns>
        [HttpDelete]
        [Route("/api/BIMsmith/UserProduct/Remove")]
        public async Task<IActionResult> RemoveProductFromMyBIMsmith(string token, string ownerId, int productId, string streamId)
        {
            if (token != ConfigurationHelper.GetValue("ApiAccessToken"))
            {
                return BadRequest("API Access token is invalid");
            }

            using (var unitOfWork = UnitOfWork.Create())
            {
                await _bIMsmithService.RemoveProductFromMyBIMsmithAsync(unitOfWork, ownerId, productId, streamId);

                CacheHelper.ClearSpecificCache("*/api/BIMsmith/UserProduct/List*");

                return Ok();
            }
        }

        /// <summary>
        /// Remove manufacturer from BIMsmith list.
        /// </summary>
        /// <param name="ownerId"></param>
        /// <param name="manufacturerId"></param>
        /// <param name="token">Auth token</param>
        /// <returns></returns>
        [HttpDelete]
        [Route("/api/BIMsmith/UserManufacturer/Remove")]
        public async Task<IActionResult> RemoveManufacturerFromMyBIMsmith(string token, string ownerId, int manufacturerId)
        {
            if (token != ConfigurationHelper.GetValue("ApiAccessToken"))
            {
                return BadRequest("API Access token is invalid");
            }

            using (var unitOfWork = UnitOfWork.Create())
            {
                await _bIMsmithService.RemoveManufacturerFromMyBIMsmithAsync(unitOfWork, ownerId, manufacturerId);

                CacheHelper.ClearSpecificCache("*/api/BIMsmith/UserManufacturer/List*");

                return Ok();
            }
        }

        /// <summary>
        /// Copy product to another stream 
        /// </summary>
        /// <param name="ownerId"></param>
        /// <param name="productId"></param>
        /// <param name="fromStreamId"></param>
        /// <param name="toStreamId"></param>
        /// <param name="token">Auth token</param>
        /// <returns></returns>
        [HttpPost]
        [Route("/api/BIMsmith/UserProduct/Copy")]
        public async Task<IActionResult> CopyProductToMyBIMsmith(string token, string ownerId, int productId, string fromStreamId, string toStreamId)
        {
            if (token != ConfigurationHelper.GetValue("ApiAccessToken"))
            {
                return BadRequest("API Access token is invalid");
            }

            using (var unitOfWork = UnitOfWork.Create())
            {
                await _bIMsmithService.CopyProductToMyBIMsmith(unitOfWork, ownerId, productId, fromStreamId, toStreamId);
                return Ok();
            }
        }

        /// <summary>
        /// Clean unused files in system to reduse storage usage
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("/api/BIMsmith/Manage/ClearUnusedFiles")]
        public async Task<IActionResult> ClearUnusedFiles(string token)
        {
            if (token != ConfigurationHelper.GetValue("ApiAccessToken"))
            {
                return BadRequest("API Access token is invalid");
            }

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                string photosFolder = Path.Combine(Path.GetTempPath(), "Photos");
                string filesFolder = Path.Combine(Path.GetTempPath(), "Files");

                return Ok(await _bIMsmithService.ClearUnusedFiles(unitOfWork, photosFolder, filesFolder));
            }
        }

        /// <summary>
        /// Re-generate preview for Pdf and Txt files
        /// </summary>
        /// <param name="token"></param>
        /// <param name="pdf">Include or exclude PDF</param>
        /// <param name="image">Include or exclude Image</param>
        /// <param name="txt">Include or exclude Txt</param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("/api/BIMsmith/Manage/GeneratePreviewForPdfAndTxtFiles")]
        public async Task<IActionResult> GeneratePreviewForPdfAndTxtFiles(string token, bool pdf, bool image, bool txt, int offset = 0, int count = 100)
        {
            if (token != ConfigurationHelper.GetValue("ApiAccessToken"))
            {
                return BadRequest("API Access token is invalid");
            }

            if (pdf == false && image == false && txt == false)
            {
                return BadRequest("Need to select at least one type");
            }

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                string filesFolder = Path.Combine(Path.GetTempPath(), "Files");
                return Ok(await _bIMsmithService.GeneratePreviewForPdfAndTxtFilesAsync(unitOfWork, filesFolder, pdf, image, txt, offset, count));
            }
        }

        /// <summary>
        /// Changes links to files from http to https
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("/api/BIMsmith/Manage/ChangeFilesUrl")]
        public async Task<IActionResult> ChangeFilesUrl(string token)
        {
            if (token != ConfigurationHelper.GetValue("ApiAccessToken"))
            {
                return BadRequest("API Access token is invalid");
            }

            string http = "http://";
            string https = "https://";
            var takeCount = 250;
            var needContinue = true;
            var httpLinkFiles = new List<Domain.DBModels.File>(takeCount);
            while (needContinue)
            {
                using (var unitOfWork = UnitOfWork.Create())
                {
                    await _bIMsmithService.ChangeFilesUrlAsync(unitOfWork, httpLinkFiles, http, https, takeCount, needContinue);
                }
            }
            return Ok();
        }


        /// <summary>
        /// Get list of manufacturers which user can see on analytics dashboard
        /// </summary>
        /// <param name="token"></param>
        /// <param name="userId"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("/api/BIMsmith/GetAvailableAccessToAnalytics")]
        public async Task<IActionResult> GetAvailableAccessToAnalytics(string token, string userId, string email = null)
        {
            if (token != ConfigurationHelper.GetValue("ApiAccessToken"))
            {
                return BadRequest("API Access token is invalid");
            }

            var result = await CheckAnalyticsAsync(token, userId, email);
            return Ok(result);
        }

        /// <summary>
        /// Check if analytics user has access to specified manufacturer
        /// </summary>
        /// <param name="token">The access token</param>
        /// <param name="userId">The user identifier</param>
        /// <param name="email">The user email</param>
        /// <param name="manufacturerId">The manufacturer identifier</param>
        /// <param name="projectType">The project type</param>
        /// <returns></returns>
        [HttpGet]
        [Route("/api/BIMsmith/HasAnalyticsManufacturerAccess")]
        public async Task<IActionResult> HasAnalyticsManufacturerAccess(string token, string userId, string email, int manufacturerId, ProjectType projectType)
        {
            if (token != ConfigurationHelper.GetValue("ApiAccessToken"))
            {
                return BadRequest("API Access token is invalid");
            }

            var result = await _analyticsService.HasAnalyticsManufacturerAccessAsync(userId, email, manufacturerId, projectType);
            return Ok(result);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("/api/BIMsmith/UpdateCacheOfStarters")]
        public async Task<IActionResult> UpdateCacheOfStarters(string token)
        {
            if (token != ConfigurationHelper.GetValue("ApiAccessToken"))
            {
                return BadRequest("API Access token is invalid");
            }

            await _starterService.RefreshStartersFromForgeAsync();
            return Ok();
        }


        /// <summary>
        /// Send email to customer from angularis site
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("/api/BIMsmith/SendEmail")]
        public async Task<IActionResult> SendEmail([FromBody] SendEmailModel model)
        {
            if (model.AngularisToken != ConfigurationHelper.GetValue("AngularisSiteAccessToken"))
                return BadRequest("API Access token is invalid");

            var emailsPath = Path.Combine(_webHostEnvironment.WebRootPath, "Emails");
            await EmailNotificationHelper.Create(emailsPath).SendEmail(ConfigurationHelper.GetValue("ShoutEmail"), model.Title, model.HtmlBody);

            if (!string.IsNullOrWhiteSpace(model.EmailToMailchimp))
            {
                var mailChimpKey = ConfigurationHelper.GetValue("MailChimpAngulerisSiteApiKey");
                var listId = ConfigurationHelper.GetValue("MailChimpAngulerisSiteListId");
                var swatchboxDomain = ConfigurationHelper.GetValue("SwatchboxDomain");
                if (Request.GetDisplayUrl().ToLower().Contains(swatchboxDomain.ToLower()))
                    listId = ConfigurationHelper.GetValue("MailChimpSwatchboxSubscribersListId");

                var mailChimpManager = new MailChimp.Net.MailChimpManager(mailChimpKey);
                var member = new MailChimp.Net.Models.Member { EmailAddress = model.EmailToMailchimp, StatusIfNew = MailChimp.Net.Models.Status.Subscribed };
                foreach (var item in model.MailchimpProperties)
                {
                    member.MergeFields.Add(item.Key, item.Value);
                }
                await mailChimpManager.Members.AddOrUpdateAsync(listId, member);
            }

            return Ok();
        }

        private async Task<dynamic> CheckAnalyticsAsync(string token, string userId, string email = null)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var query = unitOfWork.ManufacturerRepository.GetAll()
                    .Select(a => new
                    {
                        Manufacturer = a,
                        AccessToMarketData = true,
                        AccessToMarketManufacturerActionsData = true,
                        AccessToForgeData = true,
                        AccessToNanolumensData = true
                    });

                bool fullAccess = true;

                var user = await unitOfWork.UserRepository.GetAll().FirstOrDefaultAsync(a => a.Id == userId);
                if (user == null && !string.IsNullOrWhiteSpace(email))
                    user = await unitOfWork.UserRepository.GetAll().FirstOrDefaultAsync(a => a.Email.Trim().ToLower() == email.Trim().ToLower());

                if (user != null)
                {
                    var adminEmails = ConfigurationHelper.GetValue("AdministratorEmails").Split(',').Select(a => a.ToLower()).ToList();

                    if (adminEmails.Contains(user.Email.ToLower()) == false && !await _userManager.IsInRoleAsync(user, DbConstants.AdminRole))
                    {
                        fullAccess = false;

                        var owners = unitOfWork.ManufacturerRepository.GetAll().Where(a => a.OwnerId == user.Id)
                                    .Select(a => new
                                    {
                                        Manufacturer = a,
                                        AccessToMarketData = true,
                                        AccessToMarketManufacturerActionsData = true,
                                        AccessToForgeData = true,
                                        AccessToNanolumensData = true
                                    });

                        query = unitOfWork.ManufacturerAdminUserRepository.GetAll().Where(a =>
                                            a.AdminUserId == user.Id &&
                                            ((a.Roles & ManufacturerAdminRole.Analytics) == ManufacturerAdminRole.Analytics
                                            ||
                                            (a.Roles & ManufacturerAdminRole.AnalyticsForge) == ManufacturerAdminRole.AnalyticsForge
                                            ||
                                            (a.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess
                                            ||
                                            (a.Roles & ManufacturerAdminRole.NanolumensAnalytics) == ManufacturerAdminRole.NanolumensAnalytics)
                                            ).Select(a => new
                                            {
                                                Manufacturer = a.Manufacturer,
                                                AccessToMarketData = (a.Roles & ManufacturerAdminRole.Analytics) == ManufacturerAdminRole.Analytics || (a.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess,
                                                AccessToMarketManufacturerActionsData = (a.Roles & ManufacturerAdminRole.Analytics) == ManufacturerAdminRole.Analytics && (a.Roles & ManufacturerAdminRole.AnalyticsManufacturerActionsData) == ManufacturerAdminRole.AnalyticsManufacturerActionsData || (a.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess,
                                                AccessToForgeData = (a.Roles & ManufacturerAdminRole.AnalyticsForge) == ManufacturerAdminRole.AnalyticsForge || (a.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess,
                                                AccessToNanolumensData = ((a.Roles & ManufacturerAdminRole.NanolumensAnalytics) == ManufacturerAdminRole.NanolumensAnalytics || (a.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess) && a.ManufacturerId == AnalyticsConstants.NanolumensManufacturerId
                                            });

                        query = query.Concat(owners).Distinct();
                    }
                }
                else
                {
                    fullAccess = false;
                    query = unitOfWork.ManufacturerAdminUserRepository.GetAll().Where(a =>
                                      a.Email.Trim().ToLower() == email.Trim().ToLower() &&
                                      ((a.Roles & ManufacturerAdminRole.Analytics) == ManufacturerAdminRole.Analytics
                                      ||
                                      (a.Roles & ManufacturerAdminRole.AnalyticsForge) == ManufacturerAdminRole.AnalyticsForge
                                      ||
                                      (a.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess
                                      ||
                                      (a.Roles & ManufacturerAdminRole.NanolumensAnalytics) == ManufacturerAdminRole.NanolumensAnalytics)
                                        ).Select(a => new
                                        {
                                            Manufacturer = a.Manufacturer,
                                            AccessToMarketData = (a.Roles & ManufacturerAdminRole.Analytics) == ManufacturerAdminRole.Analytics || (a.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess,
                                            AccessToMarketManufacturerActionsData = (a.Roles & ManufacturerAdminRole.Analytics) == ManufacturerAdminRole.Analytics && (a.Roles & ManufacturerAdminRole.AnalyticsManufacturerActionsData) == ManufacturerAdminRole.AnalyticsManufacturerActionsData || (a.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess,
                                            AccessToForgeData = (a.Roles & ManufacturerAdminRole.AnalyticsForge) == ManufacturerAdminRole.AnalyticsForge || (a.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess,
                                            AccessToNanolumensData = ((a.Roles & ManufacturerAdminRole.NanolumensAnalytics) == ManufacturerAdminRole.NanolumensAnalytics || (a.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess) && a.ManufacturerId == AnalyticsConstants.NanolumensManufacturerId
                                        });
                }

                string userEmail = user?.Email ?? email;
                bool salesforceAccess = await unitOfWork.ManufacturerAdminUserRepository.GetAll()
                    .AnyAsync(x => x.Email.Trim().ToLower() == userEmail.Trim().ToLower()
                                && ((x.Roles & ManufacturerAdminRole.SalesforceAccess) == ManufacturerAdminRole.SalesforceAccess
                                || (x.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess));

                var manufacturers = await query
                    .Select(a => new
                    {
                        id = a.Manufacturer.Id,
                        name = a.Manufacturer.Name,
                        site = a.Manufacturer.Site,
                        analyticsSettings = a.Manufacturer.AnalyticsSetting,
                        logo = new
                        {
                            id = a.Manufacturer.PhotoId,
                            small = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.SmallImgUrl : null,
                            big = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.OriginalImgUrl : null
                        },
                        published = a.Manufacturer.Published,
                        accessToMarketData = a.AccessToMarketData,
                        accessToMarketManufacturerActionsData = a.AccessToMarketManufacturerActionsData,
                        accessToForgeData = a.AccessToForgeData,
                        accessToNanolumensData = a.AccessToNanolumensData,
                        swatchboxManufacturerId = a.Manufacturer.SwatchboxManufacturerId
                    })
                    .ToListAsync();

                dynamic result = new
                {
                    fullAccess = fullAccess,
                    salesforceAccess = salesforceAccess,
                    manufacturers = manufacturers.Any() ? manufacturers : null
                };

                return result;
            }
        }

        /// <summary>
        /// Gets product and manufacturer dictionary
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("/api/BIMsmith/GetProductManufacturerDictionary")]
        public async Task<IActionResult> GetProductManufacturerDictionary(string token)
        {
            if (token != ConfigurationHelper.GetValue("ApiAccessToken"))
                return BadRequest("API Access token is invalid");

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var result = await _analyticsService.GetProductManufacturerDictionaryAsync(unitOfWork);
                return Ok(result);
            }
        }


        /// <summary>
        /// Gets manufacturer and manufacturer dictionary
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("/api/BIMsmith/GetDetailManufacturerDictionary")]
        public async Task<IActionResult> GetDetailManufacturerDictionary(string token)
        {
            if (token != ConfigurationHelper.GetValue("ApiAccessToken"))
                return BadRequest("API Access token is invalid");

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var result = await _analyticsService.GetDetailManufacturerDictionaryAsync(unitOfWork);
                return Ok(result);
            }
        }
    }
}