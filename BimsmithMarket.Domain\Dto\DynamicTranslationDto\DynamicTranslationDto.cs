﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Dto.DynamicTranslationDto
{
    public class AddDynamicTranslationDto
    {
        public string Value { get; set; }

        [Required]
        public string LanguageCode { get; set; }

        [Required]
        public int TranslatableEntityFieldId { get; set; }

        [Required]
        public int EntityId { get; set; }
    }

    public class AddDynamicTranslationListDto
    {
        public List<AddDynamicTranslationDto> Items { get; set; }
    }

    public class EditDynamicTranslationDto : AddDynamicTranslationDto
    {
        [Required]
        public int Id { get; set; }
    }

    public class ListDynamicTranslationDto : EditDynamicTranslationDto
    {

    }
}