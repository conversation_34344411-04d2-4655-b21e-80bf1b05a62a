﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Domain.DBModels.PaymentDbModels;
using BIMsmithMarket.Domain.Dto.PaymentsDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.PaymentServices
{
    public class PaymentPlanService : IPaymentPlanService
    {
        public async Task<PaymentPlanDto> CreateAsync(PaymentPlanDto model, IUnitOfWork unitOfWork)
        {
            var priorityPresent = unitOfWork.PaymentPlanRepository.GetAll().Any(x => x.PaymentPriority == model.PaymentPriority);
            if (priorityPresent)
                throw new InvalidInputException($"Payment plan priority should be uniq. current {model.PaymentPriority}");

            PaymentPlan paymentPlan = model.Adapt<PaymentPlan>();
            unitOfWork.PaymentPlanRepository.Insert(paymentPlan);
            await unitOfWork.SaveAsync();

            return paymentPlan.Adapt<PaymentPlanDto>();
        }

        public async Task<PaymentPlanDto> UpdateAsync(PaymentPlanDto model, IUnitOfWork unitOfWork)
        {
            if (!model.Id.HasValue)
                throw new InvalidInputException("Invalid paymentPlanId");
            PaymentPlan paymentPlan = unitOfWork.PaymentPlanRepository.GetById(model.Id.Value);
            if (paymentPlan == null)
                throw new InvalidInputException($"paymentPlan id {model.Id.Value} not found");

            model.Adapt(paymentPlan);
            unitOfWork.PaymentPlanRepository.Edit(paymentPlan);
            await unitOfWork.SaveAsync();

            return paymentPlan.Adapt<PaymentPlanDto>();
        }

        public async Task DeleteAsync(int id, IUnitOfWork unitOfWork)
        {
            PaymentPlan paymentPlan = unitOfWork.PaymentPlanRepository.GetById(id);
            if (paymentPlan == null)
                throw new InvalidInputException($"paymentPlan id {id} not found");

            unitOfWork.PaymentPlanRepository.Delete(paymentPlan);
            await unitOfWork.SaveAsync();
        }

        public async Task<PaymentPlanDto> GetAsync(int id, IUnitOfWork unitOfWork)
        {
            PaymentPlan dbItem = unitOfWork.PaymentPlanRepository.GetById(id);
            if (dbItem == null)
                throw new DbItemNotFoundException($"price id {id} not found");

            //Active or unactive check created on Active getter!

            return dbItem.Adapt<PaymentPlanDto>();
        }

        public async Task<List<PaymentPlanDto>> ListAsync(IUnitOfWork unitOfWork)
        {
            List<PaymentPlan> dbItems = await unitOfWork.PaymentPlanRepository.GetAll().AsNoTracking().ToListAsync();
            if (!dbItems.Any())
                throw new DbItemNotFoundException($"There is no payment plans");
            return dbItems.Adapt<List<PaymentPlanDto>>();
        }
    }
}
