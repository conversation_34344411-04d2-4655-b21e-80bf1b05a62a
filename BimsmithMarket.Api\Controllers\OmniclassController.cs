﻿using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("api/[controller]/[action]")]
    public class OmniclassController : BaseApiController
    {
        private readonly IOmniclassService _omniclassService;

        public OmniclassController(IOmniclassService omniclassService)
        {
            _omniclassService = omniclassService;
        }

        /// <summary>
        /// Search Omniclass by code or title
        /// </summary>
        /// <param name="q"></param>
        /// <param name="offset">Offset</param>
        /// <param name="count">Count in search result</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("Search")]
        public async Task<IActionResult> Search(string q = null, int offset = 0, int count = 20)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _omniclassService.SearchAsync(unitOfWork, q, offset, count));
            }
        }

        /// <summary>
        /// Get detail information about Omniclass
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("Get")]
        public async Task<IActionResult> Get(int id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _omniclassService.GetAsync(unitOfWork, id));
            }
        }

        /// <summary>
        /// Get list of omniclasses for one level
        /// </summary>
        /// <param name="parentId"></param>
        /// <param name="q"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("LevelList")]
        public async Task<IActionResult> LevelList(int? parentId = null, string q = null)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _omniclassService.LevelListAsync(unitOfWork, parentId, q));
            }
        }
    }
}
