﻿using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace BIMsmithMarket.Services.Helpers
{
    public static class RequestHelper
    {
        public static string BuildQueryString(IEnumerable<KeyValuePair<string, string>> parameters)
        {
            if (!parameters.Any())
                return "";

            var builder = new StringBuilder("?");

            var separator = "";
            foreach (var parameter in parameters.Where(kvp => kvp.Value != null))
            {
                builder.AppendFormat("{0}{1}={2}", separator, parameter.Key, parameter.Value);

                separator = "&";
            }

            return builder.ToString();
        }
        public static string BuildEncodedBody(IEnumerable<KeyValuePair<string, string>> parameters)
        {
            if (!parameters.Any())
                return "";

            var builder = new StringBuilder("");

            var separator = "";
            foreach (var parameter in parameters.Where(kvp => kvp.Value != null))
            {
                var paramWithValue = $"{parameter.Key}={HttpUtility.UrlEncode(parameter.Value)}";
                builder.AppendFormat("{0}{1}", separator, paramWithValue);

                separator = "&";
            }

            return builder.ToString();
        }

    }
}