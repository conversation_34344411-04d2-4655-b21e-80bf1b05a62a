1. Add admin user to admin db
use admin
db.createUser({user:"Name", pwd:"Password", roles: ["userAdminAnyDatabase", "dbAdminAnyDatabase", "readWriteAnyDatabase"]})

2. Setup mongod.cfg
#Add network settings
net:
  port: 27017
  bindIp: 0.0.0.0 #for remoute access use this format

#Add security settings
security:
  authorization: 'enabled'

3. Enable access to selected port via Firewall settings

4. Run mongod as a Windows Service
mongod --install --config "C:\Program Files\MongoDB\Server\5.0\bin\mongod.cfg" --serviceName "MongoDB"

5. To get remote access use conection string
mongodb://[userName]:[password]@[ipAddress]:[port]/[dbName]?authSource=admin