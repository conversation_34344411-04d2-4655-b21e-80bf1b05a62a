﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Dto.AnnouncementDto;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// Announcement Controller
    /// </summary>
    [Route("api/[controller]/[action]/{id?}")]
    public class AnnouncementController : BaseApiController
    {
        /// <summary>
        /// Announcement service
        /// </summary>
        private readonly IAnnouncementService _announcementService;

        private readonly IUploadService _uploadService;

        public AnnouncementController(
            IAnnouncementService announcementService,
            IUploadService uploadService)
        {
            _announcementService = announcementService;
            _uploadService = uploadService;
        }

        /// <summary>
        /// Adds announcement
        /// </summary>
        /// <param name="model">The announcement creation model</param>
        /// <returns></returns>       
        /// 
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        public async Task<IActionResult> Add(AddAnnouncementDto model)
        {
            string userId = AuthHelper.GetUserInfo(HttpContext.Request, ClaimTypes.NameIdentifier);
            return Ok(await _announcementService.AddAsync(model, userId));
        }

        /// <summary>
        /// Edits announcements
        /// </summary>
        /// <param name="model">The announcement editing model</param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        public async Task<IActionResult> Edit(EditAnnouncementDto model)
        {
            string userId = AuthHelper.GetUserInfo(HttpContext.Request, ClaimTypes.NameIdentifier);
            return Ok(await _announcementService.EditAsync(model, userId));
        }

        /// <summary>
        /// Gets announcement with id specified
        /// </summary>
        /// <param name="id">The announcement indentifier</param>
        /// <returns></returns>    
        /// 
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        public async Task<IActionResult> Get(int id)
        {
            return Ok(await _announcementService.GetAsync(id));
        }

        /// <summary>
        /// Deletes announcement with id specified
        /// </summary>
        /// <param name="id">The announcement identifier</param>
        /// <returns></returns>
        /// 
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpDelete]
        public async Task<IActionResult> Delete(int id)
        {
            await _announcementService.DeleteAsync(id);
            return Ok();
        }

        /// <summary>
        /// Gets the announcement list
        /// </summary>
        /// <param name="count">The count to take</param>
        /// <param name="offset">The offset to skip</param>
        /// <param name="isActive">The flag for active</param>
        /// <param name="regionId">The region identifier</param>
        /// <param name="stateId">The state identifier</param>
        /// <returns></returns>
        [HttpGet]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> List(int count = 10, int offset = 0, bool? isActive = null, string regionId = null, string stateId = null)
        {
            if (regionId == "null")
                regionId = null;

            if (stateId == "null")
                stateId = null;

            return Ok(await _announcementService.ListAsync(count, offset, isActive, regionId, stateId));
        }

        /// <summary>
        /// Upload Announcement icon
        /// </summary>
        /// <returns></returns>
        [HttpPost]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> UploadIcon()
        {
            if (!(HttpContext.Request.HasFormContentType && HttpContext.Request.Form.Files.Any()))
                return BadRequest("Mime Multipart Content missing");

            var iconUrl = await _uploadService.SaveIcoToBlobAsync(HttpContext.Request.Form.Files[0]);
            if (string.IsNullOrEmpty(iconUrl))
                return BadRequest("Something went wrong");

            return Ok(new { iconUrl });
        }
    }
}