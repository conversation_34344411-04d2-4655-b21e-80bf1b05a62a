﻿@using System.Configuration;
@using BIMsmithMarket.Core.Helpers
@model BIMsmithBlog.Models.BlogDetailsModel


<header class="header-wrap">
    <div class="header">
        <i class="fa fa-search mob" id="mob-search-btn"></i>
        <a href="#" class="menu"><span></span><span></span><span></span>menu</a>
        <div class="top clear">
            <a class="logo" href="@ConfigurationHelper.GetValue("BimsmithUrl")" target="_blank"><img src="~/images/logo.png" alt=""></a>
            <div class="sign-in">
                <div style="display: none;" id="user-name"></div>
                <div style="display: none;" id="head-drop" class="head-drop">
                    <div class="av">
                        <img id="profile-image" alt="" src="/images/img01.jpg" height="383" width="383">
                        <div id="settings-name" class="name"></div>
                    </div>
                    <div class="links">
                        <a href="@ConfigurationHelper.GetValue("MarketUrl")/">Market</a>
                        <a href="@ConfigurationHelper.GetValue("ForgeUrl")/">forge</a>
                        <a href="@ConfigurationHelper.GetValue("BimsmithUrl")/NewMyBIMSmith/settings">MyBIMsmith</a>
                    </div>
                </div>
                <a id="logout-button" style="display: none;" class="def-btn logout-button" href="javascript:void(0)">Log out</a>
                <a id="login-button" style="display: inline-block;" class="def-btn" href="@ConfigurationHelper.GetValue("BimsmithUrl")/Account/Login/">Log in</a>
                <a id="signup-button" style="display: inline-block;" class="g-btn" href="@ConfigurationHelper.GetValue("BimsmithUrl")/Account/Register">Sign up</a>
            </div>
        </div>
        <div class="bot clear">
            <ul class="links clear">
                <li><a href="/">All</a></li>
                @foreach (var category in Model.Categories)
                {
                    if (category.IsActive)
                    {
                        <li class="active"><a href="/blog/category/@category.VanityId">@category.Name</a></li>
                    }
                    else
                    {
                        <li><a href="/blog/category/@category.VanityId">@category.Name</a></li>
                    }
                }
                <li id="mob-login"><a href="@ConfigurationHelper.GetValue("BimsmithUrl")/Account/Login/">Sign In</a></li>
                <li id="mob-regis"><a href="@ConfigurationHelper.GetValue("BimsmithUrl")/Account/Register">Sign Up</a></li>
            </ul>
            <div class="search">
                <i class="fa fa-search"></i>
                <input type="search">
            </div>
            <div class="search mob">
                <input id="mob-search" type="search">
            </div>
        </div>
    </div>
</header>
<div class="main details">
    <div class="breadcrumbs-block" id="category-block">
        <a class="breadcrumb-item" href="/">All</a>
        <div class="breadcrumb-item-divider">></div>
        <a id="category-name" class="breadcrumb-item capitalize" href="/blog/category/@Model.Category.VanityId">@Model.Category.Name</a>
        <div class="breadcrumb-item-divider">></div>
        <a class="breadcrumb-item" href="/@Model.VanityId">@Model.Title</a>
    </div>
    <div id="subscribe">
        <i class="fa fa-times sub-close"></i>
        <div class="sub-img-block">
            <img class="sub-img" src="~/images/envelope.svg" alt="Mail" />
            <h4 class="sub-title-xs">Stay up to date on the latest in BIM, Revit, and Architecture.</h4>
        </div>
        <div class="sub-block">
            <h4>Stay up to date on the latest in BIM, Revit, and Architecture.</h4>
            <div class="footer sub-footer">
                <div class="clear">
                    @using (Html.BeginForm("Subscribe", "Blog", FormMethod.Post, new { id = "logoutForm" }))
                    {
                        <div class="sub clear">
                            <button type="submit">Subscribe</button>
                            <input type="email" name="email" placeholder="Enter your email here" required>
                        </div>
                    }
                </div>
            </div>
        </div>
        <div class="footer sub-footer sub-footer-xs">
            <div class="clear">
                @using (Html.BeginForm("Subscribe", "Blog", FormMethod.Post, new { id = "logoutForm" }))
                {
                    <div class="sub clear">
                        <button type="submit">Subscribe</button>
                        <input type="email" name="email" placeholder="Enter your email here" required>
                    </div>
                }
            </div>
        </div>

    </div>
    <div class="block">
        <h2>@Model.Title</h2>
        <p class="author-title">
            <img class="head-img" src="@Model.AuthorImage" />
            @Model.AuthorTitle
        </p>
        <div class="btns center">
            <a class="n-green-btn" href="/blog/category/@Model.Category.VanityId">@Model.Category.Name</a>
            @if (Model.PublishedDate.HasValue)
            {
                <span class="n-gray-btn" href="">@Model.PublishedDate.Value.ToString("d")</span>
            }
            else
            {
                <span class="n-gray-btn" href="">Not published</span>
            }
        </div>
        <div class="hero-img">
            <img src="@Model.ImageUrlBig" alt="">
        </div>

    </div>
    <div class="content">
        @Html.Raw(Model.HtmlBody)
    </div>

    <div class="comments">
        <div class="com clear">
            @using (Html.BeginForm("AddComment", "Blog", FormMethod.Post, new { id = "addCommentForm" }))
            {
                <h5>Comment</h5>
                <input type="hidden" name="vanityId" value="@Model.VanityId" />

                if (ViewBag.IsLogged)
                {
                    <textarea name="comment" id="" cols="30" rows=""></textarea>
                    <button type="submit">Add</button>
                    <div class="captcha-block">
                        <div class="g-recaptcha" data-sitekey="6Lf52q8ZAAAAACkC_uWLqH28KyE-rXgfN0LMigxx"></div>
                    </div>
                }
                else
                {
                    <textarea name="comment" id="" cols="30" rows="" disabled>Please login or register to post a comment</textarea>
                    <button type="submit" disabled="disabled">Add</button>
                }
            }
        </div>
        <div class="answ">
            @foreach (var comment in Model.BlogComments)
            {
                <div class="box">
                    <h4>@comment.OwnerName, @comment.Date.ToString()</h4>
                    <p>@comment.Comment</p>
                </div>
            }
        </div>
    </div>
    <div class="articles">
        <ul class="art-list">
            @foreach (var blogItem in Model.OtherBlogs)
            {
                <li>
                    <a href="/@blogItem.VanityId">
                        <img src="@blogItem.ImageUrlSmall" alt="">
                    </a>
                    <div class="btns center">
                        <a class="n-green-btn" href="/blog/category/@blogItem.Category.VanityId">@blogItem.Category.Name</a>
                        <span class="n-gray-btn" href="">@blogItem.PublishedDate.ToString("d")</span>
                    </div>
                    <a href="/@blogItem.VanityId"><h3>@blogItem.Title</h3></a>
                    <p>@blogItem.Descriptions</p>
                    <a class="more" href="/@blogItem.VanityId">read more</a>
                </li>
            }
        </ul>
    </div>
</div>