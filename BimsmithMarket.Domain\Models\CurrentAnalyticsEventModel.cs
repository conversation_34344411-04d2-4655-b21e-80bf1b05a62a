﻿using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Models
{
    public class CurrentAnalyticsEventModel
    {
        [Required]
        public int ProjectId { get; set; }

        [Required]
        [StringLength(36)]
        public string UID { get; set; }

        [StringLength(100)]
        public string Email { get; set; }

        [Required]
        [StringLength(7)]
        public string Cult { get; set; }

        public int Duration { get; set; }

        [StringLength(100)]
        public string City { get; set; }
        public int? ManufacturerId { get; set; }

        [Required]
        public long UserVisitId { get; set; }

        [Required]
        public string UrlLocation { get; set; }
        [Required]
        [StringLength(100)]
        public string EventCategory { get; set; }

        [Required]
        [StringLength(100)]
        public string EventAction { get; set; }

        [StringLength(200)]
        public string EventTitle { get; set; }

        public string EventValue { get; set; }

        [StringLength(4)]
        public string CountryCode { get; set; }

        [StringLength(100)]
        public string Country { get; set; }

        [StringLength(100)]
        public string Region { get; set; }
    }
}