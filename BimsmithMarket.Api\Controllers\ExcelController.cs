﻿using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    public class ExcelController : BaseApiController
    {
        //Static excel import service
        private readonly IStaticExcelImportService _staticExcelImportService;

        public ExcelController(IStaticExcelImportService staticExcelImportService)
        {
            _staticExcelImportService = staticExcelImportService;
        }

        /// <summary>
        /// Uploads file for import
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier</param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        [Route("/api/StaticExcel/Import")]
        public async Task<IActionResult> StaticExcelImport(int manufacturerId)
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);

#if DEBUG
            if (string.IsNullOrWhiteSpace(userId))
                userId = DbConstants.AdminUserId;
#endif
            if (!(Request.HasFormContentType && Request.Form.Files.Any()))
                return BadRequest("Please attach file");

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _staticExcelImportService.UploadFileAsync(Request.Form.Files[0], manufacturerId, userId, unitOfWork));
            }
        }

        /// <summary>
        /// Canceks import for specified file
        /// </summary>
        /// <param name="id">The file identifier</param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        [Route("/api/StaticExcel/CancelImport")]
        public async Task<IActionResult> StaticExcelCancelImport(int id)
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);

#if DEBUG
            if (string.IsNullOrWhiteSpace(userId))
                userId = DbConstants.AdminUserId;
#endif

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                await _staticExcelImportService.CancelImportAsync(id, userId, unitOfWork);
                return Ok();
            }
        }

        /// <summary>
        /// Returns list of static excel files
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier</param>
        /// <param name="status">The file processing file</param>
        /// <param name="count">The count to take</param>
        /// <param name="offset">The offset to skip</param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        [Route("/api/StaticExcel/List")]
        public async Task<IActionResult> StaticExcelList(
            int? manufacturerId = null,
            ExcelProcessingStatus? status = null,
            int count = 50,
            int offset = 0)
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);

#if DEBUG
            if (string.IsNullOrWhiteSpace(userId))
                userId = DbConstants.AdminUserId;
#endif

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _staticExcelImportService.ListAsync(userId, unitOfWork, manufacturerId, status, count, offset));
            }
        }
    }
}