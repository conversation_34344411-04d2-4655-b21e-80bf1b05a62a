﻿using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface ICategoryService
    {
        Task<dynamic> AddAsync(AddCategoryModel model, string webRootPath, string userId, IUnitOfWork unitOfWork);

        Task<OperationResultDto> EditAsync(EditCategoryModel model, string webRootPath, string userId, IUnitOfWork unitOfWork);

        Task<dynamic> GetBimsmithVanityUrlPairs();

        Task<CategorySEODto> GetVanityUrlByBimsmithUrl(string bimsmithUrl);

        Task<BaseNameListDto[]> GetNamesListAsync(EntityIdsDto model, IUnitOfWork unitOfWork);

        Task<string> ExcelExportAsync(IUnitOfWork unitOfWork);

        Task<CategoryExcelImportResultDto> ImportExcelAsync(IFormFile formFile, string userId, IUnitOfWork unitOfWork);

        Task<OperationResultDto> DeleteAsync(DeleteCategoryModel model, string userId, IProductService productService, IUnitOfWork unitOfWork);

        Task<List<dynamic>> TreeAsync(
            IUnitOfWork unitOfWork,
            int manufacturerId = -1,
            string manufacturerName = null,
            bool isStaging = false,
            string langCode = null);

        List<int> GetAllCategoryHierarchyIds(int categoryId, CategoryTreeDto[] allCategories);
    }
}