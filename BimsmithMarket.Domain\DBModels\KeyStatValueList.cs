﻿using Microsoft.EntityFrameworkCore;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{

    [Index(nameof(Value), Name = "IX_KeyStatValueList_Value")]
    [Index(nameof(ConvertValue), Name = "IX_KeyStatValueList_ConvertValue")]
    public class KeyStatValueList
    {
        public int Id { get; set; }

        public int? ProductStatsId { get; set; }

        public int? ProductLineStatsId { get; set; }

        public int? KeyStatUnitId { get; set; }

        public int? ConvertKeyStatUnitId { get; set; } //New

        /// <summary>
        /// Parsed value as float
        /// </summary>
        public float? FloatValue { get; set; }

        /// <summary>
        /// Parsed min range value as float
        /// </summary>
        public float? FloatMinRangeValue { get; set; }

        /// <summary>
        /// Parsed max range value as float
        /// </summary>
        public float? FloatMaxRangeValue { get; set; }


        [StringLength(200)]
        public string Value { get; set; }

        public string MinRangeValue { get; set; }

        public string MaxRangeValue { get; set; }


        [StringLength(200)]
        public string ConvertValue { get; set; }

        public string ConvertMinRangeValue { get; set; }

        public string ConvertMaxRangeValue { get; set; }

        public string Note { get; set; }


        public string CreatedById { get; set; }

        public DateTime CreatedDate { get; set; }

        /// ------------------------------------------
        [ForeignKey("CreatedById")]
        public virtual ApplicationUser CreatedBy { get; set; }

        [ForeignKey("ProductStatsId")]
        public virtual ProductStats ProductStats { get; set; }

        [ForeignKey("ProductLineStatsId")]
        public virtual ProductLineStats ProductLineStats { get; set; }

        [ForeignKey("KeyStatUnitId")]
        public virtual KeyStatUnit KeyStatUnit { get; set; }

        [ForeignKey("ConvertKeyStatUnitId")]
        public virtual KeyStatUnit ConvertKeyStatUnit { get; set; }

        public KeyStatValueList()
        {

        }
    }
}