﻿namespace BIMsmithMarket.Services.Helpers
{
    /// <summary>
    /// 
    /// </summary>
    public static class MetaDataHelper
    {
        /// <summary>
        /// Get default meta data for Product
        /// </summary>
        /// <param name="productName"></param>
        /// <param name="mainCatergoryName"></param>
        /// <param name="manufacturerName"></param>
        /// <returns></returns>
        public static MetaDataModel GetProductMetaData(string productName, string mainCatergoryName, string manufacturerName)
        {
            var result = new MetaDataModel();
            result.Title = $"Free {mainCatergoryName} Revit Download – {productName} – BIMsmith Market";
            result.Description = $"Free Revit Family Download for {manufacturerName} {productName}. Download BIM Content, Cut Sheets, Specs, Installation Guides and More in the Cloud with BIMsmith Market. Download Now For Free.";
            result.Keywords = $"{manufacturerName},{productName},bim,revit,family,file,download,bimsmith,market";
            return result;
        }

        /// <summary>
        /// Get default meta data for Manufacturer
        /// </summary>
        /// <param name="manufacturerName"></param>
        /// <returns></returns>
        public static MetaDataModel GetManufacturerMetaData(string manufacturerName)
        {
            var result = new MetaDataModel();
            result.Title = $"{manufacturerName} Revit Families & BIM Content – BIMsmith Market";
            result.Description = $"Download {manufacturerName} Revit Families, Cut Sheets, Specs, Installation Guides, and More For Free With BIMsmith. Get The Highest Quality BIM Content & Product Data You Need From The Manufacturers You Trust. Start Downloading Now.";
            result.Keywords = $"free,revit,files,bim,bimsmith,{manufacturerName}";
            return result;
        }


        /// <summary>
        /// Get default meta data for Category
        /// </summary>
        /// <param name="categoryName"></param>
        /// <returns></returns>
        public static MetaDataModel GetCategoryMetaData(string categoryName)
        {
            var result = new MetaDataModel();
            result.Title = $"{categoryName}  Revit Families – Download Free BIM Content – BIMsmith Market";
            result.Description = $"Download {categoryName} Revit Files For Free With BIMsmith. Get The Highest Quality BIM Content You Need From The Manufacturers You Trust. Start Downloading Now.";
            result.Keywords = $"free,revit,files,bim,bimsmith,{categoryName}";

            return result;
        }

    }

    public class MetaDataModel
    {
        public string Title { get; set; }

        public string Description { get; set; }

        public string Keywords { get; set; }
    }
}