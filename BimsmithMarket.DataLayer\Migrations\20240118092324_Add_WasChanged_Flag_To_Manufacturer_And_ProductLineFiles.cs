﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BIMsmithMarket.DataLayer.Migrations
{
    /// <inheritdoc />
    public partial class Add_WasChanged_Flag_To_Manufacturer_And_ProductLineFiles : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "WasChanged",
                table: "ProductLineFiles",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "WasChanged",
                table: "ManufacturerFiles",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.Sql(@"UPDATE ProductLineFiles SET WasChanged = 1");

            migrationBuilder.Sql(@"UPDATE ManufacturerFiles SET WasChanged = 1");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "WasChanged",
                table: "ProductLineFiles");

            migrationBuilder.DropColumn(
                name: "WasChang<PERSON>",
                table: "ManufacturerFiles");
        }
    }
}
