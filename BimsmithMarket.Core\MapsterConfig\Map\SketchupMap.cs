﻿using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto.SketchupDto;
using Mapster;
using System.Linq;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public class SketchupMap
    {
        public static void Register(TypeAdapterConfig config)
        {
            config.ForType<Product, SketchupProductDto>()
                .Map(d => d.SkecthupFileUrl, s => s.ProductFiles.First(p => p.ProjectDataTypeId == SketchupConstants.SkecthupProjectDataTypeId).File.Url)
                .Map(d => d.SketchupFileName, s => s.ProductFiles.First(p => p.ProjectDataTypeId == SketchupConstants.SkecthupProjectDataTypeId).File.FileName)
                .Map(d => d.ThumbnailFileUrl, s => s.ProductPhotos.First().Photo.OriginalImgUrl)
                .Map(d => d.ThumbnailFileName, s => s.ProductPhotos.First().Photo.Name)
                .Map(d => d.Title, s => s.Name)
                .Map(d => d.Description, s => s.Description.Replace("\n", "").Replace("\r", ""))
                .AfterMapping((s, d) =>
                {
                    if (!string.IsNullOrWhiteSpace(d.ThumbnailFileName) && d.ThumbnailFileName.Contains("?"))
                        d.ThumbnailFileName = d.ThumbnailFileName.Remove(d.ThumbnailFileName.LastIndexOf('?'));
                });
        }
    }
}