﻿
using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.ChangeLog;
using BIMsmithMarket.Domain.Dto.ProductLineExcelDto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models.MassTransit.Events;
using BIMsmithMarket.Services.Interfaces;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using Flurl;
using Mapster;
using MassTransit;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class ProductLineExcelService : IProductLineExcelService
    {
        private readonly IChangeLogService _changeLogService;
        private readonly IProductLineService _productLineService;
        private readonly IPublishEndpoint _publishEndpoint;

        public ProductLineExcelService(
            IChangeLogService changeLogService,
            IProductLineService productLineService,
            IPublishEndpoint publishEndpoint)
        {
            _changeLogService = changeLogService;
            _productLineService = productLineService;
            _publishEndpoint = publishEndpoint;
        }

        public async Task<string> GetProductLineExcelAsync(int manufacturerId, IUnitOfWork unitOfWork)
        {
            ProductLineExcelDto[] productLines = await GetProductLinesForExportAsync(manufacturerId, unitOfWork);
            string filePath = Path.Combine(Path.GetTempPath(), $"ProductLineExport_{Guid.NewGuid()}.xlsx");
            SpreadsheetDocument spreadsheetDocument;
            WorkbookPart workbookpart;
            WorksheetPart worksheetPart;
            CommonExcelProvider.CreateDocument(filePath, out spreadsheetDocument, out workbookpart, out worksheetPart);

            //columns
            Columns columns = new Columns();
            uint headerColumnIndex = 1;
            int columnsCount = 7 + AttachmentConstants.AttachmentTitles.Count;
            for (int i = 0; i < columnsCount; i++)
                columns.AppendChild(new Column { Min = headerColumnIndex, Max = headerColumnIndex++, Width = 25, CustomWidth = true });
            worksheetPart.Worksheet.AppendChild(columns);
            workbookpart.Workbook.Save();

            CommonExcelProvider.CreateSheet(spreadsheetDocument, workbookpart, worksheetPart, "Product Lines");

            // Get the sheetData cell table.
            SheetData sheetData = worksheetPart.Worksheet.AppendChild(new SheetData());
            int rowIndex = 1;
            Row rowHeader = new Row();
            rowHeader.Height = 90;
            rowHeader.CustomHeight = true;
            sheetData.AppendChild(rowHeader);

            int columnIndex = 1;

            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(ProductLineExcelConstants.IdCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(ProductLineExcelConstants.NameCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(ProductLineExcelConstants.DescriptionCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(ProductLineExcelConstants.NotesCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(ProductLineExcelConstants.CertificatesCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(ProductLineExcelConstants.QualityItemsCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(ProductLineExcelConstants.ForgeProductLinesCaption, CellValues.String, columnIndex++, rowIndex, 2));
            foreach (string title in AttachmentConstants.AttachmentTitles)
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell($"{title} Urls", CellValues.String, columnIndex++, rowIndex, 2));

            rowIndex++;

            foreach (ProductLineExcelDto productLine in productLines)
            {
                Row row = new Row();
                sheetData.AppendChild(row);

                columnIndex = 1;

                row.AppendChild(CommonExcelProvider.ConstructCell(productLine.Id.ToString(), CellValues.Number, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(productLine.Name, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(productLine.Description, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(productLine.Notes, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(ProductLineExcelConstants.ItemsDelimeterString, productLine.CertificateIds), CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(ProductLineExcelConstants.ItemsDelimeterString, productLine.QualityItemIds), CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(ProductLineExcelConstants.ItemsDelimeterString, productLine.ForgeProductLineIds), CellValues.String, columnIndex++, rowIndex));
                foreach (string title in AttachmentConstants.AttachmentTitles)
                    row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(ProductLineExcelConstants.ItemsDelimeterString, productLine.ExternalAttachments.Where(x => x.Title == title).Select(x => x.SyncUrl)), CellValues.String, columnIndex++, rowIndex));

                rowIndex++;
            }

            workbookpart.Workbook.Save();

            spreadsheetDocument.Dispose();

            return filePath;
        }

        public async Task<ProductLineExcelImportResultDto> ImportProductLineExcelAsync(int manufacturerId, IFormFile formFile, string userId, IUnitOfWork unitOfWork)
        {
            if (!await unitOfWork.ManufacturerRepository.GetAll().AnyAsync(x => x.Id == manufacturerId))
                throw new InvalidInputException("Manufacturer does not exist");

            List<ProductLineExcelDto> parsedProductLines = await ParseProductLinesAsync(formFile);
            List<string> errors = parsedProductLines.SelectMany(x => x.Errors).ToList();
            List<ProductLineExcelDto> parsedProductLineWithoutErrors = parsedProductLines.Where(x => !x.Errors.Any()).ToList();

            foreach (ProductLineExcelDto parsedProductLine in parsedProductLineWithoutErrors)
            {
                await ImportSingleProductLineAsync(parsedProductLine, manufacturerId, userId, errors, unitOfWork);
            }

            return new ProductLineExcelImportResultDto
            {
                Errors = errors
            };
        }

        #region private methods
        private async Task<ProductLineExcelDto[]> GetProductLinesForExportAsync(int manufacturerId, IUnitOfWork unitOfWork)
        {
            return await unitOfWork.ProductLineRepository.GetAllAsNoTracking()
                .Where(x => x.ManufacturerId == manufacturerId)
                .Select(x => new ProductLineExcelDto
                {
                    Id = x.Id,
                    CertificateIds = x.ProductLineCertificates.Where(c => c.ExternalCertificateId != null).Select(c => c.ExternalCertificateId.Value),
                    Description = x.Description,
                    ExternalAttachments = x.ProductLineFiles.Where(f => f.IsAttachment && !(f.File.SyncUrl == string.Empty || f.File.SyncUrl == null))
                        .Select(f => new ProductLineExternalAttachmentDto
                        {
                            SyncUrl = f.File.SyncUrl,
                            Title = f.File.Title
                        }),
                    ForgeProductLineIds = x.ForgeProductLineIds.Select(f => f.ForgeProductLineId),
                    Name = x.Name,
                    Notes = x.Note,
                    QualityItemIds = x.ProductLineQualityItems.Select(q => q.QualityItemId)
                })
                .ToArrayAsync();
        }

        private async Task<List<ProductLineExcelDto>> ParseProductLinesAsync(IFormFile formFile)
        {
            using (Stream stream = formFile.OpenReadStream())
            {
                SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Open(stream, false);
                WorkbookPart workbookpart = spreadsheetDocument.WorkbookPart;
                Workbook workbook = workbookpart.Workbook;
                IEnumerable<Sheet> sheets = workbook.Descendants<Sheet>();
                WorksheetPart worksheetPart = (WorksheetPart)workbookpart.GetPartById(sheets.First().Id);
                SharedStringTablePart sharedStringPart = workbookpart.SharedStringTablePart;

                List<Row> rows = worksheetPart.Worksheet.Descendants<Row>().ToList();
                Row headerRow = rows.FirstOrDefault();
                if (headerRow == null)
                    throw new InvalidInputException("Can't to found Header row");

                List<string> errors = new List<string>();
                List<Cell> headerCells = headerRow.Elements<Cell>().ToList();
                int cellIndex = 0;
                CommonExcelProvider.ValidCellValue(headerCells[cellIndex++], ProductLineExcelConstants.IdCaption, sharedStringPart, errors, $"Wrong position of column with name '{ProductLineExcelConstants.IdCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells[cellIndex++], ProductLineExcelConstants.NameCaption, sharedStringPart, errors, $"Wrong position of column with name '{ProductLineExcelConstants.NameCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells[cellIndex++], ProductLineExcelConstants.DescriptionCaption, sharedStringPart, errors, $"{ProductLineExcelConstants.DescriptionCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells[cellIndex++], ProductLineExcelConstants.NotesCaption, sharedStringPart, errors, $"Wrong position of column with name '{ProductLineExcelConstants.NotesCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells[cellIndex++], ProductLineExcelConstants.CertificatesCaption, sharedStringPart, errors, $"Wrong position of column with name '{ProductLineExcelConstants.CertificatesCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells[cellIndex++], ProductLineExcelConstants.QualityItemsCaption, sharedStringPart, errors, $"Wrong position of column with name '{ProductLineExcelConstants.QualityItemsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells[cellIndex++], ProductLineExcelConstants.ForgeProductLinesCaption, sharedStringPart, errors, $"Wrong position of column with name '{ProductLineExcelConstants.ForgeProductLinesCaption}'");
                foreach (string title in AttachmentConstants.AttachmentTitles)
                    CommonExcelProvider.ValidCellValue(headerCells[cellIndex++], $"{title} Urls", sharedStringPart, errors, $"Wrong position of column with name '{title} Urls'");

                if (errors.Any())
                    throw new InvalidInputException(string.Join(Environment.NewLine, errors));

                List<ProductLineExcelDto> productLines = new List<ProductLineExcelDto>();
                int rowIndex = 2;
                foreach (var row in rows.Skip(1)) // skip header row
                {
                    cellIndex = 1;
                    var cells = row.Elements<Cell>().ToList();

                    if (!cells.Any() || cells.All(x => !CommonExcelProvider.HasCellValue(x, sharedStringPart)))
                    {
                        rowIndex++;
                        continue;
                    }

                    ProductLineExcelDto productLine = new ProductLineExcelDto();
                    productLine.RowNumber = rowIndex;
                    productLine.Id = CommonExcelProvider.GetCellNullIntValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart, errors);
                    cellIndex++;
                    productLine.Name = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                    cellIndex++;

                    if (string.IsNullOrWhiteSpace(productLine.Name))
                        productLine.Errors.Add($"Product Line Name must have value Row Index {productLine.RowNumber}");

                    productLine.Description = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                    cellIndex++;
                    productLine.Notes = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                    cellIndex++;
                    string certificateIdsString = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                    if (!string.IsNullOrWhiteSpace(certificateIdsString))
                        productLine.CertificateIds = certificateIdsString.Split(ProductLineExcelConstants.ItemsDelimeterChar).Select(x => int.Parse(x)).ToList();
                    cellIndex++;
                    string qualityItemIds = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                    if (!string.IsNullOrWhiteSpace(qualityItemIds))
                        productLine.QualityItemIds = qualityItemIds.Split(ProductLineExcelConstants.ItemsDelimeterChar).Select(x => int.Parse(x)).ToList();
                    cellIndex++;
                    string forgeProductLineIdsString = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                    if (!string.IsNullOrWhiteSpace(forgeProductLineIdsString))
                        productLine.ForgeProductLineIds = forgeProductLineIdsString.Split(ProductLineExcelConstants.ItemsDelimeterChar).ToList();
                    cellIndex++;
                    List<ProductLineExternalAttachmentDto> attachments = new List<ProductLineExternalAttachmentDto>();
                    foreach (string title in AttachmentConstants.AttachmentTitles)
                    {
                        string urlsString = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        if (!string.IsNullOrWhiteSpace(urlsString))
                            attachments.AddRange(urlsString.Split(ProductLineExcelConstants.ItemsDelimeterChar).Select(x => new ProductLineExternalAttachmentDto
                            {
                                SyncUrl = x,
                                Title = title
                            }).ToList());
                        cellIndex++;
                    }
                    productLine.ExternalAttachments = attachments;
                    foreach (string url in productLine.ExternalAttachments.Select(x => x.SyncUrl))
                    {
                        if (!await url.IsActiveUrlAsync())
                            productLine.Errors.Add($"Invalid url {url} for Product Line with Row Index {productLine.RowNumber}");
                    }
                    productLines.Add(productLine);
                    rowIndex++;
                }

                return productLines;
            }
        }

        private async Task ImportSingleProductLineAsync(ProductLineExcelDto parsedProductLine, int manufacturerId, string userId, List<string> errors, IUnitOfWork unitOfWork)
        {
            try
            {
#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Started processing for product line with row index {parsedProductLine.RowNumber}");
#endif
                unitOfWork.BeginTransaction();
                bool isNewProductLine = !parsedProductLine.Id.HasValue;
                ProductLine productLine = new ProductLine();
                ProductLineChangeLogDto currentModel = new();

                if (isNewProductLine)
                {
                    productLine = parsedProductLine.Adapt<ProductLine>();
                    productLine.ManufacturerId = manufacturerId;
                    productLine.CreatedById = userId;
                    productLine.CreatedDate = DateTime.UtcNow;
                    unitOfWork.ProductLineRepository.Insert(productLine);
                    await unitOfWork.SaveAsync();
                }
                else
                {
                    productLine = await unitOfWork.ProductLineRepository.GetByIdAsync(parsedProductLine.Id.Value);
                    if (productLine == null)
                        throw new InvalidInputException("Product line not found");

                    if (productLine.ManufacturerId != manufacturerId)
                        throw new InvalidInputException("Product line attachment to another manufacturer is not allowed");

                    currentModel = await _productLineService.GetModelForChangeLogAsync(productLine.Id, unitOfWork);

                    parsedProductLine.Adapt(productLine);
                    unitOfWork.ProductLineRepository.Edit(productLine);
                    await unitOfWork.SaveAsync();
                }
#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Passed insert or update for product line with row index {parsedProductLine.RowNumber}");
#endif
                await AddOrUpdateProductLineCertificatesAsync(productLine, parsedProductLine, userId, unitOfWork);
#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Passed insert or update of certificates for product line with row index {parsedProductLine.RowNumber}");
#endif
                await AddOrUpdateProductLineQualityItemsAsync(productLine, parsedProductLine, userId, unitOfWork);
#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Passed insert or update of quality items for product line with row index {parsedProductLine.RowNumber}");
#endif
                await AddOrUpdateProductLineForgeProductLinesAsync(productLine, parsedProductLine, userId, unitOfWork);
#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Passed insert or update of forge product lines for product line with row index {parsedProductLine.RowNumber}");
#endif
                foreach (string title in AttachmentConstants.AttachmentTitles)
                    await AddOrUpdateProductLineExternalAttachmentsAsync(productLine, parsedProductLine, title, userId, unitOfWork);
#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Passed insert or update of external attachments for product line with row index {parsedProductLine.RowNumber}");
#endif
                await SetAttachmentsWeightAsync(productLine, unitOfWork);
#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Passed attachments weight for product line with row index {parsedProductLine.RowNumber}");
#endif
                await unitOfWork.SaveAsync();
                unitOfWork.CommitTransaction();
#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Passed insert or update for product line with row index {parsedProductLine.RowNumber}");
#endif
                if (isNewProductLine)
                    await _changeLogService.LogEntityActionAsync(EntityType.ProductLine, productLine.Id, EntityAction.Add, userId, unitOfWork);
                else
                {
                    ProductLineChangeLogDto newModel = await _productLineService.GetModelForChangeLogAsync(productLine.Id, unitOfWork);
                    await _changeLogService.TrackAndLogChangesAsync(currentModel, newModel, EntityType.ProductLine, productLine.Id, userId, unitOfWork);
                    await _publishEndpoint.Publish(new ProductLineChangedEvent
                    {
                        ProductLineId = productLine.Id
                    });
                }
            }
            catch (Exception ex)
            {
#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Failed insert or update for product line with row index {parsedProductLine.RowNumber}");
#endif
                unitOfWork.RollbackTransaction();
                errors.Add($"Error to create or update product line with Row Index {parsedProductLine.RowNumber}: {ex.GetAllMessages()}");
            }
        }

        private async Task AddOrUpdateProductLineCertificatesAsync(ProductLine productLine, ProductLineExcelDto parsedProductLine, string userId, IUnitOfWork unitOfWork)
        {
            List<int> existingItemIds = await unitOfWork.ProductLineCertificateRepository.GetAll()
                .Where(x => x.ProductLineId == productLine.Id)
                .Select(x => x.ExternalCertificateId.Value)
                .ToListAsync();

            List<int> itemIdsToAdd = parsedProductLine.CertificateIds.Except(existingItemIds).ToList();
            List<int> itemIdsToDelete = existingItemIds.Except(parsedProductLine.CertificateIds).ToList();

            foreach (int itemId in itemIdsToAdd)
            {
                ProductLineCertificate productLineCertificate = new ProductLineCertificate
                {
                    ProductLineId = productLine.Id,
                    ExternalCertificateId = itemId,
                    CreatedById = userId,
                    CreatedDate = DateTime.UtcNow
                };

                unitOfWork.ProductLineCertificateRepository.Insert(productLineCertificate);
            }

            ProductLineCertificate[] itemsToDelete = await unitOfWork.ProductLineCertificateRepository.GetAll()
                .Where(x => x.ProductLineId == productLine.Id && x.ExternalCertificateId != null && itemIdsToDelete.Contains(x.ExternalCertificateId.Value))
                .ToArrayAsync();
            unitOfWork.ProductLineCertificateRepository.Delete(itemsToDelete);
        }

        private async Task AddOrUpdateProductLineQualityItemsAsync(ProductLine productLine, ProductLineExcelDto parsedProductLine, string userId, IUnitOfWork unitOfWork)
        {
            List<int> existingItemIds = await unitOfWork.ProductLineQualityItemRepository.GetAll()
                .Where(x => x.ProductLineId == productLine.Id)
                .Select(x => x.QualityItemId)
                .ToListAsync();

            List<int> itemIdsToAdd = parsedProductLine.QualityItemIds.Except(existingItemIds).ToList();
            List<int> itemIdsToDelete = existingItemIds.Except(parsedProductLine.QualityItemIds).ToList();

            foreach (int itemId in itemIdsToAdd)
            {
                ProductLineQualityItem productLineQualityItem = new ProductLineQualityItem
                {
                    ProductLineId = productLine.Id,
                    QualityItemId = itemId,
                    CreatedById = userId,
                    CreatedDate = DateTime.UtcNow
                };

                unitOfWork.ProductLineQualityItemRepository.Insert(productLineQualityItem);
            }

            ProductLineQualityItem[] itemsToDelete = await unitOfWork.ProductLineQualityItemRepository.GetAll()
                .Where(x => x.ProductLineId == productLine.Id && itemIdsToDelete.Contains(x.QualityItemId))
                .ToArrayAsync();
            unitOfWork.ProductLineQualityItemRepository.Delete(itemsToDelete);
        }

        private async Task AddOrUpdateProductLineForgeProductLinesAsync(ProductLine productLine, ProductLineExcelDto parsedProductLine, string userId, IUnitOfWork unitOfWork)
        {
            List<string> existingItemIds = await unitOfWork.ForgeProductLineIdsRepository.GetAll()
                .Where(x => x.ProductLineId == productLine.Id)
                .Select(x => x.ForgeProductLineId)
                .ToListAsync();

            List<string> itemIdsToAdd = parsedProductLine.ForgeProductLineIds.Except(existingItemIds).ToList();
            List<string> itemIdsToDelete = existingItemIds.Except(parsedProductLine.ForgeProductLineIds).ToList();

            foreach (string itemId in itemIdsToAdd)
            {
                ForgeProductLineIds productLineForgeProductLine = new ForgeProductLineIds
                {
                    ProductLineId = productLine.Id,
                    ForgeProductLineId = itemId
                };

                unitOfWork.ForgeProductLineIdsRepository.Insert(productLineForgeProductLine);
            }

            ForgeProductLineIds[] itemsToDelete = await unitOfWork.ForgeProductLineIdsRepository.GetAll()
                .Where(x => x.ProductLineId == productLine.Id && itemIdsToDelete.Contains(x.ForgeProductLineId))
                .ToArrayAsync();
            unitOfWork.ForgeProductLineIdsRepository.Delete(itemsToDelete);
        }

        private async Task AddOrUpdateProductLineExternalAttachmentsAsync(ProductLine productLine, ProductLineExcelDto parsedProductLine, string title, string userId, IUnitOfWork unitOfWork)
        {
            List<string> existingUrls = await unitOfWork.ProductLineFileRepository.GetAll()
                .Where(x => x.ProductLineId == productLine.Id
                         && x.IsAttachment
                         && !(x.File.SyncUrl == null || x.File.SyncUrl == string.Empty)
                         && x.File.Title == title)
                .Select(x => x.File.SyncUrl)
                .ToListAsync();

            List<string> parsedUrls = parsedProductLine.ExternalAttachments.Where(x => x.Title == title).Select(x => x.SyncUrl).ToList();
            List<string> urlsToAdd = parsedUrls.Except(existingUrls).ToList();
            List<string> urlsToDelete = existingUrls.Except(parsedUrls).ToList();

            foreach (string url in urlsToAdd)
            {
                string attachUrl = url;
                if (attachUrl[attachUrl.Length - 1] == '/')
                {
                    attachUrl = attachUrl.Remove(attachUrl.Length - 1);
                }
                if (!attachUrl.StartsWith("http"))
                {
                    attachUrl = "http://" + attachUrl;
                }

                HttpResponseHeadersInfo headers = await WepApiProvider.GetHeadersAsync(attachUrl);
                string fileName = headers.FileName ?? FileHelper.RemoveQueryPath(Path.GetFileName(attachUrl));
                string mediaType = MimeTypeProvider.GetMimeType(fileName);

                var dbFile = new Domain.DBModels.File();
                dbFile.FileName = fileName;
                dbFile.MediaType = mediaType;
                dbFile.CreatedById = userId;
                dbFile.Title = title;
                dbFile.CreatedDate = DateTime.UtcNow;
                dbFile.PreviewUrl = new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment("assets/img/default_preview.png");
                dbFile.NextSyncDateTime = DateTime.UtcNow.AddDays(-1);
                dbFile.SyncStatusCode = (int)HttpStatusCode.OK;
                dbFile.UpdatesCount = 0;
                dbFile.SyncUrl = attachUrl;

                dbFile.Url = attachUrl;
                dbFile.FileSize = 0;
                dbFile.CheckSum = string.Empty;

                unitOfWork.FileRepository.Insert(dbFile);
                unitOfWork.Save();

                ProductLineFile productFile = new ProductLineFile();
                productFile.ProductLineId = productLine.Id;
                productFile.FileId = dbFile.Id;
                productFile.IsAttachment = true;
                productFile.CreatedById = userId;
                productFile.CreatedDate = DateTime.UtcNow;
                productFile.WasChanged = true;
                unitOfWork.ProductLineFileRepository.Insert(productFile);
            }

            ProductLineFile[] itemsToDelete = await unitOfWork.ProductLineFileRepository.GetAll()
                .Where(x => x.ProductLineId == productLine.Id
                         && x.IsAttachment
                         && !(x.File.SyncUrl == null || x.File.SyncUrl == string.Empty)
                         && x.File.Title == title
                         && urlsToDelete.Contains(x.File.SyncUrl))
                .ToArrayAsync();
            unitOfWork.ProductLineFileRepository.Delete(itemsToDelete);
        }

        private async Task SetAttachmentsWeightAsync(ProductLine productLine, IUnitOfWork unitOfWork)
        {
            List<string> attachmentOrder = await unitOfWork.AttachmentOrderRepository.GetAll()
                .Where(a => a.ManufacturerId == productLine.ManufacturerId)
                .OrderBy(x => x.Order)
                .Select(b => b.Type)
                .ToListAsync();

            var productLineAttachments = productLine.ProductLineFiles.Where(x => x.IsAttachment)
                .GroupBy(a => new { Title = a.File?.Title ?? "", SyncUrl = a.File?.SyncUrl ?? "" })
                .Select(a => a)
                .ToList();

            if (attachmentOrder != null && attachmentOrder.Any())
                productLineAttachments = productLineAttachments.OrderBy(a => attachmentOrder.IndexOf(a.Key.SyncUrl)).ToList();
            else
                productLineAttachments = productLineAttachments.OrderBy(a => AttachmentOrderProvider.DefaultAttachmentsOrder.IndexOf(a.Key.Title)).ToList();

            int weight = 1;
            foreach (var productLineAttachment in productLineAttachments)
            {
                var groupAttachments = productLineAttachment.ToList();
                foreach (var attachment in groupAttachments)
                {
                    attachment.Weight = weight++;
                }
            }
        }

        #endregion
    }
}