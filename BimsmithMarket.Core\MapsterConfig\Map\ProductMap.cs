﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.DetailDto;
using BIMsmithMarket.Domain.Dto.Product;
using BIMsmithMarket.Domain.Dto.SwatchboxDto;
using Flurl;
using Mapster;
using System.Linq;
using System.Text.RegularExpressions;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public class ProductMap
    {
        public static void Register(TypeAdapterConfig config)
        {
            config.ForType<Product, SwatchboxProductsResponseDto>()
                .Map(d => d.ProductId, s => s.Id)
                .Map(d => d.ProductLink, s => new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment($"product/{s.Manufacturer.HubVanityURL}/revit-bim-{Regex.Replace(Regex.Replace(s.Name.Trim().Replace(' ', '-'), "([^()s/-Z0-9a-z_-]+)", ""), "[-]{2,}", "-")}-{s.Id}", false).ToString())
                .Map(d => d.ProductName, s => s.Name)
                .Map(d => d.ManufacturerName, s => s.Manufacturer.Name)
                .Map(d => d.ManufacturerLink, s => new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment($"/{s.Manufacturer.HubVanityURL}", false).ToString());

            config.ForType<Product, ProductMongoDto>()
                .Map(d => d.ProductRatingCount, s => s.ProductRatings.Where(b => b.Type == ProductRatingType.ProductRating).Count())
                .Map(d => d.ContentRatingCount, s => s.ProductRatings.Where(b => b.Type == ProductRatingType.ContentRating).Count())
                .Map(d => d.Photo, s => s.Photo == null ? new PhotoProductWithStatusDto() : new PhotoProductWithStatusDto
                {
                    PhotoId = s.PhotoId,
                    UploadUrl = s.Photo.UploadUrl,
                    Small = s.Photo.SmallImgUrl,
                    Middle = s.Photo.OriginalImgUrl ?? s.Photo.OriginalImgUrl.Replace("_b", "_m"),
                    Big = s.Photo.OriginalImgUrl,
                    UpdatesCount = s.Photo.UpdatesCount,
                    FileSyncStatusCode = s.Photo.SyncStatusCode,
                })
                .Map(d => d.Category, s => s.Category == null ? null : new CategoryDto
                {
                    Id = s.CategoryId,
                    Name = s.Category.Name,
                    VanityUrl = s.Category.VanityUrl,
                    Parent = s.Category.ParentCategory == null ? null : new CategoryDto
                    {
                        Id = s.Category.ParentCategoryId.Value,
                        Name = s.Category.ParentCategory.Name,
                        VanityUrl = s.Category.ParentCategory.VanityUrl,
                        Parent = s.Category.ParentCategory.ParentCategory == null ? null : new CategoryDto
                        {
                            Id = s.Category.ParentCategory.ParentCategoryId.Value,
                            Name = s.Category.ParentCategory.ParentCategory.Name,
                            VanityUrl = s.Category.ParentCategory.ParentCategory.VanityUrl
                        }
                    }
                })
                .Map(d => d.Categories, s => s.ProductCategories.Select(c => new CategoryDto
                {
                    Id = c.CategoryId,
                    Name = c.Category.Name,
                    VanityUrl = c.Category.VanityUrl,
                    Parent = c.Category.ParentCategory == null ? null : new CategoryDto
                    {
                        Id = c.Category.ParentCategoryId.Value,
                        Name = c.Category.ParentCategory.Name,
                        VanityUrl = c.Category.VanityUrl,
                        Parent = c.Category.ParentCategory.ParentCategory == null ? null : new CategoryDto
                        {
                            Id = c.Category.ParentCategory.ParentCategoryId.Value,
                            Name = c.Category.ParentCategory.ParentCategory.Name,
                            VanityUrl = c.Category.VanityUrl
                        }
                    }
                }).ToArray())
                .Map(d => d.Manufacture, s => new ManufacturerDto
                {
                    Id = s.ManufacturerId,
                    Name = s.Manufacturer.Name,
                    Site = s.Manufacturer.Site,
                    SwatchboxManufacturerId = s.Manufacturer.SwatchboxManufacturerId,
                    VideoUrl = s.Manufacturer.VideoUrl,
                    NodeSetting = s.Manufacturer.NodeSetting,
                    LetsTalkSettings = s.Manufacturer.LetsTalkSettings,
                    Logo = new PhotoLogoDto
                    {
                        Id = s.Manufacturer.PhotoId,
                        Small = s.Manufacturer.Photo != null ? s.Manufacturer.Photo.SmallImgUrl : null,
                        Original = s.Manufacturer.Photo != null ? s.Manufacturer.Photo.OriginalImgUrl : null,
                    }
                })
                .Map(d => d.ExternalCertificates, s => s.ProductCertificates.Where(b => b.ExternalCertificateId != null).Select(b => b.ExternalCertificateId.Value).ToArray())
                .Map(d => d.Samples, s => s.ProductSamples.Select(b => b.SampleId).ToArray())
                .Map(d => d.QualityItems, s => s.ProductQualityItems.Select(r => new QualityItemDto
                {
                    Id = r.QualityItemId,
                    Name = r.QualityItem.Name == null ? null : r.QualityItem.Name,
                    IconUrl = r.QualityItem.IconUrl == null ? null : r.QualityItem.IconUrl
                }).ToArray())
                .Map(d => d.ExternalMasterformatIds, s => s.ProductMasterformats.Select(x => x.ExternalMasterformatId).ToArray())
                .Map(d => d.ProductPhotos, s => s.ProductPhotos.Select(r => new PhotoProductWithMainParamDto
                {
                    PhotoId = r.PhotoId,
                    UploadUrl = r.Photo.UploadUrl,
                    Small = r.Photo.SmallImgUrl,
                    Middle = r.Photo.OriginalImgUrl.Replace("_b", "_m"), // try to get middle size
                    Big = r.Photo.OriginalImgUrl,
                    IsMainPhoto = r.PhotoId == s.PhotoId,
                    UpdatesCount = r.Photo.UpdatesCount,
                    FileSyncStatusCode = r.Photo.SyncStatusCode
                })
                .OrderByDescending(x => x.IsMainPhoto)
                .ToArray())
                .Map(d => d.ProductFiles, s => s.ProductFiles.Where(f => f.IsAttachment).Select(r => new ProductGetFileWithRegionDto
                {
                    Id = r.FileId,
                    CustomFileId = r.CustomFileId,
                    Title = r.File.Title,
                    FileName = r.File.FileName,
                    FileSize = r.File.FileSize,
                    RegionIds = r.RegionIds,
                    StateIds = r.StateIds,
                    UpdatesCount = r.File.UpdatesCount,
                    FileSyncStatusCode = r.File.SyncStatusCode,
                    FileSyncUrl = r.File.SyncUrl,
                    MimeType = r.File.MediaType,
                    Url = r.File.Url,
                    Preview = r.File.PreviewUrl,
                    Weight = r.Weight
                })
                .OrderByDescending(x => x.Weight)
                .ToArray())
                .Map(d => d.UlProductFiles, s => s.ProductFiles.Where(f => f.IsULPartnership).Select(r => new ProductGetFileDto
                {
                    Id = r.FileId,
                    CustomFileId = r.CustomFileId,
                    Title = r.File.Title,
                    FileName = r.File.FileName,
                    FileSize = r.File.FileSize,
                    UpdatesCount = r.File.UpdatesCount,
                    FileSyncStatusCode = r.File.SyncStatusCode,
                    FileSyncUrl = r.File.SyncUrl,
                    MimeType = r.File.MediaType,
                    Url = r.File.Url,
                    Preview = r.File.PreviewUrl,
                }).ToArray())
                .Map(d => d.ProjectFiles, s => s.ProductFiles.Where(f => !f.IsAttachment && !f.IsULPartnership).Select(r => new ProjectGetFileWithVersionDto
                {
                    Id = r.FileId,
                    CustomFileId = r.CustomFileId,
                    RegionIds = r.RegionIds,
                    StateIds = r.StateIds,
                    SoftwareRelease = r.SoftwareRelease,
                    ContentCreatedby = r.ContentCreatedby,
                    ContentCheckedBy = r.ContentCheckedBy,
                    FileVersion = r.FileVersion,
                    SoftwareVersionId = r.SoftwareVersionId,
                    SoftwareVersion = r.SoftwareVersion != null ? new SoftwareVersionDto
                    {
                        Id = r.SoftwareVersion.Id,
                        Title = r.SoftwareVersion.Title,
                        Header = r.SoftwareVersion.Header,
                        ParentId = r.SoftwareVersion.ParentId
                    } : null,
                    ProjectDataType = r.ProjectDataType != null ? new ProjectGetDataTypeDto
                    {
                        Id = r.ProjectDataTypeId,
                        Title = r.ProjectDataType.Title,
                        Header = r.ProjectDataType.Header,
                        ParentId = r.ProjectDataType.ParentId
                    } : null,
                    Title = r.File.Title,
                    FileName = r.File.FileName,
                    FileSize = r.File.FileSize,
                    MimeType = r.File.MediaType,
                    UpdatesCount = r.File.UpdatesCount,
                    FileSyncStatusCode = r.File.SyncStatusCode,
                    FileSyncUrl = r.File.SyncUrl,
                    Url = r.File.Url,
                    Preview = r.File.PreviewUrl,
                }).ToArray())
                .Map(d => d.ProductStats, s => s.ProductStats == null ? null : s.ProductStats.OrderBy(r => r.Order).Select(r => new ProductStatsDto
                {
                    KeyStatId = r.KeyStatId,
                    Name = r.KeyStat == null ? null : r.KeyStat.Name,
                    Note = r.Note,
                    KeyStatType = r.KeyStatType,
                    Order = r.Order,
                    KeyStatUnitId = r.KeyStatUnitId,
                    KeyStatUnit = r.KeyStatUnit == null ? null : new KeyStatDto
                    {
                        AUnitName = r.KeyStatUnit.AUnitName,
                        BUnitName = r.KeyStatUnit.BUnitName,
                        UnitMetricType = r.KeyStatUnit.UnitMetricType
                    },
                    ConvertKeyStatUnitId = r.ConvertKeyStatUnitId,
                    ConvertKeyStatUnit = r.ConvertKeyStatUnit == null ? null : new KeyStatDto
                    {
                        AUnitName = r.ConvertKeyStatUnit.AUnitName,
                        BUnitName = r.ConvertKeyStatUnit.BUnitName,
                        UnitMetricType = r.ConvertKeyStatUnit.UnitMetricType
                    },
                    SingleValue = r.Value,
                    MinRangeValue = r.MinRangeValue,
                    MaxRangeValue = r.MaxRangeValue,
                    ConvertSingleValue = r.ConvertValue,
                    ConvertMinRangeValue = r.ConvertMinRangeValue,
                    ConvertMaxRangeValue = r.ConvertMaxRangeValue,
                    MultipleValues = r.KeyStatValueList == null ? null : r.KeyStatValueList.Select(k => new KeyStatListDto
                    {
                        KeyStatUnitId = k.KeyStatUnitId,
                        KeyStatUnit = k.KeyStatUnit == null ? null : new KeyStatDto
                        {
                            AUnitName = k.KeyStatUnit.AUnitName,
                            BUnitName = k.KeyStatUnit.BUnitName,
                            UnitMetricType = k.KeyStatUnit.UnitMetricType
                        },
                        Note = k.Note,
                        ConvertKeyStatUnitId = k.ConvertKeyStatUnitId,
                        ConvertKeyStatUnit = k.ConvertKeyStatUnit == null ? null : new KeyStatDto
                        {
                            AUnitName = k.ConvertKeyStatUnit.AUnitName,
                            BUnitName = k.ConvertKeyStatUnit.BUnitName,
                            UnitMetricType = k.ConvertKeyStatUnit.UnitMetricType
                        },
                        Value = !(k.Value == null || k.Value.Trim() == string.Empty) ? k.Value : k.MinRangeValue,
                        MaxValue = k.MaxRangeValue,
                        ConvertValue = k.ConvertValue ?? k.ConvertMinRangeValue,
                        ConvertMaxValue = k.ConvertMaxRangeValue
                    })
                })
                .OrderBy(r => r.Order)
                .ToArray())
                .Map(d => d.RelatedProducts, s => s.RelatedProducts.Select(r => new RelatedProductDto
                {
                    ProductId = r.RelatedProductId,
                    Name = r.RelatedProductItem.Name,
                    CategoryName = r.RelatedProductItem.Category.Name,
                    ManufacturerName = r.RelatedProductItem.Manufacturer.Name,
                    PhotoUrl = r.RelatedProductItem.PhotoId != null ? r.RelatedProductItem.Photo.MiddleImgUrl : null
                }).ToArray())
                .Map(d => d.Details, s => s.ProductDetails.Select(x => new ProductDetailDto
                {
                    Id = x.DetailId,
                    Name = x.Detail.Name,
                    Photos = x.Detail.DetailPhotos.Select(p => new PhotoProductDto
                    {
                        PhotoId = p.PhotoId,
                        UploadUrl = p.Photo.UploadUrl,
                        Small = p.Photo.SmallImgUrl,
                        Middle = p.Photo.OriginalImgUrl.Replace("_b", "_m"),
                        Big = p.Photo.OriginalImgUrl
                    }).ToArray()
                }).ToArray())
                .Map(d => d.FooterAdImage, s => s.FooterAdImageId == null ? null : new FooterAdImageDto
                {
                    Id = s.FooterAdImageId,
                    Small = s.FooterAdImageId != null ? s.FooterAdImage.SmallImgUrl : null,
                    Big = s.FooterAdImageId != null ? s.FooterAdImage.OriginalImgUrl : null
                });

            config.ForType<Product, ProductGetDto>()
                .Map(d => d.ExternalProductId, s => s.ExternalId)
                .Map(d => d.ProductRatingCount, s => s.ProductRatings.Where(b => b.Type == ProductRatingType.ProductRating).Count())
                .Map(d => d.ContentRatingCount, s => s.ProductRatings.Where(b => b.Type == ProductRatingType.ContentRating).Count())
                .Map(d => d.Keywords, s => s.Keywords == null ? null : s.Keywords.Trim())
                .Map(d => d.ProductLine, s => s.ProductLineId == null ? new ProductLineDto() : new ProductLineDto
                {
                    Id = s.ProductLineId,
                    Name = s.ProductLine.Name,
                    RegionIds = s.ProductLine.RegionIds,
                    StateIds = s.ProductLine.StateIds,
                    ExternalCertificates = s.ProductLine.ProductLineCertificates.Select(b => b.ExternalCertificateId).ToArray(),
                    QualityItems = s.ProductLine.ProductLineQualityItems.Select(r => new QualityItemDto
                    {
                        Id = r.QualityItemId,
                        Name = r.QualityItem.Name,
                        IconUrl = r.QualityItem.IconUrl
                    }).ToArray(),
                    ProductFiles = s.ProductLine.ProductLineFiles.Where(f => f.IsAttachment).Select(r => new ProductGetFileWithWeightDto
                    {
                        Id = r.FileId,
                        CustomFileId = r.CustomFileId,
                        Title = r.File.Title,
                        FileName = r.File.FileName,
                        FileSize = r.File.FileSize,
                        MimeType = r.File.MediaType,
                        UpdatesCount = r.File.UpdatesCount,
                        FileSyncStatusCode = r.File.SyncStatusCode,
                        FileSyncUrl = r.File.SyncUrl,
                        Url = r.File.Url,
                        Preview = r.File.PreviewUrl,
                        Weight = r.Weight
                    })
                    .OrderByDescending(x => x.Weight)
                    .ToArray(),
                    ProjectFiles = s.ProductLine.ProductLineFiles.Where(f => !f.IsAttachment).Select(r => new ProjectGetFileWithTypeDto
                    {
                        Id = r.FileId,
                        CustomFileId = r.CustomFileId,
                        SoftwareRelease = r.SoftwareRelease,
                        ContentCreatedby = r.ContentCreatedby,
                        ContentCheckedBy = r.ContentCheckedBy,
                        FileVersion = r.FileVersion,
                        ProjectType = new ProjectGetDataTypeDto
                        {
                            Id = r.ProjectDataTypeId,
                            Title = r.ProjectDataType.Title,
                            Header = r.ProjectDataType.Header,
                            ParentId = r.ProjectDataType.ParentId
                        },
                        Title = r.File.Title,
                        FileName = r.File.FileName,
                        FileSize = r.File.FileSize,
                        MimeType = r.File.MediaType,
                        UpdatesCount = r.File.UpdatesCount,
                        FileSyncStatusCode = r.File.SyncStatusCode,
                        FileSyncUrl = r.File.SyncUrl,
                        Url = r.File.Url,
                        Preview = r.File.PreviewUrl
                    })
                    .ToArray()
                })
                .Map(d => d.Photo, s => s.Photo == null ? new PhotoProductWithStatusDto() : new PhotoProductWithStatusDto
                {
                    PhotoId = s.PhotoId,
                    UploadUrl = s.Photo.UploadUrl,
                    Small = s.Photo.SmallImgUrl,
                    Middle = s.Photo.OriginalImgUrl ?? s.Photo.OriginalImgUrl.Replace("_b", "_m"),
                    Big = s.Photo.OriginalImgUrl,
                    UpdatesCount = s.Photo.UpdatesCount,
                    FileSyncStatusCode = s.Photo.SyncStatusCode,
                })
                .Map(d => d.Category, s => s.Category == null ? null : new CategoryDto
                {
                    Id = s.CategoryId,
                    Name = s.Category.Name,
                    VanityUrl = s.Category.VanityUrl,
                    Parent = s.Category.ParentCategory == null ? null : new CategoryDto
                    {
                        Id = s.Category.ParentCategoryId.Value,
                        Name = s.Category.ParentCategory.Name,
                        VanityUrl = s.Category.ParentCategory.VanityUrl,
                        Parent = s.Category.ParentCategory.ParentCategory == null ? null : new CategoryDto
                        {
                            Id = s.Category.ParentCategory.ParentCategoryId.Value,
                            Name = s.Category.ParentCategory.ParentCategory.Name,
                            VanityUrl = s.Category.ParentCategory.ParentCategory.VanityUrl
                        }
                    }
                })
                .Map(d => d.CategoryIds, s => s.ProductCategories.Select(c => c.CategoryId).ToArray())
                .Map(d => d.Categories, s => s.ProductCategories.Select(c => new CategoryDto
                {
                    Id = c.CategoryId,
                    Name = c.Category.Name,
                    VanityUrl = c.Category.VanityUrl,
                    Parent = c.Category.ParentCategory == null ? null : new CategoryDto
                    {
                        Id = c.Category.ParentCategoryId.Value,
                        Name = c.Category.ParentCategory.Name,
                        VanityUrl = c.Category.VanityUrl,
                        Parent = c.Category.ParentCategory.ParentCategory == null ? null : new CategoryDto
                        {
                            Id = c.Category.ParentCategory.ParentCategoryId.Value,
                            Name = c.Category.ParentCategory.ParentCategory.Name,
                            VanityUrl = c.Category.VanityUrl
                        }
                    }
                }).ToArray())
                .Map(d => d.Manufacture, s => new ManufacturerDto
                {
                    Id = s.ManufacturerId,
                    Name = s.Manufacturer.Name,
                    Site = s.Manufacturer.Site,
                    SwatchboxManufacturerId = s.Manufacturer.SwatchboxManufacturerId,
                    VideoUrl = s.Manufacturer.VideoUrl,
                    NodeSetting = s.Manufacturer.NodeSetting,
                    LetsTalkSettings = s.Manufacturer.LetsTalkSettings,
                    Logo = new PhotoLogoDto
                    {
                        Id = s.Manufacturer.PhotoId,
                        Small = s.Manufacturer.Photo != null ? s.Manufacturer.Photo.SmallImgUrl : null,
                        Original = s.Manufacturer.Photo != null ? s.Manufacturer.Photo.OriginalImgUrl : null,
                    }
                })
                .Map(d => d.ExternalCertificates, s => s.ProductCertificates.Where(b => b.ExternalCertificateId != null).Select(b => b.ExternalCertificateId.Value).ToArray())
                .Map(d => d.Samples, s => s.ProductSamples.Select(b => b.SampleId).ToArray())
                .Map(d => d.QualityItems, s => s.ProductQualityItems.Select(r => new QualityItemDto
                {
                    Id = r.QualityItemId,
                    Name = r.QualityItem.Name == null ? null : r.QualityItem.Name,
                    IconUrl = r.QualityItem.IconUrl == null ? null : r.QualityItem.IconUrl
                }).ToArray())
                .Map(d => d.Cisfbs, s => s.ProductCisfbs.Select(c => new FormatDto
                {
                    Id = c.CisfbId,
                    Code = c.Cisfb.Code,
                    Title = c.Cisfb.Title
                }).ToArray())
                .Map(d => d.ExternalMasterformatIds, s => s.ProductMasterformats.Select(x => x.ExternalMasterformatId).ToArray())
                .Map(d => d.Omniclasses, s => s.ProductOmniclasses.Select(r => new FormatDto
                {
                    Id = r.OmniclassId,
                    Code = r.Omniclass.Code,
                    Title = r.Omniclass.Title
                }).ToArray())
                .Map(d => d.Uniclasses, s => s.ProductUniclasses.Select(r => new FormatDto
                {
                    Id = r.UniclassId,
                    Code = r.Uniclass.Code,
                    Title = r.Uniclass.Title
                }).ToArray())
                .Map(d => d.Uniformats, s => s.ProductUniformats.Select(r => new FormatDto
                {
                    Id = r.UniformatId,
                    Code = r.Uniformat.Code,
                    Title = r.Uniformat.Title
                }).ToArray())
                .Map(d => d.ProductPhotos, s => s.ProductPhotos.Select(r => new PhotoProductWithMainParamDto
                {
                    PhotoId = r.PhotoId,
                    UploadUrl = r.Photo.UploadUrl,
                    Small = r.Photo.SmallImgUrl,
                    Middle = r.Photo.OriginalImgUrl.Replace("_b", "_m"), // try to get middle size
                    Big = r.Photo.OriginalImgUrl,
                    IsMainPhoto = r.PhotoId == s.PhotoId,
                    UpdatesCount = r.Photo.UpdatesCount,
                    FileSyncStatusCode = r.Photo.SyncStatusCode
                })
                .OrderByDescending(x => x.IsMainPhoto)
                .ToArray())
                .Map(d => d.ProductFiles, s => s.ProductFiles.Where(f => f.IsAttachment).Select(r => new ProductGetFileWithRegionDto
                {
                    Id = r.FileId,
                    CustomFileId = r.CustomFileId,
                    Title = r.File.Title,
                    FileName = r.File.FileName,
                    FileSize = r.File.FileSize,
                    RegionIds = r.RegionIds,
                    StateIds = r.StateIds,
                    UpdatesCount = r.File.UpdatesCount,
                    FileSyncStatusCode = r.File.SyncStatusCode,
                    FileSyncUrl = r.File.SyncUrl,
                    MimeType = r.File.MediaType,
                    Url = r.File.Url,
                    Preview = r.File.PreviewUrl,
                    Weight = r.Weight
                })
                .OrderByDescending(x => x.Weight)
                .ToArray())
                .Map(d => d.UlProductFiles, s => s.ProductFiles.Where(f => f.IsULPartnership).Select(r => new ProductGetFileDto
                {
                    Id = r.FileId,
                    CustomFileId = r.CustomFileId,
                    Title = r.File.Title,
                    FileName = r.File.FileName,
                    FileSize = r.File.FileSize,
                    UpdatesCount = r.File.UpdatesCount,
                    FileSyncStatusCode = r.File.SyncStatusCode,
                    FileSyncUrl = r.File.SyncUrl,
                    MimeType = r.File.MediaType,
                    Url = r.File.Url,
                    Preview = r.File.PreviewUrl,
                }).ToArray())
                .Map(d => d.ProjectFiles, s => s.ProductFiles.Where(f => !f.IsAttachment && !f.IsULPartnership).Select(r => new ProjectGetFileWithVersionDto
                {
                    Id = r.FileId,
                    CustomFileId = r.CustomFileId,
                    RegionIds = r.RegionIds,
                    StateIds = r.StateIds,
                    SoftwareRelease = r.SoftwareRelease,
                    ContentCreatedby = r.ContentCreatedby,
                    ContentCheckedBy = r.ContentCheckedBy,
                    FileVersion = r.FileVersion,
                    SoftwareVersionId = r.SoftwareVersionId,
                    SoftwareVersion = r.SoftwareVersion != null ? new SoftwareVersionDto
                    {
                        Id = r.SoftwareVersion.Id,
                        Title = r.SoftwareVersion.Title,
                        Header = r.SoftwareVersion.Header,
                        ParentId = r.SoftwareVersion.ParentId
                    } : null,
                    ProjectDataType = r.ProjectDataType != null ? new ProjectGetDataTypeDto
                    {
                        Id = r.ProjectDataTypeId,
                        Title = r.ProjectDataType.Title,
                        Header = r.ProjectDataType.Header,
                        ParentId = r.ProjectDataType.ParentId
                    } : null,
                    Title = r.File.Title,
                    FileName = r.File.FileName,
                    FileSize = r.File.FileSize,
                    MimeType = r.File.MediaType,
                    UpdatesCount = r.File.UpdatesCount,
                    FileSyncStatusCode = r.File.SyncStatusCode,
                    FileSyncUrl = r.File.SyncUrl,
                    Url = r.File.Url,
                    Preview = r.File.PreviewUrl,
                }).ToArray())
                .Map(d => d.ProductStats, s => s.ProductStats == null ? null : s.ProductStats.OrderBy(r => r.Order).Select(r => new ProductStatsDto
                {
                    KeyStatId = r.KeyStatId,
                    Name = r.KeyStat == null ? null : r.KeyStat.Name,
                    Note = r.Note,
                    KeyStatType = r.KeyStatType,
                    Order = r.Order,
                    KeyStatUnitId = r.KeyStatUnitId,
                    KeyStatUnit = r.KeyStatUnit == null ? null : new KeyStatDto
                    {
                        AUnitName = r.KeyStatUnit.AUnitName,
                        BUnitName = r.KeyStatUnit.BUnitName,
                        UnitMetricType = r.KeyStatUnit.UnitMetricType
                    },
                    ConvertKeyStatUnitId = r.ConvertKeyStatUnitId,
                    ConvertKeyStatUnit = r.ConvertKeyStatUnit == null ? null : new KeyStatDto
                    {
                        AUnitName = r.ConvertKeyStatUnit.AUnitName,
                        BUnitName = r.ConvertKeyStatUnit.BUnitName,
                        UnitMetricType = r.ConvertKeyStatUnit.UnitMetricType
                    },
                    SingleValue = r.Value,
                    MinRangeValue = r.MinRangeValue,
                    MaxRangeValue = r.MaxRangeValue,
                    ConvertSingleValue = r.ConvertValue,
                    ConvertMinRangeValue = r.ConvertMinRangeValue,
                    ConvertMaxRangeValue = r.ConvertMaxRangeValue,
                    MultipleValues = r.KeyStatValueList == null ? null : r.KeyStatValueList.Select(k => new KeyStatListDto
                    {
                        KeyStatUnitId = k.KeyStatUnitId,
                        KeyStatUnit = k.KeyStatUnit == null ? null : new KeyStatDto
                        {
                            AUnitName = k.KeyStatUnit.AUnitName,
                            BUnitName = k.KeyStatUnit.BUnitName,
                            UnitMetricType = k.KeyStatUnit.UnitMetricType
                        },
                        Note = k.Note,
                        ConvertKeyStatUnitId = k.ConvertKeyStatUnitId,
                        ConvertKeyStatUnit = k.ConvertKeyStatUnit == null ? null : new KeyStatDto
                        {
                            AUnitName = k.ConvertKeyStatUnit.AUnitName,
                            BUnitName = k.ConvertKeyStatUnit.BUnitName,
                            UnitMetricType = k.ConvertKeyStatUnit.UnitMetricType
                        },
                        Value = !(k.Value == null || k.Value.Trim() == string.Empty) ? k.Value : k.MinRangeValue,
                        MaxValue = k.MaxRangeValue,
                        ConvertValue = k.ConvertValue ?? k.ConvertMinRangeValue,
                        ConvertMaxValue = k.ConvertMaxRangeValue
                    })
                })
                .OrderBy(r => r.Order)
                .ToArray())
                .Map(d => d.RelatedProducts, s => s.RelatedProducts.Select(r => new RelatedProductDto
                {
                    ProductId = r.RelatedProductId,
                    Name = r.RelatedProductItem.Name,
                    CategoryName = r.RelatedProductItem.Category.Name,
                    ManufacturerName = r.RelatedProductItem.Manufacturer.Name,
                    PhotoUrl = r.RelatedProductItem.PhotoId != null ? r.RelatedProductItem.Photo.MiddleImgUrl : null
                }).ToArray())
                .Map(d => d.UpdateDate, s => s.ModifiedDate ?? s.CreatedDate)
                .Map(d => d.Details, s => s.ProductDetails.Select(x => new ProductDetailDto
                {
                    Id = x.DetailId,
                    Name = x.Detail.Name,
                    Photos = x.Detail.DetailPhotos.Select(p => new PhotoProductDto
                    {
                        PhotoId = p.PhotoId,
                        UploadUrl = p.Photo.UploadUrl,
                        Small = p.Photo.SmallImgUrl,
                        Middle = p.Photo.OriginalImgUrl.Replace("_b", "_m"),
                        Big = p.Photo.OriginalImgUrl
                    }).ToArray()
                }).ToArray())
                .Map(d => d.FooterAdImage, s => s.FooterAdImageId == null ? null : new FooterAdImageDto
                {
                    Id = s.FooterAdImageId,
                    Small = s.FooterAdImageId != null ? s.FooterAdImage.SmallImgUrl : null,
                    Big = s.FooterAdImageId != null ? s.FooterAdImage.OriginalImgUrl : null
                });

            config.ForType<Product, AdminListProductDto>()
                .Map(d => d.UpdateDate, s => s.ModifiedDate ?? s.CreatedDate)
                .Map(d => d.PhotoUrl, s => s.PhotoId != null ? s.Photo.MiddleImgUrl : s.ProductPhotos.FirstOrDefault().Photo.MiddleImgUrl);

            config.ForType<Product, ProductManufacturerDto>()
                .Map(d => d.ProductId, s => s.Id)
                .Map(d => d.ManufacturerId, s => s.ManufacturerId)
                .Map(d => d.ProductName, s => s.Name);
        }
    }
}