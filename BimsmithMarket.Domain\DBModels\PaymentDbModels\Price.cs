﻿using BIMsmithMarket.Domain.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace BIMsmithMarket.Domain.DBModels.PaymentDbModels
{
    /// <summary>
    /// Product basic price without any discount 
    /// Shows Price type (Is this product free or not)
    /// </summary>
    public class Price
    {
        [Key]
        [ForeignKey("Product")]
        public int ProductId { get; set; }
        public PriceType PriceType { get; set; }
        public decimal Amount { get; set; }
        public virtual Product Product { get; set; }
    }
}
