﻿using System;
using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Models
{
    public class UserListModel
    {
        public string Id { get; set; }

        public string UserName { get; set; }

        public string FirstName { get; set; }

        public string LastName { get; set; }

        public string Email { get; set; }

        public bool IsAdmin { get; set; }

        public bool IsSuperAdmin { get; set; }

        public bool IsSalesForceAccess { get; set; }

        public ICollection<string> Roles { get; set; }

        public DateTime? CreatedDate { get; set; }

        public string Company { get; set; }

        public IEnumerable<string> RoleIds { get; set; }
    }
}