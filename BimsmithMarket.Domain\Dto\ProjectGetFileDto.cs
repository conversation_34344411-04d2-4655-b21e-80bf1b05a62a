﻿using System;

namespace BIMsmithMarket.Domain.Dto
{
    public class ProjectGetFileDto
    {
        public int Id { get; set; }
        public string CustomFileId { get; set; }
        public string Title { get; set; }
        public string FileName { get; set; }
        public long FileSize { get; set; }
        public string MimeType { get; set; }
        public string Url { get; set; }
        public string Preview { get; set; }
        public string RegionIds { get; set; }
        public string StateIds { get; set; }
        public string SoftwareRelease { get; set; }
        public string ContentCreatedby { get; set; }
        public string ContentCheckedBy { get; set; }
        public string FileVersion { get; set; }
        public int UpdatesCount { get; set; }
        public int FileSyncStatusCode { get; set; }
        public string FileSyncUrl { get; set; }
        public DateTime ModifiedDate { get; set; }
    }

    public class ProjectGetFileWithVersionDto : ProjectGetFileDto
    {
        public int? SoftwareVersionId { get; set; }

        public SoftwareVersionDto SoftwareVersion { get; set; }

        public ProjectGetDataTypeDto ProjectDataType { get; set; }

        public DateTime CreatedDate { get; set; }

        public string UserName { get; set; }
    }

    public class ProjectGetFileWithTypeDto : ProjectGetFileDto
    {
        public ProjectDataTypeDto ProjectType { get; set; }
    }
}