﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto.Manufacturer;
using Mapster;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public static class PhotoMap
    {
        public static void Register(TypeAdapterConfig config)
        {
            config.ForType<Photo, AddManufacturerResultLogo>()
                .Map(d => d.Small, s => s.SmallImgUrl)
                .Map(d => d.Original, s => s.OriginalImgUrl);
        }
    }
}