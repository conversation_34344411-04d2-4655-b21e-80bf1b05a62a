﻿using BIMsmithMarket.Domain.Dto.PluginFileDto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IPluginFileService
    {
        Task<GetPluginFileDto> GetAsync(IUnitOfWork unitOfWork, int id);

        Task<List<GetPluginFileDto>> ListAsync(PluginType pluginType, IUnitOfWork unitOfWork);

        Task<EditPluginFileDto> AddAsync(IUnitOfWork unitOfWork, string userId, AddPluginFileDto model);

        Task<EditPluginFileDto> EditAsync(IUnitOfWork unitOfWork, string userId, EditPluginFileDto model);

        Task DeleteAsync(IUnitOfWork unitOfWork, int id);

        Task<List<PluginFileVersionDto>> VersionListAsync(bool orderByDate, PluginType pluginType, IUnitOfWork unitOfWork);

        Task<List<PluginFileVersionUpdateDto>> VersionUpdatesAsync(string version, PluginType pluginType, IUnitOfWork unitOfWork);
    }
}