﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.Services;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Net.Http.Headers;
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Security.Claims;
using System.Text.Json;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.ActionFilters
{
    public class WebApiOutputCacheAttribute : Attribute, IAsyncActionFilter
    {
        // cache length in seconds
        private int _timespan;
        private bool _needAuthorization;
        private static ICacheService _cacheService = new CacheService();

        /// <summary>
        /// Initializes a new instance of the <see cref="WebApiOutputCacheAttribute"/> class.
        /// </summary>
        /// <param name="timespan">The timespan.</param>
        /// <param name="needAuthorization"></param>
        public WebApiOutputCacheAttribute(int timespan, bool needAuthorization = false)
        {
            _timespan = timespan;
            _needAuthorization = needAuthorization;
        }

        public async Task OnActionExecutionAsync(
            ActionExecutingContext executingContext,
            ActionExecutionDelegate next)
        {
            var cacheKey = Path.Combine(executingContext.HttpContext.Request.Path, executingContext.HttpContext.Request.QueryString.ToString());

            if (string.IsNullOrWhiteSpace(cacheKey))
                return;

            if (executingContext.HttpContext.Request.Method == HttpMethod.Post.ToString() || executingContext.HttpContext.Request.Method == HttpMethod.Put.ToString())
            {
                cacheKey += $"+{await GetBodyHashSumAsync(executingContext.HttpContext.Request)}";
            }

            if (_needAuthorization)
            {
                cacheKey += "+UserId=" + AuthHelper.GetUserInfo(executingContext.HttpContext.Request, ClaimTypes.NameIdentifier);
            }

            if (_cacheService.Contains(cacheKey))
            {
                var cachedValue = _cacheService.Get<string>(cacheKey);
                if (!string.IsNullOrWhiteSpace(cachedValue))
                {
                    executingContext.Result = new OkObjectResult(JsonSerializer.Deserialize(cachedValue, typeof(object)));
                    executingContext.HttpContext.Response.GetTypedHeaders().CacheControl = SetClientCache();
                    return;
                }
            }

            ActionExecutedContext executedContext = await next();

            if (executedContext.HttpContext.Response != null && executedContext.HttpContext.Response.StatusCode == (int)HttpStatusCode.OK)
            {
                if (!_cacheService.Contains(cacheKey) && executedContext.HttpContext.Response.Body != null)
                {
                    if (executedContext.Result is OkObjectResult)
                    {
                        _cacheService.Set(cacheKey, JsonSerializer.Serialize(((OkObjectResult)executedContext.Result).Value, new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase }), _timespan);
                    }
                }

                executedContext.HttpContext.Response.GetTypedHeaders().CacheControl = SetClientCache();
            }
        }

        /// <summary>
        /// Clears the cache.
        /// </summary>
        public static void ClearAllCache()
        {
            ICollection<string> cacheKeys = _cacheService.GetKeys();
            foreach (string cacheKey in cacheKeys)
            {
                _cacheService.Remove(cacheKey);
            }
        }

        /// <summary>
        /// Clears keys by pattern
        /// </summary>
        /// <param name="pattern"></param>
        public static void ClearCacheKeysByPattern(string pattern)
        {
            ICollection<string> cacheKeys = _cacheService.GetKeys(pattern);
            foreach (string cacheKey in cacheKeys)
            {
                _cacheService.Remove(cacheKey);
            }
        }

        /// <summary>
        /// Clears specific cache keys.
        /// </summary>
        public static void ClearSpecificCacheKeys(IList<string> keys = null)
        {
            if (keys != null)
            {
                foreach (var key in keys)
                {
                    if (_cacheService.Contains(key))
                    {
                        _cacheService.Remove(key);
                    }

                    if (_cacheService.Contains(key + ":response-ct"))
                    {
                        _cacheService.Remove(key + ":response-ct");
                    }
                }
                return;
            }
        }

        #region private methods
        private async Task<string> GetBodyHashSumAsync(HttpRequest httpRequest)
        {
            httpRequest.Body.Position = 0;
            using StreamReader reader = new StreamReader(httpRequest.Body);
            string requestBody = await reader.ReadToEndAsync();
            httpRequest.Body.Position = 0;
            if (!string.IsNullOrWhiteSpace(requestBody))
            {
                return HashProvider.CalculateMD5Hash(requestBody);
            }

            return string.Empty;
        }

        private CacheControlHeaderValue SetClientCache()
        {
            var cachecontrol = new CacheControlHeaderValue();
            cachecontrol.MaxAge = TimeSpan.FromSeconds(-1);
            cachecontrol.MustRevalidate = true;
            cachecontrol.NoCache = true;
            cachecontrol.NoStore = true;
            cachecontrol.Extensions.Add(new NameValueHeaderValue("ETag", Guid.NewGuid().ToString()));
            return cachecontrol;
        }
        #endregion
    }
}