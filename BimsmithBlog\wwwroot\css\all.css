body {
  color: #797979;
  font: 14px/30px Ubuntu, Helvetica, sans-serif;
  background: #fff;
  -webkit-text-size-adjust: 100%;
  margin: 0;
}

h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6, div, span, a, input, html, body, p, textarea, button {
    font-family: Ubuntu, Helvetica, sans-serif !important;
}

a, a.blog-header {
  color: #000;
  outline: none;
  text-decoration: none;
}
a:hover, a.blog-header:hover {
  text-decoration: none;
}
img {
  max-width: 100%;
  height: auto;
  border-style: none;
  vertical-align: top;
  display: block;
}
input, textarea, select, button {
  font: 100% Arial, Helvetica, sans-serif;
  vertical-align: middle;
  color: #000;
  outline: none;
  border: 0;
}

video, iframe, object, embed {max-width: 100%;}

textarea {
  overflow: auto;
  resize: vertical;
}

button:focus,
a:focus,
input:focus,
textarea:focus,
select:focus{ outline: none; }
article, aside, details, figcaption, figure, footer, header, main, nav, section, summary {display: block;}
input:invalid{ box-shadow: none;}
input:-moz-submit-invalid { box-shadow: none;}
input:-moz-ui-invalid { box-shadow:none;}

input,
input[type="text"],
input[type="tel"],
input[type="email"],
input[type="url"],
input[type="password"],
input[type="search"],
textarea {
  background: #fff;
  -webkit-appearance: none;
  display: block;
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  border-radius: 0;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
input[type=text]::-ms-clear,
input[type=search]::-ms-clear,
input[type=text]::-ms-reveal,
input[type=search]::-ms-reveal {
    display: none;
}

input[type="search"] {-webkit-appearance: none;}
input[type="search"]::-webkit-search-cancel-button {-webkit-appearance: none;}
input[type="search"]::-webkit-search-decoration { display: none;}

input:-moz-placeholder { color: #d0d0d0;opacity: 1;}
input::-moz-placeholder {color: #d0d0d0;opacity: 1;}
input:-ms-input-placeholder {color: #d0d0d0;opacity: 1;}
input::-webkit-input-placeholder {color: #d0d0d0;opacity: 1;}
input:focus:-moz-placeholder {opacity: 0;}
input:focus::-moz-placeholder { opacity: 0;}
input:focus:-ms-input-placeholder { opacity: 0;}
input:focus::-webkit-input-placeholder {opacity: 0;}

textarea:-moz-placeholder {color: #252222;opacity: 1;}
textarea::-moz-placeholder {color: #252222;opacity: 1;}
textarea:-ms-input-placeholder {color: #252222;opacity: 1;}
textarea::-webkit-input-placeholder {color: #252222;opacity: 1;}
textarea:focus:-moz-placeholder {opacity: 0;}
textarea:focus::-moz-placeholder {opacity: 0;}
textarea:focus:-ms-input-placeholder {opacity: 0;}
textarea:focus::-webkit-input-placeholder {opacity: 0;}

html, body, div, span, applet, object, iframe, table, caption, tbody, tfoot, thead, tr, th, td,
del, dfn, em, font, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var,
h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code,
dl, dt, dd, ol, ul, li, fieldset, form, legend {
  vertical-align: baseline;
  outline: 0;
  padding: 0;
  margin: 0;
  border: 0;
  }

ol, ul {list-style: none;}

article, aside, details, figcaption, figure, footer, header, main, nav, section, summary {display: block;}
label {
  vertical-align: middle;
}
button::-moz-focus-inner,
input::-moz-focus-inner {
  padding: 0;
  border: 0;
}

h1, h2, h3, h4, h5, h6 {line-height: 1.3;}
h1 {font: 500 40px/1 Ubuntu, Helvetica, sans-serif; color: #fff;}
h2 {font: 500 24px/28px Ubuntu, Helvetica, sans-serif; color: #000;}
h3 {font: 500 20px/24px Ubuntu, Helvetica, sans-serif; color: #000; margin: 0 0 20px 0;}
h4 {font: 500 18px/1 Ubuntu, Helvetica, sans-serif; color: #000; margin: 0 0 20px 0;}
h5 {font: 400 16px/1 Ubuntu, Helvetica, sans-serif; color: #000; margin: 0 0 20px 0;}
h6 {font-size: 90%;}

blockquote, blockquote:before, blockquote:after, q, q:before, q:after {quotes: none;}
blockquote:before, blockquote:after, q:before, q:after {content: '';}

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sup {top: -0.5em;}
sub {bottom: -0.25em;}

table, caption, tbody, tfoot, thead, tr, th, td {
  border-collapse: collapse;
  border-spacing: 0;
}

.clear:after{
	display: block;
	content: "";
	clear: both;
}
.center{
	text-align: center;
}
.def-btn {
  color: #000;
  display: inline-block;
  vertical-align: middle;
  font-weight: bold;
  text-transform: uppercase;
  -webkit-transition: 0.2s all 0s;
  -moz-transition: 0.2s all 0s;
  -o-transition: 0.2s all 0s;
  transition: 0.2s all 0s;
  font-size: 16px;
  background:transparent;
  cursor: pointer;
}
.def-btn:hover{
	color: #3db28e;
}
.g-btn {
  display: inline-block;
  height: 44px;
  padding: 0 30px;
  border-radius: 20px;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border: 2px solid #3db28e;
  color: #3db28e;
  font: bold 16px/40px Ubuntu, Helvetica, sans-serif;
  text-transform: uppercase;
  -webkit-transition: 0.2s all 0s;
  -moz-transition: 0.2s all 0s;
  -o-transition: 0.2s all 0s;
  transition: 0.2s all 0s;
  vertical-align: middle;
}
.g-btn:hover{
	color: #fff;
	background: #3db28e;
}
.btns{
	position: relative;
	line-height: 0;
	font-size: 0;
}
.btns:before{
	content: "";
	background: #d0d0d0;
	display: block;
	position:absolute; 
	left:50%; 
	top:50%;
	-webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  -moz-border-radius: 100%;
  -webkit-border-radius: 100%;
  border-radius: 100%;
}
.n-green-btn{
	display: inline-block;
	height: 28px;
	line-height: 28px;
	border: 1px solid #3db28e;
	-moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  width: 110px;
  padding: 0 5px;
  text-transform: uppercase;
  font: 700 12px/28px Ubuntu, Helvetica, sans-serif;
  color: #3db28e;
  white-space: nowrap;
  overflow: hidden;
	text-overflow: ellipsis;
	-webkit-transition: 0.2s all 0s;
  -moz-transition: 0.2s all 0s;
  -o-transition: 0.2s all 0s;
  transition: 0.2s all 0s;
  text-align: center;
  margin: 20px 15px;
}
.n-green-btn:hover{
	color: #fff;
	background: #3db28e;
}
.n-gray-btn{
	display: inline-block;
	height: 28px;
	line-height: 28px;
	-moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  border: 1px solid #d0d0d0;
  width: 110px;
  padding: 0 5px;
  text-transform: uppercase;
  font: 700 12px/28px Ubuntu, Helvetica, sans-serif;
  color: #d0d0d0;
  white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	-webkit-transition: 0.2s all 0s;
  -moz-transition: 0.2s all 0s;
  -o-transition: 0.2s all 0s;
  transition: 0.2s all 0s;
  text-align: center;
  margin: 20px 15px;
}

.header-wrap{
	padding: 20px 15px;
	/* height: 150px; */
	border-bottom: 1px solid #eeeeee;
	width: 100%;
/* 	position: fixed; */
	-moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  background: #fff;
  z-index: 999;
}
.header-wrap .header{
	max-width: 1060px;
	margin: 0 auto;
}
.header .menu{
	display: none;
	position:absolute; 
	right: 15px; 
	top: 33px;
	width: 22px;
	font-size: 0;
	line-height: 0;
}
.header .menu span{
	display: block;
	height: 2px;
	-webkit-transition: all .5s;
	-o-transition: all .5s;
	transition: all .5s;
	background: #415a68;
}
.header .menu.on span{
	margin-left: 4px;
}
.header .menu:hover span,
.header .menu.on:hover span{
	background: #80b2cf !important;
}
.header .menu span+span{
	margin-top: 5px;
}
.header .menu.on span:nth-child(1){
  -webkit-transform: rotate(45deg) translate(10%,8%);
  -ms-transform: rotate(45deg) translate(10%,8%);
  -o-transform: rotate(45deg) translate(10%,8%);
  transform: rotate(45deg) translate(10%,8%);
  -webkit-transform-origin: 0 0;
  -moz-transform-origin: 0 0;
  -ms-transform-origin: 0 0;
  -o-transform-origin: 0 0;
  transform-origin: 0 0;
  background:#415a68;
 }
.header .menu.on span:nth-child(2){opacity: 0;}
.header .menu.on span:nth-child(3){
  -webkit-transform: rotate(-45deg) translate(6%, 30%);
  -ms-transform: rotate(-45deg) translate(6%, 30%);
  -o-transform: rotate(-45deg) translate(6%, 30%);
  transform: rotate(-45deg) translate(6%, 30%);
  -webkit-transform-origin: 0 100%;
  -moz-transform-origin: 0 100%;
  -ms-transform-origin: 0 100%;
  -o-transform-origin: 0 100%;
  transform-origin: 0 100%;
  background: #415a68;
 }
.header .logo{
	float: left;
	width: 205px;
	height: 44px;
}
.header .logo img{
	display: block;
	max-width: 100%;
	max-height: 100%;
}
.header .top{
	margin: 0 0 40px;
}
.header .sign{
	float: right;
}
.header .sign .g-btn{
	margin: 0 0 0 15px;
}
.header .bot{
	height: 33px;
	line-height: 33px;
}
.header .bot .links{
	float: left;
}
.header .bot .links li{
	float: left;
	margin: 0 25px 0 0;
	position: relative;
}
.header .bot .links li:last-child{
	/*
        margin: 0;
	    padding: 0 0 0 25px;
	*/
}
.header .bot .links li:last-child:before {
    /*
    position:absolute; 
	left:0; 
	top: 50%;
	content: "";
	background: #b9b9b9;
	height: 14px;
	width: 2px;
	display: inline-block;
	margin: -7px 0 0 0;
*/
}
.header .bot .links li a{
	font: 700 14px/24px Ubuntu, Helvetica, sans-serif;
	text-transform: uppercase;
	color: #000;
}
.header .bot .links li.active a,
.header .bot .links li a:hover{
	color: #3db28e;
}
.header .bot .search{
	float: right;
	border-left: 1px solid #eeeeee;
	height: 33px;
	line-height: 33px;
}
.header .bot .search .fa-search{
	color: #000;
	font-size: 22px;
	display: inline-block;
	float: right;
	margin: 3px 0;
	cursor: pointer;
}
.header .bot .search .fa-search:hover{
	color: #3db28e;
}
.header .bot .search input{
	float: right;
	width: 0;
	padding: 0;
	margin: 0 8px;
	height: 33px;
	line-height: 33px;
	-webkit-transition: 0.3s all 0s;
  -moz-transition: 0.3s all 0s;
  -o-transition: 0.3s all 0s;
  transition: 0.3s all 0s;
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  border: 1px solid transparent;
}
.header .bot .search input.active{
	width: 150px;
	border: 1px solid #b9b9b9;
	padding: 0 10px;
}
.main{
	padding: 30px 15px;
	max-width: 1060px;
	margin: 0 auto;
}
.main h2{
	text-align: center;
}
.main .btns .soc{
	position:absolute; 
	right: 0; 
	top: 50%;
	margin: -16px 0 0 0;
}
.main .btns .soc a{
	display: inline-block;
	width: 33px;
	height: 33px;
	background: #999999;
	-moz-border-radius: 100%;
  -webkit-border-radius: 100%;
  border-radius: 100%;
  margin: 0 0 0 7px;
}
.main .btns .soc a i{
	font-size: 20px;
	line-height: 33px;
	display: inline-block;
	text-align: center;
	color: #fff;
}
.main .content{
		max-width: 780px;
		margin: 40px auto;
        font: 16px/24px Ubuntu, Helvetica, sans-serif!important;
}
.main .content p{
	margin: 0 0 20px;
}
.main > .block{
	border-bottom: 1px solid #eeeeee;
}
.main .comments{
	max-width: 780px;
	margin: 40px auto;
}
.main .com{
	margin: 0 0 20px;
}
.main .com textarea{
	min-height: 50px;
	border: 1px solid #d9d9d9;
	width: 100%;
	margin: 0 0 20px;
	padding: 20px;
	-moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.main .com button{
	float: right;
	height: 42px;
	background: #3db28e;
	-moz-border-radius: 20px;
  -webkit-border-radius: 20px;
  border-radius: 20px;
  padding: 0 30px;
  color: #fff;
  font: 500 16px/42px Ubuntu, Helvetica, sans-serif;
  text-transform: uppercase;
  cursor: pointer;
}
.main .com button:hover{
	opacity: 0.7;
}
.main .answ{

}
.main .answ .box{
	margin: 0 0 20px;
	padding: 0 0 20px;
	border-bottom: 1px solid #eeeeee;
}
.main .answ .box:last-child{
	border: none;
	padding: 0;
}
.main .answ p{
	font: 400 16px/22px Ubuntu, Helvetica, sans-serif;
	margin: 0 0 20px;
    white-space: pre-wrap;
}
.main .answ p:last-child{
	margin: 0;
}
.main .articles{
	overflow: hidden;
}
.main .art-list{
	margin:  0 -30px 0 0;
	line-height: 0;
	font-size: 0;
	padding: 30px 0 0 0;
	border-bottom: 1px solid #d0d0d0;
}
.main .art-list li{
	padding: 0 30px 50px 0;
	-moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 33.33%;
  display: inline-block;
  vertical-align: top;
}
.main .art-list li img{
	width: 100%;
	height: 190px;
}
.main .art-list li h2{
	text-align: left;
}
.main .art-list li p{
	font: 400 16px/22px Ubuntu, Helvetica, sans-serif;
	margin: 0 0 20px;
}
.main .art-list li p:last-child{
	margin: 0;
}
.main .art-list li .more{
	display: inline-block;
	color: #3db28e;
	text-transform: uppercase;
	font: 700 16px/1 Ubuntu, Helvetica, sans-serif;
}
.main .art-list li .more:hover{
	opacity: 0.7;
}
.footer{
	max-width: 1060px;
	margin: 0 auto 30px;
	padding: 0 15px;
	position: relative;
}
.footer .logo{
	width: 160px;
	float: left;
	margin: 5px 0;
}
.footer .logo img{
	max-width: 100%;
	display: block;
}
.footer .sub{
	float: right;
	height: 42px;
}
.footer .sub button{
	float: right;
	height: 42px;
	background: #3db28e;
	-moz-border-radius: 20px;
  -webkit-border-radius: 20px;
  border-radius: 20px;
  padding: 0 30px;
  color: #fff;
  font: 500 16px/42px Ubuntu, Helvetica, sans-serif;
  text-transform: uppercase;
  cursor: pointer;
}
.footer .sub button:hover{
	opacity: 0.7;
}
.footer .sub input{
	background: #f1f1f1;
	padding: 0 20px;
	-moz-border-radius: 20px;
  -webkit-border-radius: 20px;
  border-radius: 20px;
  color: #d0d0d0;
  height: 42px;
  font: 500 16px/42px Ubuntu, Helvetica, sans-serif;
  margin: 0 15px;
  float: right;
}
.footer .sub span{
	float: right;
	font: 500 14px/42px Ubuntu, Helvetica, sans-serif;
	color: #000;
}
.footer .copy{
	margin: 30px 0;
}
#top{
	width: 45px;
	height: 45px;
	background: #f1f1f1;
	z-index: 9;
	position:absolute; 
	right: -100px; 
	top:0;
	-moz-border-radius: 100%;
  -webkit-border-radius: 100%;
  border-radius: 100%;
  color: #bebebe;
  text-align: center;
  cursor: pointer;
}
#top:hover{
	opacity: 0.7;
}
#top i{
	line-height: 45px;
	font-size: 22px;
}

/* BLOG */
.blog .header-wrap{
	padding: 20px 0;
}
.blog .header{
	max-width: 100%;
}
.blog .header .top,
.blog .header .bot{
	max-width: 1060px;
	margin-left: auto;
	margin-right: auto;
	padding: 0 15px;
}
.blog .header .top{
	margin: 0 auto 20px;
}
.blog .header .bot{
	margin: 20px auto 0;
}
.blog .header .middle{
	min-height: 550px;
	background: url("../images/blog-bg.png")center center no-repeat;
	width: 100%;
	background-size: cover;
	position: relative;
}
.blog .header .middle .txt{
	text-align: center;
	position:absolute; 
	left:50%; 
	top:50%;
	-webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  margin: 0 -40% 0 0;
}
.blog .header .middle .txt span{
	color: #fff;
	display: inline-block;
	margin: 20px 0 0 0;
}
.blog .main > .block{
	border: none;
	line-height: 0;
	font-size: 0;
	overflow: hidden;
}
.blog .main > .block .img-box{
	display: inline-block;
	width: 66.66%;
	-moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  vertical-align: top;
}
.blog .main > .block .r-box{
	display: inline-block;
	width: 33.33%;
	vertical-align: top;
	-moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0 0 0 30px;
}
.blog .main > .block .r-box h2{
	text-align: left;
	margin: 0 0 20px;
}
.blog .main > .block .r-box p{
	font: 400 16px/22px Ubuntu, Helvetica, sans-serif;
}
.blog .articles{
	margin: 30px 0 0 0;
}
.blog .articles > h3{
	margin: 0;
}
.sub-title-xs {
    display: none;
}
.sub-footer-xs  {
    display: none;
}

.header .fa-search.mob, .header .search.mob {
    display: none !important;
}

#mob-login, #mob-regis {
    display: none;
}

@media (max-width: 1300px) {
    #top{
		display: none;
	}
}
@media (max-width: 1000px){
	.header .menu{
		display: block;
	}
	.header-wrap{
		position: relative;
	}
	.header .logo{
		display: block;
		float: none;
		margin: 0 0 15px;
	}
	.header .sign{
		display: block;
		float: none;
	}
	.header .top{
		margin: 0 0 15px;
	}
	.header .bot{
		height: auto;
	}
	.header .bot .links{
		position:absolute; 
		right:0; 
		top:100%;
		display: none;
		background: #fff;
		z-index: 99;
		padding: 0 20px;
		border: 1px solid #eeeeee;
	}
	.blog .header .bot .links{
		top: 143px;
	}
	.header .bot .links li{
		display: block;
		float: none;
		margin: 10px 0;
	}
	.header .bot .links li:last-child:before{
		display: none;
	}
	.header .bot .links li:last-child{
		padding: 0;
		margin: 10px 0;
	}
	.header .bot .search{
		float: none;
		display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    border: none;
	}
	.header .bot .search i{
		float: none;
	}
	.header .bot .search input{
		-webkit-box-flex: 1;
    -ms-flex: 1;
        flex: 1;
    margin: 0 0 0 15px;
	}
	.footer .logo{
		float: none;
		display: block;
		margin: 0 auto 30px;
	}
	.footer .sub,
	.footer .sub button,
	.footer .sub input,
	.footer .sub span{
		float: none;
		height: auto;
	}
	.footer .sub button,
	.footer .sub input,
	.footer .sub span{
		margin: 0 auto 20px;
		display: block;
		text-align: center;
	}
	.blog .main > .block .r-box,
	.blog .main > .block .img-box{
		width: 100%;
		padding: 0;
	}
}
@media (max-width: 800px){
	.main .art-list li{
		width: 50%;
	}
	.main .btns .soc{
		position: static;
		margin: 20px 0;
	}
	.main .btns .soc a{
		margin: 0 5px;
	}
}
@media (max-width: 550px){
    .details .breadcrumbs-block {
        display: none !important;
    }

    .main.details h2 {
        font-size: 20px;
        margin-top: 0px;
    }
    .details .soc {
        display: none;
    }
    .details .n-gray-btn, .details .n-green-btn {
        margin-top: 5px;
        margin-bottom: 10px;
    }
    .header .bot .search {
        display: none;
    }
    .header .bot .search.mob {
        display: block !important;
    }
    .header .fa-search.mob {
        display: block !important;
        position: absolute;
        right: 45px;
        top: 32px;
        width: 22px;
        font-size: 17px;
    }

    .blog .header .top, .blog .header .bot {
        margin-bottom: 5px;
        margin-top: 0px;
    } 
    
    .header .bot .search.mob {
        height: 100%;
        line-height: 0px;
    } 
    
    .main .art-list {
        margin: 0;
    }
	.main .art-list li{
		width: 100%;
		padding: 0 0 50px;
	}
    .sub-block {
        display: none;
    }

    .sub-title-xs {
        display: block;
    }

    .sub-footer-xs {
        display: block;
        margin: 0px;
        width: 100%;
    }
    .sub-img-block {
        width: 100% !important;
    }
    #subscribe {
        flex-direction: column;
        align-items: baseline !important;
        display: none !important;
    }
    .sign-in {
        display: none;
    }
    .header-wrap {
        padding: 20px 0px 0px;
    }
    .blog .header .bot .links {
        top: 60px;
    }
    #mob-login {
        border-top: 1px solid #e6e6e6;
        padding-top: 10px;
    }
    #mob-login, #mob-regis {
        display: block;
    }
    .sub-footer-xs .sub.clear input {
        margin-right: 115px !important;
        padding-right: 35px;
    }

    .sub-footer-xs .sub button {
        position: absolute;
        right: 0px;
    }
}

.content h1 {
    color: black;
}

.content h2 {
    text-align: left;
    font-size: 30px;
}

.sign-in {
    float: right;
    margin: 0;
    position: relative;
}

    .sign-in .def-btn {
        margin: 0 25px 0 0;
        font-size: 16px;
    }

#user-name {
    color: #00b898;
    font-size: 18px;
    margin: 10px 20px -2px 0;
    text-transform: none;
    vertical-align: middle;
    cursor: pointer;
    background: url("../images/select-arrow.png") right center no-repeat;
    padding: 0 15px 0 0;
}

#settings-name {
    color: #00b898;
}

.head-drop {
    padding: 20px;
    border: 1px solid #ebebeb;
    background: #fff;
    max-width: 250px;
    width: 100%;
    box-sizing: border-box;
    position: absolute;
    right: 104px;
    top: 50px;
    z-index: 1;
}

    .head-drop:after {
        content: "";
        background: url("../images/drop-arr.jpg") no-repeat;
        width: 21px;
        height: 10px;
        position: absolute;
        top: -10px;
        right: 30px;
    }

    .head-drop .av {
        display: flex;
        align-items: center;
        padding: 0 0 15px;
    }

        .head-drop .av img {
            display: inline-block;
            vertical-align: middle;
            width: 65px;
            height: 65px;
            border-radius: 100%;
            padding: 0 0px 0 0;
            margin-right: 15px;
            box-sizing: content-box;
        }

        .head-drop .av div {
            display: inline-block;
            vertical-align: middle;
        }

    .head-drop .settings {
        border-bottom: 1px solid #ebebeb;
    }

        .head-drop .settings a {
            color: #000;
            font: 14px/18px Ubuntu,Helvetica,sans-serif;
            display: table;
            margin: 0 0 10px;
        }

            .head-drop .settings a:hover {
                opacity: 0.7;
            }

    .head-drop .links {
        margin: 15px 0 0;
    }

        .head-drop .links a {
            color: #000;
            font: 14px/18px Ubuntu,Helvetica,sans-serif;
            display: block;
            margin: 0 0 10px;
            height: 40px;
            border-radius: 20px;
            font: 700 14px/36px Ubuntu,Helvetica,sans-serif;
            text-transform: uppercase;
            text-align: center;
        }

            .head-drop .links a:nth-child(1) {
                border: 2px solid #d5731a;
            }

            .head-drop .links a:nth-child(2) {
                border: 2px solid #f9b61e;
            }

            .head-drop .links a:nth-child(3) {
                border: 2px solid #3db28e;
            }

            .head-drop .links a:hover {
                opacity: 0.7;
                text-decoration: none;
            }

    .head-drop.show-menu {
        display: block !important;
    }

.def-btn.logout-button {
    margin-top: 13px;
}

.author-title {
    font: 16px/24px Ubuntu, Helvetica, sans-serif !important;
    text-align: center;
}

.author-title .head-img {
    border-radius: 100%;
    max-width: 32px;
    max-height: 32px;
}

.hero-img {
    display: block;
    text-align: center;
    text-align: -webkit-center;
}

.hero-img img {
    margin: auto;
}

.breadcrumbs-block {
    display: none;
    align-items: center;
    margin-bottom: 30px;
}

.breadcrumb-item {
    color: #ccc;
    cursor: pointer;
    font-weight: 500;
}

.breadcrumb-item:hover {
    color: #ccc;
    cursor: pointer;
}

.breadcrumb-item:last-child {
    color: black;
}

.breadcrumb-item-divider {
    color: #ccc;
    margin: 0px 10px 3px;
}

.subscribe-content {
    color: black;
    display: flex;
    justify-content: center;
    flex-direction: column;
    height: calc(100vh - 320px);
    align-items: center;
}

.subscribe-content h1 {
    color: black;
    font-size: 60px;
    font-weight: 600;
    margin-top: 0px;
}

.subscribe-content h3 {
    font-weight: 500;
}

.subscribe-content .grey {
    margin-top: 30px;
    color: #adadad;
    font-weight: 400;
}

.subscribe-content .footer {
    margin-top: 30px;
}

.head-img {
    max-width: 32px;
    max-height: 32px;
    display: inline-block;
    border-radius: 100%;
    margin-right: 10px;
}

.d-f{
    display: flex;
}
.a-i-c {
    align-items: center;
}

.sub-img {
    width: 100px;
    margin-right: 25px;
}

#subscribe {
    display: none;
    justify-content: center;
    align-items: center;
    padding: 20px;
    border: 2px solid #eee;
    position: relative;
}

.sub-block h4 {
    margin-top: 0px;
    margin-right: 100px;
    font-size: 20px;
}

.sub-close {
    position: absolute;
    right: 7px;
    top: 5px;
    font-size: 18px;
}

.sub-close:hover {
    cursor: pointer;
    opacity: 0.7;
}

.sub-footer {
    padding: 0px;
}

.sub-footer .sub.clear {
    width: 100%;
    display: flex;
    flex-direction: row-reverse;
}

.sub-footer .sub.clear input {
    width: 100%;
    margin: 0px 15px 0px 0px;
    height: 42px;
    text-align: left;
}

.sub-img-block {
    width: 125px;
    display: flex;
    align-items: center;
}

.pagination li.active a {
    background: #3db28e;
    border-color: #3db28e;
}
.pagination li.active a:hover {
    background: #339678;
    border-color: #339678;
}

.pagination li span {
    color: #3db28e;
}

.footer-custom {
    background: #191919;
    padding: 3%;
    color: #8f8f8f;
    font-size: 14px;
    line-height: 1.2;
    position: relative;
}

.footer-custom__bg {
    position: absolute;
    left: 50%;
    bottom: 0;
    transform: translateX(-50%);
    height: 460px;
    object-fit: cover;
    z-index: 1;
}

.footer-custom h4 {
    margin: 0 0 10px;
    font-size: 18px;
    color: #fff;
}

.footer-custom a {
    color: #8f8f8f;
}

.footer-custom__wrap {
    max-width: 1500px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.footer-custom__row {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.footer-custom__col {
    padding: 0 10px;
    width: 25%;
    flex-grow: 0;
}

.footer-custom__col_grow {
    flex-grow: 1;
}

.footer-custom__col_full-size {
    width: 100%;
}

.footer-custom__list {
    margin: 0 0 40px;
    padding: 0;
    list-style: none;
}

.footer-custom__list li {
    margin: 0 0 20px;
}

.footer-custom__list dt {
    color: #fff;
    font-size: 14px;
    line-height: 1;
}

.footer-custom__list dd {
    margin: 0 0 15px;
}

.footer-custom__list dt:first-child {
    font-size: 18px;
    margin: 0 0 10px;
}

.footer-custom__list dt:first-child + dd {
    margin: 0 0 15px;
}

.footer-custom__logo {
    display: inline-block;
    margin: 0 0 40px;
}

.footer-custom__copy {
    text-align: right;
}

.footer-custom__list_wrap {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    height: 145px;
}

.footer-custom__list_wrap li {
    margin-right: 1.5%;
    width: 24%;
}

.footer-custom__soc-list {
    display: flex;
    margin: 0 0 30px;
}

.footer-custom__soc-list li {
    height: 15px;
    width: 15px;
    margin: 0 15px 0 0;
}

.footer-custom__soc-list a {
    display: block;
    height: 100%;
    overflow: hidden;
}

.footer-custom__soc-list img {
    object-fit: contain;
    height: 100%;
    width: 100%;
}

.footer-custom__soc-list a:hover {
    opacity: .8;
}

.footer-custom .handle-glyph {
    position: absolute;
    color: #3cb08c;
    font-size: 15px;
    font-family: 'Glyphicons Halflings' !important;
    top: 20px;
    right: 15px;
    pointer-events: none;
    display: none;
}

@media only screen and (max-width: 800px) {
    .footer-custom__col_mobile {
        width: 100%;
        display: flex;
        justify-content: space-between;
    }

    .footer-custom__soc-list {
        justify-content: center;
    }

    .footer-custom__soc-list li {
        height: 30px;
        width: 30px;
    }

    .footer-custom__copy {
        text-align: center;
    }
}

@media only screen and (max-width: 600px) {
    .footer-custom__row {
        flex-direction: column;
    }

    .footer-custom__col {
        width: 100%;
        flex-direction: column;
        padding: 0;
        margin: 0;
        position: relative;
    }

    .footer-custom__col_grow {
        margin: 0 0 40px;
    }

    .footer-custom__col_grow .footer-custom__col {
        border-bottom: 1px solid #797979;
    }

    .footer-custom__col_grow .footer-custom__col:first-child {
        border-top: 1px solid #797979;
    }

    .footer-custom h4 {
        font-size: 20px;
        padding: 20px;
        margin: 0;
    }

    .footer-custom h4 br {
        display: none;
    }

    .footer-custom h4 + .footer-custom__list {
        display: none;
    }

    .footer-custom__list {
        padding: 0 10%;
    }

    .footer-custom h4 + .footer-custom__list.footer-custom__list_active {
        display: block;
    }

    .footer-custom__list_wrap {
        height: auto;
    }

    .footer-custom__logo {
        display: flex;
        justify-content: center;
        width: 100%;
    }

    .footer-custom__col_mobile {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;
    }

    .footer-custom__col_mobile dl.footer-custom__list {
        padding: 0;
        width: 48%;
    }

    .footer-custom .handle-glyph {
        display: block;
    }
}
@media only screen and (max-width: 1600px) {
    .footer-custom {
        zoom: 0.75;
    }
}