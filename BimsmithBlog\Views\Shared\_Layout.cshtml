﻿@using System.Configuration;
@using BIMsmithMarket.Core.Helpers
@using Microsoft.AspNetCore.Http.Extensions

<!DOCTYPE html>
<html>
<head>
    @Html.Hidden("BimsmithUrl", @ConfigurationHelper.GetValue("BimsmithUrl"))
    <meta charset="utf-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, maximum-scale=1, target-densityDPI=device-dpi" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="google-site-verification" content="S931UZF3IQ9ehCHUtrDeFGsTowM-hC7cX-ar3sRWYVI" />
    <meta name="msvalidate.01" content="5533F7259691FCA7EF63EFB3CDED200F" />

    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=UA-39604333-4"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', 'UA-39604333-4');
    </script>

    @if (ViewBag.IsDetailPage != null && ViewBag.IsDetailPage)
    {
        <title>@ViewBag.Title</title>
        if (ViewBag.IsPrivate)
        {
            <meta name="robots" content="noindex">
        }
        <meta name="description" content="@ViewBag.MetaDescription">
        <meta name="keywords" content="@ViewBag.MetaKeywords">

        <meta property="og:title" content="@ViewBag.Title" />
        <meta property="og:description" content="@ViewBag.MetaDescription" />
        <meta property="og:image" content="@ViewBag.OgImageUrlBig" />
        <meta property="og:author" content="@ViewBag.OgAuthorTitle" />
        <meta property="og:type" content="article" />
        <meta name="author" content="@ViewBag.OgAuthorTitle">

        <meta name="twitter:card" content="summary_large_image">
        <meta name="twitter:title" content="@ViewBag.Title">

        <script src='https://www.google.com/recaptcha/api.js'></script>
    }
    else
    {
        <title>BIMsmith Blog - Everything Revit, Architecture, and Building Products</title>
        <meta name="description" content="Original and curated content by longtime members and experts in the AEC community. Join the conversation for everything from Revit families to building products to architecture and design inspiration.">
        <meta name="keywords" content="bimsmith,blog,articles,building products,revit,architecture,design">
        <meta property="og:image" content="https://blog.bimsmith.com/images/BIMsmith-blog-header-600x320.png" />
    }
    <meta property="og:url" content="@(Context.Request.GetDisplayUrl() ?? string.Empty)" />

    <link rel="icon" type="image/x-icon" href="~/favicon.png">


    <link rel="stylesheet" type="text/css" media="all" href="~/Css/font-awesome.css" />
    <link rel="stylesheet" type="text/css" media="all" href="~/Css/all.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css" integrity="sha384-HSMxcRTRxnN+Bdg0JdbxYKrThecOKuH5zCYotlSAcp1+c8xmyTe9GYg1l9a69psu" crossorigin="anonymous">
    <link href="https://fonts.googleapis.com/css?family=Ubuntu:300,300i,400,400i,500,500i,700,700i" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet" />

    <script src='~/Scripts/jquery-3.2.1.min.js'></script>
    <script src='~/Scripts/main.js'></script>
    <script src='~/Scripts/notifications.js'></script>
    <script type='text/javascript' src='https://cdnjs.cloudflare.com/ajax/libs/js-cookie/2.1.4/js.cookie.min.js'></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script type="text/javascript">
        if (!window.document.referrer || ((window.document.referrer.indexOf('blog.bimsmith.com') == -1) && (window.document.referrer.indexOf('http://localhost:53436') == -1))) {
            localStorage.setItem('session', 'true');
        }
    </script>
</head>
<body onload="onBodyLoad()">
    <div class="wrapper blog">
        @RenderBody()
        <footer class="footer clear">
            <div class="clear">
                @using (Html.BeginForm("Subscribe", "Blog", FormMethod.Post, new { id = "logoutForm" }))
                {
                    <div class="sub clear">
                        <button type="submit">Subscribe</button>
                        <input type="email" name="email" placeholder="Enter your email here" required />
                        <span>Subscribe to receive regular updates</span>
                    </div>
                }
            </div>
            <div id="top"><i class="fa fa-angle-up"></i></div>
        </footer>
        <div class="footer-custom">
          <img class="footer-custom__bg" src="~/images/footer-bg.png" alt="bg">
          <div class="footer-custom__wrap">
            <div class="footer-custom__row">
              <div class="footer-custom__col footer-custom__col_mobile">
                <a class="footer-custom__logo" href="@ConfigurationHelper.GetValue("BimsmithUrl")/">
                  <img src="~/images/logo-footer.png" alt="BIMsmith">
                </a>
                <dl class="footer-custom__list">
                  <dt>BIMsmith Headquarters</dt>
                  <dd>68 S. Grove Ave, Elgin, IL 60120 USA</dd>
                  <dt>Sales</dt>
                  <dd>
                    <div>
                      <a href="tel:+1225054246">+1(224)505-4246</a>
                    </div>
                    <div>
                      <a href="mailto:<EMAIL>"><EMAIL></a>
                    </div>
                  </dd>
                  <dt>Support</dt>
                  <dd>
                    <div>
                      <a href="tel:+12246999545">+1(224)699-9545</a>
                    </div>
                    <div>
                      <a href="mailto:<EMAIL>"><EMAIL></a>
                    </div>
                  </dd>
                </dl>
                <dl class="footer-custom__list">
                  <dt>BIMsmith UK & Europe</dt>
                  <dd>
                    Atrium Camden, 2 North Yard,
                    <br>
                    Chalk Farm Rd, London NW1 8AH
                  </dd>
                  <dt>Sales</dt>
                  <dd>
                    <div>
                      <a href="tel:+4402033656255">+44(0)************</a>
                    </div>
                    <div>
                      <a href="mailto:<EMAIL>"><EMAIL></a>
                    </div>
                  </dd>
                  <dt>Support</dt>
                  <dd>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                  </dd>
                </dl>
              </div>
              <div class="footer-custom__col footer-custom__col_grow">
                <div class="footer-custom__row">
                  <div class="footer-custom__col">
                    <h4>
                      ANGULERIS
                      <br>
                      TECHNOLOGIES
                    </h4>
                    <ul class="footer-custom__list">
                      <li class="footer-custom__list-element">
                        <a href="https://anguleris.com/bim-strategy">BIM Strategy</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="https://anguleris.com/bim-strategy/bim-content-creation">BIM Content Creation</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="https://anguleris.com/bim-strategy/intelligent-distribution">Intelligent BIM Distribution</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="https://anguleris.com/bim-strategy/website-optimization">Website Optimization</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="https://anguleris.com/custom-software-development">Custom BIM Software</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="https://www.swatchbox.com">Sample Programs</a>
                      </li>
                    </ul>
                    <span class="glyphicon glyphicon-chevron-down handle-glyph"></span>
                  </div>
                  <div class="footer-custom__col">
                    <h4><br>BIMSMITH</h4>
                    <ul class="footer-custom__list">
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("MarketUrl")">BIMsmith Market</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("ForgeUrl")/Welcome">BIMsmith Forge</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("BlogUrl")/">BIMsmith Blog</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("MarketUrl")/revit-materials">Revit Materials</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("MarketUrl")/revit-families">Revit Families</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("BimsmithUrl")/revit-plugin">Revit Plugin</a>
                      </li>
                    </ul>
                    <span class="glyphicon glyphicon-chevron-down handle-glyph"></span>
                  </div>
                  <div class="footer-custom__col">
                    <h4><br>COMMUNITY</h4>
                    <ul class="footer-custom__list">
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("BlogUrl")/">Blog</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("BimsmithUrl")/contact">Contact</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("BimsmithUrl")/contact">Support</a>
                      </li>
                    </ul>
                    <span class="glyphicon glyphicon-chevron-down handle-glyph"></span>
                  </div>
                  <div class="footer-custom__col">
                    <h4><br>LEGAL</h4>
                    <ul class="footer-custom__list">
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("BimsmithUrl")/legal/privacy-policy">Privacy Policy</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("BimsmithUrl")/legal/terms-and-conditions">Terms and Conditions</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("BimsmithUrl")/legal/patents-and-intellectual-property">Patents and Intellectual Property</a>
                      </li>
                    </ul>
                    <span class="glyphicon glyphicon-chevron-down handle-glyph"></span>
                  </div>
                  <div class="footer-custom__col footer-custom__col_full-size">
                    <h4><br>TOP CATEGORIES</h4>
                    <ul class="footer-custom__list footer-custom__list_wrap">
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("MarketUrl")/category/Walls_revit">Walls</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("MarketUrl")/category/Floor-revit">Floors</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("MarketUrl")/category/Ceilings_revit">Ceilings</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("MarketUrl")/category/Roofing_revit">Roofs</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("MarketUrl")/category/Lighting_revit">Lighting</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("MarketUrl")/category/Furniture_revit">Furniture</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("MarketUrl")/category/Doors_revit">Doors</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("MarketUrl")/category/Windows-revit">Windows</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("MarketUrl")/category/HVAC_revit">HVAC</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("MarketUrl")/category/Paints__Coatings_revit">Paint</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("MarketUrl")/category/Tile_revit">Tile</a>
                      </li>
                      <li class="footer-custom__list-element">
                        <a href="@ConfigurationHelper.GetValue("MarketUrl")/category/Decking_revit">Decking</a>
                      </li>
                      <li class="footer-custom__list-element"></li>
                    </ul>
                    <span class="glyphicon glyphicon-chevron-down handle-glyph"></span>
                  </div>
                </div>
              </div>
            </div>
            <div class="footer-custom__row">
              <div class="footer-custom__col footer-custom__col_full-size">
                <ul class="footer-custom__soc-list">
                  <li>
                    <a href="https://www.facebook.com/thebimsmith/">
                      <img src="~/images/soc-fb.png" alt="Face Book">
                    </a>
                  </li>
                  <li>
                    <a href="https://twitter.com/thebimsmith">
                      <img src="~/images/soc-tw.png" alt="Twitter">
                    </a>
                  </li>
                  <li>
                    <a href="https://www.youtube.com/channel/UCAPJyryHrdzN5nTxYSvFqNQ">
                      <img src="~/images/soc-youtube.png" alt="You Tube">
                    </a>
                  </li>
                  <li>
                    <a href="https://www.linkedin.com/company/10901027?trk=tyah&trkInfo=clickedVertical%3Ashowcase%2CclickedEntityId%3A10901027%2Cidx%3A1-1-1%2CtarId%3A1478971756516%2Ctas%3ABIMsmi">
                      <img src="~/images/soc-in.png" alt="LinkedIn">
                    </a>
                  </li>
                  <li>
                    <a href="https://www.pinterest.com/thebimsmith/">
                      <img src="~/images/soc-p.png" alt="Pinterest">
                    </a>
                  </li>
                  <li>
                    <a href="https://instagram.com/thebimsmith">
                      <img src="~/images/instagram_white.png" alt="Instagram">
                    </a>
                  </li>
                </ul>
              </div>
            </div>
            <div class="footer-custom__copy">&copy; @DateTime.UtcNow.Year Anguleris Technologies</div>
          </div>
        </div>
    </div>

    <script src="https://cdn.ckeditor.com/4.7.0/full-all/ckeditor.js"></script>
    <script src="~/bundles/jquerybundle.min.js"></script>
    <script src="~/bundles/bootstrapjsbundle.min.js"></script> 
    @RenderSection("scripts", required: false)

    <script>
        function onBodyLoad() {

            @if (!string.IsNullOrWhiteSpace(ViewBag.NotificationInfo))
            {
                @:notificationInfo('@ViewBag.NotificationInfo');
            }

            }
    </script>

</body>
</html>
