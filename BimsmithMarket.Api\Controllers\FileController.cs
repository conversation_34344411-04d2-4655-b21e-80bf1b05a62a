﻿using Azure.Storage.Blobs.Specialized;
using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Api.Services;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using BIMsmithMarket.Services.PaymentServices;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// File Controller
    /// </summary>
    [Route("api/[controller]/[action]")]
    public class FileController : BaseApiController
    {
        /// <summary>
        /// The log manager
        /// </summary>
        private readonly SendAnalyticsService _sendAnalyticsService;
        private readonly IFileService _fileService;
        private readonly IPaymentService _paymentService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly SlackWebHook _slackWebHook;

        public FileController(
            IFileService fileService,
            IHttpClientFactory httpClientFactory,
            SendAnalyticsService sendAnalyticsService,
            SlackWebHook slackWebHook,
            IPaymentService paymentService
            )
        {
            _fileService = fileService;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;
            ServicePointManager.ServerCertificateValidationCallback += (sender, certificate, chain, sslPolicyErrors) => true;
            _httpClientFactory = httpClientFactory;
            _sendAnalyticsService = sendAnalyticsService;
            _slackWebHook = slackWebHook;
            _paymentService = paymentService;
        }

        /// <summary>
        /// Gets the zip. 
        /// Used for any file download by front end
        /// Should be use for any download
        /// </summary>
        /// <param name="model">The model with file identifiers</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> DownloadFiles(DownloadFilesDto model)
        {
            try
            {
                if (!model.FileIds.Any())
                {
                    return NotFound();
                }

                int[] productFileIds = model.FileIds.Distinct().OrderBy(a => a).ToArray();
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);

                using (IUnitOfWork unitOfWork = UnitOfWork.Create())
                {
                    var filesCheckResult = await _paymentService.CheckSelectedFilesAsync(productFileIds, unitOfWork, userId, model.ProductId);
                    if (filesCheckResult.Any(x => !x.DownloadAvailable))
                    {
                        return Ok(filesCheckResult);
                    }

                    bool downloadConfirmation = true;
                    string localFileFolder = Path.Combine(Path.GetTempPath(), "Files");
                    var initDownloadZipResult = await _fileService.InitDownloadZipAsync(productFileIds, model.ProductId, null, localFileFolder, downloadConfirmation);
                    if (initDownloadZipResult == null)
                    {
                        return NotFound();
                    }

#if RELEASE
                await SendSlackDownloadZipMessage(model.FileName);
#endif
                    var sasToken = _fileService.GenerateSasTokenForArchive(model.FileName, initDownloadZipResult.File);
                    Stream blobStream = await initDownloadZipResult.File.OpenReadAsync();
                    if (blobStream.Length <= 22)
                    {
                        initDownloadZipResult.File.Delete();
                        return NotFound(new { message = "Empty archive was created, cant find any content!" });
                    }
                    return Ok(new { downloadUrl = initDownloadZipResult.File.Uri.AbsoluteUri + sasToken, filesSkipped = initDownloadZipResult.FilesSkipped });
                }
            }
            catch (Exception ex)
            {
                Log.Error($"File Controller DownloadFiles. Exception: {ex.GetAllMessages()}. Model: {JsonConvert.SerializeObject(model)}");
                return StatusCode(StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// Used for external integration 
        /// </summary>
        /// <param name="fileId"> file id</param>
        /// <param name="productId"></param>
        /// <param name="manufacturerId"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> Download(int fileId, int productId, int manufacturerId)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var file = await unitOfWork.FileRepository.GetByIdAsync(fileId);
                var product = await unitOfWork.ProductRepository.GetByIdAsync(productId);
                if (file == null)
                {
                    return NotFound();
                }

                try
                {
                    var newAnalyticsEvents = await _sendAnalyticsService.GenerateProductTypeNewAnalyticsEvents(fileId, productId, manufacturerId, Request, unitOfWork);
                    await _sendAnalyticsService.SendEventsToNewAnalytics(newAnalyticsEvents);
                }
                catch (Exception ex)
                {
                    LogHelper.LogInfo(ConfigurationHelper.GetValue("DownloadFileLogPath"), $"ERROR FILECONTROLLER File download Analytics Send FileId: {fileId} ProductId: {productId} ManufacturerId: {manufacturerId} Referer: {Request.Headers.Referer} Requested Url {Request.GetDisplayUrl()} Exception: {ex.GetAllMessages()}");
                }

                try
                {
                    var httpClient = _httpClientFactory.CreateClient();
                    {
                        var downloadResult = await httpClient.GetByteArrayAsync(file.Url);
                        if (product != null && !product.Published)
                            Response.Headers.Add("X-Robots-Tag", "noindex");
                        return File(downloadResult, "application/octet-stream", file.FileName ?? file.Title ?? "File");
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.LogInfo(ConfigurationHelper.GetValue("DownloadFileLogPath"), $"ERROR FILECONTROLLER File download File Get FileId: {fileId} ProductId: {productId} ManufacturerId: {manufacturerId} Referer: {Request.Headers.Referer} Requested Url {Request.GetDisplayUrl()} Exception: {ex.GetAllMessages()}");
                    return StatusCode(StatusCodes.Status500InternalServerError);
                }
            }
        }

        /// <summary>
        /// Used for attachs, return pdf to show, not as download
        /// </summary>
        /// <param name="id"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetForPreview(int id, string type = "inline")
        {
            string folder = Path.GetTempPath();

            AzureStorageService azureBlobProvider = new AzureStorageService(
                ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                ConfigurationHelper.GetValue("Environment"),
                bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                false);
            var filesContainer = azureBlobProvider.GetContainerByName(AzureStorageConstants.FilesContainer);

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var dbFile = await unitOfWork.FileRepository.GetAll().FirstOrDefaultAsync(a => a.Id == id);

                if (dbFile != null)
                {
                    var blobName = Path.GetFileName(dbFile.Url);
                    BlockBlobClient blobFile = filesContainer.GetBlockBlobClient(blobName);

                    if (blobFile.Exists())
                    {
                        if (type == "txt")
                        {
                            var stream = blobFile.OpenRead();
                            return File(stream, "text/plain", blobName);
                        }
                        else
                        {
                            var stream = blobFile.OpenRead();
                            return File(stream, dbFile.MediaType);
                        }
                    }
                    else
                    {
                        var updateUrl = dbFile.Url;
                        if (updateUrl.StartsWith("https"))
                        {
                            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
                        }

                        if (updateUrl[updateUrl.Length - 1] == '/')
                        {
                            updateUrl = updateUrl.Remove(updateUrl.Length - 1);
                        }
                        updateUrl = updateUrl.ToLower();

                        try
                        {
                            var httpClient = _httpClientFactory.CreateClient();
                            {
                                httpClient.Timeout = TimeSpan.FromMinutes(5);

                                var fileBytes = await httpClient.GetByteArrayAsync(updateUrl);

                                if (type == "inline")
                                    return File(fileBytes, dbFile.MediaType);
                                else
                                    return File(fileBytes, dbFile.MediaType, Path.GetFileName(updateUrl));
                            }
                        }
                        catch (Exception ex)
                        {
                            return NotFound(ex.Message);
                        }
                    }
                }
                else
                {
                    return NotFound("File not exists");
                }
            }
        }

        /// <summary>
        /// Return all product files with selected type 
        /// used on Product microsites (There are some files that can be downloaded without Auth)
        /// </summary>
        /// <param name="productId"></param>
        /// <param name="fileType"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetProductFiles(int productId, int fileType = 1)
        {
            var initDownloadZipResult = await _fileService.InitDownloadZipAsync(null, productId, fileType, localFileFolder: Path.GetTempPath());
            if (initDownloadZipResult == null)
            {
                return NotFound();
            }

            var cd = new System.Net.Mime.ContentDisposition
            {
                FileName = _fileService.FixFileName(fileType.ToString() + productId) + ".zip",
                Inline = false,
            };

#if RELEASE
            await SendSlackDownloadZipMessage($"{fileType.ToString()} was downloaded on Product {productId}");
#endif

            Response.Headers.Add("Content-Disposition", cd.ToString());
            Response.Headers.Add("Access-Control-Allow-Headers", "Range");
            Response.Headers.Add("Access-Control-Expose-Headers", "Accept-Ranges, Content-Encoding, Content-Length, Content-Range");

            Stream blobStream = await initDownloadZipResult.File.OpenReadAsync();
            if (blobStream.Length <= 22)
            {
                initDownloadZipResult.File.Delete();
                return NotFound("Empty archive was created, cant find any content!");
            }
            return File(blobStream, "application/zip", cd.FileName);
        }

        /// <summary>
        /// Gets the zip with all manufacturer project files with specified project data types
        /// </summary>
        /// <param name="model">Model with parameters</param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        public async Task<IActionResult> DownloadManufacturerProjectFiles(DownloadManufacturerProjectFilesDto model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var blobZipFile = await _fileService.GetManufacturerProjectFilesZipAsync(model, unitOfWork);
                if (blobZipFile == null)
                    return NotFound();

                var sasToken = _fileService.GenerateSasTokenForArchive(model.FileName, blobZipFile);
                Stream blobStream = await blobZipFile.OpenReadAsync();
                if (blobStream.Length <= 22)
                {
                    blobZipFile.Delete();
                    return NotFound(new { message = "Empty archive was created, cant find any content!" });
                }

                return Ok(new { downloadUrl = blobZipFile.Uri.AbsoluteUri + sasToken });
            }
        }

        #region Private methods
        private async Task SendSlackDownloadZipMessage(string fileName)
        {

            //Send to Slack
            //----------------------
            string email = null;
            if (this.User != null && this.User.Identity != null)
            {
                email = AuthHelper.GetUserInfo(Request, ClaimTypes.Email);
            }
            await _slackWebHook.SendDownloadZipMessage(email, fileName);
            //----------------------
        }
        #endregion
    }
}