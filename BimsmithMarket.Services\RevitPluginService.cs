﻿using BIMsmithMarket.Domain.Dto.RevitPlugin;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class RevitPluginService : IRevitPluginService
    {
        public async Task<object> GetPluginItemsAsync(IUnitOfWork unitOfWork, ICategoryService categoryService)
        {
            var treeCategories = await categoryService.TreeAsync(unitOfWork);
            int limit = 20;
            int[] treeCategoryIds = treeCategories.OrderByDescending(x => x.count).Select(x => (int)x.id).ToArray();
            Dictionary<int, int> categoryCount = treeCategories.ToDictionary(x => (int)x.id, x => (int)x.count);

            var categories = await unitOfWork.CategoryRepository.GetAll()
                        .Where(c => c.IncludeRevitPlugin
                                 && c.IconUrl.StartsWith("http")
                                 && treeCategoryIds.Contains(c.Id))
                        .Select(c => new RevitPluginCategoryDto
                        {
                            Id = c.Id,
                            Name = c.Name,
                            VanityUrl = c.VanityUrl,
                            RevitIcon = new RevitPluginCategoryIconDto
                            {
                                OriginalUrl = c.RevitPluginPhoto.OriginalImgUrl ?? c.IconUrl,
                                SmallUrl = c.RevitPluginPhoto.SmallImgUrl ?? c.IconUrl
                            },
                            Count = 0
                        })
                        .ToArrayAsync();

            Parallel.ForEach(categories, x => x.Count = categoryCount[x.Id]);
            categories = categories.OrderByDescending(x => x.Count).Take(limit).OrderBy(x => x.Name).ToArray();

            var manufacturers = await unitOfWork.ManufacturerRepository.GetAll()
                .Where(c => c.IncludeRevitPlugin)
                .Select(c => new
                {
                    id = c.Id,
                    promotedId = c.PromotedId,
                    name = c.Name,
                    vanityUrl = c.HubVanityURL,
                    revitIcon = new
                    {
                        originalUrl = c.RevitPluginPhoto.OriginalImgUrl,
                        smallUrl = c.RevitPluginPhoto.SmallImgUrl
                    }
                })
                .OrderBy(x => x.name)
                .ToListAsync();

            return new
            {
                categories = categories,
                manufacturers = manufacturers
            };
        }

        public async Task<object> GetPromotedManufacturers(IUnitOfWork unitOfWork, int count)
        {
            var response = await unitOfWork.ManufacturerRepository.GetAll()
                        .Where(c => c.IncludeRevitPlugin && c.PromotedId != null)
                        .OrderBy(c => c.PromotedId)
                        .Select(c => new
                        {
                            id = c.Id,
                            promotedId = c.PromotedId.Value,
                            name = c.Name,
                            vanityUrl = c.HubVanityURL,
                            revitIcon = new
                            {
                                originalUrl = c.RevitPluginPhoto.OriginalImgUrl,
                                smallUrl = c.RevitPluginPhoto.SmallImgUrl
                            }
                        })
                        .Take(count)
                        .ToListAsync();

            return response;
        }

        public async Task<object> PromoteManufacturersAsync(IUnitOfWork unitOfWork, PromoteManufacturersModel manufacturers)
        {
            var oldPromotedManufacturers = await unitOfWork.ManufacturerRepository.GetAll().Where(m => m.PromotedId != null).ToListAsync();
            foreach (var oldPromoted in oldPromotedManufacturers)
            {
                oldPromoted.PromotedId = null;
                unitOfWork.ManufacturerRepository.Edit(oldPromoted);
            }
            await unitOfWork.SaveAsync();

            var promotedManufacturersId = manufacturers.ManufacturersToPromote.Select(p => p.Id).ToList();
            var newPromotedManufacturers = await unitOfWork.ManufacturerRepository.GetAll().Where(m => promotedManufacturersId.Contains(m.Id) && m.IncludeRevitPlugin).ToListAsync();
            foreach (var newPromoted in newPromotedManufacturers)
            {
                newPromoted.PromotedId = manufacturers.ManufacturersToPromote.First(m => m.Id == newPromoted.Id).Order;
                unitOfWork.ManufacturerRepository.Edit(newPromoted);
            }
            await unitOfWork.SaveAsync();

            var response = newPromotedManufacturers.Select(c => new
            {
                id = c.Id,
                promotedId = c.PromotedId.Value,
                name = c.Name,
                vanityUrl = c.HubVanityURL,
                revitIcon = new
                {
                    originalUrl = c.RevitPluginPhoto?.OriginalImgUrl,
                    smallUrl = c.RevitPluginPhoto?.SmallImgUrl
                }
            });

            return response;
        }
    }
}
