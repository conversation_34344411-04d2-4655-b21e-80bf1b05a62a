﻿using Azure.Storage.Blobs;
using BIMsmithMarket.Domain.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IManufacturerBackupFileService
    {
        string GetManufacturerBackupFileName(int manufacturerId, string backupFileName);

        Task<BlobContainerClient> GetManufacturerBackupContainerAsync();

        Task SaveManufacturerBackupToFileAsync(ManufacturerBackupModel manufacturerBackupModel);

        string ConvertBackupFileNameToDateString(string backupFileName);

        Task<List<string>> GetManufacturerBackupFilesAsync(int manufacturerId);
    }
}