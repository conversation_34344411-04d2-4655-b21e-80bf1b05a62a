﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Configurations>Debug;Release;Dev;UAT</Configurations>
	</PropertyGroup>

	<PropertyGroup>
		<ServerGarbageCollection>true</ServerGarbageCollection>
		<ConcurrentGarbageCollection>true</ConcurrentGarbageCollection>
	</PropertyGroup>

	<ItemGroup>
		<Content Remove="bundleconfig.json" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Models\" />
		<Folder Include="Views\" />
		<Folder Include="wwwroot\bundles\" />
	</ItemGroup>

	<ItemGroup>
		<None Include="bundleconfig.json" />
		<None Include="wwwroot\Css\all.css" />
		<None Include="wwwroot\Css\font-awesome.css" />
		<None Include="wwwroot\Css\froala_editor.pkgd.min.css" />
		<None Include="wwwroot\Css\froala_style.min.css" />
		<None Include="wwwroot\Css\Site.css" />
		<None Include="wwwroot\fonts\fontawesome-webfont.eot" />
		<None Include="wwwroot\fonts\fontawesome-webfont.svg" />
		<None Include="wwwroot\fonts\fontawesome-webfont.ttf" />
		<None Include="wwwroot\fonts\fontawesome-webfont.woff" />
		<None Include="wwwroot\fonts\fontawesome-webfont.woff2" />
		<None Include="wwwroot\fonts\FontAwesome.otf" />
		<None Include="wwwroot\images\avatar_default.jpg" />
		<None Include="wwwroot\images\BIMsmith-blog-header-600x320.png" />
		<None Include="wwwroot\images\blog-bg.png" />
		<None Include="wwwroot\images\drop-arr.jpg" />
		<None Include="wwwroot\images\envelope.svg" />
		<None Include="wwwroot\images\footer-bg.png" />
		<None Include="wwwroot\images\img-main01.jpg" />
		<None Include="wwwroot\images\img-main02.jpg" />
		<None Include="wwwroot\images\img-main03.jpg" />
		<None Include="wwwroot\images\img-main04.jpg" />
		<None Include="wwwroot\images\img01.jpg" />
		<None Include="wwwroot\images\instagram_white.png" />
		<None Include="wwwroot\images\logo-footer.png" />
		<None Include="wwwroot\images\logo.png" />
		<None Include="wwwroot\images\select-arrow.png" />
		<None Include="wwwroot\images\soc-fb.png" />
		<None Include="wwwroot\images\soc-in.png" />
		<None Include="wwwroot\images\soc-p.png" />
		<None Include="wwwroot\images\soc-tw.png" />
		<None Include="wwwroot\images\soc-youtube.png" />
		<None Include="wwwroot\Scripts\bootstrap.js" />
		<None Include="wwwroot\Scripts\bootstrap.min.js" />
		<None Include="wwwroot\Scripts\jquery-1.10.2.intellisense.js" />
		<None Include="wwwroot\Scripts\jquery-1.10.2.js" />
		<None Include="wwwroot\Scripts\jquery-1.10.2.min.js" />
		<None Include="wwwroot\Scripts\jquery-1.10.2.min.map" />
		<None Include="wwwroot\Scripts\jquery-3.2.1.min.js" />
		<None Include="wwwroot\Scripts\jquery.validate-vsdoc.js" />
		<None Include="wwwroot\Scripts\jquery.validate.js" />
		<None Include="wwwroot\Scripts\jquery.validate.min.js" />
		<None Include="wwwroot\Scripts\main.js" />
		<None Include="wwwroot\Scripts\modernizr-2.6.2.js" />
		<None Include="wwwroot\Scripts\notifications.js" />
		<None Include="wwwroot\Scripts\respond.js" />
		<None Include="wwwroot\Scripts\respond.matchmedia.addListener.js" />
		<None Include="wwwroot\Scripts\respond.matchmedia.addListener.min.js" />
		<None Include="wwwroot\Scripts\respond.min.js" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="BuildBundlerMinifier" Version="3.2.449" />
		<PackageReference Include="MailChimp.Net.V3" Version="5.5.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\BIMsmithMarket.Core\BIMsmithMarket.Core.csproj" />
		<ProjectReference Include="..\BIMsmithMarket.DataLayer\BIMsmithMarket.DataLayer.csproj" />
		<ProjectReference Include="..\BIMsmithMarket.Domain\BIMsmithMarket.Domain.csproj" />
		<ProjectReference Include="..\BIMsmithMarket.Services\BIMsmithMarket.Services.csproj" />
	</ItemGroup>

</Project>
