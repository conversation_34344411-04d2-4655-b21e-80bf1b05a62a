﻿using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface ICustomCategoryIconService
    {
        Task<AdminListCustomCategoryIconDto> AddAsync(AddCustomCategoryIconDto model, string userId, IUnitOfWork unitOfWork);

        Task<AdminListCustomCategoryIconDto> EditAsync(EditCustomCategoryIconDto model, string userId, IUnitOfWork unitOfWork);

        Task<OperationResultDto> DeleteAsync(int id, IUnitOfWork unitOfWork);

        Task<PaginationListDto<AdminListCustomCategoryIconDto>> AdminListAsync(int categoryId, IUnitOfWork unitOfWork, int offset = 0, int count = 10);

        Task<PublicListCustomCategoryIconDto[]> PublicListAsync(int manufacturerId, IUnitOfWork unitOfWork);
    }
}