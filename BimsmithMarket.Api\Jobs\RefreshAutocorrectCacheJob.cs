﻿using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using Microsoft.EntityFrameworkCore;
using Quartz;
using Serilog;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Jobs
{
    [DisallowConcurrentExecution]
    public class RefreshAutocorrectCacheJob : IJob
    {
        private readonly string _jobName = "Refresh Autocorrect Cache Job";

        public async Task Execute(IJobExecutionContext context)
        {
            Log.Information($"[{_jobName}] started");

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                await unitOfWork.CurrentDbContext.Database.ExecuteSqlRawAsync("dbo.RebuildAutocorrectCache");
            }
        }
    }
}