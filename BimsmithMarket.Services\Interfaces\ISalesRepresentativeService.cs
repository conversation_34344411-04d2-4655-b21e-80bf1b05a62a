﻿using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.SalesRepresentative;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface ISalesRepresentativeService
    {
        Task<AdminGetSalesRepresentativeDto> AddAsync(AddSalesRepresentativeDto model, string userId, IUnitOfWork unitOfWork);

        Task<AdminGetSalesRepresentativeDto> EditAsync(EditSalesRepresentativeDto model, string userId, IUnitOfWork unitOfWork);

        Task<AdminGetSalesRepresentativeDto> AdminGetAsync(int id, IUnitOfWork unitOfWork);

        Task<PaginationListDto<AdminListSalesRepresentativeDto>> AdminListAsync(IUnitOfWork unitOfWork, int manufacturerId, string query = null, int offset = 0, int count = 10);

        Task<OperationResultDto> DeleteAsync(int id, IUnitOfWork unitOfWork);

        Task<PublicListSalesRepresentativeDto[]> PublicListAsync(int manufacturerId, IUnitOfWork unitOfWork);
    }
}