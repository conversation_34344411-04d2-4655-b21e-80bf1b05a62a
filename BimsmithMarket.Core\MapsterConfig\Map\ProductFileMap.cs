﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Models;
using Mapster;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public class ProductFileMap
    {
        public static void Register(TypeAdapterConfig config)
        {
            config.ForType<ProductFile, ProjectFileDto>()
                .Map(d => d.Id, s => s.FileId)
                .Map(d => d.Title, s => s.File.Title)
                .Map(d => d.FileName, s => s.File.FileName)
                .Map(d => d.Url, s => s.File.Url);

            config.ForType<ProductFile, ProductFileDto>()
                .Map(d => d.Id, s => s.FileId)
                .Map(d => d.Title, s => s.File.Title)
                .Map(d => d.FileName, s => s.File.FileName)
                .Map(d => d.Url, s => s.File.Url)
                .Map(d => d.Preview, s => s.File.PreviewUrl)
                .Map(d => d.MimeType, s => s.File.MediaType);

            config.ForType<ProductLine, BaseNameListDto>()
                .Map(d => d.Id, s => s.Id)
                .Map(d => d.Name, s => s.Name);

            config.ForType<Category, BaseNameListDto>()
                .Map(d => d.Id, s => s.Id)
                .Map(d => d.Name, s => s.Name);

            config.ForType<ProductFile, ProjectFileModel>()
                .Map(d => d.ProjectDataTypeTypeId, s => s.ProjectDataTypeId);

            config.ForType<ProjectFileModel, ProductFile>()
                .Map(d => d.ProjectDataTypeId, s => s.ProjectDataTypeTypeId)
                .Map(d => d.IsAttachment, s => s.ProjectDataTypeTypeId == null);
        }
    }
}
