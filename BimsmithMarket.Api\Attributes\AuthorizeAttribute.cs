﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Services;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.AspNetCore.Mvc.Filters;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;

namespace BIMsmithMarket.Api.Attributes
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
    public class AuthorizeAttribute : Attribute, IAuthorizationFilter
    {
        private readonly IAuthorizationService _authorizationService = new AuthorizationService(new CacheService());

        public string Roles { get; set; }

        public AuthorizeAttribute(string roles = null)
        {
            Roles = roles;
        }

        public void OnAuthorization(AuthorizationFilterContext context)
        {
            if (context.Filters.Any(item => item is IAllowAnonymousFilter))
                return;

            string token = AuthHelper.GetToken(context.HttpContext.Request);

            try
            {
                if (string.IsNullOrWhiteSpace(token))
                {
                    context.Result = CreateUnauthorizedResponse();
                    return;
                }

                if (_authorizationService.IsTokenInvalid(token))
                {
                    context.Result = CreateUnauthorizedResponse();
                    return;
                }

                JwtSecurityToken securityToken = AuthHelper.ParseSecurityToken(token);
                var userRoles = AuthHelper.GetUserClaimsFromToken(securityToken, ClaimTypes.Role);
                
                if (Roles != null)
                {
                    List<string> selectedRoles = Roles.Split(",").ToList();
                    if (selectedRoles != null && selectedRoles.Any() && !selectedRoles.Any(x => userRoles.Any(ur => ur.ToLower() == x.ToLower())))
                    {
                        context.Result = CreateUnauthorizedResponse();
                        return;
                    }
                }
            }
            catch (Exception ex)
            {
                context.Result = CreateUnauthorizedResponse($"Unauthorized Exception:{ex.GetAllMessages()}");
            }
        }

        #region private methods
        private JsonResult CreateUnauthorizedResponse(string message = "Unauthorized")
        {
            return new JsonResult(new { message = "Unauthorized" }) { StatusCode = StatusCodes.Status401Unauthorized };
        }
        #endregion
    }
}