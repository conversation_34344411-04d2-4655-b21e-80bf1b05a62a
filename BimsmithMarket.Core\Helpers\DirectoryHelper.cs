﻿using System.IO;

namespace BIMsmithMarket.Core.Helpers
{
    public class DirectoryHelper
    {
        public static void EmptyDirectory(string selectedDirectory)
        {
            DirectoryInfo di = new DirectoryInfo(selectedDirectory);

            foreach (FileInfo file in di.GetFiles())
            {
                file.Delete();
            }
            foreach (DirectoryInfo dir in di.GetDirectories())
            {
                dir.Delete(true);
            }
        }
    }
}