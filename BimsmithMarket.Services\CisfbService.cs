﻿using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class CisfbService : ICisfbService
    {
        public async Task<object> SearchAsync(IUnitOfWork unitOfWork, string q, int offset, int count)
        {
            var query = unitOfWork.CisfbRepository.GetAll();

            if (!string.IsNullOrEmpty(q))
            {
                query = query.Where(e => e.Code.StartsWith(q) || e.Title.Contains(q));
            }

            var countOfData = await query.CountAsync();

            var list = await query
                .OrderBy(a => a.Id)
                .Skip(offset)
                .Take(count)
                .Select(a => new
                {
                    id = a.Id,
                    code = a.Code,
                    title = a.Title,
                    parentId = a.ParentId
                })
                .AsNoTracking()
                .ToListAsync();

            return new
            {
                count = countOfData,
                data = list
            };
        }

        public async Task<object> GetAsync(IUnitOfWork unitOfWork, int id)
        {
            var item = await unitOfWork.CisfbRepository.GetByIdAsync(id);

            return new
            {
                id = item.Id,
                code = item.Code,
                title = item.Title,
                parentId = item.ParentId
            };
        }

        public async Task<object> LevelListAsync(IUnitOfWork unitOfWork, int? parentId, string q)
        {
            var query = unitOfWork.CisfbRepository.GetAll().Where(a => a.ParentId == parentId);

            if (!string.IsNullOrEmpty(q))
            {
                query = query.Where(e => e.Code.StartsWith(q) || e.Title.Contains(q));
            }

            return await query
                .OrderBy(a => a.Id)
                .Select(a => new
                {
                    id = a.Id,
                    code = a.Code,
                    title = a.Title,
                    parentId = a.ParentId
                })
                .AsNoTracking()
                .ToListAsync();
        }
    }
}
