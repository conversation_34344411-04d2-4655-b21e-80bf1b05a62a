﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using Mapster;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public class StarterMap
    {
        public static void Register(TypeAdapterConfig config)
        {
            config.ForType<Starter, StarterListDto>()
                .Map(d => d.DateModified, s => s.UtcDateModified ?? s.UtcDateCreated)
                .Map(d => d.ForgeProductLines, s => s.ProductLines);

            config.ForType<StarterProductLines, StarterProductLineDto>()
                .Map(d => d.MarketProductLineId, s => s.ProductLineId)
                .Map(d => d.Name, s => s.ProductLine.Name);
        }
    }
}