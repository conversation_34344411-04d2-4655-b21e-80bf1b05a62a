﻿using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using Microsoft.EntityFrameworkCore;
using Quartz;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Jobs
{
    [DisallowConcurrentExecution]
    public class DeleteUnusedBlobsJob : IJob
    {
        private readonly string _jobName = "Delete Unused Blobs Job";
        private readonly IAzureStorageService _azureStorageService;

        public DeleteUnusedBlobsJob(IAzureStorageService azureStorageService)
        {
            _azureStorageService = azureStorageService;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            Log.Information($"[{_jobName}] started");

            var allowedContainers = GetAllowedContainers();

            try
            {
                var blobsToDelete = new List<string>();
                using (IUnitOfWork unitOfWork = UnitOfWork.Create())
                {
                    var urls = await GetUrlsFromDBAsync(unitOfWork);

                    foreach (var containerName in allowedContainers)
                    {
                        var container = _azureStorageService.GetContainerByName(containerName);

                        var resultSegment = container.GetBlobs();

                        blobsToDelete.AddRange(resultSegment.Select(x => container.Uri.AbsoluteUri + x.Name).Except(urls));
                    }
                }
                System.IO.File.WriteAllText("BlobUrlsToDelete.txt", string.Join("\n", blobsToDelete));
            }
            catch (Exception e)
            {
                Log.Error($"[{_jobName}] Error: {e.GetAllMessages()}");
            }
        }

        #region private methods
        private async Task<ICollection<string>> GetUrlsFromDBAsync(IUnitOfWork unitOfWork)
        {
            return await unitOfWork.FileRepository.GetAll().Select(x => x.Url)
                .Union(unitOfWork.FileRepository.GetAll().Select(x => x.PreviewUrl))
                .Union(unitOfWork.BlogPostRepository.GetAll().Select(x => x.ImageUrlBig))
                .Union(unitOfWork.BlogPostRepository.GetAll().Select(x => x.ImageUrlSmall))
                .Union(unitOfWork.BlogPostRepository.GetAll().Select(x => x.AuthorImage))
                .Union(unitOfWork.CategoryRepository.GetAll().Select(x => x.IconUrl))
                .Union(unitOfWork.EventRepository.GetAll().Select(x => x.AuthorImage))
                .Union(unitOfWork.EventRepository.GetAll().Select(x => x.ImageUrlBig))
                .Union(unitOfWork.EventRepository.GetAll().Select(x => x.ImageUrlSmall))
                .Union(unitOfWork.HelpCategoryRepository.GetAll().Select(x => x.IconUrl))
                .Union(unitOfWork.NewsRepository.GetAll().Select(x => x.ImageUrlBig))
                .Union(unitOfWork.NewsRepository.GetAll().Select(x => x.ImageUrlSmall))
                .Union(unitOfWork.PhotoRepository.GetAll().Select(x => x.MiddleImgUrl))
                .Union(unitOfWork.PhotoRepository.GetAll().Select(x => x.OriginalImgUrl))
                .Union(unitOfWork.PhotoRepository.GetAll().Select(x => x.SmallImgUrl))
                .Union(unitOfWork.QualityItemRepository.GetAll().Select(x => x.IconUrl))
                .Union(unitOfWork.StarterRepository.GetAll().Select(x => x.Preview))
                .ToArrayAsync();
        }

        private string[] GetAllowedContainers()
        {
            var allowedContainers = new string[]
            {
                AzureStorageConstants.ArchivesContainer,
                AzureStorageConstants.AttachmentsContainer,
                AzureStorageConstants.FilesContainer,
                AzureStorageConstants.IconsContainer,
                AzureStorageConstants.LockedArchiveContainer,
                AzureStorageConstants.LockedProductFilesContainer,
                AzureStorageConstants.NewsContentContainer,
                AzureStorageConstants.PhotosContainer,
                AzureStorageConstants.TempFilesContainer
            };

            bool isDevEnvironment = bool.Parse(ConfigurationHelper.GetValue("Environment"));
            if (isDevEnvironment)
            {
                string environment = ConfigurationHelper.GetValue("Environment");
                allowedContainers = allowedContainers.Select(x => x + $"-{environment}").ToArray();
            }

            return allowedContainers;
        }
        #endregion
    }
}