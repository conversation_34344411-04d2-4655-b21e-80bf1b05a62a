﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Domain.DBModels.PaymentDbModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.PaymentsDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.PaymentServices
{
    public class UserPaidProductService : IUserPaidProductService
    {
        public async Task<UserPaidProductDto> AddEditProduct(UserPaidProductDto model, IUnitOfWork unitOfWork)
        {
            var product = unitOfWork.ProductRepository.GetById(model.PaidProductId);
            if (product == null)
                throw new InvalidInputException($"There is no product with id {model.PaidProductId}");

            var user = unitOfWork.UserRepository.GetAll().FirstOrDefault(x => x.Id == model.UserId);
            if (user == null)
                throw new InvalidInputException($"There is no user with id {model.UserId}");

            var currentUserPaidProduct = unitOfWork.UserPaidProductRepository.GetAll()
                                                   .FirstOrDefault(x => x.PaidProductId == model.PaidProductId &&
                                                                       x.UserId == model.UserId);
            if (currentUserPaidProduct != null && (currentUserPaidProduct.Paid || currentUserPaidProduct.StripeSessionMark > DateTime.UtcNow.AddMinutes(-15)))
            {
                currentUserPaidProduct.Adapt(model);
                unitOfWork.UserPaidProductRepository.Edit(currentUserPaidProduct);
            }
            else if (currentUserPaidProduct != null)
            {
                unitOfWork.UserPaidProductRepository.Delete(currentUserPaidProduct);
                currentUserPaidProduct = model.Adapt<UserPaidProduct>();
                unitOfWork.UserPaidProductRepository.Insert(currentUserPaidProduct);
            }
            else
            {
                currentUserPaidProduct = model.Adapt<UserPaidProduct>();
                unitOfWork.UserPaidProductRepository.Insert(currentUserPaidProduct);
            }
            await unitOfWork.SaveAsync();

            return currentUserPaidProduct.Adapt<UserPaidProductDto>();

        }

        public async Task ChangeStatus(string sessionId, string stripeSessionStatus, IUnitOfWork unitOfWork)
        {
            var userProduct = unitOfWork.UserPaidProductRepository.GetAll().FirstOrDefault(x => x.StripeSessionId == sessionId);
            if (userProduct == null)
                throw new DbItemNotFoundException($"There is no userProduct with session {stripeSessionStatus}");
            userProduct.StripeSessionStatus = stripeSessionStatus;
            userProduct.StripeSessionMark = DateTime.UtcNow;
            unitOfWork.UserPaidProductRepository.Edit(userProduct);
            await unitOfWork.SaveAsync();
        }

        public async Task DeleteProduct(int paydProductId, IUnitOfWork unitOfWork)
        {
            var paydProduct = unitOfWork.UserPaidProductRepository.GetById(paydProductId);
            if (paydProduct == null)
                throw new InvalidInputException($"paydProduct not found {paydProductId}");

            unitOfWork.UserPaidProductRepository.Delete(paydProduct);
            await unitOfWork.SaveAsync();
        }

        public async Task<List<UserPaidProductDto>> GetUserProductsAsync(
            string userId,
            IUnitOfWork unitOfWork,
            bool all = false)
        {
            var dbItems = await unitOfWork.UserPaidProductRepository.GetAll()
                                           .Where(x => x.UserId == userId).ToListAsync();
            if (!all)
                dbItems = dbItems.Where(x => x.Paid).ToList();

            var result = dbItems.Adapt<List<UserPaidProductDto>>();
            return result;
        }

        public async Task<bool> CheckUserProductAsync(
            string userId,
            int productId,
            IUnitOfWork unitOfWork)
        {
            return await unitOfWork.UserPaidProductRepository.GetAll()
                .AnyAsync(x => x.UserId == userId
                            && x.PaidProductId == productId
                            && x.StripeSessionStatus == "paid");
        }

        public async Task<CheckUserProductResponseDto[]> BatchCheckUserProductAsync(
            CheckUserProductRequestDto model,
            IUnitOfWork unitOfWork)
        {
            CheckUserProductResponseDto[] items = await unitOfWork.UserPaidProductRepository.GetAll()
                .Where(x => model.ProductIds.Contains(x.PaidProductId)
                         && x.UserId == model.UserId)
                .Select(x => new CheckUserProductResponseDto
                {
                    ProductId = x.PaidProductId,
                    UserId = x.UserId,
                    Paid = x.StripeSessionStatus == "paid"
                })
                .ToArrayAsync();

            return model.ProductIds.Select(x => new CheckUserProductResponseDto
            {
                ProductId = x,
                UserId = items.FirstOrDefault(i => i.ProductId == x)?.UserId,
                Paid = items.FirstOrDefault(i => i.ProductId == x)?.Paid ?? false
            }).ToArray();
        }

        public async Task<IEnumerable<CheckUserProductsResultDto>> CheckUserProductsAsync(
            EntityIdsDto model,
            string userId,
            IUnitOfWork unitOfWork)
        {
            Dictionary<int, bool> dbUserProducts = (await unitOfWork.UserPaidProductRepository.GetAllAsNoTracking()
                .Where(x => x.UserId == userId
                         && model.Ids.Contains(x.PaidProductId))
                                .ToArrayAsync())
                .ToDictionary(x => x.PaidProductId, x => x.Paid);

            return model.Ids.Select(x => new CheckUserProductsResultDto
            {
                ProductId = x,
                Paid = dbUserProducts.ContainsKey(x) ? dbUserProducts[x] : false
            }).ToList();
        }

        public async Task DeleteAsync(
            int Id,
            IUnitOfWork unitOfWork)
        {
            var paidProduct = unitOfWork.UserPaidProductRepository.GetById(Id);
            if (paidProduct == null)
                throw new InvalidInputException("There is no user paid product");
            unitOfWork.UserPaidProductRepository.Delete(paidProduct);
            await unitOfWork.SaveAsync();
        }

    }
}
