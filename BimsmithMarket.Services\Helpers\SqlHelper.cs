﻿using BIMsmithMarket.Domain.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace BIMsmithMarket.Services.Helpers
{
    public static class SqlHelper
    {
        public static SqlQueryModel GenerateInsertQuery<T>(T model, string tableName)
        {
            var parameters = new List<object>();
            var query = new StringBuilder("INSERT INTO ");
            query.Append(tableName);
            query.Append(" (");
            var modelType = model.GetType();
            var properties = modelType.GetProperties().Where(x => x.PropertyType.IsValueType || x.PropertyType == typeof(string)).ToList();
            var propertyNames = properties.Select(x => x.Name).ToArray();
            query.Append(string.Join(",", propertyNames));
            query.Append(") VALUES(");
            var firstProperty = true;
            var parameterIndex = 0;
            foreach (var property in properties)
            {
                if (!firstProperty)
                {
                    query.Append(",");
                }
                firstProperty = false;

                var value = property.GetValue(model);
                if (property.PropertyType.IsEnum)
                {
                    value = Convert.ChangeType(value, Enum.GetUnderlyingType(value.GetType()));
                }

                string sqlParamaterName = $"{{{parameterIndex++}}}";
                query.Append(sqlParamaterName);
                
                parameters.Add(value);
            }
            query.Append(")");

            return new SqlQueryModel
            {
                Query = query.ToString(),
                Parameters = parameters
            };
        }

        public static string GenerateSetIdentityOnQuery(string tableName)
        {
            return "SET IDENTITY_INSERT " + tableName + " ON";
        }

        public static string GenerateSetIdentityOffQuery(string tableName)
        {
            return "SET IDENTITY_INSERT " + tableName + " OFF";
        }
    }
}