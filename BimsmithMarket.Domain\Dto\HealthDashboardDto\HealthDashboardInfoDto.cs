﻿using BIMsmithMarket.Domain.Enums;

namespace BIMsmithMarket.Domain.Dto.HealthDashboardDto
{
    public class HealthDashboardInfoDto
    {
        public int RecordsCount { get; set; }

        public HealthDashboardAccessType Type { get; set; }

        public HealthDashboardType DashboardMissingType { get; set; }

        public HealthDashboardFilterPeriod FilterPeriod { get; set; }

        public string DashboardName { get; set; }

        public int EntityId { get; set; }
    }
}