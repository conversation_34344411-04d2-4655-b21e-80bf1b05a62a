﻿using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("api/[controller]/[action]")]
    public class CisfbController : BaseApiController
    {
        private readonly ICisfbService _cisfbService;

        public CisfbController(ICisfbService cisfbService)
        {
            _cisfbService = cisfbService;
        }

        /// <summary>
        /// Search Cisfb by code or title
        /// </summary>
        /// <param name="q"></param>
        /// <param name="offset">Offset</param>
        /// <param name="count">Count in search result</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("Search")]
        public async Task<IActionResult> Search(string q = null, int offset = 0, int count = 20)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _cisfbService.SearchAsync(unitOfWork, q, offset, count));
            }
        }

        /// <summary>
        /// Get detail information about Cisfb
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("Get")]
        public async Task<IActionResult> Get(int id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _cisfbService.GetAsync(unitOfWork, id));
            }
        }

        /// <summary>
        /// Get list of cisfbs for one level
        /// </summary>
        /// <param name="parentId"></param>
        /// <param name="q"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("LevelList")]
        public async Task<IActionResult> LevelList(int? parentId = null, string q = null)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _cisfbService.LevelListAsync(unitOfWork, parentId, q));
            }
        }
    }
}