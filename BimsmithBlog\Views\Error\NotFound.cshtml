﻿@using System.Configuration;
@using BIMsmithMarket.Core.Helpers

<header class="header-wrap">
    <div class="header">
        <a href="#" class="menu"><span></span><span></span><span></span>menu</a>
        <div class="top clear">
            <a class="logo" href="/"><img src="~/images/logo.png" alt=""></a>
            <div class="sign-in">
                <div style="display: none;" id="user-name"></div>
                <div style="display: none;" id="head-drop" class="head-drop">
                    <div class="av">
                        <img id="profile-image" alt="" src="/images/img01.jpg" height="383" width="383">
                        <div id="settings-name" class="name"></div>
                    </div>
                    <div class="links">
                        <a href="ConfigurationHelper.GetValue("MarketUrl")">Market</a>
                        <a href="@ConfigurationHelper.GetValue("ForgeUrl")/">forge</a>
                        <a href="@ConfigurationHelper.GetValue("BimsmithUrl")/NewMyBIMSmith/settings">MyBIMsmith</a>
                    </div>
                </div>
                <a id="logout-button" style="display: none;" class="def-btn logout-button" href="javascript:void(0)">Log out</a>
                <a id="login-button" style="display: inline-block;" class="def-btn" href="@ConfigurationHelper.GetValue("BimsmithUrl")/Account/Login/">Log in</a>
                <a id="signup-button" style="display: inline-block;" class="g-btn" href="@ConfigurationHelper.GetValue("BimsmithUrl")/Account/Register">Sign up</a>
            </div>
        </div>

        <div class="bot clear">
           
            <div class="search">
                <i class="fa fa-search"></i>
                <input type="search">
            </div>
        </div>
    </div>
</header>
<div class="main">
    <div class="block" style="height:600px">
        <h3 class="center">404 - Page Not Found</h3>
    </div>
</div>

