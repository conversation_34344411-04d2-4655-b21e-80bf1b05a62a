﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Models;
using Mapster;
using System.Linq;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public static class ManufacturerBackupMap
    {
        public static void Register(TypeAdapterConfig config)
        {
            config.ForType<Product, ProductBackupModel>()
                .Map(d => d.CategoryIds, s => s.ProductCategories.Select(a => a.CategoryId).Distinct())
                .Map(d => d.OmniclassIds, s => s.ProductOmniclasses.Select(a => a.OmniclassId).Distinct())
                .Map(d => d.UniclassIds, s => s.ProductUniclasses.Select(a => a.UniclassId).Distinct())
                .Map(d => d.UniformatIds, s => s.ProductUniformats.Select(a => a.UniformatId).Distinct())
                .Map(d => d.ExternalMasterformatIds, s => s.ProductMasterformats.Select(a => a.ExternalMasterformatId).Distinct())
                .Map(d => d.CisfbIds, s => s.ProductCisfbs.Select(a => a.CisfbId).Distinct())
                .Map(d => d.Photos, s => s.ProductPhotos)
                .Map(d => d.Files, s => s.ProductFiles.Where(a => a.IsAttachment))
                .Map(d => d.ProjectFiles, s => s.ProductFiles.Where(a => !a.IsAttachment))
                .Map(d => d.QualityItemIds, s => s.ProductQualityItems.Select(a => a.QualityItemId).Distinct())
                .Map(d => d.ExternalCertificateIds, s => s.ProductCertificates.Where(a => a.ExternalCertificateId != null).Select(a => a.ExternalCertificateId.Value).Distinct())
                .Map(d => d.SampleIds, s => s.ProductSamples.Select(a => a.SampleId).Distinct())
                .Map(d => d.RelatedProductIds, s => s.RelatedProducts.Select(a => a.RelatedProductId).Distinct())
                .Map(d => d.DetailIds, s => s.ProductDetails.Select(a => a.DetailId));

            config.ForType<ProductLine, ProductLineBackupModel>()
                .Map(d => d.ManufacturerId, s => s.ManufacturerId.Value)
                .Map(d => d.StarterIds, s => s.Starters.Select(a => a.StarterId))
                .Map(d => d.ForgeProductLineIds, s => s.ForgeProductLineIds.Select(a => a.ForgeProductLineId))
                .Map(d => d.Files, s => s.ProductLineFiles.Where(a => a.IsAttachment))
                .Map(d => d.ProjectFiles, s => s.ProductLineFiles.Where(a => !a.IsAttachment))
                .Map(d => d.ExternalCertificateIds, s => s.ProductLineCertificates.Select(a => a.ExternalCertificateId.Value))
                .Map(d => d.QualityItemIds, s => s.ProductLineQualityItems.Select(a => a.QualityItemId));

            config.ForType<ManufacturerFile, FileDto>()
                .Map(d => d.Title, s => s.File.Title);

            config.ForType<ProductFile, FileDto>()
               .Map(d => d.Title, s => s.File.Title);

            config.ForType<ProductLineFile, FileDto>()
               .Map(d => d.Title, s => s.File.Title);

            config.ForType<Detail, DetailBackupModel>()
                .Map(d => d.ManufacturerId, s => s.ManufacturerId.Value)
                .Map(d => d.ExternalMasteformatIds, s => s.DetailMasterformats.Select(a => a.ExternalMasterformatId))
                .Map(d => d.Photos, s => s.DetailPhotos)
                .Map(d => d.ProjectFiles, s => s.DetailFiles)
                .Map(d => d.ApplicationIds, s => s.DetailDetailApplications.Select(a => a.DetailApplicationId))
                .Map(d => d.ProductIds, s => s.ProductDetails.Select(a => a.ProductId))
                .Map(d => d.RelatedDetailIds, s => s.RelatedDetails.Select(a => a.DetailId == s.Id ? a.RelatedDetailId.Value : a.DetailId));
        }
    }
}