﻿using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IStaticExcelImportService
    {
        Task<int> UploadFileAsync(IFormFile formFile, int manufacturerId, string userId, IUnitOfWork unitOfWork);

        Task ProcessFileQueueAsync(IUnitOfWork unitOfWork);

        Task ProcessProductQueueAsync(IUnitOfWork unitOfWork);

        Task CancelImportAsync(int id, string userId, IUnitOfWork unitOfWork);

        Task<PaginationListDto<StaticExcelFileListDto>> ListAsync(
            string userId,
            IUnitOfWork unitOfWork,
            int? manufacturerId = null,
            ExcelProcessingStatus? status = null,
            int count = 50,
            int offset = 0);
    }
}