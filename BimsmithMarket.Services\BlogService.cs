﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Helpers;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Flurl;
using Newtonsoft.Json;

namespace BIMsmithMarket.Services
{
    public class BlogService : IBlogService
    {
        private IHttpClientFactory _httpClientFactory;
        private static readonly char[] Separator = new char[] { '_' };

        public BlogService(IHttpClientFactory httpClientFactory)
        {
            _httpClientFactory = httpClientFactory;
        }

        public async Task<object> PublicListAsync(IUnitOfWork unitOfWork, int categoryId, string q, int offset, int count, bool isFeaturedFirst, TargetBlogType targetType, string author, string vanityCategoryid, string ip)
        {
            var blogCategoriesQuerry = unitOfWork.BlogCategoryRepository.GetAll();
            if (targetType != TargetBlogType.All)
            {
                blogCategoriesQuerry = blogCategoriesQuerry.Where(x => x.TargetTypes.Any(a => a.TargetBlogType == targetType));
            }

            var blogCategories = await blogCategoriesQuerry.Select(a => new
            {
                id = a.Id,
                name = a.Name,
                vanityId = a.VanityId,
                targetTypes = a.TargetTypes.Select(x => x.TargetBlogType)
            })
            .AsNoTracking()
            .ToListAsync();

            var query = unitOfWork.BlogPostRepository.GetAll();

            if (categoryId != -1)
            {
                query = unitOfWork.BlogPostRepository.GetAll().Where(a => a.BlogCategory.Id == categoryId);
            }

            if (string.IsNullOrWhiteSpace(q) == false)
            {
                query = query.Where(a => a.Title.Contains(q) || a.Descriptions.Contains(q));
            }

            if (string.IsNullOrWhiteSpace(author) == false)
            {
                author = author.Trim();
                query = query.Where(a => a.AuthorTitle.ToLower() == author.ToLower());
            }

            if (isFeaturedFirst)
            {
                query = query.Where(a => a.PublishOption == PublishOption.Published)
                            .OrderByDescending(a => a.IsFeatured)
                            .ThenByDescending(a => a.PublishedDate);
            }
            else
            {
                query = query.Where(a => a.PublishOption == PublishOption.Published)
                            .OrderByDescending(a => a.PublishedDate);
            }

            if (!string.IsNullOrEmpty(vanityCategoryid))
            {
                vanityCategoryid = vanityCategoryid.Trim();
                query = query.Where(a => a.BlogCategory.VanityId == vanityCategoryid);
            }

            if (targetType != TargetBlogType.All)
            {
                query = query.Where(a => a.TargetTypes.Any(x => x.TargetBlogType == targetType));
            }
            
            using HttpClient httpClient = new();
            Url getLocationUrl = new Url(ConfigurationHelper.GetValue("AnalyticsWebApiBaseAddress"))
                .AppendPathSegment("api/Location/GetLocation")
                .SetQueryParam("ipAddress", ip);
            using HttpResponseMessage locationResponse = await httpClient.GetAsync(getLocationUrl);

            Url urlContinentsList = new Url(ConfigurationHelper.GetValue("CBOBaseAddress"))
                .AppendPathSegment("api/Continent/List")
                .SetQueryParam("isActive", true);
            using HttpResponseMessage continentsResponse = await httpClient.GetAsync(urlContinentsList);

            string locationContent = await locationResponse.Content.ReadAsStringAsync();
            var analyticsLocationModel = JsonConvert.DeserializeObject<AnalyticsLocationModel>(locationContent);
                
            var contentContinents = await continentsResponse.Content.ReadAsStringAsync();
            var continents = JsonConvert.DeserializeObject<List<LocationModel.Root>>(contentContinents);

            var allCountries = continents.SelectMany(s => s.countries).Distinct().ToList();
            var currentCountry = allCountries.FirstOrDefault(w => w.code == analyticsLocationModel.CountryCode);
            var currentCountryId = currentCountry?.id.ToString();

            var allBlogPosts = await query
                .Select(a => new
                {
                    id = a.Id,
                    vanityId = a.VanityId,
                    category = new
                    {
                        id = a.BlogCategory.Id,
                        name = a.BlogCategory.Name,
                        vanityId = a.BlogCategory.VanityId
                    },
                    title = a.Title,
                    descriptions = a.Descriptions,
                    publishedDate = a.PublishedDate.HasValue ? a.PublishedDate.Value : DateTime.MinValue,
                    authorTitle = a.AuthorTitle,
                    authorImage = a.AuthorImage,
                    imageUrlBig = a.ImageUrlBig,
                    imageUrlSmall = a.ImageUrlSmall,
                    publishOption = a.PublishOption,
                    blogTags = a.Tags,
                    featuredHomepage = a.TargetTypes.Any(tt => tt.TargetBlogType == TargetBlogType.FeaturedHomepage),
                    RegionIds = a.RegionIds,
                    StateIds = a.StateIds
                })
                .AsNoTracking()
                .ToListAsync();

            var filteredBlogPosts = allBlogPosts
                .Where(w => string.IsNullOrEmpty(w.RegionIds) || (!string.IsNullOrEmpty(w.RegionIds) && w.RegionIds.Split(Separator).Contains(currentCountryId)))
                .ToList();

            var countOfData = filteredBlogPosts.Count;

            var blogPosts = filteredBlogPosts
                .Skip(offset)
                .Take(count)
                .ToList();

            return new
            {
                countOfPosts = countOfData,
                posts = blogPosts,
                categories = blogCategories
            };
        }

        public async Task UpdateAuthorPhotoAsync(IUnitOfWork unitOfWork, UpdateAuthorPhotoModel model)
        {
            var posts = await unitOfWork.BlogPostRepository.GetAll()
                       .Where(x => x.AuthorEmail == model.Email && x.BlogCategory.TargetTypes
                           .Any(w => w.TargetBlogType == model.BlogType))
                       .ToListAsync();

            foreach (var post in posts)
            {
                post.AuthorImage = model.PhotoUrl;
                unitOfWork.BlogPostRepository.Edit(post);
            }

            await unitOfWork.SaveAsync();
        }

        public async Task<object> ListAsync(IUnitOfWork unitOfWork, string q, int offset, int count)
        {
            var query = unitOfWork.BlogPostRepository.GetAll();

            if (!string.IsNullOrEmpty(q))
            {
                query = query.Where(e =>
                           e.Title.Contains(q));
            }

            var coundOfData = await query.CountAsync();

            var list = await query
                .OrderByDescending(a => a.CreatedDate < a.ModifiedDate ? a.ModifiedDate : a.CreatedDate)
                .Skip(offset)
                .Take(count)
                .Select(a => new
                {
                    id = a.Id,
                    vanityId = a.VanityId,
                    title = a.Title,
                    category = a.BlogCategory.Name,
                    imageUrlSmall = a.ImageUrlSmall,
                    publishOption = a.PublishOption,
                    createdDate = a.CreatedDate,
                    createdBy = new
                    {
                        id = a.CreatedById,
                        firstName = a.CreatedBy.FirstName,
                        lastName = a.CreatedBy.LastName
                    },
                    RegionIds = a.RegionIds,
                    StateIds = a.StateIds
                })
                .AsNoTracking()
                .ToListAsync();

            return new
            {
                count = coundOfData,
                data = list,
            };
        }

        public async Task<object> GetAsync(IUnitOfWork unitOfWork, int id, string vanityId)
        {
            var query = unitOfWork.BlogPostRepository.GetAll();

            if (id != -1)
            {
                query = query.Where(c => c.Id == id);
            }
            else
            {
                query = query.Where(c => c.VanityId == vanityId);
            }

            var item = await query
                .Select(a => new
                {
                    id = a.Id,
                    vanityId = a.VanityId,
                    blogCategoryId = a.BlogCategoryId,
                    title = a.Title,
                    imageUrlSmall = a.ImageUrlSmall,
                    imageUrlBig = a.ImageUrlBig,
                    htmlBody = a.HtmlBody,
                    publishOption = a.PublishOption,
                    authorTitle = a.AuthorTitle,
                    authorEmail = a.AuthorEmail,
                    authorImageUrl = a.AuthorImage,
                    tags = a.Tags,
                    targetTypes = a.TargetTypes.Select(tt => new
                    {
                        targetType = tt.TargetBlogType
                    }),
                    metaDescription = a.MetaDescription,
                    metaKeywords = a.MetaKeywords,
                    metaTitle = a.MetaTitle,
                    viewCount = a.ViewCount,
                    publishedDate = a.PublishedDate,
                    createdDate = a.CreatedDate,
                    modifiedDate = a.ModifiedDate,
                    createdBy = new
                    {
                        id = a.CreatedById,
                        firstName = a.CreatedBy.FirstName,
                        lastName = a.CreatedBy.LastName
                    },
                    modifiedBy = a.ModifiedById == null ? null : new
                    {
                        id = a.ModifiedById,
                        firstName = a.ModifiedBy.FirstName,
                        lastName = a.ModifiedBy.LastName
                    },
                    mentionedEntries = a.BlogMentionedEntries.Select(m => new
                    {
                        name = m.Name,
                        entryId = m.EntryId,
                        entryType = m.EntryType,
                        entryUrl = m.EntryUrl
                    }),
                    schedulePublishDate = a.SchedulePublishDate
                })
                .FirstOrDefaultAsync();

            if (item == null)
            {
                throw new DbItemNotFoundException("Not found the Blog post");
            }

            return item;
        }

        public async Task<object> AddAsync(IUnitOfWork unitOfWork, string userId, AddBlogPostModel model)
        {
            if (await unitOfWork.BlogPostRepository.GetAll().AnyAsync(a => a.VanityId == model.VanityId))
                throw new InvalidInputException("This VanityId is occupied");

            // Check if Blog has same type with blogCategory
            var blogCategory = await unitOfWork.BlogCategoryRepository.GetByIdAsync(model.BlogCategoryId.Value);
            if (blogCategory == null)
            {
                throw new InvalidInputException("Can't find BlogCategory");
            }
            var blogCategoryTargetTypes = blogCategory.TargetTypes.Select(x => x.TargetBlogType.ToString()).ToList();
            var blogPostTargetTypes = model.TargetTypes.Select(x => x.ToString()).ToList();
            var difference = blogCategoryTargetTypes.Where(x => blogPostTargetTypes.Contains(x)).ToList();
            if (difference.Count() < blogPostTargetTypes.Count())
            {
                throw new InvalidInputException("Blog category don't contain blogBost Target type.");
            }

            BlogPost blogPost = new BlogPost();
            blogPost.VanityId = model.VanityId;
            blogPost.BlogCategoryId = model.BlogCategoryId.Value;
            blogPost.Title = model.Title.Trim();
            blogPost.HtmlBody = model.HtmlBody;
            blogPost.ImageUrlBig = model.ImageUrlBig;
            blogPost.ImageUrlSmall = model.ImageUrlSmall;
            blogPost.Tags = model.Tags;
            blogPost.MetaDescription = model.MetaDescription;
            blogPost.MetaKeywords = model.MetaKeywords;
            blogPost.MetaTitle = model.MetaTitle;
            blogPost.PublishOption = model.PublishOption;
            blogPost.AuthorTitle = model.AuthorTitle;
            blogPost.AuthorEmail = model.AuthorEmail;
            blogPost.PublishedDate = model.PublishedDate;
            blogPost.CreatedById = userId;
            blogPost.CreatedDate = DateTime.UtcNow;
            blogPost.SchedulePublishDate = model.SchedulePublishDate;

            if (blogPost.PublishOption == PublishOption.Published && blogPost.PublishedDate == null)
            {
                blogPost.PublishedDate = DateTime.UtcNow;
            }

            if (!string.IsNullOrEmpty(model.AuthorImageUrl))
            {
                blogPost.AuthorImage = model.AuthorImageUrl;
            }

            var plainText = HtmlToText.TryConvertHtml(model.HtmlBody).Trim();
            blogPost.Descriptions = plainText.Length > 230 ? plainText.Substring(0, 230) + "..." : plainText;

            unitOfWork.BlogPostRepository.Insert(blogPost);

            await unitOfWork.SaveAsync();

            foreach (var targetType in model.TargetTypes)
            {
                var blogTargetType = new BlogTargetType
                {
                    TargetBlogType = targetType,
                    BlogPostId = blogPost.Id
                };
                unitOfWork.BlogTargetTypeRepository.Insert(blogTargetType);
            }
            await unitOfWork.SaveAsync();

            if (model.MentionedEntries != null)
            {
                foreach (var item in model.MentionedEntries)
                {
                    BlogMentionedEntry entry = new BlogMentionedEntry();
                    entry.BlogPostId = blogPost.Id;
                    entry.Name = item.Name;
                    entry.EntryId = item.EntryId;
                    entry.EntryType = item.EntryType;
                    entry.EntryUrl = item.EntryUrl;
                    entry.CreatedDate = DateTime.UtcNow;
                    unitOfWork.BlogMentionedEntryRepository.Insert(entry);
                }
            }

            await unitOfWork.SaveAsync();

            return new
            {
                id = blogPost.Id
            };
        }

        public async Task<object> EditAsync(IUnitOfWork unitOfWork, string userId, EditBlogPostModel model)
        {
            if (await unitOfWork.BlogPostRepository.GetAll().AnyAsync(a => a.Id != model.Id && a.VanityId == model.VanityId))
            {
                throw new InvalidInputException("This VanityId is occupied");
            }

            BlogPost blogPost = await unitOfWork.BlogPostRepository.GetByIdAsync(model.Id);

            if (blogPost == null)
            {
                throw new DbItemNotFoundException("The blog post not found");
            }

            blogPost.VanityId = model.VanityId;
            blogPost.BlogCategoryId = model.BlogCategoryId.Value;
            blogPost.Title = model.Title.Trim();
            blogPost.HtmlBody = model.HtmlBody;
            blogPost.ImageUrlBig = model.ImageUrlBig;
            blogPost.ImageUrlSmall = model.ImageUrlSmall;
            blogPost.Tags = model.Tags;
            blogPost.MetaDescription = model.MetaDescription;
            blogPost.MetaKeywords = model.MetaKeywords;
            blogPost.MetaTitle = model.MetaTitle;
            blogPost.PublishOption = model.PublishOption;
            blogPost.AuthorTitle = model.AuthorTitle;
            blogPost.AuthorEmail = model.AuthorEmail;
            blogPost.PublishedDate = model.PublishedDate;
            blogPost.ModifiedById = userId;
            blogPost.ModifiedDate = DateTime.UtcNow;
            blogPost.SchedulePublishDate = model.SchedulePublishDate;

            if (!string.IsNullOrEmpty(model.AuthorImageUrl))
            {
                blogPost.AuthorImage = model.AuthorImageUrl;
            }

            if (blogPost.PublishOption == PublishOption.Published && blogPost.PublishedDate == null)
            {
                blogPost.PublishedDate = DateTime.UtcNow;
            }

            var plainText = HtmlToText.TryConvertHtml(model.HtmlBody).Trim();
            blogPost.Descriptions = plainText.Length > 230 ? plainText.Substring(0, 230) + "..." : plainText;

            unitOfWork.BlogPostRepository.Edit(blogPost);

            //Delete old Mentioned Entries
            var oldMentionedEntries = blogPost.BlogMentionedEntries.ToList();
            unitOfWork.BlogMentionedEntryRepository.Delete(oldMentionedEntries);

            //Create new Mentioned Entries
            if (model.MentionedEntries != null)
            {
                foreach (var item in model.MentionedEntries)
                {
                    BlogMentionedEntry entry = new BlogMentionedEntry();
                    entry.BlogPostId = blogPost.Id;
                    entry.Name = item.Name;
                    entry.EntryId = item.EntryId;
                    entry.EntryType = item.EntryType;
                    entry.EntryUrl = item.EntryUrl;
                    entry.CreatedDate = DateTime.UtcNow;
                    unitOfWork.BlogMentionedEntryRepository.Insert(entry);
                }
            }

            await unitOfWork.SaveAsync();

            //delete old TargetTypes
            var existsTarget = blogPost.TargetTypes.ToList();
            unitOfWork.BlogTargetTypeRepository.Delete(existsTarget);

            foreach (var targetType in model.TargetTypes)
            {
                var blogTargetType = new BlogTargetType
                {
                    TargetBlogType = targetType,
                    BlogPostId = blogPost.Id
                };
                unitOfWork.BlogTargetTypeRepository.Insert(blogTargetType);
            }
            await unitOfWork.SaveAsync();

            return new
            {
                id = blogPost.Id
            };
        }

        public async Task DeleteAsync(IUnitOfWork unitOfWork, string userId, int id)
        {
            BlogPost blogPost = await unitOfWork.BlogPostRepository.GetByIdAsync(id);

            if (blogPost == null)
            {
                throw new DbItemNotFoundException("The blog post not found");
            }

            //Delete BlogTargetTypes
            var oldBlogTargetType = blogPost.TargetTypes.ToList();
            unitOfWork.BlogTargetTypeRepository.Delete(oldBlogTargetType);

            //Delete  Mentioned Entries
            var oldMentionedEntries = blogPost.BlogMentionedEntries.ToList();
            unitOfWork.BlogMentionedEntryRepository.Delete(oldMentionedEntries);

            //Delete blog entry
            unitOfWork.BlogPostRepository.Delete(blogPost);

            await unitOfWork.SaveAsync();
        }

        public async Task<object> CategoriesAsync(IUnitOfWork unitOfWork)
        {
            var items = await unitOfWork.BlogCategoryRepository.GetAll()
                    .OrderBy(a => a.Name)
                    .Select(a => new
                    {
                        id = a.Id,
                        vanityId = a.VanityId,
                        name = a.Name,
                        status = a.Status,
                        createdDate = a.CreatedDate,
                        targetTypes = a.TargetTypes.Select(x => new
                        {
                            targetType = x.TargetBlogType.ToString(),
                            targetTypeint = x.TargetBlogType
                        }),
                        createdBy = new
                        {
                            id = a.CreatedById,
                            firstName = a.CreatedBy.FirstName,
                            lastName = a.CreatedBy.LastName
                        }
                    })
                    .AsNoTracking()
                    .ToListAsync();

            return items;
        }

        public async Task<object> AddCategoryAsync(IUnitOfWork unitOfWork, string userId, AddBlogCategoryModel model)
        {
            if (await unitOfWork.BlogCategoryRepository.GetAll().AnyAsync(a => a.VanityId == model.VanityId))
            {
                throw new InvalidInputException("This VanityId is occupied");
            }

            BlogCategory blogCategory = new BlogCategory();
            blogCategory.VanityId = model.VanityId;
            blogCategory.Name = model.Name.Trim();
            blogCategory.CreatedById = userId;
            blogCategory.CreatedDate = DateTime.UtcNow;


            unitOfWork.BlogCategoryRepository.Insert(blogCategory);

            await unitOfWork.SaveAsync();


            // Add terget types
            //------------------
            foreach (var targetType in model.TargetTypes)
            {
                var blogCategoryTargetType = new BlogCategoryTargetType
                {
                    TargetBlogType = targetType,
                    BlogCategoryId = blogCategory.Id
                };
                unitOfWork.BlogCategoryTargetTypeRepository.Insert(blogCategoryTargetType);
            }
            await unitOfWork.SaveAsync();


            return new
            {
                id = blogCategory.Id
            };
        }

        public async Task<object> EditCategoryAsync(IUnitOfWork unitOfWork, string userId, EditBlogCategoryModel model)
        {
            if (await unitOfWork.BlogCategoryRepository.GetAll().AnyAsync(a => a.Id != model.Id && a.VanityId == model.VanityId))
            {
                throw new InvalidInputException("This VanityId is occupied");
            }


            BlogCategory blogCategory = await unitOfWork.BlogCategoryRepository.GetByIdAsync(model.Id);
            if (blogCategory == null)
            {
                throw new DbItemNotFoundException("The blog category not found");
            }

            // Delete old Target types
            List<BlogCategoryTargetType> blogCategoryTargetTypes = blogCategory.TargetTypes.ToList();
            if (blogCategoryTargetTypes != null)
            {
                unitOfWork.BlogCategoryTargetTypeRepository.Delete(blogCategoryTargetTypes);
                await unitOfWork.SaveAsync();
            }

            foreach (var targetType in model.TargetTypes)
            {
                var blogCategoryTargetType = new BlogCategoryTargetType
                {
                    TargetBlogType = targetType,
                    BlogCategoryId = blogCategory.Id
                };
                unitOfWork.BlogCategoryTargetTypeRepository.Insert(blogCategoryTargetType);
            }
            await unitOfWork.SaveAsync();


            blogCategory.VanityId = model.VanityId;
            blogCategory.Name = model.Name.Trim();
            blogCategory.ModifiedById = userId;
            blogCategory.ModifiedDate = DateTime.UtcNow;

            unitOfWork.BlogCategoryRepository.Edit(blogCategory);

            await unitOfWork.SaveAsync();

            return new
            {
                id = blogCategory.Id
            };
        }

        public async Task<object> CommentsListAsync(IUnitOfWork unitOfWork, int? blogPostId, string q, int offset, int count)
        {
            var query = unitOfWork.BlogCommentRepository.GetAll();

            if (!string.IsNullOrWhiteSpace(q))
            {
                query = query.Where(e => e.Comment.Contains(q) || e.OwnerName.Contains(q));
            }


            if (blogPostId != null)
            {
                query = query.Where(e => e.BlogPostId == blogPostId.Value);
            }

            var list = await query
                .OrderByDescending(a => a.CreatedDate)
                .Skip(offset)
                .Take(count)
                .Select(a => new
                {
                    id = a.Id,
                    blogPostId = a.BlogPostId,
                    blogPostTitle = a.BlogPost.Title,
                    ownerName = a.OwnerName,
                    comment = a.Comment,
                    createdDate = a.CreatedDate,
                    createdBy = new
                    {
                        id = a.CreatedById,
                        firstName = a.CreatedBy.FirstName,
                        lastName = a.CreatedBy.LastName
                    }
                })
                .ToListAsync();

            return new
            {
                count = await query.CountAsync(),
                data = list,
            };
        }

        public async Task AddSwatchboxComment(IUnitOfWork unitOfWork, string emailsPath, AddSwatchboxBlogCommentModel model)
        {
            ApplicationUser userSwatchBox = await unitOfWork.UserRepository.GetAll()
                    .FirstOrDefaultAsync(x => x.Email == "<EMAIL>");

            // It will be used only once
            if (userSwatchBox == null)
            {
                userSwatchBox = new ApplicationUser
                {
                    Id = Guid.NewGuid().ToString(),
                    Email = "<EMAIL>",
                    FirstName = "SwatchboxBlog",
                    LastName = "SwatchboxBlog",
                    UserName = "<EMAIL>",
                    CreatedDate = DateTime.UtcNow
                };
                unitOfWork.UserRepository.Insert(userSwatchBox);
                try
                {
                    await unitOfWork.SaveAsync();
                }
                catch
                {
                }
            }

            var user = new
            {
                Email = userSwatchBox.Email,
                FirstName = userSwatchBox.FirstName,
                LastName = userSwatchBox.LastName,
                UserId = userSwatchBox.Id
            };

            var post = await unitOfWork.BlogPostRepository.GetAll()
                .FirstOrDefaultAsync(a => a.VanityId == model.PostVanityId);

            if (post == null)
            {
                throw new InvalidInputException("Invalid Post Id");
            }

            BlogComment blogComment = new BlogComment();
            blogComment.BlogPostId = post.Id;
            blogComment.Comment = model.Comment;
            blogComment.OwnerName = user?.FirstName ?? "Guest";
            blogComment.CreatedById = user?.UserId;
            blogComment.CreatedDate = DateTime.UtcNow;
            blogComment.Targettype = TargetBlogType.SwatchBox;

            unitOfWork.BlogCommentRepository.Insert(blogComment);
            try
            {
                await unitOfWork.SaveAsync();
            }
            catch
            {
            }
#if !DEBUG
            List<string> destinations = ConfigurationHelper.GetValue("commentAdminEmail").Split(",").ToList();
            var body = @"<!DOCTYPE html>
                                    <html>
                                    <body>
                                    <h2>Comment information:</h2>
                                    <ul>
                                        <li>Id - " + blogComment.Id.ToString() + @";</li>
                                        <li>OwnerName - " + blogComment.OwnerName + @";</li>
                                        <li>Comment - " + blogComment.Comment + @";</li>
                                        <li>Post id - " + blogComment.BlogPostId + @";</li>
                                        <li>Post title - " + post.Title + @";</li>";

            foreach (BlogTargetType targetType in post.TargetTypes)
                body += $@"<li>Comment type - {targetType.TargetBlogType};</li>";

            body += @"</ul></body></html>";

            await BIMsmithMarket.Core.Providers.EmailNotificationHelper.Create(emailsPath).SendEmail(destinations, "Add comment notification", body);
#endif
        }

        public async Task DeleteCommentAsync(IUnitOfWork unitOfWork, int id)
        {
            BlogComment blogComment = unitOfWork.BlogCommentRepository.GetById(id);
            if (blogComment == null)
            {
                throw new DbItemNotFoundException("The blog comment not found");
            }

            //Delete blog entry
            unitOfWork.BlogCommentRepository.Delete(blogComment);
            await unitOfWork.SaveAsync();
        }

        public async Task<bool> IsReCaptchValidAsync(WebResponse response)
        {
            var result = false;

            using (StreamReader stream = new StreamReader(response.GetResponseStream()))
            {
                JObject jResponse = JObject.Parse(stream.ReadToEnd());
                var isSuccess = jResponse.Value<bool>("success");
                result = (isSuccess) ? true : false;
            }

            return result;
        }

        public async Task<MetaInfoDto> GetNewsMetaInfoAsync(string vanityId, IUnitOfWork unitOfWork)
        {
            var news = await unitOfWork.BlogPostRepository.GetAll()
                                       .Where(x => x.VanityId.ToLower() == vanityId.ToLower())
                                       .FirstOrDefaultAsync();

            if (news == null)
                throw new DbItemNotFoundException();

            return news.Adapt<MetaInfoDto>();
        }

        public async Task<string> GetUserPhoto(string email)
        {
            var secretKey = ConfigurationHelper.GetValue("ApiAccessToken");
            var endPoint = ConfigurationHelper.GetValue("BimsmithApiUrl");

            HttpClient client = _httpClientFactory.CreateClient();
            {
                HttpRequestMessage httpRequestMessage = new HttpRequestMessage();
                httpRequestMessage.Headers.Add("X-API-Token", secretKey);
                httpRequestMessage.RequestUri = new Uri($"{endPoint}/users/picture?email={email}");
                httpRequestMessage.Method = HttpMethod.Get;

                using (var response = await client.SendAsync(httpRequestMessage))
                {
                    if (response.IsSuccessStatusCode)
                    {
                        var content = await response.Content.ReadAsStringAsync();
                        return content.Trim('\"');
                    }
                    else
                    {
                        return string.Empty;
                    }
                }
            }
        }
    }
}