﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class VanityHistoryService : IVanityHistoryService
    {
        public async Task<object> ListAsync(IUnitOfWork unitOfWork, string q, int parentId, int entityId, int offset, int count)
        {
            var vanityHistories = unitOfWork.VanityHistoryRepository.GetAll();
            if (!string.IsNullOrWhiteSpace(q))
            {
                vanityHistories = vanityHistories.Where(a => a.VanityUrl.Contains(q) || a.EntityType.Contains(q));
            }

            if (parentId != -1)
            {
                vanityHistories = vanityHistories.Where(a => a.ParentId == parentId);
            }

            if (entityId != -1)
            {
                vanityHistories = vanityHistories.Where(a => a.EntityId == entityId);
            }

            var dataCount = await vanityHistories.CountAsync();
            var list = await vanityHistories
            .OrderByDescending(x => x.CreatedDate)
            .Select(a => new
            {
                id = a.Id,
                entityType = a.EntityType,
                entityId = a.EntityId,
                url = a.VanityUrl,
                dateTime = a.CreatedDate,
                parent = a.ParentId == null ? null : new
                {
                    id = a.ParentId,
                    url = a.Parent.VanityUrl,
                    entityId = a.EntityId,
                    parent = a.Parent.ParentId == null ? null : new
                    {
                        id = a.Parent.ParentId,
                        url = a.Parent.Parent.VanityUrl,
                        entityId = a.EntityId
                    }
                }
            })
            .Skip(offset)
            .Take(count)
            .AsNoTracking()
            .ToListAsync();

            return new
            {
                count = dataCount,
                data = list
            };
        }

        public async Task<object> AddAsync(IUnitOfWork unitOfWork, AddVanityHistoryModel model)
        {
            var vanityHistory = new VanityHistory
            {
                EntityId = model.EntityId,
                EntityType = model.EntityType,
                VanityUrl = model.VanityUrl,
                ParentId = model.ParentId,
                CreatedDate = DateTime.UtcNow
            };

            unitOfWork.VanityHistoryRepository.Insert(vanityHistory);

            await unitOfWork.SaveAsync();

            return new
            {
                id = vanityHistory.Id,
                entityType = vanityHistory.EntityType,
                entityId = vanityHistory.EntityId,
                url = vanityHistory.VanityUrl
            };
        }
    }
}
