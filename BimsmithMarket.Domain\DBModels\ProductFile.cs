﻿using BIMsmithMarket.Domain.DBModels.RevitProcessing;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class ProductFile : BaseEntity
    {
        public string CustomFileId { get; set; }

        public int ProductId { get; set; }

        public int FileId { get; set; }

        public int Weight { get; set; }

        public bool WasChanged { get; set; }

        /// <summary>
        /// Parent category Id of project data type
        /// </summary>
        public int? ProjectDataTypeId { get; set; }
        [ForeignKey("ProjectDataTypeId")]
        public virtual ProjectDataType ProjectDataType { get; set; }

        /// <summary>
        /// Old field that will be recreated with SoftwareVersion logic
        /// </summary>
        public string SoftwareRelease { get; set; }
        /// <summary>
        /// Link to child ProjectDataType that set exact Version
        /// </summary>
        public int? SoftwareVersionId { get; set; }
        [ForeignKey("SoftwareVersionId")]
        public virtual ProjectDataType SoftwareVersion { get; set; }

        public string ContentCreatedby { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        public string FileVersion { get; set; }

        public string ContentCheckedBy { get; set; }

        public bool IsULPartnership { get; set; }

        public bool IsAttachment { get; set; }

        /// ------------------------------------------

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }

        [ForeignKey("FileId")]
        public virtual File File { get; set; }

        public virtual ICollection<RevitJob> RevitJobs { get; set; }

        public ProductFile()
        {
            RevitJobs = new List<RevitJob>();
        }
    }
}