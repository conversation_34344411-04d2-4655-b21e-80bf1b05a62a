﻿using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IBIMsmithService
    {
        Task<object> UserProductsListAsync(IUnitOfWork unitOfWork, string userId, string streamId, int offset, int count);
        Task<object> UserManufacturersListAsync(IUnitOfWork unitOfWork, string userId, int offset, int count);
        Task RemoveProductFromMyBIMsmithAsync(IUnitOfWork unitOfWork, string ownerId, int productId, string streamId);
        Task RemoveManufacturerFromMyBIMsmithAsync(IUnitOfWork unitOfWork, string ownerId, int manufacturerId);
        Task CopyProductToMyBIMsmith(IUnitOfWork unitOfWork, string ownerId, int productId, string fromStreamId, string toStreamId);
        Task<object> ClearUnusedFiles(IUnitOfWork unitOfWork, string photosFolder, string filesFolder);
        Task<object> GeneratePreviewForPdfAndTxtFilesAsync(IUnitOfWork unitOfWork, string filesFolder, bool pdf, bool image, bool txt, int offset, int count);
        Task ChangeFilesUrlAsync(IUnitOfWork unitOfWork, List<Domain.DBModels.File> httpLinkFiles, string http, string https, int takeCount, bool needContinue);
    }
}
