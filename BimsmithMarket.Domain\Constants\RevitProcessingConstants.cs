﻿using BIMsmithMarket.Domain.Dto.RevitProcessing;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Enums.RevitProcessing;

namespace BIMsmithMarket.Domain.Constants
{
    public static class RevitProcessingConstants
    {
        public const int RevitProjectDataTypeId = 1;

        public const int MinimumRevitVersion = 2021;

        public static readonly string[] RevitVersions = ["2021", "2022", "2023", "2024", "2025"];

        public static readonly string[] UpdateParametersRevitFileExtensions = [".rfa"];

        public static readonly string[] UpdateVersionRevitFileExtenstions = [".rfa", ".rvt", ".rte"];

        public static readonly RevitProcessTypeDto[] RevitProcessTypes =
        [
            new RevitProcessTypeDto { Type = RevitProcessType.UpdateParameterValues, Description = "Update Parameter Values" },
            new RevitProcessTypeDto { Type = RevitProcessType.UpdateRevitVersion, Description = "Update Revit Version" }
        ];

        public static readonly MarketFieldDto[] MarketFields =
        [
            new MarketFieldDto { MarketField = MarketField.ManufacturerName, Description = "Manufacturer Name" },
            new MarketFieldDto { MarketField = MarketField.ProductName, Description = "Product Name" },
            new MarketFieldDto { MarketField = MarketField.ProductAssemblyCode, Description = "Product Assembly Code" },
            new MarketFieldDto { MarketField = MarketField.ProductLineName, Description = "Product Line Name" }
        ];

        public const string AccessTokenHeader = "X-Revit-Processing-Access-Token";

        public const string RevitProcessingCaption = "Revit Processing";
    }
}