﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class ProductRating
    {
        public int Id { get; set; }

        public int ProductId { get; set; }

        public int Rating { get; set; }

        public string Comment { get; set; }

        public ProductRatingType Type { get; set; }

        public string PostedById { get; set; }

        public DateTime PostedDate { get; set; }

        /// ------------------------------------------
        [<PERSON><PERSON><PERSON>("PostedById")]
        public virtual ApplicationUser PostedBy { get; set; }

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }
    }

    public enum ProductRatingType
    {
        ProductRating = 0,
        ContentRating = 1
    }
}