﻿using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IVanityHistoryService
    {
        Task<object> ListAsync(IUnitOfWork unitOfWork, string q, int parentId, int entityId, int offset, int count);
        Task<object> AddAsync(IUnitOfWork unitOfWork, AddVanityHistoryModel model);
    }
}
