﻿using System.IO;
using System.Linq;
using System.Text.RegularExpressions;

namespace BIMsmithMarket.Core.Helpers
{
    public static class FileHelper
    {
        private static readonly string[] _vectorExtensions = new[] { ".svg" };

        public static string RemoveInvalidChars(string fileName)
        {
            if (string.IsNullOrWhiteSpace(fileName))
                return string.Empty;

            var regexSearch = new string(Path.GetInvalidFileNameChars()) + new string(Path.GetInvalidPathChars());
            Regex r = new Regex(string.Format("[{0}]", Regex.Escape(regexSearch)));
            fileName = r.Replace(fileName, "");

            //Remove Unicode Characters using Regex
            fileName = Regex.Replace(fileName, @"[^\u0000-\u007F]", string.Empty);

            if (fileName.Length > 250)
            {
                fileName = fileName.Substring(0, 250);
            }

            return fileName;
        }

        public static string RemoveQueryPath(string fileName)
        {
            if (fileName.Contains("?"))
                fileName = fileName.Substring(0, fileName.IndexOf("?"));

            return fileName;
        }

        public static bool IsVectorExtension(string extension)
        {
            return _vectorExtensions.Any(x => x == extension.Trim().ToLower());
        }
    }
}