﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto.AnnouncementDto;
using Mapster;
using System.Linq;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public static class AnnouncementMap
    {
        public static void Register(TypeAdapterConfig config)
        {
            config.ForType<Announcement, GetAnnouncementDto>()
                .Map(dest => dest.ManufacturerIds, opt => opt.ManufacturerAnnouncements.Select(x => x.ManufacturerId));
        }
    }
}