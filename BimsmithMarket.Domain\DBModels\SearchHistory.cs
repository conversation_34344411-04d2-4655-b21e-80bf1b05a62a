﻿using System;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.DBModels
{
    public class SearchHistory
    {
        public int Id { get; set; }

        public DateTime QueryDate { get; set; }

        [StringLength(100)]
        public string Email { get; set; }

        [StringLength(50)]
        public string IPAddress { get; set; }

        [StringLength(100)]
        public string Query { get; set; }

        public string Options { get; set; }

        public int NumResults { get; set; }

        public long SearchTime { get; set; }

        public long TotalTime { get; set; }

        public int NumSecondary { get; set; }
    }
}