﻿using BIMsmithMarket.Domain.DBModels;
using System;
using System.Collections.Generic;

namespace BIMsmithBlog.Models
{
    public class BlogDetailsModel : CommonModel
    {
        public int Id { get; set; }

        public string VanityId { get; set; }

        public string Title { get; set; }

        public string Descriptions { get; set; }

        public string AuthorTitle { get; set; }

        public string AuthorEmail { get; set; }

        public string AuthorImage { get; set; }

        public CategoryModel Category { get; set; }

        public string HtmlBody { get; set; }

        public string ImageUrlBig { get; set; }

        public string ImageUrlSmall { get; set; }

        public DateTime? PublishedDate { get; set; }

        public List<string> Tags { get; set; }

        public List<BlogComment> BlogComments { get; set; }

        public List<BlogPreview> OtherBlogs { get; set; }

        public PublishOption PublishOption { get; set; }
    }

    public class BlogComment
    {
        public int Id { get; set; }

        public string OwnerName { get; set; }

        public DateTime Date { get; set; }

        public string Comment { get; set; }

    }
}