﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using Mapster;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public static class StaticExcelMap
    {
        public static void Register(TypeAdapterConfig config)
        {
            config.ForType<StaticExcelFile, StaticExcelFileListDto>()
                .Map(d => d.ValidationErrors, s => !string.IsNullOrWhiteSpace(s.ValidationErrors) ? JsonConvert.DeserializeObject<List<string>>(s.ValidationErrors) : new List<string>());

            config.ForType<StaticExcelProductError, StaticExcelProductErrorListDto>()
                .Map(d => d.Errors, s => !string.IsNullOrWhiteSpace(s.Errors) ? JsonConvert.DeserializeObject<List<string>>(s.Errors) : new List<string>());
        }
    }
}