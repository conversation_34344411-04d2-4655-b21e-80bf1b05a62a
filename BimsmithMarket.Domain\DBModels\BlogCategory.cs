﻿using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.DBModels
{
    [Index(nameof(VanityId), Name = "IX_BlogCategory_VanityId")]
    public class BlogCategory : BaseEntity
    {
        [StringLength(300)]
        public string VanityId { get; set; }

        public string Name { get; set; }

        public int Status { get; set; }

        public virtual ICollection<BlogPost> BlogPosts { get; set; }

        public virtual ICollection<BlogCategoryTargetType> TargetTypes { get; set; }

        public BlogCategory()
        {
            BlogPosts = new List<BlogPost>();
            TargetTypes = new List<BlogCategoryTargetType>();
        }
    }
}