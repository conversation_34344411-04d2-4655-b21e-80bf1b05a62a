﻿using System.Net;
using System.Net.Http;

namespace BIMsmithMarket.Core.Helpers
{
    public static class NetworkHelper
    {
        public static bool IsResponseInvalid(HttpResponseMessage response)
        {
            return response == null
                || (response != null
                && ((int)response.StatusCode >= 500
                || response.StatusCode == HttpStatusCode.Forbidden
                || response.StatusCode == HttpStatusCode.TooManyRequests
                || response.StatusCode == HttpStatusCode.RequestTimeout));
        }
    }
}