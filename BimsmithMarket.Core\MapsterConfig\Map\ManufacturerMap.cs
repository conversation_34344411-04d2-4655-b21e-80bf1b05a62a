﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.Manufacturer;
using BIMsmithMarket.Domain.Models;
using Mapster;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public static class ManufacturerMap
    {
        public static void Register(TypeAdapterConfig config)
        {
            config.ForType<Manufacturer, ManufacturerInfoModel>()
                .Map(d => d.VanityUrl, s => s.HubVanityURL);

            config.ForType<Manufacturer, ManufacturerListDto>()
                .Map(d => d.ProductsCount, s => s.Products.Count)
                .Map(d => d.UpdateDate, s => s.ModifiedDate ?? s.CreatedDate)
                .Map(d => d.Logo, s => new ManufacturerListLogoDto
                {
                    Id = s.PhotoId,
                    Small = s.PhotoId != null ? s.Photo.SmallImgUrl : null,
                    Big = s.PhotoId != null ? s.Photo.OriginalImgUrl : null,
                })
                .Map(d => d.RevitIcon, s => s.RevitPluginPhotoId == null ? null : new ManufacturerListRevitIconDto
                {
                    Id = s.RevitPluginPhotoId,
                    SmallUrl = s.RevitPluginPhoto.SmallImgUrl,
                    OriginalUrl = s.RevitPluginPhoto.OriginalImgUrl
                });

            config.ForType<RequestPricingUser, RequestPricingUserListDto>()
                .Map(d => d.UserEmail, s => s.CreatedBy.Email)
                .Map(d => d.UserFirstName, s => s.CreatedBy.FirstName)
                .Map(d => d.UserLastName, s => s.CreatedBy.LastName)
                .Map(d => d.ProductName, s => s.Product != null ? s.Product.Name : string.Empty);

            config.ForType<RequestPricingUser, RequestPricingUserExcelDto>()
                .Map(d => d.UserEmail, s => s.CreatedBy.Email)
                .Map(d => d.UserFirstName, s => s.CreatedBy.FirstName)
                .Map(d => d.UserLastName, s => s.CreatedBy.LastName)
                .Map(d => d.ProductName, s => s.Product != null ? s.Product.Name : string.Empty);

            config.ForType<Manufacturer, AddManufacturerResult>()
                .Map(d => d.Logo, s => s.Photo)
                .Map(d => d.RevitIconUrl, s => s.RevitPluginPhoto.SmallImgUrl);

            config.ForType<Manufacturer, ManufacturerBackupModel>()
                .Ignore(d => d.Products)
                .Ignore(d => d.ProductLines)
                .Ignore(d => d.ManufacturerPhotoIds)
                .Ignore(d => d.ManufacturerFiles)
                .Ignore(d => d.Details)
                .Ignore(d => d.SubscribedUsers)
                .Ignore(d => d.LetsTalkUsers)
                .Ignore(d => d.LunchAndLearnUsers)
                .Ignore(d => d.BimRequests)
                .Ignore(d => d.ManufacturerAdminUsers)
                .Ignore(d => d.AttachmentOrders)
                .Ignore(d => d.RequestPricingUsers)
                .Ignore(d => d.SalesRepresentatives);
        }
    }
}