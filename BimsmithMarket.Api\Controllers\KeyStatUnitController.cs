﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Helpers;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Globalization;
using System.Security.Claims;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("api/[controller]/[action]/{id?}")]
    public class KeyStatUnitController : BaseApiController
    {
        private readonly IKeyStatUnitService _keyStatUnitService;

        public KeyStatUnitController(IKeyStatUnitService keyStatUnitService)
        {
            _keyStatUnitService = keyStatUnitService;
        }

        /// <summary>
        /// Get list of available unit groups
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("Groups")]
        public async Task<IActionResult> Groups()
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _keyStatUnitService.GroupsAsync(unitOfWork));
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("ConvertValue")]
        public async Task<IActionResult> ConvertValue(string formula, double value)
        {
            var resut = RPNExpression.Calculate(formula, value);
            return Ok(resut);
        }

        /// <summary>
        /// Convert string value to relation unit key
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("TryToConvertValue")]
        public async Task<IActionResult> TryToConvertValue(string stringValue, int fromKeyStatUnitId, int toKeyStatUnitId)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var relation = await unitOfWork.KeyStatUnitRelationRepository.GetAll().FirstAsync(a => a.FromUnitId == fromKeyStatUnitId && a.ToUnitId == toKeyStatUnitId);

                var resut = TryToConvertValue(stringValue, relation);
                return Ok(resut);
            }
        }

        /// <summary>
        /// Get list of key stat units
        /// </summary>
        /// <param name="q"></param>
        /// <param name="group">filter by group</param>
        /// <param name="count"></param>
        /// <param name="offset"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("List")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> List(string q = null, string group = null, int count = 10, int offset = 0)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _keyStatUnitService.ListAsync(unitOfWork, q, group, count, offset));
            }
        }

        /// <summary>
        /// Get detailed information about Key Stat Unit
        /// </summary>
        /// <param name="id">Id of Key Stat Unit</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("Get")]
        public async Task<IActionResult> Get(int id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _keyStatUnitService.GetAsync(unitOfWork, id));
            }
        }

        /// <summary>
        /// Add new Key stat unit: Role-ADMIN
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Add")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> Add([FromBody] AddKeyStatUnitModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
                var result = await _keyStatUnitService.AddAsync(unitOfWork, model, userId);

                CacheHelper.ClearSpecificCache("*/api/KeyStatUnit/List*");

                return Ok(result);
            }
        }

        /// <summary>
        /// Edit Key stat unit: Role-ADMIN
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Edit")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> Edit([FromBody] EditKeyStatUnitModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
                await _keyStatUnitService.EditAsync(unitOfWork, model, userId);

                CacheHelper.ClearSpecificCache("*/api/KeyStatUnit/List*");

                return Ok();
            }
        }

        /// <summary>
        /// Delete key stat unit (Note: will be delated all dependens for this item): Role-ADMIN
        /// </summary>
        /// <param name="id">Key Stat Id</param>
        /// <returns></returns>
        [HttpDelete]
        [ActionName("Delete")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> Delete(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                await _keyStatUnitService.DeleteAsync(unitOfWork, id);

                CacheHelper.ClearSpecificCache("*/api/KeyStatUnit/List*");

                return Ok();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="stringValue"></param>
        /// <param name="relation"></param>
        /// <returns></returns>
        public static string TryToConvertValue(string stringValue, KeyStatUnitRelation relation)
        {
            string result = "Error";

            var regex = new Regex("^[0-9,./ ]+$");

            stringValue = stringValue.Trim(' ', '\"');

            if (regex.IsMatch(stringValue))
            {
                double doubleValue = 0;

                var stringValues = stringValue.Split(' ');

                if (stringValues.Length == 1) //4.34 , 4/3, 1/2
                {
                    doubleValue = RPNExpression.Calculate(stringValues[0]);
                }
                else if (stringValues.Length == 2) //4 4, 4 4/3, 4.43 4/3
                {
                    var v1 = stringValues[0];
                    var v2 = stringValues[1];

                    if (v2.Contains('/')) //the value is inch
                    {
                        doubleValue = RPNExpression.Calculate(v1) + RPNExpression.Calculate(v2);
                    }
                    else // the value is foot
                    {
                        doubleValue = RPNExpression.Calculate(v1) + (RPNExpression.Calculate(v2) * 0.0833333);
                    }
                }
                else if (stringValues.Length == 3) //4 4 3/4  (foot inch inch)
                {
                    var v1 = stringValues[0];
                    var v2 = stringValues[1];
                    var v3 = stringValues[1];

                    doubleValue = doubleValue = RPNExpression.Calculate(v1) + ((RPNExpression.Calculate(v2) + RPNExpression.Calculate(v3)) * 0.0833333);
                }
                else
                {
                    throw new NotImplementedException();
                }

                var value = RPNExpression.Calculate(relation.Relation, doubleValue);
                value = Math.Round(value, relation.RoundCountDigits);

                result = value.ToString(CultureInfo.InvariantCulture);
            }

            return result;
        }
    }
}