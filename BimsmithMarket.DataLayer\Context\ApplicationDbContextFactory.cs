﻿using BIMsmithMarket.Core.Helpers;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using System;

namespace BIMsmithMarket.DataLayer.Context
{
    public class ApplicationDbContextFactory
    {
        private static readonly Lazy<PooledDbContextFactory<ApplicationDbContext>> _factory = new Lazy<PooledDbContextFactory<ApplicationDbContext>>(GetFactory);

        public static ApplicationDbContext CreateContext()
        {
            return _factory.Value.CreateDbContext();
        }

        #region private methods
        private static PooledDbContextFactory<ApplicationDbContext> GetFactory()
        {
            DbContextOptions<ApplicationDbContext> options = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseLazyLoadingProxies()
                .UseSqlServer(ConfigurationHelper.GetValue("ConnectionStrings:MarketDBConnection"))
                .Options;

            return new PooledDbContextFactory<ApplicationDbContext>(options);
        }
        #endregion
    }
}