﻿using BIMsmith.ExcelProvider;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using DocumentFormat.OpenXml.Spreadsheet;
using System;
using System.IO;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class ChangeLogExcelService : IChangeLogExcelService
    {
        private readonly IChangeLogService _changeLogService;
        private readonly IExcelHelper _excelHelper;

        public ChangeLogExcelService(
            IChangeLogService changeLogService,
            IExcelHelper excelHelper)
        {
            _changeLogService = changeLogService;
            _excelHelper = excelHelper;
        }

        public async Task<string> GetExcelAsync(
            IUnitOfWork unitOfWork,
            EntityType entityType,
            int entityId,
            DateTime? startDate = null,
            DateTime? endDate = null)
        {
            ChangeLogExcelDto[] items = await _changeLogService.ExcelListAsync(unitOfWork, entityType, entityId, startDate, endDate);
            string fileName = $"ChangeLogsExcel_{Guid.NewGuid()}.xlsx";
            string filePath = Path.Combine(Path.GetTempPath(), fileName);

            CreateExcelOptions excelOptions = new()
            {
                FilePath = filePath,
                Sheets =
                [
                    new ExcelSheet
                    {
                        Data = items,
                        Fields =
                        [
                            new ExcelSheetField { Name = nameof(ChangeLogExcelDto.Id) },
                            new ExcelSheetField { Name = nameof(ChangeLogExcelDto.CreatedDate) },
                            new ExcelSheetField { Name = nameof(ChangeLogExcelDto.UserEmail) },
                            new ExcelSheetField { Name = nameof(ChangeLogExcelDto.EntityAction) },
                            new ExcelSheetField { Name = nameof(ChangeLogExcelDto.Field) },
                            new ExcelSheetField { Name = nameof(ChangeLogExcelDto.OldValue) },
                            new ExcelSheetField { Name = nameof(ChangeLogExcelDto.NewValue) },
                        ],
                        Headers =
                        [
                            ChangeLogExcelConstants.IdCaption,
                            ChangeLogExcelConstants.CreatedDateCaption,
                            ChangeLogExcelConstants.UserEmailCaption,
                            ChangeLogExcelConstants.EntityActionCaption,
                            ChangeLogExcelConstants.FieldCaption,
                            ChangeLogExcelConstants.OldValueCaption,
                            ChangeLogExcelConstants.NewValueCaption
                        ],
                        Name = "Change Logs"
                    }
                ]
            };

            _excelHelper.CreateExcel(excelOptions);

            return filePath;
        }
    }
}