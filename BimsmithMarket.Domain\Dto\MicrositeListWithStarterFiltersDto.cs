﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Enums;
using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Dto
{
    public class MicrositeListWithStarterFiltersDto
    {
        public string ManufacturerName { get; set; } = null;

        public string VanityUrlPart { get; set; } = null;

        public string Q { get; set; } = null;

        public string ProjectTypeIds { get; set; } = null;

        public int CategoryId { get; set; } = -1;

        public int ProductLineId { get; set; } = -1;

        public int Offset { get; set; } = 0;

        public int Count { get; set; } = 10;

        public bool Published { get; set; } = false;

        public bool PublishedOnCustomMicrosite { get; set; } = false;

        public bool WithStarter { get; set; } = true;

        public StartersPosition StartersPosition { get; set; } = StartersPosition.EndWithStarters;

        public ProductSortType SortType { get; set; } = ProductSortType.Relevant;

        public bool IncludeHideOnMicrosite { get; set; }

        public string RegionId { get; set; } = null;

        public string StateId { get; set; } = null;

        public ICollection<MicrositeListWithStarterFiltersKeyStatDto> KeyStats { get; set; }
    }

    public class MicrositeListWithStarterFiltersKeyStatDto
    {
        public int KeyStatId { get; set; }

        public ICollection<MicrositeListWithStarterFiltersKeyStatValueDto> Values { get; set; }

        public MicrositeListWithStarterFiltersKeyStatDto()
        {
            Values = new List<MicrositeListWithStarterFiltersKeyStatValueDto>();
        }
    }

    public class MicrositeListWithStarterFiltersKeyStatValueDto
    {
        public string Value { get; set; }

        public string MaxRangeValue { get; set; }

        public KeyStatType KeyStatType { get; set; }

        public int? KeyStatUnitId { get; set; }
    }
}