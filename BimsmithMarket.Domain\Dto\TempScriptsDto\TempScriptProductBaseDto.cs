﻿using System;
using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Dto.TempScriptsDto
{
    public class TempScriptProductBaseDto
    {
        public int ProductId { get; set; }

        public string ProductName { get; set; }

        public string ProductPublicPage { get; set; }

        public string ProductAdminPanelPage { get; set; }

        public int ManufacturerId { get; set; }

        public string ManufacturerName { get; set; }
    }

    public class TempScriptProductBaseDtoEqualityComparer : IEqualityComparer<TempScriptProductBaseDto>
    {
        public bool Equals(TempScriptProductBaseDto x, TempScriptProductBaseDto y)
        {
            if (ReferenceEquals(x, y))
                return true;

            if (x == null || y == null)
                return false;

            return x.ProductId == y.ProductId
                   && x.ProductName == y.ProductName
                   && x.ProductPublicPage == y.ProductPublicPage
                   && x.ProductAdminPanelPage == y.ProductAdminPanelPage
                   && x.ManufacturerId == y.ManufacturerId
                   && x.ManufacturerName == y.ManufacturerName;
        }

        public int GetHashCode(TempScriptProductBaseDto obj)
        {
            return HashCode.Combine(
                obj.ProductId,
                obj.ProductName,
                obj.ProductPublicPage,
                obj.ProductAdminPanelPage,
                obj.ManufacturerId,
                obj.ManufacturerName
            );
        }
    }
}