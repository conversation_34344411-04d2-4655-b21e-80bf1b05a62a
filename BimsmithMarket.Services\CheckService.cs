﻿using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Services.Interfaces;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class CheckService : ICheckService
    {
        public async Task<OperationResultDto> CheckSMTPCredentialsAsync(string subject)
        {
            await EmailNotificationHelper.Create(null).SendTestEmailAsync(subject);
            return new OperationResultDto
            {
                Status = Domain.Enums.OperationResultStatus.Succeed
            };
        }
    }
}