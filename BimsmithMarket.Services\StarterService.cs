﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.ForgeDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class StarterService : IStarterService
    {
        private readonly IHttpClientFactory _httpClientFactory;

        public StarterService(IHttpClientFactory httpClientFactory)
        {
            _httpClientFactory = httpClientFactory;
        }

        /// <summary>
        /// Refresh starters by forgeManufacturerId
        /// </summary>
        /// <param name="forgeManufacturerId"></param>
        public async Task RefreshStartersFromForgeAsync(string forgeManufacturerId = null)
        {
            List<ForgeStarterDto> forgeStarters = null;

            HttpClient client = _httpClientFactory.CreateClient();
            {
                var jsonFilter = new
                {
                    Count = 1000,
                    Active = true,
                    manufacturerId = forgeManufacturerId
                };

                var jsonBody = JsonConvert.SerializeObject(jsonFilter);
                var forgeStartersUpdateUrl = ConfigurationHelper.GetValue("ForgeStartersEndPoint");
                using (var response = await client.PostAsync(forgeStartersUpdateUrl, new StringContent(jsonBody, Encoding.UTF8, "application/json")))
                {
                    if (response.StatusCode == HttpStatusCode.OK)
                    {
                        var jsonContent = await response.Content.ReadAsStringAsync();
                        var apiDataResult = JsonConvert.DeserializeObject<ForgeStartersDto>(jsonContent);
                        forgeStarters = apiDataResult.Data;
                    }
                    else
                    {
                        throw new Exception($"Could not update forge starters. Status code: {response.StatusCode}");
                    }
                }
            }

            if (forgeStarters != null)
            {
                using (IUnitOfWork unitOfWork = UnitOfWork.Create())
                {
                    unitOfWork.CurrentDbContext.ChangeTracker.AutoDetectChangesEnabled = false;

                    var allStartersQuery = unitOfWork.StarterRepository.GetAll();
                    if (forgeManufacturerId != null)
                    {
                        allStartersQuery = allStartersQuery.Where(x => x.ForgeManufacturerId == forgeManufacturerId);
                    }
                    var allStarters = allStartersQuery.ToList();
                    var forgeStartersIds = forgeStarters.Select(x => x.Id).ToList();

                    var excessStarters = allStarters.Where(x => !forgeStartersIds.Contains(x.Id)).ToList();
                    foreach (var excessStarter in excessStarters)
                    {
                        var starterProductLines = excessStarter.ProductLines.ToList();
                        unitOfWork.StarterProductLinesRepository.Delete(starterProductLines);
                        unitOfWork.Save();
                    }
                    unitOfWork.StarterRepository.Delete(excessStarters);
                    unitOfWork.Save();

                    foreach (var forgeStarter in forgeStarters)
                    {
                        Starter marketStarter = unitOfWork.StarterRepository.GetAll().FirstOrDefault(x => x.Id == forgeStarter.Id);

                        bool newStarter = marketStarter == null ? true : false;
                        if (newStarter) marketStarter = new Starter();

                        marketStarter.Id = forgeStarter.Id;
                        marketStarter.ForgeManufacturerId = forgeStarter.Manufacturer.Id;
                        marketStarter.ManufacturerName = forgeStarter.Manufacturer.Name;
                        marketStarter.Name = forgeStarter.Name;
                        marketStarter.Brand = forgeStarter.Brand;
                        marketStarter.Orientation = forgeStarter.Orientation;
                        marketStarter.Preview = forgeStarter.I;
                        marketStarter.UpdateCacheTime = DateTime.UtcNow;

                        var allCountry = forgeStarter.StarterCountries?.Where(x => x.Country?.ToLower() == "all").FirstOrDefault();
                        if (allCountry != null)
                        {
                            marketStarter.STC = allCountry.Stc;
                            marketStarter.FSTC = allCountry.FSTC;
                            marketStarter.RValue = allCountry.R;
                            marketStarter.Fire = allCountry.Fire;
                            marketStarter.UL = allCountry.Ul;
                            marketStarter.IIC = allCountry.IIC;
                            marketStarter.FIIC = allCountry.FIIC;
                            marketStarter.NFPA285 = allCountry.Nfpa285;
                        }

                        //Save Starter
                        //---------------------
                        if (newStarter)
                        {
                            marketStarter.UtcDateCreated = DateTime.UtcNow;
                            unitOfWork.StarterRepository.Insert(marketStarter);
                        }
                        else
                        {
                            marketStarter.UtcDateModified = DateTime.UtcNow;
                            unitOfWork.StarterRepository.Edit(marketStarter);
                        }
                        try
                        {
                            await unitOfWork.SaveAsync();
                        }
                        catch (Exception)
                        {

                        }

                        //Delete old productLines From Starter
                        if (!newStarter)
                        {
                            var marketStartersProductLines = marketStarter.ProductLines.ToList();
                            if (marketStartersProductLines.Any())
                            {
                                unitOfWork.StarterProductLinesRepository.Delete(marketStartersProductLines);
                                unitOfWork.Save();
                            }
                        }

                        foreach (var productLineForStarter in forgeStarter.ProductLines)
                        {
                            //Using only Forge productLines that connected to market ProductLine
                            var marketProductLineId = unitOfWork.ForgeProductLineIdsRepository.GetAll()
                                                               .FirstOrDefault(x => x.ForgeProductLineId == productLineForStarter.ForgeId)
                                                              ?.ProductLineId;

                            if (marketProductLineId != null)
                            {
                                var starterProductLine = new StarterProductLines
                                {
                                    ProductLineId = marketProductLineId.Value,
                                    StarterId = marketStarter.Id
                                };
                                unitOfWork.StarterProductLinesRepository.Insert(starterProductLine);
                                await unitOfWork.SaveAsync();

                                marketStarter.ProductLines.Add(starterProductLine);

                                unitOfWork.StarterRepository.Edit(marketStarter);
                                await unitOfWork.SaveAsync();
                            }

                            //ToDo: Add new ProductLine From Forge!
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Lists the starters
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier</param>
        /// <param name="q">The query string</param>
        /// <param name="offset">The offset to skip</param>
        /// <param name="count">The count to take</param>
        /// <returns></returns>
        public async Task<PaginationListDto<StarterListDto>> ListAsync(int manufacturerId = -1, bool? published = null, bool? staging = null, string q = null, int offset = 0, int count = 10)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var query = unitOfWork.StarterRepository.GetAll();

                if (!string.IsNullOrEmpty(q))
                {
                    query = query.Where(e => e.Brand.StartsWith(q) || e.Name.Contains(q) || e.ManufacturerName.StartsWith(q));
                }

                if (manufacturerId != -1)
                {
                    var forgeManufacturerId = unitOfWork.ManufacturerRepository.GetAll().Where(a => a.Id == manufacturerId).Select(a => a.ForgeManufacturerId).First();
                    query = query.Where(a => a.ForgeManufacturerId == forgeManufacturerId);
                }

                if (staging.HasValue)
                {
                    published = null;
                    query = query.Where(x => x.Staging == staging || x.Published);
                }

                if (published.HasValue)
                {
                    query = query.Where(x => x.Published == published);
                }

                var countOfData = await query.CountAsync();

                var items = await query
                    .OrderByDescending(a => a.Weight)
                    .ProjectToType<StarterListDto>()
                    .Skip(offset)
                    .Take(count)
                    .ToArrayAsync();

                var result = new PaginationListDto<StarterListDto>
                {
                    Count = countOfData,
                    Data = items
                };

                return result;
            }
        }

        public async Task UpdateWeightsAsync(UpdateWeightsDto model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                unitOfWork.BeginTransaction();
                var starterIds = model.Weights.Select(a => a.Id).ToList();
                var starters = unitOfWork.StarterRepository.GetAll().Where(a => starterIds.Contains(a.Id)).ToList();

                foreach (var starter in starters)
                {
                    starter.Weight = model.Weights.First(a => a.Id == starter.Id).Weight;
                    unitOfWork.StarterRepository.Edit(starter);
                }

                await unitOfWork.SaveAsync();
                unitOfWork.CommitTransaction();
            }
        }

        public async Task SetPublishStatusAsync(SetStarterStatusDto model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                Starter starter = await unitOfWork.StarterRepository.GetAll()
                                                  .FirstOrDefaultAsync(x => x.Id == model.EntityId);

                if (starter == null)
                    throw new InvalidInputException("Starter not found");

                starter.Published = model.Status;
                starter.UtcDateModified = DateTime.UtcNow;
                unitOfWork.StarterRepository.Edit(starter);
                await unitOfWork.SaveAsync();
            }
        }

        public async Task SetStagingStatusAsync(SetStarterStatusDto model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                Starter starter = await unitOfWork.StarterRepository.GetAll()
                                                  .FirstOrDefaultAsync(x => x.Id == model.EntityId);

                if (starter == null)
                    throw new InvalidInputException("Starter not found");

                starter.Staging = model.Status;
                starter.UtcDateModified = DateTime.UtcNow;
                unitOfWork.StarterRepository.Edit(starter);
                await unitOfWork.SaveAsync();
            }
        }
    }
}