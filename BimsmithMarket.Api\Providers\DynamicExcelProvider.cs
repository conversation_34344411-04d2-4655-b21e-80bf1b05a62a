﻿using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models.ExcelModels.DynamicExcel;
using BIMsmithMarket.Domain.Models.ExcelModels.StaticExcel;
using BIMsmithMarket.Services;
using BIMsmithMarket.Services.HealthDashboardServices;
using BIMsmithMarket.Services.Interfaces;
using BIMsmithMarket.Services.PaymentServices;
using BIMsmithMarket.Services.ProductServices;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using Flurl;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Providers
{
    public static class DynamicExcelProvider
    {
        private static readonly ICollection<DynamicExcelMappingModel> _dynamicExcelMappingModels = SetDynamicExcelMapping();
        private static readonly IMasterformatService _masterformatService = new MasterformatService(new CacheService());
        private const string RevitFilesShareDirectoryName = "marketrevitfiles";
        private const string InternsRevitFilesShareDirectoryName = "internsrevitfiles";
        private static readonly IPriceService _priceService = new PriceService(new FileService());
        private static readonly ProductService _productService = new ProductService(_priceService, new MongoRepository<ProductMongoDto>(), new CacheService(), new SlackWebHook(), new HealthDashboardService(), new FileService());
        private static readonly List<string> _restrictedSybmols = new List<string> { "\u001e" };

        public static async Task<List<DynamicExcelProductModel>> GetProductsForDynamicExcelAsync(int manufacturerId, int categoryId, IUnitOfWork unitOfWork)
        {
            var revitProjectDataTypeId = unitOfWork.ProjectDataTypeRepository.GetAll().FirstOrDefault(a => a.Title == "Revit").Id;
            var products = unitOfWork.ProductRepository.GetAll().Where(a => a.ManufacturerId == manufacturerId || a.CategoryId == categoryId).ToList()
                .Select(a => new DynamicExcelProductModel
                {
                    Id = a.Id.ToString(),
                    Name = a.Name,
                    ManufacturerId = a.ManufacturerId.ToString(),
                    CategoryId = a.CategoryId.ToString(),
                    ProductCategoryIds = string.Join(";", a.ProductCategories.Select(b => b.CategoryId).ToList()),
                    ProductLineId = a.ProductLineId.HasValue ? a.ProductLineId.ToString() : string.Empty,
                    ProductUrl = a.ProductUrl,
                    ULUrl = a.ULUrl,
                    PhotoId = a.PhotoId.HasValue ? a.PhotoId.ToString() : string.Empty,
                    PhotoURLs = string.Join("|", a.ProductPhotos.Where(pp => pp.Photo != null && pp.Photo.UploadUrl != null).Select(pp => pp.Photo.UploadUrl).ToList()),
                    Description = a.Description,
                    VideoUrl = a.VideoUrl,
                    VanityURL = a.VanityURL,
                    MetaTitle = a.MetaTitle,
                    MetaDescription = a.MetaDescription,
                    MetaKeywords = a.MetaKeywords,
                    Keywords = a.Keywords,
                    Published = a.Published ? "1" : "0",
                    PublishedOnCustomMicrosite = a.PublishedOnCustomMicrosite ? "1" : "0",
                    Staging = a.Staging ? "1" : "0",
                    ExternalId = a.ExternalId,
                    PublishToPartner = a.PublishToPartner ? "1" : "0",
                    ForgeWallURL = a.ForgeWallURL,
                    ForgeFloorURL = a.ForgeFloorURL,
                    ForgeCeilingURL = a.ForgeCeilingURL,
                    ForgeRoofURL = a.ForgeRoofURL,
                    ProductCertificates = string.Join(";", a.ProductCertificates.Where(p => p.ExternalCertificateId != null).Select(p => p.ExternalCertificateId.Value).ToList()),
                    ProductCisfbs = string.Join(";", a.ProductCisfbs.Select(p => p.Cisfb.Code).ToList()),
                    ProductExternalMasterformatIds = a.ProductMasterformats.Select(x => x.ExternalMasterformatId).ToList(),
                    ProductOmniclasses = string.Join(";", a.ProductOmniclasses.Select(p => p.Omniclass.Code).ToList()),
                    ProductUniclasses = string.Join(";", a.ProductUniclasses.Select(p => p.Uniclass.Code).ToList()),
                    ProductUniformats = string.Join(";", a.ProductUniformats.Select(p => p.Uniformat.Code).ToList()),
                    ProductCutSheetUrls = string.Join("|", a.ProductFiles.Where(pf => pf.File != null && pf.File.SyncUrl != null && pf.File.SyncUrl != "" && pf.IsAttachment && pf.File.Title == "Product Cut Sheet").Select(pf => pf.File.SyncUrl).ToList()),
                    PartSpecUrls = string.Join("|", a.ProductFiles.Where(pf => pf.File != null && pf.File.SyncUrl != null && pf.File.SyncUrl != "" && pf.IsAttachment && pf.File.Title == "3-Part Specification").Select(pf => pf.File.SyncUrl).ToList()),
                    ProductBrochureUrls = string.Join("|", a.ProductFiles.Where(pf => pf.File != null && pf.File.SyncUrl != null && pf.File.SyncUrl != "" && pf.IsAttachment && pf.File.Title == "Product Brochure").Select(pf => pf.File.SyncUrl).ToList()),
                    ImageUrls = string.Join("|", a.ProductFiles.Where(pf => pf.File != null && pf.File.SyncUrl != null && pf.File.SyncUrl != "" && pf.IsAttachment && pf.File.Title == "Image").Select(pf => pf.File.SyncUrl).ToList()),
                    SubmittalUrls = string.Join("|", a.ProductFiles.Where(pf => pf.File != null && pf.File.SyncUrl != null && pf.File.SyncUrl != "" && pf.IsAttachment && pf.File.Title == "Submittal").Select(pf => pf.File.SyncUrl).ToList()),
                    CatalogUrls = string.Join("|", a.ProductFiles.Where(pf => pf.File != null && pf.File.SyncUrl != null && pf.File.SyncUrl != "" && pf.IsAttachment && pf.File.Title == "Catalog").Select(pf => pf.File.SyncUrl).ToList()),
                    WarrantyUrls = string.Join("|", a.ProductFiles.Where(pf => pf.File != null && pf.File.SyncUrl != null && pf.File.SyncUrl != "" && pf.IsAttachment && pf.File.Title == "Warranty").Select(pf => pf.File.SyncUrl).ToList()),
                    TestingDataUrls = string.Join("|", a.ProductFiles.Where(pf => pf.File != null && pf.File.SyncUrl != null && pf.File.SyncUrl != "" && pf.IsAttachment && pf.File.Title == "Testing Data").Select(pf => pf.File.SyncUrl).ToList()),
                    SafetyDataSheetUrls = string.Join("|", a.ProductFiles.Where(pf => pf.File != null && pf.File.SyncUrl != null && pf.File.SyncUrl != "" && pf.IsAttachment && pf.File.Title == "Safety Data Sheet").Select(pf => pf.File.SyncUrl).ToList()),
                    InstallationGuideUrls = string.Join("|", a.ProductFiles.Where(pf => pf.File != null && pf.File.SyncUrl != null && pf.File.SyncUrl != "" && pf.IsAttachment && pf.File.Title == "Installation Guide").Select(pf => pf.File.SyncUrl).ToList()),
                    HpdUrls = string.Join("|", a.ProductFiles.Where(pf => pf.File != null && pf.File.SyncUrl != null && pf.File.SyncUrl != "" && pf.IsAttachment && pf.File.Title == "HPD").Select(pf => pf.File.SyncUrl).ToList()),
                    MasterPartSpecUrls = string.Join("|", a.ProductFiles.Where(pf => pf.File != null && pf.File.SyncUrl != null && pf.File.SyncUrl != "" && pf.IsAttachment && pf.File.Title == AttachmentConstants.MasterspecFileTitle).Select(pf => pf.File.SyncUrl).ToList()),
                    RevitFiles = string.Join("|", a.ProductFiles.Where(pf => !pf.IsAttachment
                            && pf.ProjectDataTypeId == revitProjectDataTypeId
                            && string.IsNullOrWhiteSpace(pf.File.SyncUrl))
                        .Select(x => x.File.Title).Distinct().ToList()),
                    Weight = a.Weight.ToString(),
                    RegionIds = a.RegionIds,
                    EpdUrls = string.Join("|", a.ProductFiles.Where(pf => pf.File != null && pf.File.SyncUrl != null && pf.File.SyncUrl != "" && pf.IsAttachment && pf.File.Title == "EPD").Select(pf => pf.File.SyncUrl).ToList()),
                    AttachmentFileNames = string.Join(";", a.ProductFiles.Where(x => x.IsAttachment && string.IsNullOrWhiteSpace(x.File.SyncUrl)).Select(x => x.File.FileName)),
                    ProjectFileNames = string.Join(";", a.ProductFiles.Where(x => !x.IsAttachment && string.IsNullOrWhiteSpace(x.File.SyncUrl)).Select(x => x.File.FileName)),
                    ProjectFiles = a.ProductFiles.Where(pf =>
                            pf.File != null &&
                            pf.File.SyncUrl != null &&
                            pf.File.SyncUrl != "" &&
                            !pf.IsAttachment &&
                            pf.ProjectDataTypeId != null &&
                            !pf.File.SyncUrl.Contains(RevitFilesShareDirectoryName) &&
                            !pf.File.SyncUrl.Contains(InternsRevitFilesShareDirectoryName))
                        .Select(p => new ProductFileModel
                        {
                            CustomFileId = p.CustomFileId,
                            FileSyncUrl = p.File.SyncUrl,
                            FileTitle = p.File.Title,
                            FileType = p.SoftwareVersionId.HasValue ? new ProjectDataTypeModel
                            {
                                Title = p.SoftwareVersion.Title
                            }
                            :
                            new ProjectDataTypeModel
                            {
                                Title = p.ProjectDataType.Title
                            }
                        }).ToList(),
                    ProductQualityItems = a.ProductQualityItems.Select(p => p.QualityItem.Name).ToList(),
                    ProductStats = a.ProductStats.Select(p => new ProductProductStatModel
                    {
                        KeyStatName = p.KeyStat.Name,
                        KeyStatType = p.KeyStatType,
                        Note = p.Note,
                        KeyStatUnit = p.KeyStatUnit == null ? null : new ProductStatKeyStatUnitModel
                        {
                            GroupName = p.KeyStatUnit.GroupName,
                            BUnitName = p.KeyStatUnit.BUnitName
                        },
                        Value = p.Value,
                        MinRangeValue = p.MinRangeValue,
                        MaxRangeValue = p.MaxRangeValue,
                        KeyStatValueList = p.KeyStatValueList.Select(ksvl => new ProductStatKeyStatValueListModel
                        {
                            Value = ksvl.Value,
                            MinRangeValue = ksvl.MinRangeValue,
                            MaxRangeValue = ksvl.MaxRangeValue,
                            Note = ksvl.Note,
                            KeyStatUnit = ksvl.KeyStatUnit == null ? null : new ProductStatKeyStatUnitModel
                            {
                                BUnitName = ksvl.KeyStatUnit.BUnitName,
                                GroupName = ksvl.KeyStatUnit.GroupName
                            }
                        }).ToList()
                    }).Where(ps => ps.KeyStatType != KeyStatType.None).ToList()
                })
                .OrderBy(x => x.Id)
                .ToList();

            var allMasterformats = await _masterformatService.GetBackofficeMasterformatsAsync(products.SelectMany(x => x.ProductExternalMasterformatIds).ToList());
            if (allMasterformats.Any())
            {
                foreach (var product in products)
                {
                    if (product.ProductExternalMasterformatIds.Any())
                    {
                        product.ProductExternalMasterformats = string.Join(";", allMasterformats.Where(x => product.ProductExternalMasterformatIds.Contains(x.Id)).Select(x => x.Code).ToList());
                    }
                }
            }

            return products;
        }

        public static string GetDynamicExcel(DynamicExcelProductsModel productsModel, DynamicExportExcelModel exportModel, string tempFolder)
        {
            exportModel.StaticFields.Insert(0, DynamicExcelConstants.IdCaption);
            var dynamicExcelMapping = GetDynamicExcelMapping();

            if (!Directory.Exists(tempFolder))
            {
                Directory.CreateDirectory(tempFolder);
            }

            var filePath = Path.Combine(tempFolder, $"dynamicExcel_{Guid.NewGuid()}.xlsx");

            SpreadsheetDocument spreadsheetDocument;
            WorkbookPart workbookpart;
            WorksheetPart worksheetPart;

            CreateSpreadsheet(filePath, out spreadsheetDocument, out workbookpart, out worksheetPart);
            var distinctProjectTypes = new List<ProjectDataTypeModel>();
            using (var unitOfWork = UnitOfWork.Create())
            {
                distinctProjectTypes = unitOfWork.ProjectDataTypeRepository.GetAll().Select(x => new ProjectDataTypeModel { Title = x.Title }).OrderBy(x => x.Title).ToList();
            }

            var distinctProductStats = exportModel.IncludeProductStats ?
                productsModel.Products.SelectMany(p => p.ProductStats).Distinct(new ProductStatModelComparer())
                .OrderBy(a => a.KeyStatType)
                .ThenBy(a => a.KeyStatUnit?.GroupName).ThenBy(a => a.KeyStatUnit?.BUnitName)
                .ThenBy(a => a.KeyStatValueList, new ProductStatKeyStatValueListModelComparer()).ThenBy(a => a.KeyStatValueList, new ProductStatKeyStatValueListModelComparer())
                .ThenBy(a => a.KeyStatName)
                .ToList()
                : null;

            uint headerColumnIndex = CreateDynamicExcelHeaderColumns(productsModel, exportModel, workbookpart, worksheetPart, distinctProjectTypes, distinctProductStats);

            //Sheets
            SheetData sheetData = CreateDynamicExcelSheet(productsModel.FileName, spreadsheetDocument, workbookpart, worksheetPart);

            int rowIndex = 1;
            if (exportModel.IncludeProductStats && productsModel.Products.SelectMany(x => x.ProductStats).Any())
            {
                CreateDynamicExcelAdditionalColumnsForKeystats(exportModel, headerColumnIndex, distinctProductStats, sheetData, rowIndex);
                rowIndex = 4;
            }

            FillDynamicExcelHeaderColumns(productsModel, exportModel, dynamicExcelMapping, distinctProjectTypes, distinctProductStats, sheetData, rowIndex);

            rowIndex++;
            foreach (var product in productsModel.Products)
            {
                FillDynamicExcelProductData(productsModel, exportModel, dynamicExcelMapping, distinctProjectTypes, distinctProductStats, sheetData, rowIndex, product);
                rowIndex++;
            }

            workbookpart.Workbook.Save();

            spreadsheetDocument.Dispose();

            return filePath;
        }

        public static async Task<List<string>> ImportDynamicExcelAsync(string filePath, int manufacturerId, int categoryId, string userId, string hostUrl)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                unitOfWork.BeginTransaction();
                var manufacturer = await unitOfWork.ManufacturerRepository.GetByIdAsync(manufacturerId);
                var productLineIdsByManufacturer = unitOfWork.ProductLineRepository.GetAll()
                    .Where(a => a.ManufacturerId != null && a.ManufacturerId.Value == manufacturerId)
                    .Select(a => new { a.Id, a.ManufacturerId })
                    .AsEnumerable()
                    .GroupBy(a => a.ManufacturerId.Value)
                    .ToDictionary(a => a.Key, a => a.Select(b => b.Id));
                var revitFileIdsQueue = new List<string>();
                var allQualityItems = await unitOfWork.QualityItemRepository.GetAll().Select(a => new { Id = a.Id, Name = a.Name.Trim() }).ToListAsync();
                var allKeyStats = await unitOfWork.KeyStatRepository.GetAll().Select(a => new { Id = a.Id, Name = a.Name.Trim() }).ToListAsync();
                var allKeyStatUnits = await unitOfWork.KeyStatUnitRepository.GetAll().Select(a => new { Id = a.Id, GroupName = a.GroupName.Trim(), BUnitName = a.BUnitName.Trim() }).ToListAsync();
                var allProjectDataTypes = await unitOfWork.ProjectDataTypeRepository.GetAll().ToListAsync();

                // if excelErrors.Count != 0 - excel has invalid structure
                var excelErrors = new List<string>();
                var qualityItems = new List<ProductQualityItemsModel>();
                var productStats = new List<ProductProductStatModel>();
                var projectTypes = new List<ProjectDataTypeModel>();

                var spreadsheetDocument = SpreadsheetDocument.Open(filePath, false);
                var workbookpart = spreadsheetDocument.WorkbookPart;
                var workbook = workbookpart.Workbook;
                var sheets = workbook.Descendants<Sheet>();
                var worksheetPart = (WorksheetPart)workbookpart.GetPartById(sheets.First().Id);
                var sharedStringPart = workbookpart.SharedStringTablePart;
                var rows = worksheetPart.Worksheet.Descendants<Row>().ToList();
                var projectFilesInImport = false;
                var qualityItemsInImport = false;
                var keyStatsInImport = false;

                // Prepare headers
                var rowHeader1 = rows.FirstOrDefault();
                var headerCells1 = rowHeader1.Elements<Cell>().ToList();
                var headerRowIndex = 1;

                for (int i = 0; i < headerCells1.Count; i++)
                {
                    var cellReference = $"{CommonExcelProvider.ColumnIndexToColumnLetter(i + 1)}{headerRowIndex}";
                    var cellValue1 = CommonExcelProvider.GetCellStringValue(headerCells1.SingleOrDefault(c => c.CellReference.Value == cellReference), sharedStringPart);
                    if (!string.IsNullOrWhiteSpace(cellValue1))
                    {
                        if (Enum.TryParse(cellValue1, out KeyStatType keyStatType))
                        {
                            keyStatsInImport = true;
                            break;
                        }
                    }
                }

                var headerRowsToSkip = 0;
                if (keyStatsInImport) headerRowsToSkip = 3;
                var headerRow = rows.Skip(headerRowsToSkip).FirstOrDefault();
                var headerRowCells = headerRow.Elements<Cell>().ToList();
                var headerCells = new List<string>();
                for (var i = 0; i < headerRowCells.Count; i++)
                {
                    var cellReference = $"{CommonExcelProvider.ColumnIndexToColumnLetter(i + 1)}{headerRowsToSkip + 1}";
                    var cellHeader = CommonExcelProvider.GetCellStringValue(headerRowCells.SingleOrDefault(c => c.CellReference.Value == cellReference), sharedStringPart);
                    headerCells.Add(cellHeader);
                }

                if (!headerCells.Any())
                {
                    excelErrors.Add("There is no data to import");
                }
                if (!headerCells.Contains(DynamicExcelConstants.IdCaption))
                {
                    excelErrors.Add("Spreadsheed does not contains Id field, import could not be finished");
                }
                else
                {
                    ICollection<DynamicExcelMappingModel> mappings = GetDynamicExcelMapping();

                    var staticFieldsCount = 0;
                    var dynamicFieldsCount = 0;
                    foreach (var headerCell in headerCells)
                    {
                        if (mappings.Any(x => x.Caption.ToUpper() == headerCell.ToUpper()))
                            staticFieldsCount++;
                    }

                    dynamicFieldsCount = headerCells.Count - staticFieldsCount;
                    var productStatsStartIndex = headerCells.Count;
                    if (keyStatsInImport)
                    {
                        var rowHeader2 = rows.Skip(1).FirstOrDefault();
                        var rowHeader3 = rows.Skip(2).FirstOrDefault();
                        var rowHeader4 = rows.Skip(3).FirstOrDefault();
                        var headerCells2 = rowHeader2.Elements<Cell>().ToList();
                        var headerCells3 = rowHeader3.Elements<Cell>().ToList();
                        var headerCells4 = rowHeader4.Elements<Cell>().ToList();
                        productStatsStartIndex = ParseDocumentKeyStats(ref excelErrors, ref productStats, sharedStringPart, headerCells1, headerCells2, headerCells3, headerCells4, 1);
                    }

                    // return errors if structure of pdf is invalid or problems with parse key stats
                    if (excelErrors.Any())
                    {
                        return excelErrors;
                    }

                    int startProjectTypeIndex = staticFieldsCount;
                    int startQualityItemsIndex = staticFieldsCount;

                    for (int i = startProjectTypeIndex; i < productStatsStartIndex; i++)
                    {
                        var cellReference = $"{CommonExcelProvider.ColumnIndexToColumnLetter(i + 1)}{headerRowsToSkip + 1}";
                        var headerCellValue = CommonExcelProvider.GetCellStringValue(headerRowCells.SingleOrDefault(c => c.CellReference.Value == cellReference), sharedStringPart);
                        if (allQualityItems.Select(a => a.Name).Distinct().ToList().Contains(headerCellValue))
                        {
                            startQualityItemsIndex = i;
                            qualityItemsInImport = true;
                            break;
                        }
                        else
                        {
                            if (!string.IsNullOrWhiteSpace(headerCellValue))
                            {
                                projectTypes.Add(new ProjectDataTypeModel { Title = headerCellValue, CellRefNumber = i + 1 });
                                projectFilesInImport = true;
                            }
                        }
                    }

                    startQualityItemsIndex = staticFieldsCount + projectTypes.Count;

                    for (int i = startQualityItemsIndex; i < productStatsStartIndex; i++)
                    {
                        var cellReference = $"{CommonExcelProvider.ColumnIndexToColumnLetter(i + 1)}{headerRowsToSkip + 1}";
                        var cellValue = CommonExcelProvider.GetCellStringValue(headerRowCells.SingleOrDefault(c => c.CellReference.Value == cellReference), sharedStringPart);
                        qualityItems.Add(new ProductQualityItemsModel { Name = cellValue, CellRefNumber = i + 1 });
                    }

                    keyStatsInImport = dynamicFieldsCount > 0 && productStatsStartIndex != headerCells.Count;

                    var productIdIndex = headerCells.IndexOf(DynamicExcelConstants.IdCaption);

                    var handleImportModel = new DynamicExcelHandleImportModel();
                    handleImportModel.Errors = excelErrors;
                    handleImportModel.Manufacturer = manufacturer;
                    handleImportModel.RevitFileIdsQueue = revitFileIdsQueue;
                    handleImportModel.UserId = userId;
                    handleImportModel.UnitOfWork = unitOfWork;
                    handleImportModel.Masterformats = await _masterformatService.GetBackofficeMasterformatsAsync();

                    var rowsToSkip = headerRowsToSkip + 1;
                    var rowIndex = rowsToSkip + 1;

                    List<int> productIds = new List<int>();

                    // Parse product
                    foreach (var row in rows.Skip(rowsToSkip))
                    {
                        // productId/list errors
                        var productErrors = new Dictionary<int, List<string>>();
                        var cells = row.Elements<Cell>().ToList();
                        var productIdCellReference = $"{CommonExcelProvider.ColumnIndexToColumnLetter(productIdIndex + 1)}{rowIndex}";
                        int productId = CommonExcelProvider.GetCellIntValue(cells.SingleOrDefault(c => c.CellReference.Value == productIdCellReference), sharedStringPart, excelErrors);
                        Product product = await unitOfWork.ProductRepository.GetByIdAsync(productId);

                        if (product == null)
                            excelErrors.Add($"Product Id {productId}. Can`t find this product");
                        else
                        {
                            var cellIndex = 1;
                            //static fields
                            foreach (string headerCell in headerCells)
                            {
                                try
                                {
                                    DynamicExcelMappingModel mapping = mappings.FirstOrDefault(x => x.Caption.ToUpper() == headerCell.ToUpper());
                                    if (mapping != null)
                                    {
                                        string cellReference = $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}";
                                        string cellValue = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == cellReference), sharedStringPart);

                                        if (mapping.FieldName.ToLower() == DynamicExcelConstants.ProductLineIdCaption.ToLower()
                                            && !string.IsNullOrWhiteSpace(cellValue))
                                        {
                                            int productLineId;
                                            if (int.TryParse(cellValue, out productLineId) && !productLineIdsByManufacturer[manufacturerId].Contains(productLineId))
                                            {
                                                productErrors.AddProductError($"Product Id {productId}. Can't find product line id '{productLineId}' for manufacturer", productId);
                                                continue;
                                            }
                                        }

                                        mapping.HandleFieldImportFunction?.Invoke(handleImportModel, product, cellValue, hostUrl);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    productErrors.AddProductError($"Product Id {productId}. {headerCell} - {ex.Message}", productId);
                                }
                                cellIndex++;
                            }
                            //dynamic fields
                            if (projectFilesInImport)
                            {
                                try
                                {
                                    var importedProjectFiles = ParseProductProjectFiles(projectTypes, sharedStringPart, startProjectTypeIndex, startQualityItemsIndex, cells, rowIndex);
                                    HandleProjectFilesImport(ref product, ref productErrors, importedProjectFiles, allProjectDataTypes, userId, hostUrl);
                                    if (product.Price != null)
                                        await _priceService.MoveProductFilesBetweenPaidAndFreeContainerAsync(productId, product.Price.PriceType, product.Price.PriceType, unitOfWork, true);
                                }
                                catch (Exception ex)
                                {
                                    productErrors.AddProductError($"Product Id {productId}. Error while importing project files - {ex.Message}", productId);
                                }
                            }
                            if (qualityItemsInImport)
                            {
                                try
                                {
                                    var importedProductQualityItemNames = ParseProductQualityItemNames(excelErrors, qualityItems, sharedStringPart, productStatsStartIndex, startQualityItemsIndex, cells, rowIndex);
                                    HandleProductQualityItemsImport(ref product, userId, importedProductQualityItemNames, allQualityItems);
                                }
                                catch (Exception ex)
                                {
                                    productErrors.AddProductError($"Product Id {productId}. Error while importing quality items - {ex.Message}", productId);
                                }
                            }
                            if (keyStatsInImport)
                            {
                                try
                                {
                                    var keyStatsOrders = DeleteOldKeyStats(unitOfWork, product);
                                    var importedProductStats = ParseProductKeyStats(productStats, sharedStringPart, productStatsStartIndex, cells, rowIndex);
                                    HandleKeyStatsImport(ref product, unitOfWork, userId, importedProductStats, keyStatsOrders, allKeyStats, allKeyStatUnits);
                                }
                                catch (Exception ex)
                                {
                                    productErrors.AddProductError($"Product Id {productId}. Error while importing key stats - {ex.Message}", productId);
                                }

                                product.IsImperialDefault = product.ProductStats != null &&
                                            product.ProductStats.Any(a => (a.KeyStatUnit != null && (a.KeyStatUnit.BUnitName.ToLowerInvariant() == "in" || a.KeyStatUnit.BUnitName.ToLowerInvariant() == "ft")) ||
                                            (a.KeyStatValueList != null && (a.KeyStatValueList.Any(b => b.KeyStatUnit != null && b.KeyStatUnit.BUnitName.ToLowerInvariant() == "in") ||
                                                                            a.KeyStatValueList.Any(b => b.KeyStatUnit != null && b.KeyStatUnit.BUnitName.ToLowerInvariant() == "ft"))));
                            }

                            if (product.Price == null)
                                await _priceService.SetDefaultPriceToProductAsync(product.Id, unitOfWork);

                            try
                            {
                                HandleProductMetaInfo(unitOfWork, manufacturer, product);
                            }
                            catch (Exception ex)
                            {
                                productErrors.AddProductError($"Product Id {productId}. Error while handling meta info - {ex.Message}", productId);
                            }

                            // TODO: add to global dict
                            List<string> urlsValidationErrors = await ValidateProductUrlsAsync(product, headerCells);
                            foreach (string error in urlsValidationErrors)
                                productErrors.AddProductError(error, product.Id);

                            if (!productErrors.TryGetValue(product.Id, out _))
                            {
                                product.ModifiedById = userId;
                                product.ModifiedDate = DateTime.UtcNow;

                                unitOfWork.ProductRepository.Edit(product);
                                unitOfWork.Save();
                            }
                        }
                        rowIndex++;

                        // Add error message from dictinary
                        foreach (KeyValuePair<int, List<string>> prodErrors in productErrors)
                        {
                            foreach (string error in prodErrors.Value)
                            {
                                excelErrors.Add(error);
                            }
                        }
                        productIds.Add(productId);
                    }

                    try
                    {
                        unitOfWork.CommitTransaction();
                        foreach (int productId in productIds)
                            await _productService.SaveProductToMongoAsync(productId, true, unitOfWork);

                        var azureBlobProvider = new AzureStorageService(
                            ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                            ConfigurationHelper.GetValue("Environment"),
                            bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                            false);
                        var revitsQueue = azureBlobProvider.GetQueueByName(AzureStorageConstants.ProductRevitsQueue);

                        foreach (var fileIdString in revitFileIdsQueue)
                        {
                            revitsQueue.SendMessage(fileIdString);
                        }
                        CacheHelper.ClearSpecificCache("*/api/Product/List*");
                        CacheHelper.ClearSpecificCache("*/api/Product/AdminList*");
                    }
                    catch (Exception ex)
                    {
                        unitOfWork.RollbackTransaction();
                        excelErrors.Add($"Transaction was failed. {ex.Message}");
                    }
                }

                spreadsheetDocument.Dispose();

                return excelErrors;
            }
        }

        private static int ParseDocumentKeyStats(
            ref List<string> errors,
            ref List<ProductProductStatModel> productStats,
            SharedStringTablePart sharedStringPart,
            List<Cell> headerCells1,
            List<Cell> headerCells2,
            List<Cell> headerCells3,
            List<Cell> headerCells4,
            int startRowIndex)
        {
            int productStatsStartIndex = 0;
            for (int i = 0; i < headerCells1.Count; i++)
            {
                var cellValue1Reference = $"{CommonExcelProvider.ColumnIndexToColumnLetter(i + 1)}{startRowIndex}";
                var cellValue1 = CommonExcelProvider.GetCellStringValue(headerCells1.SingleOrDefault(c => c.CellReference.Value == cellValue1Reference), sharedStringPart);
                if (!string.IsNullOrWhiteSpace(cellValue1))
                {
                    KeyStatType keyStatType;
                    if (Enum.TryParse(cellValue1, out keyStatType))
                    {
                        var cellValue4 = CommonExcelProvider.GetCellStringValue(headerCells4[i], sharedStringPart);
                        if (!string.IsNullOrWhiteSpace(cellValue4))
                        {
                            var productStat = new ProductProductStatModel
                            {
                                KeyStatType = keyStatType,
                                KeyStatName = cellValue4,
                                CellRefNumber = i + 1
                            };

                            var cellValue2 = CommonExcelProvider.GetCellStringValue(headerCells2[i], sharedStringPart); // get by cellRef if is needed
                            var cellValue3 = CommonExcelProvider.GetCellStringValue(headerCells3[i], sharedStringPart);

                            if (productStat.KeyStatType == KeyStatType.SingleNumeric || productStat.KeyStatType == KeyStatType.NumericRange)
                            {
                                if (!string.IsNullOrWhiteSpace(cellValue2) || !string.IsNullOrWhiteSpace(cellValue3))
                                {
                                    productStat.KeyStatUnit = new ProductStatKeyStatUnitModel
                                    {
                                        GroupName = cellValue2,
                                        BUnitName = cellValue3
                                    };
                                }
                            }

                            productStats.Add(productStat);
                        }
                        else
                        {
                            errors.Add($"Empty KeyStatName cell: '{headerCells4[i].CellReference}'");
                        }
                    }
                    else
                    {
                        errors.Add($"Invalid KeyStatType in cell: '{headerCells1[i].CellReference}'");
                    }
                }
                if (productStats.Count == 1)
                {
                    productStatsStartIndex = i;
                }
            }

            if (productStatsStartIndex == 0)
            {
                productStatsStartIndex = headerCells1.Count;
            }

            return productStatsStartIndex;
        }

        public static ICollection<DynamicExcelMappingModel> GetDynamicExcelMapping()
        {
            return _dynamicExcelMappingModels;
        }

        public static dynamic GetDynamicExcelColumnList()
        {
            var columnList = _dynamicExcelMappingModels.Where(x => x.Caption.ToUpper() != DynamicExcelConstants.IdCaption.ToUpper())
                .Select(x => new
                {
                    Key = x.FieldName,
                    Value = x.Caption
                }).ToList();
            return columnList;
        }

        public static List<string> GetDynamicExcelColumnKeysList()
        {
            var keysList = _dynamicExcelMappingModels.Where(x => x.Caption.ToUpper() != DynamicExcelConstants.IdCaption.ToUpper())
                .Select(x => x.FieldName)
                .ToList();
            return keysList;
        }


        public static string GetFilePath(string baseFolder, int fileId)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var file = unitOfWork.FileRepository.GetById(fileId);
                return Path.Combine(baseFolder, file.Id.ToString() + Path.GetExtension(file.FileName));
            }
        }

        private static void FillDynamicExcelProductData(DynamicExcelProductsModel productsModel, DynamicExportExcelModel exportModel, ICollection<DynamicExcelMappingModel> dynamicExcelMapping, List<ProjectDataTypeModel> distinctProjectTypes, List<ProductProductStatModel> distinctProductStats, SheetData sheetData, int rowIndex, DynamicExcelProductModel product)
        {
            Row row = new Row();
            sheetData.AppendChild(row);

            var columnIndex = 1;

            foreach (var field in exportModel.StaticFields)
            {
                var mappedField = dynamicExcelMapping.FirstOrDefault(x => x.FieldName.ToLower() == field.ToLower());
                if (mappedField != null)
                {
                    var productFieldValue = product.GetType().GetProperty(mappedField.FieldName).GetValue(product)?.ToString();
                    if (!string.IsNullOrWhiteSpace(productFieldValue))
                    {
                        foreach (string restrictedSybmol in _restrictedSybmols)
                        {
                            if (productFieldValue.Contains(restrictedSybmol))
                                productFieldValue = productFieldValue.Replace(restrictedSybmol, string.Empty);
                        }
                    }
                    row.AppendChild(CommonExcelProvider.ConstructCell(productFieldValue, mappedField.DataType, columnIndex++, rowIndex));
                }
            }

            if (exportModel.IncludeProjectTypes && distinctProjectTypes.Any())
            {
                columnIndex = FillDynamicExcelProjectTypesData(distinctProjectTypes, rowIndex, product, row, columnIndex);
            }

            if (exportModel.IncludeQualityItems && productsModel.QualityItems.Any())
            {
                columnIndex = FillDynamicExcelQualityItemsData(productsModel, rowIndex, product, row, columnIndex);
            }

            if (exportModel.IncludeProductStats && productsModel.Products.SelectMany(x => x.ProductStats).Any())
            {
                columnIndex = FillDynamicExcelProductStatsData(distinctProductStats, rowIndex, product, row, columnIndex);
            }
        }

        private static int FillDynamicExcelProductStatsData(List<ProductProductStatModel> distinctProductStats, int rowIndex, DynamicExcelProductModel product, Row row, int columnIndex)
        {
            foreach (var productStat in distinctProductStats)
            {
                var productProductStat = product.ProductStats.FirstOrDefault(aps =>
                    aps.KeyStatType == productStat.KeyStatType &&
                    aps.KeyStatName == productStat.KeyStatName &&
                    (
                     (
                      !productStat.KeyStatValueList.Any() && !aps.KeyStatValueList.Any() &&
                      (
                       (productStat.KeyStatUnit == null && aps.KeyStatUnit == null) ||
                       (productStat.KeyStatUnit != null && aps.KeyStatUnit != null &&
                       aps.KeyStatUnit.GroupName == productStat.KeyStatUnit.GroupName &&
                       aps.KeyStatUnit.BUnitName == productStat.KeyStatUnit.BUnitName)
                      )
                     )
                     ||
                     (
                        productStat.KeyStatValueList.Any() && aps.KeyStatValueList.Any() &&
                         ((productStat.KeyStatValueList.First().KeyStatUnit == null && aps.KeyStatValueList.First().KeyStatUnit == null) ||
                         (productStat.KeyStatValueList.First().KeyStatUnit != null && aps.KeyStatValueList.First().KeyStatUnit != null &&
                           aps.KeyStatValueList.First().KeyStatUnit.GroupName == productStat.KeyStatValueList.First().KeyStatUnit.GroupName &&
                           aps.KeyStatValueList.First().KeyStatUnit.BUnitName == productStat.KeyStatValueList.First().KeyStatUnit.BUnitName))
                     )
                    )
                   );

                if (productProductStat != null)
                {
                    switch (productProductStat.KeyStatType)
                    {
                        case KeyStatType.SingleNumeric:
                            row.AppendChild(CommonExcelProvider.ConstructCell(!string.IsNullOrWhiteSpace(productProductStat.Note) ? $"{productProductStat.Value}*{productProductStat.Note}" : productProductStat.Value, CellValues.String, columnIndex++, rowIndex));
                            break;
                        case KeyStatType.NumericRange:
                            var numericRange = !string.IsNullOrWhiteSpace(productProductStat.Note) ? $"{productProductStat.MinRangeValue ?? string.Empty}*{productProductStat.MaxRangeValue ?? string.Empty}*{productProductStat.Note}" : $"{productProductStat.MinRangeValue ?? string.Empty}*{productProductStat.MaxRangeValue ?? string.Empty}";
                            row.AppendChild(CommonExcelProvider.ConstructCell(numericRange, CellValues.String, columnIndex++, rowIndex));
                            break;
                        case KeyStatType.MultivalueNumeric:
                            var multivalueNumeric = string.Join(";", productProductStat.KeyStatValueList.Select(x => $"{x.KeyStatUnit?.GroupName ?? string.Empty}*{x.KeyStatUnit?.BUnitName ?? string.Empty}*{x.Value ?? string.Empty}*{x.MaxRangeValue ?? string.Empty}*{x.Note ?? string.Empty}"));
                            row.AppendChild(CommonExcelProvider.ConstructCell(multivalueNumeric, CellValues.String, columnIndex++, rowIndex));
                            break;
                        case KeyStatType.TextValue:
                            row.AppendChild(CommonExcelProvider.ConstructCell(productProductStat.Value, CellValues.String, columnIndex++, rowIndex));
                            break;
                        case KeyStatType.TextMultivalue:
                            var textMultivalue = string.Join(";", productProductStat.KeyStatValueList.Select(x => x.Value));
                            row.AppendChild(CommonExcelProvider.ConstructCell(textMultivalue, CellValues.String, columnIndex++, rowIndex));
                            break;
                        case KeyStatType.None:
                            row.AppendChild(CommonExcelProvider.ConstructCell(string.Empty, CellValues.String, columnIndex++, rowIndex));
                            break;
                        default:
                            throw new NotImplementedException("KeyStatType not found!");
                    }
                }
                else
                {
                    row.AppendChild(CommonExcelProvider.ConstructCell(string.Empty, CellValues.String, columnIndex++, rowIndex));
                }
            }

            return columnIndex;
        }

        private static int FillDynamicExcelQualityItemsData(DynamicExcelProductsModel productsModel, int rowIndex, DynamicExcelProductModel product, Row row, int columnIndex)
        {
            foreach (var qualityItem in productsModel.QualityItems)
            {
                int hasQuality = product.ProductQualityItems.Contains(qualityItem.Name) ? 1 : 0;
                row.AppendChild(CommonExcelProvider.ConstructCell(hasQuality.ToString(), CellValues.Number, columnIndex++, rowIndex));
            }

            return columnIndex;
        }

        private static int FillDynamicExcelProjectTypesData(List<ProjectDataTypeModel> distinctProjectTypes, int rowIndex, DynamicExcelProductModel product, Row row, int columnIndex)
        {
            foreach (var projectType in distinctProjectTypes)
            {
                var productProjectTypes = product.ProjectFiles.Where(p => p.FileType.Title == projectType.Title).ToList();
                if (productProjectTypes != null && productProjectTypes.Any())
                {
                    var fileUrls = productProjectTypes.Select(pf => pf.FileSyncUrl).ToList();
                    row.AppendChild(CommonExcelProvider.ConstructCell(string.Join("|", fileUrls), CellValues.String, columnIndex++, rowIndex));
                }
                else
                {
                    row.AppendChild(CommonExcelProvider.ConstructCell(string.Empty, CellValues.String, columnIndex++, rowIndex));
                }
            }

            return columnIndex;
        }

        private static void FillDynamicExcelHeaderColumns(DynamicExcelProductsModel productsModel, DynamicExportExcelModel exportModel, ICollection<DynamicExcelMappingModel> dynamicExcelMapping, List<ProjectDataTypeModel> distinctProjectTypes, List<ProductProductStatModel> distinctProductStats, SheetData sheetData, int rowIndex)
        {
            Row headerRow = new Row();
            headerRow.Height = 90;
            headerRow.CustomHeight = true;

            sheetData.AppendChild(headerRow);

            int columnIndex = 1;

            foreach (var field in exportModel.StaticFields)
            {
                var mappedField = dynamicExcelMapping.FirstOrDefault(x => x.FieldName.ToLower() == field.ToLower());
                if (mappedField != null)
                {
                    headerRow.AppendChild(CommonExcelProvider.ConstructCell(mappedField.Caption, CellValues.String, columnIndex++, rowIndex, mappedField.StyleIndex));
                }
            }

            if (exportModel.IncludeProjectTypes && distinctProjectTypes.Any())
            {
                foreach (var projectDataType in distinctProjectTypes)
                {
                    headerRow.AppendChild(CommonExcelProvider.ConstructCell(projectDataType.Title, CellValues.String, columnIndex++, rowIndex, 3));
                }
            }

            if (exportModel.IncludeQualityItems && productsModel.QualityItems.Any())
            {
                foreach (var qualityItem in productsModel.QualityItems)
                {
                    headerRow.AppendChild(CommonExcelProvider.ConstructCell(qualityItem.Name, CellValues.String, columnIndex++, rowIndex, 3));
                }
            }

            if (exportModel.IncludeProductStats && productsModel.Products.SelectMany(x => x.ProductStats).Any())
            {
                foreach (var productStat in distinctProductStats)
                {
                    headerRow.AppendChild(CommonExcelProvider.ConstructCell(productStat.KeyStatName, CellValues.String, columnIndex++, rowIndex, 2));
                }
            }
        }

        private static void CreateDynamicExcelAdditionalColumnsForKeystats(DynamicExportExcelModel exportModel, uint headerColumnIndex, List<ProductProductStatModel> distinctProductStats, SheetData sheetData, int rowIndex)
        {
            Row rowHeader1 = new Row();
            sheetData.AppendChild(rowHeader1);

            Row rowHeader2 = new Row();
            sheetData.AppendChild(rowHeader2);

            Row rowHeader3 = new Row();
            sheetData.AppendChild(rowHeader3);

            for (int ci = 1; ci < headerColumnIndex - distinctProductStats.Count; ci++)
            {
                rowHeader1.AppendChild(CommonExcelProvider.ConstructCell(string.Empty, CellValues.String, ci, rowIndex, 2));
                rowHeader2.AppendChild(CommonExcelProvider.ConstructCell(string.Empty, CellValues.String, ci, rowIndex + 1, 2));
                rowHeader3.AppendChild(CommonExcelProvider.ConstructCell(string.Empty, CellValues.String, ci, rowIndex + 2, 2));
            }

            int statColumnIndex = (int)headerColumnIndex - distinctProductStats.Count;

            foreach (var productStat in distinctProductStats)
            {
                rowHeader1.AppendChild(CommonExcelProvider.ConstructCell(productStat.KeyStatType.ToString(), CellValues.String, statColumnIndex, rowIndex, 2));
                if (productStat.KeyStatType == KeyStatType.SingleNumeric || productStat.KeyStatType == KeyStatType.NumericRange)
                {
                    ProductStatKeyStatUnitModel keyStatUnit = new ProductStatKeyStatUnitModel
                    {
                        GroupName = string.Empty,
                        BUnitName = string.Empty
                    };
                    if (productStat.KeyStatUnit != null)
                    {
                        keyStatUnit = productStat.KeyStatUnit;
                    }
                    else if (productStat.KeyStatValueList != null && productStat.KeyStatValueList.Any())
                    {
                        var valueListFirst = productStat.KeyStatValueList.FirstOrDefault();
                        if (valueListFirst != null && valueListFirst.KeyStatUnit != null)
                        {
                            keyStatUnit = valueListFirst.KeyStatUnit;
                        }
                    }
                    rowHeader2.AppendChild(CommonExcelProvider.ConstructCell(keyStatUnit.GroupName, CellValues.String, statColumnIndex, rowIndex + 1, 2));
                    rowHeader3.AppendChild(CommonExcelProvider.ConstructCell(keyStatUnit.BUnitName, CellValues.String, statColumnIndex, rowIndex + 2, 2));
                }
                else
                {
                    rowHeader2.AppendChild(CommonExcelProvider.ConstructCell(string.Empty, CellValues.String, statColumnIndex, rowIndex + 1, 2));
                    rowHeader3.AppendChild(CommonExcelProvider.ConstructCell(string.Empty, CellValues.String, statColumnIndex, rowIndex + 2, 2));
                }

                statColumnIndex++;
            }
        }

        private static SheetData CreateDynamicExcelSheet(string fileName, SpreadsheetDocument spreadsheetDocument, WorkbookPart workbookpart, WorksheetPart worksheetPart)
        {
            Sheets sheets = workbookpart.Workbook.AppendChild(new Sheets());

            //Append a new worksheet and associate it with the workbook.
            Sheet sheet = new Sheet()
            {
                Id = spreadsheetDocument.WorkbookPart.
                GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = fileName.Length > 30 ? fileName.Substring(0, 30) : fileName
            };
            sheets.Append(sheet);
            workbookpart.Workbook.Save();

            // Get the sheetData cell table.
            SheetData sheetData = worksheetPart.Worksheet.AppendChild(new SheetData());
            return sheetData;
        }

        private static uint CreateDynamicExcelHeaderColumns(DynamicExcelProductsModel productsModel, DynamicExportExcelModel exportModel, WorkbookPart workbookpart, WorksheetPart worksheetPart, List<ProjectDataTypeModel> distinctProjectTypes, List<ProductProductStatModel> distinctProductStats)
        {
            Columns columns = new Columns();
            var columnWidth = 15;
            uint headerColumnIndex = 1;
            for (headerColumnIndex = 1; headerColumnIndex < exportModel.StaticFields.Count + 1; headerColumnIndex++)
            {
                columns.AppendChild(new Column { Min = headerColumnIndex, Max = headerColumnIndex, Width = columnWidth, CustomWidth = true });
            }

            if (exportModel.IncludeProjectTypes && distinctProjectTypes.Any())
            {
                foreach (var projType in distinctProjectTypes)
                {
                    columns.AppendChild(new Column { Min = headerColumnIndex, Max = headerColumnIndex, Width = columnWidth, CustomWidth = true });
                    headerColumnIndex++;
                }
            }

            if (exportModel.IncludeQualityItems && productsModel.QualityItems.Any())
            {
                foreach (var qualityItem in productsModel.QualityItems)
                {
                    columns.AppendChild(new Column { Min = headerColumnIndex, Max = headerColumnIndex, Width = columnWidth, CustomWidth = true });
                    headerColumnIndex++;
                }
            }

            if (exportModel.IncludeProductStats && productsModel.Products.SelectMany(x => x.ProductStats).Any())
            {
                foreach (var productStat in distinctProductStats)
                {
                    columns.AppendChild(new Column { Min = headerColumnIndex, Max = headerColumnIndex, Width = columnWidth, CustomWidth = true });
                    headerColumnIndex++;
                }
            }

            worksheetPart.Worksheet.AppendChild(columns);
            workbookpart.Workbook.Save();

            return headerColumnIndex;
        }

        private static void CreateSpreadsheet(string filePath, out SpreadsheetDocument spreadsheetDocument, out WorkbookPart workbookpart, out WorksheetPart worksheetPart)
        {
            // Create a spreadsheet document by supplying the filepath.
            // By default, AutoSave = true, Editable = true, and Type = xlsx.
            spreadsheetDocument = SpreadsheetDocument.Create(filePath, SpreadsheetDocumentType.Workbook);

            // Add a WorkbookPart to the document.
            workbookpart = spreadsheetDocument.AddWorkbookPart();
            workbookpart.Workbook = new Workbook();

            // Add a WorksheetPart to the WorkbookPart.
            worksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            worksheetPart.Worksheet = new Worksheet();


            Fonts fonts = new Fonts(
                new DocumentFormat.OpenXml.Spreadsheet.Font( // Index 0 - default
                    new FontSize() { Val = 10 }
                ));

            Fills fills = new Fills(
                    new Fill(new PatternFill() { PatternType = PatternValues.None }), // Index 0 - default
                    new Fill(new PatternFill(new ForegroundColor { Rgb = new HexBinaryValue() { Value = "FFE7E6E6" } }) { PatternType = PatternValues.Solid }), // Index 1 - default
                    new Fill(new PatternFill(new ForegroundColor { Rgb = new HexBinaryValue() { Value = "FFBDD7EE" } }) { PatternType = PatternValues.Solid }) // Index 2 - header
                );

            Borders borders = new Borders(
                    new Border() // index 0 default
                );

            CellFormats cellFormats = new CellFormats(
                    new CellFormat(), // default
                    new CellFormat { FontId = 0, FillId = 1, BorderId = 0, ApplyFill = true }, // body
                    new CellFormat { FontId = 0, FillId = 2, BorderId = 0, ApplyFill = true }, // header
                    new CellFormat { FontId = 0, FillId = 2, BorderId = 0, ApplyFill = true, Alignment = new Alignment { TextRotation = 90, WrapText = true } } // header for certificate
                );

            Stylesheet styleSheet = new Stylesheet(fonts, fills, borders, cellFormats);

            // Adding style
            WorkbookStylesPart stylePart = workbookpart.AddNewPart<WorkbookStylesPart>();
            stylePart.Stylesheet = styleSheet;
            stylePart.Stylesheet.Save();
        }

        private static ICollection<DynamicExcelMappingModel> SetDynamicExcelMapping()
        {
            var dynamicExcelMappingModel = new List<DynamicExcelMappingModel>();

            dynamicExcelMappingModel.Add(
                new DynamicExcelMappingModel
                {
                    Caption = DynamicExcelConstants.IdCaption,
                    FieldName = DynamicExcelConstants.IdField,
                    DataType = CellValues.Number,
                    StyleIndex = 2
                });

            dynamicExcelMappingModel.Add(
                new DynamicExcelMappingModel
                {
                    Caption = DynamicExcelConstants.NameCaption,
                    FieldName = DynamicExcelConstants.NameField,
                    DataType = CellValues.String,
                    StyleIndex = 2,
                    HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                    {
                        product.Name = cellValue;
                    }
                });

            dynamicExcelMappingModel.Add(
                new DynamicExcelMappingModel
                {
                    Caption = DynamicExcelConstants.ManufacturerIdCaption,
                    FieldName = DynamicExcelConstants.ManufacturerIdField,
                    DataType = CellValues.Number,
                    StyleIndex = 2,
                    HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                    {
                        product.ManufacturerId = int.Parse(cellValue);
                    }
                });

            dynamicExcelMappingModel.Add(
                new DynamicExcelMappingModel
                {
                    Caption = DynamicExcelConstants.CategoryIdCaption,
                    FieldName = DynamicExcelConstants.CategoryIdField,
                    DataType = CellValues.Number,
                    StyleIndex = 2,
                    HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                    {
                        product.CategoryId = int.Parse(cellValue);
                    }
                });

            dynamicExcelMappingModel.Add(
                new DynamicExcelMappingModel
                {
                    Caption = DynamicExcelConstants.ProductCategoryIdsCaption,
                    FieldName = DynamicExcelConstants.ProductCategoryIdsField,
                    DataType = CellValues.String,
                    StyleIndex = 2,
                    HandleFieldImportFunction = HandleProductCategoriesImport
                });

            dynamicExcelMappingModel.Add(
                new DynamicExcelMappingModel
                {
                    Caption = DynamicExcelConstants.ProductLineIdCaption,
                    FieldName = DynamicExcelConstants.ProductLineIdField,
                    DataType = CellValues.Number,
                    StyleIndex = 2,
                    HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                    {
                        int productLineId;
                        if (int.TryParse(cellValue, out productLineId))
                            product.ProductLineId = productLineId;
                        else product.ProductLineId = null;
                    }
                });

            dynamicExcelMappingModel.Add(
                new DynamicExcelMappingModel
                {
                    Caption = DynamicExcelConstants.PhotoIdCaption,
                    FieldName = DynamicExcelConstants.PhotoIdField,
                    DataType = CellValues.Number,
                    StyleIndex = 2,
                    HandleFieldImportFunction = HandlePhotoIdImport
                });

            dynamicExcelMappingModel.Add(
                new DynamicExcelMappingModel
                {
                    Caption = DynamicExcelConstants.PhotoURLsCaption,
                    FieldName = DynamicExcelConstants.PhotoURLsField,
                    DataType = CellValues.String,
                    StyleIndex = 2,
                    HandleFieldImportFunction = HandlePhotoUrlsImport
                });

            dynamicExcelMappingModel.Add(
                new DynamicExcelMappingModel
                {
                    Caption = DynamicExcelConstants.DescriptionCaption,
                    FieldName = DynamicExcelConstants.DescriptionField,
                    DataType = CellValues.String,
                    StyleIndex = 2,
                    HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                    {
                        product.Description = cellValue;
                    }
                });
            dynamicExcelMappingModel.Add(
                new DynamicExcelMappingModel
                {
                    Caption = DynamicExcelConstants.VideoUrlCaption,
                    FieldName = DynamicExcelConstants.VideoUrlField,
                    DataType = CellValues.String,
                    StyleIndex = 2,
                    HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                    {
                        product.VideoUrl = cellValue;
                    }
                });
            dynamicExcelMappingModel.Add(
                new DynamicExcelMappingModel
                {
                    Caption = DynamicExcelConstants.VanityURLCaption,
                    FieldName = DynamicExcelConstants.VanityURLField,
                    DataType = CellValues.String,
                    StyleIndex = 2,
                    HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                    {
                        product.VanityURL = cellValue;
                    }
                });
            dynamicExcelMappingModel.Add(
                new DynamicExcelMappingModel
                {
                    Caption = DynamicExcelConstants.MetaTitleCaption,
                    FieldName = DynamicExcelConstants.MetaTitleField,
                    DataType = CellValues.String,
                    StyleIndex = 2,
                    HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                    {
                        product.MetaTitle = cellValue;
                    }
                });

            dynamicExcelMappingModel.Add(
                new DynamicExcelMappingModel
                {
                    Caption = DynamicExcelConstants.MetaDescriptionCaption,
                    FieldName = DynamicExcelConstants.MetaDescriptionField,
                    DataType = CellValues.String,
                    StyleIndex = 2,
                    HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                    {
                        product.MetaDescription = cellValue;
                    }
                });
            dynamicExcelMappingModel.Add(
                new DynamicExcelMappingModel
                {
                    Caption = DynamicExcelConstants.MetaKeywordsCaption,
                    FieldName = DynamicExcelConstants.MetaKeywordsField,
                    DataType = CellValues.String,
                    StyleIndex = 2,
                    HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                    {
                        product.MetaKeywords = cellValue;
                    }
                });
            dynamicExcelMappingModel.Add(
                new DynamicExcelMappingModel
                {
                    Caption = DynamicExcelConstants.KeywordsCaption,
                    FieldName = DynamicExcelConstants.KeywordsField,
                    DataType = CellValues.String,
                    StyleIndex = 2,
                    HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                    {
                        product.Keywords = cellValue;
                    }
                });

            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.ProductCertificatesCaption,
                   FieldName = DynamicExcelConstants.ProductCertificatesField,
                   DataType = CellValues.String,
                   StyleIndex = 2,
                   HandleFieldImportFunction = HandleProductCertificatesImport
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.ProductExternalMasterformatsCaption,
                   FieldName = DynamicExcelConstants.ProductExternalMasterformatsField,
                   DataType = CellValues.String,
                   StyleIndex = 2,
                   HandleFieldImportFunction = HandleProductExternalMasterformatsImport
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.ProductOmniclassesCaption,
                   FieldName = DynamicExcelConstants.ProductOmniclassesField,
                   DataType = CellValues.String,
                   StyleIndex = 2,
                   HandleFieldImportFunction = HandleProductOmniclassesImport
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.ProductUniclassesCaption,
                   FieldName = DynamicExcelConstants.ProductUniclassesField,
                   DataType = CellValues.String,
                   StyleIndex = 2,
                   HandleFieldImportFunction = HandleProductUniclassesImport
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.ProductUniformatsCaption,
                   FieldName = DynamicExcelConstants.ProductUniformatsField,
                   DataType = CellValues.String,
                   StyleIndex = 2,
                   HandleFieldImportFunction = HandleProductUniformatsImport
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.ProductCisfbsCaption,
                   FieldName = DynamicExcelConstants.ProductCisfbsField,
                   DataType = CellValues.String,
                   StyleIndex = 2,
                   HandleFieldImportFunction = HandleProductCisfbsImport
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.ProductCutSheetUrlsCaption,
                   FieldName = DynamicExcelConstants.ProductCutSheetUrlsField,
                   DataType = CellValues.String,
                   StyleIndex = 2,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       HandleProductFilesImport(cellValue, product, handleImportModel.UserId, "Product Cut Sheet", hostUrl);
                   }
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.PartSpecUrlsCaption,
                   FieldName = DynamicExcelConstants.PartSpecUrlsField,
                   DataType = CellValues.String,
                   StyleIndex = 3,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       HandleProductFilesImport(cellValue, product, handleImportModel.UserId, "3-Part Specification", hostUrl);
                   }
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.ProductBrochureUrlsCaption,
                   FieldName = DynamicExcelConstants.ProductBrochureUrlsField,
                   DataType = CellValues.String,
                   StyleIndex = 3,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       HandleProductFilesImport(cellValue, product, handleImportModel.UserId, "Product Brochure", hostUrl);
                   }
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.ImageUrlsCaption,
                   FieldName = DynamicExcelConstants.ImageUrlsField,
                   DataType = CellValues.String,
                   StyleIndex = 3,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       HandleProductFilesImport(cellValue, product, handleImportModel.UserId, "Image", hostUrl);
                   }
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.SubmittalUrlsCaption,
                   FieldName = DynamicExcelConstants.SubmittalUrlsField,
                   DataType = CellValues.String,
                   StyleIndex = 3,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       HandleProductFilesImport(cellValue, product, handleImportModel.UserId, "Submittal", hostUrl);
                   }
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.CatalogUrlsCaption,
                   FieldName = DynamicExcelConstants.CatalogUrlsField,
                   DataType = CellValues.String,
                   StyleIndex = 3,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       HandleProductFilesImport(cellValue, product, handleImportModel.UserId, "Catalog", hostUrl);
                   }
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.WarrantyUrlsCaption,
                   FieldName = DynamicExcelConstants.WarrantyUrlsField,
                   DataType = CellValues.String,
                   StyleIndex = 3,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       HandleProductFilesImport(cellValue, product, handleImportModel.UserId, "Warranty", hostUrl);
                   }
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.TestingDataUrlsCaption,
                   FieldName = DynamicExcelConstants.TestingDataUrlsField,
                   DataType = CellValues.String,
                   StyleIndex = 3,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       HandleProductFilesImport(cellValue, product, handleImportModel.UserId, "Testing Data", hostUrl);
                   }
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.SafetyDataSheetUrlsCaption,
                   FieldName = DynamicExcelConstants.SafetyDataSheetUrlsField,
                   DataType = CellValues.String,
                   StyleIndex = 3,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       HandleProductFilesImport(cellValue, product, handleImportModel.UserId, "Safety Data Sheet", hostUrl);
                   }
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.InstallationGuideUrlsCaption,
                   FieldName = DynamicExcelConstants.InstallationGuideUrlsField,
                   DataType = CellValues.String,
                   StyleIndex = 3,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       HandleProductFilesImport(cellValue, product, handleImportModel.UserId, "Installation Guide", hostUrl);
                   }
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.HpdUrlsCaption,
                   FieldName = DynamicExcelConstants.HpdUrlsField,
                   DataType = CellValues.String,
                   StyleIndex = 3,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       HandleProductFilesImport(cellValue, product, handleImportModel.UserId, "HPD", hostUrl);
                   }
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.MasterPartSpecUrlsCaption,
                   FieldName = DynamicExcelConstants.MasterPartSpecUrlsField,
                   DataType = CellValues.String,
                   StyleIndex = 3,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       HandleProductFilesImport(cellValue, product, handleImportModel.UserId, AttachmentConstants.MasterspecFileTitle, hostUrl);
                   }
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.PublishedCaption,
                   FieldName = DynamicExcelConstants.PublishedField,
                   DataType = CellValues.String,
                   StyleIndex = 3,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       product.Published = cellValue == "1" ? true : false;
                   }
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.PublishedOnCustomMicrositeCaption,
                   FieldName = DynamicExcelConstants.PublishedOnCustomMicrositeField,
                   DataType = CellValues.String,
                   StyleIndex = 3,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       product.PublishedOnCustomMicrosite = cellValue == "1" ? true : false;
                   }
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.StagingCaption,
                   FieldName = DynamicExcelConstants.StagingField,
                   DataType = CellValues.String,
                   StyleIndex = 3,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       product.Staging = cellValue == "1" ? true : false;
                   }
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.ProductUrlCaption,
                   FieldName = DynamicExcelConstants.ProductUrlField,
                   DataType = CellValues.String,
                   StyleIndex = 2,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       product.ProductUrl = cellValue;
                   }
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.ULUrlCaption,
                   FieldName = DynamicExcelConstants.ULUrlField,
                   DataType = CellValues.String,
                   StyleIndex = 2,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       product.ULUrl = cellValue;
                   }
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.RevitFilesCaption,
                   FieldName = DynamicExcelConstants.RevitFilesField,
                   DataType = CellValues.String,
                   StyleIndex = 2,
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.PublishToPartnerCaption,
                   FieldName = DynamicExcelConstants.PublishToPartnerField,
                   DataType = CellValues.String,
                   StyleIndex = 2,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       product.PublishToPartner = cellValue == "1" ? true : false;
                   }
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.ForgeWallURLCaption,
                   FieldName = DynamicExcelConstants.ForgeWallURLField,
                   DataType = CellValues.String,
                   StyleIndex = 2,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       product.ForgeWallURL = cellValue;
                   }
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.ForgeFloorURLCaption,
                   FieldName = DynamicExcelConstants.ForgeFloorURLField,
                   DataType = CellValues.String,
                   StyleIndex = 2,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       product.ForgeFloorURL = cellValue;
                   }
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.ForgeCeilingURLCaption,
                   FieldName = DynamicExcelConstants.ForgeCeilingURLField,
                   DataType = CellValues.String,
                   StyleIndex = 2,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       product.ForgeCeilingURL = cellValue;
                   }
               });
            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.ForgeRoofURLCaption,
                   FieldName = DynamicExcelConstants.ForgeRoofURLField,
                   DataType = CellValues.String,
                   StyleIndex = 2,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       product.ForgeRoofURL = cellValue;
                   }
               });

            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.ExternalIdCaption,
                   FieldName = DynamicExcelConstants.ExternalIdField,
                   DataType = CellValues.String,
                   StyleIndex = 2,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       product.ExternalId = cellValue;
                   }
               });

            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.WeightCaption,
                   FieldName = DynamicExcelConstants.WeightField,
                   DataType = CellValues.Number,
                   StyleIndex = 2,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       float productWeight;
                       if (float.TryParse(cellValue, out productWeight))
                       {
                           product.Weight = productWeight;
                       }
                   }
               });

            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.LocalizationCaption,
                   FieldName = DynamicExcelConstants.LocalizationField,
                   DataType = CellValues.String,
                   StyleIndex = 2,
                   HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                   {
                       product.RegionIds = cellValue;
                   }
               });

            dynamicExcelMappingModel.Add(
                new DynamicExcelMappingModel()
                {
                    Caption = DynamicExcelConstants.EpdUrlsCaption,
                    FieldName = DynamicExcelConstants.EpdUrlsField,
                    DataType = CellValues.String,
                    StyleIndex = 3,
                    HandleFieldImportFunction = (handleImportModel, product, cellValue, hostUrl) =>
                    {
                        HandleProductFilesImport(cellValue, product, handleImportModel.UserId, "EPD", hostUrl);
                    },
                });

            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.AttachmentFileNamesCaption,
                   FieldName = DynamicExcelConstants.AttachmentFileNamesField,
                   DataType = CellValues.String,
                   StyleIndex = 2,
               });

            dynamicExcelMappingModel.Add(
               new DynamicExcelMappingModel
               {
                   Caption = DynamicExcelConstants.ProjectFileNamesCaption,
                   FieldName = DynamicExcelConstants.ProjectFileNamesField,
                   DataType = CellValues.String,
                   StyleIndex = 2,
               });

            return dynamicExcelMappingModel;
        }

        private static void SetProductFilesWeight(IUnitOfWork unitOfWork, Manufacturer manufacturer, Product product)
        {
            var attachmentOrdersByManufacturer = unitOfWork.AttachmentOrderRepository.GetAll()
                .Where(a => a.ManufacturerId == manufacturer.Id)
                .AsEnumerable()
                .GroupBy(a => a.ManufacturerId)
                .ToDictionary(a => a.Key, a => a.OrderBy(b => b.Order)
                .Select(b => b.Type)
                .ToList());

            var productAttachments = product.ProductFiles.Where(x => x.IsAttachment)
                .GroupBy(a => new { Title = a.File?.Title ?? "", SyncUrl = a.File?.SyncUrl ?? "", a.CustomFileId })
                .Select(a => a)
                .ToList();

            if (attachmentOrdersByManufacturer != null && attachmentOrdersByManufacturer.Any())
            {
                productAttachments = productAttachments.OrderBy(a => attachmentOrdersByManufacturer[manufacturer.Id].IndexOf(a.Key.SyncUrl)).ToList();
            }
            else
            {
                productAttachments = productAttachments.OrderBy(a => AttachmentOrderProvider.DefaultAttachmentsOrder.IndexOf(a.Key.Title)).ToList();
            }

            var productWeight = 1;
            foreach (var productAttachment in productAttachments)
            {
                var groupAttachments = productAttachment.ToList();
                foreach (var attachment in groupAttachments)
                {
                    attachment.Weight = productWeight;
                    productWeight++;
                }
            }
        }

        private static void HandleProductMetaInfo(IUnitOfWork unitOfWork, Manufacturer manufacturer, Product product)
        {
            var mainCategoryName = unitOfWork.CategoryRepository.GetById(product.CategoryId)?.Name;
            _productService.HandleProductMedataData(ref product, mainCategoryName, manufacturer.Name);
        }

        #region specific field handle import methods
        private static void HandleProductCisfbsImport(DynamicExcelHandleImportModel handleImportModel, Product product, string cellValue, string hostUrl)
        {
            var importedCisfbsCodes = !string.IsNullOrWhiteSpace(cellValue) ? cellValue.Split(';').ToList() : new List<string>();
            var existingCisfbsCodes = product.ProductCisfbs.Where(x => x.Cisfb != null).Select(x => x.Cisfb.Code).ToList();
            var cisfbsToAddCodes = importedCisfbsCodes.Except(existingCisfbsCodes).ToList();
            var cisfbsToDeleteCodes = existingCisfbsCodes.Except(importedCisfbsCodes).ToList();
            if (product.ProductCisfbs == null)
                product.ProductCisfbs = new List<ProductCisfb>();

            foreach (var cisfbsCode in cisfbsToAddCodes)
            {
                var Cisfbs = handleImportModel.UnitOfWork.CisfbRepository.GetAll().FirstOrDefault(x => x.Code.ToUpper() == cisfbsCode.ToUpper());
                if (Cisfbs == null)
                    handleImportModel.Errors.Add($"Wrong code of Cisfbs: '{cisfbsCode}'");
                else
                {
                    ProductCisfb productCisfb = new ProductCisfb();
                    productCisfb.CisfbId = Cisfbs.Id;
                    productCisfb.ProductId = product.Id;
                    productCisfb.CreatedById = handleImportModel.UserId;
                    productCisfb.CreatedDate = DateTime.UtcNow;
                    handleImportModel.UnitOfWork.ProductCisfbRepository.Insert(productCisfb);
                }
            }
            var CisfbsToDelete = handleImportModel.UnitOfWork.ProductCisfbRepository.GetAll()
            .Where(x => x.ProductId == product.Id && x.Cisfb != null && cisfbsToDeleteCodes.Contains(x.Cisfb.Code))
            .ToList();
            CisfbsToDelete.ForEach(x => handleImportModel.UnitOfWork.ProductCisfbRepository.Delete(x));
        }

        private static void HandleProductUniformatsImport(DynamicExcelHandleImportModel handleImportModel, Product product, string cellValue, string hostUrl)
        {
            var importedUniformatCodes = !string.IsNullOrWhiteSpace(cellValue) ? cellValue.Split(';').ToList() : new List<string>();
            if (product.ProductUniformats == null) product.ProductUniformats = new List<ProductUniformat>();
            var existingUniformatCodes = product.ProductUniformats.Where(x => x.Uniformat != null).Select(x => x.Uniformat.Code).ToList();
            var uniformatToAddCodes = importedUniformatCodes.Except(existingUniformatCodes).ToList();
            var uniformatToDeleteCodes = existingUniformatCodes.Except(importedUniformatCodes).ToList();
            foreach (var uniformatCode in uniformatToAddCodes)
            {
                var uniformat = handleImportModel.UnitOfWork.UniformatRepository.GetAll().FirstOrDefault(x => x.Code.ToUpper() == uniformatCode.ToUpper());
                if (uniformat == null) handleImportModel.Errors.Add($"Wrong code of uniformat: '{uniformatCode}'");
                else
                {
                    ProductUniformat productUniformat = new ProductUniformat();
                    productUniformat.UniformatId = uniformat.Id;
                    productUniformat.ProductId = product.Id;
                    productUniformat.CreatedById = handleImportModel.UserId;
                    productUniformat.CreatedDate = DateTime.UtcNow;
                    handleImportModel.UnitOfWork.ProductUniformatRepository.Insert(productUniformat);
                }
            }
            var uniformatToDelete = handleImportModel.UnitOfWork.ProductUniformatRepository.GetAll()
            .Where(x => x.ProductId == product.Id && x.Uniformat != null && uniformatToDeleteCodes.Contains(x.Uniformat.Code))
            .ToList();
            uniformatToDelete.ForEach(x => handleImportModel.UnitOfWork.ProductUniformatRepository.Delete(x));
        }

        private static void HandleProductUniclassesImport(DynamicExcelHandleImportModel handleImportModel, Product product, string cellValue, string hostUrl)
        {
            var importedUniclassCodes = !string.IsNullOrWhiteSpace(cellValue) ? cellValue.Split(';').ToList() : new List<string>();
            if (product.ProductUniclasses == null) product.ProductUniclasses = new List<ProductUniclass>();
            var existingUniclassCodes = product.ProductUniclasses.Where(x => x.Uniclass != null).Select(x => x.Uniclass.Code).ToList();
            var uniclassToAddCodes = importedUniclassCodes.Except(existingUniclassCodes).ToList();
            var uniclassToDeleteCodes = existingUniclassCodes.Except(importedUniclassCodes).ToList();
            foreach (var uniclassCode in uniclassToAddCodes)
            {
                var uniclass = handleImportModel.UnitOfWork.UniclassRepository.GetAll().FirstOrDefault(x => x.Code.ToUpper() == uniclassCode.ToUpper());
                if (uniclass == null) handleImportModel.Errors.Add($"Wrong code of uniclass: '{uniclassCode}'");
                else
                {
                    ProductUniclass productUniclass = new ProductUniclass();
                    productUniclass.UniclassId = uniclass.Id;
                    productUniclass.ProductId = product.Id;
                    productUniclass.CreatedById = handleImportModel.UserId;
                    productUniclass.CreatedDate = DateTime.UtcNow;
                    handleImportModel.UnitOfWork.ProductUniclassRepository.Insert(productUniclass);
                }
            }
            var uniclassToDelete = handleImportModel.UnitOfWork.ProductUniclassRepository.GetAll()
            .Where(x => x.ProductId == product.Id && x.Uniclass != null && uniclassToDeleteCodes.Contains(x.Uniclass.Code))
            .ToList();
            uniclassToDelete.ForEach(x => handleImportModel.UnitOfWork.ProductUniclassRepository.Delete(x));
        }

        private static void HandleProductOmniclassesImport(DynamicExcelHandleImportModel handleImportModel, Product product, string cellValue, string hostUrl)
        {
            var importedOmniclassCodes = !string.IsNullOrWhiteSpace(cellValue) ? cellValue.Split(';').ToList() : new List<string>();
            if (product.ProductOmniclasses == null) product.ProductOmniclasses = new List<ProductOmniclass>();
            var existingOmniclassCodes = product.ProductOmniclasses.Where(x => x.Omniclass != null).Select(x => x.Omniclass.Code).ToList();
            var omniclassToAddCodes = importedOmniclassCodes.Except(existingOmniclassCodes).ToList();
            var omniclassToDeleteCodes = existingOmniclassCodes.Except(importedOmniclassCodes).ToList();
            foreach (var omniclassCode in omniclassToAddCodes)
            {
                var omniclass = handleImportModel.UnitOfWork.OmniclassRepository.GetAll().FirstOrDefault(x => x.Code.ToUpper() == omniclassCode.ToUpper());
                if (omniclass == null) handleImportModel.Errors.Add($"Wrong code of omniclass: '{omniclassCode}'");
                else
                {
                    ProductOmniclass productOmniclass = new ProductOmniclass();
                    productOmniclass.OmniclassId = omniclass.Id;
                    productOmniclass.ProductId = product.Id;
                    productOmniclass.CreatedById = handleImportModel.UserId;
                    productOmniclass.CreatedDate = DateTime.UtcNow;
                    handleImportModel.UnitOfWork.ProductOmniclassRepository.Insert(productOmniclass);
                }
            }
            var omniclassToDelete = handleImportModel.UnitOfWork.ProductOmniclassRepository.GetAll()
            .Where(x => x.ProductId == product.Id && x.Omniclass != null && omniclassToDeleteCodes.Contains(x.Omniclass.Code))
            .ToList();
            omniclassToDelete.ForEach(x => handleImportModel.UnitOfWork.ProductOmniclassRepository.Delete(x));
        }

        private static void HandleProductExternalMasterformatsImport(DynamicExcelHandleImportModel handleImportModel, Product product, string cellValue, string hostUrl)
        {
            var importedMasterFormatCodes = !string.IsNullOrWhiteSpace(cellValue) ? cellValue.Split(';').ToList() : new List<string>();
            if (product.ProductMasterformats == null) product.ProductMasterformats = new List<ProductMasterformat>();
            var existingExternalMasterformatIds = product.ProductMasterformats.Select(x => x.ExternalMasterformatId).ToList();
            var importedExternalMasterformatIds = handleImportModel.Masterformats.Where(x => importedMasterFormatCodes.Contains(x.Code.Trim())).Select(x => x.Id).ToList();
            var masterFormatToAddIds = importedExternalMasterformatIds.Except(existingExternalMasterformatIds).ToList();
            var masterFormatToDeleteIds = existingExternalMasterformatIds.Except(importedExternalMasterformatIds).ToList();
            foreach (var masterformatId in masterFormatToAddIds)
            {
                var masterFormat = handleImportModel.Masterformats.FirstOrDefault(x => x.Id == masterformatId);
                if (masterFormat == null) handleImportModel.Errors.Add($"Wrong code of external masterformatId: '{masterformatId}'");
                else
                {
                    ProductMasterformat productMasterformat = new ProductMasterformat();
                    productMasterformat.ExternalMasterformatId = masterFormat.Id;
                    productMasterformat.ProductId = product.Id;
                    productMasterformat.CreatedById = handleImportModel.UserId;
                    productMasterformat.CreatedDate = DateTime.UtcNow;
                    handleImportModel.UnitOfWork.ProductMasterformatRepository.Insert(productMasterformat);
                }
            }
            var masterFormatToDelete = handleImportModel.UnitOfWork.ProductMasterformatRepository.GetAll()
            .Where(x => x.ProductId == product.Id && masterFormatToDeleteIds.Contains(x.ExternalMasterformatId))
            .ToList();
            masterFormatToDelete.ForEach(x => handleImportModel.UnitOfWork.ProductMasterformatRepository.Delete(x));
        }

        private static void HandleProductCertificatesImport(DynamicExcelHandleImportModel handleImportModel, Product product, string cellValue, string hostUrl)
        {
            var importedCertificateIds = !string.IsNullOrWhiteSpace(cellValue) ? cellValue.Split(';').Select(x => int.Parse(x)).ToList() : new List<int>();
            _productService.AddOrUpdateProductCertificates(product, handleImportModel.UnitOfWork, importedCertificateIds, handleImportModel.UserId);
        }

        private static void HandlePhotoUrlsImport(DynamicExcelHandleImportModel handleImportModel, Product product, string cellValue, string hostUrl)
        {
            var photoURLs = !string.IsNullOrWhiteSpace(cellValue) ? cellValue.Split('|').ToList() : new List<string>();
            if (product.ProductPhotos == null)
                product.ProductPhotos = new List<ProductPhoto>();

            IEnumerable<string> existingPhotoUrls = product.ProductPhotos.Where(x => x.Photo != null && x.Photo.UploadUrl != null).Select(x => x.Photo.UploadUrl);
            IEnumerable<string> photoToAddUrls = photoURLs.Except(existingPhotoUrls);
            IEnumerable<string> photoToDeleteUrls = existingPhotoUrls.Except(photoURLs);

            foreach (string photoUrl in photoToAddUrls)
            {
                var attachUrl = photoUrl.Trim();
                if (!attachUrl.StartsWith("http"))
                {
                    attachUrl = "http://" + attachUrl;
                }

                string fileName = Path.GetFileName(attachUrl);

                Photo photo = new Photo
                {
                    CreatedById = handleImportModel.UserId,
                    CreatedDate = DateTime.UtcNow,
                    Name = fileName,
                    UploadUrl = attachUrl,
                };

                handleImportModel.UnitOfWork.PhotoRepository.Insert(photo);
                handleImportModel.UnitOfWork.Save();

                int redirectsCount = 0;
            StartRedirect:

                if (attachUrl.StartsWith("https://"))
                {
                    ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;
                    ServicePointManager.ServerCertificateValidationCallback += (sender, certificate, chain, sslPolicyErrors) => true;
                }

                using (HttpClient httpClient = new())
                {
                    httpClient.Timeout = TimeSpan.FromSeconds(5);
                    using (var photoResponse = httpClient.GetAsync(attachUrl).Result)
                    {
                        if (photoResponse.StatusCode == HttpStatusCode.OK || photoResponse.StatusCode == HttpStatusCode.Accepted)
                        {
                            var originalImage = new MemoryStream();
                            using (Stream photoResponseStream = photoResponse.Content.ReadAsStream())
                            {
                                var imageName = $"{handleImportModel.Manufacturer.Name}-{product.Name}-revit-";
                                PhotoProvider.CreateProductPhotosAsync(photo, originalImage, Path.GetExtension(attachUrl), imageName, false).Wait();
                                photo.SyncStatusCode = (int)HttpStatusCode.OK;
                                photo.UpdatesCount = 0;
                            }
                        }
                        else if ((photoResponse.StatusCode == HttpStatusCode.Moved || photoResponse.StatusCode == HttpStatusCode.MovedPermanently || photoResponse.StatusCode == HttpStatusCode.Found) &&
                                 !string.IsNullOrWhiteSpace(photoResponse.Headers.Location?.ToString()) &&
                                 redirectsCount < 2)
                        {
                            redirectsCount++;
                            attachUrl = photoResponse.Headers.Location.ToString();
                            photoResponse.Dispose();
                            goto StartRedirect;
                        }
                    }

                    product.ProductPhotos.Add(new ProductPhoto
                    {
                        ProductId = product.Id,
                        PhotoId = photo.Id,
                        CreatedById = handleImportModel.UserId,
                        CreatedDate = DateTime.UtcNow
                    });
                }
            }

            var photoToDeleteIds = handleImportModel.UnitOfWork.PhotoRepository.GetAll().Where(x => photoToDeleteUrls.Contains(x.UploadUrl)).Select(x => x.Id).ToList();
            var productPhotosToDelete = handleImportModel.UnitOfWork.ProductPhotoRepository.GetAll()
                .Where(x => photoToDeleteIds.Contains(x.PhotoId) && x.ProductId == product.Id)
                .ToList();

            handleImportModel.UnitOfWork.ProductPhotoRepository.Delete(productPhotosToDelete);

            //TODO: Rework when file deletion is implementing
            //handleImportModel.UnitOfWork.PhotoRepository.GetAll().Where(x => photoToDeleteIds.Contains(x.Id)).ToList().ForEach(x => handleImportModel.UnitOfWork.PhotoRepository.Delete(x));
        }

        private static void HandlePhotoIdImport(DynamicExcelHandleImportModel handleImportModel, Product product, string cellValue, string hostUrl)
        {
            int photoId;
            var photoIdHasValue = int.TryParse(cellValue, out photoId);
            if (photoIdHasValue)
            {
                var photoFromDb = handleImportModel.UnitOfWork.PhotoRepository.GetAll().FirstOrDefault(x => x.Id == photoId);
                product.PhotoId = photoFromDb?.Id;
                product.Photo = photoFromDb;
                product.PhotoId = photoId;
            }
            else
            {
                product.PhotoId = null;
            }
        }

        private static void HandleProductCategoriesImport(DynamicExcelHandleImportModel handleImportModel, Product product, string cellValue, string hostUrl)
        {
            var importedCategoryIds = !string.IsNullOrWhiteSpace(cellValue) ? cellValue.Split(';').Select(x => int.Parse(x)).ToList() : new List<int>();
            _productService.AddOrUpdateProductCategories(product, handleImportModel.UnitOfWork, importedCategoryIds);
        }

        private static void HandleProductFilesImport(string cellValue, Product product, string userId, string fileTitle, string hostUrl)
        {
            var urls = !string.IsNullOrWhiteSpace(cellValue) ? cellValue.Split('|').ToList() : new List<string>();
            if (product.ProductFiles == null)
                product.ProductFiles = new List<ProductFile>();
            var existingProductFiles = product.ProductFiles.Where(x => x.IsAttachment && x.File != null && x.File.SyncUrl != null && x.File.SyncUrl != "" && x.File.Title == fileTitle).ToList();
            var allProductFiles = product.ProductFiles.Where(x => x.IsAttachment).ToList();
            var maxFileWeight = allProductFiles.Any() ? allProductFiles.Max(x => x.Weight) : 0;
            var existingProductFileSyncUrls = existingProductFiles.Select(x => x.File.SyncUrl).ToList();
            var productFileUrlsToAdd = urls.Except(existingProductFileSyncUrls).ToList();

            foreach (var url in productFileUrlsToAdd)
            {
                var attachUrl = url.Trim();
                if (attachUrl[attachUrl.Length - 1] == '/')
                {
                    attachUrl = attachUrl.Remove(attachUrl.Length - 1);
                }
                if (!attachUrl.StartsWith("http"))
                {
                    attachUrl = "http://" + attachUrl;
                }

                HttpResponseHeadersInfo headers = WepApiProvider.GetHeaders(attachUrl);
                string fileName = headers.FileName ?? FileHelper.RemoveQueryPath(Path.GetFileName(attachUrl));
                string mediaType = MimeTypeProvider.GetMimeType(fileName);

                var productFile = new ProductFile()
                {
                    ProductId = product.Id,
                    IsAttachment = true,
                    Weight = ++maxFileWeight,
                    CreatedById = userId,
                    File = new Domain.DBModels.File()
                    {
                        FileName = fileName,
                        MediaType = mediaType,
                        CreatedById = userId,
                        Title = fileTitle,
                        CreatedDate = DateTime.UtcNow,
                        NextSyncDateTime = DateTime.UtcNow.AddDays(-1),
                        SyncStatusCode = (int)HttpStatusCode.OK,
                        UpdatesCount = 0,
                        SyncUrl = attachUrl,
                        PreviewUrl = new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment("/assets/img/default_preview.png").ToString(),
                        Url = attachUrl,
                        FileSize = 0,
                        CheckSum = string.Empty
                    }
                };

                product.ProductFiles.Add(productFile);
            }
        }

        #region handle project files import
        private static List<ProductFileModel> ParseProductProjectFiles(List<ProjectDataTypeModel> projectTypes, SharedStringTablePart sharedStringPart, int startProjectTypeIndex, int startQualityItemsIndex, List<Cell> cells, int rowIndex)
        {
            //dynamic fields
            var importedProjectFiles = new List<ProductFileModel>();
            for (int i = startProjectTypeIndex; i < startQualityItemsIndex; i++)
            {
                var cellReference = $"{CommonExcelProvider.ColumnIndexToColumnLetter(i + 1)}{rowIndex}";
                var cell = cells.SingleOrDefault(c => c.CellReference.Value == cellReference);
                var projectUrls = CommonExcelProvider.GetCellStringValue(cell, sharedStringPart)?.Split('|');
                var projectType = projectTypes.First(a => a.CellRefNumber == i + 1);
                if (projectUrls != null)
                {
                    foreach (var productFileUrl in projectUrls)
                    {
                        if (!string.IsNullOrEmpty(productFileUrl))
                        {
                            var newProductFile = new ProductFileModel
                            {
                                FileTitle = Path.GetFileName(productFileUrl),
                                FileSyncUrl = productFileUrl,
                                FileType = new ProjectDataTypeModel { Title = projectType.Title }
                            };
                            importedProjectFiles.Add(newProductFile);
                        }
                    }
                }
            }

            return importedProjectFiles;
        }

        private static void HandleProjectFilesImport(ref Product product, ref Dictionary<int, List<string>> productErrors, List<ProductFileModel> importedProjectFiles, IEnumerable<ProjectDataType> allProjectDataTypes, string userId, string hostUrl)
        {
            var productProjectFiles = product.ProductFiles.Where(x => !x.IsAttachment && x.File != null && !string.IsNullOrWhiteSpace(x.File.SyncUrl)).ToList();
            var existingProductProjectFiles = productProjectFiles.Select(x => new DynamicExcelProjectFileModel { SyncUrl = x.File.SyncUrl, ProjectDataTypeTitle = x.SoftwareVersionId == null ? x.ProjectDataType?.Title : x.SoftwareVersion?.Title }).ToList();
            var importedProductProjectFiles = importedProjectFiles.Select(x => new DynamicExcelProjectFileModel { SyncUrl = x.FileSyncUrl, ProjectDataTypeTitle = x.FileType.Title }).ToList();
            var productProjectFilesToAdd = importedProductProjectFiles.Except(existingProductProjectFiles, new DynamicExcelProjectFileModelComparer()).ToList();
            var productFilesToAdd = importedProjectFiles
                .Where(x => productProjectFilesToAdd.Select(f => f.SyncUrl).Contains(x.FileSyncUrl)
                && productProjectFilesToAdd.Select(f => f.ProjectDataTypeTitle).Contains(x.FileType.Title)).ToList();

            foreach (ProductFileModel projectFile in productFilesToAdd)
            {
                var attachUrl = projectFile.FileSyncUrl.Trim();
                if (attachUrl[attachUrl.Length - 1] == '/')
                {
                    attachUrl = attachUrl.Remove(attachUrl.Length - 1);
                }
                if (!attachUrl.StartsWith("http"))
                {
                    attachUrl = "http://" + attachUrl;
                }

                HttpResponseHeadersInfo headers = WepApiProvider.GetHeaders(attachUrl);
                string fileName = headers.FileName ?? FileHelper.RemoveQueryPath(Path.GetFileName(attachUrl));
                string mediaType = MimeTypeProvider.GetMimeType(fileName);

                var dbFile = new Domain.DBModels.File();
                dbFile.FileName = fileName;
                dbFile.MediaType = mediaType;
                dbFile.CreatedById = userId;
                dbFile.Title = projectFile.FileTitle;
                dbFile.CreatedDate = DateTime.UtcNow;
                dbFile.NextSyncDateTime = DateTime.UtcNow.AddDays(-1);
                dbFile.SyncStatusCode = (int)HttpStatusCode.OK;
                dbFile.UpdatesCount = 0;
                dbFile.SyncUrl = attachUrl;
                dbFile.PreviewUrl = new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment("/assets/img/default_preview.png").ToString();
                dbFile.Url = attachUrl;
                dbFile.FileSize = 0;
                dbFile.CheckSum = string.Empty;

                var productFile = new ProductFile();
                productFile.ProductId = product.Id;
                productFile.CustomFileId = projectFile.CustomFileId;
                productFile.FileId = dbFile.Id;
                productFile.File = dbFile;
                productFile.CreatedById = userId;
                productFile.CreatedDate = DateTime.UtcNow;
                // maybe in some case can be null
                var projectDataType = allProjectDataTypes.FirstOrDefault(a => a.Title == projectFile.FileType.Title);

                if (projectDataType != null)
                {
                    if (projectDataType.ParentId.HasValue)
                    {
                        productFile.ProjectDataTypeId = projectDataType.ParentId;
                        productFile.SoftwareVersionId = projectDataType.Id;
                    }
                    else
                    {
                        productFile.ProjectDataTypeId = projectDataType.Id;
                    }

                    product.ProductFiles.Add(productFile);
                }
                else
                {
                    productErrors.AddProductError($"Product Id: {product.Id}. Can`t find Project File with title {projectFile.FileType.Title}", product.Id);
                }

            }
        }
        #endregion

        #region handle quality items import
        private static List<string> ParseProductQualityItemNames(List<string> errors, List<ProductQualityItemsModel> qualityItems, SharedStringTablePart sharedStringPart, int productStatsStartIndex, int startQualityItemsIndex, List<Cell> cells, int rowIndex)
        {
            var importedProductQualityItemNames = new List<string>();
            for (int i = startQualityItemsIndex; i < productStatsStartIndex; i++)
            {
                var cellReference = $"{CommonExcelProvider.ColumnIndexToColumnLetter(i + 1)}{rowIndex}";
                var cell = cells.SingleOrDefault(c => c.CellReference.Value == cellReference);
                var intValue = CommonExcelProvider.GetCellIntValue(cell, sharedStringPart, errors);
                if (intValue == 1)
                {
                    var certificate = qualityItems.First(a => a.CellRefNumber == i + 1);
                    importedProductQualityItemNames.Add(certificate.Name.Trim());
                }
            }

            return importedProductQualityItemNames;
        }

        private static void HandleProductQualityItemsImport(ref Product product, string userId, List<string> importedProductQualityItemNames, IEnumerable<dynamic> allQualityItems)
        {
            var existingProductQualityItemNames = product.ProductQualityItems.Where(x => x.QualityItem != null).Select(x => x.QualityItem.Name.Trim()).ToList();
            var productQualityItemNamesToAdd = importedProductQualityItemNames.Except(existingProductQualityItemNames).ToList();
            var productQualityItemNamesToDelete = existingProductQualityItemNames.Except(importedProductQualityItemNames).ToList();

            foreach (string qualityItemName in productQualityItemNamesToAdd)
            {
                var productQualityItem = new ProductQualityItem();
                productQualityItem.ProductId = product.Id;
                productQualityItem.QualityItemId = allQualityItems.First(a => a.Name == qualityItemName).Id;
                productQualityItem.CreatedById = userId;
                productQualityItem.CreatedDate = DateTime.UtcNow;

                product.ProductQualityItems.Add(productQualityItem);
            }

            var productQualityItemsToDelete = product.ProductQualityItems.Where(x => x.QualityItem != null && productQualityItemNamesToDelete.Contains(x.QualityItem.Name)).ToList();

            foreach (ProductQualityItem productQualityItem in productQualityItemsToDelete)
            {
                product.ProductQualityItems.Remove(productQualityItem);
            }
        }
        #endregion

        #region handle product stats import
        private static List<ProductProductStatModel> ParseProductKeyStats(List<ProductProductStatModel> productStats,
            SharedStringTablePart sharedStringPart,
            int productStatsStartIndex,
            List<Cell> cells,
            int rowIndex)
        {
            var importedProductStats = new List<ProductProductStatModel>();
            for (int i = productStatsStartIndex; i < productStats.Count + productStatsStartIndex; i++)
            {
                var stringValue = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(i + 1)}{rowIndex}"), sharedStringPart);
                if (!string.IsNullOrWhiteSpace(stringValue))
                {
                    var productStat = productStats.First(a => a.CellRefNumber == i + 1);
                    var newProductStat = new ProductProductStatModel
                    {
                        KeyStatType = productStat.KeyStatType,
                        KeyStatName = productStat.KeyStatName,
                        KeyStatUnit = productStat.KeyStatUnit
                    };
                    switch (newProductStat.KeyStatType)
                    {
                        case KeyStatType.SingleNumeric:
                            newProductStat.Value = stringValue;
                            var stringValues = stringValue.Split('*');
                            if (stringValues.Length > 1)
                            {
                                newProductStat.Value = stringValues[0];
                                newProductStat.Note = stringValues[1];
                            }
                            break;
                        case KeyStatType.TextValue:
                            newProductStat.Value = stringValue;
                            break;
                        case KeyStatType.MultivalueNumeric:
                            var multivalueNumericValues = new List<ProductStatKeyStatValueListModel>();
                            newProductStat.KeyStatValueList = new List<ProductStatKeyStatValueListModel>();
                            var multiValueStrings = stringValue.Split(';');
                            foreach (var multiValueString in multiValueStrings)
                            {
                                var values = multiValueString.Split('*');
                                multivalueNumericValues.Add(new ProductStatKeyStatValueListModel
                                {
                                    KeyStatUnit = new ProductStatKeyStatUnitModel
                                    {
                                        GroupName = values[0],
                                        BUnitName = values[1]
                                    },
                                    Value = values[2],
                                    MaxRangeValue = !string.IsNullOrWhiteSpace(values[3]) ? values[3] : null,
                                    Note = values[4]
                                });
                            }
                            newProductStat.KeyStatValueList.AddRange(multivalueNumericValues);
                            break;
                        case KeyStatType.TextMultivalue:
                            newProductStat.KeyStatValueList = new List<ProductStatKeyStatValueListModel>();
                            var multivalue = new List<ProductStatKeyStatValueListModel>();
                            if (stringValue.Contains("*"))
                            {
                                multivalue = stringValue.Split(';')
                                .Where(a => !string.IsNullOrWhiteSpace(a))
                                .Select(a => new ProductStatKeyStatValueListModel
                                {
                                    Value = a.Split('*').Count() > 1 ? null : a,
                                    MinRangeValue = a.Split('*').Count() > 1 ? a.Split('*')[0] : null,
                                    MaxRangeValue = a.Split('*').Count() > 1 ? a.Split('*')[1] : null
                                }).ToList();
                            }
                            else
                            {
                                multivalue = stringValue.Split(';')
                                .Where(a => !string.IsNullOrWhiteSpace(a))
                                .Select(a => new ProductStatKeyStatValueListModel
                                {
                                    Value = a
                                }).ToList();
                            }
                            newProductStat.KeyStatValueList.AddRange(multivalue);
                            break;
                        case KeyStatType.NumericRange:
                            var numericRange = stringValue.Split('*');
                            newProductStat.MinRangeValue = numericRange.Count() > 1 ? numericRange[0] : null;
                            newProductStat.MaxRangeValue = numericRange.Count() > 1 ? numericRange[1] : null;
                            newProductStat.Note = numericRange.Count() > 2 ? numericRange[2] : string.Empty;
                            break;
                        default:
                            throw new NotImplementedException("KeyStatType is not found!");
                    }

                    importedProductStats.Add(newProductStat);
                }
            }

            return importedProductStats;
        }

        private static List<KeyValuePair<ProductProductStatModel, int>> DeleteOldKeyStats(IUnitOfWork unitOfWork, Product product)
        {
            var keyStatsOrders = new List<KeyValuePair<ProductProductStatModel, int>>();
            var productStatsToDelete = product.ProductStats.ToList();
            foreach (var productStatToDelete in productStatsToDelete)
            {
                keyStatsOrders.Add(new KeyValuePair<ProductProductStatModel, int>(new ProductProductStatModel
                {
                    KeyStatName = productStatToDelete.KeyStat.Name,
                    KeyStatType = productStatToDelete.KeyStatType,
                    Note = productStatToDelete.Note,
                    KeyStatUnit = productStatToDelete.KeyStatUnit == null ? null : new ProductStatKeyStatUnitModel
                    {
                        GroupName = productStatToDelete.KeyStatUnit.GroupName,
                        BUnitName = productStatToDelete.KeyStatUnit.BUnitName
                    },
                    Value = productStatToDelete.Value,
                    MinRangeValue = productStatToDelete.MinRangeValue,
                    MaxRangeValue = productStatToDelete.MaxRangeValue,
                    KeyStatValueList = productStatToDelete.KeyStatValueList.Select(ksvl => new ProductStatKeyStatValueListModel
                    {
                        Value = ksvl.Value,
                        MinRangeValue = ksvl.MinRangeValue,
                        MaxRangeValue = ksvl.MaxRangeValue,
                        KeyStatUnit = ksvl.KeyStatUnit == null ? null : new ProductStatKeyStatUnitModel
                        {
                            BUnitName = ksvl.KeyStatUnit.BUnitName,
                            GroupName = ksvl.KeyStatUnit.GroupName
                        }
                    }).ToList()
                }, productStatToDelete.Order));
                unitOfWork.KeyStatValueListRepository.Delete(productStatToDelete.KeyStatValueList.ToList());
            }
            unitOfWork.ProductStatsRepository.Delete(productStatsToDelete);
            return keyStatsOrders;
        }

        private static void HandleKeyStatsImport(ref Product product, IUnitOfWork unitOfWork, string userId, List<ProductProductStatModel> importedProductStats, List<KeyValuePair<ProductProductStatModel, int>> keyStatsOrders, IEnumerable<dynamic> allKeyStats, IEnumerable<dynamic> allKeyStatUnits)
        {
            int categoryId = product.CategoryId;
            var categoryKeyStats = unitOfWork.CategoryKeyStatRepository.GetAll().Where(ck => ck.CategoryId == categoryId).ToList();

            //Add product productStats
            if (importedProductStats != null)
            {
                foreach (var parsedProductStat in importedProductStats)
                {
                    var keyStat = allKeyStats.First(ks => ks.Name.ToLower() == parsedProductStat.KeyStatName.ToLower());

                    var keyStatUnit = parsedProductStat.KeyStatUnit != null ?
                            allKeyStatUnits.First(ks => ks.GroupName.Trim().ToLower() == parsedProductStat.KeyStatUnit.GroupName.ToLower()
                            && ks.BUnitName.Trim().ToLower() == parsedProductStat.KeyStatUnit.BUnitName.ToLower()) : null;

                    var newProductStat = new ProductStats();
                    var productStatsComparer = new ProductStatModelComparer();
                    var prevKeyStatOrders = keyStatsOrders.Where(a => productStatsComparer.Equals(a.Key, parsedProductStat)).ToList();

                    newProductStat.Order = prevKeyStatOrders.Any() ? prevKeyStatOrders.First().Value : categoryKeyStats.Count + 1;
                    newProductStat.ProductId = product.Id;
                    newProductStat.KeyStatId = keyStat.Id;
                    newProductStat.KeyStatType = parsedProductStat.KeyStatType;
                    newProductStat.Note = parsedProductStat.Note;

                    if (parsedProductStat.KeyStatValueList != null && parsedProductStat.KeyStatValueList.Any())
                    {
                        newProductStat.KeyStatUnitId = null;
                    }
                    else
                    {
                        newProductStat.KeyStatUnitId = keyStatUnit?.Id;
                    }

                    newProductStat.CreatedById = userId;
                    newProductStat.CreatedDate = DateTime.UtcNow;

                    if (parsedProductStat.KeyStatType == KeyStatType.SingleNumeric || parsedProductStat.KeyStatType == KeyStatType.TextValue)
                    {
                        newProductStat.Value = parsedProductStat.Value;
                    }
                    else if (parsedProductStat.KeyStatType == KeyStatType.NumericRange)
                    {
                        newProductStat.MinRangeValue = parsedProductStat.MinRangeValue;
                        newProductStat.MaxRangeValue = parsedProductStat.MaxRangeValue;
                    }
                    else if (parsedProductStat.KeyStatType == KeyStatType.TextMultivalue)
                    {
                        foreach (var item in parsedProductStat.KeyStatValueList)
                        {
                            KeyStatValueList keyStatValueListItem = new KeyStatValueList();

                            keyStatValueListItem.Value = item.Value;
                            keyStatValueListItem.MinRangeValue = item.MinRangeValue;
                            keyStatValueListItem.MaxRangeValue = item.MaxRangeValue;
                            keyStatValueListItem.KeyStatUnitId = keyStatUnit?.Id;
                            keyStatValueListItem.Note = item.Note;
                            keyStatValueListItem.CreatedById = userId;
                            keyStatValueListItem.CreatedDate = DateTime.UtcNow;

                            newProductStat.KeyStatValueList.Add(keyStatValueListItem);
                        }
                    }
                    else if (parsedProductStat.KeyStatType == KeyStatType.MultivalueNumeric)
                    {
                        foreach (var item in parsedProductStat.KeyStatValueList)
                        {
                            var valueListKeyStatUnit = item.KeyStatUnit != null ?
                                allKeyStatUnits.FirstOrDefault(ks => ks.GroupName.Trim().ToLower() == item.KeyStatUnit.GroupName.ToLower()
                                && ks.BUnitName.Trim().ToLower() == item.KeyStatUnit.BUnitName.ToLower()) : null;

                            var keyStatValueListItem = new KeyStatValueList();
                            keyStatValueListItem.Value = item.Value;
                            keyStatValueListItem.MinRangeValue = item.MinRangeValue;
                            keyStatValueListItem.MaxRangeValue = item.MaxRangeValue;
                            keyStatValueListItem.KeyStatUnitId = valueListKeyStatUnit?.Id;
                            keyStatValueListItem.Note = item.Note;
                            keyStatValueListItem.CreatedById = userId;
                            keyStatValueListItem.CreatedDate = DateTime.UtcNow;

                            newProductStat.KeyStatValueList.Add(keyStatValueListItem);
                        }
                    }
                    else if (parsedProductStat.KeyStatType == KeyStatType.None)
                    {
                        // empty key stat
                    }
                    else
                    {
                        throw new NotImplementedException($"Product Id {product.Id}. Passed not implemented KeyStatType");
                    }

                    product.ProductStats.Add(newProductStat);
                }
            }
        }
        #endregion
        #endregion

        private static async Task<List<string>> ValidateProductUrlsAsync(Product product, List<string> cells)
        {
            var errors = new List<string>();

            foreach (ProductFile productFile in product.ProductFiles.Where(x => !(x.File.SyncUrl == null || x.File.SyncUrl.Trim() == string.Empty)))
            {
                if (!string.IsNullOrEmpty(productFile.File?.Url) && !await productFile.File?.Url?.IsActiveUrlAsync())
                {
                    errors.Add($"Product Id {product.Id}. Invalid Product File url: {productFile.File.Url}");
                }
            }

            // main photo of product
            if (cells.Contains(DynamicExcelConstants.PhotoURLsCaption))
            {
                if (!string.IsNullOrEmpty(product.Photo?.UploadUrl) && !await product.Photo?.UploadUrl?.IsActiveImageUrlAsync())
                {
                    errors.Add($"Product Id {product.Id}. Invalid Main Product Photo url: {product.Photo.UploadUrl}");
                }
            }

            bool needCheckProductUrl = cells.Contains(DynamicExcelConstants.ProductUrlField) && !string.IsNullOrEmpty(product.ProductUrl);
            if (needCheckProductUrl && !await product.ProductUrl?.IsActiveUrlAsync())
            {
                errors.Add($"Product Id {product.Id}. Invalid Product url: {product.ProductUrl}");
            }

            bool needCheckProductULUrl = cells.Contains(DynamicExcelConstants.ULUrlCaption) && !string.IsNullOrEmpty(product.ULUrl);
            if (needCheckProductULUrl && !await product.ULUrl?.IsActiveUrlAsync())
            {
                errors.Add($"Product Id {product.Id}. Invalid UL SPOT URL: {product.ULUrl}");
            }

            bool needCheckProductForgeWallURL = cells.Contains(DynamicExcelConstants.ForgeWallURLCaption) && !string.IsNullOrEmpty(product.ForgeWallURL);
            if (needCheckProductForgeWallURL && !await product.ForgeWallURL?.IsActiveUrlAsync())
            {
                errors.Add($"Product Id {product.Id}. Invalid Forge Wall url: {product.ForgeWallURL}");
            }

            bool needCheckProductForgeFloorURL = cells.Contains(DynamicExcelConstants.ForgeFloorURLCaption) && !string.IsNullOrEmpty(product.ForgeFloorURL);
            if (needCheckProductForgeFloorURL && !await product.ForgeFloorURL?.IsActiveUrlAsync())
            {
                errors.Add($"Product Id {product.Id}. Invalid Forge Floor url: {product.ForgeFloorURL}");
            }

            bool needCheckProductForgeCeilingURL = cells.Contains(DynamicExcelConstants.ForgeCeilingURLCaption) && !string.IsNullOrEmpty(product.ForgeCeilingURL);
            if (needCheckProductForgeCeilingURL && !await product.ForgeCeilingURL?.IsActiveUrlAsync())
            {
                errors.Add($"Product Id {product.Id}. Invalid Forge Ceiling url: {product.ForgeCeilingURL}");
            }

            bool needCheckProductForgeRoofURL = cells.Contains(DynamicExcelConstants.ForgeRoofURLCaption) && !string.IsNullOrEmpty(product.ForgeRoofURL);
            if (needCheckProductForgeRoofURL && !await product.ForgeRoofURL?.IsActiveUrlAsync())
            {
                errors.Add($"Product Id {product.Id}. Invalid Forge Roof url: {product.ForgeRoofURL}");
            }

            return errors;
        }

        private static void AddProductError(this Dictionary<int, List<string>> productErrors, string errorMessage, int productId)
        {
            if (!productErrors.TryGetValue(productId, out List<string> errors))
            {
                errors = new List<string>();
                productErrors.Add(productId, errors);
            }
            errors.Add(errorMessage);
        }
    }
}
