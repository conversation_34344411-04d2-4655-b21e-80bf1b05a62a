﻿using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.HealthDashboardDto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.HealthDashboardServices
{
    public class HealthDashboardExcelService : IHealthDashboardExcelService
    {
        private readonly IHealthDashboardService _healthDashboardService;
        private readonly IUploadFileService _uploadFileService;

        public HealthDashboardExcelService(
             IHealthDashboardService healthDashboardService,
             IUploadFileService uploadFileService)
        {
            _healthDashboardService = healthDashboardService;
            _uploadFileService = uploadFileService;
        }

        public async Task<string> GetBrokenLinksExcelAsync(HealthDashboardFilterDto model, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                int[] fileIds = await _healthDashboardService.GetFileIdsAsync(model, unitOfWork, cancellationToken);
                int[] fileIdsWithBrokenLink = await _healthDashboardService.GetBrokenLinksFileIdsAsync(fileIds, unitOfWork, cancellationToken);
                List<HealthDashboardExportProductBrokenLinksDto> productsInfo = await GetBrokenLinkProductsInfoAsync(unitOfWork, fileIdsWithBrokenLink, cancellationToken);
                return await GenerateBrokenLinkExcelAsync(productsInfo, cancellationToken);
            }
        }

        public async Task<string> GetDashboardExcelAsync(HealthDashboardFilterDto model, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            string filePath = string.Empty;
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                switch (model.DashboardMissingType)
                {
                    case HealthDashboardType.BrokenLinks:
                        {
                            int[] fileIds = await _healthDashboardService.GetFileIdsAsync(model, unitOfWork, cancellationToken);
                            int[] fileIdsWithBrokenLink = await _healthDashboardService.GetBrokenLinksFileIdsAsync(fileIds, unitOfWork, cancellationToken);
                            List<HealthDashboardExportProductBrokenLinksDto> productsInfo = await GetBrokenLinkProductsInfoAsync(unitOfWork, fileIdsWithBrokenLink, cancellationToken);

                            cancellationToken.ThrowIfCancellationRequested();

                            filePath = await GenerateBrokenLinkExcelAsync(productsInfo, cancellationToken);
                        }
                        break;
                    case HealthDashboardType.BlankDescriptions:
                        {
                            int[] productIds = await _healthDashboardService.GetBlankDescriptionsProductIdsAsync(model, unitOfWork, cancellationToken);
                            List<HealthDashboardExportProductBlankDescriptionsDto> productsInfo = await GetBlankDescriptionProductsInfoAsync(unitOfWork, productIds, cancellationToken);

                            cancellationToken.ThrowIfCancellationRequested();

                            filePath = await GenerateBlankDescriptionExcelAsync(productsInfo, cancellationToken);
                        }
                        break;
                    case HealthDashboardType.NoRevitFiles:
                        {
                            int[] productIds = await _healthDashboardService.GetNoRevitFileProductIdsAsync(model, unitOfWork, cancellationToken);
                            List<HealthDashboardExportNoRevitFileDto> productInfo = await GetNoRevitFileProductsInfoAsync(unitOfWork, productIds, cancellationToken);

                            cancellationToken.ThrowIfCancellationRequested();

                            filePath = await GenerateNoRevitFileExcelAsync(productInfo, cancellationToken);
                        }
                        break;
                    case HealthDashboardType.RevitMissingExtension:
                        {
                            int[] fileids = await _healthDashboardService.GetRevitFileNoExtensionFileIdsAsync(model, unitOfWork, cancellationToken);
                            List<HealthDashboardExportRevitWithoutDescriptionDto> productInfo = await GetRevitFileNoExtensionProductInfosAsync(unitOfWork, fileids, cancellationToken);

                            cancellationToken.ThrowIfCancellationRequested();

                            filePath = await GenerateRevitMissingExtensionFileExcelAsync(productInfo, cancellationToken);
                        }
                        break;
                    case HealthDashboardType.ProductsWithZipFilesNumber:
                        {
                            ICollection<HealthDashboardExportProductWithZipFilesDto> productInfo = await _healthDashboardService.GetProductsWithZipFilesAsync(model, unitOfWork, cancellationToken);

                            cancellationToken.ThrowIfCancellationRequested();

                            filePath = await GenerateProductsWithZipFilesExcelAsync(productInfo, cancellationToken);
                        }
                        break;
                }
            }
            return filePath;
        }

        public async Task<ExcelImportResultDto> RepairBrokenLinksAsync(IFormFile formFile, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var errors = new List<string>();
            var warnings = new List<string>();
            var existingProductIds = new List<int>();
            using (var stream = formFile.OpenReadStream())
            {
                SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Open(stream, false);

                using (var unitOfWork = UnitOfWork.Create())
                {
                    var workbookpart = spreadsheetDocument.WorkbookPart;
                    var workbook = workbookpart.Workbook;
                    var sheets = workbook.Descendants<Sheet>();
                    var worksheetPart = (WorksheetPart)workbookpart.GetPartById(sheets.First().Id);
                    var sharedStringPart = workbookpart.SharedStringTablePart;

                    var rows = worksheetPart.Worksheet.Descendants<Row>().ToList();
                    var rowHeader = rows.FirstOrDefault();
                    if (rowHeader == null)
                        throw new InvalidInputException("Can't to found Header row");

                    var headerCells = rowHeader.Elements<Cell>().ToList();
                    CommonExcelProvider.ValidCellValue(headerCells[0], HealthDashboardConstants.ExcelProductIdCaption, sharedStringPart, errors, $"Wrong position of column with name '{HealthDashboardConstants.ExcelProductIdCaption}'");
                    CommonExcelProvider.ValidCellValue(headerCells[1], HealthDashboardConstants.ExcelProductNameCaption, sharedStringPart, errors, $"Wrong position of column with name '{HealthDashboardConstants.ExcelProductNameCaption}'");
                    CommonExcelProvider.ValidCellValue(headerCells[2], HealthDashboardConstants.ExcelAdminPanelProductPageUrlCaption, sharedStringPart, errors, $"{HealthDashboardConstants.ExcelAdminPanelProductPageUrlCaption}'");
                    CommonExcelProvider.ValidCellValue(headerCells[3], HealthDashboardConstants.ExcelMarketProductPageUrlCaption, sharedStringPart, errors, $"Wrong position of column with name '{HealthDashboardConstants.ExcelMarketProductPageUrlCaption}'");
                    CommonExcelProvider.ValidCellValue(headerCells[4], HealthDashboardConstants.ExcelAttachmentTypeCaption, sharedStringPart, errors, $"Wrong position of column with name '{HealthDashboardConstants.ExcelAttachmentTypeCaption}'");
                    CommonExcelProvider.ValidCellValue(headerCells[5], HealthDashboardConstants.ExcelAttachmentSourceUrlCaption, sharedStringPart, errors, $"Wrong position of column with name '{HealthDashboardConstants.ExcelAttachmentSourceUrlCaption}'");
                    CommonExcelProvider.ValidCellValue(headerCells[6], HealthDashboardConstants.ExcelAttachmentSourceStatusCaption, sharedStringPart, errors, $"Wrong position of column with name '{HealthDashboardConstants.ExcelAttachmentSourceStatusCaption}'");
                    CommonExcelProvider.ValidCellValue(headerCells[7], HealthDashboardConstants.ExcelFileIdCaption, sharedStringPart, errors, $"Wrong position of column with name '{HealthDashboardConstants.ExcelFileIdCaption}'");

                    if (errors.Any())
                        throw new InvalidInputException(string.Join(Environment.NewLine, errors));

                    int rowIndex = 2;
                    foreach (var row in rows.Skip(1)) // skip header row
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        var cells = row.Elements<Cell>().ToList();

                        if (!cells.Any() || cells.All(x => !CommonExcelProvider.HasCellValue(x, sharedStringPart)))
                        {
                            rowIndex++;
                            continue;
                        }

                        int cellsToSkip = 5;
                        int cellIndex = cellsToSkip + 1;

                        string sourceUrl = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        cellIndex += 2;

                        int fileId = CommonExcelProvider.GetCellIntValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart, errors);
                        var file = await unitOfWork.FileRepository.GetByIdAsync(fileId);
                        if (file == null)
                            errors.Add($"File with id {fileId} not found");

                        file.SyncUrl = sourceUrl;
                        file.UpdatesCount = 0;
                        file.NextSyncDateTime = DateTime.UtcNow.AddDays(-1);
                        unitOfWork.FileRepository.Edit(file);

                        rowIndex++;
                    }

                    await unitOfWork.SaveAsync(cancellationToken);

                    return new ExcelImportResultDto
                    {
                        Errors = errors,
                        Warnings = warnings
                    };
                }
            }
        }

        public async Task<ExcelImportResultDto> RepairProductsAsync(IFormFile formFile, HealthDashboardType dashboardType, string userId, CancellationToken cancellationToken = default)
        {
            switch (dashboardType)
            {
                case HealthDashboardType.BrokenLinks:
                    return await RepairBrokenLinksAsync(formFile, cancellationToken);
                case HealthDashboardType.BlankDescriptions:
                    return await FixBlankDescriptionAsync(formFile, cancellationToken);
                case HealthDashboardType.NoRevitFiles:
                    return await FixNoRevitFileAsync(formFile, userId, cancellationToken);
                case HealthDashboardType.RevitMissingExtension:
                    return await FixRevitExtensionAsync(formFile, cancellationToken);
                default:
                    return new ExcelImportResultDto();
            }
        }

        #region private methods

        private async Task<ExcelImportResultDto> FixBlankDescriptionAsync(IFormFile formFile, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var errors = new List<string>();
            var warnings = new List<string>();
            var existingProductIds = new List<int>();
            using (var stream = formFile.OpenReadStream())
            {
                SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Open(stream, false);

                using (var unitOfWork = UnitOfWork.Create())
                {
                    var workbookpart = spreadsheetDocument.WorkbookPart;
                    var workbook = workbookpart.Workbook;
                    var sheets = workbook.Descendants<Sheet>();
                    var worksheetPart = (WorksheetPart)workbookpart.GetPartById(sheets.First().Id);
                    var sharedStringPart = workbookpart.SharedStringTablePart;

                    var rows = worksheetPart.Worksheet.Descendants<Row>().ToList();
                    var rowHeader = rows.FirstOrDefault();
                    if (rowHeader == null)
                        throw new InvalidInputException("Can't to found Header row");

                    var headerCells = rowHeader.Elements<Cell>().ToList();
                    CommonExcelProvider.ValidCellValue(headerCells[0], HealthDashboardConstants.ExcelProductIdCaption, sharedStringPart, errors, $"Wrong position of column with name '{HealthDashboardConstants.ExcelProductIdCaption}'");
                    CommonExcelProvider.ValidCellValue(headerCells[1], HealthDashboardConstants.ExcelProductNameCaption, sharedStringPart, errors, $"Wrong position of column with name '{HealthDashboardConstants.ExcelProductNameCaption}'");
                    CommonExcelProvider.ValidCellValue(headerCells[2], HealthDashboardConstants.ExcelAdminPanelProductPageUrlCaption, sharedStringPart, errors, $"{HealthDashboardConstants.ExcelAdminPanelProductPageUrlCaption}'");
                    CommonExcelProvider.ValidCellValue(headerCells[3], HealthDashboardConstants.ExcelMarketProductPageUrlCaption, sharedStringPart, errors, $"Wrong position of column with name '{HealthDashboardConstants.ExcelMarketProductPageUrlCaption}'");
                    CommonExcelProvider.ValidCellValue(headerCells[4], HealthDashboardConstants.ExcelDescriptionCaption, sharedStringPart, errors, $"Wrong position of column with name '{HealthDashboardConstants.ExcelDescriptionCaption}'");

                    if (errors.Any())
                        throw new InvalidInputException(string.Join(Environment.NewLine, errors));

                    int rowIndex = 2;
                    foreach (var row in rows.Skip(1)) // skip header row
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        var cells = row.Elements<Cell>().ToList();

                        if (!cells.Any() || cells.All(x => !CommonExcelProvider.HasCellValue(x, sharedStringPart)))
                        {
                            rowIndex++;
                            continue;
                        }

                        int cellIndex = 1;

                        int productId = CommonExcelProvider.GetCellIntValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart, errors);
                        cellIndex += 4;

                        string description = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        var product = await unitOfWork.ProductRepository.GetByIdAsync(productId);
                        if (product == null)
                            errors.Add($"Product with id {productId} not found");

                        product.Description = description;
                        unitOfWork.ProductRepository.Edit(product);

                        rowIndex++;
                    }

                    await unitOfWork.SaveAsync(cancellationToken);

                    return new ExcelImportResultDto
                    {
                        Errors = errors,
                        Warnings = warnings
                    };
                }
            }
        }

        private async Task<ExcelImportResultDto> FixNoRevitFileAsync(IFormFile formFile, string userId, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var errors = new List<string>();
            var warnings = new List<string>();
            var existingProductIds = new List<int>();
            using (var stream = formFile.OpenReadStream())
            {
                SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Open(stream, false);

                using (var unitOfWork = UnitOfWork.Create())
                {
                    var workbookpart = spreadsheetDocument.WorkbookPart;
                    var workbook = workbookpart.Workbook;
                    var sheets = workbook.Descendants<Sheet>();
                    var worksheetPart = (WorksheetPart)workbookpart.GetPartById(sheets.First().Id);
                    var sharedStringPart = workbookpart.SharedStringTablePart;

                    var rows = worksheetPart.Worksheet.Descendants<Row>().ToList();
                    var rowHeader = rows.FirstOrDefault();
                    if (rowHeader == null)
                        throw new InvalidInputException("Can't to found Header row");

                    var headerCells = rowHeader.Elements<Cell>().ToList();
                    CommonExcelProvider.ValidCellValue(headerCells[0], HealthDashboardConstants.ExcelProductIdCaption, sharedStringPart, errors, $"Wrong position of column with name '{HealthDashboardConstants.ExcelProductIdCaption}'");
                    CommonExcelProvider.ValidCellValue(headerCells[1], HealthDashboardConstants.ExcelProductNameCaption, sharedStringPart, errors, $"Wrong position of column with name '{HealthDashboardConstants.ExcelProductNameCaption}'");
                    CommonExcelProvider.ValidCellValue(headerCells[2], HealthDashboardConstants.ExcelAdminPanelProductPageUrlCaption, sharedStringPart, errors, $"{HealthDashboardConstants.ExcelAdminPanelProductPageUrlCaption}'");
                    CommonExcelProvider.ValidCellValue(headerCells[3], HealthDashboardConstants.ExcelMarketProductPageUrlCaption, sharedStringPart, errors, $"Wrong position of column with name '{HealthDashboardConstants.ExcelMarketProductPageUrlCaption}'");
                    CommonExcelProvider.ValidCellValue(headerCells[4], HealthDashboardConstants.ExcelRevitUploadUrlCaption, sharedStringPart, errors, $"Wrong position of column with name '{HealthDashboardConstants.ExcelRevitUploadUrlCaption}'");

                    if (errors.Any())
                        throw new InvalidInputException(string.Join(Environment.NewLine, errors));

                    int rowIndex = 2;
                    foreach (var row in rows.Skip(1)) // skip header row
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        var cells = row.Elements<Cell>().ToList();

                        if (!cells.Any() || cells.All(x => !CommonExcelProvider.HasCellValue(x, sharedStringPart)))
                        {
                            rowIndex++;
                            continue;
                        }

                        int cellIndex = 1;

                        int productId = CommonExcelProvider.GetCellIntValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart, errors);
                        cellIndex += 4;

                        string revitFileUrl = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);

                        if (string.IsNullOrEmpty(revitFileUrl))
                        {
                            errors.Add($"Fill Revit file url at row {rowIndex}");
                            continue;
                        }

                        var product = await unitOfWork.ProductRepository.GetByIdAsync(productId);
                        if (product == null)
                        {
                            errors.Add($"Product with id {productId} not found");
                            rowIndex++;
                            continue;
                        }

                        var dbFile = await _uploadFileService.AddFileAsync(formFile, userId, Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "App_Data\\Files"),
                            Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "assets\\img\\default_preview.png"), revitFileUrl, false);

                        if (dbFile == null)
                            errors.Add("Cant attach revit file");

                        Domain.DBModels.ProductFile productFile = new Domain.DBModels.ProductFile
                        {
                            FileId = dbFile.Id,
                            ProductId = product.Id,
                            IsAttachment = false,
                            ProjectDataTypeId = 1
                        };


                        unitOfWork.ProductFileRepository.Insert(productFile);

                        rowIndex++;
                    }

                    await unitOfWork.SaveAsync(cancellationToken);

                    return new ExcelImportResultDto
                    {
                        Errors = errors,
                        Warnings = warnings
                    };
                }
            }
        }

        private async Task<ExcelImportResultDto> FixRevitExtensionAsync(IFormFile formFile, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var errors = new List<string>();
            var warnings = new List<string>();
            var existingProductIds = new List<int>();
            using (var stream = formFile.OpenReadStream())
            {
                SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Open(stream, false);

                using (var unitOfWork = UnitOfWork.Create())
                {
                    var workbookpart = spreadsheetDocument.WorkbookPart;
                    var workbook = workbookpart.Workbook;
                    var sheets = workbook.Descendants<Sheet>();
                    var worksheetPart = (WorksheetPart)workbookpart.GetPartById(sheets.First().Id);
                    var sharedStringPart = workbookpart.SharedStringTablePart;

                    var rows = worksheetPart.Worksheet.Descendants<Row>().ToList();
                    var rowHeader = rows.FirstOrDefault();
                    if (rowHeader == null)
                        throw new InvalidInputException("Can't to found Header row");

                    var headerCells = rowHeader.Elements<Cell>().ToList();
                    CommonExcelProvider.ValidCellValue(headerCells[0], HealthDashboardConstants.ExcelProductIdCaption, sharedStringPart, errors, $"Wrong position of column with name '{HealthDashboardConstants.ExcelProductIdCaption}'");
                    CommonExcelProvider.ValidCellValue(headerCells[1], HealthDashboardConstants.ExcelProductNameCaption, sharedStringPart, errors, $"Wrong position of column with name '{HealthDashboardConstants.ExcelProductNameCaption}'");
                    CommonExcelProvider.ValidCellValue(headerCells[2], HealthDashboardConstants.ExcelAdminPanelProductPageUrlCaption, sharedStringPart, errors, $"{HealthDashboardConstants.ExcelAdminPanelProductPageUrlCaption}'");
                    CommonExcelProvider.ValidCellValue(headerCells[3], HealthDashboardConstants.ExcelMarketProductPageUrlCaption, sharedStringPart, errors, $"Wrong position of column with name '{HealthDashboardConstants.ExcelMarketProductPageUrlCaption}'");
                    CommonExcelProvider.ValidCellValue(headerCells[4], HealthDashboardConstants.ExcelFileIdCaption, sharedStringPart, errors, $"Wrong position of column with name '{HealthDashboardConstants.ExcelFileIdCaption}'");
                    CommonExcelProvider.ValidCellValue(headerCells[5], HealthDashboardConstants.ExcelRevitFileNameCaption, sharedStringPart, errors, $"Wrong position of column with name '{HealthDashboardConstants.ExcelRevitFileNameCaption}'");

                    if (errors.Any())
                        throw new InvalidInputException(string.Join(Environment.NewLine, errors));

                    int rowIndex = 2;
                    foreach (var row in rows.Skip(1)) // skip header row
                    {
                        var cells = row.Elements<Cell>().ToList();

                        if (!cells.Any() || cells.All(x => !CommonExcelProvider.HasCellValue(x, sharedStringPart)))
                        {
                            rowIndex++;
                            continue;
                        }

                        int cellIndex = 5;

                        int fileId = CommonExcelProvider.GetCellIntValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart, errors);
                        cellIndex += 1;

                        string fileName = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        var file = await unitOfWork.FileRepository.GetByIdAsync(fileId);
                        if (file == null)
                            errors.Add($"File with id {file} not found");

                        var azureBlobProvider = new AzureStorageService(
                            ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                            ConfigurationHelper.GetValue("Environment"),
                            bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                            false);
                        var filesContainer = azureBlobProvider.GetContainerByName(AzureStorageConstants.FilesContainer);

                        var oldName = Path.GetFileName(file.Url);
                        var newBlob = await azureBlobProvider.RenameBlobAsync(filesContainer, fileName, oldName, file.Url);
                        if (newBlob != null)
                        {
                            file.Url = newBlob.Uri.ToString();
                        }

                        file.FileName = fileName;
                        file.Title = fileName;
                        unitOfWork.FileRepository.Edit(file);

                        rowIndex++;
                    }

                    await unitOfWork.SaveAsync();

                    return new ExcelImportResultDto
                    {
                        Errors = errors,
                        Warnings = warnings
                    };
                }
            }
        }

        #region Get Products info

        private async Task<List<HealthDashboardExportProductBrokenLinksDto>> GetBrokenLinkProductsInfoAsync(IUnitOfWork unitOfWork, int[] fileIds, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            List<HealthDashboardExportProductBrokenLinksDto> productsInfo = new List<HealthDashboardExportProductBrokenLinksDto>();

            var manufacturerFiles = (await unitOfWork.ManufacturerFileRepository.GetAll()
                                                                                   .Where(x => x.IsAttachment
                                                                                            && fileIds.Contains(x.FileId))
                                                                                   .ToArrayAsync(cancellationToken))
                                    .Adapt<HealthDashboardExportProductBrokenLinksDto[]>();

            productsInfo.AddRange(manufacturerFiles);

            HealthDashboardExportProductBrokenLinksDto[] productLineFiles = (await unitOfWork.ProductLineFileRepository.GetAll()
                                                                 .Where(x => x.IsAttachment
                                                                          && fileIds.Contains(x.FileId))
                                                                 .ToArrayAsync(cancellationToken))
                                                                 .Adapt<HealthDashboardExportProductBrokenLinksDto[]>();

            productsInfo.AddRange(productLineFiles);

            HealthDashboardExportProductBrokenLinksDto[] productFiles = (await unitOfWork.ProductFileRepository.GetAll()
                                                                              .Where(x => x.IsAttachment
                                                                                       && fileIds.Contains(x.FileId))
                                                                              .ToArrayAsync(cancellationToken))
                                                             .Adapt<HealthDashboardExportProductBrokenLinksDto[]>();

            productsInfo.AddRange(productFiles);

            return productsInfo;
        }

        private async Task<List<HealthDashboardExportProductBlankDescriptionsDto>> GetBlankDescriptionProductsInfoAsync(IUnitOfWork unitOfWork, int[] productIds, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            List<HealthDashboardExportProductBlankDescriptionsDto> productsInfo = new List<HealthDashboardExportProductBlankDescriptionsDto>();

            var products = (await unitOfWork.ProductRepository.GetAll()
                                            .Where(x => productIds.Contains(x.Id))
                                            .ToArrayAsync(cancellationToken))
                                    .Adapt<HealthDashboardExportProductBlankDescriptionsDto[]>();

            productsInfo.AddRange(products);

            return productsInfo;
        }

        private async Task<List<HealthDashboardExportNoRevitFileDto>> GetNoRevitFileProductsInfoAsync(IUnitOfWork unitOfWork, int[] productIds, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            List<HealthDashboardExportNoRevitFileDto> productsInfo = new List<HealthDashboardExportNoRevitFileDto>();

            var products = (await unitOfWork.ProductRepository.GetAll()
                                            .Where(x => productIds.Contains(x.Id))
                                            .ToArrayAsync(cancellationToken))
                                    .Adapt<HealthDashboardExportNoRevitFileDto[]>();

            productsInfo.AddRange(products);

            return productsInfo;
        }

        private async Task<List<HealthDashboardExportRevitWithoutDescriptionDto>> GetRevitFileNoExtensionProductInfosAsync(IUnitOfWork unitOfWork, int[] fileIds, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            List<HealthDashboardExportRevitWithoutDescriptionDto> productsInfo = new List<HealthDashboardExportRevitWithoutDescriptionDto>();

            var manufacturerFiles = (await unitOfWork.ManufacturerFileRepository.GetAll()
                                                     .Where(x => fileIds.Contains(x.FileId))
                                                     .ToArrayAsync(cancellationToken))
                                    .Adapt<HealthDashboardExportRevitWithoutDescriptionDto[]>();

            productsInfo.AddRange(manufacturerFiles);

            HealthDashboardExportRevitWithoutDescriptionDto[] productLineFiles = (await unitOfWork.ProductLineFileRepository.GetAll()
                                                                 .Where(x => fileIds.Contains(x.FileId))
                                                                 .ToArrayAsync(cancellationToken))
                                                                 .Adapt<HealthDashboardExportRevitWithoutDescriptionDto[]>();

            productsInfo.AddRange(productLineFiles);

            HealthDashboardExportRevitWithoutDescriptionDto[] productFiles = (await unitOfWork.ProductFileRepository.GetAll()
                                                                              .Where(x => fileIds.Contains(x.FileId))
                                                                              .ToArrayAsync(cancellationToken))
                                                             .Adapt<HealthDashboardExportRevitWithoutDescriptionDto[]>();

            productsInfo.AddRange(productFiles);

            return productsInfo;
        }

        #endregion

        #region Generate excel

        private async Task<string> GenerateBrokenLinkExcelAsync(List<HealthDashboardExportProductBrokenLinksDto> productsInfo, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            string excelName = $"Broken Links Excel {Guid.NewGuid()}";
            string filePath = Path.Combine(Path.GetTempPath(), $"{excelName}.xlsx");

            // Create a spreadsheet document by supplying the filepath.
            // By default, AutoSave = true, Editable = true, and Type = xlsx.
            SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Create(filePath, SpreadsheetDocumentType.Workbook);

            // Add a WorkbookPart to the document.
            WorkbookPart workbookpart = spreadsheetDocument.AddWorkbookPart();
            workbookpart.Workbook = new Workbook();

            // Add a WorksheetPart to the WorkbookPart.
            WorksheetPart worksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            worksheetPart.Worksheet = new Worksheet();

            Fonts fonts = GetFonts();

            Fills fills = GetFills();

            Borders borders = GetBorders();

            CellFormats cellFormats = GetCellFormats();

            Stylesheet styleSheet = new Stylesheet(fonts, fills, borders, cellFormats);

            // Adding style
            WorkbookStylesPart stylePart = workbookpart.AddNewPart<WorkbookStylesPart>();
            stylePart.Stylesheet = styleSheet;
            stylePart.Stylesheet.Save();

            //columns
            Columns columns = new Columns();
            int columsCount = 8;

            for (uint headerColumnIndex = 1; headerColumnIndex < columsCount + 1; headerColumnIndex++)
                columns.AppendChild(new Column { Min = headerColumnIndex, Max = headerColumnIndex, Width = 25, CustomWidth = true });

            worksheetPart.Worksheet.AppendChild(columns);
            workbookpart.Workbook.Save();

            //Sheets
            Sheets sheets = workbookpart.Workbook.AppendChild(new Sheets());

            //Append a new worksheet and associate it with the workbook.
            Sheet sheet = new Sheet()
            {
                Id = spreadsheetDocument.WorkbookPart.
                GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = excelName
            };
            sheets.Append(sheet);
            workbookpart.Workbook.Save();

            // Get the sheetData cell table.
            SheetData sheetData = worksheetPart.Worksheet.AppendChild(new SheetData());

            int rowIndex = 1;
            Row rowHeader = new Row();
            sheetData.AppendChild(rowHeader);

            int columnIndex = 1;

            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelProductIdCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelProductNameCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelAdminPanelProductPageUrlCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelMarketProductPageUrlCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelAttachmentTypeCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelAttachmentSourceUrlCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelAttachmentSourceStatusCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelFileIdCaption, CellValues.String, columnIndex++, rowIndex, 2));

            rowIndex++;
            foreach (var product in productsInfo)
            {
                cancellationToken.ThrowIfCancellationRequested();

                Row row = new Row();
                sheetData.AppendChild(row);

                columnIndex = 1;

                row.AppendChild(CommonExcelProvider.ConstructCell(product.ProductId.ToString(), CellValues.Number, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.ProductName, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.AdminPanelProductPageUrl, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.MarketProductPageUrl, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.AttachmentType, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.AttachmentSourceUrl, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.AttachmentSourceStatus.ToString(), CellValues.Number, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.FileId.ToString(), CellValues.Number, columnIndex++, rowIndex));

                rowIndex++;
            }

            cancellationToken.ThrowIfCancellationRequested();

            workbookpart.Workbook.Save();

            spreadsheetDocument.Dispose();

            return filePath;
        }

        private async Task<string> GenerateBlankDescriptionExcelAsync(List<HealthDashboardExportProductBlankDescriptionsDto> productsInfo, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            string excelName = $"Blank Description Excel {Guid.NewGuid()}";
            string filePath = Path.Combine(Path.GetTempPath(), $"{excelName}.xlsx");

            // Create a spreadsheet document by supplying the filepath.
            // By default, AutoSave = true, Editable = true, and Type = xlsx.
            SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Create(filePath, SpreadsheetDocumentType.Workbook);

            // Add a WorkbookPart to the document.
            WorkbookPart workbookpart = spreadsheetDocument.AddWorkbookPart();
            workbookpart.Workbook = new Workbook();

            // Add a WorksheetPart to the WorkbookPart.
            WorksheetPart worksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            worksheetPart.Worksheet = new Worksheet();

            Fonts fonts = GetFonts();

            Fills fills = GetFills();

            Borders borders = GetBorders();

            CellFormats cellFormats = GetCellFormats();

            Stylesheet styleSheet = new Stylesheet(fonts, fills, borders, cellFormats);

            // Adding style
            WorkbookStylesPart stylePart = workbookpart.AddNewPart<WorkbookStylesPart>();
            stylePart.Stylesheet = styleSheet;
            stylePart.Stylesheet.Save();

            //columns
            Columns columns = new Columns();
            int columsCount = 5;

            for (uint headerColumnIndex = 1; headerColumnIndex < columsCount + 1; headerColumnIndex++)
                columns.AppendChild(new Column { Min = headerColumnIndex, Max = headerColumnIndex, Width = 25, CustomWidth = true });

            worksheetPart.Worksheet.AppendChild(columns);
            workbookpart.Workbook.Save();

            //Sheets
            Sheets sheets = workbookpart.Workbook.AppendChild(new Sheets());

            //Append a new worksheet and associate it with the workbook.
            Sheet sheet = new Sheet()
            {
                Id = spreadsheetDocument.WorkbookPart.
                GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = excelName
            };
            sheets.Append(sheet);
            workbookpart.Workbook.Save();

            // Get the sheetData cell table.
            SheetData sheetData = worksheetPart.Worksheet.AppendChild(new SheetData());

            int rowIndex = 1;
            Row rowHeader = new Row();
            sheetData.AppendChild(rowHeader);

            int columnIndex = 1;

            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelProductIdCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelProductNameCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelAdminPanelProductPageUrlCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelMarketProductPageUrlCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelDescriptionCaption, CellValues.String, columnIndex++, rowIndex, 2));

            rowIndex++;
            foreach (var product in productsInfo)
            {
                cancellationToken.ThrowIfCancellationRequested();

                Row row = new Row();
                sheetData.AppendChild(row);

                columnIndex = 1;

                row.AppendChild(CommonExcelProvider.ConstructCell(product.ProductId.ToString(), CellValues.Number, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.ProductName, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.AdminPanelProductPageUrl, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.MarketProductPageUrl, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.Description, CellValues.String, columnIndex++, rowIndex));

                rowIndex++;
            }

            cancellationToken.ThrowIfCancellationRequested();

            workbookpart.Workbook.Save();

            spreadsheetDocument.Dispose();

            return filePath;
        }

        private async Task<string> GenerateNoRevitFileExcelAsync(List<HealthDashboardExportNoRevitFileDto> productsInfo, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            string excelName = $"No Revit File Excel {Guid.NewGuid()}";
            string filePath = Path.Combine(Path.GetTempPath(), $"{excelName}.xlsx");

            // Create a spreadsheet document by supplying the filepath.
            // By default, AutoSave = true, Editable = true, and Type = xlsx.
            SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Create(filePath, SpreadsheetDocumentType.Workbook);

            // Add a WorkbookPart to the document.
            WorkbookPart workbookpart = spreadsheetDocument.AddWorkbookPart();
            workbookpart.Workbook = new Workbook();

            // Add a WorksheetPart to the WorkbookPart.
            WorksheetPart worksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            worksheetPart.Worksheet = new Worksheet();

            Fonts fonts = GetFonts();

            Fills fills = GetFills();

            Borders borders = GetBorders();

            CellFormats cellFormats = GetCellFormats();

            Stylesheet styleSheet = new Stylesheet(fonts, fills, borders, cellFormats);

            // Adding style
            WorkbookStylesPart stylePart = workbookpart.AddNewPart<WorkbookStylesPart>();
            stylePart.Stylesheet = styleSheet;
            stylePart.Stylesheet.Save();

            //columns
            Columns columns = new Columns();
            int columsCount = 5;

            for (uint headerColumnIndex = 1; headerColumnIndex < columsCount + 1; headerColumnIndex++)
                columns.AppendChild(new Column { Min = headerColumnIndex, Max = headerColumnIndex, Width = 25, CustomWidth = true });

            worksheetPart.Worksheet.AppendChild(columns);
            workbookpart.Workbook.Save();

            //Sheets
            Sheets sheets = workbookpart.Workbook.AppendChild(new Sheets());

            //Append a new worksheet and associate it with the workbook.
            Sheet sheet = new Sheet()
            {
                Id = spreadsheetDocument.WorkbookPart.
                GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = excelName
            };
            sheets.Append(sheet);
            workbookpart.Workbook.Save();

            // Get the sheetData cell table.
            SheetData sheetData = worksheetPart.Worksheet.AppendChild(new SheetData());

            int rowIndex = 1;
            Row rowHeader = new Row();
            sheetData.AppendChild(rowHeader);

            int columnIndex = 1;

            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelProductIdCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelProductNameCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelAdminPanelProductPageUrlCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelMarketProductPageUrlCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelRevitUploadUrlCaption, CellValues.String, columnIndex++, rowIndex, 2));

            rowIndex++;
            foreach (var product in productsInfo)
            {
                cancellationToken.ThrowIfCancellationRequested();

                Row row = new Row();
                sheetData.AppendChild(row);

                columnIndex = 1;

                row.AppendChild(CommonExcelProvider.ConstructCell(product.ProductId.ToString(), CellValues.Number, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.ProductName, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.AdminPanelProductPageUrl, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.MarketProductPageUrl, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.RevitFileUrl, CellValues.String, columnIndex++, rowIndex));

                rowIndex++;
            }

            cancellationToken.ThrowIfCancellationRequested();

            workbookpart.Workbook.Save();

            spreadsheetDocument.Dispose();

            return filePath;
        }

        private async Task<string> GenerateRevitMissingExtensionFileExcelAsync(List<HealthDashboardExportRevitWithoutDescriptionDto> productsInfo, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            string excelName = $"Revit Missing Extension {Guid.NewGuid()}";
            string filePath = Path.Combine(Path.GetTempPath(), $"{excelName}.xlsx");

            // Create a spreadsheet document by supplying the filepath.
            // By default, AutoSave = true, Editable = true, and Type = xlsx.
            SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Create(filePath, SpreadsheetDocumentType.Workbook);

            // Add a WorkbookPart to the document.
            WorkbookPart workbookpart = spreadsheetDocument.AddWorkbookPart();
            workbookpart.Workbook = new Workbook();

            // Add a WorksheetPart to the WorkbookPart.
            WorksheetPart worksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            worksheetPart.Worksheet = new Worksheet();

            Fonts fonts = GetFonts();

            Fills fills = GetFills();

            Borders borders = GetBorders();

            CellFormats cellFormats = GetCellFormats();

            Stylesheet styleSheet = new Stylesheet(fonts, fills, borders, cellFormats);

            // Adding style
            WorkbookStylesPart stylePart = workbookpart.AddNewPart<WorkbookStylesPart>();
            stylePart.Stylesheet = styleSheet;
            stylePart.Stylesheet.Save();

            //columns
            Columns columns = new Columns();
            int columsCount = 5;

            for (uint headerColumnIndex = 1; headerColumnIndex < columsCount + 1; headerColumnIndex++)
                columns.AppendChild(new Column { Min = headerColumnIndex, Max = headerColumnIndex, Width = 25, CustomWidth = true });

            worksheetPart.Worksheet.AppendChild(columns);
            workbookpart.Workbook.Save();

            //Sheets
            Sheets sheets = workbookpart.Workbook.AppendChild(new Sheets());

            //Append a new worksheet and associate it with the workbook.
            Sheet sheet = new Sheet()
            {
                Id = spreadsheetDocument.WorkbookPart.
                GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = excelName
            };
            sheets.Append(sheet);
            workbookpart.Workbook.Save();

            // Get the sheetData cell table.
            SheetData sheetData = worksheetPart.Worksheet.AppendChild(new SheetData());

            int rowIndex = 1;
            Row rowHeader = new Row();
            sheetData.AppendChild(rowHeader);

            int columnIndex = 1;

            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelProductIdCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelProductNameCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelAdminPanelProductPageUrlCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelMarketProductPageUrlCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelFileIdCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelRevitFileNameCaption, CellValues.String, columnIndex++, rowIndex, 2));

            rowIndex++;
            foreach (var product in productsInfo)
            {
                cancellationToken.ThrowIfCancellationRequested();

                Row row = new Row();
                sheetData.AppendChild(row);

                columnIndex = 1;

                row.AppendChild(CommonExcelProvider.ConstructCell(product.ProductId.ToString(), CellValues.Number, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.ProductName, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.AdminPanelProductPageUrl, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.MarketProductPageUrl, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.FileId, CellValues.Number, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.FileName, CellValues.String, columnIndex++, rowIndex));

                rowIndex++;
            }

            cancellationToken.ThrowIfCancellationRequested();

            workbookpart.Workbook.Save();

            spreadsheetDocument.Dispose();

            return filePath;
        }

        private async Task<string> GenerateProductsWithZipFilesExcelAsync(ICollection<HealthDashboardExportProductWithZipFilesDto> productsInfo, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            string excelName = $"Products with zip files {Guid.NewGuid()}";
            string filePath = Path.Combine(Path.GetTempPath(), $"{excelName}.xlsx");

            // Create a spreadsheet document by supplying the filepath.
            // By default, AutoSave = true, Editable = true, and Type = xlsx.
            SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Create(filePath, SpreadsheetDocumentType.Workbook);

            // Add a WorkbookPart to the document.
            WorkbookPart workbookpart = spreadsheetDocument.AddWorkbookPart();
            workbookpart.Workbook = new Workbook();

            // Add a WorksheetPart to the WorkbookPart.
            WorksheetPart worksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            worksheetPart.Worksheet = new Worksheet();

            Fonts fonts = GetFonts();

            Fills fills = GetFills();

            Borders borders = GetBorders();

            CellFormats cellFormats = GetCellFormats();

            Stylesheet styleSheet = new Stylesheet(fonts, fills, borders, cellFormats);

            // Adding style
            WorkbookStylesPart stylePart = workbookpart.AddNewPart<WorkbookStylesPart>();
            stylePart.Stylesheet = styleSheet;
            stylePart.Stylesheet.Save();

            //columns
            Columns columns = new Columns();
            int columsCount = 5;

            for (uint headerColumnIndex = 1; headerColumnIndex < columsCount + 1; headerColumnIndex++)
                columns.AppendChild(new Column { Min = headerColumnIndex, Max = headerColumnIndex, Width = 25, CustomWidth = true });

            worksheetPart.Worksheet.AppendChild(columns);
            workbookpart.Workbook.Save();

            //Sheets
            Sheets sheets = workbookpart.Workbook.AppendChild(new Sheets());

            //Append a new worksheet and associate it with the workbook.
            Sheet sheet = new Sheet()
            {
                Id = spreadsheetDocument.WorkbookPart.
                GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = excelName
            };
            sheets.Append(sheet);
            workbookpart.Workbook.Save();

            // Get the sheetData cell table.
            SheetData sheetData = worksheetPart.Worksheet.AppendChild(new SheetData());

            int rowIndex = 1;
            Row rowHeader = new Row();
            sheetData.AppendChild(rowHeader);

            int columnIndex = 1;

            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelProductIdCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelProductNameCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelAdminPanelProductPageUrlCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelMarketProductPageUrlCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(HealthDashboardConstants.ExcelZipFilesCaption, CellValues.String, columnIndex++, rowIndex, 2));

            rowIndex++;
            foreach (var product in productsInfo)
            {
                cancellationToken.ThrowIfCancellationRequested();

                Row row = new Row();
                sheetData.AppendChild(row);

                columnIndex = 1;

                row.AppendChild(CommonExcelProvider.ConstructCell(product.ProductId.ToString(), CellValues.Number, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.ProductName, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.AdminPanelProductPageUrl, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.MarketProductPageUrl, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.ZipFiles, CellValues.String, columnIndex++, rowIndex));

                rowIndex++;
            }

            cancellationToken.ThrowIfCancellationRequested();

            workbookpart.Workbook.Save();

            spreadsheetDocument.Dispose();

            return filePath;
        }

        #endregion

        #region Private helper methods

        private Fonts GetFonts()
        {
            return new Fonts(
                new Font( // Index 0 - default
                    new FontSize() { Val = 10 }
                ));
        }

        private Fills GetFills()
        {
            return new Fills(
                    new Fill(new PatternFill() { PatternType = PatternValues.None }), // Index 0 - default
                    new Fill(new PatternFill(new ForegroundColor { Rgb = new HexBinaryValue() { Value = "FFE7E6E6" } }) { PatternType = PatternValues.Solid }), // Index 1 - default
                    new Fill(new PatternFill(new ForegroundColor { Rgb = new HexBinaryValue() { Value = "FFBDD7EE" } }) { PatternType = PatternValues.Solid }) // Index 2 - header
                );
        }

        private Borders GetBorders()
        {
            return new Borders(
                    new Border() // index 0 default
                );
        }

        private CellFormats GetCellFormats()
        {
            return new CellFormats(
                    new CellFormat(), // default
                    new CellFormat { FontId = 0, FillId = 1, BorderId = 0, ApplyFill = true }, // body
                    new CellFormat { FontId = 0, FillId = 2, BorderId = 0, ApplyFill = true }, // header
                    new CellFormat { FontId = 0, FillId = 2, BorderId = 0, ApplyFill = true, Alignment = new Alignment { TextRotation = 90, WrapText = true } } // header for certificate
                );
        }
        #endregion

        #endregion
    }
}