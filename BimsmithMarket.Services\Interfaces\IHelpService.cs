﻿using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IHelpService
    {
        Task<object> ArticleFeedbackAsync(IUnitOfWork unitOfWork, int count, int offset);
        Task<object> AddCategoryAsync(IUnitOfWork unitOfWork, AddEditHelpCategoryModel model, string userId);
        Task<object> EditCategoryAsync(IUnitOfWork unitOfWork, AddEditHelpCategoryModel model, string userId);
        Task DeleteCategoryAsync(IUnitOfWork unitOfWork, int id);
        Task<object> CategoryListAsync(IUnitOfWork unitOfWork, int count, int offset);
        Task<object> GetCategoryAsync(IUnitOfWork unitOfWork, int id);
        Task<object> AddArticleAsync(IUnitOfWork unitOfWork, AddEditHelpArticleModel model, string userId);
        Task<object> EditArticleAsync(IUnitOfWork unitOfWork, AddEditHelpArticleModel model, string userId);
        Task DeleteArticleAsync(IUnitOfWork unitOfWork, int id, string userId);
        Task<object> ArticleListAsync(IUnitOfWork unitOfWork, int count, int offset);
        Task<object> GetArticleAsync(IUnitOfWork unitOfWork, int id);
        Task<int> WebsiteAddArticleReactionAsync(IUnitOfWork unitOfWork, HelpAtricleReactionModel model);
        Task<object> WebsiteCategoryListAsync(IUnitOfWork unitOfWork);
        Task<object> WebsiteCategoryAsync(IUnitOfWork unitOfWork, string categoryUrl);
        Task<object> WebsiteArticleAsync(IUnitOfWork unitOfWork, string articleUrl);
        Task<object> WebsiteArticleListAsync(IUnitOfWork unitOfWork, int? categoryId, int count, int offset, string query);
    }
}
