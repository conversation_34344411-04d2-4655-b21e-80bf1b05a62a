﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BIMsmithMarket.DataLayer.Migrations
{
    public partial class Add_RequestPricing_To_Manufacturer : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "RequestPricingEmail",
                table: "Manufacturers",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RequestPricingEmailCC",
                table: "Manufacturers",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "RequestPricingEnabled",
                table: "Manufacturers",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "RequestPricingEmail",
                table: "Manufacturers");

            migrationBuilder.DropColumn(
                name: "RequestPricingEmailCC",
                table: "Manufacturers");

            migrationBuilder.DropColumn(
                name: "RequestPricingEnabled",
                table: "Manufacturers");
        }
    }
}
