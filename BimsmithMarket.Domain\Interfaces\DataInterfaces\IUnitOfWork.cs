﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.DBModels.DynamicTranslationDbModels;
using BIMsmithMarket.Domain.DBModels.PaymentDbModels;
using BIMsmithMarket.Domain.DBModels.RevitProcessing;
using Microsoft.EntityFrameworkCore;
using System;
using System.Data;
using System.Threading;
using System.Threading.Tasks;

namespace BIMsmithMarket.Domain.Interfaces.DataInterfaces
{
    public interface IUnitOfWork : IDisposable
    {
        void BeginTransaction();

        void BeginTransaction(IsolationLevel isolationLevel);

        void RollbackTransaction();

        void CommitTransaction();

        void Detached(object entity);

        void RejectChanges();

        //-----------------------------------------------
        IRepository<ApplicationUser> UserRepository { get; }
        IRepository<ApplicationUserRole> UserRoleRepository { get; }
        IRepository<ApplicationRole> RoleRepository { get; }
        IRepository<ApplicationUserClaim> UserClaimReporisory { get; }
        IRepository<ApplicationUserLogin> UserLoginRepository { get; }
        IRepository<Manufacturer> ManufacturerRepository { get; }
        IRepository<Photo> PhotoRepository { get; }
        IRepository<Product> ProductRepository { get; }
        IRepository<RelatedProduct> RelatedProductRepository { get; }
        IRepository<Category> CategoryRepository { get; }
        IRepository<KeyStat> KeyStatRepository { get; }
        IRepository<CategoryKeyStat> CategoryKeyStatRepository { get; }
        IRepository<File> FileRepository { get; }
        IRepository<ProductPhoto> ProductPhotoRepository { get; }
        IRepository<ProductSample> ProductSampleRepository { get; }
        IRepository<ProductFile> ProductFileRepository { get; }
        IRepository<ProductLine> ProductLineRepository { get; }
        IRepository<ForgeProductLineIds> ForgeProductLineIdsRepository { get; }
        IRepository<ProductStats> ProductStatsRepository { get; }
        IRepository<ProductRating> ProductRatingRepository { get; }
        IRepository<ProductCertificate> ProductCertificateRepository { get; }
        IRepository<ProductCategory> ProductCategoryRepository { get; }
        IRepository<QualityItem> QualityItemRepository { get; }
        IRepository<ProductQualityItem> ProductQualityItemRepository { get; }
        IRepository<UserBIMsmithManufacturer> UserBIMsmithManufacturerRepository { get; }
        IRepository<ManufacturerAdminUser> ManufacturerAdminUserRepository { get; }
        IRepository<UserBIMsmithLLManufacturer> UserBIMsmithLLManufacturerRepository { get; }
        IRepository<UserBIMsmithLTManufacturer> UserBIMsmithLTManufacturerRepository { get; }
        IRepository<UserBIMsmithBIMManufacturer> UserBIMsmithBIMManufacturerRepository { get; }
        IRepository<UserBIMsmithProduct> UserBIMsmithProductRepository { get; }
        IRepository<Address> AddressRepository { get; }
        IRepository<ReportItem> ReportItemRepository { get; }
        IRepository<Masterformat> MasterformatRepository { get; }
        IRepository<Omniclass> OmniclassRepository { get; }
        IRepository<Uniclass> UniclassRepository { get; }
        IRepository<ProductUniclass> ProductUniclassRepository { get; }
        IRepository<ProductOmniclass> ProductOmniclassRepository { get; }
        IRepository<Uniformat> UniformatRepository { get; }
        IRepository<ProductUniformat> ProductUniformatRepository { get; }
        IRepository<ProductMasterformat> ProductMasterformatRepository { get; }
        IRepository<KeyStatUnit> KeyStatUnitRepository { get; }
        IRepository<Cisfb> CisfbRepository { get; }
        IRepository<ProductCisfb> ProductCisfbRepository { get; }
        IRepository<KeyStatValueList> KeyStatValueListRepository { get; }
        IRepository<KeyStatUnitRelation> KeyStatUnitRelationRepository { get; }
        IRepository<Localization> LocalizationRepository { get; }
        IRepository<News> NewsRepository { get; }
        IRepository<NewsCategory> NewsCategoryRepository { get; }
        IRepository<SearchHistory> SearchHistoryRepository { get; }
        IRepository<Setting> SettingRepository { get; }
        IRepository<Synonym> SynonymRepository { get; }
        IRepository<BlogPost> BlogPostRepository { get; }
        IRepository<BlogCategory> BlogCategoryRepository { get; }
        IRepository<BlogComment> BlogCommentRepository { get; }
        IRepository<BlogMentionedEntry> BlogMentionedEntryRepository { get; }
        IRepository<BlogTargetType> BlogTargetTypeRepository { get; }
        IRepository<BlogCategoryTargetType> BlogCategoryTargetTypeRepository { get; }
        IRepository<EmailSubscriber> EmailSubscriberRepository { get; }
        IRepository<ProductLineFile> ProductLineFileRepository { get; }
        IRepository<ProductLineStats> ProductLineStatsRepository { get; }
        IRepository<ProductLineCertificate> ProductLineCertificateRepository { get; }
        IRepository<ProductLineQualityItem> ProductLineQualityItemRepository { get; }
        IRepository<ManufacturerFile> ManufacturerFileRepository { get; }
        IRepository<ManufacturerAdditionalFile> ManufacturerAdditionalFileRepository { get; }
        IRepository<ManufacturerPhoto> ManufacturerPhotoRepository { get; }
        IRepository<ProjectDataType> ProjectDataTypeRepository { get; }
        IRepository<Starter> StarterRepository { get; }
        IRepository<StarterProductLines> StarterProductLinesRepository { get; }
        IRepository<PluginFile> PluginFileRepository { get; }
        IRepository<ULRatingSystemSustainableCredit> ULRatingSystemSustainableCreditRepository { get; }
        IRepository<ULCertificate> ULCertificateRepository { get; }
        IRepository<ULStandardNumber> ULStandardNumberRepository { get; }
        IRepository<Company> CompanyRepository { get; }
        IRepository<AttachmentOrder> AttachmentOrderRepository { get; }
        IRepository<VanityHistory> VanityHistoryRepository { get; }
        IRepository<HelpCategory> HelpCategoryRepository { get; }
        IRepository<HelpArticle> HelpArticleRepository { get; }
        IRepository<Event> EventRepository { get; }
        IRepository<Announcement> AnnouncementRepository { get; }
        IRepository<Detail> DetailRepository { get; }
        IRepository<DetailApplication> DetailApplicationRepository { get; }
        IRepository<DetailDetailApplication> DetailDetailApplicationRepository { get; }
        IRepository<DetailRating> DetailRatingRepository { get; }
        IRepository<DetailFile> DetailFileRepository { get; }
        IRepository<DetailPhoto> DetailPhotoRepository { get; }
        IRepository<ProductDetail> ProductDetailRepository { get; }
        IRepository<DetailMasterformat> DetailMasterformatRepository { get; }
        IRepository<RelatedDetail> RelatedDetailRepository { get; }
        IRepository<DetailScale> DetailScaleRepository { get; }
        IRepository<FeatureSetting> FeatureSettingRepository { get; }
        IRepository<NewsTarget> NewsTargetRepository { get; }
        IRepository<ManufacturerStyleFile> ManufacturerStyleFileRepository { get; }
        IRepository<Price> PriceRepository { get; }
        IRepository<PaymentPlan> PaymentPlanRepository { get; }
        IRepository<ProductPrice> ProductPriceRepository { get; }
        IRepository<UserPaidProduct> UserPaidProductRepository { get; }
        IRepository<ManufacturerAnnouncement> ManufacturerAnnouncementRepository { get; }
        IRepository<HealthDashboardAccess> HealthDashboardAccessRepository { get; }
        IRepository<DynamicTranslation> DynamicTranslationRepository { get; }
        IRepository<TranslatableEntity> TranslatableEntityRepository { get; }
        IRepository<TranslatableEntityField> TranslatableEntityFieldRepository { get; }
        IRepository<PublishedDisplayOrderView> PublishedDisplayOrderViewRepository { get; }
        IRepository<ProductDisplayOrderView> ProductDisplayOrderViewRepository { get; }
        IRepository<StaticExcelFile> StaticExcelFileRepository { get; }
        IRepository<StaticExcelProductError> StaticExcelProductErrorRepository { get; }
        IRepository<DropboxSetting> DropboxSettingRepository { get; }
        IRepository<CustomCategoryIcon> CustomCategoryIconRepository { get; }
        IRepository<SalesRepresentative> SalesRepresentativeRepository { get; }
        IRepository<ChangeLog> ChangeLogRepository { get; }
        IRepository<Note> NoteRepository { get; }
        IRepository<NoteNotificationUser> NoteNotificationUserRepository { get; }
        IRepository<RequestPricingUser> RequestPricingUserRepository { get; }
        IRepository<RevitProcess> RevitProcessRepository { get; }
        IRepository<RevitProcessProduct> RevitProcessProductRepository { get; }
        IRepository<RevitJobRevitParameterMapping> RevitJobRevitParameterMappingRepository { get; }
        IRepository<RevitJob> RevitJobRepository { get; }
        IRepository<RevitProcessProjectDataType> RevitProcessProjectDataTypeRepository { get; }
        IRepository<RevitProcessRevitParameterMapping> RevitProcessRevitParameterMappingRepository { get; }
        //------------------------------------------------


        DbContext CurrentDbContext { get; }

        int Save();

        Task<int> SaveAsync(CancellationToken cancellationToken = default);
    }
}