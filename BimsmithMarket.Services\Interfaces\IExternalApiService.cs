﻿using Azure.Storage.Blobs.Specialized;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.ExternalApi;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces;

public interface IExternalApiService
{
    Task<object> SearchProductsAsync(string query,
        bool isMasterspec,
        bool onlyFree,
        int count,
        int offset,
        string regionId,
        IUnitOfWork unitOfWork);

    Task<BlockBlobClient> DownloadProjectFilesAsync(int productId, HttpRequest request, string referer, string displayUrl, IUnitOfWork unitOfWork);

    Task<BlockBlobClient> DownloadTechnicalDocsAsync(int productId, HttpRequest request, string referer, string displayUrl, IUnitOfWork unitOfWork);

    Task<PaginationListDto<ExternalApiTechnicalDocsMappingDto>> TechnicalDocsMappingAsync(int productId, IUnitOfWork unitOfWork);

    Task<ExternalOperationResultDto> InitProductsUpdateAsync(ExternalApiUpdateProductsDto model, string userId, IUnitOfWork unitOfWork);

    Task ProcessExternalApiUpdateProductsQueueAsync(IUnitOfWork unitOfWork);
}