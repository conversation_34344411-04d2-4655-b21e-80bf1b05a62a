﻿using GhostscriptSharp;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using Spire.Doc;
using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;

namespace BIMsmithMarket.Core.Providers
{
    public class ThumbnailProvider
    {
        private static string[] _wordFormats = new string[]
        {
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/rtf"
        };

        public static ThumbnailResult GetAndSaveThumbnail(string filePath, string mediaType, int attachId)
        {
            ThumbnailResult result = new ThumbnailResult();

            string[] pdfArgs = new string[0];

            try
            {
                string folderThumbnail = Path.GetDirectoryName(filePath);
                var thumbnailPath = Path.Combine(folderThumbnail, string.Format("{0}_p.png", attachId));

                if (!Directory.Exists(folderThumbnail))
                {
                    Directory.CreateDirectory(folderThumbnail);
                }

                if (mediaType.StartsWith("image"))
                {
                    SaveImageThumbnail(filePath, thumbnailPath);
                    result.HasError = false;
                    result.FilePath = thumbnailPath;
                }
                else if (mediaType == "application/pdf")
                {
                    GhostscriptWrapper.SetDllDirectory(folderThumbnail);

                    var thumbnailPDFPath = Path.Combine(folderThumbnail, string.Format("{0}_p.jpg", attachId));

                    pdfArgs = GhostscriptWrapper.GetArgs(filePath, thumbnailPDFPath, 1, 96, 96);

                    GhostscriptWrapper.GeneratePageThumb(filePath, thumbnailPDFPath, 1, 96, 96);

                    SaveImageThumbnail(thumbnailPDFPath, thumbnailPath);
                    File.Delete(thumbnailPDFPath);
                    result.HasError = false;
                    result.FilePath = thumbnailPath;
                }
                else if (mediaType == "text/plain")
                {
                    SaveImageThumbnailForTxt(filePath, thumbnailPath);
                    result.HasError = false;
                    result.FilePath = thumbnailPath;
                }
                else if (_wordFormats.Contains(mediaType))
                {
                    string thumbnailWordPath = Path.Combine(folderThumbnail, string.Format("{0}_p.jpg", attachId));
                    Document doc = new();
                    doc.LoadFromFile(filePath);
                    using Stream imageStream = doc.SaveToImages(0, ImageFormat.Png);
                    using (FileStream fileStream = File.Create(thumbnailWordPath))
                    {
                        imageStream.CopyTo(fileStream);
                    }
                    SaveImageThumbnail(thumbnailWordPath, thumbnailPath);
                    File.Delete(thumbnailWordPath);
                    result.HasError = false;
                    result.FilePath = thumbnailPath;
                }
                else
                {
                    result.HasError = true;
                }
            }
            catch (Exception e)
            {
                result.Error = string.Format("{0} args:{1}", e.Message, string.Join(" ; ", pdfArgs));
                result.HasError = true;
            }
            return result;
        }

        private static void SaveImageThumbnail(string srcPath, string desPath)
        {
            using SixLabors.ImageSharp.Image original = SixLabors.ImageSharp.Image.Load(srcPath);
            double scale = Math.Max(original.Height, original.Width) / 250d;
            original.Mutate(x => x.Resize(new SixLabors.ImageSharp.Size((int)(original.Width / scale), (int)(original.Height / scale))));
            original.SaveAsPng(desPath);
        }

        private static void SaveImageThumbnailForTxt(string txtFilePath, string desPath)
        {
            string backgroundPath = Path.Combine(Directory.GetCurrentDirectory(), "App_Data\\Files\\img\\text_preview.png");
            using (System.Drawing.Image backgroundImage = System.Drawing.Image.FromFile(backgroundPath))
            {
                using (var b = new Bitmap(250, 250))
                {
                    b.SetResolution(backgroundImage.HorizontalResolution, backgroundImage.VerticalResolution);
                    using (var g = Graphics.FromImage(b))
                    {
                        g.Clear(System.Drawing.Color.White);

                        g.DrawImage(backgroundImage, new System.Drawing.Point(0, 0));

                        System.Drawing.Font myFont = new System.Drawing.Font("Arial", 7);

                        System.Drawing.Drawing2D.LinearGradientBrush myBrush = new
                               System.Drawing.Drawing2D.LinearGradientBrush(new System.Drawing.RectangleF(0, 0, 10, 10),
                               System.Drawing.Color.Black, System.Drawing.Color.Black, System.Drawing.Drawing2D.LinearGradientMode.Horizontal);

                        string text = File.ReadAllText(txtFilePath);

                        g.DrawString(text,
                            myFont, myBrush, new System.Drawing.RectangleF(45, 65, 165, 170));
                    }

                    b.Save(desPath, ImageFormat.Png);
                }
            }
        }

        public class ThumbnailResult
        {
            public bool HasError { get; set; }

            public string FilePath { get; set; }

            public string Error { get; set; }
        }
    }
}
