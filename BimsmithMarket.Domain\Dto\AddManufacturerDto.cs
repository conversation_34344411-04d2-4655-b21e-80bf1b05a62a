﻿using BIMsmithMarket.Domain.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Dto
{
    public class AddManufacturerDto
    {
        [Required]
        [StringLength(200)]
        public string Name { get; set; }

        public string ManufacturerHubTitle { get; set; }

        public string ManufacturerHubSubtitle { get; set; }

        public string Note { get; set; }

        public string ForgeManufacturerId { get; set; }

        public string Site { get; set; }

        public string Description { get; set; }

        public int? PhotoId { get; set; }

        public int? ImageId { get; set; }

        public int? OriginalImageId { get; set; }

        public string PhoneMask { get; set; }

        public string PhoneNumber { get; set; }

        public string PageSetting { get; set; }

        public string Synonyms { get; set; }

        public string AnalyticsSetting { get; set; }

        public string NodeSetting { get; set; }

        public AddressDto Address { get; set; }

        public bool IsOnForge { get; set; }

        public bool IsOnMarket { get; set; }

        public bool LAndL { get; set; }

        public bool SendULInfo { get; set; }

        public bool LetsTalk { get; set; }

        public string LetsTalkSettings { get; set; }

        public string VideoUrl { get; set; }

        public string HubVanityURL { get; set; }

        public string ForgeUrl { get; set; }

        public string MarketUrl { get; set; }

        public bool SubscribeButton { get; set; }

        public string MetaDescription { get; set; }

        public string MetaKeywords { get; set; }

        public string MetaTitle { get; set; }

        public string EmailLandL { get; set; }

        public string EmailLetsTalk { get; set; }

        public string EmailLandLCC { get; set; }

        public string EmailLetsTalkCC { get; set; }

        public string OwnerId { get; set; }

        public bool IncludeRevitPlugin { get; set; }

        public int? RevitPluginPhotoId { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        public string Keywords { get; set; }

        public bool Published { get; set; }

        public bool PublishToPartner { get; set; }

        public bool Staging { get; set; }

        public string ManualUrl { get; set; }

        public ManufacturerType Type { get; set; }

        public string SwatchboxManufacturerId { get; set; }

        public List<FileDto> Files { get; set; }

        public List<FileDto> AdditionalFiles { get; set; }

        public string DetailsMicrositeSettings { get; set; }

        public int? AnnouncementId { get; set; }

        public List<ManufacturerStyleFileDto> StyleFiles { get; set; }

        public bool ShowFooterAd { get; set; }

        public string FooterAdUrl { get; set; }

        public int? FooterAdImageId { get; set; }

        public bool UseCustomLoginRegisterScreen { get; set; }

        public string CustomSignInBackgroundColor { get; set; }

        public string CustomSignInTextColor { get; set; }

        public string CustomSignInDescription { get; set; }

        public int? CustomSignInLogoImageId { get; set; }

        public List<int> ChildIds { get; set; }

        public bool IsParentManufacturer { get; set; }
        
        [StringLength(200)]
        public string CustomMicrositeName { get; set; }

        public bool RequestPricingEnabled { get; set; }

        [StringLength(100)]
        public string RequestPricingEmail { get; set; }

        [StringLength(100)]
        public string RequestPricingEmailCC { get; set; }
    }

    public class EditManufacturerDto : AddManufacturerDto
    {
        [Required]
        public int Id { get; set; }
    }

    public class AddressDto
    {
        public string Address1 { get; set; }

        public string Address2 { get; set; }

        public string Country { get; set; }

        public string State { get; set; }

        public string City { get; set; }

        public string Province { get; set; }

        public string Zip { get; set; }
    }

    public class ManufacturerListDto
    {
        public int Id { get; set; }

        public string Name { get; set; }

        public string ManufacturerHubTitle { get; set; }

        public string ManufacturerHubSubtitle { get; set; }

        public string Site { get; set; }

        public string HubVanityURL { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        public ManufacturerType Type { get; set; }

        public ManufacturerListLogoDto Logo { get; set; }

        public int IncludeRevitPlugin { get; set; }

        public ManufacturerListRevitIconDto RevitIcon { get; set; }

        public int ProductsCount { get; set; }

        public DateTime UpdateDate { get; set; }

        public bool Published { get; set; }

        public bool PublishToPartner { get; set; }

        public string DetailsMicrositeSettings { get; set; }

        public HealthCheckStatus HealthCheckStatus { get; set; }

        public float Weight { get; set; }
    }

    public class ManufacturerListLogoDto
    {
        public int? Id { get; set; }

        public string Small { get; set; }

        public string Big { get; set; }
    }

    public class ManufacturerListRevitIconDto
    {
        public int? Id { get; set; }

        public string SmallUrl { get; set; }

        public string OriginalUrl { get; set; }
    }
}