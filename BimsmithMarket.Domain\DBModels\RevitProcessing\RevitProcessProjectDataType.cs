﻿using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels.RevitProcessing
{
    public class RevitProcessProjectDataType : BaseEntity
    {
        public int RevitProcessId { get; set; }

        public int ProjectDataTypeId { get; set; }

        [ForeignKey("RevitProcessId")]
        public virtual RevitProcess RevitProcess { get; set; }

        [ForeignKey("ProjectDataTypeId")]
        public virtual ProjectDataType ProjectDataType { get; set; }
    }
}