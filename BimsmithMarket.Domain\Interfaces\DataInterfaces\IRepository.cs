﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Domain.Interfaces.DataInterfaces
{
    public interface IRepository<T>
    {
        T GetById(int id);

        Task<T> GetByIdAsync(int id);

        IQueryable<T> GetAll();

        IQueryable<T> GetAllAsNoTracking();

        void Edit(T entity);

        void Insert(T entity);

        void InsertRange(IEnumerable<T> entities);

        void Delete(T entity);

        void Delete(IEnumerable<T> entities);

        void Detached(T entity);
    }
}