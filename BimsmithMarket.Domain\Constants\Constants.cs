﻿using BIMsmithMarket.Domain.DBModels;
using System.Collections.Generic;
using System.Collections.ObjectModel;

namespace BIMsmithMarket.Domain.Constants
{
    public static class Constants
    {
        public const string UrlVanityRegax = @"^([\(\)A-Za-z0-9_-])+$";
        public const string ReplaceUrlVanityRegax = "([^()A-Z0-9a-z_-]+)";
        /// <summary>
        /// The thumbnail media types
        /// </summary>
        public static readonly IList<string> ThumbnailMediaTypes = new ReadOnlyCollection<string>(
            new List<string> { "application/pdf", "text/plain", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/rtf" });

        public static readonly Dictionary<DetailOrientation, string> DetailOrientationCaptions = new Dictionary<DetailOrientation, string>()
        {
            { DetailOrientation.Elevation, "Elevation" },
            { DetailOrientation.Plan, "Plan" },
            { DetailOrientation.Section, "Section" },
            { DetailOrientation.ThreeDAxon, "3D Axon" }
        };
    }
}