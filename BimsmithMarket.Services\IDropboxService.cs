﻿using BIMsmithMarket.Domain.Dto.DropboxDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System.IO;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public interface IDropboxService
    {
        Task UpdateDropboxFilesAsync(UpdateDropboxFilesDto model, IUnitOfWork unitOfWork);

        Task<Stream> ExportDropboxLinksAsync(string dropboxPath, string userId, IUnitOfWork unitOfWork);

        Task CheckAuthorizationAsync(string userId, IUnitOfWork unitOfWork);

        Task CompleteOAuthAsync(DropboxOAuthResponseDto model, string userId, IUnitOfWork unitOfWork);
    }
}