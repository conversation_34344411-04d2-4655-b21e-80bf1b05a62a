﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class ULRatingSystemSustainableCredit : BaseEntity
    {
        [StringLength(200)]
        [Required]
        public string Name { get; set; }

        [Required]
        public int ProductId { get; set; }

        /// ------------------------------------------
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }
    }
}