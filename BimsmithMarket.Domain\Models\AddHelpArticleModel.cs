﻿using BIMsmithMarket.Domain.DBModels;
using System;

namespace BIMsmithMarket.Domain.Models
{
    public class AddEditHelpArticleModel
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string VanityUrl { get; set; }
        public string MetaDescription { get; set; }
        public string MetaKeywords { get; set; }
        public string Title { get; set; }
        public string HelpTags { get; set; }
        public bool FAQ { get; set; }
        public string HtmlBody { get; set; }
        public HelpCategoryStatus Status { get; set; }
        public DateTime? PublishedDate { get; set; }
        public int[] HelpCategoriesIds { get; set; }
    }
}