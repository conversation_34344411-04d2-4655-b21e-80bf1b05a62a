{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"MongoDb": "mongodb://MongoDBAdmin:xWyXe9c$bHSDzBv2y!@127.0.0.1:27017/Market-Dev?authSource=admin", "MarketDBConnection": "Data Source=127.0.0.1;Initial Catalog=bimsmith-market-dev;User ID=sa;Password=****************;Connect Timeout=15;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultipleActiveResultSets=true;MultiSubnetFailover=False"}, "BaseLogPath": "C:\\Logs\\DEV\\", "IsDevEnvironment": "true", "LoginDomain": "https://dev.bimsmith.com/", "LoginSecretKey": "Qa123456", "LoginEndPoint": "https://dev.bimsmith.com/api/auth/validatecredentials/", "BimsmithApiToken": "c1R3C79Z9dVl7Ienc3fF2A9pc", "BimsmithApiUrl": "https://dev.bimsmith.com/api", "ForgeStartersEndPoint": "https://forgetaxonomy-uat.bimsmith.com/api/Starters/MarketWeb/Filter", "AdministratorEmails": "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,sba<PERSON><PERSON><PERSON><EMAIL>,<EMAIL>,r<PERSON><EMAIL>,p<PERSON><PERSON><PERSON>@qarea.com,<EMAIL>,r<PERSON><PERSON><PERSON><PERSON>@qarea.us,<EMAIL>,<EMAIL>,and<PERSON><PERSON><PERSON>@qarea.us,<EMAIL>,matsydon<PERSON>@qarea.us,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>", "AnalyticsWebApiBaseAddress": "https://analytics-web-api-dev.bimsmith.com/", "AnalyticsWriterBaseAddress": "https://analytics-writer-api-dev.bimsmith.com", "CBOBaseAddress": "https://bimsmith-translatorapi-dev.bimsmith.com/", "MarketFrontBaseUrl": "https://market-dev.bimsmith.com/", "MarketApiBaseUrl": "https://api-market-dev.bimsmith.com/", "RobotsContent": "User-agent: *;Disallow: /", "ExternalApiLogFilePath": "C:\\LogFiles\\ExternalApi\\DEV\\ExternalApiLog.txt", "ManufacturerBackupsPath": "C:\\Market\\DEV\\Manufacturer Backups\\", "Environment": "dev", "CacheConnection": "host=127.0.0.1;port=6379;instance=search_cache_dev;allowAdmin=true;defaultDatabase=0", "Secret": "vwCVgRND5hD41gkUnKtE3DuLo5z3GVsx"}