﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto.SketchupDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class SketchupService : ISketchupService
    {
        private readonly HttpClient _httpClient;
        private readonly IHttpClientFactory _httpClientFactory;

        public SketchupService(int manufacturerId, IHttpClientFactory httpClientFactory)
        {
            if (ConfigurationHelper.GetValue("Environment") != "prod")
                throw new InvalidInputException("Skecthup push is allowed only on production environment");

            _httpClientFactory = httpClientFactory;
            _httpClient = httpClientFactory.CreateClient();
            _httpClient.BaseAddress = new Uri(ConfigurationHelper.GetValue("SketchupAPI:BaseAddress"));
            string apiKey = string.Empty;
            switch (manufacturerId)
            {
                case SketchupConstants.BrizoManufacturerId: apiKey = ConfigurationHelper.GetValue("SketchupAPI:BrizoAPIKey"); break;
                case SketchupConstants.DeltaFaucetManufacturerId: apiKey = ConfigurationHelper.GetValue("SketchupAPI:DeltaFaucetAPIKey"); break;
                case SketchupConstants.PeerlessFaucetManufacturerId: apiKey = ConfigurationHelper.GetValue("SketchupAPI:PeerlessFaucetAPIKey"); break;
                case SketchupConstants.DanverOutdoorsKitchensManufacturerId: apiKey = ConfigurationHelper.GetValue("SketchupAPI:DanverOutdoorsKitchensAPIKey"); break;
            }
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", apiKey);
        }

        public async Task AddProductsAsync(SketchupProductDto[] products, IUnitOfWork unitOfWork)
        {
            using (var fileDownloadClient = new HttpClient())
            {
#if DEBUG
                int totalCount = products.Length;
                int counter = 0;
#endif
                foreach (var product in products)
                {
                    MultipartFormDataContent formContent = await CreatePayloadAsync(product);
                    var result = await _httpClient.PostAsync("3dw/v1.0/products/", formContent);
                    var details = await result.Content.ReadAsStringAsync();
                    LogHelper.LogInfo(ConfigurationHelper.GetValue("SketchupAPI:LogFilePath"), $"Details of response for product {product.Id} = {details}");

                    if (result.IsSuccessStatusCode)
                    {
                        var whpId = result.Headers.FirstOrDefault(x => x.Key == "WHP-ID").Value.First();
                        var dbProduct = await unitOfWork.ProductRepository.GetByIdAsync(product.Id);
                        dbProduct.SketchupId = whpId;
                        unitOfWork.ProductRepository.Edit(dbProduct);
                        await unitOfWork.SaveAsync();

                        LogHelper.LogInfo(ConfigurationHelper.GetValue("SketchupAPI:LogFilePath"), $"Done for product {product.Id}");
                        LogHelper.LogInfo(ConfigurationHelper.GetValue("SketchupAPI:LogFilePath"), $"WHP-ID = {whpId}");
                    }
#if DEBUG
                    counter++;
                    Debug.WriteLine($"Processed {counter} of {totalCount} ({((float)counter / totalCount) * 100} %)");
#endif
                }
            }
        }

        public async Task UpdateProductsAsync(SketchupProductDto[] products)
        {
#if DEBUG
            int totalCount = products.Length;
            int counter = 0;
#endif
            foreach (var product in products)
            {
                MultipartFormDataContent formContent = await CreatePayloadAsync(product);
                var request = new HttpRequestMessage(new HttpMethod("PATCH"), $"3dw/v1.0/products/{product.SketchupId}");
                request.Content = formContent;
                var result = await _httpClient.SendAsync(request);
                var details = await result.Content.ReadAsStringAsync();
                LogHelper.LogInfo(ConfigurationHelper.GetValue("SketchupAPI:LogFilePath"), $"Details of response for product {product.Id} = {details}");

                if (result.IsSuccessStatusCode)
                {
                    LogHelper.LogInfo(ConfigurationHelper.GetValue("SketchupAPI:LogFilePath"), $"Done update for product {product.Id}");
                }
#if DEBUG
                counter++;
                Debug.WriteLine($"Processed {counter} of {totalCount} ({((float)counter / totalCount) * 100} %)");
#endif
            }
        }

        public async Task<SketchupProductDto[]> GetProductsWithSketchup(SketchupFilterProductsDto model, bool isUpdate, IUnitOfWork unitOfWork)
        {
            var productQuery = unitOfWork.ProductRepository.GetAll()
                                         .Where(x => x.Published
                                                  && x.ManufacturerId == model.ManufacturerId
                                                  && x.ProductFiles.Any(p => p.ProjectDataTypeId == SketchupConstants.SkecthupProjectDataTypeId))
                                         .Include(x => x.ProductFiles).ThenInclude(f => f.File)
                                         .Include(x => x.ProductPhotos).ThenInclude(f => f.Photo)
                                         .AsQueryable();

            if (model.ProductId.HasValue)
                productQuery = productQuery.Where(x => x.Id == model.ProductId);

            if (model.ProductLineId.HasValue)
                productQuery = productQuery.Where(x => x.ProductLineId == model.ProductLineId);

            if (isUpdate)
                productQuery = productQuery.Where(x => x.SketchupId != null);
            else
                productQuery = productQuery.Where(x => x.SketchupId == null);

            Product[] products = await productQuery.AsSplitQuery().ToArrayAsync();
            return products.Adapt<SketchupProductDto[]>();
        }

        #region private methods
        private async Task<MultipartFormDataContent> CreatePayloadAsync(SketchupProductDto dbProduct)
        {
            var product = new
            {
                title = dbProduct.Title ?? string.Empty,
                description = dbProduct.Description ?? string.Empty,
                externalUrl = new Flurl.Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment("product").AppendPathSegment(dbProduct.Id).ToString()
            };

            var formContent = new MultipartFormDataContent();
            var test = JsonConvert.SerializeObject(product);
            formContent.Add(new StringContent(JsonConvert.SerializeObject(product)), "product");
            var fileHttpClient = _httpClientFactory.CreateClient();
            {
                var sketchupFileBytes = await fileHttpClient.GetByteArrayAsync(dbProduct.SkecthupFileUrl);
                formContent.Add(new ByteArrayContent(sketchupFileBytes, 0, sketchupFileBytes.Length), "file", dbProduct.SketchupFileName);
                var thumbnailFileBytes = await fileHttpClient.GetByteArrayAsync(dbProduct.ThumbnailFileUrl);
                formContent.Add(new ByteArrayContent(thumbnailFileBytes, 0, thumbnailFileBytes.Length), "customThumbnailFile", dbProduct.ThumbnailFileName);
            }
            return formContent;
        }
        #endregion
    }
}