﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.Authorization;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// Account controller.
    /// </summary>
#if !DEBUG
    [Authorize]
#endif
    [Route("api/[controller]/[action]")]
    public class AccountController : BaseApiController
    {
        private readonly IAccountService _accountService;
        /// <summary>
        /// The user manager
        /// </summary>
        private readonly IUserService _userService;
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly IAnalyticsService _analyticsService;
        private readonly IAuthorizationService _authorizationService;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly SlackWebHook _slackWebHook;

        public AccountController(
            IAccountService accountService,
            IUserService userService,
            IWebHostEnvironment webHostEnvironment,
            IAnalyticsService analyticsService,
            IAuthorizationService authorizationService,
            IHttpClientFactory httpClientFactory,
            SlackWebHook slackWebHook
            )
        {
            _accountService = accountService;
            _userService = userService;
            _webHostEnvironment = webHostEnvironment;
            _analyticsService = analyticsService;
            _authorizationService = authorizationService;
            _httpClientFactory = httpClientFactory;
            _slackWebHook = slackWebHook;
        }

        /// <summary>
        /// Login user
        /// </summary>
        /// <param name="model">The login model</param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        [Route("/token")]
        public async Task<IActionResult> Login(LoginDto model)
        {
            LoginResultDto loginResult = await LoginUserAsync(model);
            return Ok(loginResult);
        }

        /// <summary>
        /// Checks if user is authorized
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Authorize]
        public async Task<IActionResult> CheckAuthorization()
        {
            return Ok();
        }

        /// <summary>
        /// Log out user
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Authorize]
        public async Task<IActionResult> Logout()
        {
            await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            _authorizationService.InvalidateToken(AuthHelper.GetToken(Request));
            bool browserDisallowsSameSiteNone = BrowserHelper.DisallowsSameSiteNone(Request.Headers["User-Agent"]);
            RemoveCookies(browserDisallowsSameSiteNone);
            return Ok(new SuccessResponseDto { Message = "Success" });
        }

        /// <summary>
        /// Get user info
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Authorize]
        public async Task<IActionResult> GetUserInfo()
        {
            string email = AuthHelper.GetUserInfo(Request, ClaimTypes.Email);
            return Ok(await _userService.GetUserInfoByEmailAsync(email));
        }

        /// <summary>
        /// Get list of my stream
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("/api/account/myBIMsmith/stream/list")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<HttpResponseMessage> MyBIMsmithStreamList()
        {
            var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            var userEmail = AuthHelper.GetUserInfo(Request, ClaimTypes.Email);
            var userToken = Providers.AuthenticationService.GenerateAuthToken(userId, userEmail);
            var url = string.Format("https://bimsmith.com/api/users/{0}/streams?token={1}", userId, userToken);

            HttpClient client = _httpClientFactory.CreateClient();
            return await client.GetAsync(url);
        }

        #region BIMsmithMarket product 

        /// <summary>
        /// Get list of all myBIMsmith products.
        /// </summary>
        /// <param name="q"></param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("/api/account/myBIMsmith/products/list")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> MyBIMsmithProductsList(string q = null, int offset = 0, int count = 10)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
                return Ok(await _accountService.MyBIMsmithProductsList(unitOfWork, userId, q, offset, count));
            }
        }

        /// <summary>
        /// Add product to My BIMsmith list.
        /// </summary>
        /// <param name="productId"></param>
        /// <param name="streamId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("/api/account/myBIMsmith/products/add")]
        public async Task<IActionResult> AddProductToMyBIMsmith(int productId, string streamId)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
                await _accountService.AddProductToMyBIMsmithAsync(unitOfWork, userId, productId, streamId);

                CacheHelper.ClearSpecificCache("*/api/account/myBIMsmith/products/list*");

                return Ok();
            }
        }

        /// <summary>
        /// Remove product from My BIMsmith list.
        /// </summary>
        /// <param name="productId"></param>
        /// <param name="streamId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("/api/account/myBIMsmith/products/remove")]
        public async Task<IActionResult> RemoveProductFromMyBIMsmith(int productId, string streamId)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);

                var userBIMsmithProduct = await unitOfWork.UserBIMsmithProductRepository.GetAll().FirstOrDefaultAsync(a => a.ProductId == productId && a.AddedById == userId);

                if (userBIMsmithProduct != null)
                {
                    unitOfWork.UserBIMsmithProductRepository.Delete(userBIMsmithProduct);
                    await unitOfWork.SaveAsync();
                }
                else
                {
                    return NotFound("Not found the Product in user MyBIMsmith list");
                }

                CacheHelper.ClearSpecificCache("*/api/account/myBIMsmith/products/list*");

                return Ok();
            }
        }

        #endregion

        #region BIMsmithMarket manufacturer 

        /// <summary>
        /// Subscribe to manufacturer.
        /// </summary>
        /// <param name="manufacturerName"></param>
        /// <param name="manufacturerId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("/api/account/myBIMsmith/manufacturer/subscribe")]
        public async Task<IActionResult> SubscribeToManufacturer(string manufacturerName = null, int? manufacturerId = null)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
                string ip = HttpContext?.Connection?.RemoteIpAddress?.ToString();

                if (!manufacturerId.HasValue)
                {
                    manufacturerId = (await unitOfWork.ManufacturerRepository.GetAll().FirstAsync(a => a.Name == manufacturerName)).Id;
                }

                if (!await unitOfWork.UserBIMsmithManufacturerRepository.GetAll()
                    .AnyAsync(a => a.ManufacturerId == manufacturerId && a.AddedById == userId))
                {
                    UserBIMsmithManufacturer userBIMsmithManufacturer = new UserBIMsmithManufacturer();
                    userBIMsmithManufacturer.ManufacturerId = manufacturerId.Value;
                    userBIMsmithManufacturer.AddedById = userId;
                    userBIMsmithManufacturer.AddedDate = DateTime.UtcNow;

                    unitOfWork.UserBIMsmithManufacturerRepository.Insert(userBIMsmithManufacturer);
                    await unitOfWork.SaveAsync();
                }

                //Hook to slack
                var name = unitOfWork.ManufacturerRepository.GetAll().Where(a => a.Id == manufacturerId).Select(a => a.Name).First();
                await _slackWebHook.SendSubscribeManufacturerMessage(AuthHelper.GetUserInfo(Request, ClaimTypes.Email), name);

                GenerateSalesforceEventOptions options = new()
                {
                    EventType = UserEventType.Subscribe,
                    UserId = userId,
                    Referer = Request.GetTypedHeaders()?.Referer?.AbsoluteUri?.ToString(),
                    ManufacturerId = manufacturerId,
                    ManufacturerName = manufacturerName,
                    Ip = ip
                };
                await _analyticsService.SaveSalesForceEventToAnalyticsAsync(await GenerateSalesforceEventViewModelAsync(options));

                CacheHelper.ClearSpecificCache("*/api/Manufacturer/Get*");

                return Ok();
            }
        }


        /// <summary>
        /// Unsubscribe from manufacturer.
        /// </summary>
        /// <param name="manufacturerName"></param>
        /// <param name="manufacturerId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("/api/account/myBIMsmith/manufacturer/unsubscribe")]
        public async Task<IActionResult> UnsubscribeFromManufacturer(string manufacturerName = null, int? manufacturerId = null)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
                string ip = HttpContext?.Connection?.RemoteIpAddress?.ToString();

                if (manufacturerId.HasValue == false)
                {
                    manufacturerId = (await unitOfWork.ManufacturerRepository.GetAll().FirstAsync(a => a.Name == manufacturerName)).Id;
                }

                var userBIMsmithManufacturer = unitOfWork.UserBIMsmithManufacturerRepository.GetAll().Where(a => a.ManufacturerId == manufacturerId.Value && a.AddedById == userId);
                if (userBIMsmithManufacturer != null)
                {
                    unitOfWork.UserBIMsmithManufacturerRepository.Delete(userBIMsmithManufacturer);
                    await unitOfWork.SaveAsync();
                }

                GenerateSalesforceEventOptions options = new()
                {
                    EventType = UserEventType.Unsubscribe,
                    UserId = userId,
                    Referer = Request.GetTypedHeaders()?.Referer?.AbsoluteUri?.ToString(),
                    ManufacturerId = manufacturerId,
                    ManufacturerName = manufacturerName,
                    Ip = ip
                };
                await _analyticsService.SaveSalesForceEventToAnalyticsAsync(await GenerateSalesforceEventViewModelAsync(options));

                CacheHelper.ClearSpecificCache("*/api/Manufacturer/Get*");

                return Ok();
            }
        }


        #endregion

        #region BIMsmithMarket manufacturer lets talk

        /// <summary>
        /// Create lets talk request.
        /// </summary>
        /// <param name="manufacturerName">Name of the manufacturer.</param>
        /// <param name="manufacturerId">The manufacturer identifier.</param>
        /// <param name="productName">Name of the product.</param>
        /// <param name="productLink">The product link.</param>
        /// <param name="timeZone">The time zone.</param>
        /// <param name="message">The message.</param>
        /// <param name="city">The city.</param>
        /// <param name="state">The state.</param>
        /// <param name="country">The country.</param>
        /// <param name="userType">Type of the user.</param>
        /// <param name="requestURL">The request URL.</param>
        /// <returns></returns>
        [HttpPost]
        [Route("/api/account/myBIMsmith/manufacturer/RequestLetsTalk")]
        public async Task<IActionResult> LetsTalkToManufacturer(RequestLetsTalkDto model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
                string ip = HttpContext?.Connection?.RemoteIpAddress?.ToString();

                if (!model.ManufacturerId.HasValue)
                {
                    model.ManufacturerId = await unitOfWork.ManufacturerRepository.GetAll().Where(a => a.Name.ToLower() == model.ManufacturerName.ToLower()).Select(x => x.Id).FirstOrDefaultAsync();
                }

                if (!await unitOfWork.UserBIMsmithLTManufacturerRepository.GetAll()
                    .AnyAsync(a => a.ManufacturerId == model.ManufacturerId && a.AddedById == userId))
                {
                    UserBIMsmithLTManufacturer userBIMsmithManufacturer = new UserBIMsmithLTManufacturer();
                    userBIMsmithManufacturer.ManufacturerId = model.ManufacturerId.Value;
                    userBIMsmithManufacturer.ProductId = model.ProductId;
                    userBIMsmithManufacturer.AddedById = userId;
                    userBIMsmithManufacturer.AddedDate = DateTime.UtcNow;
                    userBIMsmithManufacturer.Timezone = model.TimeZone;

                    unitOfWork.UserBIMsmithLTManufacturerRepository.Insert(userBIMsmithManufacturer);
                    await unitOfWork.SaveAsync();
                }

                var manufacturer = await unitOfWork.ManufacturerRepository.GetAll()
                    .Where(x => x.Id == model.ManufacturerId.Value)
                    .Select(x => new
                    {
                        x.EmailLetsTalk,
                        x.EmailLetsTalkCC,
                        x.Name
                    })
                    .FirstAsync();
                var user = await unitOfWork.UserRepository.GetAll().FirstOrDefaultAsync(a => a.Id == userId);

                //Hook to slack
                await _slackWebHook.SendLetsTalkManufacturerMessage(AuthHelper.GetUserInfo(Request, ClaimTypes.Email), manufacturer.Name);
                if (!string.IsNullOrEmpty(manufacturer.EmailLetsTalk))
                {
                    var emailsPath = Path.Combine(_webHostEnvironment.WebRootPath, "Emails");
                    await EmailNotificationHelper.Create(emailsPath).SendLetsTalkEmail(
                        manufacturer.EmailLetsTalk,
                        manufacturer.EmailLetsTalkCC,
                        user.Email,
                        user.FirstName,
                        user.LastName,
                        model.City,
                        model.State,
                        model.Country,
                        model.UserType,
                        model.Message,
                        model.RequestURL,
                        model.ProductName,
                        model.ProductLink
                    );
                }

                GenerateSalesforceEventOptions options = new()
                {
                    EventType = UserEventType.LetsTalk,
                    UserId = userId,
                    Referer = Request.GetTypedHeaders()?.Referer?.AbsoluteUri?.ToString(),
                    ManufacturerId = model.ManufacturerId,
                    ManufacturerName = model.ManufacturerName,
                    ProductId = model.ProductId,
                    Ip = ip
                };
                await _analyticsService.SaveSalesForceEventToAnalyticsAsync(await GenerateSalesforceEventViewModelAsync(options));

                CacheHelper.ClearSpecificCache("*/api/Manufacturer/Get*");
            }

            return Ok();
        }

        #endregion

        #region BIMsmithMarket manufacturer lunch and learn

        /// <summary>
        /// Create lunch and learn request.
        /// </summary>
        /// <param name="manufacturerName">Name of the manufacturer.</param>
        /// <param name="manufacturerId">The manufacturer identifier.</param>
        /// <param name="message">The message.</param>
        /// <returns></returns>
        [HttpPost]
        [Route("/api/account/myBIMsmith/manufacturer/RequestLunchAndLearn")]
        public async Task<IActionResult> LunchAndLearnToManufacturer(string manufacturerName = null, int? manufacturerId = null, string message = "")
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
                string ip = HttpContext?.Connection?.RemoteIpAddress?.ToString();

                if (manufacturerId.HasValue == false)
                {
                    manufacturerId = (await unitOfWork.ManufacturerRepository.GetAll().FirstAsync(a => a.Name == manufacturerName)).Id;
                }

                if (!await unitOfWork.UserBIMsmithLLManufacturerRepository.GetAll()
                    .AnyAsync(a => a.ManufacturerId == manufacturerId && a.AddedById == userId))
                {
                    UserBIMsmithLLManufacturer userBIMsmithManufacturer = new UserBIMsmithLLManufacturer();
                    userBIMsmithManufacturer.ManufacturerId = manufacturerId.Value;
                    userBIMsmithManufacturer.AddedById = userId;
                    userBIMsmithManufacturer.AddedDate = DateTime.UtcNow;

                    unitOfWork.UserBIMsmithLLManufacturerRepository.Insert(userBIMsmithManufacturer);
                    await unitOfWork.SaveAsync();
                }


                var manufacturer = await unitOfWork.ManufacturerRepository.GetByIdAsync(manufacturerId.Value);
                var user = await unitOfWork.UserRepository.GetAll().FirstOrDefaultAsync(a => a.Id == userId);

                //Hook to slack
                await _slackWebHook.SendLLManufacturerMessage(AuthHelper.GetUserInfo(Request, ClaimTypes.Email), manufacturer.Name);


                if (!string.IsNullOrEmpty(manufacturer.EmailLandL))
                {
                    var emailsPath = Path.Combine(_webHostEnvironment.WebRootPath, "Emails");
                    //Send email to manufacturer
                    await EmailNotificationHelper.Create(emailsPath).SendLunchAndLearnEmail(manufacturer.EmailLandL, manufacturer.EmailLandLCC, manufacturer.Name, user?.Email,
                                                                                    user?.FirstName + " " + user?.LastName, message);
                }

                GenerateSalesforceEventOptions options = new()
                {
                    EventType = UserEventType.RequestLunchAndLearn,
                    UserId = userId,
                    Referer = Request.GetTypedHeaders()?.Referer?.AbsoluteUri?.ToString(),
                    ManufacturerId = manufacturerId,
                    ManufacturerName = manufacturerName,
                    Ip = ip
                };
                await _analyticsService.SaveSalesForceEventToAnalyticsAsync(await GenerateSalesforceEventViewModelAsync(options));

                CacheHelper.ClearSpecificCache("*/api/Manufacturer/Get*");

                return Ok();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="manufacturerId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("/api/account/myBIMsmith/manufacturer/DeleteLunchAndLearn")]
        public async Task<IActionResult> DeleteLunchAndLearn(int manufacturerId)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
                string ip = HttpContext?.Connection?.RemoteIpAddress?.ToString();

                var llManufacturer = await unitOfWork.UserBIMsmithLLManufacturerRepository.GetAll()
                                        .FirstOrDefaultAsync(a => a.ManufacturerId == manufacturerId && a.AddedById == userId);
                if (llManufacturer == null)
                {
                    return BadRequest("There is no Lunch And Learn for such manufacturer.");
                }
                else
                {
                    unitOfWork.UserBIMsmithLLManufacturerRepository.Delete(llManufacturer);
                    await unitOfWork.SaveAsync();

                    GenerateSalesforceEventOptions options = new()
                    {
                        EventType = UserEventType.LunchandLearnPending,
                        UserId = userId,
                        Referer = Request.GetTypedHeaders()?.Referer?.AbsoluteUri?.ToString(),
                        ManufacturerId = manufacturerId,
                        Ip = ip
                    };
                    await _analyticsService.SaveSalesForceEventToAnalyticsAsync(await GenerateSalesforceEventViewModelAsync(options));

                    CacheHelper.ClearSpecificCache("*/api/Manufacturer/Get*");

                    return Ok();
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="manufacturerId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("/api/account/myBIMsmith/manufacturer/DeleteLetsTalk")]
        public async Task<IActionResult> DeleteLetsTalk(int manufacturerId)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
                string ip = HttpContext?.Connection?.RemoteIpAddress?.ToString();

                var ltManufacturer = await unitOfWork.UserBIMsmithLTManufacturerRepository.GetAll()
                                        .FirstOrDefaultAsync(a => a.ManufacturerId == manufacturerId && a.AddedById == userId);
                if (ltManufacturer == null)
                {
                    return BadRequest("There is no Lets Talk request for such manufacturer.");
                }
                else
                {
                    unitOfWork.UserBIMsmithLTManufacturerRepository.Delete(ltManufacturer);
                    await unitOfWork.SaveAsync();

                    GenerateSalesforceEventOptions options = new()
                    {
                        EventType = UserEventType.MeetingRequestPending,
                        UserId = userId,
                        Referer = Request.GetTypedHeaders()?.Referer?.AbsoluteUri?.ToString(),
                        ManufacturerId = manufacturerId,
                        Ip = ip
                    };
                    await _analyticsService.SaveSalesForceEventToAnalyticsAsync(await GenerateSalesforceEventViewModelAsync(options));

                    CacheHelper.ClearSpecificCache("*/api/Manufacturer/Get*");

                    return Ok();
                }
            }
        }

        #endregion

        #region BIMsmithMarket manufacturer BIM request

        /// <summary>
        /// Create BIM request
        /// </summary>
        /// <param name="message">The message.</param>
        /// <param name="manufacturerName">Name of the manufacturer.</param>
        /// <param name="manufacturerId">The manufacturer identifier.</param>
        /// <param name="productName">Name of the product.</param>
        /// <param name="productLink">The product link.</param>
        /// <param name="sourceUrl">The source URL.</param>
        /// <returns></returns>
        [HttpPost]
        [Route("/api/account/myBIMsmith/manufacturer/RequestBIM")]
        public async Task<IActionResult> RequestBIMToManufacturer(
            string message,
            string manufacturerName = null,
            int? manufacturerId = null,
            string productName = null,
            string productLink = null,
            string sourceUrl = null
        )
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
                var userEmail = AuthHelper.GetUserInfo(Request, ClaimTypes.Email);
                string ip = HttpContext?.Connection?.RemoteIpAddress?.ToString();
                var user = await unitOfWork.UserRepository.GetAll().FirstOrDefaultAsync(a => (userEmail != null && a.Email == userEmail) || a.Id == userId);

                if (!manufacturerId.HasValue)
                {
                    manufacturerId = (await unitOfWork.ManufacturerRepository.GetAll().FirstAsync(a => a.Name == manufacturerName)).Id;
                }

                if (!await unitOfWork.UserBIMsmithBIMManufacturerRepository.GetAll()
                    .AnyAsync(a => a.ManufacturerId == manufacturerId && a.AddedById == userId))
                {
                    UserBIMsmithBIMManufacturer userBIMsmithBIMManufacturer = new UserBIMsmithBIMManufacturer();
                    userBIMsmithBIMManufacturer.ManufacturerId = manufacturerId.Value;
                    userBIMsmithBIMManufacturer.Message = message;
                    userBIMsmithBIMManufacturer.ProductLink = productLink;
                    userBIMsmithBIMManufacturer.ProductName = productName;
                    userBIMsmithBIMManufacturer.AddedById = userId;
                    userBIMsmithBIMManufacturer.AddedDate = DateTime.UtcNow;

                    unitOfWork.UserBIMsmithBIMManufacturerRepository.Insert(userBIMsmithBIMManufacturer);
                    await unitOfWork.SaveAsync();
                }

                var manufacturer = await unitOfWork.ManufacturerRepository.GetByIdAsync(manufacturerId.Value);

                //Hook to slack
                await _slackWebHook.SendBIMManufacturerMessage(user.Email, manufacturer.Name, message);

                await _slackWebHook.SendBIMRequestMessage(userEmail, user?.FirstName, user?.LastName, message, sourceUrl, manufacturer.Name);

                var emailsPath = Path.Combine(_webHostEnvironment.WebRootPath, "Emails");
                await EmailNotificationHelper.Create(emailsPath).SendBIMRequestEmail(userEmail, user?.FirstName, user?.LastName, message, sourceUrl, manufacturer.Name);

                GenerateSalesforceEventOptions options = new()
                {
                    EventType = UserEventType.RequestBIM,
                    UserId = userId,
                    Referer = Request.GetTypedHeaders()?.Referer?.AbsoluteUri?.ToString(),
                    ManufacturerId = manufacturerId,
                    ManufacturerName = manufacturerName,
                    Message = message,
                    Ip = ip
                };
                await _analyticsService.SaveSalesForceEventToAnalyticsAsync(await GenerateSalesforceEventViewModelAsync(options));

                return Ok();
            }
        }

        #endregion

        private async Task<SalesForceUserEventViewModel> GenerateSalesforceEventViewModelAsync(GenerateSalesforceEventOptions options)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                Manufacturer manufacturer = null;
                if (!options.ManufacturerId.HasValue)
                {
                    manufacturer = await unitOfWork.ManufacturerRepository.GetAll().FirstOrDefaultAsync(a => a.Name == options.ManufacturerName);
                }
                else
                {
                    manufacturer = await unitOfWork.ManufacturerRepository.GetAll().FirstOrDefaultAsync(a => a.Id == options.ManufacturerId);
                }

                string productName = await unitOfWork.ProductRepository.GetAll()
                    .Where(x => x.Id == options.ProductId)
                    .Select(x => x.Name)
                    .FirstOrDefaultAsync();

                var user = await unitOfWork.UserRepository.GetAll()
                    .Select(x => new
                    {
                        x.Email,
                        x.FirstName,
                        x.LastName,
                        x.Id
                    })
                    .FirstOrDefaultAsync(x => x.Id == options.UserId);

                BIMsmithUserInfoDto bimsmithUser = await _userService.GetBIMsmithUserInfoByIdAsync(options.UserId);
                AnalyticsLocationModel location = await _analyticsService.GetLocationByIpAsync(options.Ip);

                SalesForceUserEventViewModel salesForceEventModel = new()
                {
                    ActionType = options.EventType,
                    Country = bimsmithUser?.Country,
                    State = bimsmithUser?.State ?? location?.Region,
                    City = bimsmithUser?.City ?? location?.City,
                    Province = string.Empty,
                    Zip = string.Empty,
                    CompanyName = bimsmithUser?.Company,
                    Email = user?.Email,
                    EventDate = DateTime.UtcNow,
                    FirstName = user?.FirstName,
                    LastName = user?.LastName,
                    ManufacturerId = manufacturer?.Id ?? 0,
                    Notes = options.Message,
                    ManufacturerName = manufacturer?.Name,
                    ContactId = options.UserId,
                    PageOrigination = options.Referer,
                    ProductName = productName
                };

                return salesForceEventModel;
            }
        }

        #region Private methods
        private async Task<LoginResultDto> LoginUserAsync(LoginDto model)
        {
            LoginIdentityResultDto loginIdentityResult = await _userService.GetIdentityAsync(model);
            ClaimsIdentity identity = loginIdentityResult?.Identity;
            if (identity == null)
                throw new UnauthorizedAccessException(loginIdentityResult?.Error ?? "Invalid Email or Password");

            int liveTimeInSeconds = AuthorizationConstants.SecondsInWeek;
            if (model.RememberMe)
                liveTimeInSeconds = AuthorizationConstants.SecondsInMonth;

            string encodedJwt = AuthHelper.GenerateJwtToken(identity, liveTimeInSeconds);
            bool browserDisallowsSameSiteNone = BrowserHelper.DisallowsSameSiteNone(Request.Headers["User-Agent"]);
            SetCookie(AuthorizationConstants.CookieName, encodedJwt, liveTimeInSeconds, browserDisallowsSameSiteNone);
            if (loginIdentityResult.BIMsmithCookies != null)
                SetBIMsmithCookies(loginIdentityResult.BIMsmithCookies, browserDisallowsSameSiteNone);
            await AuthenticateAsync(identity.Claims);

            LoginResultDto loginResult = loginIdentityResult.Adapt<LoginResultDto>();
            loginResult.Access_token = encodedJwt;
            loginResult.Expires_in = liveTimeInSeconds;

            return loginResult;
        }

        private void SetCookie(string key, string value, long expireTimeInSeconds, bool browserDisallowsSameSiteNone)
        {
            CookieOptions option = new CookieOptions();

            option.Expires = DateTime.UtcNow.AddSeconds(expireTimeInSeconds);
            option.Domain = ConfigurationHelper.GetValue("Domain");
            option.Path = "/";
            option.HttpOnly = false;
            option.Secure = true;

            if (!browserDisallowsSameSiteNone)
                option.SameSite = SameSiteMode.None;

            Response.Cookies.Append(key, value, option);
        }

        private void SetBIMsmithCookies(CookieCollection bimsmithCookies, bool browserDisallowsSameSiteNone)
        {
            if (bimsmithCookies == null)
                return;

            foreach (Cookie cookie in bimsmithCookies)
            {
                CookieOptions option = new()
                {
                    Expires = cookie.Expires != DateTime.MinValue ? cookie.Expires : null,
                    Domain = cookie.Domain,
                    Path = cookie.Path,
                    HttpOnly = cookie.HttpOnly,
                    Secure = cookie.Secure,
                };

                if (!browserDisallowsSameSiteNone)
                {
                    option.SameSite = SameSiteMode.None;
                }

                Response.Cookies.Append(cookie.Name, cookie.Value, option);
            }
        }

        private async Task AuthenticateAsync(IEnumerable<Claim> claims)
        {
            ClaimsIdentity id = new ClaimsIdentity(claims, "ApplicationCookie", ClaimsIdentity.DefaultNameClaimType, ClaimsIdentity.DefaultRoleClaimType);
            await HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, new ClaimsPrincipal(id));
        }

        private void RemoveCookies(bool browserDisallowsSameSiteNone)
        {
            foreach (string cookie in Request.Cookies.Keys)
            {
                CookieOptions cookieOptions = new();
                cookieOptions.Expires = DateTime.Now.AddDays(-1);
                cookieOptions.Domain = ConfigurationHelper.GetValue("Domain");
                cookieOptions.Path = "/";
                cookieOptions.HttpOnly = false;
                cookieOptions.Secure = true;

                if (!browserDisallowsSameSiteNone)
                {
                    cookieOptions.SameSite = SameSiteMode.None;
                }

                Response.Cookies.Append(cookie, string.Empty, cookieOptions);
            }
        }
        #endregion
    }
}