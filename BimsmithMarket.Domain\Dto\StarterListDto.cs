﻿using BIMsmithMarket.Domain.DBModels;
using System;
using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Dto
{
    public class StarterListDto
    {
        public string Id { get; set; }

        public string Name { get; set; }

        public string ManufacturerName { get; set; }

        public string Brand { get; set; }

        public StarterOrientation Orientation { get; set; }

        public string Preview { get; set; }

        public ICollection<StarterProductLineDto> ForgeProductLines { get; set; }

        public string Stc { get; set; }

        public string Fstc { get; set; }

        public string Rvalue { get; set; }

        public string Fire { get; set; }

        public string Ul { get; set; }

        public string Iic { get; set; }

        public string Fiic { get; set; }

        public string Nfpa285 { get; set; }

        public float Weight { get; set; }

        public bool Published { get; set; }

        public bool Staging { get; set; }

        public DateTime? DateModified { get; set; }
    }

    public class StarterProductLineDto
    {
        public int MarketProductLineId { get; set; }

        public string Name { get; set; }
    }
}