﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BIMsmithMarket.DataLayer.Migrations
{
    public partial class Configured_Relations_For_Manufacturer : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Details_Manufacturers_ManufacturerId",
                table: "Details");

            migrationBuilder.DropForeignKey(
                name: "FK_ManufacturerAdditionalFiles_Manufacturers_ManufacturerId",
                table: "ManufacturerAdditionalFiles");

            migrationBuilder.DropForeignKey(
                name: "FK_ManufacturerAnnouncements_Manufacturers_ManufacturerId",
                table: "ManufacturerAnnouncements");

            migrationBuilder.DropForeignKey(
                name: "FK_ManufacturerFiles_Manufacturers_ManufacturerId",
                table: "ManufacturerFiles");

            migrationBuilder.DropForeignKey(
                name: "FK_ManufacturerPhotoes_Manufacturers_ManufacturerId",
                table: "ManufacturerPhotoes");

            migrationBuilder.DropForeignKey(
                name: "FK_StaticExcelFiles_Manufacturers_ManufacturerId",
                table: "StaticExcelFiles");

            migrationBuilder.AlterColumn<int>(
                name: "ManufacturerId",
                table: "Details",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddForeignKey(
                name: "FK_Details_Manufacturers_ManufacturerId",
                table: "Details",
                column: "ManufacturerId",
                principalTable: "Manufacturers",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_ManufacturerAdditionalFiles_Manufacturers_ManufacturerId",
                table: "ManufacturerAdditionalFiles",
                column: "ManufacturerId",
                principalTable: "Manufacturers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_ManufacturerAnnouncements_Manufacturers_ManufacturerId",
                table: "ManufacturerAnnouncements",
                column: "ManufacturerId",
                principalTable: "Manufacturers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_ManufacturerFiles_Manufacturers_ManufacturerId",
                table: "ManufacturerFiles",
                column: "ManufacturerId",
                principalTable: "Manufacturers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_ManufacturerPhotoes_Manufacturers_ManufacturerId",
                table: "ManufacturerPhotoes",
                column: "ManufacturerId",
                principalTable: "Manufacturers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_StaticExcelFiles_Manufacturers_ManufacturerId",
                table: "StaticExcelFiles",
                column: "ManufacturerId",
                principalTable: "Manufacturers",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Details_Manufacturers_ManufacturerId",
                table: "Details");

            migrationBuilder.DropForeignKey(
                name: "FK_ManufacturerAdditionalFiles_Manufacturers_ManufacturerId",
                table: "ManufacturerAdditionalFiles");

            migrationBuilder.DropForeignKey(
                name: "FK_ManufacturerAnnouncements_Manufacturers_ManufacturerId",
                table: "ManufacturerAnnouncements");

            migrationBuilder.DropForeignKey(
                name: "FK_ManufacturerFiles_Manufacturers_ManufacturerId",
                table: "ManufacturerFiles");

            migrationBuilder.DropForeignKey(
                name: "FK_ManufacturerPhotoes_Manufacturers_ManufacturerId",
                table: "ManufacturerPhotoes");

            migrationBuilder.DropForeignKey(
                name: "FK_StaticExcelFiles_Manufacturers_ManufacturerId",
                table: "StaticExcelFiles");

            migrationBuilder.AlterColumn<int>(
                name: "ManufacturerId",
                table: "Details",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Details_Manufacturers_ManufacturerId",
                table: "Details",
                column: "ManufacturerId",
                principalTable: "Manufacturers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_ManufacturerAdditionalFiles_Manufacturers_ManufacturerId",
                table: "ManufacturerAdditionalFiles",
                column: "ManufacturerId",
                principalTable: "Manufacturers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_ManufacturerAnnouncements_Manufacturers_ManufacturerId",
                table: "ManufacturerAnnouncements",
                column: "ManufacturerId",
                principalTable: "Manufacturers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_ManufacturerFiles_Manufacturers_ManufacturerId",
                table: "ManufacturerFiles",
                column: "ManufacturerId",
                principalTable: "Manufacturers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_ManufacturerPhotoes_Manufacturers_ManufacturerId",
                table: "ManufacturerPhotoes",
                column: "ManufacturerId",
                principalTable: "Manufacturers",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_StaticExcelFiles_Manufacturers_ManufacturerId",
                table: "StaticExcelFiles",
                column: "ManufacturerId",
                principalTable: "Manufacturers",
                principalColumn: "Id");
        }
    }
}
