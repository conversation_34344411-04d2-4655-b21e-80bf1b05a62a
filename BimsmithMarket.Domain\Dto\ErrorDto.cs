﻿using System.Net;

namespace BIMsmithMarket.Domain.Dto
{
    public class ErrorDto
    {
        /// <summary>
        /// Gets or sets the server error code.
        /// </summary>
        /// <value>
        /// The server error code.
        /// </value>
        public int ServerErrorCode { get; set; }

        /// <summary>
        /// Gets or sets the HTTP status code.
        /// </summary>
        /// <value>
        /// The HTTP status code.
        /// </value>
        public HttpStatusCode HttpStatusCode { get; set; }

        /// <summary>
        /// Gets or sets the message.
        /// </summary>
        /// <value>
        /// The message.
        /// </value>
        public string Message { get; set; }

        /// <summary>
        /// Gets or sets the error message.
        /// </summary>
        /// <value>
        /// The error message.
        /// </value>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// Gets or sets the details.
        /// </summary>
        /// <value>
        /// The details.
        /// </value>
        public object Details { get; set; }
    }
}