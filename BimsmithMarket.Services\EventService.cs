﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Helpers;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class EventService : IEventService
    {
        public async Task<BaseAddEditResultDto> AddAsync(IUnitOfWork unitOfWork, AddEditEventDto model, string userId)
        {
            Event newEvent = model.Adapt<Event>();

            newEvent.CreatedById = userId;
            newEvent.CreatedDate = DateTime.UtcNow;

            if (newEvent.Published && newEvent.PublishedDate == null)
            {
                newEvent.PublishedDate = DateTime.UtcNow;
            }

            string plainText = HtmlToText.TryConvertHtml(model.HtmlBody).Trim();
            newEvent.Description = plainText.Length > 230 ? plainText.Substring(0, 230) + "..." : plainText;

            unitOfWork.EventRepository.Insert(newEvent);

            await unitOfWork.SaveAsync();

            BaseAddEditResultDto result = new BaseAddEditResultDto
            {
                Id = newEvent.Id
            };

            return result;
        }

        public async Task<BaseAddEditResultDto> EditAsync(IUnitOfWork unitOfWork, AddEditEventDto model, string userId)
        {
            Event dbEvent = await unitOfWork.EventRepository.GetByIdAsync(model.Id);
            if (dbEvent == null)
                throw new InvalidInputException("The event not found");

            model.Adapt(dbEvent);
            dbEvent.ModifiedById = userId;
            dbEvent.ModifiedDate = DateTime.UtcNow;

            if (dbEvent.Published == true && dbEvent.PublishedDate == null)
            {
                dbEvent.PublishedDate = DateTime.UtcNow;
            }

            string plainText = HtmlToText.TryConvertHtml(model.HtmlBody).Trim();
            dbEvent.Description = plainText.Length > 230 ? plainText.Substring(0, 230) + "..." : plainText;

            unitOfWork.EventRepository.Edit(dbEvent);

            await unitOfWork.SaveAsync();

            BaseAddEditResultDto result = new BaseAddEditResultDto
            {
                Id = dbEvent.Id
            };

            return result;
        }

        public async Task DeleteAsync(IUnitOfWork unitOfWork, int id)
        {
            Event dbEvent = await unitOfWork.EventRepository.GetByIdAsync(id);

            if (dbEvent == null)
                throw new InvalidInputException("The event not found");

            unitOfWork.EventRepository.Delete(dbEvent);

            await unitOfWork.SaveAsync();
        }

        public async Task<AdminGetEventDto> GetAsync(IUnitOfWork unitOfWork, int id)
        {
            AdminGetEventDto item = await unitOfWork.EventRepository.GetAll()
                .Where(x => x.Id == id)
                .ProjectToType<AdminGetEventDto>()
                .FirstOrDefaultAsync();

            if (item == null)
                throw new DbItemNotFoundException("Event not found");

            return item;
        }

        public async Task<PaginationListDto<AdminListEventDto>> AdminListAsync(IUnitOfWork unitOfWork, string q = null, int offset = 0, int count = 10)
        {
            IQueryable<Event> query = unitOfWork.EventRepository.GetAll();

            if (!string.IsNullOrEmpty(q))
            {
                query = query.Where(x => x.Title.Contains(q));
            }

            int totalCount = await query.CountAsync();

            AdminListEventDto[] list = await query
                .OrderByDescending(a => a.CreatedDate < a.ModifiedDate ? a.ModifiedDate : a.CreatedDate)
                .ProjectToType<AdminListEventDto>()
                .Skip(offset)
                .Take(count)
                .ToArrayAsync();

            return new PaginationListDto<AdminListEventDto>
            {
                Count = totalCount,
                Data = list,
            };
        }

        public async Task<PublicListEventDto[]> PublicListAsync(IUnitOfWork unitOfWork)
        {
            DateTime date = DateTime.UtcNow.Date;
            return await unitOfWork.EventRepository.GetAll()
                .Where(x => (x.Published && !x.UnpublishAfterDateHasPassed)
                         || (x.Published && x.UnpublishAfterDateHasPassed && x.Date >= date))
                .ProjectToType<PublicListEventDto>()
                .ToArrayAsync();
        }
    }
}