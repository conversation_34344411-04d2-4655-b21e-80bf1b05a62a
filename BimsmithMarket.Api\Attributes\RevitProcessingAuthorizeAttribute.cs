﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Domain.Constants;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Attributes
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
    public class RevitProcessingAuthorizeAttribute : Attribute, IAsyncAuthorizationFilter
    {
        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            string revitProcessingAccessToken = context.HttpContext?.Request?.Headers[RevitProcessingConstants.AccessTokenHeader];
            bool hasAccess = !string.IsNullOrWhiteSpace(revitProcessingAccessToken) && revitProcessingAccessToken == ConfigurationHelper.GetValue("RevitProcessingAccessToken");

            if (!hasAccess)
                context.Result = new JsonResult(new { message = "Unauthorized" }) { StatusCode = StatusCodes.Status401Unauthorized };
        }
    }
}