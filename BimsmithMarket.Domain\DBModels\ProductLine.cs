﻿using BIMsmithMarket.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    [Index(nameof(Name), Name = "IX_ProductLine_Name")]
    public class ProductLine : BaseEntity
    {
        [StringLength(200)]
        public string Name { get; set; }

        public string Note { get; set; }

        public string Description { get; set; }

        public int? ManufacturerId { get; set; }

        public string ForgeManufacturerId { get; set; }

        public float Weight { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        public HealthCheckStatus HealthCheckStatus { get; set; }

        /// ------------------------------------------
        [ForeignKey("ManufacturerId")]
        public virtual Manufacturer Manufacturer { get; set; }

        public virtual ICollection<Product> Products { get; set; }

        public virtual ICollection<StarterProductLines> Starters { get; set; }

        public virtual ICollection<ForgeProductLineIds> ForgeProductLineIds { get; set; }

        public virtual ICollection<ProductLineStats> ProductLineStats { get; set; }

        public virtual ICollection<ProductLineFile> ProductLineFiles { get; set; }

        public virtual ICollection<ProductLineCertificate> ProductLineCertificates { get; set; }

        public virtual ICollection<ProductLineQualityItem> ProductLineQualityItems { get; set; }

        public ProductLine()
        {
            Products = new List<Product>();
            Starters = new List<StarterProductLines>();
            ForgeProductLineIds = new List<ForgeProductLineIds>();
            ProductLineStats = new List<ProductLineStats>();
            ProductLineFiles = new List<ProductLineFile>();
            ProductLineCertificates = new List<ProductLineCertificate>();
            ProductLineQualityItems = new List<ProductLineQualityItem>();
        }
    }
}