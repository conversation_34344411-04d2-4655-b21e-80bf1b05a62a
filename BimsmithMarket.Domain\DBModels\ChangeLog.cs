﻿using BIMsmithMarket.Domain.Enums;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.DBModels
{
    public class ChangeLog : BaseEntity
    {
        public EntityAction EntityAction { get; set; }

        public EntityType EntityType { get; set; }

        public int EntityId { get; set; }

        [StringLength(100)]
        public string Field { get; set; }

        [StringLength(1000)]
        public string OldValue { get; set; }

        [StringLength(1000)]
        public string NewValue { get; set; }
    }
}