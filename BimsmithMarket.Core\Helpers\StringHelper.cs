﻿using System.Text.RegularExpressions;

namespace BIMsmithMarket.Core.Helpers
{
    public static class StringHelper
    {
        public static string FirstCharacterToLower(string str)
        {
            if (string.IsNullOrEmpty(str) || char.IsLower(str, 0))
                return str;

            return char.ToLowerInvariant(str[0]) + str.Substring(1);
        }

        public static string FixUrlString(string input)
        {
            input = Regex.Replace(input, "[^A-Za-z0-9 ]", "");
            input = Regex.Replace(input, " ", "-");
            input = Regex.Replace(input, "^\\-+|\\-+$", "");

            return input;
        }
    }
}