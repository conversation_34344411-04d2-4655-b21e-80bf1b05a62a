﻿
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.DataLayer;
using Microsoft.AspNetCore.Mvc;

namespace BIMsmithMarket.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    public class LegalPageController : BaseApiController
    {
        /// <summary>
        /// Gets this instance.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("List")]
        [Authorize(Roles = DbConstants.CheckAdminRole)]
        public IActionResult CheckAdmin()
        {
            return Ok();
        }
    }
}