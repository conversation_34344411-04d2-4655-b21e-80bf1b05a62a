{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Information"}}, "ConnectionStrings": {"MongoDb": "mongodb://MongoDBAdmin:xWyXe9c$bHSDzBv2y!@*************:27017/Market-Uat?authSource=admin", "MarketDBConnection": "Data Source=*************;Initial Catalog=bimsmith-market-uat;User ID=sa;Password=****************;Connect Timeout=15;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultipleActiveResultSets=true;MultiSubnetFailover=False"}, "BaseLogPath": "C:\\Work Folder\\", "IsDevEnvironment": "true", "BimsmithStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=bimsmithstorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "InternsBimsmithStorageConnectionString": "DefaultEndpointsProtocol=https;AccountName=internsbimsmithstorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "AngularisSiteAccessToken": "PDlqI8xBfVJaBZuH51F8pSxmVOaZjWfYF1uWIZYsdfzcArA8", "StripePrivateKey": "sk_test_51Jbi81H02bD6aDoneSpaeyfYh2Q3RO9dTHyLDAjAmJpUOMVCSAnrtL0J90x0Sregm41V0O9qqRmzA5bZW7LZ7Tn500DXBmz8rq", "StripeOpenKey": "pk_test_51Jbi81H02bD6aDonTGTbbBIRoktNBq3Oaqi7irqqriMxOp4xpxM4gnouNBsphqlVUicGuotxNwPrUwnJmXifgTkh00FYuekXTj", "ForgeStartersEndPoint": "http://localhost:62018/api/Starters/MarketWeb/Filter", "EncryptionKey": "mwj8@254d]WZ!78f$H", "MailChimpAPIKey": "*************************************", "AdministratorEmails": "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,sba<PERSON><PERSON><PERSON><EMAIL>,<EMAIL>,r<PERSON><EMAIL>,p<PERSON><PERSON><PERSON>@qarea.com,<EMAIL>,r<PERSON><PERSON><PERSON><PERSON>@qarea.us,<EMAIL>,<EMAIL>,and<PERSON><PERSON><PERSON>@qarea.us,<EMAIL>,matsydon<PERSON>@qarea.us,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>", "ContentManagersEmails": "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>", "AllowedDurationMinutes": "720", "LoginDomain": "https://dev.bimsmith.com/", "LoginEndPoint": "https://dev.bimsmith.com/api/auth/validatecredentials/", "LoginSecretKey": "Qa123456", "emailAccountInfo": "<EMAIL>", "emailAccountDisplayName": "Market Info", "commentAdminEmail": "<EMAIL>,<EMAIL>", "CompaniesSyncEndPoint": "api/Company/Sync", "DocumentDBAuthKey": "T3iusgmBq8LzL8QquDQmwOWWmQwY+k9RY69EESB1733daCAbe+R+Wzyaqdg/KrI46z/Z6Dlhvfp7ff71chji0g==", "BimsmithApiToken": "c1R3C79Z9dVl7Ienc3fF2A9pc", "BimsmithApiUrl": "https://dev.bimsmith.com/api", "ApiAccessToken": "c1R3C79Z9dVl7Ienc3fF2A9pc", "SupportEmail": "<EMAIL>", "AnalyticsWebApiBaseAddress": "https://analytics-web-api-dev.bimsmith.com/", "AnalyticsWriterBaseAddress": "https://analytics-writer-api-dev.bimsmith.com", "DownloadStatisticsUsername": "<EMAIL>", "DownloadStatisticsUserId": "********-9999-9999-9999-************", "GoogleAnalyticsApiCollectEndpoint": "https://www.google-analytics.com/collect", "GoogleAnalyticsApiResourceId": "UA-*********-1", "CBOBaseAddress": "https://bimsmith-translatorapi-dev.bimsmith.com/", "MailchimpCoffeeClubListId": "943f2c30d8", "ShoutEmail": "<EMAIL>", "MailChimpCofeeClubAPIKey": "************************************", "SwatchboxDomain": "swatchbox.com", "BimsmithAccessToken": "AQ1rgvei0eOCmk9tOCJT2QbTwOz8oP", "MarketFrontBaseUrl": "https://localhost:7170/", "MarketApiBaseUrl": "https://localhost:44302/", "MarketUserAgent": "BIMsmith Market", "RobotsContent": "User-agent: *;Disallow: /", "GoogleRecaptchaSecretKey": "6Lf52q8ZAAAAAIjwa0c0gKEIuxKIynM6ZhtSxiJD", "GoogleRecaptchaPublicKey": "6Lf52q8ZAAAAACkC_uWLqH28KyE-rXgfN0LMigxx", "GoogleRecaptchaApiUrl": "https://www.google.com/recaptcha/api/siteverify", "ManufacturerBackupsPath": "C:\\Work Folder\\Manufacturer Backups\\", "ExternalApiLogFilePath": "C:\\LogFiles\\ExternalApi\\LOCAL\\ExternalApiLog.txt", "SwatchboxMarketAccessKey": "LaFq68YBuWPpvns3", "Environment": "local", "MailChimpAngulerisSiteApiKey": "************************************", "MailChimpAngulerisSiteListId": "a617a1461f", "MailChimpSwatchboxSubscribersListId": "fe6e9d8089", "CacheConnection": "host=127.0.0.1;port=6379;instance=search_cache_local;allowAdmin=true;defaultDatabase=0", "SketchupAPI": {"BaseAddress": "https://3dwarehouse.sketchup.com/", "BrizoAPIKey": "90bd1fbe-4981-4fc1-b9e6-78d2cbdf922a", "DeltaFaucetAPIKey": "12b7309c-b823-48d0-abf0-e835daef9ee7", "PeerlessFaucetAPIKey": "28d0ace7-e004-400d-a37b-d4a44de6a3e8", "DanverOutdoorsKitchensAPIKey": "3c0c025f-9926-4064-91bd-8344fb6a79bb", "LogFilePath": "D:\\home\\LogFiles\\SketchupApi\\SketchupApiLog.txt"}, "Domain": "bimsmith.com", "Secret": "Zb6UYD7cw2b45X9mnYutzFyP0krUyGlI", "Smtp": {"Host": "smtp.office365.com", "Port": "587", "EnableSsl": "true", "UserName": "<EMAIL>", "Password": "vcxwvrpwlkdwjxwf", "ChecksEmail": "<EMAIL>"}, "DownloadFileLogPath": "C:\\Work Folder\\File Downloads.txt", "ExternalApiLogPath": "C:\\Work Folder\\ExternalApiLogs.txt", "Slack": {"Hooks": {"ReportingMarket": "*****************************************************************************", "ReportingSearch": "*****************************************************************************", "RequestBIM": "*****************************************************************************"}, "Channels": {"ReportingMarket": "#reporting-bsm-market", "ReportingSearch": "#reporting-search", "RequestBIM": "#reporting-bsm-requestbim", "RevitProcessing": "#reporting-bsm-revit-processor"}}, "ExternalApi": {"TrimTexAccessToken": "Rmmm0xc6uYmDy97e2hF6jWy3fCo8HHj6"}, "DevelopmentAllowedEmails": "<EMAIL>,pav<PERSON><EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,sba<PERSON><PERSON><PERSON><EMAIL>,<EMAIL>,r<PERSON><EMAIL>,pap<PERSON><PERSON>@qarea.com,<EMAIL>,ryzhy<PERSON>@qarea.us,<EMAIL>,<EMAIL>,matsy<PERSON><PERSON>@qarea.us,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>", "RevitProcessingAccessToken": "P>bs*h8Q-rxp5Y9w$~/T_dfX(:AJgcvD"}