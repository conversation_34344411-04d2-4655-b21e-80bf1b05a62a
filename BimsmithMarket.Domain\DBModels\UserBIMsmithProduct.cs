﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class UserBIMsmithProduct
    {
        public int Id { get; set; }

        public int ProductId { get; set; }

        public string StreamId { get; set; }

        public string AddedById { get; set; }

        public DateTime AddedDate { get; set; }

        /// ------------------------------------------
        [ForeignKey("AddedById")]
        public virtual ApplicationUser AddedBy { get; set; }

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }
    }
}