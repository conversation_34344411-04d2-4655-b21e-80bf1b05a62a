﻿using System;

namespace BIMsmithMarket.Domain.Models
{
    public class SalesForceUserEventViewModel
    {
        public string FirstName { get; set; }

        public string LastName { get; set; }

        public string Email { get; set; }

        public string CompanyName { get; set; }

        public string Country { get; set; }

        public string State { get; set; }

        public string City { get; set; }

        public string Province { get; set; }

        public string Zip { get; set; }

        public string Notes { get; set; }

        public UserEventType ActionType { get; set; }

        public int ManufacturerId { get; set; }

        public DateTime EventDate { get; set; }

        public string ManufacturerName { get; set; }

        public string ContactId { get; set; }

        public string PageOrigination { get; set; }

        public string ProductName { get; set; }
    }

    public enum UserEventType
    {
        Subscribe = 1,
        LetsTalk = 2,
        RequestLunchAndLearn = 3,
        RequestBIM = 4,
        RequestPricing = 5,
        Unsubscribe = -1,
        MeetingRequestPending = -2,
        LunchandLearnPending = -3
    }
}