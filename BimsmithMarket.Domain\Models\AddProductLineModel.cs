﻿using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Models
{
    public class AddProductLineModel
    {
        [Required]
        [StringLength(200)]
        public string Name { get; set; }

        [Required]
        public int ManufacturerId { get; set; }

        public string Note { get; set; }

        public List<string> ForgeProductLineIds { get; set; }

        public string Description { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        public List<FileDto> Files { get; set; }

        public List<ProjectFileModel> ProjectFiles { get; set; }

        public List<int> ExternalCertificateIds { get; set; }

        public List<int> QualityItemIds { get; set; }
    }

    public class EditProductLineModel : AddProductLineModel
    {
        [Required]
        public int Id { get; set; }
    }

    public class ProductLineListDto
    {
        public int Id { get; set; }

        public string Name { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        public int? ManufacturerId { get; set; }

        public string ManufacturerName { get; set; }

        public string Description { get; set; }

        public int ProductsCount { get; set; }

        public DateTime UpdateDate { get; set; }

        public HealthCheckStatus HealthCheckStatus { get; set; }
    }
}