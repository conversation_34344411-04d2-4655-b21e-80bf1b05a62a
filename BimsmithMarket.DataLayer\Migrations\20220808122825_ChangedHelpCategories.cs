﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BIMsmithMarket.DataLayer.Migrations
{
    public partial class ChangedHelpCategories : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "HelpArticleHelpCategory",
                columns: table => new
                {
                    HelpArticlesId = table.Column<int>(type: "int", nullable: false),
                    HelpCategoriesId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HelpArticleHelpCategory", x => new { x.HelpArticlesId, x.HelpCategoriesId });
                    table.ForeignKey(
                        name: "FK_HelpArticleHelpCategory_HelpArticles_HelpArticlesId",
                        column: x => x.HelpArticlesId,
                        principalTable: "HelpArticles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_HelpArticleHelpCategory_HelpCategories_HelpCategoriesId",
                        column: x => x.HelpCategoriesId,
                        principalTable: "HelpCategories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_HelpArticleHelpCategory_HelpCategoriesId",
                table: "HelpArticleHelpCategory",
                column: "HelpCategoriesId");

            migrationBuilder.Sql(@"INSERT INTO HelpArticleHelpCategory
                                  (HelpArticlesId, HelpCategoriesId)
                                  SELECT HelpArticle_Id, HelpCategory_Id
                                  FROM HelpCategoryHelpArticles");

            migrationBuilder.DropTable(
                name: "HelpCategoryHelpArticles");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "HelpArticleHelpCategory");

            migrationBuilder.CreateTable(
                name: "HelpCategoryHelpArticles",
                columns: table => new
                {
                    HelpArticle_Id = table.Column<int>(type: "int", nullable: false),
                    HelpCategory_Id = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HelpCategoryHelpArticles", x => new { x.HelpArticle_Id, x.HelpCategory_Id });
                    table.ForeignKey(
                        name: "FK_HelpCategoryHelpArticles_HelpArticles_HelpArticle_Id",
                        column: x => x.HelpArticle_Id,
                        principalTable: "HelpArticles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_HelpCategoryHelpArticles_HelpCategories_HelpCategory_Id",
                        column: x => x.HelpCategory_Id,
                        principalTable: "HelpCategories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });
        }
    }
}