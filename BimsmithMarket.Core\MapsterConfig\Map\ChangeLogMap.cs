﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using Mapster;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public static class ChangeLogMap
    {
        public static void Register(TypeAdapterConfig config)
        {
            config.ForType<ChangeLog, AdminListChangeLogDto>()
                .Map(d => d.Action, s => s.EntityAction.ToString())
                .Map(d => d.UserEmail, s => s.CreatedBy.Email);

            config.ForType<ChangeLog, ChangeLogExcelDto>()
                .Map(d => d.UserEmail, s => s.CreatedBy.Email);
        }
    }
}