﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto.AnnouncementDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;

using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Services
{
    public class AnnouncementService : IAnnouncementService
    {
        public async Task<GetAnnouncementDto> AddAsync(AddAnnouncementDto model, string userId)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var announcement = model.Adapt<Announcement>();
                announcement.CreatedById = userId;
                announcement.CreatedDate = DateTime.UtcNow;
                unitOfWork.AnnouncementRepository.Insert(announcement);
                await unitOfWork.SaveAsync();

                if (model.ManufacturerIds != null)
                {
                    await AddOrUpdateManufacturerAnnouncementAsync(model, announcement.Id, userId, unitOfWork);
                }

                CacheHelper.ClearSpecificCache("*/api/Announcement/List*");

                return announcement.Adapt<GetAnnouncementDto>();
            }
        }

        public async Task<GetAnnouncementDto> EditAsync(EditAnnouncementDto model, string userId)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var dbAnnouncement = unitOfWork.AnnouncementRepository.GetById(model.Id);
                if (dbAnnouncement == null) throw new InvalidInputException("Announcement not found");

                model.Adapt(dbAnnouncement);

                var existingItemIds = dbAnnouncement.ManufacturerAnnouncements
                    .Select(s => s.ManufacturerId)
                    .ToList();

                var itemToAddIds = model.ManufacturerIds.Except(existingItemIds).ToList();
                var itemToDeleteIds = existingItemIds.Except(model.ManufacturerIds).ToList();

                foreach (var manufacturerId in itemToAddIds)
                {
                    ManufacturerAnnouncement manufacturerAnnouncement = new ManufacturerAnnouncement();
                    manufacturerAnnouncement.AnnouncementId = model.Id;
                    manufacturerAnnouncement.ManufacturerId = manufacturerId;
                    unitOfWork.ManufacturerAnnouncementRepository.Insert(manufacturerAnnouncement);
                }

                var itemsToDelete = unitOfWork.ManufacturerAnnouncementRepository.GetAll()
                    .Where(x => x.AnnouncementId == model.Id && itemToDeleteIds.Contains(x.AnnouncementId))
                    .ToList();
                itemsToDelete.ForEach(x => unitOfWork.ManufacturerAnnouncementRepository.Delete(x));

                dbAnnouncement.ModifiedById = userId;
                dbAnnouncement.ModifiedDate = DateTime.UtcNow;
                unitOfWork.AnnouncementRepository.Edit(dbAnnouncement);
                await unitOfWork.SaveAsync();

                await AddOrUpdateManufacturerAnnouncementAsync(model, dbAnnouncement.Id, userId, unitOfWork);

                CacheHelper.ClearSpecificCache("*/api/Announcement/List*");

                return dbAnnouncement.Adapt<GetAnnouncementDto>();
            }
        }

        public async Task<GetAnnouncementDto> GetAsync(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var dbAnnouncement = await unitOfWork.AnnouncementRepository.GetByIdAsync(id);
                if (dbAnnouncement == null)
                    throw new DbItemNotFoundException("Announcement not found");

                return dbAnnouncement.Adapt<GetAnnouncementDto>();
            }
        }

        public async Task<GetAnnouncementDto[]> ListAsync(int count, int offset, bool? isActive, string regionId = null, string stateId = null)
        {
            using (var unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var announcementQuery = unitOfWork.AnnouncementRepository.GetAll()
                    .Include(i => i.ManufacturerAnnouncements)
                    .AsQueryable();

                if (isActive.HasValue)
                    announcementQuery = announcementQuery.Where(x => x.IsActive == isActive);

                if (!string.IsNullOrWhiteSpace(regionId))
                    announcementQuery = announcementQuery.Where(x => x.RegionIds == null || x.RegionIds == string.Empty || x.RegionIds.Contains(regionId));

                if (!string.IsNullOrWhiteSpace(stateId))
                    announcementQuery = announcementQuery.Where(x => x.StateIds == null || x.StateIds == string.Empty || x.StateIds.Contains(stateId));

                var dbAnnouncements = await announcementQuery
                    .Include(x => x.ManufacturerAnnouncements)
                    .OrderBy(x => x.Id)
                    .Take(count)
                    .Skip(offset)
                    .AsNoTracking()
                    .ToArrayAsync();

                //search in memory to exclude partial equality
                if (!string.IsNullOrWhiteSpace(regionId))
                    dbAnnouncements = dbAnnouncements.Where(x => x.RegionIds == null || x.RegionIds == string.Empty || x.RegionIds.Split('_').Contains(regionId)).ToArray();

                //search in memory to exclude partial equality
                if (!string.IsNullOrWhiteSpace(stateId))
                    dbAnnouncements = dbAnnouncements.Where(x => x.StateIds == null || x.StateIds == string.Empty || x.StateIds.Split('_').Contains(stateId)).ToArray();

                return dbAnnouncements.Adapt<GetAnnouncementDto[]>();
            }
        }

        public async Task DeleteAsync(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var dbAnnouncement = await unitOfWork.AnnouncementRepository.GetByIdAsync(id);
                if (dbAnnouncement == null)
                    throw new InvalidInputException("Announcement not found");

                var manufacturerAnnouncements = await unitOfWork.ManufacturerAnnouncementRepository.GetAll()
                                                                .Where(x => x.AnnouncementId == id)
                                                                .ToArrayAsync();

                unitOfWork.ManufacturerAnnouncementRepository.Delete(manufacturerAnnouncements);
                unitOfWork.AnnouncementRepository.Delete(dbAnnouncement);

                await unitOfWork.SaveAsync();

                CacheHelper.ClearSpecificCache("*/api/Announcement/List*");
            }
        }

        #region private methods
        private ManufacturerAnnouncement AddManufacturerAnnouncement(string userId, int announcementId, int manufacturerId)
        {
            return new ManufacturerAnnouncement
            {
                AnnouncementId = announcementId,
                ManufacturerId = manufacturerId,
                CreatedById = userId,
                CreatedDate = DateTime.UtcNow
            };
        }

        private async Task AddOrUpdateManufacturerAnnouncementAsync(AddAnnouncementDto model, int announcementId, string userId, IUnitOfWork unitOfWork)
        {
            //sanitize manufacturer ids in model
            model.ManufacturerIds = model.ManufacturerIds.Where(x => x != -1).ToArray();

            var existingManufacturerIds = unitOfWork.ManufacturerAnnouncementRepository.GetAll()
                                                                              .Where(x => x.AnnouncementId == announcementId)
                                                                              .Select(x => x.ManufacturerId)
                                                                              .ToList();
            var manufacturerIdsToAdd = model.ManufacturerIds.Except(existingManufacturerIds).ToList();
            var manufacturerIdsToDelete = existingManufacturerIds.Except(model.ManufacturerIds).ToList();
            foreach (var manufacturerId in manufacturerIdsToAdd)
            {
                var manufacturerAnnouncement = AddManufacturerAnnouncement(userId, announcementId, manufacturerId);
                unitOfWork.ManufacturerAnnouncementRepository.Insert(manufacturerAnnouncement);
            }

            foreach (var manufacturerId in manufacturerIdsToDelete)
            {
                var manufacturerAnnouncement = unitOfWork.ManufacturerAnnouncementRepository.GetAll()
                                                         .FirstOrDefault(x => x.ManufacturerId == manufacturerId
                                                                          && x.AnnouncementId == announcementId);
                if (manufacturerAnnouncement != null)
                {
                    unitOfWork.ManufacturerAnnouncementRepository.Delete(manufacturerAnnouncement);
                }
            }

            await unitOfWork.SaveAsync();
        }
        #endregion
    }
}