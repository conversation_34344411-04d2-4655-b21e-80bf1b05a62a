﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Models
{
    public class SendEmailModel
    {
        [Required]
        public string AngularisToken { get; set; }

        [Required]
        [StringLength(200)]
        public string Title { get; set; }

        [Required]
        public string HtmlBody { get; set; }

        public string EmailToMailchimp { get; set; }

        public List<MailchimpProperty> MailchimpProperties { get; set; }
    }

    public class MailchimpProperty
    {
        [Required]
        public string Key { get; set; }

        [Required]
        public string Value { get; set; }
    }
}