﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    [Index(nameof(VanityId), Name = "IX_News_VanityId")]
    public class News : BaseEntity
    {
        [StringLength(300)]
        public string VanityId { get; set; }

        public int? NewsCategoryId { get; set; }

        public string Title { get; set; }

        public string Descriptions { get; set; }

        public string HtmlBody { get; set; }

        public string ImageUrlBig { get; set; }

        public string ImageUrlSmall { get; set; }

        public string AuthorTitle { get; set; }

        public string Tags { get; set; }

        public string MetaDescription { get; set; }

        public string MetaKeywords { get; set; }

        public string MetaTitle { get; set; }

        public int ViewCount { get; set; }

        public PublishOption PublishOption { get; set; }

        public DateTime? PublishedDate { get; set; }

        public int Status { get; set; }

        public DateTime? SchedulePublishDate { get; set; }

        /// ------------------------------------------
        [ForeignKey("NewsCategoryId")]
        public virtual NewsCategory NewsCategory { get; set; }

        public virtual ICollection<NewsTarget> NewsTargets { get; set; }

        public News()
        {
            NewsTargets = new List<NewsTarget>();
        }
    }
}