﻿using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.PaymentsDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IUserPaidProductService
    {
        Task<UserPaidProductDto> AddEditProduct(UserPaidProductDto model, IUnitOfWork unitOfWork);

        Task ChangeStatus(string sessionId, string stripeSessionStatus, IUnitOfWork unitOfWork);

        Task DeleteProduct(int paydProductId, IUnitOfWork unitOfWork);

        Task<List<UserPaidProductDto>> GetUserProductsAsync(
            string userId,
            IUnitOfWork unitOfWork,
            bool all = false);

        Task<bool> CheckUserProductAsync(
            string userId,
            int productId,
            IUnitOfWork unitOfWork);

        Task<CheckUserProductResponseDto[]> BatchCheckUserProductAsync(
            CheckUserProductRequestDto model,
            IUnitOfWork unitOfWork);

        Task<IEnumerable<CheckUserProductsResultDto>> CheckUserProductsAsync(
            EntityIdsDto model,
            string userId,
            IUnitOfWork unitOfWork);

        Task DeleteAsync(
            int Id,
            IUnitOfWork unitOfWork);
    }
}