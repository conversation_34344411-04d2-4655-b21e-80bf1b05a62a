﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class CategoryKeyStatService : ICategoryKeyStatService
    {
        public async Task<object> UnitsAsync(IUnitOfWork unitOfWork)
        {
            var query = unitOfWork.KeyStatUnitRepository.GetAll();

            var list = await unitOfWork.KeyStatUnitRepository.GetAll()
                .OrderBy(a => a.GroupName)
                .GroupBy(a => a.GroupName)
                .Select(a => new
                {
                    groupName = a.Key,
                    items = a.Select(u => new
                    {
                        id = u.Id,
                        groupName = u.GroupName,
                        aUnitName = u.AUnitName,
                        bUnitName = u.BUnitName,
                        aFormat = u.AFormat,
                        unitMetricType = u.UnitMetricType,
                        relation = u.Relation,
                        description = u.Description,
                        createdDate = u.CreatedDate
                    })
                })
                .AsNoTracking()
                .ToListAsync();

            return list;
        }

        public async Task<object> ListAsync(IUnitOfWork unitOfWork, string q, int offset, int count)
        {
            var query = unitOfWork.KeyStatRepository.GetAll();

            if (!string.IsNullOrEmpty(q))
            {
                query = query.Where(a => a.Name.Contains(q));
            }

            var countOfData = await query.CountAsync();

            var list = await query
                .OrderBy(a => a.Name)
                .Skip(offset)
                .Take(count)
                .Select(a => new
                {
                    id = a.Id,
                    name = a.Name,
                    note = a.Note,
                    createdDate = a.CreatedDate,
                    productCount = a.ProductStats.Select(p => p.ProductId).Distinct().Count()
                })
                .AsNoTracking()
                .ToListAsync();

            return new
            {
                count = countOfData,
                data = list
            };
        }

        public async Task<object> GetAsync(IUnitOfWork unitOfWork, int id)
        {
            var item = await unitOfWork.KeyStatRepository.GetAll()
                .Where(a => a.Id == id)
                .Select(a => new
                {
                    id = a.Id,
                    name = a.Name,
                    note = a.Note,
                    createdDate = a.CreatedDate,
                })
                .FirstOrDefaultAsync();

            if (item == null)
                throw new DbItemNotFoundException("Not found the Key stat");

            return item;
        }

        public async Task<object> AddAsync(IUnitOfWork unitOfWork, string userId, AddCategoryKeyStatModel model)
        {
            unitOfWork.BeginTransaction();

            KeyStat keyStat = new KeyStat();
            keyStat.Name = model.Name.Trim();
            keyStat.Note = model.Note?.Trim();
            keyStat.CreatedById = userId;
            keyStat.CreatedDate = DateTime.UtcNow;

            unitOfWork.KeyStatRepository.Insert(keyStat);
            await unitOfWork.SaveAsync();

            unitOfWork.CommitTransaction();

            return new
            {
                id = keyStat.Id,
            };
        }

        public async Task EditAsync(IUnitOfWork unitOfWork, string userId, EditCategoryKeyStatModel model)
        {
            unitOfWork.BeginTransaction();

            KeyStat keyStat = unitOfWork.KeyStatRepository.GetById(model.Id);
            keyStat.Name = model.Name.Trim();
            keyStat.Note = model.Note?.Trim();
            keyStat.ModifiedById = userId;
            keyStat.ModifiedDate = DateTime.UtcNow;

            unitOfWork.KeyStatRepository.Edit(keyStat);

            await unitOfWork.SaveAsync();

            unitOfWork.CommitTransaction();
        }

        public async Task DeleteAsync(IUnitOfWork unitOfWork, int id)
        {
            unitOfWork.BeginTransaction();

            var keyStat = await unitOfWork.KeyStatRepository.GetByIdAsync(id);

            var categoryKeyStats = keyStat.CategoryKeyStats.ToList();

            if (categoryKeyStats.Count > 0)
            {
                unitOfWork.CategoryKeyStatRepository.Delete(categoryKeyStats);
            }

            var productStats = keyStat.ProductStats.ToList();
            if (productStats.Count > 0)
            {
                unitOfWork.ProductStatsRepository.Delete(productStats);
            }
            unitOfWork.KeyStatRepository.Delete(keyStat);

            await unitOfWork.SaveAsync();

            unitOfWork.CommitTransaction();
        }
    }
}
