﻿using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class ManufacturerPhoto : BaseEntity
    {
        public int ManufacturerId { get; set; }

        public int PhotoId { get; set; }

        public string Title { get; set; }

        /// ------------------------------------------

        [<PERSON><PERSON><PERSON>("ManufacturerId")]
        public virtual Manufacturer Manufacturer { get; set; }

        [ForeignKey("PhotoId")]
        public virtual Photo Photo { get; set; }
    }
}