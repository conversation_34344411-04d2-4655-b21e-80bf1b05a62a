﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
#if !DEBUG
    [Authorize(Roles = DbConstants.AdminRole)]
#endif
    /// <summary>
    /// 
    /// </summary>
    /// <seealso cref="BIMsmithMarket.Api.Controllers.BaseApiController" />
    [Route("api/[controller]/[action]")]
    public class VanityHistoryController : BaseApiController
    {
        private readonly IVanityHistoryService _vanityHistoryService;

        public VanityHistoryController(IVanityHistoryService vanityHistoryService)
        {
            _vanityHistoryService = vanityHistoryService;
        }

        /// <summary>
        /// Return vanityHistories List
        /// </summary>
        /// <param name="q">The q.</param>
        /// <param name="parentId">The parent identifier.</param>
        /// <param name="entityId">The entity identifier.</param>
        /// <param name="offset">The offset.</param>
        /// <param name="count">The count.</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("List")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> List(string q = null, int parentId = -1, int entityId = -1, int offset = 0, int count = 20)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _vanityHistoryService.ListAsync(unitOfWork, q, parentId, entityId, offset, count));
            }
        }

        /// <summary>
        /// Delete vanityHistory by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Delete")]
        public async Task<IActionResult> Delete(int id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var vanityHistory = await unitOfWork.VanityHistoryRepository.GetByIdAsync(id);
                if (vanityHistory != null)
                {
                    unitOfWork.VanityHistoryRepository.Delete(vanityHistory);
                    await unitOfWork.SaveAsync();

                    CacheHelper.ClearSpecificCache("*/api/VanityHistory/List*");

                    return Ok();
                }
                return BadRequest("Can't find vanityHistory or something went wrong.");
            }
        }


        /// <summary>
        /// Add new vanityHistory
        /// </summary>
        /// <param name="model">The model</param>
        /// <returns>vanityHistory model</returns>
        [HttpPost]
        [ActionName("Add")]
        public async Task<IActionResult> Add([FromBody] AddVanityHistoryModel model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _vanityHistoryService.AddAsync(unitOfWork, model));
            }
        }
    }
}