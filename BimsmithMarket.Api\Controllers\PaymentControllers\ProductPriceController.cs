﻿using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Dto.PaymentsDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers.PaymentControllers
{
#if !DEBUG
    [Authorize(Roles = DbConstants.AdminRole)]
#endif
    [Route("api/[controller]/[action]/{id?}")]
    public class ProductPriceController : BaseApiController
    {
        private readonly IProductPriceService _productPriceService;

        public ProductPriceController(IProductPriceService productPriceService)
        {
            _productPriceService = productPriceService;
        }

        [HttpPost]
        public async Task<IActionResult> Create(ProductPriceCreateDto model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var result = await _productPriceService.AddProductAsync(model, unitOfWork);
                return Ok(result);
            }
        }

        [HttpDelete]
        [Route("/api/ProductPrice/MoveProduct/PaymentPaln/{paymentPlanId}/Product/{productId}")]
        public async Task<IActionResult> Delete(int paymentPlanId, int productId)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                await _productPriceService.MoveProductAsync(paymentPlanId, productId, unitOfWork);
                return Ok(new { success = true });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetProductPrices(int id, bool onlyAvaliable = false)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var result = await _productPriceService.GetProductPricesAsync(id, unitOfWork, onlyAvaliable);
                return Ok(result);
            }
        }
    }
}