﻿using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.Manufacturer;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IManufacturerService
    {
        Task<bool> UpdateThumbnailAsync(int manufacturerId);

        ManufacturerInfoModel GetBySwatchboxId(MarketManufacturerInfoRequestModel model, string swatchboxMarketAccessKey);

        Task<bool> UpdateRegionIdsAsync(int manufacturerId, string regionIds, string userId, IUnitOfWork unitOfWork);

        Task<bool> UpdateStateIdsAsync(int manufacturerId, string stateIds, string userId, IUnitOfWork unitOfWork);

        Task<string[]> VanityUrlListAsync(IUnitOfWork unitOfWork);

        Task<FollowManufacturerDto[]> FollowListAsync(string userId, IUnitOfWork unitOfWork);

        Task<OperationResultDto> UpdateWeightAsync(ManufacturerWeightDto model, string userId, IUnitOfWork unitOfWork);

        Task<PaginationListDto<ManufacturerPopularListDto>> PopularListAsync(
            IUnitOfWork unitOfWork,
            string regionId = null,
            string stateId = null,
            int? categoryId = null,
            string categoryVanityUrl = null,
            int? externalMasterformatId = null,
            string masterformatCode = null,
            string masterformatName = null,
            int offset = 0,
            int count = 10);

        Task<OperationResultDto> RequestPricingAsync(
            RequestPricingDto model,
            string templatePath,
            string userId,
            string userEmail,
            string ip,
            string referer,
            IUserService userService,
            IAnalyticsService analyticsService,
            SlackWebHook slackWebHook,
            IUnitOfWork unitOfWork);

        Task<PaginationListDto<RequestPricingUserListDto>> RequestPricingUserListAsync(
            int manufacturerId,
            IUnitOfWork unitOfWork,
            int offset = 0,
            int count = 10);

        Task<OperationResultDto> DeleteRequestPricingUserAsync(
            int id,
            IUnitOfWork unitOfWork);

        public Task<PaginationListDto<ManufacturerRevitMicrositesPublicListDto>> RevitMicrositesPublicListAsync(
            IUnitOfWork unitOfWork,
            string query = null,
            string regionId = null,
            string stateId = null);

        Task<AddManufacturerResult> AddAsync(AddManufacturerDto model, string userId, IUnitOfWork unitOfWork);

        Task<EditManufacturerResult> EditAsync(EditManufacturerDto model, string userId, IUnitOfWork unitOfWork, bool useExternalTransaction = false);
    }
}