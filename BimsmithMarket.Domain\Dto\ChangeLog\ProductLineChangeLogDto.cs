﻿using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Dto.ChangeLog
{
    public class ProductLineChangeLogDto
    {
        public string ProductLineName { get; set; }

        public string ProductLineDescription { get; set; }

        public string ProductLineNotes { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        public List<ProductLineChangeLogFileDto> Attachments { get; set; }

        public List<int> Certification { get; set; }

        public List<string> QualityIcons { get; set; }

        public List<string> ProductLineForgeId { get; set; }
    }

    public class ProductLineChangeLogFileDto
    {
        public string Type { get; set; }

        public string Name { get; set; }

        public string CustomId { get; set; }

        public string SourceUrl { get; set; }
    }
}