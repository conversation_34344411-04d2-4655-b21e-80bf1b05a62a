﻿using Azure.Storage.Blobs.Specialized;
using Azure.Storage.Queues;
using Azure.Storage.Queues.Models;
using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.Api.Services;
using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.ExternalApi;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using BIMsmithMarket.Services.Search;
using BIMsmithMarket.Services.Search.FullTextSearch;
using Flurl;
using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services;

public class ExternalApiService : IExternalApiService
{
    private readonly IAzureStorageService _azureBlobProvider;
    private readonly IProductService _productService;
    private readonly IFileService _fileService;
    private readonly SendAnalyticsService _sendAnalyticsService;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly Dictionary<string, int> _manufacturerExternalApiKeys = new Dictionary<string, int>()
    {
        { ConfigurationHelper.GetValue("ExternalApi:TrimTexAccessToken"), ExternalApiConstants.TrimTexManufacturerId }
    };

    public ExternalApiService(
        IProductService productService,
        IFileService fileService,
        SendAnalyticsService sendAnalyticsService,
        IAzureStorageService azureStorageService,
        IHttpClientFactory httpClientFactory)
    {
        _productService = productService;
        _fileService = fileService;
        _sendAnalyticsService = sendAnalyticsService;
        _azureBlobProvider = azureStorageService;
        _httpClientFactory = httpClientFactory;
    }

    public async Task<object> SearchProductsAsync(
        string query,
        bool isMasterspec,
        bool onlyFree,
        int count,
        int offset,
        string regionId,
        IUnitOfWork unitOfWork)
    {
        var searchCache = SearchCache.Get(unitOfWork);

        List<dynamic> resultData = new List<dynamic>();

        int countOfProducts = 0;
        var take = (count - resultData.Count) < 0 ? 0 : (count - resultData.Count);
        if (take > 0)
        {
            var productSearchOptions = new ProductSearchOptions
            {
                Query = query,
                RegionId = regionId,
                AttachUrl = isMasterspec ? "productmasterspec" : string.Empty,
                OnlyFree = onlyFree,
                SortType = ProductSortType.Relevant,
                Skip = offset,
                Take = take,
                SearchSource = SearchSource.External
            };

            IProductSearchResults results;

            // call search algorithm
            var databaseResults = ProductSearch.Search(productSearchOptions,
                unitOfWork.ProductRepository.GetAll().AsNoTracking(),
                unitOfWork.StarterRepository.GetAll().AsNoTracking(), searchCache, unitOfWork);

            // use original database results object
            results = databaseResults;

            var primaryResults = results.PrimaryResults(offset, take, unitOfWork, a => new ProductSearchResult<object>
            {
                ProductId = a.Id,
                StarterId = null,
                ItemType = "product",
                Projection = new ProductForExternalDto
                {
                    Id = a.Id,
                    Name = a.Name,
                    Description = a.Description,
                    PhotoUrl = a.PhotoId != null
                        ? a.Photo.MiddleImgUrl
                        : a.ProductPhotos.FirstOrDefault().Photo.MiddleImgUrl
                },
            }, a => new ProductSearchResult<object>()).Select(r => new
            {
                itemType = r.ItemType,
                item = r.Projection
            });

            resultData.AddRange(primaryResults.ToList());
            countOfProducts = results.CountOfProducts;
        }

        var countTotal = countOfProducts;
        var result = new
        {
            count = countTotal,
            data = resultData
        };

        return result;
    }

    public async Task<BlockBlobClient> DownloadProjectFilesAsync(int productId, HttpRequest request, string referer, string displayUrl, IUnitOfWork unitOfWork)
    {
        var productExists = _productService.ProductExists(productId);
        if (!productExists)
            throw new DbItemNotFoundException("Product not found");

        var fileIds = await _productService.GetProjectFilesForProductAsync(productId, unitOfWork);

        if (!fileIds.Any())
            throw new DbItemNotFoundException("Product does not contain project docs");

        var projectTypeIds = await _productService.GetProjectFilesDataTypesAsync(productId, unitOfWork);

        var initDownloadZipResult = await _fileService.InitDownloadZipAsync(fileIds, localFileFolder: Path.GetTempPath());
        if (initDownloadZipResult == null)
            throw new BlobFileNotFoundException("Files not found");

        Stream blobStream = await initDownloadZipResult.File.OpenReadAsync();
        if (blobStream.Length <= 22)
        {
            initDownloadZipResult.File.Delete();
            throw new BlobFileNotFoundException("Empty archive was created");
        }

        try
        {
            foreach (var projectType in projectTypeIds)
                await _sendAnalyticsService.SendEventToGoogleAnalytics(
                    ConfigurationHelper.GetValue("DownloadStatisticsUserId"));

            var newAnalyticsEvents =
                await _sendAnalyticsService.GenerateProjectTypeNewAnalyticsEvents(productId, projectTypeIds,
                    string.Join("_", fileIds.ToArray()), request, unitOfWork);
            await _sendAnalyticsService.SendEventsToNewAnalytics(newAnalyticsEvents);
        }
        catch (Exception ex)
        {
            LogHelper.LogInfo(ConfigurationHelper.GetValue("ExternalApiLogPath"),
                $"ERROR ExternalApiController File download ProductId: {productId} Referer: {referer} Requested Url {displayUrl} Exception: {ex.GetAllMessages()}");
        }

        return initDownloadZipResult.File;
    }

    public async Task<BlockBlobClient> DownloadTechnicalDocsAsync(int productId, HttpRequest request, string referer, string displayUrl, IUnitOfWork unitOfWork)
    {
        var productExists = _productService.ProductExists(productId);
        if (!productExists)
            throw new DbItemNotFoundException("Product not found");

        var fileIds = await _productService.GetProductFilesForProductAsync(productId, unitOfWork);

        if (!fileIds.Any())
            throw new DbItemNotFoundException("Product does not contain technical docs");

        var initDownloadZipResult = await _fileService.InitDownloadZipAsync(fileIds, localFileFolder: Path.GetTempPath());
        if (initDownloadZipResult == null)
            throw new BlobFileNotFoundException("Files not found");

        Stream blobStream = await initDownloadZipResult.File.OpenReadAsync();
        if (blobStream.Length <= 22)
        {
            initDownloadZipResult.File.Delete();
            throw new BlobFileNotFoundException("Empty archive was created");
        }

        try
        {
            await _sendAnalyticsService.SendEventToGoogleAnalytics(ConfigurationHelper.GetValue("DownloadStatisticsUserId"));

            foreach (var fileId in fileIds)
            {
                var newAnalyticsEvents = await _sendAnalyticsService.GenerateProductTypeNewAnalyticsEvents(fileId, productId, default, request, unitOfWork);
                await _sendAnalyticsService.SendEventsToNewAnalytics(newAnalyticsEvents);
            }
        }
        catch (Exception ex)
        {
            LogHelper.LogInfo(ConfigurationHelper.GetValue("ExternalApiLogPath"),
                $"ERROR ExternalApiController File download ProductId: {productId} Referer: {referer} Requested Url {displayUrl} Exception: {ex.GetAllMessages()}");
        }

        return initDownloadZipResult.File;
    }

    public async Task<PaginationListDto<ExternalApiTechnicalDocsMappingDto>> TechnicalDocsMappingAsync(int productId, IUnitOfWork unitOfWork)
    {
        bool productExists = _productService.ProductExists(productId);
        if (!productExists)
            throw new DbItemNotFoundException("Product not found");

        int[] fileIds = await _productService.GetProductFilesForProductAsync(productId, unitOfWork);

        if (!fileIds.Any())
            throw new DbItemNotFoundException("Product does not contain technical docs");

        List<ExternalApiTechnicalDocsMappingDto> items = new();

        items.AddRange(await unitOfWork.ProductFileRepository.GetAll()
            .Where(x => fileIds.Contains(x.FileId))
            .ProjectToType<ExternalApiTechnicalDocsMappingDto>()
            .ToArrayAsync());

        items.AddRange(await unitOfWork.ProductLineFileRepository.GetAll()
            .Where(x => fileIds.Contains(x.FileId))
            .ProjectToType<ExternalApiTechnicalDocsMappingDto>()
            .ToArrayAsync());

        items.AddRange(await unitOfWork.ManufacturerFileRepository.GetAll()
            .Where(x => fileIds.Contains(x.FileId))
            .ProjectToType<ExternalApiTechnicalDocsMappingDto>()
            .ToArrayAsync());

        return new PaginationListDto<ExternalApiTechnicalDocsMappingDto>
        {
            Data = items,
            Count = items.Count
        };
    }

    public async Task<ExternalOperationResultDto> InitProductsUpdateAsync(ExternalApiUpdateProductsDto model, string userId, IUnitOfWork unitOfWork)
    {
        int manufacturerId = await CheckManufacturerAccessAsync(model, unitOfWork);

        QueueClient externalApiUpdateProductsQueue = _azureBlobProvider.GetQueueByName(AzureStorageConstants.ExternalApiUpdateProductsQueue);
        foreach (ExternalApiProductExternalDto externalProductModel in model.Products)
        {
            ExternalApiProductInternalDto internalProductModel = externalProductModel.Adapt<ExternalApiProductInternalDto>();
            internalProductModel.UserId = userId;
            internalProductModel.ManufacturerId = manufacturerId;
            await externalApiUpdateProductsQueue.SendMessageAsync(JsonConvert.SerializeObject(internalProductModel));
        }

        return new ExternalOperationResultDto
        {
            Status = ExternalApiConstants.PendingStatus
        };
    }

    public async Task ProcessExternalApiUpdateProductsQueueAsync(IUnitOfWork unitOfWork)
    {
        QueueClient externalApiUpdateProductsQueue = _azureBlobProvider.GetQueueByName(AzureStorageConstants.ExternalApiUpdateProductsQueue);
        while (await _azureBlobProvider.GetMessageCountAsync(externalApiUpdateProductsQueue.Name) > 0)
        {
            QueueMessage message = await externalApiUpdateProductsQueue.ReceiveMessageAsync();

            if (message == null)
                continue;

            try
            {
                ExternalApiProductInternalDto externalProduct = JsonConvert.DeserializeObject<ExternalApiProductInternalDto>(message.Body.ToString());

                if (externalProduct == null)
                {
                    await externalApiUpdateProductsQueue.DeleteMessageAsync(message.MessageId, message.PopReceipt);
                    continue;
                }

                unitOfWork.BeginTransaction();
                await UpdateProductAsync(externalProduct, unitOfWork);
                unitOfWork.CommitTransaction();
            }
            catch (Exception ex)
            {
                unitOfWork.RollbackTransaction();
                LogHelper.LogInfo(ConfigurationHelper.GetValue("ExternalApiLogPath"), $"[ExternalAPI Product Update error] Exception: {ex.GetAllMessages()} Message: {message.Body}");
            }
            finally
            {
                await externalApiUpdateProductsQueue.DeleteMessageAsync(message.MessageId, message.PopReceipt);
            }
        }
    }

    #region private methods
    private async Task<int> CheckManufacturerAccessAsync(ExternalApiUpdateProductsDto model, IUnitOfWork unitOfWork)
    {
        if (string.IsNullOrWhiteSpace(model.AccessToken))
            throw new UnauthorizedAccessException("Invalid API key");

        if (!_manufacturerExternalApiKeys.TryGetValue(model.AccessToken, out int manufacturerId))
            throw new UnauthorizedAccessException("Invalid API key");

        int[] productIds = model.Products.Select(x => x.Id).ToArray();

        bool authorizedUpdate = await unitOfWork.ProductRepository.GetAll()
            .Where(x => productIds.Contains(x.Id))
            .AllAsync(x => x.ManufacturerId == manufacturerId);

        if (!authorizedUpdate)
            throw new UnauthorizedAccessException("Attempt to update products of another manufacturer");

        return manufacturerId;
    }

    private async Task UpdateProductAsync(ExternalApiProductInternalDto model, IUnitOfWork unitOfWork)
    {
        var productInfo = await unitOfWork.ProductRepository.GetAll()
            .Select(x => new
            {
                Id = x.Id,
                Name = x.Name,
                ManufacturerName = x.Manufacturer.Name
            })
            .FirstOrDefaultAsync();

        await UpdateProductPhotosAsync(model.Photos, model.Id, productInfo.Name, productInfo.ManufacturerName, model.UserId, unitOfWork);
        await UpdateProductAttachmentsAsync(model.Attachments, model.Id, model.UserId, unitOfWork);
        await UpdateProductContentFilesAsync(model.ContentFiles, model.Id, model.UserId, unitOfWork);
    }

    private async Task UpdateProductPhotosAsync(IEnumerable<ExternalApiProductPhotoDto> photos, int productId, string productName, string manufacturerName, string userId, IUnitOfWork unitOfWork)
    {
        IEnumerable<string> photoURLs = photos.Select(x => x.Url);
        IEnumerable<string> existingPhotoUrls = await unitOfWork.ProductPhotoRepository.GetAll()
            .Where(x => x.ProductId == productId && x.Photo != null && x.Photo.UploadUrl != null)
            .Select(x => x.Photo.UploadUrl)
            .ToArrayAsync();
        IEnumerable<string> photoToAddUrls = photoURLs.Except(existingPhotoUrls);
        IEnumerable<string> photoToDeleteUrls = existingPhotoUrls.Except(photoURLs);
        List<ProductPhoto> productPhotos = new List<ProductPhoto>();

        foreach (string photoUrl in photoToAddUrls)
        {
            string attachUrl = photoUrl.Trim();
            if (!attachUrl.StartsWith("http"))
            {
                attachUrl = "http://" + attachUrl;
            }

            string fileName = Path.GetFileName(attachUrl);

            Photo photo = new Photo
            {
                CreatedById = userId,
                CreatedDate = DateTime.UtcNow,
                Name = fileName,
                UploadUrl = attachUrl,
            };

            unitOfWork.PhotoRepository.Insert(photo);
            await unitOfWork.SaveAsync();

            int redirectsCount = 0;
        StartRedirect:

            if (attachUrl.StartsWith("https://"))
            {
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;
                ServicePointManager.ServerCertificateValidationCallback += (sender, certificate, chain, sslPolicyErrors) => true;
            }

            HttpClient httpClient = _httpClientFactory.CreateClient();
            {
                httpClient.Timeout = TimeSpan.FromSeconds(5);
                using HttpResponseMessage photoResponse = await httpClient.GetAsync(attachUrl);
                {
                    if (photoResponse.StatusCode == HttpStatusCode.OK || photoResponse.StatusCode == HttpStatusCode.Accepted)
                    {
                        using (Stream photoResponseStream = photoResponse.Content.ReadAsStream())
                        {
                            string imageName = $"{manufacturerName}-{productName}-revit-";
                            await PhotoProvider.CreateProductPhotosAsync(photo, photoResponseStream, Path.GetExtension(attachUrl), imageName, false);
                            photo.SyncStatusCode = (int)HttpStatusCode.OK;
                            photo.UpdatesCount = 0;
                        }
                    }
                    else if ((photoResponse.StatusCode == HttpStatusCode.Moved || photoResponse.StatusCode == HttpStatusCode.MovedPermanently || photoResponse.StatusCode == HttpStatusCode.Found)
                        && !string.IsNullOrWhiteSpace(photoResponse.Headers.Location?.ToString())
                        && redirectsCount < 2)
                    {
                        redirectsCount++;
                        attachUrl = photoResponse.Headers.Location.ToString();
                        photoResponse.Dispose();
                        goto StartRedirect;
                    }
                }

                productPhotos.Add(new ProductPhoto
                {
                    ProductId = productId,
                    PhotoId = photo.Id,
                    CreatedById = userId,
                    CreatedDate = DateTime.UtcNow
                });
            }

            unitOfWork.ProductPhotoRepository.InsertRange(productPhotos);
            await unitOfWork.SaveAsync();

            //await unitOfWork.ProductPhotoRepository.GetAll()
            //    .Where(x => x.ProductId == productId && x.Photo != null && x.Photo.UploadUrl != null && photoToDeleteUrls.Contains(x.Photo.UploadUrl))
            //    .ExecuteDeleteAsync();
        }
    }

    private async Task UpdateProductAttachmentsAsync(IEnumerable<ExternalApiProductAttachmentDto> attachments, int productId, string userId, IUnitOfWork unitOfWork)
    {
        IEnumerable<string> urls = attachments.Select(x => x.Url);
        IEnumerable<string> existingProductFileSyncUrls = await unitOfWork.ProductFileRepository.GetAll()
            .Where(x => x.ProductId == productId && x.IsAttachment && x.File != null && x.File.SyncUrl != null && x.File.SyncUrl != "")
            .Select(x => x.File.SyncUrl)
            .ToArrayAsync();
        int maxFileWeight = await unitOfWork.ProductFileRepository.GetAll()
            .Where(x => x.ProductId == productId && x.IsAttachment)
            .Select(x => x.Weight)
            .DefaultIfEmpty()
            .MaxAsync();
        IEnumerable<string> productFileUrlsToAdd = urls.Except(existingProductFileSyncUrls).ToList();
        IEnumerable<string> productFileUrlsToDelete = existingProductFileSyncUrls.Except(urls);
        List<ProductFile> productFiles = new List<ProductFile>();

        foreach (string url in productFileUrlsToAdd)
        {
            string attachUrl = url.Trim();
            if (attachUrl[attachUrl.Length - 1] == '/')
            {
                attachUrl = attachUrl.Remove(attachUrl.Length - 1);
            }
            if (!attachUrl.StartsWith("http"))
            {
                attachUrl = "http://" + attachUrl;
            }

            HttpResponseHeadersInfo headers = await WepApiProvider.GetHeadersAsync(attachUrl);
            string fileName = !string.IsNullOrWhiteSpace(headers.FileName) ? headers.FileName : FileHelper.RemoveQueryPath(Path.GetFileName(attachUrl));
            string mediaType = MimeTypeProvider.GetMimeType(fileName);

            ProductFile productFile = new ProductFile()
            {
                ProductId = productId,
                IsAttachment = true,
                Weight = ++maxFileWeight,
                CreatedById = userId,
                File = new Domain.DBModels.File()
                {
                    FileName = fileName,
                    MediaType = mediaType,
                    CreatedById = userId,
                    Title = attachments.FirstOrDefault(x => x.Url == url)?.Type,
                    CreatedDate = DateTime.UtcNow,
                    NextSyncDateTime = DateTime.UtcNow.AddDays(-1),
                    SyncStatusCode = (int)HttpStatusCode.OK,
                    UpdatesCount = 0,
                    SyncUrl = attachUrl,
                    PreviewUrl = new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment("/assets/img/default_preview.png").ToString(),
                    Url = attachUrl,
                    FileSize = 0,
                    CheckSum = string.Empty
                }
            };

            productFiles.Add(productFile);
        }

        unitOfWork.ProductFileRepository.InsertRange(productFiles);
        await unitOfWork.SaveAsync();

        //await unitOfWork.ProductFileRepository.GetAll()
        //    .Where(x => x.ProductId == productId && x.IsAttachment && x.File != null && x.File.SyncUrl != null && x.File.SyncUrl != "" && productFileUrlsToDelete.Contains(x.File.SyncUrl))
        //    .ExecuteDeleteAsync();
    }

    private async Task UpdateProductContentFilesAsync(IEnumerable<ExternalApiProductContentFileDto> contentFiles, int productId, string userId, IUnitOfWork unitOfWork)
    {
        IEnumerable<string> urls = contentFiles.Select(x => x.Url);
        IEnumerable<string> existingProjectFileSyncUrls = await unitOfWork.ProductFileRepository.GetAll()
            .Where(x => x.ProductId == productId && !x.IsAttachment && x.File != null && x.File.SyncUrl != null && x.File.SyncUrl != "")
            .Select(x => x.File.SyncUrl)
            .ToArrayAsync();
        IEnumerable<string> projectFileUrlsToAdd = urls.Except(existingProjectFileSyncUrls).ToList();
        IEnumerable<string> projectFileUrlsToDelete = existingProjectFileSyncUrls.Except(urls);
        List<ProductFile> productFiles = new List<ProductFile>();
        var allProjectTypes = await unitOfWork.ProjectDataTypeRepository.GetAll()
            .Select(x => new
            {
                x.Id,
                x.Title
            })
            .ToArrayAsync();

        foreach (string url in projectFileUrlsToAdd)
        {
            ExternalApiProductContentFileDto contentFile = contentFiles.FirstOrDefault(x => x.Url == url);
            string attachUrl = url;
            if (attachUrl[attachUrl.Length - 1] == '/')
            {
                attachUrl = attachUrl.Remove(attachUrl.Length - 1);
            }
            if (!attachUrl.StartsWith("http"))
            {
                attachUrl = "http://" + attachUrl;
            }

            HttpResponseHeadersInfo headers = await WepApiProvider.GetHeadersAsync(attachUrl);
            string fileName = !string.IsNullOrWhiteSpace(headers.FileName) ? headers.FileName : FileHelper.RemoveQueryPath(Path.GetFileName(attachUrl));
            string mediaType = MimeTypeProvider.GetMimeType(fileName);

            Domain.DBModels.File dbFile = new Domain.DBModels.File();
            dbFile.FileName = fileName;
            dbFile.MediaType = mediaType;
            dbFile.CreatedById = userId;
            dbFile.Title = contentFile?.Type;
            dbFile.CreatedDate = DateTime.UtcNow;
            dbFile.NextSyncDateTime = DateTime.UtcNow.AddDays(-1);
            dbFile.SyncStatusCode = (int)HttpStatusCode.OK;
            dbFile.UpdatesCount = 0;
            dbFile.SyncUrl = attachUrl;
            dbFile.PreviewUrl = new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment("/assets/img/default_preview.png").ToString();
            dbFile.Url = attachUrl;
            dbFile.FileSize = 0;
            dbFile.CheckSum = string.Empty;

            ProductFile productFile = new ProductFile();
            productFile.ProductId = productId;
            productFile.FileId = dbFile.Id;
            productFile.File = dbFile;
            productFile.CreatedById = userId;
            productFile.CreatedDate = DateTime.UtcNow;
            // maybe in some case can be null
            var projectDataType = allProjectTypes.FirstOrDefault(a => a.Title == (contentFile?.Type ?? string.Empty));
            productFile.ProjectDataTypeId = projectDataType?.Id;

            productFiles.Add(productFile);
        }

        unitOfWork.ProductFileRepository.InsertRange(productFiles);
        await unitOfWork.SaveAsync();

        //await unitOfWork.ProductFileRepository.GetAll()
        //    .Where(x => x.ProductId == productId && !x.IsAttachment && x.File != null && x.File.SyncUrl != null && x.File.SyncUrl != "" && projectFileUrlsToDelete.Contains(x.File.SyncUrl))
        //    .ExecuteDeleteAsync();
    }
    #endregion
}