﻿using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    public class ChangeLogController : BaseApiController
    {
        private readonly IChangeLogService _changeLogService;
        private readonly IChangeLogExcelService _changeLogExcelService;

        public ChangeLogController(
            IChangeLogService changeLogService,
            IChangeLogExcelService changeLogExcelService
            )
        {
            _changeLogService = changeLogService;
            _changeLogExcelService = changeLogExcelService;
        }

        ///<summary>
        ///Gets admin list for change logs
        ///</summary>
        ///<param name="entityType">The entity type</param>
        ///<param name="entityId">The entity identifier</param>
        ///<param name="query">The search query</param>
        ///<param name="startDate">The start date</param>
        ///<param name="endDate">The end date</param>
        ///<param name="count">The count to take</param>
        ///<param name="offset">The offset to skip</param>
        /// <returns></returns>
        [HttpGet]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> AdminList(
            EntityType entityType,
            int entityId,
            string query = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int offset = 0,
            int count = 10
            )
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _changeLogService.AdminListAsync(unitOfWork, entityType, entityId, query, startDate, endDate, offset, count));
        }

        ///<summary>
        ///Exports change logs
        ///</summary>
        ///<summary>
        ///Gets admin list for change logs
        ///</summary>
        ///<param name="entityType">The entity type</param>
        ///<param name="entityId">The entity identifier</param>
        ///<param name="startDate">The start date</param>
        ///<param name="endDate">The end date</param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        [ActionName("ExcelExport")]
        public async Task<IActionResult> ExcelExport(
            EntityType entityType,
            int entityId,
            DateTime? startDate = null,
            DateTime? endDate = null
            )
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            string excelPath = await _changeLogExcelService.GetExcelAsync(unitOfWork, entityType, entityId, startDate, endDate);
            return PhysicalFile(excelPath, "text/csv", $"Change Logs.xlsx");
        }
    }
}