﻿using BIMsmithMarket.Domain.Enums;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels.PaymentDbModels
{
    /// <summary>
    /// All products that was bought by user
    /// </summary>
    public class UserPaidProduct : BaseEntity
    {
        public int PaidProductId { get; set; }
        [ForeignKey("PaidProductId")]
        public virtual Product Product { get; set; }

        public string UserId { get; set; }
        [ForeignKey("UserId")]
        public virtual ApplicationUser User { get; set; }

        public PriceType PriceType { get; set; }

        // Stripe session
        public string StripeSessionId { get; set; }

        public string StripeSessionStatus { get; set; }

        [NotMapped]
        public bool Paid { get => StripeSessionStatus == "paid"; }

        public DateTime? StripeSessionMark { get; set; }
    }
}
