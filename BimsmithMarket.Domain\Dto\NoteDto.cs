﻿using BIMsmithMarket.Domain.CustomAttributes;
using BIMsmithMarket.Domain.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Dto
{
    public class AddNoteDto
    {
        [StringLength(100)]
        [Required]
        public string Name { get; set; }

        public DateTime? DateToNotifyUsers { get; set; }

        public DateTime? DateIsEffectiveTill { get; set; }

        [RequiredNotDefault]
        public EntityType EntityType { get; set; }

        [RequiredNotDefault]
        public int EntityId { get; set; }

        [EmailAddressList]
        public string[] NotificationList { get; set; }
    }

    public class EditNoteDto : AddNoteDto
    {
        [RequiredNotDefault]
        public int Id { get; set; }
    }

    public class AdminGetNoteDto : EditNoteDto
    {

        public DateTime CreatedDate { get; set; }

        public string UserEmail { get; set; }
    }

    public class AdminListNoteInternalDto : AdminGetNoteDto
    {
    }

    public class AdminListNoteDto
    {
        public int Id { get; set; }

        public DateTime CreatedDate { get; set; }

        public string UserEmail { get; set; }

        public string Name { get; set; }

        public string DateToNotifyUsers { get; set; }

        public string DateIsEffectiveTill { get; set; }

        public EntityType EntityType { get; set; }

        public int EntityId { get; set; }

        public string[] NotificationList { get; set; }
    }

    public class NoteNotificationDto
    {
        public int Id { get; set; }

        public string Name { get; set; }

        public EntityType EntityType { get; set; }

        public int EntityId { get; set; }

        public string[] NotificationList { get; set; }
    }

    public class NoteNotificationEmailDto
    {
        public string NoteName { get; set; }

        public string EntityLinkUrl { get; set; }

        public string EntityLinkCaption { get; set; }

        public List<string> NotificationList { get; set; }
    }

    public class BaseNoteNotificationEntityInfoDto
    {

    }

    public class NoteNotificationProductLineInfoDto : BaseNoteNotificationEntityInfoDto
    {
        public int ProductLineId { get; set; }

        public string ProductLineName { get; set; }

        public int ManufacturerId { get; set; }

        public string ManufacturerName { get; set; }
    }
}