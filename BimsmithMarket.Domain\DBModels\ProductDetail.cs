﻿using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class ProductDetail : BaseEntity
    {
        public int ProductId { get; set; }

        public int DetailId { get; set; }

        [ForeignKey("DetailId")]
        public virtual Detail Detail { get; set; }

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }
    }
}