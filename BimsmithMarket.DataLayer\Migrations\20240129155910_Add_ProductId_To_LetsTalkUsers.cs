﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BIMsmithMarket.DataLayer.Migrations
{
    /// <inheritdoc />
    public partial class Add_ProductId_To_LetsTalkUsers : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "ProductId",
                table: "UserBIMsmithLTManufacturers",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_UserBIMsmithLTManufacturers_ProductId",
                table: "UserBIMsmithLTManufacturers",
                column: "ProductId");

            migrationBuilder.AddForeignKey(
                name: "FK_UserBIMsmithLTManufacturers_Products_ProductId",
                table: "UserBIMsmithLTManufacturers",
                column: "ProductId",
                principalTable: "Products",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UserBIMsmithLTManufacturers_Products_ProductId",
                table: "UserBIMsmithLTManufacturers");

            migrationBuilder.DropIndex(
                name: "IX_UserBIMsmithLTManufacturers_ProductId",
                table: "UserBIMsmithLTManufacturers");

            migrationBuilder.DropColumn(
                name: "ProductId",
                table: "UserBIMsmithLTManufacturers");
        }
    }
}
