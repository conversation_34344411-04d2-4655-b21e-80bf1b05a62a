﻿using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class BlogTargetType
    {
        public int Id { get; set; }

        public TargetBlogType TargetBlogType { get; set; }

        public int BlogPostId { get; set; }

        [ForeignKey("BlogPostId")]
        public virtual BlogPost BlogPost { get; set; }
    }

    public enum TargetBlogType
    {
        ForMarketAndAngulerisBlogs = 0, //old unused type
        ForMarketBlog = 1,
        ForAngulerisBlog = 2,
        SwatchBox = 3,
        StudioVertex = 5,
        FeaturedHomepage = 6,
        All = 4
    }
}