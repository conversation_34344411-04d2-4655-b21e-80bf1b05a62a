﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class ManufacturerStyleFile : IBaseEntity
    {
        [Key]
        public int Id { get; set; }

        public string FileVersion { get; set; }

        public string Title { get; set; }

        public int ManufacturerId { get; set; }

        public int FileId { get; set; }

        public StyleFileType Type { get; set; }

        public string CreatedById { get; set; }

        public DateTime CreatedDate { get; set; }

        public string ModifiedById { get; set; }

        public DateTime? ModifiedDate { get; set; }

        /// ------------------------------------------
        [<PERSON><PERSON><PERSON>("CreatedById")]
        public virtual ApplicationUser CreatedBy { get; set; }

        [ForeignKey("ModifiedById")]
        public virtual ApplicationUser ModifiedBy { get; set; }

        [ForeignKey("ManufacturerId")]
        public virtual Manufacturer Manufacturer { get; set; }

        [ForeignKey("FileId")]
        public virtual File File { get; set; }
    }

    public enum StyleFileType
    {
        Microsite = 1,
        ProductPageViaMicrosite = 2
    }
}