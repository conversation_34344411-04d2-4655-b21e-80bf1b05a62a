﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Configurations>Debug;Release;Dev;UAT</Configurations>
	</PropertyGroup>

	<PropertyGroup>
		<ServerGarbageCollection>true</ServerGarbageCollection>
		<ConcurrentGarbageCollection>true</ConcurrentGarbageCollection>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="wwwroot\assets\**" />
	  <Content Remove="wwwroot\assets\**" />
	  <EmbeddedResource Remove="wwwroot\assets\**" />
	  <None Remove="wwwroot\assets\**" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="MailChimp.Net.V3" Version="5.5.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.7" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.7">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Quartz.AspNetCore" Version="3.12.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.7.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.7.0" />
	</ItemGroup>

	<ItemGroup>
		<None Include="wwwroot\Emails\BIMRequestTemplate.html" />
		<None Include="wwwroot\Emails\DetailContentCommentTemplate.html" />
		<None Include="wwwroot\Emails\LetsTalkTemplate.html" />
		<None Include="wwwroot\Emails\LetsTalkWithProductTemplate.html" />
		<None Include="wwwroot\Emails\LunchAndLearnTemplate.html" />
		<None Include="wwwroot\Emails\NoteNotificationTemplate.html" />
		<None Include="wwwroot\Emails\PasswordRecoveryTemplate.html" />
		<None Include="wwwroot\Emails\ProductContentCommentTemplate.html" />
		<None Include="wwwroot\Emails\ProductRequestTemplate.html" />
		<None Include="wwwroot\Emails\ScheduleSessionRequestTemplate.html" />
		<None Include="wwwroot\Img\Cert_1.png" />
		<None Include="wwwroot\Img\Cert_2.png" />
		<None Include="wwwroot\Img\Cert_3.png" />
		<None Include="wwwroot\Img\Cert_4.png" />
		<None Include="wwwroot\Img\Def_Photo_b.png" />
		<None Include="wwwroot\Img\Def_Photo_s.png" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\BIMsmithMarket.Core\BIMsmithMarket.Core.csproj" />
		<ProjectReference Include="..\BIMsmithMarket.DataLayer\BIMsmithMarket.DataLayer.csproj" />
		<ProjectReference Include="..\BIMsmithMarket.Domain\BIMsmithMarket.Domain.csproj" />
		<ProjectReference Include="..\BIMsmithMarket.Services\BIMsmithMarket.Services.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <None Update="App_Data\Files\img\text_preview.png">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	</ItemGroup>

</Project>