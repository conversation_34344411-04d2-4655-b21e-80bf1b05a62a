﻿using Mapster;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public static class MapsterConfig
    {
        public static void RegisterAllProfiles(TypeAdapterConfig config)
        {
            config.Default.NameMatchingStrategy(NameMatchingStrategy.Flexible);
            config.AllowImplicitDestinationInheritance = true;
            ManufacturerMap.Register(config);
            ProductPriceMap.Register(config);
            AnnouncementMap.Register(config);
            UserPaidProductMap.Register(config);
            PluginFileMap.Register(config);
            ProductLineMap.Register(config);
            HealthDashboardMap.Register(config);
            ProductMap.Register(config);
            StarterMap.Register(config);
            SketchupMap.Register(config);
            ProductFileMap.Register(config);
            ProjectDataTypeMap.Register(config);
            StaticExcelMap.Register(config);
            DropboxMap.Register(config);
            CategoryMap.Register(config);
            EventMap.Register(config);
            ChangeLogMap.Register(config);
            NoteMap.Register(config);
            AnalyticsMap.Register(config);
            ExternalApiMap.Register(config);
            PhotoMap.Register(config);
            ManufacturerBackupMap.Register(config);
            RevitProcessingMap.Register(config);
        }
    }
}