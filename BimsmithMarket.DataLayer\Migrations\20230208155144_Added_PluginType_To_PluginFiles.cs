﻿using BIMsmithMarket.Domain.Enums;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BIMsmithMarket.DataLayer.Migrations
{
    public partial class Added_PluginType_To_PluginFiles : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "PluginType",
                table: "PluginFiles",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.Sql($"UPDATE PluginFiles SET PluginType = {(int)PluginType.BIMsmith}");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PluginType",
                table: "PluginFiles");
        }
    }
}
