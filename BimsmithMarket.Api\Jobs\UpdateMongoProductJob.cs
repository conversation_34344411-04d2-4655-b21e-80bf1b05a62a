﻿using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Quartz;
using Serilog;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Jobs
{
    [DisallowConcurrentExecution]
    public class UpdateMongoProductJob : IJob
    {
        private readonly string _jobName = "Update Mongo Product Job";
        private IProductService _productService;

        public UpdateMongoProductJob(IProductService productService)
        {
            _productService = productService;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            Log.Information($"[{_jobName}] started");

            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            await _productService.UpdateMongoProductsAsync(null, unitOfWork);

            Log.Information($"[{_jobName}] finished");
        }
    }
}