﻿using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;

namespace BIMsmithMarket.Core.Providers
{
    public static class CommonExcelProvider
    {
        public static void ValidCellValue(Cell cell, string compare, SharedStringTablePart sharedStringTable, List<string> errorsList, string error)
        {
            var cellValue = GetCellStringValue(cell, sharedStringTable);

            if (cellValue != compare)
            {
                errorsList.Add(error);
            }
        }

        public static string GetCellStringValue(Cell cell, SharedStringTablePart sharedStringTable)
        {
            string result = null;
            if (cell != null)
            {
                if (cell.DataType != null)
                {
                    if (cell.DataType.Value == CellValues.SharedString)
                    {
                        result = GetSharedString(cell.InnerText, sharedStringTable);
                    }
                    else if (cell.DataType.Value == CellValues.String || cell.DataType.Value == CellValues.Number)
                    {
                        result = cell.InnerText.Trim();
                    }
                    else
                    {
                        result = cell.InnerText.Trim();
                    }
                }
                else
                {
                    result = cell.InnerText.Trim();
                    double decResult = 0;
                    if (double.TryParse(result, NumberStyles.Number, CultureInfo.InvariantCulture, out decResult))
                    {
                        if (result.Contains(".") && result.Split('.')[1].Count() > 7)
                        {
                            var roundResult = Math.Round(decResult, 2);
                            result = roundResult.ToString(CultureInfo.InvariantCulture);
                        }
                    }
                }
            }
            return result;
        }

        public static int GetCellIntValue(Cell cell, SharedStringTablePart sharedStringTable, List<string> errorList)
        {
            string result = null;
            if (cell != null)
            {
                if (cell.DataType != null)
                {
                    if (cell.DataType.Value == CellValues.Number || cell.DataType.Value == CellValues.String)
                    {
                        result = cell.InnerText;
                    }
                    else if (cell.DataType.Value == CellValues.SharedString)
                    {
                        result = GetSharedString(cell.InnerText, sharedStringTable);
                    }
                }
                else if (cell.InnerText != null)
                {
                    result = cell.InnerText;
                }

                int intResult;
                if (!int.TryParse(result, out intResult))
                {
                    errorList.Add($"Invalid Data in cell {cell.CellReference}***");
                }
                return intResult;
            }

            return 0;
        }

        public static int? GetCellNullIntValue(Cell cell, SharedStringTablePart sharedStringTable, List<string> errorList)
        {
            string resultString = null;
            if (cell != null)
            {
                if (cell.DataType != null)
                {
                    if (cell.DataType.Value == CellValues.Number || cell.DataType.Value == CellValues.String)
                    {
                        resultString = cell.InnerText;
                    }
                }
                else if (cell.InnerText != null)
                {
                    resultString = cell.InnerText;
                }

                int? result = null;
                int intResult;
                if (!string.IsNullOrWhiteSpace(resultString))
                {
                    if (!int.TryParse(resultString, out intResult))
                    {
                        errorList.Add(string.Format("Invalid Data in cell {0}***", cell.CellReference));
                    }
                    else
                    {
                        result = intResult;
                    }
                }
                return result;
            }
            return null;
        }

        public static Cell ConstructCell(string value, CellValues dataType, int columnIndex, int rowIndex, uint styleIndex = 0)
        {
            var columnLetter = ColumnIndexToColumnLetter(columnIndex);
            if (value != null)
            {
                value = value.Replace(Convert.ToChar((byte)0x1D), ' ');
                value = value.Replace(Convert.ToChar((byte)0x1C), ' ');
                value = value.Replace(Convert.ToChar((byte)0x1F), ' ');
            }
            return new Cell()
            {
                CellReference = new StringValue($"{columnLetter}{rowIndex}"),
                CellValue = new CellValue(value),
                DataType = new EnumValue<CellValues>(dataType),
                StyleIndex = styleIndex
            };
        }

        public static string GetSharedString(string value, SharedStringTablePart sharedStringTable)
        {
            string result = null;
            if (sharedStringTable != null)
            {
                result = sharedStringTable.SharedStringTable.ElementAt(int.Parse(value)).InnerText.Trim();
            }

            return result;
        }

        public static string ColumnIndexToColumnLetter(int colIndex)
        {
            int div = colIndex;
            string colLetter = String.Empty;
            int mod = 0;

            while (div > 0)
            {
                mod = (div - 1) % 26;
                colLetter = (char)(65 + mod) + colLetter;
                div = (div - mod) / 26;
            }
            return colLetter;
        }

        public static bool HasCellValue(Cell cell, SharedStringTablePart sharedStringTable)
        {
            if (cell != null)
            {
                if (cell.DataType != null)
                {
                    if (cell.DataType.Value == CellValues.Number || cell.DataType.Value == CellValues.String)
                    {
                        return !string.IsNullOrWhiteSpace(cell.InnerText);
                    }
                    else if (cell.DataType.Value == CellValues.SharedString)
                    {
                        return !string.IsNullOrWhiteSpace(GetSharedString(cell.InnerText, sharedStringTable));
                    }
                }
                else if (cell.InnerText != null)
                {
                    return !string.IsNullOrWhiteSpace(cell.InnerText);
                }
            }

            return false;
        }

        public static void CreateDocument(string filePath, out SpreadsheetDocument spreadsheetDocument, out WorkbookPart workbookpart, out WorksheetPart worksheetPart)
        {
            // Create a spreadsheet document by supplying the filepath.
            // By default, AutoSave = true, Editable = true, and Type = xlsx.
            spreadsheetDocument = SpreadsheetDocument.Create(filePath, SpreadsheetDocumentType.Workbook);

            // Add a WorkbookPart to the document.
            workbookpart = spreadsheetDocument.AddWorkbookPart();
            workbookpart.Workbook = new Workbook();

            // Add a WorksheetPart to the WorkbookPart.
            worksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            worksheetPart.Worksheet = new Worksheet();


            Fonts fonts = new Fonts(
                new DocumentFormat.OpenXml.Spreadsheet.Font( // Index 0 - default
                    new FontSize() { Val = 10 }
                ));

            Fills fills = new Fills(
                    new Fill(new PatternFill() { PatternType = PatternValues.None }), // Index 0 - default
                    new Fill(new PatternFill(new ForegroundColor { Rgb = new HexBinaryValue() { Value = "FFE7E6E6" } }) { PatternType = PatternValues.Solid }), // Index 1 - default
                    new Fill(new PatternFill(new ForegroundColor { Rgb = new HexBinaryValue() { Value = "FFBDD7EE" } }) { PatternType = PatternValues.Solid }) // Index 2 - header
                );

            Borders borders = new Borders(
                    new Border() // index 0 default
                );

            CellFormats cellFormats = new CellFormats(
                    new CellFormat(), // default
                    new CellFormat { FontId = 0, FillId = 1, BorderId = 0, ApplyFill = true }, // body
                    new CellFormat { FontId = 0, FillId = 2, BorderId = 0, ApplyFill = true }, // header
                    new CellFormat { FontId = 0, FillId = 2, BorderId = 0, ApplyFill = true, Alignment = new Alignment { TextRotation = 90, WrapText = true } } // header for certificate
                );


            Stylesheet styleSheet = new Stylesheet(fonts, fills, borders, cellFormats);

            // Adding style
            WorkbookStylesPart stylePart = workbookpart.AddNewPart<WorkbookStylesPart>();
            stylePart.Stylesheet = styleSheet;
            stylePart.Stylesheet.Save();
        }

        public static void CreateSheet(SpreadsheetDocument spreadsheetDocument, WorkbookPart workbookpart, WorksheetPart worksheetPart, string title)
        {
            //Sheets
            Sheets sheets = workbookpart.Workbook.AppendChild(new Sheets());

            //Append a new worksheet and associate it with the workbook.
            Sheet sheet = new Sheet()
            {
                Id = spreadsheetDocument.WorkbookPart.
                GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = title
            };
            sheets.Append(sheet);
            workbookpart.Workbook.Save();
        }
    }
}