﻿using Serilog;
using System;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace BIMsmithMarket.Core.Helpers
{
    public static class LogHelper
    {
        public static void LogExternalApiCall(string modelString, string method)
        {
            var filePath = ConfigurationHelper.GetValue("ExternalApiLogFilePath");
            var folder = Path.GetDirectoryName(filePath);
            if (!Directory.Exists(folder)) Directory.CreateDirectory(folder);

            using (var writer = new StreamWriter(filePath, true))
            {
                writer.Write(Environment.NewLine);
                writer.WriteLine(DateTime.UtcNow);
                writer.WriteLine("Method: " + method);
                writer.WriteLine("Model: ");
                writer.WriteLine(modelString);
            }
        }

        public static void LogInfo(string filePath, string message)
        {
            try
            {
                var folder = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(folder)) Directory.CreateDirectory(folder);

                using (var writer = new StreamWriter(filePath, true))
                {
                    writer.Write(Environment.NewLine);
                    writer.WriteLine(DateTime.UtcNow);
                    writer.WriteLine(message);
                }
            }
            catch
            { }
        }

        public static ILogger Initialize(string name)
        {
            var path = Path.Combine(ConfigurationHelper.GetValue("BaseLogPath"), string.Format(@"{0}.txt", name));
            ILogger logger = new LoggerConfiguration()
                .MinimumLevel.Debug()
                .WriteTo.File(path, rollingInterval: RollingInterval.Day)
                .CreateLogger();

            return logger;
        }

        public static async Task LogHttpClientRetryAsync(HttpResponseMessage response, Exception exception)
        {
            StringBuilder stringBuilder = new("[HttpClient Retry] ");
            stringBuilder.Append($"Request Method: {response?.RequestMessage?.Method} ");
            stringBuilder.Append($"Request Uri: {response?.RequestMessage?.RequestUri} ");
            stringBuilder.Append($"Request Content: {(response?.RequestMessage?.Content != null ? await response.RequestMessage.Content.ReadAsStringAsync() : string.Empty)} ");
            stringBuilder.Append($"Response Status Code: {response?.StatusCode} ");
            stringBuilder.Append($"Response Content: {(response?.Content != null ? await response.Content.ReadAsStringAsync() : string.Empty)} ");
            stringBuilder.Append($"Exception: {exception?.GetAllMessages()}");
            Log.Error($"[HttpClient] {stringBuilder.ToString()}");
        }
    }
}