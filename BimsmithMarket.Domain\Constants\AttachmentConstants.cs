﻿using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Constants
{
    public static class AttachmentConstants
    {
        public static readonly IReadOnlyList<string> AttachmentTitles = new List<string>
        {
            ThreePartSpecificationFileTitle,
            MasterspecFileTitle,
            CatalogFileTitle,
            EPDFileTitle,
            HPDFileTitle,
            ImageFileTitle,
            InstallationGuideFileTitle,
            ProductBrochureFileTitle,
            ProductCutSheetFileTitle,
            SafetyDataSheetFileTitle,
            SubmittalFileTitle,
            TestingDataFileTitle,
            ULGreenguardFileTitle,
            ULGreenguardGoldFileTitle,
            WarrantyFileTitle
        };

        public const string MasterspecUrl = "productmasterspec";

        public const string MasterspecFileTitle = "3-Part Specification - Masterspec";

        public const string ThreePartSpecificationFileTitle = "3-Part Specification";

        public const string CatalogFileTitle = "Catalog";

        public const string EPDFileTitle = "EPD";

        public const string HPDFileTitle = "HPD";

        public const string ImageFileTitle = "Image";

        public const string InstallationGuideFileTitle = "Installation Guide";

        public const string ProductBrochureFileTitle = "Product Brochure";

        public const string ProductCutSheetFileTitle = "Product Cut Sheet";

        public const string SafetyDataSheetFileTitle = "Safety Data Sheet";

        public const string SubmittalFileTitle = "Submittal";

        public const string TestingDataFileTitle = "Testing Data";

        public const string ULGreenguardFileTitle = "UL Greenguard";

        public const string ULGreenguardGoldFileTitle = "UL Greenguard Gold";

        public const string WarrantyFileTitle = "Warranty";
    }
}