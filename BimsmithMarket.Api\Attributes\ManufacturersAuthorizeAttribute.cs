﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Attributes
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
    public class ManufacturersAuthorizeAttribute : Attribute, IAsyncAuthorizationFilter
    {
        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            IWebHostEnvironment webhostEnvironment = context.HttpContext.RequestServices.GetService<IWebHostEnvironment>();
            UserManager<ApplicationUser> userManager = context.HttpContext.RequestServices.GetService<UserManager<ApplicationUser>>();

            string manufacturersAccessConfigFile = Path.Combine(webhostEnvironment.WebRootPath, "AllowedManufacturers.json");
            List<string> allowedManufacturersList = AllowedManufacturersModel.Create(manufacturersAccessConfigFile).AllowedManufacturers.Select(x => x.ManufacturerUrl).ToList();
            string reqUri = context.HttpContext.Request.Host.ToString();
            string userId = AuthHelper.GetUserInfo(context.HttpContext.Request, ClaimTypes.NameIdentifier);
            ApplicationUser user = await userManager.FindByIdAsync(userId);
            bool hasRequiredRole = await userManager.IsInRoleAsync(user, DbConstants.AdminRole) || await userManager.IsInRoleAsync(user, DbConstants.ContentManagerRole);
            bool hasAccess = allowedManufacturersList.Contains(reqUri)
                             || hasRequiredRole
                             || ConfigurationHelper.GetValue("Environment") != "prod";

            if (!hasAccess)
                context.Result = new JsonResult(new { message = "Unauthorized" }) { StatusCode = StatusCodes.Status401Unauthorized };
        }
    }
}