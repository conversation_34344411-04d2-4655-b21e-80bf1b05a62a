﻿using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Dto
{
    public class ProjectTypePublicListDto
    {
        public int Id { get; set; }

        public string Title { get; set; }

        public string Description { get; set; }

        public string VanityUrl { get; set; }

        public string MetaTitle { get; set; }

        public string MetaDescription { get; set; }

        public string MetaKeyWords { get; set; }

        public string Header { get; set; }

        public PhotoDto Image { get; set; }

        public PhotoDto ActiveImage { get; set; }

        public PhotoDto DefaultImage { get; set; }

        public int? ParentId { get; set; }

        public virtual ICollection<int> ChildrenIds { get; set; }

        public ProjectTypePublicListDto()
        {
            ChildrenIds = new List<int>();
        }
    }
}