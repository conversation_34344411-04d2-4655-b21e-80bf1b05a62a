﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.ChangeLog;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Domain.Models.MassTransit.Events;
using BIMsmithMarket.Services.Helpers;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;

using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Services
{
    public class ProductLineService : IProductLineService
    {
        private readonly IChangeLogService _changeLogService;
        private readonly IPublishEndpoint _publishEndpoint;

        public ProductLineService(
            IChangeLogService changeLogService,
            IPublishEndpoint publishEndpoint)
        {
            _changeLogService = changeLogService;
            _publishEndpoint = publishEndpoint;
        }

        public async Task<bool> DeleteProductLineAsync(IUnitOfWork unitOfWork, ProductLine productLine)
        {
            //Update product 
            //-------------------
            var products = await unitOfWork.ProductRepository.GetAll().Where(a => a.ProductLineId == productLine.Id).ToListAsync();

            foreach (var product in products)
            {
                product.ProductLineId = null;
                unitOfWork.ProductRepository.Edit(product);
            }
            //------------------

            var productLineCertificates = await unitOfWork.ProductLineCertificateRepository.GetAll().Where(a => a.ProductLineId == productLine.Id).ToListAsync();
            unitOfWork.ProductLineCertificateRepository.Delete(productLineCertificates);

            var productLineFiles = await unitOfWork.ProductLineFileRepository.GetAll().Where(a => a.ProductLineId == productLine.Id).ToListAsync();
            unitOfWork.ProductLineFileRepository.Delete(productLineFiles);

            var productLineQualityItems = await unitOfWork.ProductLineQualityItemRepository.GetAll().Where(a => a.ProductLineId == productLine.Id).ToListAsync();
            unitOfWork.ProductLineQualityItemRepository.Delete(productLineQualityItems);

            unitOfWork.ProductLineRepository.Delete(productLine);

            await unitOfWork.SaveAsync();

            return true;
        }

        public async Task<AddEditProductLineResponse> AddOrUpdateProductLineAsync(AddProductLineModel model, IUnitOfWork unitOfWork, string userId, int? productLineId = null, bool isRestoredFromBackup = false)
        {
            ProductLine productLine;
            if (productLineId.HasValue && !isRestoredFromBackup)
            {
                productLine = await unitOfWork.ProductLineRepository.GetByIdAsync(productLineId.Value);
                if (productLine == null) throw new Exception("Product line not found");
            }
            else
            {
                productLine = new ProductLine();
            }
            productLine.Name = model.Name.Trim();
            productLine.ManufacturerId = model.ManufacturerId;
            productLine.Description = model.Description;
            productLine.RegionIds = model.RegionIds;
            productLine.StateIds = model.StateIds;
            productLine.Note = model.Note;

            if (productLineId.HasValue && !isRestoredFromBackup)
            {
                productLine.ModifiedById = userId;
                productLine.ModifiedDate = DateTime.UtcNow;
                unitOfWork.ProductLineRepository.Edit(productLine);
            }
            else
            {
                productLine.CreatedById = userId;
                productLine.CreatedDate = DateTime.UtcNow;
                if (isRestoredFromBackup)
                {
                    productLine.Id = productLineId.Value;
                    var setIdentityOnQuery = SqlHelper.GenerateSetIdentityOnQuery(DbConstants.ProductLinesTableName);
                    var insertQuery = SqlHelper.GenerateInsertQuery(productLine, DbConstants.ProductLinesTableName);
                    var setIndentityOffQuery = SqlHelper.GenerateSetIdentityOffQuery(DbConstants.ProductLinesTableName);
                    var query = string.Join(";", setIdentityOnQuery, insertQuery.Query, setIndentityOffQuery);
                    await unitOfWork.CurrentDbContext.Database.ExecuteSqlRawAsync(query, insertQuery.Parameters);
                }
                else
                {
                    unitOfWork.ProductLineRepository.Insert(productLine);
                }
            }

            await unitOfWork.SaveAsync();

            await AddOrUpdateProductLineAttachmentsAsync(productLine, unitOfWork, model.Files, userId);
            await AddOrUpdateProductLineForgeProductLines(productLine, unitOfWork, model.ForgeProductLineIds);
            await AddOrUpdateProductProjectFilesAsync(productLine, unitOfWork, model.ProjectFiles, userId);
            await AddOrUpdateProductLineCertificatesAsync(productLine, unitOfWork, model.ExternalCertificateIds, userId);
            await AddOrUpdateProductLineQualityItemsAsync(productLine, unitOfWork, model.QualityItemIds, userId);

            await unitOfWork.SaveAsync();

            return new AddEditProductLineResponse
            {
                Id = productLine.Id,
                Description = productLine.Description,
                Name = productLine.Name,
                RegionIds = productLine.RegionIds,
                StateIds = productLine.StateIds
            };
        }

        public async Task<bool> UpdateRegionIdsAsync(int id, string regionIds, string userId, IUnitOfWork unitOfWork)
        {
            var productLine = await unitOfWork.ProductLineRepository.GetByIdAsync(id);
            if (productLine == null)
                throw new DbItemNotFoundException($"Manufacturer not found {id}");

            ProductLineChangeLogDto currentModel = await GetModelForChangeLogAsync(id, unitOfWork);

            await unitOfWork.ProductLineRepository.GetAll()
                .Where(x => x.Id == id)
                .ExecuteUpdateAsync(x => x.SetProperty(p => p.RegionIds, regionIds)
                    .SetProperty(p => p.ModifiedById, userId)
                    .SetProperty(p => p.ModifiedDate, DateTime.UtcNow));

            ProductLineChangeLogDto newModel = await GetModelForChangeLogAsync(id, unitOfWork);
            await _changeLogService.TrackAndLogChangesAsync(currentModel, newModel, EntityType.ProductLine, productLine.Id, userId, unitOfWork);

            return true;
        }

        public async Task<bool> UpdateStateIds(int id, string stateIds, string userId, IUnitOfWork unitOfWork)
        {
            var productLine = await unitOfWork.ProductLineRepository.GetByIdAsync(id);
            if (productLine == null)
                throw new DbItemNotFoundException($"Manufacturer not found {id}");

            ProductLineChangeLogDto currentModel = await GetModelForChangeLogAsync(id, unitOfWork);

            await unitOfWork.ProductLineRepository.GetAll()
                .Where(x => x.Id == id)
                .ExecuteUpdateAsync(x => x.SetProperty(p => p.StateIds, stateIds)
                    .SetProperty(p => p.ModifiedById, userId)
                    .SetProperty(p => p.ModifiedDate, DateTime.UtcNow));

            ProductLineChangeLogDto newModel = await GetModelForChangeLogAsync(id, unitOfWork);
            await _changeLogService.TrackAndLogChangesAsync(currentModel, newModel, EntityType.ProductLine, productLine.Id, userId, unitOfWork);

            return true;
        }

        public async Task<BaseNameListDto[]> GetNamesListAsync(EntityIdsDto model, IUnitOfWork unitOfWork)
        {
            return await unitOfWork.ProductLineRepository.GetAll()
                .Where(x => model.Ids.Distinct().Contains(x.Id))
                .ProjectToType<BaseNameListDto>()
                .ToArrayAsync();
        }

        public async Task<ProductLineAddEditResultDto> AddAsync(AddProductLineModel model, string userId, IUnitOfWork unitOfWork)
        {
            unitOfWork.BeginTransaction();

            ProductLine productLine = new ProductLine();
            productLine.Name = model.Name.Trim();
            productLine.ManufacturerId = model.ManufacturerId;
            productLine.Description = model.Description;
            productLine.RegionIds = model.RegionIds;
            productLine.StateIds = model.StateIds;
            productLine.Note = model.Note;
            productLine.CreatedById = userId;
            productLine.CreatedDate = DateTime.UtcNow;

            unitOfWork.ProductLineRepository.Insert(productLine);

            await unitOfWork.SaveAsync();

            //Add files
            foreach (var fileModel in model.Files)
            {
                ProductLineFile productLineFile = new ProductLineFile();
                productLineFile.ProductLineId = productLine.Id;
                productLineFile.CustomFileId = fileModel.CustomFileId;
                productLineFile.FileId = fileModel.FileId;
                productLineFile.IsAttachment = true;
                productLineFile.CreatedById = userId;
                productLineFile.CreatedDate = DateTime.UtcNow;
                productLineFile.WasChanged = true;
                unitOfWork.ProductLineFileRepository.Insert(productLineFile);

                var file = await unitOfWork.FileRepository.GetByIdAsync(fileModel.FileId);
                if (file.Title != fileModel.Title)
                {
                    file.Title = fileModel.Title;
                    unitOfWork.FileRepository.Edit(file);
                }
            }

            //Add ForgeProductLinesIds
            if (model.ForgeProductLineIds.Any() && model.ForgeProductLineIds != null)
            {
                foreach (var forgeProductLine in model.ForgeProductLineIds)
                {
                    ForgeProductLineIds forgeProductLineId = new ForgeProductLineIds
                    {
                        ForgeProductLineId = forgeProductLine,
                        ProductLineId = productLine.Id
                    };
                    unitOfWork.ForgeProductLineIdsRepository.Insert(forgeProductLineId);
                    await unitOfWork.SaveAsync();
                }
            }

            //Add project files
            foreach (var projectFile in model.ProjectFiles)
            {
                ProductLineFile productLineFile = new ProductLineFile();
                productLineFile.ProductLineId = productLine.Id;
                productLineFile.CustomFileId = projectFile.CustomFileId;
                productLineFile.FileId = projectFile.FileId;
                productLineFile.SoftwareRelease = projectFile.SoftwareRelease;
                productLineFile.ContentCreatedby = projectFile.ContentCreatedby;
                productLineFile.ContentCheckedBy = projectFile.ContentCheckedBy;
                productLineFile.FileVersion = projectFile.FileVersion;
                productLineFile.ProjectDataTypeId = projectFile.ProjectDataTypeTypeId;
                productLineFile.IsAttachment = projectFile.ProjectDataTypeTypeId == null;
                productLineFile.CreatedById = userId;
                productLineFile.CreatedDate = DateTime.UtcNow;
                productLineFile.WasChanged = true;
                unitOfWork.ProductLineFileRepository.Insert(productLineFile);

                var file = await unitOfWork.FileRepository.GetByIdAsync(projectFile.FileId);
                if (file.Title != projectFile.Title)
                {
                    file.Title = projectFile.Title;
                    unitOfWork.FileRepository.Edit(file);
                }
            }

            //Add external product line certifications
            if (model.ExternalCertificateIds != null)
            {
                foreach (var certificateId in model.ExternalCertificateIds)
                {
                    ProductLineCertificate productLineCertificate = new ProductLineCertificate();
                    productLineCertificate.ProductLineId = productLine.Id;
                    productLineCertificate.ExternalCertificateId = certificateId;
                    productLineCertificate.CreatedById = userId;
                    productLineCertificate.CreatedDate = DateTime.UtcNow;
                    unitOfWork.ProductLineCertificateRepository.Insert(productLineCertificate);
                }
            }

            //Add product Quality Items
            if (model.QualityItemIds != null)
            {
                foreach (var qualityItemId in model.QualityItemIds)
                {
                    ProductLineQualityItem productLineQualityItem = new ProductLineQualityItem();
                    productLineQualityItem.ProductLineId = productLine.Id;
                    productLineQualityItem.QualityItemId = qualityItemId;
                    productLineQualityItem.CreatedById = userId;
                    productLineQualityItem.CreatedDate = DateTime.UtcNow;
                    unitOfWork.ProductLineQualityItemRepository.Insert(productLineQualityItem);
                }
            }

            await unitOfWork.SaveAsync();
            unitOfWork.CommitTransaction();

            await _changeLogService.LogEntityActionAsync(EntityType.ProductLine, productLine.Id, EntityAction.Add, userId, unitOfWork);

            return new ProductLineAddEditResultDto
            {
                Id = productLine.Id,
                Name = productLine.Name,
                RegionIds = productLine.RegionIds,
                StateIds = productLine.StateIds,
                Description = productLine.Description
            };
        }

        public async Task<ProductLineAddEditResultDto> EditAsync(EditProductLineModel model, string userId, IUnitOfWork unitOfWork)
        {
            unitOfWork.BeginTransaction();

            ProductLine productLine = await unitOfWork.ProductLineRepository.GetByIdAsync(model.Id);

            ProductLineChangeLogDto currentModel = await GetModelForChangeLogAsync(productLine.Id, unitOfWork);

            productLine.Name = model.Name.Trim();
            productLine.ManufacturerId = model.ManufacturerId;
            productLine.Description = model.Description;
            productLine.RegionIds = model.RegionIds;
            productLine.StateIds = model.StateIds;
            productLine.Note = model.Note;
            productLine.CreatedById = userId;
            productLine.CreatedDate = DateTime.UtcNow;

            unitOfWork.ProductLineRepository.Edit(productLine);

            #region Delete Old Data

            var productFiles = productLine.ProductLineFiles.ToList();
            unitOfWork.ProductLineFileRepository.Delete(productFiles);

            var forgeProductLines = productLine.ForgeProductLineIds.ToList();
            unitOfWork.ForgeProductLineIdsRepository.Delete(forgeProductLines);

            var productCertificates = productLine.ProductLineCertificates.ToList();
            unitOfWork.ProductLineCertificateRepository.Delete(productCertificates);

            var productQualityItems = productLine.ProductLineQualityItems.ToList();
            unitOfWork.ProductLineQualityItemRepository.Delete(productQualityItems);

            #endregion

            await unitOfWork.SaveAsync();

            //Add forge product lines
            if (model.ForgeProductLineIds.Any() && model.ForgeProductLineIds != null)
            {
                foreach (var forgeProductLine in model.ForgeProductLineIds)
                {
                    ForgeProductLineIds forgeProductLineId = new ForgeProductLineIds
                    {
                        ForgeProductLineId = forgeProductLine,
                        ProductLineId = productLine.Id
                    };
                    unitOfWork.ForgeProductLineIdsRepository.Insert(forgeProductLineId);
                    await unitOfWork.SaveAsync();
                }
            }

            //Add files
            foreach (var fileModel in model.Files)
            {
                ProductLineFile productLineFile = new ProductLineFile();
                productLineFile.ProductLineId = productLine.Id;
                productLineFile.CustomFileId = fileModel.CustomFileId;
                productLineFile.FileId = fileModel.FileId;
                productLineFile.IsAttachment = true;
                productLineFile.CreatedById = userId;
                productLineFile.CreatedDate = DateTime.UtcNow;
                productLineFile.WasChanged = true;
                unitOfWork.ProductLineFileRepository.Insert(productLineFile);

                var file = await unitOfWork.FileRepository.GetByIdAsync(fileModel.FileId);
                if (file.Title != fileModel.Title)
                {
                    file.Title = fileModel.Title;
                    unitOfWork.FileRepository.Edit(file);
                }
            }

            //Add project files
            foreach (var projectFile in model.ProjectFiles)
            {
                ProductLineFile productLineFile = new ProductLineFile();
                productLineFile.ProductLineId = productLine.Id;
                productLineFile.CustomFileId = projectFile.CustomFileId;
                productLineFile.FileId = projectFile.FileId;
                productLineFile.SoftwareRelease = projectFile.SoftwareRelease;
                productLineFile.ContentCreatedby = projectFile.ContentCreatedby;
                productLineFile.ContentCheckedBy = projectFile.ContentCheckedBy;
                productLineFile.FileVersion = projectFile.FileVersion;
                productLineFile.ProjectDataTypeId = projectFile.ProjectDataTypeTypeId;
                productLineFile.IsAttachment = projectFile.ProjectDataTypeTypeId == null;
                productLineFile.CreatedById = userId;
                productLineFile.CreatedDate = DateTime.UtcNow;
                productLineFile.WasChanged = true;
                unitOfWork.ProductLineFileRepository.Insert(productLineFile);

                var file = await unitOfWork.FileRepository.GetByIdAsync(projectFile.FileId);
                if (file.Title != projectFile.Title)
                {
                    file.Title = projectFile.Title;
                    unitOfWork.FileRepository.Edit(file);
                }
            }

            //Add product line certificates
            if (model.ExternalCertificateIds != null)
            {
                foreach (var certificateId in model.ExternalCertificateIds)
                {
                    ProductLineCertificate productLineCertificate = new ProductLineCertificate();
                    productLineCertificate.ProductLineId = productLine.Id;
                    productLineCertificate.ExternalCertificateId = certificateId;
                    productLineCertificate.CreatedById = userId;
                    productLineCertificate.CreatedDate = DateTime.UtcNow;
                    unitOfWork.ProductLineCertificateRepository.Insert(productLineCertificate);
                }
            }

            //Add product Quality Items
            if (model.QualityItemIds != null)
            {
                foreach (var qualityItemId in model.QualityItemIds)
                {
                    ProductLineQualityItem productLineQualityItem = new ProductLineQualityItem();
                    productLineQualityItem.ProductLineId = productLine.Id;
                    productLineQualityItem.QualityItemId = qualityItemId;
                    productLineQualityItem.CreatedById = userId;
                    productLineQualityItem.CreatedDate = DateTime.UtcNow;
                    unitOfWork.ProductLineQualityItemRepository.Insert(productLineQualityItem);
                }
            }

            await unitOfWork.SaveAsync();

            unitOfWork.CommitTransaction();

            ProductLineChangeLogDto newModel = await GetModelForChangeLogAsync(productLine.Id, unitOfWork);

            await _changeLogService.TrackAndLogChangesAsync(currentModel, newModel, EntityType.ProductLine, productLine.Id, userId, unitOfWork);

            await _publishEndpoint.Publish(new ProductLineChangedEvent
            {
                ProductLineId = productLine.Id
            });

            return new ProductLineAddEditResultDto
            {
                Id = productLine.Id,
                Description = productLine.Description,
                Name = productLine.Name,
                RegionIds = productLine.RegionIds,
                StateIds = productLine.StateIds
            };
        }

        public async Task<OperationResultDto> DeleteAsync(int id, string userId, IUnitOfWork unitOfWork)
        {
            var productLine = await unitOfWork.ProductLineRepository.GetByIdAsync(id);

            unitOfWork.BeginTransaction();

            await DeleteProductLineAsync(unitOfWork, productLine);

            unitOfWork.CommitTransaction();

            await _changeLogService.LogEntityActionAsync(EntityType.ProductLine, id, EntityAction.Delete, userId, unitOfWork);

            return new OperationResultDto
            {
                Status = OperationResultStatus.Succeed
            };
        }

        public async Task<ProductLineListDto[]> ListAsync(
            IUnitOfWork unitOfWork,
            string q = null,
            int manufacturerId = -1,
            string manufacturerName = null,
            int categoryId = -1,
            bool onlyWithPublishedProducts = false,
            bool isStaging = false,
            string regionId = null
            )
        {
            var query = unitOfWork.ProductLineRepository.GetAll();

            if (manufacturerName != null)
            {
                manufacturerId = unitOfWork.ManufacturerRepository.GetAll().First(a => a.Name.ToLower() == manufacturerName.ToLower()).Id;
            }

            if (manufacturerId != -1)
            {
                query = query.Where(a => a.ManufacturerId == manufacturerId);
            }

            if (categoryId != -1)
            {
                var queryForPids = unitOfWork.ProductRepository.GetAll();

                if (categoryId != -1)
                {
                    queryForPids = queryForPids.Where(a => a.CategoryId == categoryId || a.Category.ParentCategoryId == categoryId || (a.Category.ParentCategoryId != null && a.Category.ParentCategory.ParentCategoryId == categoryId));
                }

                var pIds = await queryForPids
                    .AsNoTracking()
                    .Where(x => x.ProductLineId != null)
                    .Select(a => a.ProductLineId.Value)
                    .Distinct()
                    .ToListAsync();

                query = query.Where(a => pIds.Contains(a.Id));
            }

            if (!string.IsNullOrEmpty(q))
            {
                query = query.Where(e =>
                           e.Name.Contains(q));
            }

            if (onlyWithPublishedProducts && !isStaging)
            {
                query = query.Where(a => a.Products.Any(p => p.Published));
            }
            else if (isStaging)
            {
                query = query.Where(a => a.Products.Any(p => p.Published || p.Staging));
            }

            if (!string.IsNullOrWhiteSpace(regionId))
            {
                query = query.Where(a => a.RegionIds == null
                    || a.RegionIds == string.Empty
                    || a.RegionIds == regionId
                    || a.RegionIds.StartsWith(regionId + "_")
                    || a.RegionIds.EndsWith("_" + regionId)
                    || a.RegionIds.Contains("_" + regionId + "_"));
            }

            var list = await query.OrderBy(x => x.Name).AsNoTracking().ToArrayAsync();
            var mappedList = list.BuildAdapter()
                                 .AddParameters("onlyWithPublishedProducts", onlyWithPublishedProducts)
                                 .AddParameters("manufacturerId", manufacturerId)
                                 .AdaptToType<ProductLineListDto[]>();

            return mappedList;
        }

        public async Task<ProductLineGetDto> GetAsync(int id, IUnitOfWork unitOfWork)
        {
            ProductLineGetDto item = await unitOfWork.ProductLineRepository.GetAll()
                .AsSingleQuery()
                .Where(a => a.Id == id)
                .Select(a => new ProductLineGetDto
                {
                    Id = a.Id,
                    Name = a.Name,
                    RegionIds = a.RegionIds,
                    StateIds = a.StateIds,
                    ManifacturerId = a.ManufacturerId,
                    ManufacturerName = a.Manufacturer.Name,
                    Note = a.Note,
                    Description = a.Description,
                    ExternalCertificates = a.ProductLineCertificates.Select(b => b.ExternalCertificateId),
                    ForgeProductLineIds = a.ForgeProductLineIds.Select(b => b.ForgeProductLineId),
                    QualityItems = a.ProductLineQualityItems.Select(r => new ProductLineGetQualityItemDto
                    {
                        Id = r.QualityItemId,
                        Name = r.QualityItem.Name,
                        IconUrl = r.QualityItem.IconUrl
                    }),
                    ProductFiles = a.ProductLineFiles.Where(f => f.IsAttachment).Select(r => new ProductLineGetProductFileDto
                    {
                        Id = r.FileId,
                        CustomFileId = r.CustomFileId,
                        Title = r.File.Title,
                        FileName = r.File.FileName,
                        FileSize = r.File.FileSize,
                        MimeType = r.File.MediaType,
                        UpdatesCount = r.File.UpdatesCount,
                        FileSyncStatusCode = r.File.SyncStatusCode,
                        FileSyncUrl = r.File.SyncUrl,
                        Url = r.File.Url,
                        Preview = r.File.PreviewUrl,
                        Weight = r.Weight
                    })
                        .OrderByDescending(x => x.Weight)
                        .AsEnumerable(),
                    ProjectFiles = a.ProductLineFiles.Where(f => !f.IsAttachment).Select(r => new ProductLineGetProductLineFileDto
                    {
                        Id = r.FileId,
                        CustomFileId = r.CustomFileId,
                        SoftwareRelease = r.SoftwareRelease,
                        ContentCreatedby = r.ContentCreatedby,
                        ContentCheckedBy = r.ContentCheckedBy,
                        FileVersion = r.FileVersion,
                        ProjectType = new ProductLineGetProjectTypeDto
                        {
                            Id = r.ProjectDataTypeId,
                            Title = r.ProjectDataType.Title,
                            Header = r.ProjectDataType.Header
                        },
                        Title = r.File.Title,
                        FileName = r.File.FileName,
                        FileSize = r.File.FileSize,
                        MimeType = r.File.MediaType,
                        UpdatesCount = r.File.UpdatesCount,
                        FileSyncStatusCode = r.File.SyncStatusCode,
                        FileSyncUrl = r.File.SyncUrl,
                        Url = r.File.Url,
                        Preview = r.File.PreviewUrl,
                    }),
                    UpdateDate = a.ModifiedDate ?? a.CreatedDate
                })
                .FirstOrDefaultAsync();

            if (item == null)
                throw new DbItemNotFoundException("Not found the Product Line");

            return item;
        }

        public async Task<ProductLineGetProductFileDto[]> GetProductFilesAsync(int productLineId, IUnitOfWork unitOfWork)
        {
            return await unitOfWork.ProductLineFileRepository.GetAll()
                .Where(x => x.ProductLineId == productLineId && x.IsAttachment)
                .Select(x => new ProductLineGetProductFileDto
                {
                    Id = x.FileId,
                    CustomFileId = x.CustomFileId,
                    Title = x.File.Title,
                    FileName = x.File.FileName,
                    FileSize = x.File.FileSize,
                    UpdatesCount = x.File.UpdatesCount,
                    FileSyncStatusCode = x.File.SyncStatusCode,
                    FileSyncUrl = x.File.SyncUrl,
                    MimeType = x.File.MediaType,
                    Url = x.File.Url,
                    Preview = x.File.PreviewUrl,
                    Weight = x.Weight
                })
                .OrderByDescending(x => x.Weight)
                .AsNoTracking()
                .ToArrayAsync();
        }

        public async Task<ProductLineChangeLogDto> GetModelForChangeLogAsync(int id, IUnitOfWork unitOfWork)
        {
            return await unitOfWork.ProductLineRepository.GetAll()
                .Where(x => x.Id == id)
                .ProjectToType<ProductLineChangeLogDto>()
                .FirstOrDefaultAsync();
        }

        #region private methods
        private async Task AddOrUpdateProductLineCertificatesAsync(ProductLine productLine, IUnitOfWork unitOfWork, List<int> addedItemIds, string userId)
        {
            if (addedItemIds == null) addedItemIds = new List<int>();
            if (productLine.ProductLineCertificates == null) productLine.ProductLineCertificates = new List<ProductLineCertificate>();
            var existingItemIds = productLine.ProductLineCertificates.Where(x => x.ExternalCertificateId != null).Select(x => x.ExternalCertificateId.Value).ToList();
            var itemToAddIds = addedItemIds.Except(existingItemIds).ToList();
            var itemToDeleteIds = existingItemIds.Except(addedItemIds).ToList();
            foreach (var certificateId in itemToAddIds)
            {
                ProductLineCertificate productLineCertificate = new ProductLineCertificate();
                productLineCertificate.ProductLineId = productLine.Id;
                productLineCertificate.ExternalCertificateId = certificateId;
                productLineCertificate.CreatedById = userId;
                productLineCertificate.CreatedDate = DateTime.UtcNow;
                unitOfWork.ProductLineCertificateRepository.Insert(productLineCertificate);
            }
            var itemsToDelete = await unitOfWork.ProductLineCertificateRepository.GetAll()
            .Where(x => x.ProductLineId == productLine.Id && x.ExternalCertificateId != null && itemToDeleteIds.Contains(x.ExternalCertificateId.Value))
            .ToListAsync();
            itemsToDelete.ForEach(x => unitOfWork.ProductLineCertificateRepository.Delete(x));
            await unitOfWork.SaveAsync();
        }

        private async Task AddOrUpdateProductLineAttachmentsAsync(ProductLine productLine, IUnitOfWork unitOfWork, List<FileDto> addedProductFiles, string userId)
        {
            if (addedProductFiles == null) addedProductFiles = new List<FileDto>();
            if (productLine.ProductLineFiles == null) productLine.ProductLineFiles = new List<ProductLineFile>();
            var addedItemIds = addedProductFiles.Select(x => x.FileId).ToList();
            var existingItemIds = productLine.ProductLineFiles.Where(x => x.IsAttachment).Select(x => x.FileId).ToList();
            var existingItems = productLine.ProductLineFiles.Where(x => x.IsAttachment).ToList();
            var itemIdsToAdd = addedItemIds.Except(existingItemIds).ToList();
            var itemsToAdd = addedProductFiles.Where(x => itemIdsToAdd.Contains(x.FileId)).ToList();
            var itemIdsToUpdate = addedItemIds.Intersect(existingItemIds).ToList();
            var itemsToUpdate = existingItems.Where(x => itemIdsToUpdate.Contains(x.FileId)).ToList();
            var itemIdsToDelete = existingItemIds.Except(addedItemIds).ToList();
            foreach (var fileModel in itemsToAdd)
            {
                ProductLineFile productLineFile = new ProductLineFile();
                productLineFile.ProductLineId = productLine.Id;
                productLineFile.CustomFileId = fileModel.CustomFileId;
                productLineFile.FileId = fileModel.FileId;
                productLineFile.IsAttachment = true;
                productLineFile.CreatedById = userId;
                productLineFile.CreatedDate = DateTime.UtcNow;
                productLineFile.WasChanged = true;
                unitOfWork.ProductLineFileRepository.Insert(productLineFile);

                var file = await unitOfWork.FileRepository.GetByIdAsync(fileModel.FileId);
                if (file.Title != fileModel.Title)
                {
                    file.Title = fileModel.Title;
                    unitOfWork.FileRepository.Edit(file);
                }
            }
            foreach (var productLineFile in itemsToUpdate)
            {
                var modelProductFile = addedProductFiles.FirstOrDefault(x => x.FileId == productLineFile.FileId);
                if (modelProductFile != null)
                {
                    productLineFile.ProductLineId = productLine.Id;
                    productLineFile.CustomFileId = modelProductFile.CustomFileId;
                    productLineFile.FileId = modelProductFile.FileId;
                    productLineFile.IsAttachment = true;
                    unitOfWork.ProductLineFileRepository.Edit(productLineFile);

                    var file = await unitOfWork.FileRepository.GetByIdAsync(modelProductFile.FileId);
                    if (file.Title != modelProductFile.Title)
                    {
                        file.Title = modelProductFile.Title;
                        unitOfWork.FileRepository.Edit(file);
                    }
                }
            }
            var productFilesToDelete = existingItems.Where(x => itemIdsToDelete.Contains(x.FileId)).ToList();
            productFilesToDelete.ForEach(x => unitOfWork.ProductLineFileRepository.Delete(x));
            await unitOfWork.SaveAsync();
        }

        private async Task AddOrUpdateProductLineForgeProductLines(ProductLine productLine, IUnitOfWork unitOfWork, List<string> addedItemIds)
        {
            if (addedItemIds == null) addedItemIds = new List<string>();
            if (productLine.ForgeProductLineIds == null) productLine.ForgeProductLineIds = new List<ForgeProductLineIds>();
            var existingItemIds = productLine.ForgeProductLineIds.Select(x => x.ForgeProductLineId).ToList();
            var itemToAddIds = addedItemIds.Except(existingItemIds).ToList();
            var itemToDeleteIds = existingItemIds.Except(addedItemIds).ToList();
            foreach (var forgeProductLine in itemToAddIds)
            {
                ForgeProductLineIds forgeProductLineId = new ForgeProductLineIds
                {
                    ForgeProductLineId = forgeProductLine,
                    ProductLineId = productLine.Id
                };
                unitOfWork.ForgeProductLineIdsRepository.Insert(forgeProductLineId);
                await unitOfWork.SaveAsync();
            }
            var itemsToDelete = await unitOfWork.ForgeProductLineIdsRepository.GetAll()
            .Where(x => x.ProductLineId == productLine.Id && itemToDeleteIds.Contains(x.ForgeProductLineId))
            .ToListAsync();
            itemsToDelete.ForEach(x => unitOfWork.ForgeProductLineIdsRepository.Delete(x));
            await unitOfWork.SaveAsync();
        }

        private async Task AddOrUpdateProductProjectFilesAsync(ProductLine productLine, IUnitOfWork unitOfWork, List<ProjectFileModel> addedProjectFiles, string userId)
        {
            if (addedProjectFiles == null) addedProjectFiles = new List<ProjectFileModel>();
            if (productLine.ProductLineFiles == null) productLine.ProductLineFiles = new List<ProductLineFile>();
            var addedItemIds = addedProjectFiles.Select(x => x.FileId).ToList();
            var existingItems = productLine.ProductLineFiles.Where(x => !x.IsAttachment).ToList();
            var existingItemIds = existingItems.Select(x => x.FileId).ToList();

            var itemIdsToAdd = addedItemIds.Except(existingItemIds).ToList();
            var itemsToAdd = addedProjectFiles.Where(x => itemIdsToAdd.Contains(x.FileId)).ToList();
            var itemIdsToUpdate = addedItemIds.Intersect(existingItemIds).ToList();
            var itemsToUpdate = existingItems.Where(x => itemIdsToUpdate.Contains(x.FileId)).ToList();
            var itemIdsToDelete = existingItemIds.Except(addedItemIds).ToList();
            foreach (var projectFile in itemsToAdd)
            {
                ProductLineFile productLineFile = new ProductLineFile();
                productLineFile.ProductLineId = productLine.Id;
                productLineFile.CustomFileId = projectFile.CustomFileId;
                productLineFile.FileId = projectFile.FileId;
                productLineFile.SoftwareRelease = projectFile.SoftwareRelease;
                productLineFile.ContentCreatedby = projectFile.ContentCreatedby;
                productLineFile.ContentCheckedBy = projectFile.ContentCheckedBy;
                productLineFile.FileVersion = projectFile.FileVersion;
                productLineFile.ProjectDataTypeId = projectFile.ProjectDataTypeTypeId;
                productLineFile.IsAttachment = projectFile.ProjectDataTypeTypeId == null;
                productLineFile.CreatedById = userId;
                productLineFile.CreatedDate = DateTime.UtcNow;
                productLineFile.WasChanged = true;
                unitOfWork.ProductLineFileRepository.Insert(productLineFile);

                var file = await unitOfWork.FileRepository.GetByIdAsync(projectFile.FileId);
                if (file.Title != projectFile.Title)
                {
                    file.Title = projectFile.Title;
                    unitOfWork.FileRepository.Edit(file);
                }
            }
            foreach (var productLineProjectFile in itemsToUpdate)
            {
                var modelProjectFile = addedProjectFiles.FirstOrDefault(x => x.FileId == productLineProjectFile.FileId);
                if (modelProjectFile != null)
                {
                    productLineProjectFile.ProductLineId = productLine.Id;
                    productLineProjectFile.CustomFileId = modelProjectFile.CustomFileId;
                    productLineProjectFile.FileId = modelProjectFile.FileId;
                    productLineProjectFile.SoftwareRelease = modelProjectFile.SoftwareRelease;
                    productLineProjectFile.ContentCreatedby = modelProjectFile.ContentCreatedby;
                    productLineProjectFile.ContentCheckedBy = modelProjectFile.ContentCheckedBy;
                    productLineProjectFile.FileVersion = modelProjectFile.FileVersion;
                    productLineProjectFile.ProjectDataTypeId = modelProjectFile.ProjectDataTypeTypeId;
                    productLineProjectFile.IsAttachment = modelProjectFile.ProjectDataTypeTypeId == null;
                    productLineProjectFile.ModifiedById = userId;
                    productLineProjectFile.ModifiedDate = DateTime.UtcNow;
                    unitOfWork.ProductLineFileRepository.Edit(productLineProjectFile);

                    var file = await unitOfWork.FileRepository.GetByIdAsync(modelProjectFile.FileId);
                    if (file.Title != modelProjectFile.Title)
                    {
                        file.Title = modelProjectFile.Title;
                        unitOfWork.FileRepository.Edit(file);
                    }
                }
            }
            var productLineProjectFilesToDelete = existingItems.Where(x => itemIdsToDelete.Contains(x.FileId)).ToList();
            productLineProjectFilesToDelete.ForEach(x => unitOfWork.ProductLineFileRepository.Delete(x));
            await unitOfWork.SaveAsync();
        }

        private async Task AddOrUpdateProductLineQualityItemsAsync(ProductLine productLine, IUnitOfWork unitOfWork, List<int> addedItemIds, string userId)
        {
            if (addedItemIds == null) addedItemIds = new List<int>();
            if (productLine.ProductLineQualityItems == null) productLine.ProductLineQualityItems = new List<ProductLineQualityItem>();
            var existingItemIds = productLine.ProductLineQualityItems.Select(x => x.QualityItemId).ToList();
            var itemIdsToAdd = addedItemIds.Except(existingItemIds).ToList();
            var itemsToDeleteIds = existingItemIds.Except(addedItemIds).ToList();
            foreach (var qualityItemId in itemIdsToAdd)
            {
                ProductLineQualityItem productLineQualityItem = new ProductLineQualityItem();
                productLineQualityItem.ProductLineId = productLine.Id;
                productLineQualityItem.QualityItemId = qualityItemId;
                productLineQualityItem.CreatedById = userId;
                productLineQualityItem.CreatedDate = DateTime.UtcNow;
                unitOfWork.ProductLineQualityItemRepository.Insert(productLineQualityItem);
            }
            var itemsToDelete = await unitOfWork.ProductLineQualityItemRepository.GetAll()
                .Where(x => x.ProductLineId == productLine.Id && itemsToDeleteIds.Contains(x.QualityItemId))
                .ToListAsync();
            itemsToDelete.ForEach(x => unitOfWork.ProductLineQualityItemRepository.Delete(x));
            await unitOfWork.SaveAsync();
        }
        #endregion
    }
}