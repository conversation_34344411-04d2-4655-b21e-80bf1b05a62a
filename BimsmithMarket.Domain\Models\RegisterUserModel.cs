﻿using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Models
{
    public class RegisterUserModel
    {
        [Required]
        [EmailAddress]
        [Display(Name = "Email")]
        public string Email { get; set; }

        [Required]
        [StringLength(100, ErrorMessage = "The {0} must be at least {2} characters long.", MinimumLength = 6)]
        [DataType(DataType.Password)]
        [Display(Name = "Password")]
        public string Password { get; set; }

        [DataType(DataType.Password)]
        [Display(Name = "Confirm password")]
        [Compare("Password", ErrorMessage = "The password and confirmation password do not match.")]
        public string ConfirmPassword { get; set; }

        [Required]
        [Display(Name = "First Name")]
        public string FirstName { get; set; }

        [Required]
        [Display(Name = "Last Name")]
        public string LastName { get; set; }

        [Required]
        [Display(Name = "City")]
        public string City { get; set; }

        //[Required(AllowEmptyStrings = true)]
        [Display(Name = "State")]
        public string State { get; set; }

        [Required]
        [Display(Name = "Selected Country")]
        public string SelectedCountry { get; set; }

        [Required]
        [Display(Name = "Usertype")]
        public string Usertype { get; set; }

        [Required(AllowEmptyStrings = true)]
        [Display(Name = "Usertype Other")]
        public string UsertypeOther { get; set; }

        [Required]
        [Display(Name = "Accepts Terms")]
        public bool? AcceptsTerms { get; set; }
    }
}