﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using Mapster;
using System.Linq;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public static class NoteMap
    {
        public static void Register(TypeAdapterConfig config)
        {
            config.ForType<AddNoteDto, Note>()
                .Ignore(d => d.NotificationList);

            config.ForType<Note, AdminGetNoteDto>()
                .Map(d => d.NotificationList, s => s.NotificationList.Select(n => n.Email))
                .Map(d => d.UserEmail, s => s.CreatedBy.Email);

            config.ForType<Note, NoteNotificationDto>()
                .Map(d => d.NotificationList, s => s.NotificationList.Select(n => n.Email));

            config.ForType<ProductLine, NoteNotificationProductLineInfoDto>()
                .Map(d => d.ProductLineId, s => s.Id)
                .Map(d => d.ProductLineName, s => s.Name)
                .Map(d => d.ManufacturerName, s => s.Manufacturer.Name);

            config.ForType<AdminListNoteInternalDto, AdminListNoteDto>()
                .Map(d => d.DateIsEffectiveTill, s => s.DateIsEffectiveTill.HasValue ? s.DateIsEffectiveTill.Value.ToString("yyyy-MM-dd") : null)
                .Map(d => d.DateToNotifyUsers, s => s.DateToNotifyUsers.HasValue ? s.DateToNotifyUsers.Value.ToString("yyyy-MM-dd") : null);
        }
    }
}