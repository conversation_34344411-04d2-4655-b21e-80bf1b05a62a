﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Domain.DBModels.PaymentDbModels;
using BIMsmithMarket.Domain.Dto.PaymentsDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;


namespace BIMsmithMarket.Services.PaymentServices
{
    /// <summary>
    /// ProductPaymentPlan service (because of using prduct price as current)
    /// </summary>
    public class ProductPriceService : IProductPriceService
    {
        private readonly IPriceService _priceService;

        public ProductPriceService(IPriceService priceService)
        {
            _priceService = priceService;
        }

        public async Task<ProductPriceDto> AddProductAsync(ProductPriceCreateDto model, IUnitOfWork unitOfWork)
        {
            ProductPrice productPrice = await unitOfWork.ProductPriceRepository.GetAll()
                                                  .FirstOrDefaultAsync(x => x.ProductId == model.ProductId &&
                                                                     x.PaymentPlanId == model.PaymentPlanId);
            if (productPrice != null)
                return productPrice.Adapt<ProductPriceDto>();

            var paymentPlan = unitOfWork.PaymentPlanRepository.GetById(model.PaymentPlanId);
            if (paymentPlan == null)
                throw new InvalidInputException($"Invalid PaymentPlanId {model.PaymentPlanId}");

            var product = unitOfWork.ProductRepository.GetById(model.ProductId);
            if (product == null)
                throw new InvalidInputException($"Invalid productId {model.ProductId}");

            productPrice = model.Adapt<ProductPrice>();
            unitOfWork.ProductPriceRepository.Insert(productPrice);
            await unitOfWork.SaveAsync();

            return productPrice.Adapt<ProductPriceDto>();
        }

        /// <summary>
        /// Set default free paln
        /// </summary>
        /// <param name="productId"></param>
        /// <param name="unitOfWork"></param>
        /// <returns></returns>
        public async Task SetDefaultPaymentPlan(int productId, IUnitOfWork unitOfWork)
        {
            var defpaymentPlanId = unitOfWork.PaymentPlanRepository.GetAll().FirstOrDefault(x => x.Title == "DefaultFree").Id;
            var newProductPrice = new ProductPrice
            {
                PaymentPlanId = defpaymentPlanId,
                ProductId = productId
            };
            unitOfWork.ProductPriceRepository.Insert(newProductPrice);
            await unitOfWork.SaveAsync();
        }

        public async Task MoveProductAsync(int pymentPlanId, int productId, IUnitOfWork unitOfWork)
        {
            ProductPrice productPrice = unitOfWork.ProductPriceRepository.GetAll()
                                                  .FirstOrDefault(x => x.PaymentPlanId == pymentPlanId
                                                                   && x.ProductId == productId);
            if (productPrice == null)
                throw new InvalidInputException($"PaymentPlan not found");

            unitOfWork.ProductPriceRepository.Delete(productPrice);
            await unitOfWork.SaveAsync();
        }

        public async Task<List<ProductPriceDto>> GetProductPricesAsync(
            int id,
            IUnitOfWork unitOfWork,
            bool onlyAvaliable = false)
        {
            List<ProductPriceDto> dbItems = await unitOfWork.ProductPriceRepository.GetAll()
                .Where(x => x.ProductId == id)
                .ProjectToType<ProductPriceDto>()
                .ToListAsync();

            if (!dbItems.Any())
            {
                //set default payment plan
                await SetDefaultPaymentPlan(id, unitOfWork);
            }

            //Using mapping on adapt it will calculate productCurrent parice and set Active or not payment plan
            var result = dbItems;
            if (onlyAvaliable)
            {
                result = result.Where(x => x.PaymentPlan.Active).ToList();
                if (!result.Any())
                    return new List<ProductPriceDto>();
            }

            return result;
        }

        /// <summary>
        /// Method will return actual product price info
        /// </summary>
        /// <param name="id"></param>
        /// <param name="unitOfWork"></param>
        /// <returns></returns>
        public async Task<StripeProductInfoDto> GetCurrentProductPriceAsync(
            int id,
            IUnitOfWork unitOfWork)
        {
            var product = await unitOfWork.ProductRepository.GetAll()
                .Where(x => x.Id == id)
                .Select(x => new
                {
                    Name = x.Name,
                    Description = x.Description,
                    Image = x.Photo.OriginalImgUrl,
                    Price = x.Price
                })
                .FirstOrDefaultAsync();

            if (product == null)
                throw new DbItemNotFoundException($"Product id {id} not found");

            if (product.Price == null)
                await _priceService.SetDefaultPriceToProductAsync(id, unitOfWork);

            var productPrices = await GetProductPricesAsync(id, unitOfWork, true);
            decimal currentPrice = GetCurrentProductPrice(productPrices);

            return new StripeProductInfoDto
            {
                Name = product.Name,
                Image = product.Image,
                Description = product.Description,
                ProductCurrentPrice = currentPrice,
                UnitAmount = GetStripeAmount(currentPrice),
            };
        }

        /// <summary>
        /// Will return price of product
        /// </summary>
        /// <param name="amounts"></param>
        /// <returns></returns>
        private long GetStripeAmount(decimal currentPrice)
        {
            return (int)Math.Round(currentPrice * 100, MidpointRounding.AwayFromZero);
        }

        private decimal GetCurrentProductPrice(List<ProductPriceDto> productPrices)
        {
            if (productPrices.Any())
            {
                return productPrices.First(pp =>
                        pp.PaymentPlan.PaymentPriority == productPrices.Max(x => x.PaymentPlan.PaymentPriority))
                    .CurrentProductPrice;
            }

            return 0;
        }
    }
}
