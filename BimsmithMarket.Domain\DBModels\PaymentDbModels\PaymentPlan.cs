﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.DBModels.PaymentDbModels
{
    /// <summary>
    /// Rule that will set discount to product
    /// </summary>
    public class PaymentPlan
    {
        [Key]
        public int Id { get; set; }
        public int PaymentPriority { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public decimal PaymentDiscount { get; set; }
        public DateTime? StartDateUtc { get; set; }
        public DateTime? EndDateUtc { get; set; }
        public virtual ICollection<ProductPrice> ProductPrices { get; set; }
    }
}
