﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Services.Helpers;
using Microsoft.AspNetCore.Mvc;

namespace BIMsmithMarket.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    public class SitemapDataController : BaseApiController
    {
        /// <summary>
        /// Gets bimsmith website help section nodes
        /// </summary>
        /// <param name="accessToken"></param>
        /// <param name="baseUrl"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetBimsmithHelpNodes(string accessToken, string baseUrl)
        {
            if (accessToken != ConfigurationHelper.GetValue("BimsmithAccessToken"))
                return BadRequest("Invalid acccess token");
            var helpNodes = SitemapGenerator.GetBimsmithHelpNodes(baseUrl);
            return Ok(helpNodes);
        }
    }
}