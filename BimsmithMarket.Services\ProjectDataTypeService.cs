﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class ProjectDataTypeService : IProjectDataTypeService
    {
        public async Task<object> GetAsync(IUnitOfWork unitOfWork, int id)
        {
            var projectDataType = await unitOfWork.ProjectDataTypeRepository.GetAll()
                        .Include(a => a.Image)
                        .Include(a => a.DefaultImage)
                        .Include(a => a.ActiveImage)
                        .Include(a => a.Icon)
                        .Include(a => a.MicrositeImage)
                        .SingleOrDefaultAsync(a => a.Id == id);

            if (projectDataType != null)
            {
                var response = new
                {
                    id = projectDataType.Id,
                    title = projectDataType.Title,
                    vanityUrl = projectDataType.VanityUrl,
                    header = projectDataType.Header,
                    description = projectDataType.Description,
                    metaTitle = projectDataType.MetaTitle,
                    metaDescription = projectDataType.MetaDescription,
                    metaKeyWords = projectDataType.MetaKeyWords,
                    parentId = projectDataType.ParentId,
                    image = projectDataType.ImageId == null ? null : new
                    {
                        id = projectDataType.ImageId,
                        name = projectDataType.Image.Name,
                        smallImgUrl = projectDataType.Image.SmallImgUrl,
                        originalImgUrl = projectDataType.Image.OriginalImgUrl,
                    },
                    micrositeImage = projectDataType.MicrositeImageId == null ? null : new
                    {
                        id = projectDataType.MicrositeImageId,
                        name = projectDataType.MicrositeImage.Name,
                        smallImgUrl = projectDataType.MicrositeImage.SmallImgUrl,
                        originalImgUrl = projectDataType.MicrositeImage.OriginalImgUrl,
                    },
                    icon = projectDataType.IconId == null ? null : new
                    {
                        id = projectDataType.IconId,
                        name = projectDataType.Icon.Name,
                        smallImgUrl = projectDataType.Icon.SmallImgUrl,
                        originalImgUrl = projectDataType.Icon.OriginalImgUrl,
                    },
                    defaultImage = projectDataType.DefaultImageId == null ? null : new
                    {
                        id = projectDataType.DefaultImageId,
                        name = projectDataType.DefaultImage.Name,
                        smallImgUrl = projectDataType.DefaultImage.SmallImgUrl,
                        originalImgUrl = projectDataType.DefaultImage.OriginalImgUrl,
                    },
                    activeImage = projectDataType.ActiveImageId == null ? null : new
                    {
                        id = projectDataType.ActiveImageId,
                        name = projectDataType.ActiveImage.Name,
                        smallImgUrl = projectDataType.ActiveImage.SmallImgUrl,
                        originalImgUrl = projectDataType.ActiveImage.OriginalImgUrl,
                    }
                };
                return response;
            }
            else
            {
                throw new DbItemNotFoundException($"ProjectDataType with id: {id} not found");
            }
        }

        public async Task<object> ListAsync(IUnitOfWork unitOfWork, bool onlyParents)
        {
            var projectDataTypes = unitOfWork.ProjectDataTypeRepository.GetAll();
            if (!projectDataTypes.Any())
            {
                throw new DbItemNotFoundException($"Countries = null!");
            }

            if (onlyParents)
            {
                projectDataTypes = projectDataTypes.Where(x => x.ParentId == null);
            }

            var response = await projectDataTypes.Select(projectDataType => new
            {
                id = projectDataType.Id,
                title = projectDataType.Title,
                vanityUrl = projectDataType.VanityUrl,
                header = projectDataType.Header,
                description = projectDataType.Description,
                metaTitle = projectDataType.MetaTitle,
                metaDescription = projectDataType.MetaDescription,
                metaKeyWords = projectDataType.MetaKeyWords,
                parentId = projectDataType.ParentId,
                image = projectDataType.ImageId == null ? null : new
                {
                    id = projectDataType.ImageId,
                    name = projectDataType.Image.Name,
                    smallImgUrl = projectDataType.Image.SmallImgUrl,
                    originalImgUrl = projectDataType.Image.OriginalImgUrl,
                },
                micrositeImage = projectDataType.MicrositeImageId == null ? null : new
                {
                    id = projectDataType.MicrositeImageId,
                    name = projectDataType.MicrositeImage.Name,
                    smallImgUrl = projectDataType.MicrositeImage.SmallImgUrl,
                    originalImgUrl = projectDataType.MicrositeImage.OriginalImgUrl,
                },
                icon = projectDataType.IconId == null ? null : new
                {
                    id = projectDataType.IconId,
                    name = projectDataType.Icon.Name,
                    smallImgUrl = projectDataType.Icon.SmallImgUrl,
                    originalImgUrl = projectDataType.Icon.OriginalImgUrl,
                },
                defaultImage = projectDataType.DefaultImageId == null ? null : new
                {
                    id = projectDataType.DefaultImageId,
                    name = projectDataType.DefaultImage.Name,
                    smallImgUrl = projectDataType.DefaultImage.SmallImgUrl,
                    originalImgUrl = projectDataType.DefaultImage.OriginalImgUrl,
                },
                activeImage = projectDataType.ActiveImageId == null ? null : new
                {
                    id = projectDataType.ActiveImageId,
                    name = projectDataType.ActiveImage.Name,
                    smallImgUrl = projectDataType.ActiveImage.SmallImgUrl,
                    originalImgUrl = projectDataType.ActiveImage.OriginalImgUrl,
                }
            })
            .AsNoTracking()
            .ToListAsync();

            return response;
        }

        public async Task<object> AddAsync(IUnitOfWork unitOfWork, string userId, AddProjectDataTypeModel model)
        {
            var projectDataType = new ProjectDataType
            {
                Title = model.Title,
                ParentId = model.ParentId,
                VanityUrl = model.VanityUrl,
                Header = model.Header,
                Description = model.Description,
                ActiveImageId = model.ActiveImageId,
                DefaultImageId = model.DefaultImageId,
                ImageId = model.ImageId,
                MicrositeImageId = model.MicrositeImageId,
                IconId = model.IconId,
                MetaTitle = model.MetaTitle,
                MetaDescription = model.MetaDescription,
                MetaKeyWords = model.MetaKeyWords,
                CreatedById = userId,
                CreatedDate = DateTime.UtcNow
            };
            unitOfWork.ProjectDataTypeRepository.Insert(projectDataType);
            await unitOfWork.SaveAsync();

            return new
            {
                id = projectDataType.Id,
                vanityUrl = projectDataType.VanityUrl,
                title = projectDataType.Title,
                header = projectDataType.Header,
                description = projectDataType.Description,
                metaTitle = projectDataType.MetaTitle,
                metaDescription = projectDataType.MetaDescription,
                metaKeyWords = projectDataType.MetaKeyWords
            };
        }

        public async Task<object> EditAsync(IUnitOfWork unitOfWork, string userId, EditProjectDataTypeModel model)
        {
            var projectDataType = await unitOfWork.ProjectDataTypeRepository.GetByIdAsync(model.Id);
            projectDataType.Title = model.Title;
            projectDataType.ParentId = model.ParentId;
            projectDataType.VanityUrl = model.VanityUrl;
            projectDataType.Header = model.Header;
            projectDataType.Description = model.Description;
            projectDataType.ActiveImageId = model.ActiveImageId;
            projectDataType.DefaultImageId = model.DefaultImageId;
            projectDataType.ImageId = model.ImageId;
            projectDataType.MicrositeImageId = model.MicrositeImageId;
            projectDataType.IconId = model.IconId;
            projectDataType.MetaTitle = model.MetaTitle;
            projectDataType.MetaDescription = model.MetaDescription;
            projectDataType.MetaKeyWords = model.MetaKeyWords;
            projectDataType.ModifiedById = userId;
            projectDataType.ModifiedDate = DateTime.UtcNow;
            unitOfWork.ProjectDataTypeRepository.Edit(projectDataType);
            await unitOfWork.SaveAsync();

            return new
            {
                id = projectDataType.Id,
                title = projectDataType.Title,
                vanityUrl = projectDataType.VanityUrl,
                header = projectDataType.Header,
                description = projectDataType.Description,
                metaTitle = projectDataType.MetaTitle,
                metaDescription = projectDataType.MetaDescription,
                metaKeyWords = projectDataType.MetaKeyWords
            };
        }

        public async Task DeleteAsync(IUnitOfWork unitOfWork, int id)
        {
            var projectDataType = await unitOfWork.ProjectDataTypeRepository.GetByIdAsync(id);
            if (projectDataType != null)
            {
                if (projectDataType.Children.Any())
                    throw new InvalidInputException($"Delete children before delete parrent.");

                if (await HasRelationsAsync(unitOfWork, projectDataType))
                    throw new InvalidInputException($"ProjectDataType with id {id} cannot be deleted because it has file relations");

                unitOfWork.ProjectDataTypeRepository.Delete(projectDataType);
                await unitOfWork.SaveAsync();
            }
            else
            {
                throw new DbItemNotFoundException($"ProjectDataType with id: {id} not found");
            }
        }

        public async Task<object> HasRelationsAsync(IUnitOfWork unitOfWork, int id)
        {
            var projectDataType = await unitOfWork.ProjectDataTypeRepository.GetByIdAsync(id);
            if (projectDataType != null)
            {
                var hasRelations = await HasRelationsAsync(unitOfWork, projectDataType);
                return new { hasRelations = hasRelations };
            }
            else
            {
                throw new DbItemNotFoundException($"ProjectDataType with id: {id} not found");
            }
        }

        public async Task<ICollection<ProjectTypePublicListDto>> PublicListAsync(IUnitOfWork unitOfWork)
        {
            ProjectDataType[] dbProjectDataTypes = await unitOfWork.ProjectDataTypeRepository.GetAll()
                                                                   .Include(x => x.Children)
                                                                   .Include(x => x.Image)
                                                                   .Include(x => x.ActiveImage)
                                                                   .Include(x => x.DefaultImage)
                                                                   .AsNoTracking()
                                                                   .ToArrayAsync();

            ProjectTypePublicListDto[] projectDataTypes = dbProjectDataTypes.Adapt<ProjectTypePublicListDto[]>();
            return projectDataTypes;
        }

        public async Task<ProjectDataTypeDto[]> RevitProcessingListAsync(IUnitOfWork unitOfWork)
        {
            int revitProcessingStartVersion = 2021;
            ProjectDataTypeDto[] allRevitProjectDataTypes = await unitOfWork.ProjectDataTypeRepository.GetAll()
                .Where(x => x.ParentId == RevitProcessingConstants.RevitProjectDataTypeId)
                .ProjectToType<ProjectDataTypeDto>()
                .ToArrayAsync();

            return allRevitProjectDataTypes.Where(x => int.Parse(x.Title.Replace("Revit ", string.Empty, StringComparison.OrdinalIgnoreCase)) >= revitProcessingStartVersion).ToArray();
        }

        #region private methods
        private async Task<bool> HasRelationsAsync(IUnitOfWork unitOfWork, ProjectDataType projectDataType)
        {
            var hasManufacturerFiles = await unitOfWork.ManufacturerFileRepository.GetAll().AnyAsync(x => x.ProjectDataTypeId == projectDataType.Id);
            var hasProductFiles = await unitOfWork.ProductFileRepository.GetAll().AnyAsync(x => x.ProjectDataTypeId == projectDataType.Id);
            var hasProductLineFiles = await unitOfWork.ProductLineFileRepository.GetAll().AnyAsync(x => x.ProjectDataTypeId == projectDataType.Id);
            var hasRelations = hasManufacturerFiles || hasProductFiles || hasProductLineFiles;
            return hasRelations;
        }
        #endregion
    }
}
