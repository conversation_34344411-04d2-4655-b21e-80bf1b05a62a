﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto.ChangeLog;
using BIMsmithMarket.Domain.Dto.ProductLineExcelDto;
using BIMsmithMarket.Domain.Models;
using Mapster;
using System.Linq;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public static class ProductLineMap
    {
        public static void Register(TypeAdapterConfig config)
        {
            config.ForType<ProductLine, ProductLineListDto>()
                .Map(d => d.ManufacturerName, s => s.Manufacturer.Name)
                .Map(d => d.ProductsCount, s => (bool)MapContext.Current.Parameters["onlyWithPublishedProducts"] ? s.Products.Where(p => p.Published && p.ManufacturerId == (int)MapContext.Current.Parameters["manufacturerId"]).Count() : s.Products.Count())
                .Map(d => d.UpdateDate, s => s.ModifiedDate ?? s.CreatedDate);

            config.ForType<ProductLineExcelDto, ProductLine>()
                .Map(d => d.Note, s => s.Notes)
                .Ignore(d => d.ForgeProductLineIds);

            config.ForType<ProductLine, ProductLineChangeLogDto>()
                .Map(d => d.ProductLineName, s => s.Name)
                .Map(d => d.ProductLineDescription, s => s.Description)
                .Map(d => d.ProductLineNotes, s => s.Note)
                .Map(d => d.ProductLineForgeId, s => s.ForgeProductLineIds.Select(x => x.ForgeProductLineId))
                .Map(d => d.Attachments, s => s.ProductLineFiles.Where(x => x.IsAttachment))
                .Map(d => d.Certification, s => s.ProductLineCertificates.Select(x => x.ExternalCertificateId.Value))
                .Map(d => d.QualityIcons, s => s.ProductLineQualityItems.Select(x => x.QualityItem.Name));

            config.ForType<ProductLineFile, ProductLineChangeLogFileDto>()
                .Map(d => d.Type, s => s.File.Title)
                .Map(d => d.Name, s => s.File.FileName)
                .Map(d => d.CustomId, s => s.CustomFileId)
                .Map(d => d.SourceUrl, s => s.File.SyncUrl);
        }
    }
}