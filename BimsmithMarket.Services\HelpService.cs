﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Core.Extentions;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class HelpService : IHelpService
    {
        public async Task<object> ArticleFeedbackAsync(IUnitOfWork unitOfWork, int count, int offset)
        {
            var totalCount = await unitOfWork.HelpArticleRepository.GetAll().CountAsync();
            var articles = await unitOfWork.HelpArticleRepository.GetAll().Select(x => new
            {
                Id = x.Id,
                Title = x.Title,
                Category = x.HelpCategories.Select(s => s.Title),
                Url = x.VanityUrl,
                ThumbsUp = x.ThumbsUpCount,
                ThumbsDown = x.ThumbsDownCount,
            })
            .OrderBy(x => x.Id)
            .Skip(offset)
            .Take(count)
            .ToListAsync();

            return new
            {
                articles,
                totalCount
            };
        }

        public async Task<object> AddCategoryAsync(IUnitOfWork unitOfWork, AddEditHelpCategoryModel model, string userId)
        {
            unitOfWork.BeginTransaction();

            string metaKeywords = null;
            if (!string.IsNullOrWhiteSpace(model.MetaKeywords))
            {
                metaKeywords = " " + model.MetaKeywords.Trim(' ', ',').Replace(",", ", ").Replace("  ", " ") + ", ";
            }

            var helpCategory = new HelpCategory
            {
                Title = model.Title,
                Status = model.Status,
                Weight = model.Weight,
                VanityUrl = model.VanityUrl,
                MetaDescription = model.MetaDescription,
                MetaKeywords = metaKeywords,
                MetaTitle = model.MetaTitle,
                ParentCategoryId = model.ParentCategoryId,
                IconUrl = model.IconUrl,
                CreatedById = userId,
                CreatedDate = DateTime.UtcNow
            };

            unitOfWork.HelpCategoryRepository.Insert(helpCategory);
            await unitOfWork.SaveAsync();
            unitOfWork.CommitTransaction();

            return new
            {
                Id = helpCategory.Id,
                Title = helpCategory.Title,
                VanityUrl = helpCategory.VanityUrl,
                MetaDescription = helpCategory.MetaDescription,
                MetaKeywords = helpCategory.MetaKeywords,
                Status = helpCategory.Status,
                Weight = helpCategory.Weight,
                MetaTitle = helpCategory.MetaTitle,
                ParentCategoryId = helpCategory.ParentCategoryId,
                IconUrl = helpCategory.IconUrl,
                Subcategories = helpCategory.Subcategories?.Select(x => new
                {
                    Id = x.Id,
                    Name = x.Title,
                    Status = x.Status,
                    Weight = x.Weight,
                    CreatedDate = x.CreatedDate,
                    ModifiedDate = x.ModifiedDate
                }).ToList()
            };
        }

        public async Task<object> EditCategoryAsync(IUnitOfWork unitOfWork, AddEditHelpCategoryModel model, string userId)
        {
            unitOfWork.BeginTransaction();

            string metaKeywords = null;
            if (!string.IsNullOrWhiteSpace(model.MetaKeywords))
            {
                metaKeywords = " " + model.MetaKeywords.Trim(' ', ',').Replace(",", ", ").Replace("  ", " ") + ", ";
            }

            var helpCategory = await unitOfWork.HelpCategoryRepository.GetByIdAsync(model.Id);

            if (helpCategory == null)
                throw new InvalidInputException("Help category not found");

            helpCategory.Title = model.Title;
            helpCategory.Status = model.Status;
            helpCategory.Weight = model.Weight;
            helpCategory.VanityUrl = model.VanityUrl;
            helpCategory.MetaDescription = model.MetaDescription;
            helpCategory.MetaKeywords = metaKeywords;
            helpCategory.MetaTitle = model.MetaTitle;
            helpCategory.ParentCategoryId = model.ParentCategoryId;
            helpCategory.IconUrl = model.IconUrl;
            helpCategory.ModifiedById = userId;
            helpCategory.ModifiedDate = DateTime.UtcNow;
            if (helpCategory.VanityUrl != model.VanityUrl.AsVanityUrl())
            {
                var oldVanityHistory = await unitOfWork.VanityHistoryRepository.GetAll().Where(a => a.VanityUrl == helpCategory.VanityUrl).OrderByDescending(a => a.Id).FirstOrDefaultAsync();
                if (oldVanityHistory == null)
                {
                    oldVanityHistory = new VanityHistory
                    {
                        EntityId = helpCategory.Id,
                        EntityType = "helpcategory",
                        VanityUrl = helpCategory.VanityUrl,
                        CreatedById = userId,
                        CreatedDate = DateTime.UtcNow
                    };
                    unitOfWork.VanityHistoryRepository.Insert(oldVanityHistory);
                    await unitOfWork.SaveAsync();
                }

                var vanityHistory = new VanityHistory
                {
                    EntityId = helpCategory.Id,
                    ParentId = oldVanityHistory.Id,
                    EntityType = "helpcategory",
                    VanityUrl = model.VanityUrl.AsVanityUrl(),
                    CreatedById = userId,
                    CreatedDate = DateTime.UtcNow
                };
                unitOfWork.VanityHistoryRepository.Insert(vanityHistory);
                await unitOfWork.SaveAsync();
            }

            helpCategory.VanityUrl = model.VanityUrl.AsVanityUrl();
            unitOfWork.HelpCategoryRepository.Edit(helpCategory);
            await unitOfWork.SaveAsync();
            unitOfWork.CommitTransaction();

            return new
            {
                Id = helpCategory.Id,
                Title = helpCategory.Title,
                VanityUrl = helpCategory.VanityUrl,
                MetaDescription = helpCategory.MetaDescription,
                MetaKeywords = helpCategory.MetaKeywords,
                Status = helpCategory.Status,
                Weight = helpCategory.Weight,
                MetaTitle = helpCategory.MetaTitle,
                ParentCategoryId = helpCategory.ParentCategoryId,
                IconUrl = helpCategory.IconUrl,
                Subcategories = helpCategory.Subcategories?.Select(x => new
                {
                    Id = x.Id,
                    Name = x.Title,
                    Status = x.Status,
                    Weight = x.Weight,
                    CreatedDate = x.CreatedDate,
                    ModifiedDate = x.ModifiedDate
                }).ToList()
            };
        }

        public async Task DeleteCategoryAsync(IUnitOfWork unitOfWork, int id)
        {
            var helpCategory = await unitOfWork.HelpCategoryRepository.GetByIdAsync(id);
            if (helpCategory == null)
                throw new InvalidInputException("Help category not found");

            var childCategories = helpCategory.Subcategories.ToList();
            childCategories.ForEach(x => helpCategory.Subcategories.Remove(x));
            //to add - search all subcategory tree
            unitOfWork.HelpCategoryRepository.Delete(helpCategory);
            await unitOfWork.SaveAsync();
        }

        public async Task<object> CategoryListAsync(IUnitOfWork unitOfWork, int count, int offset)
        {
            var totalCount = await unitOfWork.HelpCategoryRepository.GetAll().CountAsync();
            var helpCategories = await unitOfWork.HelpCategoryRepository.GetAll().Select(x => new
            {
                Id = x.Id,
                Title = x.Title,
                VanityUrl = x.VanityUrl,
                MetaDescription = x.MetaDescription,
                MetaKeywords = x.MetaKeywords,
                Status = x.Status,
                Weight = x.Weight,
                MetaTitle = x.MetaTitle,
                CreatedDate = x.CreatedDate,
                ModifiedDate = x.ModifiedDate,
                ParentCategoryId = x.ParentCategoryId,
                ParentCategory = x.ParentCategoryId != null ?
                new
                {
                    Id = x.ParentCategory.Id,
                    Title = x.ParentCategory.Title,
                    VanityUrl = x.ParentCategory.VanityUrl,
                    MetaDescription = x.ParentCategory.MetaDescription,
                    MetaKeywords = x.ParentCategory.MetaKeywords,
                    Status = x.ParentCategory.Status,
                    Weight = x.ParentCategory.Weight,
                    MetaTitle = x.ParentCategory.MetaTitle,
                    CreatedDate = x.ParentCategory.CreatedDate,
                    ModifiedDate = x.ParentCategory.ModifiedDate,
                    ParentCategoryId = x.ParentCategory.ParentCategoryId,
                    IconUrl = x.ParentCategory.IconUrl,
                }
                : null,
                IconUrl = x.IconUrl,
                Subcategories = x.Subcategories.Select(s => new
                {
                    Id = s.Id,
                    Title = x.Title,
                    Status = s.Status,
                    Weight = s.Weight,
                    CreatedDate = s.CreatedDate,
                    ModifiedDate = s.ModifiedDate
                }).ToList()
            })
            .OrderBy(x => x.Id)
            .Skip(offset)
            .Take(count)
            .AsNoTracking()
            .ToListAsync();

            return new
            {
                helpCategories,
                totalCount
            };
        }

        public async Task<object> GetCategoryAsync(IUnitOfWork unitOfWork, int id)
        {
            var helpCategory = await unitOfWork.HelpCategoryRepository.GetByIdAsync(id);
            if (helpCategory == null)
                throw new InvalidInputException("Help category not found");

            return new
            {
                Id = helpCategory.Id,
                Title = helpCategory.Title,
                VanityUrl = helpCategory.VanityUrl,
                MetaDescription = helpCategory.MetaDescription,
                MetaKeywords = helpCategory.MetaKeywords,
                Status = helpCategory.Status,
                Weight = helpCategory.Weight,
                MetaTitle = helpCategory.MetaTitle,
                CreatedDate = helpCategory.CreatedDate,
                ModifiedDate = helpCategory.ModifiedDate,
                ParentCategoryId = helpCategory.ParentCategoryId,
                ParentCategory = helpCategory.ParentCategoryId != null ?
                new
                {
                    Id = helpCategory.ParentCategory.Id,
                    Title = helpCategory.ParentCategory.Title,
                    VanityUrl = helpCategory.ParentCategory.VanityUrl,
                    MetaDescription = helpCategory.ParentCategory.MetaDescription,
                    MetaKeywords = helpCategory.ParentCategory.MetaKeywords,
                    Status = helpCategory.ParentCategory.Status,
                    Weight = helpCategory.ParentCategory.Weight,
                    MetaTitle = helpCategory.ParentCategory.MetaTitle,
                    CreatedDate = helpCategory.ParentCategory.CreatedDate,
                    ModifiedDate = helpCategory.ParentCategory.ModifiedDate,
                    ParentCategoryId = helpCategory.ParentCategory.ParentCategoryId,
                    IconUrl = helpCategory.ParentCategory.IconUrl,
                    Subcategories = helpCategory.ParentCategory.Subcategories.Select(x => new
                    {
                        Id = x.Id,
                        Title = x.Title,
                        Status = x.Status,
                        Weight = x.Weight,
                        CreatedDate = x.CreatedDate,
                        ModifiedDate = x.ModifiedDate
                    }).ToList()
                }
                : null,
                IconUrl = helpCategory.IconUrl,
                Subcategories = helpCategory.Subcategories.Select(x => new
                {
                    Id = x.Id,
                    Title = x.Title,
                    Status = x.Status,
                    Weight = x.Weight,
                    CreatedDate = x.CreatedDate,
                    ModifiedDate = x.ModifiedDate
                }).ToList()
            };
        }

        public async Task<object> AddArticleAsync(IUnitOfWork unitOfWork, AddEditHelpArticleModel model, string userId)
        {
            if (await unitOfWork.HelpArticleRepository.GetAll().AnyAsync(a => a.VanityUrl.ToLower() == model.VanityUrl))
                throw new InvalidInputException("This Vanity Url is occupied");

            var helpArticle = new HelpArticle
            {
                Name = model.Name,
                VanityUrl = model.VanityUrl,
                MetaDescription = model.MetaDescription,
                MetaKeywords = model.MetaKeywords,
                Title = model.Title,
                HelpTags = model.HelpTags,
                FAQ = model.FAQ,
                HtmlBody = model.HtmlBody,
                Status = model.Status,
                PublishedDate = model.PublishedDate,
                CreatedDate = DateTime.UtcNow,
                CreatedById = userId
            };
            var helpCategories = await unitOfWork.HelpCategoryRepository.GetAll().Where(x => model.HelpCategoriesIds.Contains(x.Id)).ToListAsync();
            helpArticle.HelpCategories = new List<HelpCategory>();
            foreach (var helpCategory in helpCategories) helpArticle.HelpCategories.Add(helpCategory);

            unitOfWork.HelpArticleRepository.Insert(helpArticle);
            await unitOfWork.SaveAsync();

            return new
            {
                Id = helpArticle.Id,
                Name = helpArticle.Name,
                Title = helpArticle.Title,
                VanityUrl = helpArticle.VanityUrl,
                MetaDescription = helpArticle.MetaDescription,
                MetaKeywords = helpArticle.MetaKeywords,
                Status = helpArticle.Status,
                FAQ = helpArticle.FAQ,
                HelpCategoriesIds = helpArticle.HelpCategories.Select(x => new { x.Id, x.Title }).ToList(),
                PublishedDate = helpArticle.PublishedDate,
                HtmlBody = helpArticle.HtmlBody,
                HelpTags = helpArticle.HelpTags
            };
        }

        public async Task<object> EditArticleAsync(IUnitOfWork unitOfWork, AddEditHelpArticleModel model, string userId)
        {
            if (await unitOfWork.HelpArticleRepository.GetAll().AnyAsync(a => a.VanityUrl.ToLower() == model.VanityUrl && a.Id != model.Id))
                throw new InvalidInputException("This Vanity Url is occupied");

            var helpArticle = await unitOfWork.HelpArticleRepository.GetByIdAsync(model.Id);
            if (helpArticle == null)
                throw new InvalidInputException("Help Atricle not found");

            helpArticle.Name = model.Name;
            helpArticle.VanityUrl = model.VanityUrl;
            helpArticle.MetaDescription = model.MetaDescription;
            helpArticle.MetaKeywords = model.MetaKeywords;
            helpArticle.Title = model.Title;
            helpArticle.HelpTags = model.HelpTags;
            helpArticle.FAQ = model.FAQ;
            helpArticle.HtmlBody = model.HtmlBody;
            helpArticle.Status = model.Status;
            helpArticle.ModifiedDate = DateTime.UtcNow;
            helpArticle.ModifiedById = userId;
            helpArticle.PublishedDate = model.PublishedDate;

            if (helpArticle.HelpCategories == null) helpArticle.HelpCategories = new List<HelpCategory>();
            var helpCategoriesToDelete = helpArticle.HelpCategories.Where(x => !model.HelpCategoriesIds.Contains(x.Id)).ToList();
            var helpCategoriesToAdd = unitOfWork.HelpCategoryRepository.GetAll()
                .Where(x => model.HelpCategoriesIds.Contains(x.Id)).AsEnumerable()
                .Where(x => !helpArticle.HelpCategories.Any(m => m.Id == x.Id)).ToList();
            helpCategoriesToDelete.ForEach(x => helpArticle.HelpCategories.Remove(x));
            foreach (var category in helpCategoriesToAdd) helpArticle.HelpCategories.Add(category);

            unitOfWork.HelpArticleRepository.Edit(helpArticle);

            await unitOfWork.SaveAsync();

            return new
            {
                Id = helpArticle.Id,
                Name = helpArticle.Name,
                Title = helpArticle.Title,
                VanityUrl = helpArticle.VanityUrl,
                MetaDescription = helpArticle.MetaDescription,
                MetaKeywords = helpArticle.MetaKeywords,
                Status = helpArticle.Status,
                FAQ = helpArticle.FAQ,
                HelpCategoriesIds = helpArticle.HelpCategories.Select(x => new { x.Id, x.Title }).ToList(),
                PublishedDate = helpArticle.PublishedDate,
                HtmlBody = helpArticle.HtmlBody,
                HelpTags = helpArticle.HelpTags
            };
        }

        public async Task DeleteArticleAsync(IUnitOfWork unitOfWork, int id, string userId)
        {
            var helpArticle = await unitOfWork.HelpArticleRepository.GetByIdAsync(id);
            if (helpArticle == null)
                throw new InvalidInputException("Help Atricle not found");

            helpArticle.HelpCategories.ToList().ForEach(x => helpArticle.HelpCategories.Remove(x));

            unitOfWork.HelpArticleRepository.Delete(helpArticle);

            await unitOfWork.SaveAsync();
        }

        public async Task<object> ArticleListAsync(IUnitOfWork unitOfWork, int count, int offset)
        {
            var totalCount = await unitOfWork.HelpArticleRepository.GetAll().CountAsync();
            var helpArticles = await unitOfWork.HelpArticleRepository.GetAll().Select(x =>
            new
            {
                Id = x.Id,
                ArticleName = x.Title,
                FAQ = x.FAQ,
                PublishDate = x.PublishedDate,
                VanityUrl = x.VanityUrl,
                Status = x.Status,
                Categories = x.HelpCategories.Select(c => new
                {
                    Id = c.Id,
                    Title = c.Title,
                    VanityUrl = c.VanityUrl,
                    MetaDescription = c.MetaDescription,
                    MetaKeywords = c.MetaKeywords,
                    Status = c.Status,
                    Weight = c.Weight,
                    MetaTitle = c.MetaTitle,
                    CreatedDate = c.CreatedDate,
                    ModifiedDate = c.ModifiedDate,
                    ParentCategoryId = c.ParentCategoryId,
                    ParentCategory = c.ParentCategoryId != null ?
                new
                {
                    Id = c.ParentCategory.Id,
                    Title = c.ParentCategory.Title,
                    VanityUrl = c.ParentCategory.VanityUrl,
                    MetaDescription = c.ParentCategory.MetaDescription,
                    MetaKeywords = c.ParentCategory.MetaKeywords,
                    Status = c.ParentCategory.Status,
                    Weight = c.ParentCategory.Weight,
                    MetaTitle = c.ParentCategory.MetaTitle,
                    CreatedDate = c.ParentCategory.CreatedDate,
                    ModifiedDate = c.ParentCategory.ModifiedDate,
                    ParentCategoryId = c.ParentCategory.ParentCategoryId,
                    IconUrl = c.ParentCategory.IconUrl,
                }
                : null,
                    IconUrl = c.IconUrl,
                    Subcategories = c.Subcategories.Select(s => new
                    {
                        Id = s.Id,
                        Title = c.Title,
                        Status = s.Status,
                        Weight = s.Weight,
                        CreatedDate = s.CreatedDate,
                        ModifiedDate = s.ModifiedDate
                    }).ToList()
                }).ToList()
            })
            .OrderBy(x => x.Id)
            .Skip(offset)
            .Take(count)
            .AsNoTracking()
            .ToListAsync();

            return new
            {
                helpArticles,
                totalCount
            };
        }

        public async Task<object> GetArticleAsync(IUnitOfWork unitOfWork, int id)
        {
            var helpArticle = await unitOfWork.HelpArticleRepository.GetByIdAsync(id);
            if (helpArticle == null)
                throw new InvalidInputException("Help Atricle not found");

            return new
            {
                Id = helpArticle.Id,
                Name = helpArticle.Name,
                Title = helpArticle.Title,
                VanityUrl = helpArticle.VanityUrl,
                MetaDescription = helpArticle.MetaDescription,
                MetaKeywords = helpArticle.MetaKeywords,
                Status = helpArticle.Status,
                FAQ = helpArticle.FAQ,
                HelpCategoriesIds = helpArticle.HelpCategories.Select(x => new { x.Id, x.Title }).ToList(),
                PublishedDate = helpArticle.PublishedDate,
                HtmlBody = helpArticle.HtmlBody,
                HelpTags = helpArticle.HelpTags
            };
        }

        public async Task<int> WebsiteAddArticleReactionAsync(IUnitOfWork unitOfWork, HelpAtricleReactionModel model)
        {
            var helpArticle = await unitOfWork.HelpArticleRepository.GetByIdAsync(model.Id);
            if (helpArticle == null)
                throw new InvalidInputException("Help Atricle not found");

            if (model.Reaction == HelpArticleReaction.Like) helpArticle.ThumbsUpCount++;
            else if (model.Reaction == HelpArticleReaction.Dislike) helpArticle.ThumbsDownCount++;
            unitOfWork.HelpArticleRepository.Edit(helpArticle);
            await unitOfWork.SaveAsync();

            return helpArticle.Id;
        }

        public async Task<object> WebsiteCategoryListAsync(IUnitOfWork unitOfWork)
        {
            var helpCategories = await unitOfWork.HelpCategoryRepository.GetAll()
                    .Where(x => x.ParentCategoryId == null
                    && x.Status == HelpCategoryStatus.Published)
                    .Select(x => new
                    {
                        Id = x.Id,
                        IconUrl = x.IconUrl,
                        VanityUrl = x.VanityUrl,
                        Title = x.Title,
                        MetaDescription = x.MetaDescription,
                        Weight = x.Weight
                    })
                    .OrderByDescending(x => x.Weight)
                    .AsNoTracking()
                    .ToListAsync();

            var FAQArticles = await unitOfWork.HelpArticleRepository.GetAll()
                .Where(x => x.FAQ
                && x.Status == HelpCategoryStatus.Published)
                .Select(x => new
                {
                    Id = x.Id,
                    Title = x.Title,
                    Body = x.HtmlBody,
                    VanityUrl = x.VanityUrl,
                    Name = x.Name
                })
            .AsNoTracking()
            .ToListAsync();

            return new
            {
                HelpCategories = helpCategories,
                FAQArticles = FAQArticles
            };
        }

        public async Task<object> WebsiteCategoryAsync(IUnitOfWork unitOfWork, string categoryUrl)
        {
            var helpCategory = await unitOfWork.HelpCategoryRepository.GetAll().FirstOrDefaultAsync(x => x.VanityUrl.ToLower() == categoryUrl.ToLower());
            if (helpCategory == null)
                throw new InvalidInputException("Help category with specified url not found");

            var helpCategoryArticles = await unitOfWork.HelpArticleRepository.GetAll()
                .Where(x => x.HelpCategories.Select(c => c.Id).Contains(helpCategory.Id)
                && x.Status == HelpCategoryStatus.Published).Select(x =>
                new
                {
                    Id = x.Id,
                    VanityUrl = x.VanityUrl,
                    Title = x.Title,
                    Name = x.Name,
                    Categories = x.HelpCategories.Where(c => c.Status == HelpCategoryStatus.Published).Select(c => c.Id)
                })
                .ToListAsync();
            var subCategories = await unitOfWork.HelpCategoryRepository.GetAll()
                .Where(x => x.ParentCategoryId == helpCategory.Id
                && x.Status == HelpCategoryStatus.Published)
                .ToListAsync();
            var subCategoriesIds = subCategories.Select(x => x.Id).ToList();
            var subCategoriesArticles = await unitOfWork.HelpArticleRepository.GetAll()
                .Where(x => x.Status == HelpCategoryStatus.Published
                && x.HelpCategories.Where(c => c.Status == HelpCategoryStatus.Published).Any(x=> subCategoriesIds.Contains(x.Id)))
                .Select(x => new
                {
                    Id = x.Id,
                    VanityUrl = x.VanityUrl,
                    Title = x.Title,
                    Name = x.Name,
                    Categories = x.HelpCategories.Where(c => c.Status == HelpCategoryStatus.Published).Select(c => c.Id)
                })
                .ToListAsync();
            var subCategoriesInfo = new List<dynamic>();
            foreach (var subCategory in subCategories)
            {
                subCategoriesInfo.Add(new
                {
                    Id = subCategory.Id,
                    Title = subCategory.Title,
                    VanityUrl = subCategory.VanityUrl,
                    MetaDescription = subCategory.MetaDescription,
                    Articles = subCategoriesArticles.Where(x => x.Categories.Contains(subCategory.Id))
                });
            }

            return new
            {
                Id = helpCategory.Id,
                Title = helpCategory.Title,
                VanityUrl = helpCategory.VanityUrl,
                MetaDescription = helpCategory.MetaDescription,
                MetaKeywords = helpCategory.MetaKeywords,
                MetaTitle = helpCategory.MetaTitle,
                Articles = helpCategoryArticles,
                SubCategories = subCategoriesInfo
            };
        }

        public async Task<object> WebsiteArticleAsync(IUnitOfWork unitOfWork, string articleUrl)
        {
            var helpArticle = await unitOfWork.HelpArticleRepository.GetAll().FirstOrDefaultAsync(x => x.VanityUrl.ToLower() == articleUrl.ToLower());
            if (helpArticle == null)
                throw new InvalidInputException("Help category with specified url not found");

            helpArticle.ViewCount++;
            unitOfWork.HelpArticleRepository.Edit(helpArticle);
            await unitOfWork.SaveAsync();
            return new
            {
                Id = helpArticle.Id,
                HtmlBody = helpArticle.HtmlBody,
                MetaDescription = helpArticle.MetaDescription,
                MetaKeywords = helpArticle.MetaKeywords,
                Title = helpArticle.Title,
                Name = helpArticle.Name,
                Categories = helpArticle.HelpCategories.Where(x => x.Status == HelpCategoryStatus.Published).Select(x =>
                new
                {
                    Id = x.Id,
                    Title = x.Title,
                    VanityUrl = x.VanityUrl,
                    MetaDescription = x.MetaDescription,
                    MetaKeywords = x.MetaKeywords,
                    Status = x.Status,
                    Weight = x.Weight,
                    MetaTitle = x.MetaTitle,
                    CreatedDate = x.CreatedDate,
                    ModifiedDate = x.ModifiedDate,
                    ParentCategoryId = x.ParentCategoryId,
                    ParentCategory = x.ParentCategoryId != null && x.ParentCategory.Status == HelpCategoryStatus.Published ?
                    new
                    {
                        Id = x.ParentCategory.Id,
                        Title = x.ParentCategory.Title,
                        VanityUrl = x.ParentCategory.VanityUrl,
                        MetaDescription = x.ParentCategory.MetaDescription,
                        MetaKeywords = x.ParentCategory.MetaKeywords,
                        Status = x.ParentCategory.Status,
                        Weight = x.ParentCategory.Weight,
                        MetaTitle = x.ParentCategory.MetaTitle,
                        CreatedDate = x.ParentCategory.CreatedDate,
                        ModifiedDate = x.ParentCategory.ModifiedDate,
                        ParentCategoryId = x.ParentCategory.ParentCategoryId,
                        IconUrl = x.ParentCategory.IconUrl,
                    }
                    : null,
                    IconUrl = x.IconUrl,
                    Subcategories = x.Subcategories.Where(s => s.Status == HelpCategoryStatus.Published).Select(s => new
                    {
                        Id = s.Id,
                        Title = x.Title,
                        Status = s.Status,
                        Weight = s.Weight,
                        CreatedDate = s.CreatedDate,
                        ModifiedDate = s.ModifiedDate
                    }).ToList()
                }).ToList()
            };
        }

        public async Task<object> WebsiteArticleListAsync(IUnitOfWork unitOfWork, int? categoryId, int count, int offset, string query)
        {
            var articlesQuery = unitOfWork.HelpArticleRepository.GetAll();
            if (categoryId != null) articlesQuery = unitOfWork.HelpArticleRepository.GetAll()
                    .Where(x => x.HelpCategories.Any(c => c.Id == categoryId));
            if (!string.IsNullOrWhiteSpace(query)) articlesQuery = articlesQuery.Where(x => x.Name.ToLower().Contains(query.ToLower())
                || x.Title.ToLower().Contains(query.ToLower())
                || x.HtmlBody.ToLower().Contains(query.ToLower()));
            var totalCount = await articlesQuery.CountAsync();
            var helpArticles = await articlesQuery
                .Where(x => x.Status == HelpCategoryStatus.Published)
                .OrderByDescending(x => x.PublishedDate)
                .Select(x =>
                new
                {
                    Id = x.Id,
                    Name = x.Name,
                    VanityUrl = x.VanityUrl,
                    Categories = x.HelpCategories.Where(c => c.Status == HelpCategoryStatus.Published).Select(c =>
                    new
                    {
                        Id = c.Id,
                        Title = c.Title,
                        VanityUrl = c.VanityUrl,
                        MetaDescription = c.MetaDescription,
                        MetaKeywords = c.MetaKeywords,
                        Status = c.Status,
                        Weight = c.Weight,
                        MetaTitle = c.MetaTitle,
                        CreatedDate = c.CreatedDate,
                        ModifiedDate = c.ModifiedDate,
                        ParentCategoryId = c.ParentCategoryId,
                        ParentCategory = c.ParentCategoryId != null && c.ParentCategory.Status == HelpCategoryStatus.Published ?
                        new
                        {
                            Id = c.ParentCategory.Id,
                            Title = c.ParentCategory.Title,
                            VanityUrl = c.ParentCategory.VanityUrl,
                            MetaDescription = c.ParentCategory.MetaDescription,
                            MetaKeywords = c.ParentCategory.MetaKeywords,
                            Status = c.ParentCategory.Status,
                            Weight = c.ParentCategory.Weight,
                            MetaTitle = c.ParentCategory.MetaTitle,
                            CreatedDate = c.ParentCategory.CreatedDate,
                            ModifiedDate = c.ParentCategory.ModifiedDate,
                            ParentCategoryId = c.ParentCategory.ParentCategoryId,
                            IconUrl = c.ParentCategory.IconUrl,
                        }
                        : null,
                        IconUrl = c.IconUrl,
                        Subcategories = c.Subcategories.Where(s => s.Status == HelpCategoryStatus.Published).Select(s => new
                        {
                            Id = s.Id,
                            Title = c.Title,
                            Status = s.Status,
                            Weight = s.Weight,
                            CreatedDate = s.CreatedDate,
                            ModifiedDate = s.ModifiedDate
                        }).ToList()
                    }).ToList()
                })
                .OrderBy(x => x.Id)
                .Skip(offset)
                .Take(count)
                .AsNoTracking()
                .ToListAsync();
            return new
            {
                helpArticles,
                totalCount
            };
        }
    }
}
