﻿using System.Collections.Generic;

namespace BIMsmithMarket.Domain.DBModels
{
    public class KeyStat : BaseEntity
    {
        public string Name { get; set; }

        public string Note { get; set; }
        /// ------------------------------------------

        public virtual ICollection<ProductStats> ProductStats { get; set; }

        public virtual ICollection<CategoryKeyStat> CategoryKeyStats { get; set; }

        public KeyStat()
        {
            ProductStats = new List<ProductStats>();
            CategoryKeyStats = new List<CategoryKeyStat>();
        }
    }
}