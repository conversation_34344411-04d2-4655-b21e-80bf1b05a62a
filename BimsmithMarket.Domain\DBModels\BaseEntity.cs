﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class BaseEntity : IBaseEntity
    {
        [Key]
        public int Id { get; set; }

        [StringLength(128)]
        public string CreatedById { get; set; }

        public DateTime CreatedDate { get; set; }

        [StringLength(128)]
        public string ModifiedById { get; set; }

        public DateTime? ModifiedDate { get; set; }

        [ForeignKey("CreatedById")]
        public virtual ApplicationUser CreatedBy { get; set; }

        [ForeignKey("ModifiedById")]
        public virtual ApplicationUser ModifiedBy { get; set; }



        public BaseEntity()
        {
            CreatedDate = DateTime.UtcNow;
        }
    }
}
