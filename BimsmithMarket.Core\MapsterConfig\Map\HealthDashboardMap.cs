﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto.HealthDashboardDto;
using Flurl;
using Mapster;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public static class HealthDashboardMap
    {
        public static void Register(TypeAdapterConfig config)
        {
            config.ForType<HealthDashboardAccess, HealthDashboardAssignedUserListDto>()
                .Map(d => d.UserEmail, s => s.User.Email);

            config.ForType<ProductFile, HealthDashboardExportProductBrokenLinksDto>()
                .Map(d => d.ProductName, s => s.Product.Name)
                .Map(d => d.AdminPanelProductPageUrl, s => new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment($"adminpanel/{s.ProductId}/edit/{s.Product.ManufacturerId}", false).ToString())
                .Map(d => d.MarketProductPageUrl, s => new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment($"product/{s.ProductId}", false).ToString())
                .Map(d => d.AttachmentType, s => s.File.Title)
                .Map(d => d.AttachmentSourceUrl, s => s.File.SyncUrl)
                .Map(d => d.AttachmentSourceStatus, s => s.File.SyncStatusCode);

            config.ForType<ProductLineFile, HealthDashboardExportProductBrokenLinksDto>()
               .Map(d => d.ProductId, s => s.ProductLineId)
               .Map(d => d.ProductName, s => $"Product Line {s.ProductLine.Name}")
               .Map(d => d.AdminPanelProductPageUrl, s => new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment($"adminpanel/product-line/{s.ProductLineId}/edit/{s.ProductLine.ManufacturerId}", false).ToString())
               .Map(d => d.AttachmentType, s => s.File.Title)
               .Map(d => d.AttachmentSourceUrl, s => s.File.SyncUrl)
               .Map(d => d.AttachmentSourceStatus, s => s.File.SyncStatusCode);

            config.ForType<ManufacturerFile, HealthDashboardExportProductBrokenLinksDto>()
               .Map(d => d.ProductId, s => s.Manufacturer.Id)
               .Map(d => d.ProductName, s => $"Manufacturer {s.Manufacturer.Name}")
               .Map(d => d.AdminPanelProductPageUrl, s => new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment($"adminpanel/manufacturers/{s.ManufacturerId}/edit", false).ToString())
               .Map(d => d.MarketProductPageUrl, s => new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment($"{s.Manufacturer.HubVanityURL}", false).ToString())
               .Map(d => d.AttachmentType, s => s.File.Title)
               .Map(d => d.AttachmentSourceUrl, s => s.File.SyncUrl)
               .Map(d => d.AttachmentSourceStatus, s => s.File.SyncStatusCode);

            //------------------------------------------------------------------------------
            config.ForType<Product, HealthDashboardExportProductBlankDescriptionsDto>()
                .Map(d => d.ProductId, s => s.Id)
                .Map(d => d.ProductName, s => s.Name)
                .Map(d => d.AdminPanelProductPageUrl, s => new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment($"adminpanel/{s.Id}/edit/{s.ManufacturerId}", false).ToString())
                .Map(d => d.MarketProductPageUrl, s => new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment($"product/{s.Id}", false).ToString())
                .Map(d => d.Description, s => s.Description);

            //---------------------------------------------------------------------------
            config.ForType<Product, HealthDashboardExportNoRevitFileDto>()
                .Map(d => d.ProductId, s => s.Id)
                .Map(d => d.ProductName, s => s.Name)
                .Map(d => d.AdminPanelProductPageUrl, s => new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment($"adminpanel/{s.Id}/edit/{s.ManufacturerId}", false).ToString())
                .Map(d => d.MarketProductPageUrl, s => new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment($"product/{s.Id}", false).ToString());


            //------------------------------------------------------------------------------

            config.ForType<ProductFile, HealthDashboardExportRevitWithoutDescriptionDto>()
                .Map(d => d.ProductName, s => s.Product.Name)
                .Map(d => d.AdminPanelProductPageUrl, s => new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment($"adminpanel/{s.ProductId}/edit/{s.Product.ManufacturerId}", false).ToString())
                .Map(d => d.MarketProductPageUrl, s => new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment($"product/{s.ProductId}", false).ToString())
                .Map(d => d.FileName, s => s.File.FileName)
                .Map(d => d.FileId, s => s.File.Id);

            config.ForType<ProductLineFile, HealthDashboardExportRevitWithoutDescriptionDto>()
               .Map(d => d.ProductId, s => s.ProductLineId)
               .Map(d => d.ProductName, s => $"Product Line {s.ProductLine.Name}")
               .Map(d => d.AdminPanelProductPageUrl, s => new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment($"adminpanel/product-line/{s.ProductLineId}/edit/{s.ProductLine.ManufacturerId}", false).ToString())
               .Map(d => d.FileName, s => s.File.FileName)
               .Map(d => d.FileId, s => s.File.Id);

            config.ForType<ManufacturerFile, HealthDashboardExportRevitWithoutDescriptionDto>()
               .Map(d => d.ProductId, s => s.Manufacturer.Id)
               .Map(d => d.ProductName, s => $"Manufacturer {s.Manufacturer.Name}")
               .Map(d => d.AdminPanelProductPageUrl, s => new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment($"adminpanel/manufacturers/{s.ManufacturerId}/edit", false).ToString())
               .Map(d => d.MarketProductPageUrl, s => new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment($"{s.Manufacturer.HubVanityURL}", false).ToString())
               .Map(d => d.FileName, s => s.File.FileName)
               .Map(d => d.FileId, s => s.File.Id);

            config.ForType<HealthDashboardExportProductWithZipFilesDbDto, HealthDashboardExportProductWithZipFilesDto>()
                .Map(d => d.AdminPanelProductPageUrl, s => new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment($"adminpanel/{s.ProductId}/edit/{s.ManufacturerId}", false).ToString())
                .Map(d => d.MarketProductPageUrl, s => new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment($"product/{s.ProductId}", false).ToString())
                .Map(d => d.ZipFiles, s => string.Join(", ", s.ZipFileNames));
        }
    }
}