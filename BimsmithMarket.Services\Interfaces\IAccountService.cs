﻿using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IAccountService
    {
        Task<object> MyBIMsmithProductsList(IUnitOfWork unitOfWork, string userId, string q, int offset, int count);
        Task AddProductToMyBIMsmithAsync(IUnitOfWork unitOfWork, string userId, int productId, string streamId);
    }
}
