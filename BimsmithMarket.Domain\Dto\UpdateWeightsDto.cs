﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Dto
{
    public class UpdateWeightsDto
    {
        [Required]
        public List<UpdateWeightDto> Weights { get; set; }
    }

    public class UpdateWeightDto
    {
        [Required]
        public string Id { get; set; }

        [Required]
        public float Weight { get; set; }
    }

    public class ProductUpdateWeightsDto
    {
        [Required]
        public List<ProductUpdateWeightDto> Weights { get; set; }
    }

    public class ProductUpdateWeightDto
    {
        [Required]
        public int Id { get; set; }

        [Required]
        public float Weight { get; set; }
    }
}