﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.DetailDto;
using BIMsmithMarket.Domain.Dto.SalesRepresentative;
using System;
using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Models
{
    public class ManufacturerBackupModel : EditManufacturerDto
    {
        public float Weight { get; set; }

        public int Status { get; set; }

        public string CreatedById { get; set; }

        public DateTime CreatedDate { get; set; }

        public string ModifiedById { get; set; }

        public DateTime? ModifiedDate { get; set; }

        public int? PromotedId { get; set; }

        public List<ProductBackupModel> Products { get; set; }

        public List<ProductLineBackupModel> ProductLines { get; set; }

        public List<int> ManufacturerPhotoIds { get; set; }

        public List<FileDto> ManufacturerFiles { get; set; }

        public List<DetailBackupModel> Details { get; set; }

        public List<SubscriberBackupModel> SubscribedUsers { get; set; }

        public List<LetsTalkUserBackupModel> LetsTalkUsers { get; set; }

        public List<LunchAndLearnUserBackupModel> LunchAndLearnUsers { get; set; }

        public List<BimRequestBackupModel> BimRequests { get; set; }

        public List<ManufacturerAdminUserBackupModel> ManufacturerAdminUsers { get; set; }

        public List<AttachmentOrderBackupModel> AttachmentOrders { get; set; }

        public List<RequestPricingUserBackupModel> RequestPricingUsers { get; set; }

        public List<SalesRepresentativeBackupModel> SalesRepresentatives { get; set; }

        public ManufacturerBackupModel()
        {
            ProductLines = new List<ProductLineBackupModel>();
            Products = new List<ProductBackupModel>();
            ManufacturerPhotoIds = new List<int>();
            ManufacturerFiles = new List<FileDto>();
            Details = new List<DetailBackupModel>();
            SubscribedUsers = new List<SubscriberBackupModel>();
            LetsTalkUsers = new List<LetsTalkUserBackupModel>();
            LunchAndLearnUsers = new List<LunchAndLearnUserBackupModel>();
            ManufacturerAdminUsers = new List<ManufacturerAdminUserBackupModel>();
            BimRequests = new List<BimRequestBackupModel>();
            AttachmentOrders = new List<AttachmentOrderBackupModel>();
            RequestPricingUsers = new List<RequestPricingUserBackupModel>();
            SalesRepresentatives = new List<SalesRepresentativeBackupModel>();
        }
    }

    public class ProductBackupModel : EditProductModel
    {

    }

    public class ProductLineBackupModel : EditProductLineModel
    {
        public string CreatedById { get; set; }

        public string ForgeManufacturerId { get; set; }

        public float Weight { get; set; }

        public DateTime CreatedDate { get; set; }

        public string ModifiedById { get; set; }

        public DateTime? ModifiedDate { get; set; }

        public List<string> StarterIds { get; set; }

        public List<ProductStatsDto> ProductLineStats { get; set; }
    }

    public class DetailBackupModel : EditDetailDto
    {

    }

    public class SubscriberBackupModel
    {
        public DateTime AddedDate { get; set; }

        public string AddedById { get; set; }
    }

    public class LetsTalkUserBackupModel
    {
        public int Status { get; set; }

        public string AddedById { get; set; }

        public DateTime AddedDate { get; set; }

        public int Timezone { get; set; }
    }

    public class LunchAndLearnUserBackupModel
    {
        public int Status { get; set; }

        public string AddedById { get; set; }

        public DateTime AddedDate { get; set; }
    }

    public class BimRequestBackupModel
    {
        public string Message { get; set; }

        public string ProductName { get; set; }

        public string ProductLink { get; set; }

        public int TimeZone { get; set; }

        public int Status { get; set; }

        public string AddedById { get; set; }

        public DateTime AddedDate { get; set; }
    }

    public class ManufacturerAdminUserBackupModel
    {
        public int Status { get; set; }

        public string Email { get; set; }

        public string AdminUserId { get; set; }

        public string AddedById { get; set; }

        public DateTime AddedDate { get; set; }

        public ManufacturerAdminRole Roles { get; set; }
    }

    public class AttachmentOrderBackupModel
    {
        public int Order { get; set; }

        public string Type { get; set; }
    }

    public class RequestPricingUserBackupModel
    {
        public int Id { get; set; }

        public int ManufacturerId { get; set; }

        public int? ProductId { get; set; }

        public DateTime CreatedDate { get; set; }

        public string CreatedById { get; set; }
    }

    public class SalesRepresentativeBackupModel : EditSalesRepresentativeDto
    {
        public DateTime CreatedDate { get; set; }

        public string CreatedById { get; set; }
    }

    public class ManufacturerBackupsListModel
    {
        public string SystemName { get; set; }

        public string DateString { get; set; }
    }
}