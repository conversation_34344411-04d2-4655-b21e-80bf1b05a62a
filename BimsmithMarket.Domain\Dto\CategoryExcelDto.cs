﻿using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Dto
{
    public class CategoryExcelDto : BaseExcelDto
    {
        public int? Id { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public string Keywords { get; set; }

        public string Synonyms { get; set; }

        public string MetaKeywords { get; set; }

        public string MetaTitle { get; set; }

        public string MetaDescription { get; set; }

        public string VanityUrl { get; set; }

        public string BimsmithVanityUrl { get; set; }

        public string IconUrl { get; set; }

        public int? ParentCategoryId { get; set; }

        public IEnumerable<string> KeyStatNames { get; set; }

        public float Weight { get; set; }

        public CategoryExcelDto()
        {
            KeyStatNames = new List<string>();
            Errors = new List<string>();
        }
    }
}