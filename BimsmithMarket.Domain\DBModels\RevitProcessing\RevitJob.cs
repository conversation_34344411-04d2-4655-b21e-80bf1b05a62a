﻿using BIMsmithMarket.Domain.Enums.RevitProcessing;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels.RevitProcessing
{
    public class RevitJob : BaseEntity
    {
        public RevitJobStatus Status { get; set; }

        public DateTime? FinishDate { get; set; }

        public string Report { get; set; }

        //Azure Queue fields
        [StringLength(100)]
        public string AzureQueueBaseName { get; set; }

        [StringLength(100)]
        public string AzureQueueMessageId { get; set; }

        [StringLength(100)]
        public string AzureQueuePopReceipt { get; set; }
        //

        public int RevitProcessId { get; set; }

        [ForeignKey("RevitProcessId")]
        public virtual RevitProcess RevitProcess { get; set; }

        public int ProductFileId { get; set; }

        [ForeignKey("ProductFileId")]
        public virtual ProductFile ProductFile { get; set; }

        public int ProjectDataTypeId { get; set; }

        [ForeignKey("ProjectDataTypeId")]
        public virtual ProjectDataType ProjectDataType { get; set; }

        public virtual ICollection<RevitJobRevitParameterMapping> RevitJobRevitParameterMappings { get; set; }

        public RevitJob()
        {
            RevitJobRevitParameterMappings = new List<RevitJobRevitParameterMapping>();
        }
    }
}