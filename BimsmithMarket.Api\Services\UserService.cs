﻿using BIMsmithMarket.Api.Providers;
using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.Authorization;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Domain.Models.ApplicationOauthProviderModels;
using BIMsmithMarket.Services.Interfaces;
using Flurl;
using Mapster;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Claims;
using System.Threading.Tasks;
using System.Web;

namespace BIMsmithMarket.Api.Services
{
    /// <summary>
    /// User service
    /// </summary>
    public class UserService : IUserService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IHttpClientFactory _httpClientFactory;

        public UserService(
            UserManager<ApplicationUser> userManager,
            IHttpClientFactory httpClientFactory)
        {
            _userManager = userManager;
            _httpClientFactory = httpClientFactory;
        }

        /// <summary>
        /// Indicate user subscription in social networks to bimsmith
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<bool> IsUserSubscribedAsync(
            string userId,
            IUnitOfWork unitOfWork)
        {
            return await unitOfWork.UserRepository.GetAll()
                 .Where(x => x.Id == userId)
                 .Select(x => x.Subscribed)
                 .FirstOrDefaultAsync();
        }

        /// <summary>
        /// Makes copy of BIMsmithMarket user on Market
        /// </summary>
        /// <param name="model">BIMsmithMarket user model</param>
        /// <param name="userManager">User manager</param>
        /// <returns></returns>
        public async Task<bool> AddBimsmithUser(AddBimsmithUserModel model)
        {
            if (model.SecretKey != ConfigurationHelper.GetValue("BimsmithAccessToken")) return false;

            var user = await _userManager.FindByEmailAsync(model.User.Email);
            if (user == null)
            {
                user = new ApplicationUser();
                user.Id = model.User.UserId;
                user.Email = model.User.Email;
                user.UserName = model.User.Email;
                user.FirstName = model.User.FirstName;
                user.LastName = model.User.LastName;
                user.CreatedDate = DateTime.UtcNow;

                var result = await _userManager.CreateAsync(user);

                if (result.Succeeded)
                {
                    await AddUserToRoleAsync(user.Id, DbConstants.CustomerRole);
                    return true;
                }
                return false;
            }
            else
            {
                user.Email = model.User.Email;
                user.UserName = model.User.Email;
                user.FirstName = model.User.FirstName;
                user.LastName = model.User.LastName;
                user.EmailConfirmed = true;

                var result = await _userManager.UpdateAsync(user);

                return result.Succeeded;
            }
        }

        public async Task<bool> DeleteAccountAsync(DeleteAccountModel model)
        {
            if (model.SecretKey != ConfigurationHelper.GetValue("BimsmithAccessToken"))
                return false;

            ApplicationUser user = await _userManager.FindByEmailAsync(model.Email);

            if (user == null)
                return false;

            IdentityResult result = await _userManager.DeleteAsync(user);
            return result.Succeeded;
        }

        public async Task<LoginIdentityResultDto> GetIdentityAsync(LoginDto model)
        {
            ApplicationUser user = null;
            string uToken = null;
            CookieCollection bimsmithCookies = null;

            if (!string.IsNullOrEmpty(model.Email))
            {
                LoginResult result = await CheckCredentials(model.Email, model.Password, model.RememberMe);

                if (!string.IsNullOrWhiteSpace(result.Error))
                    return new LoginIdentityResultDto
                    {
                        Error = result.Error
                    };

                uToken = result.AuthToken;
                user = result.User;
                bimsmithCookies = result.BIMsmithCookies;

                if (user == null)
                    return new LoginIdentityResultDto
                    {
                        Identity = null,
                        Error = result.Error
                    };
            }
            else
            {
                uToken = model.Password;
                var userToken = AuthenticationService.GetUserToken(uToken);
                if (userToken == null || userToken.UserId == null)
                    return null;

                user = await _userManager.FindByEmailAsync(userToken.UserEmail);

                if (user != null && (string.IsNullOrWhiteSpace(user.FirstName) || string.IsNullOrWhiteSpace(user.LastName)))
                {
                    var userInfo = await GetUserInfoById(userToken.UserId);
                    if (userInfo != null)
                    {
                        user.FirstName = userInfo.FirstName;
                        user.LastName = userInfo.LastName;
                        await _userManager.UpdateAsync(user);
                    }
                }

                if (user == null)
                {
                    var userInfo = await GetUserInfoById(userToken.UserId);
                    user = new ApplicationUser();
                    user.Id = userToken.UserId;
                    user.Email = userToken.UserEmail;
                    user.UserName = userToken.UserEmail;
                    user.CreatedDate = DateTime.UtcNow;

                    if (userInfo != null)
                    {
                        user.FirstName = userInfo.FirstName;
                        user.LastName = userInfo.LastName;
                        user.UserName = userInfo.UserName;
                    }

                    await _userManager.CreateAsync(user);
                    await AddUserToRoleAsync(user.Id, DbConstants.CustomerRole);
                }
            }

            //Update roles
            var adminEmails = ConfigurationHelper.GetValue("AdministratorEmails").Split(',').Select(a => a.ToLower()).ToList();
            var contentManagersEmails = ConfigurationHelper.GetValue("ContentManagersEmails").Split(',').Select(a => a.ToLower()).ToList();

            var userRoles = await _userManager.GetRolesAsync(user);

            var isSuperAdmin = adminEmails.Contains(user.Email.ToLower());
            if (isSuperAdmin && !userRoles.Contains(DbConstants.AdminRole))
            {
                await AddUserToRoleAsync(user.Id, DbConstants.AdminRole);
            }

            var isContentManager = contentManagersEmails.Contains(user.Email.ToLower());
            if (isContentManager && !userRoles.Contains(DbConstants.ContentManagerRole))
            {
                await AddUserToRoleAsync(user.Id, DbConstants.ContentManagerRole);
            }

            var roles = await _userManager.GetRolesAsync(user);

            if (isSuperAdmin)
                roles.Add(DbConstants.SuperAdminRole);

            using (var unitOfWork = UnitOfWork.Create())
            {
                var manufacturerRoles = unitOfWork.ManufacturerAdminUserRepository.GetAll().Where(a => a.AdminUserId == user.Id).Select(a => a.Roles).Distinct().ToList();
                var isManufacrurerOwner = unitOfWork.ManufacturerRepository.GetAll().Where(a => a.OwnerId == user.Id).Any();

                if (isManufacrurerOwner)
                {
                    roles.Add("MANUF_OWNER");
                }

                if (manufacturerRoles.Any(a =>
                ((a & ManufacturerAdminRole.Analytics) == ManufacturerAdminRole.Analytics
                ||
                (a & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess)))
                {
                    roles.Add("MANUF_ANALYTICS_ACCESS");
                }

                if (manufacturerRoles.Any(a =>
                ((a & ManufacturerAdminRole.AnalyticsForge) == ManufacturerAdminRole.AnalyticsForge
                ||
                (a & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess)))
                {
                    roles.Add("MANUF_ANALYTICS_FORGE_ACCESS");
                }
            }

            string bimsmithToken = uToken ?? AuthenticationService.GenerateAuthToken(user.Id, user.Email);

            return new LoginIdentityResultDto
            {
                FirstName = user.FirstName,
                Identity = await GetUserClaimsAsync(user.Email),
                LastName = user.LastName,
                Roles = string.Join(",", roles),
                U = bimsmithToken,
                UserId = user.Id,
                UserName = user.UserName,
                BIMsmithCookies = bimsmithCookies
            };
        }

        public async Task<ApplicationUserDto> GetUserInfoByEmailAsync(string email)
        {
            ApplicationUser user = await _userManager.FindByEmailAsync(email);
            if (user == null)
                return null;

            ApplicationUserDto userDto = user.Adapt<ApplicationUserDto>();
            IList<string> userRoles = await _userManager.GetRolesAsync(user);
            userDto.Roles = string.Join(',', userRoles);

            return userDto;
        }

        public async Task<ClaimsIdentity> GetUserClaimsAsync(string userEmail)
        {
            ApplicationUser user = await _userManager.FindByEmailAsync(userEmail);
            if (user == null)
                return null;

            IList<string> userRoles = await _userManager.GetRolesAsync(user);
            List<Claim> claims = new List<Claim>
            {
                new Claim(ClaimsIdentity.DefaultNameClaimType, user.NormalizedUserName),
                new Claim(ClaimTypes.NameIdentifier, user.Id),
                new Claim(ClaimTypes.Email, user.Email)
            };

            foreach (var role in userRoles)
            {
                claims.Add(new Claim(ClaimsIdentity.DefaultRoleClaimType, role));
            }

            ClaimsIdentity claimsIdentity =
                new ClaimsIdentity(claims, "ApplicationCookie", ClaimsIdentity.DefaultNameClaimType,
                    ClaimsIdentity.DefaultRoleClaimType);

            return claimsIdentity;
        }

        public async Task<BIMsmithUserInfoDto> GetBIMsmithUserInfoByIdAsync(string userId)
        {
            HttpClient httpClient = _httpClientFactory.CreateClient();
            Url url = new Url(ConfigurationHelper.GetValue("BimsmithApiUrl"))
                .AppendPathSegment("/auth/UserInfoById")
                .SetQueryParam("userId", userId);

            using HttpResponseMessage response = await httpClient.GetAsync(url);
            if (!response.IsSuccessStatusCode)
                return null;

            string jsonContent = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<BIMsmithUserInfoDto>(jsonContent);
        }

        public async Task<BIMsmithUserInfoDto> GetBIMsmithUserInfoByEmailAsync(string userEmail)
        {
            HttpClient httpClient = _httpClientFactory.CreateClient();
            Url url = new Url(ConfigurationHelper.GetValue("BimsmithApiUrl"))
                .AppendPathSegment("/auth/UserInfoByEmail")
                .SetQueryParam("userEmail", userEmail);

            using HttpResponseMessage response = await httpClient.GetAsync(url);
            if (!response.IsSuccessStatusCode)
                return null;

            string jsonContent = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<BIMsmithUserInfoDto>(jsonContent);
        }

        public async Task<bool> CheckAccountAsync(CheckAccountDto model, UserManager<ApplicationUser> userManager)
        {
            if (model.SecretKey != ConfigurationHelper.GetValue("BimsmithAccessToken"))
                throw new InvalidInputException("Invalid access token");

            ApplicationUser user = await userManager.FindByEmailAsync(model.Email);
            return user != null;
        }

        public async Task<bool> IsUserAdminAsync(string email, IUnitOfWork unitOfWork)
        {
            if (string.IsNullOrWhiteSpace(email))
                throw new InvalidInputException("User email cannot be empty");

            bool isUserAdmin = await unitOfWork.UserRoleRepository.GetAll()
                .AnyAsync(x => x.User.Email.ToUpper() == email.ToUpper()
                            && x.Role.Name == DbConstants.AdminRole);

            string[] superAdmin = ConfigurationHelper.GetValue("AdministratorEmails").Split(',').Select(a => a.ToUpper()).ToArray();
            bool isUserSuperAdmin = superAdmin.Contains(email.ToUpper());

            return isUserAdmin || isUserSuperAdmin;
        }

        #region private methods
        private async Task<LoginResult> CheckCredentials(string username, string password, bool rememberMe)
        {
            LoginResult result = new LoginResult();

            var secretKey = ConfigurationHelper.GetValue("LoginSecretKey");
            var endPoint = ConfigurationHelper.GetValue("LoginEndPoint");

            var parameters = new Dictionary<string, string>
            {
                { "secretkey", secretKey },
                { "email",  username },
                { "password", password },
                { "rememberme", rememberMe.ToString() }
            };

            var encodedContent = new FormUrlEncodedContent(parameters);

            var handler = new HttpClientHandler();

            using (HttpClient client = new HttpClient(handler))
            {
                try
                {
                    using (var response = await client.PostAsync(endPoint, encodedContent))
                    {
                        var jsonContent = await response.Content.ReadAsStringAsync();

                        if (response.StatusCode == HttpStatusCode.OK)
                        {
                            CookieCollection cookies = handler.CookieContainer.GetCookies(new Uri(ConfigurationHelper.GetValue("LoginDomain")));

                            result.AuthToken = HttpUtility.UrlDecode(cookies["authToken"].Value);
                            result.BIMsmithCookies = cookies;

                            var data = JsonConvert.DeserializeObject<LoginUser>(jsonContent);

                            result.User = await _userManager.FindByIdAsync(data.UserId);
                            if (result.User == null)
                                result.User = await _userManager.FindByEmailAsync(data.Email);

                            if (result.User == null)
                            {
                                result.User = new ApplicationUser();
                                result.User.Id = data.UserId;
                                result.User.Email = data.Email;
                                result.User.UserName = data.Email;
                                result.User.FirstName = data.FirstName;
                                result.User.LastName = data.LastName;
                                result.User.CreatedDate = DateTime.UtcNow;

                                var res = await _userManager.CreateAsync(result.User);

                                if (res.Succeeded)
                                {
                                    await AddUserToRoleAsync(result.User.Id, DbConstants.CustomerRole);
                                }
                                else  //problem to create the new user
                                {
                                    result.User = null;
                                }
                            }
                            else
                            {
                                result.User.Email = data.Email;
                                result.User.UserName = data.Email;
                                result.User.FirstName = data.FirstName;
                                result.User.LastName = data.LastName;
                                result.User.EmailConfirmed = true;

                                var res = await _userManager.UpdateAsync(result.User);
                                if (!res.Succeeded) //problem to update user info
                                {
                                    result.User = null;
                                }
                            }
                        }
                        else
                        {
                            HttpErrorResponseDto bimsmithError = JsonConvert.DeserializeObject<HttpErrorResponseDto>(jsonContent);
                            result.Error = bimsmithError.Message;
                        }
                    }
                }
                catch (Exception e)
                {
                    result.Error = e.Message;
                    Log.Error(e.Message, e);
                }
            }

            return result;
        }

        private async Task<ApplicationUser> GetUserInfoById(string userId)
        {
            ApplicationUser applicationUser = null;
            HttpClient client = _httpClientFactory.CreateClient();
            {
                try
                {
                    var endPoint = new Url(ConfigurationHelper.GetValue("BimsmithApiUrl"))
                        .AppendPathSegment("/auth/UserInfoById")
                        .SetQueryParam("userId", userId);

                    using (var response = await client.GetAsync(endPoint))
                    {
                        var jsonContent = await response.Content.ReadAsStringAsync();

                        if (response.IsSuccessStatusCode)
                        {
                            applicationUser = JsonConvert.DeserializeObject<ApplicationUser>(jsonContent);
                            return applicationUser;
                        }
                    }
                }
                catch (Exception)
                {
                    return null;
                }
            }

            return applicationUser;
        }

        private async Task AddUserToRoleAsync(string userId, string roleName)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            string roleId = await unitOfWork.RoleRepository.GetAll()
                .Where(x => x.Name.Trim().ToLower() == roleName.Trim().ToLower())
                .Select(x => x.Id)
                .FirstOrDefaultAsync();
            ApplicationUserRole userRole = new ApplicationUserRole();
            userRole.RoleId = roleId;
            userRole.UserId = userId;
            unitOfWork.UserRoleRepository.Insert(userRole);
            await unitOfWork.SaveAsync();
        }
        #endregion
    }
}