﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Models.ExcelModels.DynamicExcel
{
    public class DynamicExcelHandleImportModel
    {
        public IUnitOfWork UnitOfWork { get; set; }
        public string UserId { get; set; }
        public Manufacturer Manufacturer { get; set; }
        public List<string> Errors { get; set; }
        public List<string> RevitFileIdsQueue { get; set; }
        public List<MasterformatModel> Masterformats { get; set; }
    }
}