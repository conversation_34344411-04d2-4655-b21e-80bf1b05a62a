﻿using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models.MassTransit.Events;
using BIMsmithMarket.Services.Interfaces;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.MassTransit.Consumers
{
    public class QualityItemChangedConsumer : IConsumer<QualityItemChangedEvent>
    {
        private readonly IProductService _productService;

        public QualityItemChangedConsumer(IProductService productService)
        {
            _productService = productService;
        }

        public async Task Consume(ConsumeContext<QualityItemChangedEvent> context)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            int[] productIds = await unitOfWork.ProductQualityItemRepository.GetAll()
                .Where(x => x.QualityItemId == context.Message.QualityItemId)
                .Select(x => x.ProductId)
                .ToArrayAsync();

            foreach (int productId in productIds)
                await _productService.SaveProductToMongoAsync(productId, true, unitOfWork);
        }
    }
}