﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using Quartz;
using Serilog;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Jobs
{
    [DisallowConcurrentExecution]
    public class ClearSearchCacheJob : IJob
    {
        private readonly string _jobName = "Clear Search Cache Job";

        public async Task Execute(IJobExecutionContext context)
        {
            Log.Information($"[{_jobName}] started");

            CacheHelper.ClearSpecificCacheAndInvalidateSearch("*/api/Common/GeneralSearch*");
            CacheHelper.ClearSpecificCacheAndInvalidateSearch("*/api/Common/GetFilters*");
            CacheHelper.ClearSpecificCacheAndInvalidateSearch("*productIds*");
            CacheHelper.ClearSpecificCacheAndInvalidateSearch("*cachedResults*");
            CacheHelper.ClearSpecificCacheAndInvalidateSearch("*api/ProductAndStarter/Microsite/List*");
            CacheHelper.ClearSpecificCache("*api/ProjectDataType/List*");
            CacheHelper.ClearSpecificCache("*api/Category/Tree*");
            CacheHelper.ClearSpecificCache("*api/RevitPlugin/GetPluginList*");
            CacheHelper.ClearSpecificCache("*api/RevitPlugin/GetPromotedManufacturers*");

            var baseUrl = ConfigurationHelper.GetValue("MarketApiBaseUrl");
            var urls = new List<string>
                {
                    //USA first page with default filters
                    $"{baseUrl}api/Common/GeneralSearch?count=24&offset=0&orientations=&published=true&regionId=93&relatedTake=10&returnFilters=false&sortType=4&startersPosition=3",
                    //Canada first page with default filters
                    $"{baseUrl}api/Common/GeneralSearch?count=24&offset=0&orientations=&published=true&regionId=92&relatedTake=10&returnFilters=false&sortType=4&startersPosition=3",
                    //USA first  page with 'lighting'
                    $"{baseUrl}api/Common/GeneralSearch?count=24&offset=0&orientations=&published=true&q=lighting&regionId=93&relatedTake=10&returnFilters=false&sortType=0&startersPosition=3",
                    //Canada first page with 'lighting'
                    $"{baseUrl}api/Common/GeneralSearch?count=24&offset=0&orientations=&published=true&q=lighting&regionId=92&relatedTake=10&returnFilters=false&sortType=0&startersPosition=3",
                    //USA first page with 'flooring'
                    $"{baseUrl}api/Common/GeneralSearch?count=24&offset=0&orientations=&published=true&q=flooring&regionId=93&relatedTake=10&returnFilters=false&sortType=0&startersPosition=3",
                    //Canada first page with 'flooring'
                    $"{baseUrl}api/Common/GeneralSearch?count=24&offset=0&orientations=&published=true&q=flooring&regionId=92&relatedTake=10&returnFilters=false&sortType=0&startersPosition=3",
                    //USA first page with 'ceiling'
                    $"{baseUrl}api/Common/GeneralSearch?count=24&offset=0&orientations=&published=true&q=ceiling&regionId=93&relatedTake=10&returnFilters=false&sortType=0&startersPosition=3",
                    //Canada first page with 'ceiling'
                    $"{baseUrl}api/Common/GeneralSearch?count=24&offset=0&orientations=&published=true&q=ceiling&regionId=92&relatedTake=10&returnFilters=false&sortType=0&startersPosition=3",
                    //USA first page with 'floor'
                    $"{baseUrl}api/Common/GeneralSearch?count=24&offset=0&orientations=&published=true&q=floor&regionId=93&relatedTake=10&returnFilters=false&sortType=0&startersPosition=3",
                    //Canada first page with 'floor'
                    $"{baseUrl}api/Common/GeneralSearch?count=24&offset=0&orientations=&published=true&q=floor&regionId=92&relatedTake=10&returnFilters=false&sortType=0&startersPosition=3",
                    //USA first page with 'wall'
                    $"{baseUrl}api/Common/GeneralSearch?count=24&offset=0&orientations=&published=true&q=wall&regionId=93&relatedTake=10&returnFilters=false&sortType=0&startersPosition=3",
                    //Canada first page with 'wall'
                    $"{baseUrl}api/Common/GeneralSearch?count=24&offset=0&orientations=&published=true&q=wall&regionId=92&relatedTake=10&returnFilters=false&sortType=0&startersPosition=3",
                    //USA first page with 'roof'
                    $"{baseUrl}api/Common/GeneralSearch?count=24&offset=0&orientations=&published=true&q=roof&regionId=93&relatedTake=10&returnFilters=false&sortType=0&startersPosition=3",
                    //Canada first page with 'roof'
                    $"{baseUrl}api/Common/GeneralSearch?count=24&offset=0&orientations=&published=true&q=roof&regionId=92&relatedTake=10&returnFilters=false&sortType=0&startersPosition=3",
                    //BIMsmith plugin
                    $"{baseUrl}api/Common/GeneralSearch?published=true&offset=0&count=32&q=a",
                    $"{baseUrl}api/ProjectDataType/List",
                    $"{baseUrl}api/Category/Tree",
                    $"{baseUrl}api/RevitPlugin/GetPluginList",
                    $"{baseUrl}api/RevitPlugin/GetPromotedManufacturers"
                };

            var httpClient = HttpClientFactory.GetClient();
            foreach (var url in urls)
            {
                await httpClient.GetAsync(url);
                Thread.Sleep(1000);
            }
        }
    }
}