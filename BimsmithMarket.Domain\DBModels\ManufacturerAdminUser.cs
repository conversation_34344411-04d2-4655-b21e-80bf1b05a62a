﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class ManufacturerAdminUser
    {
        public int Id { get; set; }

        public int ManufacturerId { get; set; }

        public int Status { get; set; }

        //[NotMapped]
        public string Email { get; set; }

        public string AdminUserId { get; set; }

        public string AddedById { get; set; }

        public DateTime AddedDate { get; set; }

        public ManufacturerAdminRole Roles { get; set; }

        public string ModifiedById { get; set; }

        public DateTime? ModifiedDate { get; set; }

        /// ------------------------------------------
        [ForeignK<PERSON>("AddedById")]
        public virtual ApplicationUser AddedBy { get; set; }

        [ForeignKey("ModifiedById")]
        public virtual ApplicationUser ModifiedBy { get; set; }

        [ForeignKey("AdminUserId")]
        public virtual ApplicationUser AdminUser { get; set; }

        [ForeignKey("ManufacturerId")]
        public virtual Manufacturer Manufacturer { get; set; }
    }

    [Flags]
    public enum ManufacturerAdminRole
    {
        None = 0,
        Analytics = 1,
        AddDeleteProduct = 2,
        EditManufacturer = 4,
        ManageUsers = 8,
        FullAccess = 16,
        AnalyticsForge = 32,
        SalesforceAccess = 64,
        NanolumensAnalytics = 128,
        AnalyticsManufacturerActionsData = 256
    }
}