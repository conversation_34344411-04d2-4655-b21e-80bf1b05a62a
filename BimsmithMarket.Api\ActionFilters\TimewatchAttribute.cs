﻿using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc.Filters;
using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.ActionFilters
{
    public class TimewatchAttribute : Attribute, IAsyncActionFilter
    {
        private readonly string _logFilePath = @"C:\Work Folder\TimewatchLog.txt";
        private string _methodName;

        public TimewatchAttribute(string methodName)
        {
            _methodName = methodName;
        }

        public async Task OnActionExecutionAsync(
           ActionExecutingContext executingContext,
           ActionExecutionDelegate next)
        {
            #region setup
            string folder = Path.GetDirectoryName(_logFilePath);
            if (!Directory.Exists(folder))
                Directory.CreateDirectory(folder);

            string pathAndQuery = executingContext.HttpContext.Request.GetEncodedPathAndQuery();
            if (string.IsNullOrWhiteSpace(_methodName))
                _methodName = pathAndQuery;

            #endregion

            #region execute method
            Stopwatch stopwatch = Stopwatch.StartNew();
            await next();
            stopwatch.Stop();
            #endregion

            #region write log
            using StreamWriter writer = new StreamWriter(_logFilePath, true);
            writer.WriteLine($"{DateTime.UtcNow}: Method: {_methodName} Path and Query: {pathAndQuery} Elapsed Milliseconds: {stopwatch.ElapsedMilliseconds}");
            #endregion
        }
    }
}