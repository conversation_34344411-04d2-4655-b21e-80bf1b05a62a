﻿using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IKeyStatUnitService
    {
        Task<object> GroupsAsync(IUnitOfWork unitOfWork);
        Task<object> ListAsync(IUnitOfWork unitOfWork, string q, string group, int count, int offset);
        Task<object> GetAsync(IUnitOfWork unitOfWork, int id);
        Task<object> AddAsync(IUnitOfWork unitOfWork, AddKeyStatUnitModel model, string userId);
        Task EditAsync(IUnitOfWork unitOfWork, EditKeyStatUnitModel model, string userId);
        Task DeleteAsync(IUnitOfWork unitOfWork, int id);
    }
}
