﻿using BIMsmithMarket.Domain.DBModels.PaymentDbModels;
using BIMsmithMarket.Domain.Dto.PaymentsDto;
using Mapster;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public static class ProductPriceMap
    {
        public static void Register(TypeAdapterConfig config)
        {
            config.ForType<ProductPrice, ProductPriceDto>()
                .Map(dest => dest.ProductPrice, opt => opt.ProductAmount)
                .Ignore(dest => dest.CurrentProductPrice);
        }
    }
}
