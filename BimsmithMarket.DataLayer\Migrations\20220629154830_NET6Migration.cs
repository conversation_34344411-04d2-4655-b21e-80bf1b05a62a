﻿using Microsoft.EntityFrameworkCore.Migrations;
using System;

#nullable disable
namespace BIMsmithMarket.DataLayer.Migrations
{
    public partial class NET6Migration : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AspNetRoleClaims",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    RoleId = table.Column<string>(type: "nvarchar(128)", nullable: false),
                    ClaimType = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ClaimValue = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetRoleClaims", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AspNetRoleClaims_Roles_RoleId",
                        column: x => x.RoleId,
                        principalTable: "Roles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserTokens",
                columns: table => new
                {
                    UserId = table.Column<string>(type: "nvarchar(128)", nullable: false),
                    LoginProvider = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    Value = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserTokens", x => new { x.UserId, x.LoginProvider, x.Name });
                    table.ForeignKey(
                        name: "FK_AspNetUserTokens_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AspNetRoleClaims_RoleId",
                table: "AspNetRoleClaims",
                column: "RoleId");

            migrationBuilder.AddColumn<string>(
               name: "ConcurrencyStamp",
               table: "Users",
               type: "nvarchar(max)",
               nullable: true);

            migrationBuilder.AddColumn<DateTimeOffset>(
               name: "LockoutEnd",
               table: "Users",
               type: "datetimeoffset(7)",
               nullable: true);

            migrationBuilder.AddColumn<string>(
               name: "NormalizedEmail",
               table: "Users",
               type: "nvarchar(256)",
               nullable: true);

            migrationBuilder.AddColumn<string>(
               name: "NormalizedUserName",
               table: "Users",
               type: "nvarchar(max)",
               nullable: true);

            migrationBuilder.AddColumn<string>(
              name: "NormalizedName",
              table: "Roles",
              type: "nvarchar(256)",
              nullable: true);

            migrationBuilder.AddColumn<string>(
              name: "ConcurrencyStamp",
              table: "Roles",
              type: "nvarchar(max)",
              nullable: true);

            migrationBuilder.AddColumn<string>(
              name: "ApplicationUserId",
              table: "UserRoles",
              type: "nvarchar(128)",
              nullable: true);

            migrationBuilder.AddColumn<string>(
              name: "ApplicationRoleId",
              table: "UserRoles",
              type: "nvarchar(450)",
              nullable: true);

            migrationBuilder.AddColumn<string>(
              name: "ProviderDisplayName",
              table: "UserLogins",
              type: "nvarchar(max)",
              nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AspNetRoleClaims");

            migrationBuilder.DropTable(
                name: "AspNetUserTokens");
        }
    }
}
