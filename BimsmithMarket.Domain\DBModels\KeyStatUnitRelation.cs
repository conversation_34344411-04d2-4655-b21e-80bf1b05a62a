﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class KeyStatUnitRelation
    {
        [Key]
        [Column(Order = 1)]
        public int FromUnitId { get; set; }

        [Key]
        [Column(Order = 2)]
        public int ToUnitId { get; set; }

        public string Relation { get; set; }

        public float? Multiplicator { get; set; }

        public string CreatedById { get; set; }

        public DateTime CreatedDate { get; set; }

        public string ModifiedById { get; set; }

        public DateTime? ModifiedDate { get; set; }

        public bool IsDefault { get; set; }

        public int RoundCountDigits { get; set; }

        public int Status { get; set; }

        /// ------------------------------------------
        [ForeignKey("CreatedById")]
        public virtual ApplicationUser CreatedBy { get; set; }

        [ForeignKey("ModifiedById")]
        public virtual ApplicationUser ModifiedBy { get; set; }

        [ForeignKey("FromUnitId")]
        public virtual KeyStatUnit FromUnit { get; set; }

        [ForeignKey("ToUnitId")]
        public virtual KeyStatUnit ToUnit { get; set; }


    }
}
