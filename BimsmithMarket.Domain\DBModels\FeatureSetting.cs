﻿using BIMsmithMarket.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.DBModels
{

    [Index(nameof(Name), IsUnique = true)]
    public class FeatureSetting
    {
        [Key]
        public int Id { get; set; }

        [MaxLength(250)]
        public string Name { get; set; }

        public bool Status { get; set; }

        public FeatureSettingTarget Target { get; set; }
    }
}