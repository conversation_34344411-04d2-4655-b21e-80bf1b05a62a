﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class File
    {
        public int Id { get; set; }

        public string FileName { get; set; }

        public string Title { get; set; }

        public string MediaType { get; set; }

        public long FileSize { get; set; }

        public string Url { get; set; }

        public string PreviewUrl { get; set; }

        public string SyncUrl { get; set; }

        public SyncFileStatus SyncStatus { get; set; }

        public SyncRevitStatus SyncRevitStatus { get; set; }

        public int SyncStatusCode { get; set; }

        public DateTime NextSyncDateTime { get; set; }

        public int UpdatesCount { get; set; }

        public string CheckSum { get; set; }

        public string CreatedById { get; set; }

        public DateTime CreatedDate { get; set; }

        /// ------------------------------------------
        [<PERSON><PERSON><PERSON>("CreatedById")]
        public virtual ApplicationUser CreatedBy { get; set; }
    }

    public enum SyncFileStatus
    {
        Free = 0,
        InProcess = 1
    }

    public enum SyncRevitStatus
    {
        Free = 0,
        InProcess = 1,
        NotFound = 2
    }
}