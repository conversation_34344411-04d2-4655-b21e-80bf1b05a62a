﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace BIMsmithMarket.Core.Helpers
{
    public static class StringValidatorHelper
    {
        private static readonly List<string> _restrictedSybmols = new List<string> { "\u001e" };

        public static void ValidateStrings(object @object)
        {
            if (@object == null)
                return;

            Type type = @object.GetType();
            IEnumerable<PropertyInfo> allProperties = type.GetProperties().Where(x => !x.GetIndexParameters().Any());

            IEnumerable<PropertyInfo> stringProperties = allProperties.Where(x => x.PropertyType == typeof(string));
            foreach (PropertyInfo property in stringProperties)
                CheckStringProperty(@object, property);

            IEnumerable<PropertyInfo> collectionProperties = allProperties.Where(x => x.PropertyType.GetInterfaces().Contains(typeof(IEnumerable)) && x.PropertyType != typeof(string));
            foreach (PropertyInfo property in collectionProperties)
            {
                object collection = property.GetValue(@object);

                if (collection == null)
                    continue;

                foreach (object item in (IEnumerable)collection)
                {
                    IEnumerable<PropertyInfo> itemStringProperties = item.GetType().GetProperties().Where(x => !x.GetIndexParameters().Any() && x.PropertyType == typeof(string));
                    foreach (PropertyInfo stringProperty in itemStringProperties)
                        CheckStringProperty(item, stringProperty);
                }
            }

            IEnumerable<PropertyInfo> otherProperties = allProperties.Where(x => x.PropertyType != typeof(string));

            foreach (PropertyInfo property in otherProperties)
                ValidateStrings(property.GetValue(@object));
        }

        private static void CheckStringProperty(object @object, PropertyInfo property)
        {
            string propertyValue = (string)property.GetValue(@object);

            if (string.IsNullOrWhiteSpace(propertyValue))
                return;

            bool valueChanged = false;

            foreach (string restrictedSybmol in _restrictedSybmols)
            {
                if (propertyValue.Contains(restrictedSybmol))
                {
                    propertyValue = propertyValue.Replace(restrictedSybmol, string.Empty);
                    valueChanged = true;
                }
            }

            if (valueChanged)
                property.SetValue(@object, propertyValue);
        }
    }
}