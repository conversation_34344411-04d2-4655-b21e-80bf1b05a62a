﻿using BIMsmithMarket.Core.Helpers;
using System.Linq;
using System.Text;

namespace BIMsmithMarket.Services.Helpers
{
    public static class RobotsHelper
    {
        public static byte[] GenerateRobots()
        {
            var robotsContentStrings = ConfigurationHelper.GetValue("RobotsContent")?.Split(';');

            var robotsResult = new StringBuilder();

            if (robotsContentStrings.Any())
            {
                foreach (var robotsContentString in robotsContentStrings)
                {
                    robotsResult.AppendLine(robotsContentString);
                }
            }

            return Encoding.UTF8.GetBytes(robotsResult.ToString());
        }
    }
}