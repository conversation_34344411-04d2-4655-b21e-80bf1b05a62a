﻿using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using System;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IManufacturerAdminUserService
    {
        Task<object> AddAsync(IUnitOfWork unitOfWork, string userId, AddManufacturerAdminUserModel model);
        Task EditAsync(IUnitOfWork unitOfWork, string userId, EditManufacturerAdminUserModel model);
        Task DeleteAsync(IUnitOfWork unitOfWork, int id);
        Task<object> ListAsync(IUnitOfWork unitOfWork, int manufacturerId, DateTime? dateFrom, DateTime? dateTo, int count, int offset);
    }
}
