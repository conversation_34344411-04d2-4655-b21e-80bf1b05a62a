﻿using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class DetailPhoto : BaseEntity
    {

        public int DetailId { get; set; }

        public int PhotoId { get; set; }

        public string Title { get; set; }

        [ForeignKey("DetailId")]
        public virtual Detail Detail { get; set; }

        [ForeignKey("PhotoId")]
        public virtual Photo Photo { get; set; }
    }
}