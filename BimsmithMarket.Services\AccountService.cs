﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class AccountService : IAccountService
    {
        private const string Default_Stream_Id = "f22db287-52e3-483f-9079-2b527cce317f";

        public async Task<object> MyBIMsmithProductsList(IUnitOfWork unitOfWork, string userId, string q, int offset, int count)
        {
            var query = unitOfWork.UserBIMsmithProductRepository.GetAll().Where(a => a.AddedById == userId);

            if (!string.IsNullOrEmpty(q))
            {
                query = query.Where(e =>
                           e.Product.Name.Contains(q));
            }

            var countOfData = await query.CountAsync();

            var products = await query
                        .OrderBy(a => a.Id)
                        .Skip(offset)
                        .Take(count)
                        .Select(a => new
                        {
                            id = a.Id,
                            name = a.Product.Name,
                            description = a.Product.Description,
                            productRating = a.Product.ProductRating,
                            contentRating = a.Product.ContentRating,
                            category = new
                            {
                                id = a.Product.CategoryId,
                                name = a.Product.Category.Name,
                                parent = a.Product.Category.ParentCategoryId == null ? null : new
                                {
                                    id = a.Product.Category.ParentCategoryId,
                                    name = a.Product.Category.ParentCategory.Name,
                                    parent = a.Product.Category.ParentCategoryId == null ? null : new
                                    {
                                        id = a.Product.Category.ParentCategory.ParentCategoryId,
                                        name = a.Product.Category.ParentCategory.ParentCategory.Name,
                                    }
                                }
                            },
                            photoUrl = a.Product.PhotoId != null ? a.Product.Photo.SmallImgUrl : a.Product.ProductPhotos.FirstOrDefault().Photo.SmallImgUrl,
                            manufacture = a.Product.Manufacturer.Name,
                        })
                        .ToListAsync();

            return new
            {
                count = countOfData,
                data = products
            };
        }

        public async Task AddProductToMyBIMsmithAsync(IUnitOfWork unitOfWork, string userId, int productId, string streamId)
        {
            if (!await unitOfWork.UserBIMsmithProductRepository.GetAll()
                    .AnyAsync(a => a.ProductId == productId && a.AddedById == userId))
            {
                UserBIMsmithProduct userBIMsmithProduct = new UserBIMsmithProduct();
                userBIMsmithProduct.ProductId = productId;
                userBIMsmithProduct.StreamId = streamId ?? Default_Stream_Id;
                userBIMsmithProduct.AddedById = userId;
                userBIMsmithProduct.AddedDate = DateTime.UtcNow;

                unitOfWork.UserBIMsmithProductRepository.Insert(userBIMsmithProduct);
                await unitOfWork.SaveAsync();
            }
        }
    }
}
