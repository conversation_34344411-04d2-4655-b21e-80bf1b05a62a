﻿using Azure.Storage.Blobs;
using Azure.Storage.Files.Shares;
using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using Quartz;
using Serilog;
using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;

namespace BIMsmithMarket.Api.Jobs
{
    [DisallowConcurrentExecution]
    public class DownloadRevitsJob : IJob
    {
        private readonly string _jobName = "Download Revits Job";
        private const string InternsRevitFilesShareDirectoryName = "internsrevitfiles";
        private readonly IAzureStorageService _azureStorageService;

        public DownloadRevitsJob(IAzureStorageService azureStorageService)
        {
            _azureStorageService = azureStorageService;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            Log.Information($"[{_jobName}] started");

            try
            {
                var fileShareDirectory = _azureStorageService.GetFileShareByName(AzureStorageConstants.MarketRevitFileShare);
                var revitsQueue = _azureStorageService.GetQueueByName(AzureStorageConstants.ProductRevitsQueue);
                var filesContainer = _azureStorageService.GetContainerByName(AzureStorageConstants.FilesContainer);
                var rootDir = fileShareDirectory.GetRootDirectoryClient();

                AzureStorageService internsAzureBlobProvider = new AzureStorageService(
                    ConfigurationHelper.GetValue("InternsBimsmithStorageConnectionString"),
                    ConfigurationHelper.GetValue("Environment"),
                    bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                    false);
                var internsFileShareDirectory = internsAzureBlobProvider.GetFileShareByName(AzureStorageConstants.InternsRevitFileShare);
                var internsRootDir = internsFileShareDirectory.GetRootDirectoryClient();

                while (await _azureStorageService.GetMessageCountAsync(revitsQueue.Name) > 0)
                {
                    var message = revitsQueue.ReceiveMessage().Value;
                    if (message != null)
                    {
                        int fileId = -1;
                        if (int.TryParse(message.MessageText, out fileId))
                        {
                            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
                            {
                                try
                                {
                                    unitOfWork.BeginTransaction();
                                    var file = unitOfWork.FileRepository.GetAll().FirstOrDefault(f => f.Id == fileId);
                                    if (file != null)
                                    {
                                        file.SyncRevitStatus = SyncRevitStatus.Free;
                                        var fileName = HttpUtility.UrlDecode(file.FileName).ToLower();
                                        if (!string.IsNullOrWhiteSpace(file.SyncUrl) && file.SyncUrl.Contains(InternsRevitFilesShareDirectoryName))
                                        {
                                            var internsRevitFile = internsRootDir.GetFileClient(fileName);
                                            if (internsRevitFile != null && internsRevitFile.Exists())
                                            {
                                                var cloudFile = rootDir.GetFileClient(file.FileName);
                                                using (var fileStream = internsRevitFile.OpenRead())
                                                {
                                                    cloudFile.Upload(fileStream);
                                                }
                                                file.SyncUrl = cloudFile.Uri.ToString();

                                                UploadRevitToBlob(file, internsRevitFile, filesContainer);
                                            }
                                            else
                                            {
                                                file.SyncRevitStatus = SyncRevitStatus.NotFound;
                                            }
                                        }
                                        else
                                        {
                                            var revitFile = rootDir.GetFileClient(fileName);
                                            if (revitFile != null && revitFile.Exists())
                                            {
                                                UploadRevitToBlob(file, revitFile, filesContainer);
                                            }
                                            else
                                            {
                                                file.SyncRevitStatus = SyncRevitStatus.NotFound;
                                            }
                                        }

                                        unitOfWork.FileRepository.Edit(file);
                                        Debug.WriteLine($"Passed for fileId: {fileId} at " + DateTime.UtcNow.ToString());
                                        Log.Information($"[{_jobName}] " + $"Passed for fileId: {fileId} at " + DateTime.UtcNow.ToString());
                                        unitOfWork.Save();
                                        unitOfWork.CommitTransaction();
                                        revitsQueue.DeleteMessage(message.MessageId, message.PopReceipt);
                                    }
                                    else
                                    {
                                        Debug.WriteLine("Not found fileId: " + fileId + " at " + DateTime.UtcNow.ToString());
                                        Log.Information($"[{_jobName}] " + "Not found fileId: " + fileId + " at " + DateTime.UtcNow.ToString());
                                        revitsQueue.DeleteMessage(message.MessageId, message.PopReceipt);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Debug.WriteLine(ex.Message);
                                    Log.Error($"[{_jobName}] " + $"Error for file Id: {fileId}", ex);
                                    revitsQueue.DeleteMessage(message.MessageId, message.PopReceipt);
                                    continue;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Debug.WriteLine(e.Message);
                Log.Error($"[{_jobName}] " + e.Message, e);
            }
        }

        private static void UploadRevitToBlob(Domain.DBModels.File file, ShareFileClient cloudFile, BlobContainerClient filesContainer)
        {
            var blobName = file.Id.ToString() + Path.GetExtension(file.FileName);
            BlobClient fileBlobUpload = filesContainer.GetBlobClient(blobName);

            if (fileBlobUpload != null && fileBlobUpload.Exists())
            {
                fileBlobUpload.StartCopyFromUri(cloudFile.Uri);
            }
            else
            {
                using (var fileStream = cloudFile.OpenRead())
                {
                    fileBlobUpload.Upload(fileStream);
                }
            }
            file.Url = fileBlobUpload.Uri.ToString();
        }
    }
}