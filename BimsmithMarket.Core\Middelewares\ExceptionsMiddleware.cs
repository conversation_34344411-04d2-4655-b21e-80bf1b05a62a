﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Core.Helpers;
using Microsoft.AspNetCore.Http;
using Serilog;
using System;
using System.Net;
using System.Threading.Tasks;

namespace BIMsmithMarket.Core.Middelewares
{
    public class ExceptionsMiddleware
    {
        private readonly RequestDelegate _next;

        public ExceptionsMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception exception)
            {
                HttpStatusCode statusCode = HttpStatusCode.InternalServerError;

                if (exception is DbItemNotFoundException
                    || exception is BlobFileNotFoundException)
                    statusCode = HttpStatusCode.NotFound;

                if (exception is PaymentConfirmationException)
                    statusCode = HttpStatusCode.PaymentRequired;

                if (exception is InvalidInputException
                    || exception is ModifiedOperationException
                    || exception is NoRecommendationsException
                    || exception is BlockedFileExtensionException)
                    statusCode = HttpStatusCode.BadRequest;

                if (exception is UnauthorizedAccessException)
                    statusCode = HttpStatusCode.Unauthorized;

                if (exception is OperationCanceledException)
                    statusCode = HttpStatusCode.Conflict;

                context.Response.StatusCode = (int)statusCode;

                Log.Error($"Exception Middleware {exception.GetAllMessages()}");

                await context.Response.WriteAsync(exception.Message);
            }
        }
    }
}