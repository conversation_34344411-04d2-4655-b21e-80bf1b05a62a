﻿using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Models.ExcelModels.StaticExcel
{
    public class StaticExcelCache
    {
        public List<StaticExcelCacheQualityItemModel> QualityItems { get; set; }

        public List<StaticExcelCacheCisfbModel> Cisfbs { get; set; }

        public List<MasterformatModel> Masterformats { get; set; }

        public List<StaticExcelCacheOmniclassModel> Omniclasses { get; set; }

        public List<StaticExcelCacheUniclassModel> Uniclasses { get; set; }

        public List<StaticExcelCacheUniformatModel> Uniformats { get; set; }

        public List<StaticExcelCacheKeystatModel> Keystats { get; set; }

        public List<StaticExcelCacheKeystatUnitModel> KeystatUnits { get; set; }

        public List<StaticExcelCacheProjectTypeModel> ProjectTypes { get; set; }

        public List<StaticExcelManufacturerModel> Manufacturers { get; set; }

        public List<StaticExcelCategoryModel> Categories { get; set; }
    }

    public class StaticExcelCacheQualityItemModel
    {
        public int Id { get; set; }

        public string Name { get; set; }
    }

    public class StaticExcelCacheCisfbModel
    {
        public int Id { get; set; }

        public string Code { get; set; }
    }

    public class StaticExcelCacheOmniclassModel
    {
        public int Id { get; set; }

        public string Code { get; set; }
    }

    public class StaticExcelCacheUniclassModel
    {
        public int Id { get; set; }

        public string Code { get; set; }
    }

    public class StaticExcelCacheUniformatModel
    {
        public int Id { get; set; }

        public string Code { get; set; }
    }

    public class StaticExcelCacheKeystatModel
    {
        public int Id { get; set; }

        public string Name { get; set; }
    }

    public class StaticExcelCacheKeystatUnitModel
    {
        public int Id { get; set; }

        public string GroupName { get; set; }

        public string BUnitName { get; set; }
    }

    public class StaticExcelCacheProjectTypeModel
    {
        public int Id { get; set; }

        public string Title { get; set; }

        public int? ParentId { get; set; }
    }

    public class StaticExcelManufacturerModel
    {
        public int Id { get; set; }

        public string Name { get; set; }
    }

    public class StaticExcelCategoryModel
    {
        public int Id { get; set; }

        public string Name { get; set; }
    }
}