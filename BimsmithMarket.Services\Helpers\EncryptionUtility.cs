﻿using System;
using System.Security.Cryptography;
using System.Text;

namespace BIMsmithMarket.Services.Helpers
{
    public class EncryptionUtility
    {
        #region Public Methods

        /// <summary>
        /// Encrypts a data string with an encryption key
        /// </summary>
        /// <param name="encryptionKey"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        public static string Encrypt(string encryptionKey, string data)
        {
            return Convert.ToBase64String(TransformBlock(GetCrypto(encryptionKey), Encoding.ASCII.GetBytes(data)));
        }

        /// <summary>
        /// Decrypts a data string
        /// </summary>
        /// <param name="encryptionKey"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        public static string Decrypt(string encryptionKey, string data)
        {
            data = data.Replace(" ", "+");
            return Encoding.ASCII.GetString(TransformBlockBack(GetCrypto(encryptionKey), Convert.FromBase64String(data)));
        }

        #endregion

        #region Private Methods

        private static TripleDESCryptoServiceProvider GetCrypto(string encryptionKey)
        {
            var keyhash = new MD5CryptoServiceProvider().ComputeHash(Encoding.ASCII.GetBytes(encryptionKey));
            return new TripleDESCryptoServiceProvider { Key = keyhash, Mode = CipherMode.ECB };
        }

        private static byte[] TransformBlock(SymmetricAlgorithm des, byte[] data)
        {
            return des.CreateEncryptor().TransformFinalBlock(data, 0, data.Length);
        }

        private static byte[] TransformBlockBack(SymmetricAlgorithm des, byte[] data)
        {
            return des.CreateDecryptor().TransformFinalBlock(data, 0, data.Length);
        }

        #endregion
    }
}