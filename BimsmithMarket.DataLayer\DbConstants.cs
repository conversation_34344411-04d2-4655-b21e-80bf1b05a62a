﻿namespace BIMsmithMarket.DataLayer
{
    public static class DbConstants
    {
        public const string DataBaseConnectionName = "MarketDBConnection";

        public const string AdminUserId = "00000000-0000-1234-0000-000000009999";

        public const int ConnectionTimeout = 100;

        #region Feature Names
        public const string FeatureAddProductViaAdminPanel = "Add Product via Admin Panel";

        public const string FeatureUpdateProductViaAdminPanel = "Update Product via Admin Panel";

        public const string FeatureCloneProductViaAdminPanel = "Clone Product via Admin Panel";

        public const string FeatureAddUpdateProductViaExternalApi = "Add/Update Product via External Api";

        public const string FeatureDeleteProductViaAdminPanel = "Delete Product via Admin Panel";

        public const string FeatureAddManufacturerViaAdminPanel = "Add Manufacturer via Admin Panel";

        public const string FeatureUpdateManufacturerViaAdminPanel = "Update Manufacturer via Admin Panel";

        public const string FeatureDeleteManufacturerViaAdminPanel = "Delete Manufacturer via Admin Panel";

        public const string FeatureAddCategoryViaAdminPanel = "Add Category via Admin Panel";

        public const string FeatureUpdateCategoryViaAdminPanel = "Update Category via Admin Panel";

        public const string FeatureDeleteCategoryViaAdminPanel = "Delete Category via Admin Panel";

        public const string FeatureStaticProductsImport = "Import Products via Static Excel";

        public const string FeatureDynamicProductsImport = "Import Products via Dynamic Excel";

        public const string FeatureHideCSIMasterformatFilter = "Hide CSI Masterformat Filter";

        public const string FeatureHideUniformatFilter = "Hide Uniformat Filter";

        public const string FeatureHideOmniclass11Filter = "Hide Omniclass 11 Filter";

        public const string FeatureHideOmniclass12Filter = "Hide Omniclass 12 Filter";

        public const string FeatureHideOmniclass13Filter = "Hide Omniclass 13 Filter";

        public const string FeatureHideUniclassFilter = "Hide Uniclass Filter";

        public const string FeatureHideCisfbFilter = "Hide Cisfb Filter";
        #endregion

        public const string ProductsTableName = "[dbo].[Products]";

        public const string ProductLinesTableName = "[dbo].[ProductLines]";

        public const string DetailsTableName = "[dbo].[Details]";

        #region roles
        public const string AdminRole = "ADMIN";

        public const string ExternalApiRole = "EXTERNALAPI";

        public const string CustomerRole = "CUSTOMER";

        public const string ContentManagerRole = "ContentManager";

        public const string SuperAdminRole = "SUPERADMIN";

        public const string CheckAdminRole = "CheckAdmin";
        #endregion
    }
}