﻿using BIMsmithMarket.Core.Helpers;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;

namespace BIMsmithMarket.Services.Helpers
{
    public class ExcelParser
    {
        private readonly string _tempPath = Path.Combine(Path.GetTempPath(), ConfigurationHelper.GetValue("Environment"));
        public ParseOmniclassMasterformatResult ParseMasterformats(Domain.DBModels.File excelFile)
        {
            string filePath = Path.Combine(_tempPath, excelFile.Id.ToString() + Path.GetExtension(excelFile.FileName));

            var document = SpreadsheetDocument.Open(filePath, false);
            var workbookPart = document.WorkbookPart;
            var workbook = workbookPart.Workbook;
            var sheets = workbook.Descendants<Sheet>();

            string error = null;
            var errors = new List<string>();
            var items = new List<OmniclassMasterformatParseItem>();


            //A - Code
            //B - Title 1
            //C - Title 2
            //D - Title 3
            //E - Title 4

            var worksheetPart = (WorksheetPart)workbookPart.GetPartById(sheets.First().Id);
            var sharedStringPart = workbookPart.SharedStringTablePart;
            var sheetData = worksheetPart.Worksheet.GetFirstChild<SheetData>();
            var sharedStringTable = workbookPart.GetPartsOfType<SharedStringTablePart>().FirstOrDefault();

            var rows = worksheetPart.Worksheet.Descendants<Row>().Take(8250).ToList();


            OmniclassMasterformatParseItem currentItemLevel1 = null;
            OmniclassMasterformatParseItem currentItemLevel2 = null;
            OmniclassMasterformatParseItem currentItemLevel3 = null;
            OmniclassMasterformatParseItem currentItemLevel4 = null;

            //Prepare Items
            foreach (var row in rows.Skip(0))
            {
                var code = this.GetCellStringValue(row, "A", sharedStringPart, out error);

                if (!string.IsNullOrEmpty(code))
                {
                    code = code.Trim();

                    var title1 = this.GetCellStringValue(row, "B", sharedStringPart, out error);
                    var title2 = this.GetCellStringValue(row, "C", sharedStringPart, out error);
                    var title3 = this.GetCellStringValue(row, "D", sharedStringPart, out error);
                    var title4 = this.GetCellStringValue(row, "E", sharedStringPart, out error);

                    if (string.IsNullOrWhiteSpace(title1) == false) //Root
                    {
                        currentItemLevel1 = new OmniclassMasterformatParseItem(code, title1.Trim());
                        items.Add(currentItemLevel1);
                    }

                    if (string.IsNullOrWhiteSpace(title2) == false) //level 2
                    {
                        currentItemLevel2 = new OmniclassMasterformatParseItem(code, title2.Trim());
                        currentItemLevel1.Children.Add(currentItemLevel2);
                    }

                    if (string.IsNullOrWhiteSpace(title3) == false) //level 3
                    {
                        currentItemLevel3 = new OmniclassMasterformatParseItem(code, title3.Trim());
                        currentItemLevel2.Children.Add(currentItemLevel3);
                    }

                    if (string.IsNullOrWhiteSpace(title4) == false) //level 4
                    {
                        currentItemLevel4 = new OmniclassMasterformatParseItem(code, title4.Trim());
                        currentItemLevel3.Children.Add(currentItemLevel4);
                    }
                }
            }

            return new ParseOmniclassMasterformatResult(items, errors);
        }

        public ParseCompaniesResult ParseCompanyResult(BIMsmithMarket.Domain.DBModels.File excelFile)
        {
            ParseCompaniesResult parseCompaniesResult = new ParseCompaniesResult();

            string filePath = Path.Combine(_tempPath, excelFile.Id.ToString() + Path.GetExtension(excelFile.FileName));
            SpreadsheetDocument document = null;
            try
            {
                document = SpreadsheetDocument.Open(filePath, false);
            }
            catch (Exception ex)
            {
                parseCompaniesResult.Errors.Add(ex.Message);
                return parseCompaniesResult;
            }
            var workbookPart = document.WorkbookPart;
            var workbook = workbookPart.Workbook;
            var sheets = workbook.Descendants<Sheet>();

            string error = null;

            //A - Company name
            //B - Company Email domain
            //C - Update user email


            var worksheetPart = (WorksheetPart)workbookPart.GetPartById(sheets.First().Id);
            var sharedStringPart = workbookPart.SharedStringTablePart;
            var sheetData = worksheetPart.Worksheet.GetFirstChild<SheetData>();
            var sharedStringTable = workbookPart.GetPartsOfType<SharedStringTablePart>().FirstOrDefault();

            var rows = worksheetPart.Worksheet.Descendants<Row>().ToList();

            foreach (var row in rows.Skip(1))
            {
                var сompanyName = this.GetCellStringValue(row, "A", sharedStringPart, out error);

                if (string.IsNullOrEmpty(сompanyName))
                {
                    parseCompaniesResult.Errors.Add($"Empty Company Name on row {row.RowIndex}");
                    return parseCompaniesResult;
                }

                сompanyName = сompanyName.Trim();

                var emailDomain = this.GetCellStringValue(row, "B", sharedStringPart, out error);
                var matchCompanyUsersInt = this.GetCellIntValue(row, "C", out error);
                bool matchCompanyUsers = true;

                if (string.IsNullOrWhiteSpace(emailDomain))
                {
                    parseCompaniesResult.Errors.Add($"Empty Email Domain on row {row.RowIndex}");
                    return parseCompaniesResult;
                }

                emailDomain = emailDomain.Trim();

                if (matchCompanyUsersInt < 0 || matchCompanyUsersInt > 1)
                {
                    parseCompaniesResult.Errors.Add($"Invalid Update User Email value on row {row.RowIndex}");
                    return parseCompaniesResult;
                }
                matchCompanyUsers = matchCompanyUsersInt == 0 ? false : true;

                ParseCompaniesResult.ParseCompanyData parseCompanyData = new ParseCompaniesResult.ParseCompanyData()
                {
                    CompanyName = сompanyName,
                    EmailDomain = emailDomain,
                    MatchCompanyUsers = matchCompanyUsers
                };
                parseCompaniesResult.Items.Add(parseCompanyData);
            }

            return parseCompaniesResult;
        }



        public ParseOmniclassMasterformatResult ParseOmniclass(BIMsmithMarket.Domain.DBModels.File excelFile)
        {
            string filePath = Path.Combine(_tempPath, excelFile.Id.ToString() + Path.GetExtension(excelFile.FileName));

            var document = SpreadsheetDocument.Open(filePath, false);
            var workbookPart = document.WorkbookPart;
            var workbook = workbookPart.Workbook;
            var sheets = workbook.Descendants<Sheet>();

            string error = null;
            var errors = new List<string>();
            var items = new List<OmniclassMasterformatParseItem>();


            //A - Code
            //B - Title 1
            //C - Title 2
            //D - Title 3
            //E - Title 4

            var worksheetPart = (WorksheetPart)workbookPart.GetPartById(sheets.First().Id);
            var sharedStringPart = workbookPart.SharedStringTablePart;
            var sheetData = worksheetPart.Worksheet.GetFirstChild<SheetData>();
            var sharedStringTable = workbookPart.GetPartsOfType<SharedStringTablePart>().FirstOrDefault();

            var rows = worksheetPart.Worksheet.Descendants<Row>().ToList();


            OmniclassMasterformatParseItem currentItemLevel1 = null;
            OmniclassMasterformatParseItem currentItemLevel2 = null;
            OmniclassMasterformatParseItem currentItemLevel3 = null;
            OmniclassMasterformatParseItem currentItemLevel4 = null;

            //Prepare Items
            foreach (var row in rows.Skip(2))
            {
                var code = this.GetCellStringValue(row, "A", sharedStringPart, out error);

                if (!string.IsNullOrEmpty(code))
                {
                    code = code.Trim();

                    var title1 = this.GetCellStringValue(row, "B", sharedStringPart, out error);
                    var title2 = this.GetCellStringValue(row, "C", sharedStringPart, out error);
                    var title3 = this.GetCellStringValue(row, "D", sharedStringPart, out error);
                    var title4 = this.GetCellStringValue(row, "E", sharedStringPart, out error);

                    if (string.IsNullOrWhiteSpace(title1) == false) //Root
                    {
                        currentItemLevel1 = new OmniclassMasterformatParseItem(code, title1.Trim());
                        items.Add(currentItemLevel1);
                    }

                    if (string.IsNullOrWhiteSpace(title2) == false) //level 2
                    {
                        currentItemLevel2 = new OmniclassMasterformatParseItem(code, title2.Trim());
                        currentItemLevel1.Children.Add(currentItemLevel2);
                    }

                    if (string.IsNullOrWhiteSpace(title3) == false) //level 3
                    {
                        currentItemLevel3 = new OmniclassMasterformatParseItem(code, title3.Trim());
                        currentItemLevel2.Children.Add(currentItemLevel3);
                    }

                    if (string.IsNullOrWhiteSpace(title4) == false) //level 4
                    {
                        currentItemLevel4 = new OmniclassMasterformatParseItem(code, title4.Trim());
                        currentItemLevel3.Children.Add(currentItemLevel4);
                    }
                }
            }

            return new ParseOmniclassMasterformatResult(items, errors);
        }

        public ParseUnitsResult ParseUnits(Domain.DBModels.File excelFile)
        {
            string filePath = Path.Combine(_tempPath, excelFile.Id.ToString() + Path.GetExtension(excelFile.FileName));

            var document = SpreadsheetDocument.Open(filePath, false);
            var workbookPart = document.WorkbookPart;
            var workbook = workbookPart.Workbook;
            var sheets = workbook.Descendants<Sheet>();

            string error = null;
            var errors = new List<string>();
            var items = new List<UnitParseItem>();


            //A - Version
            //B - Group Name
            //C - Display
            //D - A_Unit_Name
            //E - B_Unit_Name
            //F - Relation

            var worksheetPart = (WorksheetPart)workbookPart.GetPartById(sheets.First().Id);
            var sharedStringPart = workbookPart.SharedStringTablePart;
            var sheetData = worksheetPart.Worksheet.GetFirstChild<SheetData>();
            var sharedStringTable = workbookPart.GetPartsOfType<SharedStringTablePart>().FirstOrDefault();

            var rows = worksheetPart.Worksheet.Descendants<Row>().ToList();


            //Prepare Items
            foreach (var row in rows.Skip(1)) // skip titles
            {
                var version = this.GetCellStringValue(row, "A", sharedStringPart, out error);
                var groupName = this.GetCellStringValue(row, "B", sharedStringPart, out error);
                var display = this.GetCellStringValue(row, "C", sharedStringPart, out error);
                var A_Unit_Name = this.GetCellStringValue(row, "D", sharedStringPart, out error);
                var B_Unit_Name = this.GetCellStringValue(row, "E", sharedStringPart, out error);
                var relation = this.GetCellStringValue(row, "F", sharedStringPart, out error);

                if (!string.IsNullOrEmpty(groupName))
                {
                    items.Add(new UnitParseItem
                    {
                        Version = version.Trim(),
                        GroupName = groupName.Trim(),
                        Display = display.Trim(),
                        AName = A_Unit_Name.Trim(),
                        BName = B_Unit_Name.Trim(),
                        Relation = relation.Trim(),
                    });
                }
            }

            return new ParseUnitsResult(items, errors);
        }


        public ParseUnitsResult ParseUnits2(BIMsmithMarket.Domain.DBModels.File excelFile)
        {
            string filePath = Path.Combine(_tempPath, excelFile.Id.ToString() + Path.GetExtension(excelFile.FileName));

            var document = SpreadsheetDocument.Open(filePath, false);
            var workbookPart = document.WorkbookPart;
            var workbook = workbookPart.Workbook;
            var sheets = workbook.Descendants<Sheet>().ToList();

            string error = null;
            var errors = new List<string>();
            var items = new List<UnitParseItem>();


            //B - Group Name
            //С - B_Unit_Name

            var worksheetPart = (WorksheetPart)workbookPart.GetPartById(sheets[1].Id);
            var sharedStringPart = workbookPart.SharedStringTablePart;
            var sheetData = worksheetPart.Worksheet.GetFirstChild<SheetData>();
            var sharedStringTable = workbookPart.GetPartsOfType<SharedStringTablePart>().FirstOrDefault();

            var rows = worksheetPart.Worksheet.Descendants<Row>().ToList();


            //Prepare Items
            foreach (var row in rows.Skip(1)) // skip titles
            {

                var groupName = this.GetCellStringValue(row, "B", sharedStringPart, out error);
                var B_Unit_Name = this.GetCellStringValue(row, "C", sharedStringPart, out error);

                if (!string.IsNullOrEmpty(groupName))
                {
                    items.Add(new UnitParseItem
                    {
                        Version = null,
                        GroupName = groupName.Trim(),
                        Display = null,
                        AName = null,
                        BName = B_Unit_Name.Trim(),
                        Relation = null,
                    });
                }
            }

            return new ParseUnitsResult(items, errors);
        }


        protected string GetCellStringValue(Row row, string cellIndex, SharedStringTablePart sharedStringTable, out string error)
        {
            string result = null;

            error = null;

            string cellReference = string.Format("{0}{1}", cellIndex, row.RowIndex);

            foreach (Cell c in row.Elements<Cell>())
            {
                if (c.CellReference == cellReference)
                {
                    if (c.DataType != null)
                    {
                        if (c.DataType.Value == CellValues.SharedString)
                        {
                            result = GetSharedString(c.InnerText, sharedStringTable);
                        }
                        else
                        {
                            result = c.InnerText;
                        }
                    }
                    else
                    {
                        result = c.InnerText;
                    }

                    break;
                }
            }

            return result;
        }

        protected string GetCellStringValue(Cell c, SharedStringTablePart sharedStringTable, out string error)
        {
            string result = null;

            error = null;

            if (c.DataType != null)
            {
                if (c.DataType.Value == CellValues.Number || c.DataType.Value == CellValues.String)
                {
                    result = c.InnerText;
                }
                else if (c.DataType.Value == CellValues.SharedString)
                {
                    result = GetSharedString(c.InnerText, sharedStringTable);
                }
            }

            return result;
        }

        protected int GetCellIntValue(Row row, string cellIndex, out string error)
        {
            string result = null;

            error = null;

            string cellReference = string.Format("{0}{1}", cellIndex, row.RowIndex);

            foreach (Cell c in row.Elements<Cell>())
            {
                if (c.CellReference == cellReference)
                {
                    if (c.DataType != null)
                    {
                        if (c.DataType.Value == CellValues.Number || c.DataType.Value == CellValues.String)
                        {
                            result = c.InnerText;
                        }
                    }
                    else if (c.InnerText != null)
                    {
                        result = c.InnerText;
                    }
                    break;
                }
            }

            int intResult;
            if (!int.TryParse(result, out intResult))
            {
                error = string.Format("Invalid Data in cell {0}***", cellReference);
            }

            return intResult;
        }

        protected decimal GetCellDecimalValue(Row row, string cellIndex, out string error)
        {
            string result = null;

            error = null;

            string cellReference = string.Format("{0}{1}", cellIndex, row.RowIndex);

            foreach (Cell c in row.Elements<Cell>())
            {
                if (c.CellReference == cellReference)
                {
                    if (c.DataType != null)
                    {
                        if (c.DataType.Value == CellValues.Number)
                        {
                            result = c.InnerText;
                        }
                    }
                    else if (c.InnerText != null)
                    {
                        result = c.InnerText;
                    }
                    break;
                }
            }

            decimal decResult;
            if (!Decimal.TryParse(result, NumberStyles.AllowDecimalPoint, CultureInfo.InvariantCulture, out decResult))
            {
                error = string.Format("Invalid Data in cell {0}***", cellReference);
            }

            return Math.Round(decResult, 2);
        }

        protected string GetSharedString(string value, SharedStringTablePart sharedStringTable)
        {
            string result = null;
            if (sharedStringTable != null)
            {
                result = sharedStringTable.SharedStringTable.ElementAt(int.Parse(value)).InnerText;
            }

            return result;
        }
    }

    public class ParseOmniclassMasterformatResult
    {
        public ParseOmniclassMasterformatResult(IList<OmniclassMasterformatParseItem> items, IList<string> errors)
        {
            this.Items = items;
            this.Errors = errors;
        }

        public IList<string> Errors { get; private set; }

        public IList<OmniclassMasterformatParseItem> Items { get; private set; }
    }

    public class OmniclassMasterformatParseItem
    {
        public OmniclassMasterformatParseItem(string code, string title)
        {
            this.Code = code;
            this.Title = title;
            this.Children = new List<OmniclassMasterformatParseItem>();
        }

        public string Code { get; internal set; }

        public string Title { get; set; }

        public List<OmniclassMasterformatParseItem> Children { get; set; }
    }


    public class ParseUnitsResult
    {
        public ParseUnitsResult(IList<UnitParseItem> items, IList<string> errors)
        {
            this.Items = items;
            this.Errors = errors;
        }

        public IList<string> Errors { get; private set; }

        public IList<UnitParseItem> Items { get; private set; }
    }

    public class ParseCompaniesResult
    {
        public class ParseCompanyData
        {
            public string EmailDomain { get; set; }
            public string CompanyName { get; set; }
            public bool MatchCompanyUsers { get; set; }
        }
        public IList<string> Errors { get; private set; }
        public IList<ParseCompanyData> Items { get; private set; }

        public ParseCompaniesResult()
        {
            Errors = new List<string>();
            Items = new List<ParseCompanyData>();
        }
    }


    public class UnitParseItem
    {
        public string Version { get; set; }

        public string GroupName { get; set; }

        public string Display { get; set; }

        public string AName { get; set; }

        public string BName { get; set; }

        public string Relation { get; set; }
    }


}