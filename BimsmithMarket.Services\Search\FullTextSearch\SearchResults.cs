﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System;
using System.Collections.Generic;
using System.Linq;

namespace BIMsmithMarket.Services.Search.FullTextSearch
{
    public class TokenResult
    {
        public Dictionary<int, SearchAlgorithm.FTSSearchResult> Results { get; set; } = new Dictionary<int, SearchAlgorithm.FTSSearchResult>();
        public QueryToken Token { get; set; }
    }

    public class ProductExplain
    {
        public int Position { get; set; }
        public string Product { get; set; }
        public int ProductId { get; set; }
        public int Rank { get; set; }
        public decimal Score { get; set; }
        public string Explain { get; set; } = string.Empty;
    }

    public class SearchResults
    {
        public RankedResults.ProductRank Rank(int id)
        {
            return RankedResults != null ? RankedResults.Get(id) : null;
        }

        public List<string> SearchExplain { get; } = new List<string>();
        public RankedResults RankedResults { get; set; }

        // criteria found in the query
        public List<int> FoundOmniclasses { get; } = new List<int>(); // omniclass codes found
        public List<int> FoundMasterformats { get; } = new List<int>(); // masterformat codes found
        public List<int> FoundProjectTypes { get; } = new List<int>(); // project types found
        public List<int> FoundModels { get; } = new List<int>(); // model numbers found
        public Dictionary<int, float> FoundCategories { get; } = new Dictionary<int, float>(); // categories found
        public Dictionary<int, float> FoundManufacturers { get; } = new Dictionary<int, float>(); // manufacturers found

        // non-FTS results, scored by confidence level (0..1)
        public List<int> ProductNamePartials { get; } = new List<int>(); // 'contains' name matches
        public List<int> ProductNameMatches { get; } = new List<int>(); // exact name matches
        public List<int> ProductLineNameMatches { get; } = new List<int>(); // exact name matches
        public List<int> ManufacturerNameMatches { get; } = new List<int>(); // exact name matches
        public List<int> UrlMatches { get; } = new List<int>(); // exact matches
        public Dictionary<int, float> UnitMatches { get; } = new Dictionary<int, float>(); // units found

        // FTS results, scored by weights (0..inf)
        public Dictionary<int, SearchAlgorithm.FTSSearchResult> PhraseResults { get; set; } = new Dictionary<int, SearchAlgorithm.FTSSearchResult>(); // scores looking for full phrase
        public List<TokenResult> TokenResults { get; set; } = new List<TokenResult>(); // scores looking for each token
        public int FTSTokenCount { get; set; } = 0;
        public Dictionary<int, SearchAlgorithm.FTSSearchResult> AllTokenScoring { get; set; } = new Dictionary<int, SearchAlgorithm.FTSSearchResult>(); // average score on all tokens
        public List<QueryToken> SimplePhrase { get; set; }
        public List<QueryToken> AllTokenPhrase { get; set; }

        // parameters
        public ProductSearchOptions Options { get; }
        public IQueryable<Product> AllResults { get; private set; }
        public List<QueryToken> Tokens { get; }
        public string OriginalQuery { get; }
        public string Query { get; }

        public bool HasQuery => !string.IsNullOrWhiteSpace(Query);
        public bool HasClassicResults { get; set; }

        private SearchCache _searchCache;

        public SearchResults(
            ProductSearch.SearchAlgorithm algo,
            ProductSearchOptions options,
            IQueryable<Product> input,
            SearchCache searchCache,
            IUnitOfWork unitOfWork)
        {
            AllResults = ProductSearch.ApplyCommonFilters(options, input, unitOfWork); // this can modify options

            options.Normalize();
            OriginalQuery = options.Query;
            Query = GetNormalizedQuery(options);
            Tokens = SplitIntoTokens(Query);
            Options = options;
            _searchCache = searchCache;

            Explain(() => $"Using algorithm version {algo}");
        }

        public static void Merge(Dictionary<int, float> baseValues, Dictionary<int, float> newValues)
        {
            foreach (var v in newValues)
            {
                if (!baseValues.ContainsKey(v.Key))
                {
                    baseValues.Add(v.Key, v.Value);
                }
                else
                {
                    // take higher confidence
                    baseValues[v.Key] = Math.Max(baseValues[v.Key], v.Value);
                }
            }
        }

        public static void Merge(Dictionary<int, SearchAlgorithm.FTSSearchResult> baseValues, Dictionary<int, SearchAlgorithm.FTSSearchResult> newValues)
        {
            foreach (var v in newValues)
            {
                if (!baseValues.ContainsKey(v.Key))
                {
                    baseValues.Add(v.Key, v.Value);
                }
                else if (v.Value.Score > baseValues[v.Key].Score)
                {
                    // take higher confidence
                    baseValues[v.Key] = v.Value;
                }
            }
        }

        public void Explain(Func<SearchCache, string> messageFunc)
        {
            // do not waste time on production queries
            if (!Options.GenerateExplain)
                return;

            try
            {
                var message = messageFunc(_searchCache);
                if (!string.IsNullOrWhiteSpace(message))
                    SearchExplain.Add(message);
            }
            catch (Exception)
            {
                // ignore explain exceptions
            }
        }

        public void Explain(Func<string> messageFunc)
        {
            Explain(_ => messageFunc());
        }

        public void ExplainSummary()
        {
            // do not waste time on production queries
            if (!Options.GenerateExplain)
                return;

            SearchExplain.AddRange(GenerateSummary(_searchCache));
        }

        public void NarrowDown(IQueryable<Product> allResults)
        {
            AllResults = allResults;
        }

        public void NarrowDown(System.Linq.Expressions.Expression<Func<Product, bool>> criteria)
        {
            AllResults = AllResults.Where(criteria);
        }

        public static List<QueryToken> SplitIntoTokens(string searchQuery)
        {
            int n = 0;
            return searchQuery?.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries).Select(s => new QueryToken(s, n++, QueryTokenType.Unknown)).ToList() ?? new List<QueryToken>();
        }

        public static string GetNormalizedQuery(ProductSearchOptions options)
        {
            var searchQuery = (options.Query ?? string.Empty).Trim().ToLower();
            var keywordQuery = (options.Keyword ?? string.Empty).Trim().ToLower();

            // ignore single character query
            if (searchQuery.Length < 2)
            {
                searchQuery = string.Empty;
            }
            if (keywordQuery.Length < 2)
            {
                keywordQuery = string.Empty;
            }

            if (string.IsNullOrWhiteSpace(searchQuery) && !string.IsNullOrWhiteSpace(keywordQuery))
            {
                searchQuery = keywordQuery;
            }

            while (searchQuery.Contains("  "))
            {
                searchQuery = searchQuery.Replace("  ", " ");
            }

            return searchQuery;
        }

        protected IEnumerable<string> GenerateSummary(SearchCache cache)
        {
            foreach (var token in Tokens)
            {
                yield return string.Format("Token '{0}' (corrected = '{1}', FTS = '{2}') categorised as {3}; has results = {4}", token.Original, token.Corrected, token.FTS, token.Type, token.HasResults);
            }
            /*if (FoundMasterformats.Any())
            {
                yield return string.Format("Masterformat Ids = {0}", string.Join(",", FoundMasterformats));
            }
            if (FoundOmniclasses.Any())
            {
                yield return string.Format("Omniclass Ids = {0}", string.Join(",", FoundOmniclasses));
            }
            if (FoundCategories.Any())
            {
                yield return string.Format("Categories = {0}", string.Join(",", FoundCategories.Select(c => string.Format("{0} ({1})", cache.Categories[c.Key].Name, c.Value.ToString("N2")))));
            }
            if (FoundManufacturers.Any())
            {
                yield return string.Format("Manufacturers = {0}", string.Join(",", FoundManufacturers.Select(c => string.Format("{0} ({1})", cache.Manufacturers[c.Key].Name, c.Value))));
            }
            if (ProductNameMatches.Any())
            {
                yield return string.Format("Top Product Name Match Products = {0}", string.Join(",", ProductNameMatches.Take(10)));
            }
            if (ProductLineNameMatches.Any())
            {
                yield return string.Format("Top Product Line Name Match Products = {0}", string.Join(",", ProductLineNameMatches.Take(10)));
            }
            if (ManufacturerNameMatches.Any())
            {
                yield return string.Format("Top Manufacturer Name Match Products = {0}", string.Join(",", ManufacturerNameMatches.Take(10)));
            }
            if (UrlMatches.Any())
            {
                yield return string.Format("Top Url Match Products = {0}", string.Join(",", UrlMatches.Take(10)));
            }
            if (UnitMatches.Any())
            {
                yield return string.Format("Top Unit Match Products = {0}", string.Join(",", UnitMatches.Take(10).Select(c => string.Format("{0} ({1})", c.Key, c.Value))));
            }
            if (PhraseResults.Any())
            {
                yield return string.Format("Phrase Results count = {0}", PhraseResults.Count);
            }
            if (TokenResults.Any())
            {
                yield return string.Format("Token Results count = {0}", PhraseResults.Count);
            }
            if (AllTokenScoring.Any())
            {
                yield return string.Format("AllTokenScoring Products count = {0}", AllTokenScoring.Count);
            }*/
        }
    }
}
