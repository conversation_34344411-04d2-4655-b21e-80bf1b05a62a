﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Domain.Dto;
using Microsoft.Extensions.Caching.StackExchangeRedis;
using Microsoft.Extensions.Options;
using StackExchange.Redis;
using System.Data.Common;

namespace BIMsmithMarket.Core.Providers
{
    public class RedisCacheProvider
    {
        private static RedisCache _redisCache;
        private static readonly RedisOptions _redisOptions;

        static RedisCacheProvider()
        {
            var connectionString = ConfigurationHelper.GetValue("CacheConnection");
            var connectionStringBuilder = new DbConnectionStringBuilder
            {
                ConnectionString = connectionString
            };

            _redisOptions = new RedisOptions
            {
                AllowAdmin = bool.Parse((string)connectionStringBuilder["allowAdmin"]),
                DefaultDatabase = int.Parse((string)connectionStringBuilder["defaultDatabase"]),
                Host = (string)connectionStringBuilder["host"],
                Instance = (string)connectionStringBuilder["instance"],
                Port = int.Parse((string)connectionStringBuilder["port"])
            };
        }

        public static RedisCache GetRedisCacheInstance()
        {
            if (_redisCache != null)
                return _redisCache;

            var options = new ConfigurationOptions()
            {
                EndPoints = { $"{_redisOptions.Host}:{_redisOptions.Port}" },
                DefaultDatabase = _redisOptions.DefaultDatabase
            };

            var config = Options.Create(new RedisCacheOptions()
            {
                InstanceName = _redisOptions.Instance,
                ConfigurationOptions = options
            });

            _redisCache = new RedisCache(config);

            return _redisCache;
        }

        public static RedisOptions GetRedisOptions()
        {
            return _redisOptions;
        }
    }
}