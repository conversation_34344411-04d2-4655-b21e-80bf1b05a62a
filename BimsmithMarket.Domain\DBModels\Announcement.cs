﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.DBModels
{
    public class Announcement : BaseEntity
    {
        public string Text { get; set; }

        public string IconUrl { get; set; }

        public bool IsActive { get; set; }

        public string Link { get; set; }

        [StringLength(1000)]
        public string LinkCaption { get; set; }

        public bool IsGlobal { get; set; }

        public bool ShowLink { get; set; }

        public bool OverrideColor { get; set; }

        public string BannerColor { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        public virtual ICollection<ManufacturerAnnouncement> ManufacturerAnnouncements { get; set; }

        public Announcement()
        {
            ManufacturerAnnouncements = new List<ManufacturerAnnouncement>();
        }
    }
}