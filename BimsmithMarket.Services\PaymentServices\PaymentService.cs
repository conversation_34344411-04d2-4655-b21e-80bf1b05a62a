﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.PaymentsDto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Stripe;
using Stripe.Checkout;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.PaymentServices
{
    public class PaymentService : IPaymentService
    {
        private readonly IUserPaidProductService _userPaidProductService;
        private readonly IProductPriceService _productPriceService;
        private readonly string _successUrl;
        private readonly string _cancelUrl;
        private readonly string _apiKey;
        private readonly IStripeClient _client;

        public PaymentService(
            string apiKey,
            string successUrl,
            string cancelUrl,
            IProductPriceService productPriceService,
            IUserPaidProductService userPaidProductService)
        {
            _apiKey = apiKey;
            _successUrl = successUrl;
            _cancelUrl = cancelUrl;
            _client = new StripeClient(apiKey);
            _productPriceService = productPriceService;
            _userPaidProductService = userPaidProductService;
        }

        public async Task<UserPaidProductDto> CreateCheckoutSessionAsync(
            int productId,
            string userEmail,
            string userId,
            string serverFolder,
            string apiUrl,
            IUnitOfWork unitOfWork)
        {
            if (_client == null || _apiKey == null)
                throw new NoRecommendationsException("Need to use full constructor");

            StripeConfiguration.ApiKey = _apiKey;

            var productToPay = await _productPriceService.GetCurrentProductPriceAsync(productId, unitOfWork);
            var orderImages = new List<string> { productToPay.Image };
            var stripeImages = await ImageHelper.GetStripeImagesAsync(orderImages, serverFolder, apiUrl, productId);

            var description = !string.IsNullOrWhiteSpace(productToPay.Description) ? (productToPay.Description.Length > 500 ? productToPay.Description.Substring(0, 499) : productToPay.Description) : "Description";
            var options = new SessionCreateOptions
            {
                PaymentMethodTypes = new List<string>
                {
                    "card",
                },
                LineItems = new List<SessionLineItemOptions>
                {
                  new SessionLineItemOptions
                  {
                    PriceData = new SessionLineItemPriceDataOptions
                    {
                      UnitAmount = productToPay.UnitAmount,
                      Currency = "usd",
                      ProductData = new SessionLineItemPriceDataProductDataOptions
                      {
                        Name = $"Product: {productToPay.Name}. Thank you for using BIMsmith Market",
                        Description = description,
                        Images = string.IsNullOrEmpty(stripeImages) ? null : new List<string>{ stripeImages }
                      },
                    },
                    Quantity = 1,
                  },
                },
                Mode = "payment",
                SuccessUrl = _successUrl,
                CancelUrl = _cancelUrl,
                CustomerEmail = userEmail,
                PaymentIntentData = new SessionPaymentIntentDataOptions
                {
                    Metadata = new Dictionary<string, string>
                    {
                        {"Description", description }
                    }
                }
            };

            var service = new SessionService(_client);
            Session session = service.Create(options);

            var productModel = new UserPaidProductDto
            {
                PaidProductId = productId,
                PriceType = PriceType.Premium,
                StripeSessionMark = DateTime.UtcNow,
                StripeSessionStatus = session.PaymentStatus,
                StripeSessionId = session.Id,
                UserId = userId,
            };
            return await _userPaidProductService.AddEditProduct(productModel, unitOfWork);
        }

        public async Task StripeConfirmation(
            string sessionId,
            IUnitOfWork unitOfWork)
        {
            if (_client == null || _apiKey == null)
                throw new NoRecommendationsException("Need to use full constructor");

            var sessionService = new SessionService(_client);
            Session session = sessionService.Get(sessionId);

            await _userPaidProductService.ChangeStatus(sessionId, session.PaymentStatus, unitOfWork);

            if (session.PaymentStatus != "paid")
                throw new PaymentConfirmationException("Strite session duration ended");
        }

        public async Task StripeCancelation(
            string sessionId,
            IUnitOfWork unitOfWork)
        {
            if (_client == null || _apiKey == null)
                throw new NoRecommendationsException("Need to use full constructor");
            var sessionService = new SessionService(_client);
            Session session = sessionService.Get(sessionId);

            await _userPaidProductService.ChangeStatus(sessionId, session.PaymentStatus, unitOfWork);
        }

        public async Task<OperationResultDto> EditUserSubscriptionAsync(
            SetUserSubscriptionDto model,
            string userId,
            IUnitOfWork unitOfWork)
        {
            ApplicationUser user = await unitOfWork.UserRepository.GetAll().FirstOrDefaultAsync(x => x.Id == userId);

            if (user == null)
                throw new InvalidInputException($"User with id {userId} not found");

            user.Subscribed = model.Subscribed;
            unitOfWork.UserRepository.Edit(user);
            await unitOfWork.SaveAsync();

            return new OperationResultDto
            {
                Status = OperationResultStatus.Succeed
            };
        }

        /// <summary>
        ///what exactly file paid or not
        /// </summary>
        /// <param name="productFileIds">product file Ids</param>
        /// <param name="unitOfWork"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<List<CheckPaidFilesResultDto>> CheckSelectedFilesAsync(
            int[] productFileIds,
            IUnitOfWork unitOfWork,
            string userId = null,
            int? productId = null)
        {
            var productFilesQuery = unitOfWork.ProductFileRepository.GetAll().Where(x => productFileIds.Contains(x.FileId));

            if (productId.HasValue)
            {
                productFilesQuery = productFilesQuery.Where(x => x.ProductId == productId.Value);
            }

            var productFiles = await productFilesQuery.ToListAsync();
            var notFreeProductFiles = productFiles.Where(x => !x.IsAttachment && x.Product.Price?.PriceType != PriceType.Free).ToList();
            //if there is no pay product (all are free)
            if (!notFreeProductFiles.Any())
                return MapCheckSelectedFilesResult(productFiles, productFileIds);

            // check user id only on this step because possible downloads without login, but only for free files
            if (!productFileIds.Any() || string.IsNullOrWhiteSpace(userId))
                throw new InvalidInputException("Invalid UserId or file ids do not containe any Ids");

            var userHasSubscription = unitOfWork.UserRepository.GetAll().FirstOrDefault(x => x.Id == userId)?.Subscribed;
            if (userHasSubscription == null)
                throw new InvalidInputException($" User with id {userId} not found");

            if (userHasSubscription.Value)
                notFreeProductFiles = notFreeProductFiles.Where(x => x.Product.Price?.PriceType == PriceType.Premium).ToList();

            //Get only paid products
            List<UserPaidProductDto> userProducts = await _userPaidProductService.GetUserProductsAsync(userId, unitOfWork, false);
            var paidProductsIds = userProducts.Select(a => a.PaidProductId).ToArray();
            int[] paid = productFiles.Where(x => paidProductsIds.Contains(x.ProductId))
                                                                 .Select(x => x.FileId).ToArray();
            int[] free = productFiles.Where(x => x.Product.Price?.PriceType == PriceType.Free)
                                         .Select(x => x.FileId).ToArray();
            var unPaidProducts = notFreeProductFiles.Select(x => x.Product).Select(x => x.Id).Except(paidProductsIds).ToArray();
            int[] unPaid = productFiles.Where(x => unPaidProducts.Contains(x.ProductId)).Select(x => x.FileId).ToArray();

            return MapCheckSelectedFilesResult(productFiles, free, paid, unPaid);
        }


        #region Private Methods
        /// <summary>
        /// 
        /// </summary>
        /// <param name="productFiles"></param>
        /// <param name="free"> file id as input</param>
        /// <param name="paid"> file id as input</param>
        /// <param name="unpaid"> file id as input</param>
        /// <returns></returns>
        private List<CheckPaidFilesResultDto> MapCheckSelectedFilesResult(
            List<ProductFile> productFiles,
            int[] free = null,
            int[] paid = null,
            int[] unpaid = null)
        {
            var result = new List<CheckPaidFilesResultDto>();
            if (free != null)
                foreach (var id in free)
                {
                    result.Add(new CheckPaidFilesResultDto
                    {
                        FileId = id,
                        DownloadAvailable = true,
                        AvaliabilityDescription = "Avaliable to download",
                        PriceType = PriceType.Free
                    });
                }


            if (paid != null)
                foreach (var id in paid)
                {
                    result.Add(new CheckPaidFilesResultDto
                    {
                        FileId = id,
                        DownloadAvailable = true,
                        AvaliabilityDescription = "The product has been paid for",
                        PriceType = productFiles.First(pp => pp.FileId == id).Product.Price.PriceType
                    });
                }

            if (unpaid != null)
                foreach (var id in unpaid)
                {
                    result.Add(new CheckPaidFilesResultDto
                    {
                        FileId = id,
                        DownloadAvailable = false,
                        AvaliabilityDescription = "Anavaliable product? ",
                        PriceType = productFiles.First(pp => pp.FileId == id).Product.Price.PriceType
                    });
                }

            return result;
        }
        #endregion
    }
}
