﻿using Azure.Storage.Queues.Models;
using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Quartz;
using Serilog;
using System;
using System.Diagnostics;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Jobs
{
    [DisallowConcurrentExecution]
    public class UpdateThumbnailsJob : IJob
    {
        private readonly IManufacturerService _manufacturerService;
        private readonly IProductService _productService;
        private readonly string _jobName = "Update Thumbnails Job";
        private readonly IAzureStorageService _azureStorageService;

        public UpdateThumbnailsJob(
            IManufacturerService manufacturerService,
            IProductService productService,
            IAzureStorageService azureStorageService)
        {
            _manufacturerService = manufacturerService;
            _productService = productService;
            _azureStorageService = azureStorageService;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            Log.Information($"[{_jobName}] started");

            try
            {
                var updateThumnbnailsQueue = _azureStorageService.GetQueueByName(AzureStorageConstants.UpdateThumbnailsQueue);

                while (await _azureStorageService.GetMessageCountAsync(updateThumnbnailsQueue.Name) > 0)
                {
                    QueueMessage message = await updateThumnbnailsQueue.ReceiveMessageAsync();
                    if (message != null)
                    {
                        int manufacturerId = -1;
                        if (int.TryParse(message.MessageText, out manufacturerId))
                        {
                            try
                            {
                                Debug.WriteLine($"Start updating thumbnails for manufacturerId: {manufacturerId} at " + DateTime.UtcNow.ToString());
                                Log.Information($"[{_jobName}] Start updating thumbnails for manufacturerId: {manufacturerId} at " + DateTime.UtcNow.ToString());

                                await _manufacturerService.UpdateThumbnailAsync(manufacturerId);
                                using IUnitOfWork unitOfWork = UnitOfWork.Create();
                                await _productService.UpdateMongoProductsAsync(manufacturerId, unitOfWork);

                                Debug.WriteLine($"Passed for manufacturerId: {manufacturerId} at " + DateTime.UtcNow.ToString());
                                Log.Information($"[{_jobName}] Passed for manufacturerId: {manufacturerId} at " + DateTime.UtcNow.ToString());

                                await updateThumnbnailsQueue.DeleteMessageAsync(message.MessageId, message.PopReceipt);
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine(ex.Message);
                                Log.Error($"[{_jobName}] Error for manufacturer Id: {manufacturerId}", ex);
                                await updateThumnbnailsQueue.DeleteMessageAsync(message.MessageId, message.PopReceipt);
                            }
                        }
                        else
                        {
                            Log.Information($"[{_jobName}] Cannot read message");
                            await updateThumnbnailsQueue.DeleteMessageAsync(message.MessageId, message.PopReceipt);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Debug.WriteLine(e.Message);
                Log.Error($"[{_jobName}] {e.Message}", e);
            }
        }
    }
}