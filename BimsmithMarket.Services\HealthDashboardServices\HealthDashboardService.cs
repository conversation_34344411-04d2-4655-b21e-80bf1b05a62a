﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.HealthDashboardDto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.HealthDashboardServices
{
    public class HealthDashboardService : IHealthDashboardService
    {
        private int[] _revitTypes;

        private string[] _revitExtensions = [".rfa", ".rvt", ".adsklib", ".pdf", ".txt", ".jpg", ".png", ".zip", ".tif", ".tiff", ".bmp", ".hdr", ".exr", ".dib", ".pcx"];

        public async Task<HealthCheckStatus> CheckHealthForProductLineAsync(int productLineId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            List<bool> checks = new List<bool>();
            bool attachmentCheck = await CheckHealthForProductLineAttachmentLinkAsync(productLineId, unitOfWork, cancellationToken);
            checks.Add(attachmentCheck);
            return checks.All(x => x) ? HealthCheckStatus.Ok : HealthCheckStatus.Error;
        }

        public async Task<HealthCheckStatus> CheckHealthForManufacturerAsync(int manufacturerId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            List<bool> checks = new List<bool>();
            bool attachmentCheck = await CheckHealthForManufacturerAttachmentLinkAsync(manufacturerId, unitOfWork, cancellationToken);
            checks.Add(attachmentCheck);
            return checks.All(x => x) ? HealthCheckStatus.Ok : HealthCheckStatus.Error;
        }

        public async Task<HealthCheckStatus> CheckHealthForProductAsync(int productId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            List<bool> checks = new List<bool>();
            bool attachmentCheck = await CheckHealthForProductAttachmentLinkAsync(productId, unitOfWork, cancellationToken);
            checks.Add(attachmentCheck);
            return checks.All(x => x) ? HealthCheckStatus.Ok : HealthCheckStatus.Error;
        }

        public async Task<int> CalculateBrokenLinksAsync(ICollection<int> fileIds, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            int take = 10000;
            int fileIdsCount = fileIds.Count;
            int totalCount = 0;
            for (int i = 0; i < fileIdsCount / take + 1; i++)
            {
                cancellationToken.ThrowIfCancellationRequested();

                int[] fileIdsPart = fileIds.Skip(i * take).Take(take).ToArray();
                totalCount += await unitOfWork.FileRepository.GetAll()
                                              .CountAsync(x => fileIdsPart.Contains(x.Id)
                                                            && x.SyncStatusCode != (int)HttpStatusCode.OK
                                                            && x.SyncStatusCode != (int)HttpStatusCode.Accepted, cancellationToken);
            }

            cancellationToken.ThrowIfCancellationRequested();

            return totalCount;
        }

        public async Task<int[]> GetBrokenLinksFileIdsAsync(ICollection<int> fileIds, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            if (!fileIds.Any())
                return Array.Empty<int>();

            return await unitOfWork.FileRepository.GetAll()
                                   .Where(x => fileIds.Contains(x.Id)
                                            && x.SyncStatusCode != (int)HttpStatusCode.OK
                                            && x.SyncStatusCode != (int)HttpStatusCode.Accepted)
                                   .Select(x => x.Id)
                                   .ToArrayAsync(cancellationToken);
        }

        public async Task ManageDashboardAccessAsync(HealthDashboardManageAccessDto model, string creatorUserId)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                ApplicationUser user = await unitOfWork.UserRepository.GetAll()
                                                       .FirstOrDefaultAsync(x => x.Email == model.UserEmail);

                if (user == null)
                    throw new InvalidInputException($"User with email {user.Email} not found");

                var adminRoleId = (await unitOfWork.RoleRepository.GetAll().FirstOrDefaultAsync(x => x.Name == DbConstants.AdminRole))?.Id;
                if (!await unitOfWork.UserRoleRepository.GetAll().AnyAsync(x => x.RoleId == adminRoleId))
                    throw new InvalidInputException($"User with email {user.Email} does not have admin permissions");

                HealthDashboardAccess dbHealthDashboardAccess = await unitOfWork.HealthDashboardAccessRepository.GetAll()
                                                                                .FirstOrDefaultAsync(x => x.UserId == user.Id
                                                                                                       && x.Type == model.Type
                                                                                                       && x.EntityId == model.EntityId);

                if (model.ActionType == ManageActionType.Add)
                {
                    if (dbHealthDashboardAccess != null)
                        throw new InvalidInputException($"User with email {user.Email} already has this permission");

                    dbHealthDashboardAccess = model.Adapt<HealthDashboardAccess>();
                    dbHealthDashboardAccess.UserId = user.Id;
                    dbHealthDashboardAccess.CreatedById = creatorUserId;

                    unitOfWork.HealthDashboardAccessRepository.Insert(dbHealthDashboardAccess);
                }
                else if (model.ActionType == ManageActionType.Remove)
                {
                    if (dbHealthDashboardAccess == null)
                        throw new InvalidInputException($"User with email {user.Email} does not have this permission");

                    unitOfWork.HealthDashboardAccessRepository.Delete(dbHealthDashboardAccess);
                }

                await unitOfWork.SaveAsync();
            }
        }

        public async Task<ICollection<HealthDashboardAssignedUserListDto>> AssignedUserListAsync(HealthDashboardFilterDto model, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var assignedUserList = await unitOfWork.HealthDashboardAccessRepository.GetAll()
                                                       .Where(x => x.EntityId == model.EntityId
                                                                && x.Type == model.Type)
                                                       .Include(x => x.User)
                                                       .AsNoTracking()
                                                       .ToArrayAsync(cancellationToken);

                cancellationToken.ThrowIfCancellationRequested();

                return assignedUserList.Adapt<HealthDashboardAssignedUserListDto[]>();
            }
        }

        public async Task<HealthDashboardInfoDto> GetDashboardAsync(HealthDashboardFilterDto model, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                int recordsCount = 0;
                switch (model.DashboardMissingType)
                {
                    case HealthDashboardType.BrokenLinks:
                        recordsCount = await CalculateBrokenLinksCountAsync(model, unitOfWork, cancellationToken);
                        break;
                    case HealthDashboardType.BlankDescriptions:
                        recordsCount = await CalculateBlankDescriptionsAsync(model, unitOfWork, cancellationToken);
                        break;
                    case HealthDashboardType.NoRevitFiles:
                        recordsCount = await CalculateNoRevitFileAsync(model, unitOfWork, cancellationToken);
                        break;
                    case HealthDashboardType.RevitMissingExtension:
                        recordsCount = await CalculateRevitFileMissingExtension(model, unitOfWork, cancellationToken);
                        break;
                    case HealthDashboardType.BlankPhoneNumber:
                        recordsCount = await CalculateBlankPhoneNumbersAsync(model, unitOfWork, cancellationToken);
                        break;
                    case HealthDashboardType.ProductsWithZipFilesNumber:
                        recordsCount = await CalculateBrokenLinksCountAsync(model, unitOfWork, cancellationToken);
                        break;
                }

                cancellationToken.ThrowIfCancellationRequested();

                return new HealthDashboardInfoDto
                {
                    RecordsCount = recordsCount,
                    Type = model.Type,
                    EntityId = model.EntityId
                };
            }
        }

        public async Task<List<HealthDashboardInfoDto>> GetAllTypeDashboardAsync(HealthDashboardFilterDto model, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                switch (model.Type)
                {
                    case HealthDashboardAccessType.All:
                        return await GetAllMarketDashboardAsync(cancellationToken);
                    case HealthDashboardAccessType.Manufacturer:
                        return await GetAllTypeManufacturerDashboardAsync(model, unitOfWork, cancellationToken);
                    case HealthDashboardAccessType.ProductLine:
                        return await GetAllTypeProductLineDashboardAsync(model, unitOfWork, cancellationToken);
                    default:
                        return null;
                }

            }
        }

        public async Task<List<HealthDashboardInfoDto>> GetAllTypeManufacturerDashboardAsync(HealthDashboardFilterDto model, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            List<HealthDashboardInfoDto> result = new List<HealthDashboardInfoDto>();
            List<int> itemIds = new List<int>();
            itemIds.AddRange((await CollectFileIdsForManufacturerAsync(model.EntityId, unitOfWork, cancellationToken)).Select(x => x.EntityId));
            itemIds = itemIds.Distinct().ToList();
            int count = await CalculateBrokenLinksAsync(itemIds, unitOfWork, cancellationToken);
            result.Add(new HealthDashboardInfoDto()
            {
                DashboardMissingType = HealthDashboardType.BrokenLinks,
                RecordsCount = count,
                DashboardName = HealthDashboardConstants.TotalBrokenLinksDashboardCaption,
                EntityId = model.EntityId,
                Type = HealthDashboardAccessType.Manufacturer
            });

            cancellationToken.ThrowIfCancellationRequested();

            itemIds = new List<int>(GetFilteredEntityIds(model, await CollectBlankDescriptionProductIdsForManufacturerAsync(model.EntityId, unitOfWork, cancellationToken)));

            result.Add(new HealthDashboardInfoDto()
            {
                DashboardMissingType = HealthDashboardType.BlankDescriptions,
                RecordsCount = itemIds.Distinct().Count(),
                DashboardName = HealthDashboardConstants.TotalBlankDesriptionsDashboardCaption,
                EntityId = model.EntityId,
                Type = HealthDashboardAccessType.Manufacturer
            });

            cancellationToken.ThrowIfCancellationRequested();

            itemIds = new List<int>(GetFilteredEntityIds(model, await CollectNoRevitFileProductIdsForManufacturerAsync(model.EntityId, unitOfWork, cancellationToken)));

            result.Add(new HealthDashboardInfoDto()
            {
                DashboardMissingType = HealthDashboardType.NoRevitFiles,
                RecordsCount = itemIds.Distinct().Count(),
                DashboardName = HealthDashboardConstants.TotalNoRevitFilesDashboardCaption,
                EntityId = model.EntityId,
                Type = HealthDashboardAccessType.Manufacturer
            });

            cancellationToken.ThrowIfCancellationRequested();

            itemIds = new List<int>(GetFilteredEntityIds(model, await CollectRevitNoExtensionFileIdsForManufacturerAsync(model.EntityId, unitOfWork, cancellationToken)));

            result.Add(new HealthDashboardInfoDto()
            {
                DashboardMissingType = HealthDashboardType.RevitMissingExtension,
                RecordsCount = itemIds.Distinct().Count(),
                DashboardName = HealthDashboardConstants.TotalMissingExtensionsDashboardCaption,
                EntityId = model.EntityId,
                Type = HealthDashboardAccessType.Manufacturer
            });

            cancellationToken.ThrowIfCancellationRequested();

            int blankNumber = await CalculateBlankPhoneNumbersAsync(model, unitOfWork, cancellationToken);

            result.Add(new HealthDashboardInfoDto()
            {
                DashboardMissingType = HealthDashboardType.BlankPhoneNumber,
                RecordsCount = blankNumber,
                DashboardName = HealthDashboardConstants.TotalBlankPhoneNumberDashboardCaption,
                EntityId = model.EntityId,
                Type = HealthDashboardAccessType.Manufacturer
            });

            cancellationToken.ThrowIfCancellationRequested();

            int productsCountWithZipFiles = await CalculateProductsCountWithZipFilesAsync(model, unitOfWork, cancellationToken);

            result.Add(new HealthDashboardInfoDto()
            {
                DashboardMissingType = HealthDashboardType.ProductsWithZipFilesNumber,
                RecordsCount = productsCountWithZipFiles,
                DashboardName = HealthDashboardConstants.TotalNumberOfProductsWithZipFilesDashboardCaption,
                EntityId = model.EntityId,
                Type = HealthDashboardAccessType.Manufacturer
            });

            cancellationToken.ThrowIfCancellationRequested();

            return result;
        }

        public async Task<List<HealthDashboardInfoDto>> GetAllTypeProductLineDashboardAsync(HealthDashboardFilterDto model, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            List<HealthDashboardInfoDto> result = new List<HealthDashboardInfoDto>();
            List<int> itemIds = new List<int>();
            itemIds.AddRange((await CollectFileIdsForManufacturerAsync(model.EntityId, unitOfWork, cancellationToken)).Select(x => x.EntityId));
            itemIds = itemIds.Distinct().ToList();

            cancellationToken.ThrowIfCancellationRequested();

            int count = await CalculateBrokenLinksAsync(itemIds, unitOfWork, cancellationToken);
            result.Add(new HealthDashboardInfoDto()
            {
                DashboardMissingType = HealthDashboardType.BrokenLinks,
                RecordsCount = count,
                DashboardName = HealthDashboardConstants.TotalBrokenLinksDashboardCaption,
                EntityId = model.EntityId,
                Type = HealthDashboardAccessType.Manufacturer
            });

            cancellationToken.ThrowIfCancellationRequested();

            itemIds = new List<int>(GetFilteredEntityIds(model, await CollectBlankDescriptionProductIdsForManufacturerAsync(model.EntityId, unitOfWork, cancellationToken)));

            result.Add(new HealthDashboardInfoDto()
            {
                DashboardMissingType = HealthDashboardType.BlankDescriptions,
                RecordsCount = itemIds.Distinct().Count(),
                DashboardName = HealthDashboardConstants.TotalBlankDesriptionsDashboardCaption,
                EntityId = model.EntityId,
                Type = HealthDashboardAccessType.Manufacturer
            });

            cancellationToken.ThrowIfCancellationRequested();

            itemIds = new List<int>(GetFilteredEntityIds(model, await CollectNoRevitFileProductIdsForManufacturerAsync(model.EntityId, unitOfWork, cancellationToken)));

            result.Add(new HealthDashboardInfoDto()
            {
                DashboardMissingType = HealthDashboardType.NoRevitFiles,
                RecordsCount = itemIds.Distinct().Count(),
                DashboardName = HealthDashboardConstants.TotalNoRevitFilesDashboardCaption,
                EntityId = model.EntityId,
                Type = HealthDashboardAccessType.Manufacturer
            });

            cancellationToken.ThrowIfCancellationRequested();

            itemIds = new List<int>(GetFilteredEntityIds(model, await CollectRevitNoExtensionFileIdsForManufacturerAsync(model.EntityId, unitOfWork, cancellationToken)));

            result.Add(new HealthDashboardInfoDto()
            {
                DashboardMissingType = HealthDashboardType.RevitMissingExtension,
                RecordsCount = itemIds.Distinct().Count(),
                DashboardName = HealthDashboardConstants.TotalMissingExtensionsDashboardCaption,
                EntityId = model.EntityId,
                Type = HealthDashboardAccessType.Manufacturer
            });

            cancellationToken.ThrowIfCancellationRequested();

            return result;
        }

        public async Task<ICollection<HealthDashboardInfoDto>> GetUserDashboardsAsync(HealthDashboardFilterPeriod filterPeriod, string userId, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                HealthDashboardAccessDto[] userDashboardAccesses = await GetUserDashboardListAsync(userId, unitOfWork, cancellationToken);

                List<HealthDashboardInfoDto> dashboardsInfo = new List<HealthDashboardInfoDto>();

                foreach (var item in userDashboardAccesses)
                {
                    dashboardsInfo.AddRange(await GetHealthDashboardInfoAsync(item, filterPeriod, unitOfWork, cancellationToken));
                }

                cancellationToken.ThrowIfCancellationRequested();

                return dashboardsInfo;
            }
        }

        public async Task<List<HealthDashboardInfoDto>> GetUserDashboardAsync(HealthDashboardFilterDto model, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                List<HealthDashboardInfoDto> dashboardsInfo = await GetHealthDashboardInfoAsync(new HealthDashboardAccessDto { EntityId = model.EntityId, Type = model.Type }, model.FilterPeriod, unitOfWork, cancellationToken);

                cancellationToken.ThrowIfCancellationRequested();

                return dashboardsInfo;
            }
        }

        public async Task<List<HealthDashboardInfoDto>> GetAllMarketDashboardAsync(CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            List<HealthDashboardInfoDto> result = new List<HealthDashboardInfoDto>();
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                List<int> itemIds = new List<int>();
                int[] allManufacturerIds = await unitOfWork.ManufacturerRepository.GetAll().Select(x => x.Id).ToArrayAsync(cancellationToken);

                cancellationToken.ThrowIfCancellationRequested();

                foreach (int id in allManufacturerIds)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    itemIds.AddRange((await CollectFileIdsForManufacturerAsync(id, unitOfWork, cancellationToken)).Select(x => x.EntityId).ToList());
                }

                cancellationToken.ThrowIfCancellationRequested();

                itemIds = itemIds.Distinct().ToList();
                int count = await CalculateBrokenLinksAsync(itemIds, unitOfWork, cancellationToken);

                cancellationToken.ThrowIfCancellationRequested();

                result.Add(new HealthDashboardInfoDto()
                {
                    DashboardMissingType = HealthDashboardType.BrokenLinks,
                    RecordsCount = count,
                    DashboardName = HealthDashboardConstants.TotalBrokenLinksDashboardCaption,
                    Type = HealthDashboardAccessType.All
                });

                itemIds = new List<int>((await CollectBlankDescriptionProductIdsForAllManufacturersAsync(unitOfWork, cancellationToken)).Select(x => x.EntityId));

                cancellationToken.ThrowIfCancellationRequested();

                result.Add(new HealthDashboardInfoDto()
                {
                    DashboardMissingType = HealthDashboardType.BlankDescriptions,
                    RecordsCount = itemIds.Distinct().Count(),
                    DashboardName = HealthDashboardConstants.TotalBlankDesriptionsDashboardCaption,
                    Type = HealthDashboardAccessType.All
                });

                itemIds = new List<int>((await CollectNoRevitFileProductIdsForAllManufacturersAsync(unitOfWork, cancellationToken)).Select(x => x.EntityId));

                cancellationToken.ThrowIfCancellationRequested();

                result.Add(new HealthDashboardInfoDto()
                {
                    DashboardMissingType = HealthDashboardType.NoRevitFiles,
                    RecordsCount = itemIds.Distinct().Count(),
                    DashboardName = HealthDashboardConstants.TotalNoRevitFilesDashboardCaption,
                    Type = HealthDashboardAccessType.All
                });

                itemIds = new List<int>((await CollectRevitNoExtensionFileIdsForAllManufacturersAsync(unitOfWork, cancellationToken)).Select(x => x.EntityId));

                cancellationToken.ThrowIfCancellationRequested();

                result.Add(new HealthDashboardInfoDto()
                {
                    DashboardMissingType = HealthDashboardType.RevitMissingExtension,
                    RecordsCount = itemIds.Distinct().Count(),
                    DashboardName = HealthDashboardConstants.TotalMissingExtensionsDashboardCaption,
                    Type = HealthDashboardAccessType.All
                });

                itemIds = new List<int>(await GetBlankNumberForAllManufacturersAsync(unitOfWork, cancellationToken));

                cancellationToken.ThrowIfCancellationRequested();

                result.Add(new HealthDashboardInfoDto()
                {
                    DashboardMissingType = HealthDashboardType.RevitMissingExtension,
                    RecordsCount = itemIds.Distinct().Count(),
                    DashboardName = HealthDashboardConstants.TotalBlankPhoneNumberDashboardCaption,
                    Type = HealthDashboardAccessType.All
                });

                result.Add(new HealthDashboardInfoDto()
                {
                    DashboardMissingType = HealthDashboardType.ProductsWithZipFilesNumber,
                    RecordsCount = await GetProductsCountWithZipFilesForAllManufacturersAsync(unitOfWork, cancellationToken),
                    DashboardName = HealthDashboardConstants.TotalNumberOfProductsWithZipFilesDashboardCaption,
                    Type = HealthDashboardAccessType.All
                });

                cancellationToken.ThrowIfCancellationRequested();
            }

            cancellationToken.ThrowIfCancellationRequested();

            return result;
        }

        public async Task<int[]> GetFileIdsAsync(HealthDashboardFilterDto model, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            int[] fileIds = Array.Empty<int>();
            switch (model.Type)
            {
                case HealthDashboardAccessType.All:
                    {
                        fileIds = GetFilteredEntityIds(model, await CollectFileIdsForAllManufacturersAsync(unitOfWork, cancellationToken));
                        break;
                    }
                case HealthDashboardAccessType.Manufacturer:
                    {
                        fileIds = GetFilteredEntityIds(model, await CollectFileIdsForManufacturerAsync(model.EntityId, unitOfWork, cancellationToken));
                        break;
                    }
                case HealthDashboardAccessType.ProductLine:
                    {
                        fileIds = GetFilteredEntityIds(model, await CollectFileIdsForProductLineAsync(model.EntityId, unitOfWork, cancellationToken));
                        break;
                    }
            }

            cancellationToken.ThrowIfCancellationRequested();

            return fileIds;
        }

        public async Task<int[]> GetModifiedDateFileIdsAsync(HealthDashboardFilterDto model, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            int[] fileIds = Array.Empty<int>();
            switch (model.Type)
            {
                case HealthDashboardAccessType.All:
                    {
                        fileIds = GetFilteredEntityIds(model, await CollectFileIdsForAllManufacturersAsync(unitOfWork, cancellationToken));
                        break;
                    }
                case HealthDashboardAccessType.Manufacturer:
                    {
                        fileIds = GetFilteredEntityIds(model, await CollectFileIdsForManufacturerAsync(model.EntityId, unitOfWork, cancellationToken));
                        break;
                    }
                case HealthDashboardAccessType.ProductLine:
                    {
                        fileIds = GetFilteredEntityIds(model, await CollectFileIdsForProductLineAsync(model.EntityId, unitOfWork, cancellationToken));
                        break;
                    }
            }

            return fileIds;
        }

        public async Task<int[]> GetBlankDescriptionsProductIdsAsync(HealthDashboardFilterDto model, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            int[] productIds = Array.Empty<int>();
            switch (model.Type)
            {
                case HealthDashboardAccessType.All:
                    {
                        productIds = GetFilteredEntityIds(model, await CollectBlankDescriptionProductIdsForAllManufacturersAsync(unitOfWork, cancellationToken));
                        break;
                    }
                case HealthDashboardAccessType.Manufacturer:
                    {
                        productIds = GetFilteredEntityIds(model, await CollectBlankDescriptionProductIdsForManufacturerAsync(model.EntityId, unitOfWork, cancellationToken));
                        break;
                    }
                case HealthDashboardAccessType.ProductLine:
                    {
                        productIds = GetFilteredEntityIds(model, await CollectBlankDescriptionProductIdsForProductLineAsync(model.EntityId, unitOfWork, cancellationToken));
                        break;
                    }
            }

            return productIds;
        }

        public async Task<int[]> GetNoRevitFileProductIdsAsync(HealthDashboardFilterDto model, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            int[] productIds = Array.Empty<int>();
            switch (model.Type)
            {
                case HealthDashboardAccessType.All:
                    {
                        productIds = GetFilteredEntityIds(model, await CollectNoRevitFileProductIdsForAllManufacturersAsync(unitOfWork, cancellationToken));
                        break;
                    }
                case HealthDashboardAccessType.Manufacturer:
                    {
                        productIds = GetFilteredEntityIds(model, await CollectNoRevitFileProductIdsForManufacturerAsync(model.EntityId, unitOfWork, cancellationToken));
                        break;
                    }
                case HealthDashboardAccessType.ProductLine:
                    {
                        productIds = GetFilteredEntityIds(model, await CollectNoRevitFileProductIdsForProductLineAsync(model.EntityId, unitOfWork, cancellationToken));
                        break;
                    }
            }

            cancellationToken.ThrowIfCancellationRequested();

            return productIds;
        }

        public async Task<int[]> GetRevitFileNoExtensionFileIdsAsync(HealthDashboardFilterDto model, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            int[] productIds = Array.Empty<int>();
            switch (model.Type)
            {
                case HealthDashboardAccessType.All:
                    {
                        productIds = GetFilteredEntityIds(model, await CollectRevitNoExtensionFileIdsForAllManufacturersAsync(unitOfWork, cancellationToken));
                        break;
                    }
                case HealthDashboardAccessType.Manufacturer:
                    {
                        productIds = GetFilteredEntityIds(model, await CollectRevitNoExtensionFileIdsForManufacturerAsync(model.EntityId, unitOfWork, cancellationToken));
                        break;
                    }
                case HealthDashboardAccessType.ProductLine:
                    {
                        productIds = GetFilteredEntityIds(model, await CollectRevitNoExtensionFileIdsForProductLineAsync(model.EntityId, unitOfWork, cancellationToken));
                        break;
                    }
            }

            return productIds;
        }

        public async Task<int[]> GetBlankNumberForAllManufacturersAsync(IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            int[] manufacturers = await unitOfWork.ManufacturerRepository.GetAll().Where(x => string.IsNullOrEmpty(x.PhoneNumber)).Select(x => x.Id).ToArrayAsync(cancellationToken);
            return manufacturers;
        }

        public async Task<ICollection<HealthDashboardExportProductWithZipFilesDto>> GetProductsWithZipFilesAsync(HealthDashboardFilterDto model, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            IQueryable<ProductFile> productFilesQuery = GenerateProductsZipFilesQuery(model, unitOfWork);
            int[] productIds = await productFilesQuery.Select(x => x.ProductId).Distinct().ToArrayAsync(cancellationToken);

            if (!productIds.Any())
                return Array.Empty<HealthDashboardExportProductWithZipFilesDto>();

            HealthDashboardExportProductWithZipFilesDbDto[] productInfo = await unitOfWork.ProductRepository.GetAllAsNoTracking()
                .Where(x => productIds.Contains(x.Id))
                .Select(x => new HealthDashboardExportProductWithZipFilesDbDto
                {
                    ProductId = x.Id,
                    ProductName = x.Name,
                    ManufacturerId = x.ManufacturerId,
                    ZipFileNames = x.ProductFiles.Where(f => f.File.MediaType.Contains("zip")).Select(f => f.File.FileName).ToList()
                })
                .ToArrayAsync(cancellationToken);

            return productInfo.Adapt<HealthDashboardExportProductWithZipFilesDto[]>();
        }

        public async Task<HealthDashboardAccessDto[]> GetUserDashboardListAsync(string userId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            return await unitOfWork.HealthDashboardAccessRepository.GetAll()
                .Where(x => x.UserId == userId)
                .ProjectToType<HealthDashboardAccessDto>()
                .ToArrayAsync(cancellationToken);
        }

        public async Task<HealthDashboardAccessWithNameDto[]> GetUserDashboardWithNameListAsync(HealthDashboardViewType viewType, string userId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            HealthDashboardAccessWithNameDto[] items = await GetHealthDashboardAccessesAsync(viewType, userId, unitOfWork, cancellationToken);
            items = await FillDashboardNamesAsync(items, unitOfWork);

            return items;
        }

        #region private methods

        #region Check Health

        private async Task<bool> CheckHealthForManufacturerAttachmentLinkAsync(int manufacturerId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var fileIds = await CollectFileIdsForManufacturerAsync(manufacturerId, unitOfWork, cancellationToken);

            cancellationToken.ThrowIfCancellationRequested();

            return await CheckAttachmentLinksAsync(fileIds.Select(x => x.EntityId).ToList(), unitOfWork, cancellationToken);
        }

        private async Task<bool> CheckHealthForProductLineAttachmentLinkAsync(int productLineId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var fileIds = await CollectFileIdsForProductLineAsync(productLineId, unitOfWork, cancellationToken);

            cancellationToken.ThrowIfCancellationRequested();

            return await CheckAttachmentLinksAsync(fileIds.Select(x => x.EntityId).ToList(), unitOfWork, cancellationToken);
        }

        private async Task<bool> CheckHealthForProductAttachmentLinkAsync(int productId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var fileIds = await CollectFileIdsForProductAsync(productId, unitOfWork, cancellationToken);

            cancellationToken.ThrowIfCancellationRequested();

            return await CheckAttachmentLinksAsync(fileIds.Select(x => x.EntityId).ToList(), unitOfWork, cancellationToken);
        }

        #endregion

        private async Task<bool> CheckAttachmentLinksAsync(ICollection<int> fileIds, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            if (!fileIds.Any())
                return true;

            return !await unitOfWork.FileRepository.GetAll()
                                    .Where(x => fileIds.Contains(x.Id))
                                    .AnyAsync(x => fileIds.Contains(x.Id)
                                                && x.SyncStatusCode != (int)HttpStatusCode.OK
                                                && x.SyncStatusCode != (int)HttpStatusCode.Accepted, cancellationToken);
        }

        #region Collect FileIds

        private async Task<HealthDashboardReturnDto[]> CollectFileIdsForProductLineAsync(int productLineId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var productLineFileIds = await unitOfWork.ProductLineFileRepository.GetAll()
                                                     .Where(x => x.IsAttachment
                                                              && x.File.SyncUrl != null
                                                              && x.File.SyncUrl != string.Empty
                                                              && x.ProductLineId == productLineId)
                                                     .Select(x => new HealthDashboardReturnDto { EntityId = x.FileId, LastModifiedDate = x.ModifiedDate })
                                                     .ToArrayAsync(cancellationToken);

            cancellationToken.ThrowIfCancellationRequested();

            var productFileIds = await unitOfWork.ProductFileRepository.GetAll()
                                                 .Where(x => x.IsAttachment
                                                          && x.File.SyncUrl != null
                                                          && x.File.SyncUrl != string.Empty
                                                          && x.Product.ProductLineId == productLineId)
                                                 .Select(x => new HealthDashboardReturnDto { EntityId = x.FileId, LastModifiedDate = x.ModifiedDate })
                                                 .ToArrayAsync(cancellationToken);

            cancellationToken.ThrowIfCancellationRequested();

            var fileIds = productLineFileIds.Concat(productFileIds).ToArray();
            return fileIds;
        }

        private async Task<HealthDashboardReturnDto[]> CollectFileIdsForManufacturerAsync(int manufacturerId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var manufacturerFileIds = await unitOfWork.ManufacturerFileRepository.GetAll()
                                                      .Where(x => x.IsAttachment
                                                               && x.File.SyncUrl != null
                                                               && x.File.SyncUrl != string.Empty
                                                               && x.ManufacturerId == manufacturerId)
                                                      .Select(x => new HealthDashboardReturnDto { EntityId = x.FileId, LastModifiedDate = x.ModifiedDate })
                                                      .ToArrayAsync(cancellationToken);

            cancellationToken.ThrowIfCancellationRequested();

            var productLineFileIds = await unitOfWork.ProductLineFileRepository.GetAll()
                                                     .Where(x => x.IsAttachment
                                                              && x.File.SyncUrl != null
                                                              && x.File.SyncUrl != string.Empty
                                                              && x.ProductLine.ManufacturerId == manufacturerId)
                                                     .Select(x => new HealthDashboardReturnDto { EntityId = x.FileId, LastModifiedDate = x.ModifiedDate })
                                                     .ToArrayAsync(cancellationToken);

            cancellationToken.ThrowIfCancellationRequested();

            var productFileIds = await unitOfWork.ProductFileRepository.GetAll()
                                                 .Where(x => x.IsAttachment
                                                          && x.File.SyncUrl != null
                                                          && x.File.SyncUrl != string.Empty
                                                          && x.Product.ManufacturerId == manufacturerId)
                                                 .Select(x => new HealthDashboardReturnDto { EntityId = x.FileId, LastModifiedDate = x.ModifiedDate })
                                                 .ToArrayAsync(cancellationToken);

            cancellationToken.ThrowIfCancellationRequested();

            var fileIds = manufacturerFileIds.Concat(productLineFileIds).Concat(productFileIds).Distinct().ToArray();
            return fileIds;
        }

        private async Task<HealthDashboardReturnDto[]> CollectFileIdsForProductAsync(int productId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var productFileIds = await unitOfWork.ProductFileRepository.GetAll()
                                                 .Where(x => x.IsAttachment
                                                          && x.File.SyncUrl != null
                                                          && x.File.SyncUrl != string.Empty
                                                          && x.ProductId == productId)
                                                 .Select(x => new HealthDashboardReturnDto { EntityId = x.FileId, LastModifiedDate = x.ModifiedDate })
                                                 .ToArrayAsync(cancellationToken);

            return productFileIds;
        }

        private async Task<HealthDashboardReturnDto[]> CollectFileIdsForAllManufacturersAsync(IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var manufacturerFileIds = await unitOfWork.ManufacturerFileRepository.GetAll()
                                                      .Where(x => x.IsAttachment
                                                               && x.File.SyncUrl != null
                                                               && x.File.SyncUrl != string.Empty)
                                                      .Select(x => new HealthDashboardReturnDto { EntityId = x.FileId, LastModifiedDate = x.ModifiedDate })
                                                      .ToArrayAsync(cancellationToken);

            cancellationToken.ThrowIfCancellationRequested();

            var productLineFileIds = await unitOfWork.ProductLineFileRepository.GetAll()
                                                     .Where(x => x.IsAttachment
                                                              && x.File.SyncUrl != null
                                                              && x.File.SyncUrl != string.Empty)
                                                     .Select(x => new HealthDashboardReturnDto { EntityId = x.FileId, LastModifiedDate = x.ModifiedDate })
                                                     .ToArrayAsync(cancellationToken);

            cancellationToken.ThrowIfCancellationRequested();

            var productFileIds = await unitOfWork.ProductFileRepository.GetAll()
                                                 .Where(x => x.IsAttachment
                                                          && x.File.SyncUrl != null
                                                          && x.File.SyncUrl != string.Empty)
                                                 .Select(x => new HealthDashboardReturnDto { EntityId = x.FileId, LastModifiedDate = x.ModifiedDate })
                                                 .ToArrayAsync(cancellationToken);

            cancellationToken.ThrowIfCancellationRequested();

            var fileIds = manufacturerFileIds.Concat(productLineFileIds).Concat(productFileIds).Distinct().ToArray();
            return fileIds;
        }

        private async Task<HealthDashboardReturnDto[]> CollectModifiedDateFileIdsForManufacturerAsync(int manufacturerId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var manufacturerFileIds = await unitOfWork.ManufacturerFileRepository.GetAll()
                                                      .Where(x => x.IsAttachment
                                                               && x.File.SyncUrl != null
                                                               && x.File.SyncUrl != string.Empty
                                                               && x.ManufacturerId == manufacturerId)
                                                      .Select(x => new HealthDashboardReturnDto { EntityId = x.FileId, LastModifiedDate = x.ModifiedDate.Value })
                                                      .ToArrayAsync(cancellationToken);

            var productLineFileIds = await unitOfWork.ProductLineFileRepository.GetAll()
                                                     .Where(x => x.IsAttachment
                                                              && x.File.SyncUrl != null
                                                              && x.File.SyncUrl != string.Empty
                                                              && x.ProductLine.ManufacturerId == manufacturerId)
                                                     .Select(x => new HealthDashboardReturnDto { EntityId = x.FileId, LastModifiedDate = x.ModifiedDate.Value })
                                                     .ToArrayAsync(cancellationToken);

            var productFileIds = await unitOfWork.ProductFileRepository.GetAll()
                                                 .Where(x => x.IsAttachment
                                                          && x.File.SyncUrl != null
                                                          && x.File.SyncUrl != string.Empty
                                                          && x.Product.ManufacturerId == manufacturerId)
                                                 .Select(x => new HealthDashboardReturnDto { EntityId = x.FileId, LastModifiedDate = x.ModifiedDate.Value })
                                                 .ToArrayAsync(cancellationToken);

            cancellationToken.ThrowIfCancellationRequested();

            var fileIds = manufacturerFileIds.Concat(productLineFileIds).Concat(productFileIds).Distinct().ToArray();
            return fileIds;
        }

        #endregion

        #region Collect ProductIds

        private async Task<HealthDashboardReturnDto[]> CollectBlankDescriptionProductIdsForProductLineAsync(int productLineId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            var productIds = await unitOfWork.ProductRepository.GetAll()
                                                     .Where(x => string.IsNullOrEmpty(x.Description)
                                                              && x.ProductLineId == productLineId)
                                                     .Select(x => new HealthDashboardReturnDto() { EntityId = x.Id, LastModifiedDate = x.ModifiedDate })
                                                     .ToArrayAsync(cancellationToken);
            return productIds;
        }

        private async Task<HealthDashboardReturnDto[]> CollectBlankDescriptionProductIdsForManufacturerAsync(int manufacturerId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            var productIds = await unitOfWork.ProductRepository.GetAll()
                                         .Where(x => string.IsNullOrEmpty(x.Description)
                                                  && x.ManufacturerId == manufacturerId)
                                         .Select(x => new HealthDashboardReturnDto() { EntityId = x.Id, LastModifiedDate = x.ModifiedDate })
                                         .ToArrayAsync(cancellationToken);
            return productIds;
        }

        private async Task<HealthDashboardReturnDto[]> CollectBlankDescriptionProductIdsForAllManufacturersAsync(IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var productIds = await unitOfWork.ProductRepository.GetAll()
                             .Where(x => string.IsNullOrEmpty(x.Description))
                             .Select(x => new HealthDashboardReturnDto() { EntityId = x.Id, LastModifiedDate = x.ModifiedDate })
                             .ToArrayAsync(cancellationToken);

            cancellationToken.ThrowIfCancellationRequested();

            return productIds;
        }

        #endregion

        #region Collect NoRevitFile

        private async Task<HealthDashboardReturnDto[]> CollectNoRevitFileProductIdsForProductLineAsync(int productLineId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var revitTypes = await GetRevitTypesAsync(unitOfWork, cancellationToken);

            var productIds = await unitOfWork.ProductRepository.GetAll()
                            .Where(x => x.ProductLineId == productLineId
                                        && !x.ProductFiles.Any(y => revitTypes.Contains(y.ProjectDataTypeId.Value)))
                            .Select(x => new HealthDashboardReturnDto() { EntityId = x.Id, LastModifiedDate = x.ModifiedDate })
                            .ToArrayAsync(cancellationToken);

            return productIds;
        }

        private async Task<HealthDashboardReturnDto[]> CollectNoRevitFileProductIdsForManufacturerAsync(int manufacturerId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var revitTypes = await GetRevitTypesAsync(unitOfWork, cancellationToken);

            var productIds = await unitOfWork.ProductRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId
                         && !x.ProductFiles.Any(y => revitTypes.Contains(y.ProjectDataTypeId.Value)))
                .Select(x => x.Id)
                .ToArrayAsync(cancellationToken);

            if (!productIds.Any())
                return Array.Empty<HealthDashboardReturnDto>();

            var items = await unitOfWork.ProductRepository.GetAll()
                .Where(x => productIds.Contains(x.Id))
                .Select(x => new HealthDashboardReturnDto { EntityId = x.Id, LastModifiedDate = x.ModifiedDate })
                .ToArrayAsync();

            return items;
        }

        private async Task<HealthDashboardReturnDto[]> CollectNoRevitFileProductIdsForAllManufacturersAsync(IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var revitTypes = await GetRevitTypesAsync(unitOfWork, cancellationToken);

            var productIds = await unitOfWork.ProductRepository.GetAll()
                            .Where(x => !x.ProductFiles.Any(y => revitTypes.Contains(y.ProjectDataTypeId.Value)))
                            .Select(x => new HealthDashboardReturnDto() { EntityId = x.Id, LastModifiedDate = x.ModifiedDate })
                            .ToArrayAsync(cancellationToken);

            return productIds;
        }

        #endregion

        #region Collect RevitFIle With No Extension

        private async Task<HealthDashboardReturnDto[]> CollectRevitNoExtensionFileIdsForProductLineAsync(int productLineId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var revitTypes = await GetRevitTypesAsync(unitOfWork, cancellationToken);

            var productLineFilesId = await unitOfWork.ProductLineFileRepository.GetAll()
                                        .Where(x => x.ProductLineId == productLineId
                                        && revitTypes.Contains(x.ProjectDataTypeId.Value))
                                        .Select(x => new { x.File.FileName, files = new HealthDashboardReturnDto() { EntityId = x.FileId, LastModifiedDate = x.ModifiedDate } })
                                        .ToArrayAsync(cancellationToken);

            var productFileRepository = await unitOfWork.ProductFileRepository.GetAll()
                                        .Where(x => x.Product.ProductLineId == productLineId
                                        && revitTypes.Contains(x.ProjectDataTypeId.Value))
                                        .Select(x => new { x.File.FileName, files = new HealthDashboardReturnDto() { EntityId = x.FileId, LastModifiedDate = x.ModifiedDate } })
                                        .ToArrayAsync(cancellationToken);

            var files = productLineFilesId.Concat(productFileRepository);

            var fileIds = files.Where(x => !_revitExtensions.Contains(Path.GetExtension(x.FileName).ToLower())).Select(x => x.files).Distinct().ToArray();

            cancellationToken.ThrowIfCancellationRequested();

            return fileIds;
        }

        private async Task<HealthDashboardReturnDto[]> CollectRevitNoExtensionFileIdsForManufacturerAsync(int manufacturerId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var revitTypes = await GetRevitTypesAsync(unitOfWork, cancellationToken);

            var manufacturerFilesId = await unitOfWork.ManufacturerFileRepository.GetAll()
                                        .Where(x => x.ManufacturerId == manufacturerId
                                        && revitTypes.Contains(x.ProjectDataTypeId.Value))
                                        .Select(x => new { x.File.FileName, files = new HealthDashboardReturnDto() { EntityId = x.FileId, LastModifiedDate = x.ModifiedDate } })
                                        .ToArrayAsync(cancellationToken);

            var productLineFilesId = await unitOfWork.ProductLineFileRepository.GetAll()
                                        .Where(x => x.ProductLine.ManufacturerId == manufacturerId
                                        && revitTypes.Contains(x.ProjectDataTypeId.Value))
                                        .Select(x => new { x.File.FileName, files = new HealthDashboardReturnDto() { EntityId = x.FileId, LastModifiedDate = x.ModifiedDate } })
                                        .ToArrayAsync(cancellationToken);

            var productFileRepository = await unitOfWork.ProductFileRepository.GetAll()
                                        .Where(x => x.Product.ManufacturerId == manufacturerId
                                        && revitTypes.Contains(x.ProjectDataTypeId.Value))
                                        .Select(x => new { x.File.FileName, files = new HealthDashboardReturnDto() { EntityId = x.FileId, LastModifiedDate = x.ModifiedDate } })
                                        .ToArrayAsync(cancellationToken);

            var fileIds = manufacturerFilesId.Concat(productLineFilesId).Concat(productFileRepository)
                .Where(x => !_revitExtensions.Contains(Path.GetExtension(x.FileName).ToLower()))
                .Select(x => x.files).Distinct().ToArray();

            cancellationToken.ThrowIfCancellationRequested();

            return fileIds;
        }

        private async Task<HealthDashboardReturnDto[]> CollectRevitNoExtensionFileIdsForProductAsync(int productId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var revitTypes = await GetRevitTypesAsync(unitOfWork, cancellationToken);

            var productFileRepository = await unitOfWork.ProductFileRepository.GetAll()
                                        .Where(x => x.ProductId == productId
                                        && !_revitExtensions.Contains(Path.GetExtension(x.File.FileName))
                                        && revitTypes.Contains(x.ProjectDataTypeId.Value))
                                        .Select(x => new { x.File.FileName, files = new HealthDashboardReturnDto() { EntityId = x.FileId, LastModifiedDate = x.ModifiedDate } })
                                        .ToArrayAsync(cancellationToken);

            return productFileRepository.Select(x => x.files).Distinct().ToArray();
        }

        private async Task<HealthDashboardReturnDto[]> CollectRevitNoExtensionFileIdsForAllManufacturersAsync(IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var revitTypes = await GetRevitTypesAsync(unitOfWork, cancellationToken);

            var manufacturerFilesId = await unitOfWork.ManufacturerFileRepository.GetAll()
                                        .Where(x => revitTypes.Contains(x.ProjectDataTypeId.Value))
                                        .Select(x => new { x.File.FileName, files = new HealthDashboardReturnDto() { EntityId = x.FileId, LastModifiedDate = x.ModifiedDate } })
                                        .ToArrayAsync(cancellationToken);

            var productLineFilesId = await unitOfWork.ProductLineFileRepository.GetAll()
                                        .Where(x => revitTypes.Contains(x.ProjectDataTypeId.Value))
                                        .Select(x => new { x.File.FileName, files = new HealthDashboardReturnDto() { EntityId = x.FileId, LastModifiedDate = x.ModifiedDate } })
                                        .ToArrayAsync(cancellationToken);

            var productFileRepository = await unitOfWork.ProductFileRepository.GetAll()
                                        .Where(x => revitTypes.Contains(x.ProjectDataTypeId.Value))
                                        .Select(x => new { x.File.FileName, files = new HealthDashboardReturnDto() { EntityId = x.FileId, LastModifiedDate = x.ModifiedDate } })
                                        .ToArrayAsync(cancellationToken);

            var files = manufacturerFilesId.Concat(productLineFilesId).Concat(productFileRepository);
            var fileIds = files.Where(x => !_revitExtensions.Contains(Path.GetExtension(x.FileName).ToLower())).Select(x => x.files).Distinct().ToArray();
            return fileIds;
        }

        #endregion

        #region Calculates

        private async Task<int> CalculateBrokenLinksCountAsync(HealthDashboardFilterDto model, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            int[] fileIds = await GetFileIdsAsync(model, unitOfWork, cancellationToken);

            cancellationToken.ThrowIfCancellationRequested();

            return await CalculateBrokenLinksAsync(fileIds, unitOfWork, cancellationToken);
        }

        private async Task<int> CalculateBlankDescriptionsAsync(HealthDashboardFilterDto model, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            int[] productIds = await GetBlankDescriptionsProductIdsAsync(model, unitOfWork, cancellationToken);

            cancellationToken.ThrowIfCancellationRequested();

            return productIds.Distinct().Count();
        }

        private async Task<int> CalculateNoRevitFileAsync(HealthDashboardFilterDto model, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            cancellationToken.ThrowIfCancellationRequested();

            int[] productIds = await GetNoRevitFileProductIdsAsync(model, unitOfWork, cancellationToken);

            cancellationToken.ThrowIfCancellationRequested();

            return productIds.Distinct().Count();
        }

        private async Task<int> CalculateRevitFileMissingExtension(HealthDashboardFilterDto model, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            int[] productIds = await GetRevitFileNoExtensionFileIdsAsync(model, unitOfWork, cancellationToken);
            return productIds.Distinct().Count();
        }

        private async Task<int> CalculateBlankPhoneNumbersAsync(HealthDashboardFilterDto model, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            string manufacturerPhone = await unitOfWork.ManufacturerRepository.GetAll()
                .Where(x => x.Id == model.EntityId)
                .Select(x => x.PhoneNumber)
                .FirstOrDefaultAsync(cancellationToken);

            return string.IsNullOrEmpty(manufacturerPhone) ? 1 : 0;
        }

        private async Task<int> CalculateProductsCountWithZipFilesAsync(HealthDashboardFilterDto model, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            IQueryable<ProductFile> productFileQuery = GenerateProductsZipFilesQuery(model, unitOfWork);

            int[] productIds = await productFileQuery.Select(x => x.ProductId).ToArrayAsync(cancellationToken);
            return productIds.Distinct().Count();
        }

        private IQueryable<ProductFile> GenerateProductsZipFilesQuery(HealthDashboardFilterDto model, IUnitOfWork unitOfWork)
        {
            IQueryable<ProductFile> productFileQuery = unitOfWork.ProductFileRepository.GetAllAsNoTracking()
                                                                 .Where(x => x.File.MediaType.Contains("zip"));

            if (model.Type == HealthDashboardAccessType.Manufacturer)
                productFileQuery = productFileQuery.Where(x => x.Product.ManufacturerId == model.EntityId);
            if (model.Type == HealthDashboardAccessType.ProductLine)
                productFileQuery = productFileQuery.Where(x => x.Product.ProductLineId == model.EntityId);

            return productFileQuery;
        }
        #endregion

        private int[] GetFilteredEntityIds(HealthDashboardFilterDto model, HealthDashboardReturnDto[] entities)
        {
            if (!entities.Any())
                return Array.Empty<int>();

            if (model.FilterPeriod == HealthDashboardFilterPeriod.Month)
                return entities.Where(x => x.LastModifiedDate >= DateTime.UtcNow.AddDays(-30).Date).Select(x => x.EntityId).ToArray();
            else if (model.FilterPeriod == HealthDashboardFilterPeriod.SixMonth)
                return entities.Where(x => x.LastModifiedDate < DateTime.UtcNow.AddMonths(-6).Date).Select(x => x.EntityId).ToArray();
            else
                return entities.Select(x => x.EntityId).ToArray();
        }

        public async Task<int> GetProductsCountWithZipFilesForAllManufacturersAsync(IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            return await unitOfWork.ProductFileRepository.GetAll()
                                   .Where(x => x.File.MediaType.Contains("zip"))
                                   .Select(x => x.ProductId)
                                   .Distinct()
                                   .CountAsync(cancellationToken);
        }

        private async ValueTask<int[]> GetRevitTypesAsync(IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            if (_revitTypes == null)
            {
                _revitTypes = await unitOfWork.ProjectDataTypeRepository.GetAll()
                    .Where(x => x.Id == (int)ProductFileType.Revit || x.ParentId == (int)ProductFileType.Revit)
                    .Select(x => x.Id)
                    .ToArrayAsync(cancellationToken);
            }

            return _revitTypes;
        }

        private async Task<List<HealthDashboardInfoDto>> GetHealthDashboardInfoAsync(HealthDashboardAccessDto item, HealthDashboardFilterPeriod filterPeriod, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
#if DEBUG
            Stopwatch stopwatch = Stopwatch.StartNew();
#endif

            cancellationToken.ThrowIfCancellationRequested();

            List<HealthDashboardInfoDto> dashboardsInfo = new();

            var filterDto = new HealthDashboardFilterDto
            {
                EntityId = item.EntityId,
                FilterPeriod = filterPeriod,
                Type = item.Type
            };

            int[] fileIds = await GetFileIdsAsync(filterDto, unitOfWork, cancellationToken);

            cancellationToken.ThrowIfCancellationRequested();

            HealthDashboardInfoDto dashboardInfo = new HealthDashboardInfoDto
            {
                RecordsCount = await CalculateBrokenLinksAsync(fileIds, unitOfWork, cancellationToken),
                FilterPeriod = filterPeriod,
                Type = item.Type,
                DashboardMissingType = HealthDashboardType.BrokenLinks,
                DashboardName = HealthDashboardConstants.TotalBrokenLinksDashboardCaption,
                EntityId = item.EntityId
            };

            dashboardsInfo.Add(dashboardInfo);

            cancellationToken.ThrowIfCancellationRequested();

            dashboardInfo = new HealthDashboardInfoDto
            {
                RecordsCount = await CalculateBlankDescriptionsAsync(filterDto, unitOfWork, cancellationToken),
                FilterPeriod = filterPeriod,
                Type = item.Type,
                DashboardMissingType = HealthDashboardType.BlankDescriptions,
                DashboardName = HealthDashboardConstants.TotalBlankDesriptionsDashboardCaption,
                EntityId = item.EntityId
            };

            dashboardsInfo.Add(dashboardInfo);

            cancellationToken.ThrowIfCancellationRequested();

            dashboardInfo = new HealthDashboardInfoDto
            {
                RecordsCount = await CalculateNoRevitFileAsync(filterDto, unitOfWork, cancellationToken),
                FilterPeriod = filterPeriod,
                Type = item.Type,
                DashboardMissingType = HealthDashboardType.NoRevitFiles,
                DashboardName = HealthDashboardConstants.TotalNoRevitFilesDashboardCaption,
                EntityId = item.EntityId
            };

            dashboardsInfo.Add(dashboardInfo);

            cancellationToken.ThrowIfCancellationRequested();

            dashboardInfo = new HealthDashboardInfoDto
            {
                RecordsCount = await CalculateRevitFileMissingExtension(filterDto, unitOfWork, cancellationToken),
                FilterPeriod = filterPeriod,
                Type = item.Type,
                DashboardMissingType = HealthDashboardType.RevitMissingExtension,
                DashboardName = HealthDashboardConstants.TotalMissingExtensionsDashboardCaption,
                EntityId = item.EntityId
            };

            dashboardsInfo.Add(dashboardInfo);

            cancellationToken.ThrowIfCancellationRequested();

            if (item.Type == HealthDashboardAccessType.Manufacturer)
            {
                dashboardInfo = new HealthDashboardInfoDto
                {
                    RecordsCount = await CalculateBlankPhoneNumbersAsync(filterDto, unitOfWork, cancellationToken),
                    FilterPeriod = filterPeriod,
                    Type = item.Type,
                    DashboardMissingType = HealthDashboardType.BlankPhoneNumber,
                    DashboardName = HealthDashboardConstants.TotalBlankPhoneNumberDashboardCaption,
                    EntityId = item.EntityId
                };

                dashboardsInfo.Add(dashboardInfo);

                cancellationToken.ThrowIfCancellationRequested();
            }

            dashboardInfo = new HealthDashboardInfoDto
            {
                RecordsCount = await CalculateProductsCountWithZipFilesAsync(filterDto, unitOfWork, cancellationToken),
                FilterPeriod = filterPeriod,
                Type = item.Type,
                DashboardMissingType = HealthDashboardType.ProductsWithZipFilesNumber,
                DashboardName = HealthDashboardConstants.TotalNumberOfProductsWithZipFilesDashboardCaption,
                EntityId = item.EntityId
            };

            dashboardsInfo.Add(dashboardInfo);

            cancellationToken.ThrowIfCancellationRequested();

#if DEBUG
            stopwatch.Stop();
            Debug.WriteLine($"Time spent for manufacturer {item.EntityId} {stopwatch.ElapsedMilliseconds}");
#endif

            return dashboardsInfo;
        }

        private async Task<HealthDashboardAccessWithNameDto[]> GetHealthDashboardAccessesAsync(HealthDashboardViewType viewType, string userId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default)
        {
            switch (viewType)
            {
                case HealthDashboardViewType.All:
                    {
                        List<HealthDashboardAccessWithNameDto> items = new();
                        int[] allManufacturerIds = await unitOfWork.ManufacturerRepository.GetAll().Select(x => x.Id).ToArrayAsync(cancellationToken);
                        items.AddRange(allManufacturerIds.Select(x => new HealthDashboardAccessWithNameDto { EntityId = x, Type = HealthDashboardAccessType.Manufacturer }));
                        int[] allProductLineIds = await unitOfWork.ProductLineRepository.GetAll().Select(x => x.Id).ToArrayAsync(cancellationToken);
                        items.AddRange(allProductLineIds.Select(x => new HealthDashboardAccessWithNameDto { EntityId = x, Type = HealthDashboardAccessType.ProductLine }));
                        return items.ToArray();
                    }
                case HealthDashboardViewType.AllAssigned:
                    {
                        return await unitOfWork.HealthDashboardAccessRepository.GetAll().Where(x => x.UserId == userId).ProjectToType<HealthDashboardAccessWithNameDto>().ToArrayAsync(cancellationToken);
                    }
                case HealthDashboardViewType.AllManufacturers:
                    {
                        int[] allManufacturerIds = await unitOfWork.ManufacturerRepository.GetAll().Select(x => x.Id).ToArrayAsync(cancellationToken);
                        return allManufacturerIds.Select(x => new HealthDashboardAccessWithNameDto { EntityId = x, Type = HealthDashboardAccessType.Manufacturer }).ToArray();
                    }
                case HealthDashboardViewType.AssignedManufacturers:
                    {
                        return await unitOfWork.HealthDashboardAccessRepository.GetAll().Where(x => x.UserId == userId && x.Type == HealthDashboardAccessType.Manufacturer).ProjectToType<HealthDashboardAccessWithNameDto>().ToArrayAsync(cancellationToken);
                    }
                case HealthDashboardViewType.AllProductLines:
                    {
                        int[] allProductLineIds = await unitOfWork.ProductLineRepository.GetAll().Select(x => x.Id).ToArrayAsync();
                        return allProductLineIds.Select(x => new HealthDashboardAccessWithNameDto { EntityId = x, Type = HealthDashboardAccessType.ProductLine }).ToArray();
                    }
                case HealthDashboardViewType.AssignedProductLines:
                    {
                        return await unitOfWork.HealthDashboardAccessRepository.GetAll().Where(x => x.UserId == userId && x.Type == HealthDashboardAccessType.ProductLine).ProjectToType<HealthDashboardAccessWithNameDto>().ToArrayAsync(cancellationToken);
                    }
                default: return Array.Empty<HealthDashboardAccessWithNameDto>();
            }
        }

        private async Task<HealthDashboardAccessWithNameDto[]> FillDashboardNamesAsync(HealthDashboardAccessWithNameDto[] items, IUnitOfWork unitOfWork)
        {
            int[] manufacturerIds = items.Where(x => x.Type == HealthDashboardAccessType.Manufacturer).Select(x => x.EntityId).ToArray();
            if (manufacturerIds.Any())
            {
                BaseNameListDto[] manufacturers = await unitOfWork.ManufacturerRepository.GetAll()
                       .Where(x => manufacturerIds.Contains(x.Id))
                       .ProjectToType<BaseNameListDto>()
                       .ToArrayAsync();

                Parallel.ForEach(items.Where(x => x.Type == HealthDashboardAccessType.Manufacturer), manufacturerItem =>
                {
                    manufacturerItem.Name = manufacturers.FirstOrDefault(x => x.Id == manufacturerItem.EntityId).Name;
                });
            }

            int[] productLineIds = items.Where(x => x.Type == HealthDashboardAccessType.ProductLine).Select(x => x.EntityId).ToArray();
            if (productLineIds.Any())
            {
                BaseNameListDto[] productLines = await unitOfWork.ProductLineRepository.GetAll()
                       .Where(x => productLineIds.Contains(x.Id))
                       .ProjectToType<BaseNameListDto>()
                       .ToArrayAsync();

                Parallel.ForEach(items.Where(x => x.Type == HealthDashboardAccessType.ProductLine), productLineItem =>
                {
                    productLineItem.Name = productLines.FirstOrDefault(x => x.Id == productLineItem.EntityId).Name;
                });
            }

            return items;
        }
        #endregion
    }
}