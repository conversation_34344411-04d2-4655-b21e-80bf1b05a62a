﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// Feature Setting Manage Controller
    /// </summary>
    [Route("api/[controller]/[action]")]
    public class FeatureSettingController : BaseApiController
    {
        private readonly IFeatureSettingService _featureSettingService;

        public FeatureSettingController(IFeatureSettingService featureSettingService)
        {
            _featureSettingService = featureSettingService;
        }

        /// <summary>
        /// Lists all feature settings
        /// </summary>
        /// <returns></returns>
        [HttpGet]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> List()
        {
            return Ok(await _featureSettingService.ListAsync());
        }

        /// <summary>
        /// Lists feature settings for frontend
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> PublicList()
        {
            return Ok(await _featureSettingService.ListAsync(FeatureSettingTarget.FrontEnd));
        }

        /// <summary>
        /// Sets status to feature specified
        /// </summary>
        /// <param name="model">The feature status model</param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        public async Task<IActionResult> SetStatus(FeatureSettingSetStatusModel model)
        {
            var result = await _featureSettingService.SetStatus(model);
            return Ok(result);
        }
    }
}