﻿using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class DetailRating : BaseEntity
    {
        public int DetailId { get; set; }

        public int Rating { get; set; }

        public string Comment { get; set; }

        public DetailRatingType Type { get; set; }

        [ForeignKey("DetailId")]
        public virtual Detail Detail { get; set; }
    }

    public enum DetailRatingType
    {
        ProductRating = 0,
        ContentRating = 1
    }
}