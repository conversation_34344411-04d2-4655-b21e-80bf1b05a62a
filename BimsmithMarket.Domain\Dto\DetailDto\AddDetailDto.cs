﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Models;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Dto.DetailDto
{
    public class AddDetailDto
    {
        [Required]
        public string Name { get; set; }

        [Required]
        public DetailOrientation Orientation { get; set; }

        [Required]
        public bool Interior { get; set; }

        [Required]
        public bool Exterior { get; set; }

        [Required]
        public int? DetailScaleId { get; set; }

        [Required]
        public bool Published { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        [Required]
        public int ManufacturerId { get; set; }

        public string Description { get; set; }

        [Required]
        public List<int> ExternalMasteformatIds { get; set; }

        [Required]
        public List<PhotoModel> Photos { get; set; }

        [Required]
        public List<DetailProjectFileDto> ProjectFiles { get; set; }

        public List<int> ApplicationIds { get; set; }

        public List<int> ProductIds { get; set; }

        public List<int> RelatedDetailIds { get; set; }
    }
}