﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer.Context;
using BIMsmithMarket.Services;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Builder;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using System;

namespace BIMsmithBlog
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            // Add services to the container.
            builder.Services.AddControllersWithViews();

            builder.Services.AddDistributedMemoryCache();

            builder.Services.AddSession(options =>
            {
                options.IdleTimeout = TimeSpan.FromSeconds(10);
                options.Cookie.HttpOnly = true;
                options.Cookie.IsEssential = true;
            });

            builder.Services.AddHttpClient();
            builder.Services.AddScoped<ICheckService, CheckService>();

            builder.Services
                .AddDbContextPool<ApplicationDbContext>((serviceProvider, options)
                    => options.UseLazyLoadingProxies()
                              .UseSqlServer(ConfigurationHelper.GetValue("ConnectionStrings:MarketDBConnection"))
                              .UseInternalServiceProvider(serviceProvider));

            Log.Logger = LogHelper.Initialize("Blog");

            var app = builder.Build();

            // Configure the HTTP request pipeline.
            if (!app.Environment.IsDevelopment())
            {
                app.UseExceptionHandler("/Home/Error");
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                app.UseHsts();
            }

            app.UseHttpsRedirection();

            app.UseStaticFiles();

            app.UseRouting();

            app.UseAuthorization();

            app.UseSession();

            app.MapControllerRoute(
                name: "Sitemap",
                pattern: "Sitemap.xml",
                defaults: new { controller = "SEOData", action = "Sitemap" }
            );

            app.MapControllerRoute(
               name: "robots",
               pattern: "robots.txt",
               defaults: new { controller = "SEOData", action = "Robots" }
            );

            app.MapControllerRoute(
                name: "Photo",
                pattern: "Photo/{action}",
                defaults: new { controller = "Photo", action = "Index" }
            );

            app.MapControllerRoute(
                name: "BlogDetails",
                pattern: "{vanityId}",
                defaults: new { controller = "Blog", action = "Details" }
            );

            app.MapControllerRoute(
                name: "Default",
                pattern: "{controller}/{action}/{id?}",
                defaults: new { controller = "Blog", action = "Index" }
            );

            app.Run();
        }
    }
}