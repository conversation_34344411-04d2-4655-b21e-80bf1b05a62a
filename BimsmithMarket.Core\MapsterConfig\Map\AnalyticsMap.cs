﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto.Analytics;
using Mapster;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public static class AnalyticsMap
    {
        public static void Register(TypeAdapterConfig config)
        {
            config.ForType<RequestPricingUser, AnalyticsRequestPricingUserListDto>()
                .Map(d => d.UserEmail, s => s.CreatedBy.Email)
                .Map(d => d.UserFirstName, s => s.CreatedBy.FirstName)
                .Map(d => d.UserLastName, s => s.CreatedBy.LastName)
                .Map(d => d.ProductName, s => s.Product.Name)
                .Map(d => d.Date, s => s.CreatedDate);
        }
    }
}