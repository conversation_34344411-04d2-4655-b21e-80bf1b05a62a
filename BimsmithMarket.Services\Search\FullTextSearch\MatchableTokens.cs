﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace BIMsmithMarket.Services.Search.FullTextSearch
{
    // helper class for matching consecutive tokens in a query
    public class MatchableTokens
    {
        public static Dictionary<string, MatchableTokens> SplitIntoMatchableTokens(Dictionary<string, List<int>> variants)
        {
            return variants.GroupBy(m => m.Key.Split(' ')[0]).ToDictionary(m => m.Key, m =>
                new MatchableTokens
                {
                    Prefix = m.Key,
                    Tokens = m.Select(n => new Tuple<string[], List<int>>(n.Key.Split(' ').Skip(1).ToArray(), n.Value)).ToList()
                });
        }

        // first token
        public string Prefix { get; set; }

        // possible following tokens
        public List<Tuple<string[], List<int>>> Tokens { get; set; }

        // try to match input query tokens against this entry and return match %
        public Dictionary<int, float> MatchWithConfidence(IEnumerable<QueryToken> tokens, QueryTokenType tokenType, int totalQueryTokens)
        {
            var matches = new Dictionary<int, float>();
            foreach (var expectedTokens in Tokens)
            {
                var maxTokens = expectedTokens.Item1.Length;
                var inputTokens = tokens.Take(maxTokens).ToArray();
                int numMatched = 0;
                for (int i = 0; i < inputTokens.Length; i++)
                {
                    if (expectedTokens.Item1[i] == inputTokens[i].Original || expectedTokens.Item1[i] == inputTokens[i].Corrected)
                    {
                        inputTokens[i].Type = tokenType;
                        numMatched++;
                    }
                    else
                    {
                        break;
                    }
                }
                var matchPercentage = (numMatched + 1.0f) / (maxTokens + 1.0f); // prefix was already matched earlier
                if (matchPercentage == 1 && numMatched == totalQueryTokens - 1)
                {
                    // we matched as many tokens as there were in the query, increase aboe threshold
                    matchPercentage = WeightsTable.CategoryBeelineThreshold + 0.01f;
                }
                foreach (var match in expectedTokens.Item2)
                {
                    if (!matches.ContainsKey(match))
                    {
                        matches.Add(match, matchPercentage);
                    }
                    else
                    {
                        matches[match] = Math.Max(matches[match], matchPercentage);
                    }
                }
#if (DEBUG)
                Debug.WriteLine(string.Format("MatchableTokens::MatchWithConfidence matched {0} on tokens '{1} {2}' to {3} with {4} confidence", tokenType, Prefix, string.Join(" ", inputTokens.Select(t => t.Original)), string.Join(",", matches.Keys), matchPercentage.ToString("P0")));
#endif
            }
            return matches;
        }

        // try to match input query tokens against this entry and match length
        public Dictionary<int, QueryToken[]> MatchExact(IEnumerable<QueryToken> tokens, QueryTokenType tokenType, SearchResults results)
        {
            var matches = new Dictionary<int, QueryToken[]>();
            var maxMatched = 0;
            foreach (var expectedTokens in Tokens)
            {
                var maxTokens = expectedTokens.Item1.Length;
                var inputTokens = tokens.Take(maxTokens).ToArray();
                int numMatched = 0;
                for (int i = 0; i < inputTokens.Length; i++)
                {
                    if (expectedTokens.Item1[i] == inputTokens[i].Original || expectedTokens.Item1[i] == inputTokens[i].Corrected)
                    {
                        numMatched++;
                    }
                    else
                    {
                        break;
                    }
                }
                if (maxMatched < numMatched)
                {
                    maxMatched = numMatched;
                }
                if (numMatched == maxTokens)
                {
                    foreach (var match in expectedTokens.Item2)
                    {
                        if (!matches.ContainsKey(match))
                        {
                            matches.Add(match, inputTokens);
                        }
                        else if (numMatched > matches[match].Length)
                        {
                            matches[match] = inputTokens;
                        }
                    }
                }
            }

            if (matches.Any())
                results?.Explain(c => $"Matched {tokenType} on tokens '{Prefix} {string.Join(" ", tokens.Select(t => t.Original).Take(maxMatched))}' to {string.Join(",", matches.Keys)}");

            return matches;
        }
    }
}
