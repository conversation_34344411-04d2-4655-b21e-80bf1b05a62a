﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.Authorization;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using Microsoft.AspNetCore.Identity;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IUserService
    {
        Task<bool> IsUserSubscribedAsync(string userId, IUnitOfWork unitOfWork);

        Task<bool> AddBimsmithUser(AddBimsmithUserModel model);

        Task<bool> DeleteAccountAsync(DeleteAccountModel model);

        Task<LoginIdentityResultDto> GetIdentityAsync(LoginDto model);

        Task<ApplicationUserDto> GetUserInfoByEmailAsync(string email);

        Task<ClaimsIdentity> GetUserClaimsAsync(string userEmail);

        Task<BIMsmithUserInfoDto> GetBIMsmithUserInfoByIdAsync(string userId);

        Task<BIMsmithUserInfoDto> GetBIMsmithUserInfoByEmailAsync(string userEmail);

        Task<bool> CheckAccountAsync(CheckAccountDto model, UserManager<ApplicationUser> userManager);

        Task<bool> IsUserAdminAsync(string email, IUnitOfWork unitOfWork);
    }
}