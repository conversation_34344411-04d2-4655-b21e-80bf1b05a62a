﻿using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IAttachmentOrderService
    {
        Task<object> ListAsync(IUnitOfWork unitOfWork, int manufacturerId, string q);
        Task<object> GetAsync(IUnitOfWork unitOfWork, int id);
        Task<object> AddEditAsync(IUnitOfWork unitOfWork, string userId, int manufacturerId, AddAttachmentOrderModel[] model);
        Task DeleteAsync(IUnitOfWork unitOfWork, int? id, int? manufacturerId);
    }
}
