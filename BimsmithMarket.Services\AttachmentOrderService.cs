﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class AttachmentOrderService : IAttachmentOrderService
    {
        public async Task<object> ListAsync(IUnitOfWork unitOfWork, int manufacturerId, string q)
        {
            var manufacturer = await unitOfWork.ManufacturerRepository.GetByIdAsync(manufacturerId);
            if (manufacturer == null)
            {
                throw new DbItemNotFoundException($"Cannot find manufacturer with id: {manufacturerId}");
            }

            var query = unitOfWork.AttachmentOrderRepository.GetAll().Where(a => a.ManufacturerId == manufacturerId);
            if (!string.IsNullOrWhiteSpace(q))
            {
                query = query.Where(a => a.Type.Contains(q));
            }

            var list = await query
                .Select(a => new
                {
                    id = a.Id,
                    manufacturer = new
                    {
                        id = a.ManufacturerId,
                        name = a.Manufacturer.Name
                    },
                    type = a.Type,
                    order = a.Order
                })
                .OrderBy(a => a.order)
                .AsNoTracking()
                .ToListAsync();

            if (list.Count == 0)
            {
                var order = 1;
                foreach (var attachmentOrder in AttachmentOrderProvider.DefaultAttachmentsOrder)
                {
                    list.Add(new
                    {
                        id = 0,
                        manufacturer = new
                        {
                            id = manufacturerId,
                            name = manufacturer.Name
                        },
                        type = attachmentOrder,
                        order
                    });
                    order++;
                }
            }

            return list;
        }

        public async Task<object> GetAsync(IUnitOfWork unitOfWork, int id)
        {
            var query = unitOfWork.AttachmentOrderRepository.GetAll();
            var item = await query.Where(a => a.Id == id).Select(a => new
            {
                id = a.Id,
                manufacturer = new
                {
                    id = a.ManufacturerId,
                    name = a.Manufacturer.Name
                },
                type = a.Type,
                order = a.Order
            })
            .FirstOrDefaultAsync();

            if (item == null)
            {
                throw new DbItemNotFoundException($"Not found the Category");
            }

            return item;
        }

        public async Task<object> AddEditAsync(IUnitOfWork unitOfWork, string userId, int manufacturerId, AddAttachmentOrderModel[] model)
        {
            var response = new List<dynamic>();
            var manufacturer = await unitOfWork.ManufacturerRepository.GetByIdAsync(manufacturerId);
            if (manufacturer == null)
            {
                throw new DbItemNotFoundException($"Cannot find manufacturer with id: {manufacturerId}");
            }

            var oldAttachmentOrders = await unitOfWork.AttachmentOrderRepository.GetAll().Where(a => a.ManufacturerId == manufacturerId).ToListAsync();
            unitOfWork.AttachmentOrderRepository.Delete(oldAttachmentOrders);
            await unitOfWork.SaveAsync();

            foreach (var attachmentOrder in model)
            {
                var newAttachmentOrder = new AttachmentOrder
                {
                    ManufacturerId = manufacturerId,
                    Type = attachmentOrder.Type,
                    Order = attachmentOrder.Order,
                    CreatedDate = DateTime.UtcNow,
                    CreatedById = userId
                };
                unitOfWork.AttachmentOrderRepository.Insert(newAttachmentOrder);
                await unitOfWork.SaveAsync();

                response.Add(new
                {
                    id = newAttachmentOrder.Id,
                    manufacturer = new
                    {
                        id = newAttachmentOrder.ManufacturerId,
                        name = manufacturer.Name
                    },
                    type = newAttachmentOrder.Type,
                    order = newAttachmentOrder.Order
                });
            }

            await ReorderAttachmentsAsync(manufacturerId, model);

            return response;
        }

        private async Task ReorderAttachmentsAsync(int manufacturerId, IEnumerable<AddAttachmentOrderModel> attachmentOrder)
        {
            try
            {
                var attachmentOrderList = attachmentOrder.Select(a => a.Type).ToList();
                using (var unitOfWork = UnitOfWork.Create())
                {
                    var products = await unitOfWork.ProductRepository.GetAll().Where(x => x.ManufacturerId == manufacturerId).ToListAsync();
                    if (products.Any())
                    {
                        foreach (var product in products)
                        {
                            var productAttachments = product.ProductFiles.Where(x => x.IsAttachment).ToList();
                            if (productAttachments.Any())
                            {
                                var newProductAttachments = productAttachments.OrderBy(x => attachmentOrderList.IndexOf(x.File.Title)).ToList();
                                unitOfWork.ProductFileRepository.Delete(productAttachments.ToList());
                                await unitOfWork.SaveAsync();
                                var productFileWeight = 1;
                                foreach (var newAttachment in newProductAttachments)
                                {
                                    ProductFile productFile = new ProductFile();
                                    productFile.ContentCheckedBy = newAttachment.ContentCheckedBy;
                                    productFile.ContentCreatedby = newAttachment.ContentCreatedby;
                                    productFile.CreatedBy = newAttachment.CreatedBy;
                                    productFile.CreatedById = newAttachment.CreatedById;
                                    productFile.CreatedDate = newAttachment.CreatedDate;
                                    productFile.CustomFileId = newAttachment.CustomFileId;
                                    productFile.FileId = newAttachment.FileId;
                                    productFile.FileVersion = newAttachment.FileVersion;
                                    productFile.IsAttachment = newAttachment.IsAttachment;
                                    productFile.IsULPartnership = newAttachment.IsULPartnership;
                                    productFile.ModifiedBy = newAttachment.ModifiedBy;
                                    productFile.ModifiedById = newAttachment.ModifiedById;
                                    productFile.ModifiedDate = newAttachment.ModifiedDate;
                                    productFile.ProductId = newAttachment.ProductId;
                                    productFile.ProjectDataType = newAttachment.ProjectDataType;
                                    productFile.ProjectDataTypeId = newAttachment.ProjectDataTypeId;
                                    productFile.RegionIds = newAttachment.RegionIds;
                                    productFile.StateIds = newAttachment.StateIds;
                                    productFile.SoftwareRelease = newAttachment.SoftwareRelease;
                                    productFile.Weight = productFileWeight++;

                                    unitOfWork.ProductFileRepository.Insert(productFile);
                                }
                                await unitOfWork.SaveAsync();
                            }
                        }
                    }
                }
            }
            catch (Exception)
            {
            }
        }

        public async Task DeleteAsync(IUnitOfWork unitOfWork, int? id, int? manufacturerId)
        {
            var query = unitOfWork.AttachmentOrderRepository.GetAll();
            if (id != null)
            {
                query = query.Where(a => a.Id == id.Value);
            }

            if (manufacturerId != null)
            {
                query = query.Where(a => a.ManufacturerId == manufacturerId.Value);
            }

            unitOfWork.AttachmentOrderRepository.Delete(await query.ToListAsync());
            await unitOfWork.SaveAsync();
        }
    }
}
