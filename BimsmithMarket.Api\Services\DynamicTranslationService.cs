﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.DBModels.DynamicTranslationDbModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.DynamicTranslationDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;

using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Services
{
    public class DynamicTranslationService : IDynamicTranslationService
    {
        public async Task<PaginationListDto<TranslatableEntityListDto>> TranslatableEntityListAsync(int offset = 0, int count = 10)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                IQueryable<TranslatableEntity> query = unitOfWork.TranslatableEntityRepository.GetAll().AsNoTracking();
                int totalCount = await query.CountAsync();
                TranslatableEntityListDto[] items = await query.ProjectToType<TranslatableEntityListDto>()
                                       .Skip(offset)
                                       .Take(count)
                                       .ToArrayAsync();

                return new PaginationListDto<TranslatableEntityListDto>
                {
                    Count = totalCount,
                    Data = items
                };
            }
        }

        public async Task<PaginationListDto<TranslatableEntityFieldListDto>> TranslatableEntityFieldListAsync(int translatableEntityId, int offset = 0, int count = 10)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                IQueryable<TranslatableEntityField> query = unitOfWork.TranslatableEntityFieldRepository.GetAll()
                                      .Where(x => x.TranslatableEntityId == translatableEntityId)
                                      .AsNoTracking();
                int totalCount = await query.CountAsync();
                var items = await query.ProjectToType<TranslatableEntityFieldListDto>()
                                       .Skip(offset)
                                       .Take(count)
                                       .ToArrayAsync();

                return new PaginationListDto<TranslatableEntityFieldListDto>
                {
                    Count = totalCount,
                    Data = items
                };
            }
        }

        public async Task<PaginationListDto<ListDynamicTranslationDto>> DynamicTranslationListAsync(int translatableEntityId, int entityId, int offset = 0, int count = 10)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var query = unitOfWork.DynamicTranslationRepository.GetAll()
                                      .Where(x => x.TranslatableEntityField.TranslatableEntityId == translatableEntityId
                                               && x.EntityId == entityId)
                                      .AsNoTracking();
                int totalCount = await query.CountAsync();
                var items = await query.OrderBy(x => x.Id)
                    .ProjectToType<ListDynamicTranslationDto>()
                    .Skip(offset)
                    .Take(count)
                    .ToArrayAsync();

                return new PaginationListDto<ListDynamicTranslationDto>
                {
                    Count = totalCount,
                    Data = items
                };
            }
        }

        public async Task<EditDynamicTranslationDto> AddDynamicTranslationAsync(AddDynamicTranslationDto model, string userId)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                DynamicTranslation dynamicTranslation = model.Adapt<DynamicTranslation>();
                dynamicTranslation.CreatedById = userId;
                unitOfWork.DynamicTranslationRepository.Insert(dynamicTranslation);
                await unitOfWork.SaveAsync();

                return dynamicTranslation.Adapt<EditDynamicTranslationDto>();
            }
        }
        
        public async Task AddDynamicTranslationsAsync(AddDynamicTranslationListDto model, string userId)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                foreach (var el in model.Items)
                {
                    var dynamicTranslation = el.Adapt<DynamicTranslation>();
                    var existingTranslation = unitOfWork.DynamicTranslationRepository.GetAll().FirstOrDefault(w => w.LanguageCode == el.LanguageCode && w.EntityId == el.EntityId && w.TranslatableEntityFieldId == el.TranslatableEntityFieldId);
                    
                    if (existingTranslation != null)
                    {
                        existingTranslation.ModifiedById = userId;
                        existingTranslation.ModifiedDate = DateTime.UtcNow;
                        existingTranslation.Value = el.Value;
                        unitOfWork.DynamicTranslationRepository.Edit(existingTranslation);
                    }
                    else
                    {
                        dynamicTranslation.CreatedById = userId;
                        dynamicTranslation.ModifiedDate = DateTime.UtcNow;
                        unitOfWork.DynamicTranslationRepository.Insert(dynamicTranslation);
                    }
                }
                
                await unitOfWork.SaveAsync();
            }
        }

        public async Task<EditDynamicTranslationDto> EditDynamicTranslationAsync(EditDynamicTranslationDto model, string userId)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                DynamicTranslation dynamicTranslation = await unitOfWork.DynamicTranslationRepository.GetByIdAsync(model.Id);

                if (dynamicTranslation == null)
                    throw new InvalidInputException("Dynamic translation not found");

                model.Adapt(dynamicTranslation);
                dynamicTranslation.ModifiedDate = DateTime.UtcNow;
                dynamicTranslation.ModifiedById = userId;
                unitOfWork.DynamicTranslationRepository.Edit(dynamicTranslation);
                await unitOfWork.SaveAsync();

                return dynamicTranslation.Adapt<EditDynamicTranslationDto>();
            }
        }

        public async Task<EditDynamicTranslationDto> GetDynamicTranslationAsync(int id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                DynamicTranslation dynamicTranslation = await unitOfWork.DynamicTranslationRepository.GetByIdAsync(id);

                if (dynamicTranslation == null)
                    throw new DbItemNotFoundException("Dynamic translation not found");

                return dynamicTranslation.Adapt<EditDynamicTranslationDto>();
            }
        }

        public async Task DeleteDynamicTranslationAsync(int id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                DynamicTranslation dynamicTranslation = await unitOfWork.DynamicTranslationRepository.GetByIdAsync(id);

                if (dynamicTranslation == null)
                    throw new InvalidInputException("Dynamic translation not found");

                unitOfWork.DynamicTranslationRepository.Delete(dynamicTranslation);
                await unitOfWork.SaveAsync();
            }
        }
    }
}