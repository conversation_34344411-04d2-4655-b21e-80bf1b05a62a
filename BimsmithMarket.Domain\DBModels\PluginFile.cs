﻿using BIMsmithMarket.Domain.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class PluginFile : BaseEntity
    {
        [StringLength(200)]
        public string Info { get; set; }

        [Required]
        public int FileId { get; set; }

        public string Version { get; set; }

        public string Updates { get; set; }

        public PluginType PluginType { get; set; }

        /// ------------------------------------------

        [<PERSON><PERSON><PERSON>("FileId")]
        public virtual File File { get; set; }
    }
}