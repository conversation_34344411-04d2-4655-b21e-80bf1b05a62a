﻿using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Dto
{
    public class AddCustomCategoryIconDto
    {
        [Required]
        public string IconUrl { get; set; }

        [Required]
        public int CategoryId { get; set; }

        [Required]
        public int ManufacturerId { get; set; }
    }

    public class EditCustomCategoryIconDto
    {
        [Required]
        public int Id { get; set; }

        [Required]
        public string IconUrl { get; set; }
    }

    public class AdminListCustomCategoryIconDto
    {
        public int Id { get; set; }

        public string IconUrl { get; set; }

        public int CategoryId { get; set; }

        public int ManufacturerId { get; set; }
    }

    public class PublicListCustomCategoryIconDto
    {
        public string IconUrl { get; set; }

        public int CategoryId { get; set; }

        public int ManufacturerId { get; set; }
    }
}