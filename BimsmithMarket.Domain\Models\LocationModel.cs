﻿using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Models;

public class LocationModel
{
    public class Country
    {
        public int id { get; set; }
        public string name { get; set; }
        public bool isActive { get; set; }
        public Image image { get; set; }
        public string code { get; set; }
    }

    public class Image
    {
        public int id { get; set; }
        public string url { get; set; }
    }

    public class Root
    {
        public int id { get; set; }
        public string name { get; set; }
        public bool isActive { get; set; }
        public List<Country> countries { get; set; }
    }
}