﻿@using System.Configuration;
@using BIMsmithMarket.Core.Helpers
@model BIMsmithBlog.Models.CommonModel


<header class="header-wrap">
    <div class="header">
        <a href="#" class="menu"><span></span><span></span><span></span>menu</a>
        <div class="top clear">
            <a class="logo" href="@ConfigurationHelper.GetValue("BimsmithUrl")" target="_blank"><img src="~/images/logo.png" alt=""></a>
            <div class="sign-in">
                <div style="display: none;" id="user-name"></div>
                <div style="display: none;" id="head-drop" class="head-drop">
                    <div class="av">
                        <img id="profile-image" alt="" src="/images/img01.jpg" height="383" width="383">
                        <div id="settings-name" class="name"></div>
                    </div>
                    <div class="links">
                        <a href="@ConfigurationHelper.GetValue("MarketUrl")/">Market</a>
                        <a href="@ConfigurationHelper.GetValue("ForgeUrl")/">forge</a>
                        <a href="@ConfigurationHelper.GetValue("BimsmithUrl")/NewMyBIMSmith/settings">MyBIMsmith</a>
                    </div>
                </div>
                <a id="logout-button" style="display: none;" class="def-btn logout-button" href="javascript:void(0)">Log out</a>
                <a id="login-button" style="display: inline-block;" class="def-btn" href="@ConfigurationHelper.GetValue("BimsmithUrl")/Account/Login/">Log in</a>
                <a id="signup-button" style="display: inline-block;" class="g-btn" href="@ConfigurationHelper.GetValue("BimsmithUrl")/Account/Register">Sign up</a>
            </div>
        </div>

        <div class="bot clear">
            <ul class="links clear">
                <li class="active"><a href="/">All</a></li>

                @foreach (var category in Model.Categories)
                {
                    if (category.IsActive)
                    {
                        <li class="active"><a href="/blog/category/@category.VanityId">@category.Name</a></li>
                    }
                    else
                    {
                        <li><a href="/blog/category/@category.VanityId">@category.Name</a></li>
                    }
                }
            </ul>
            <div class="search">
                <i class="fa fa-search"></i>
                <input type="search">
            </div>
        </div>
    </div>
</header>
<div class="main">

    <div class="subscribe-content">
        <h3>Stay up to date on the latest in</h3>
        <h1>BIM, Revit, & Architecture</h1>
        <h3 class="grey">Join our discussion about the tools and people building the world around us.</h3>
        <div class="footer">
            <div class="clear">
                @using (Html.BeginForm("Subscribe", "Blog", FormMethod.Post, new { id = "logoutForm" }))
                {
                    <div class="sub clear">
                        <button type="submit">Subscribe</button>
                        <input type="email" name="email" placeholder="Enter your email here" required>
                    </div>
                }
            </div>
        </div>
    </div>

</div>