﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.DBModels.DynamicTranslationDbModels;
using BIMsmithMarket.Domain.DBModels.PaymentDbModels;
using BIMsmithMarket.Domain.DBModels.RevitProcessing;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace BIMsmithMarket.DataLayer.Context
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser, ApplicationRole, string, ApplicationUserClaim, ApplicationUserRole, ApplicationUserLogin, ApplicationRoleClaim, ApplicationUserToken>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        {

        }

        #region tables
        public virtual DbSet<Manufacturer> Manufacturers { get; set; }
        public virtual DbSet<Photo> Photos { get; set; }
        public virtual DbSet<Product> Products { get; set; }
        public virtual DbSet<RelatedProduct> RelatedProducts { get; set; }
        public virtual DbSet<Category> Categories { get; set; }
        public virtual DbSet<KeyStat> KeyStats { get; set; }
        public virtual DbSet<CategoryKeyStat> CategoryKeyStats { get; set; }
        public virtual DbSet<File> Files { get; set; }
        public virtual DbSet<ProductFile> ProductFiles { get; set; }
        public virtual DbSet<ProductPhoto> ProductPhotoes { get; set; }
        public virtual DbSet<ProductSample> ProductSamples { get; set; }
        public virtual DbSet<ProductLine> ProductLines { get; set; }
        public virtual DbSet<ProductStats> ProductStats { get; set; }
        public virtual DbSet<ProductRating> ProductRatings { get; set; }
        public virtual DbSet<ProductCertificate> ProductCertificates { get; set; }
        public virtual DbSet<ProductCategory> ProductCategories { get; set; }
        public virtual DbSet<QualityItem> QualityItems { get; set; }
        public virtual DbSet<ProductQualityItem> ProductQualityItems { get; set; }
        public virtual DbSet<UserBIMsmithManufacturer> UserBIMsmithManufacturers { get; set; }
        public virtual DbSet<ManufacturerAdminUser> ManufacturerAdminUsers { get; set; }
        public virtual DbSet<UserBIMsmithLLManufacturer> UserBIMsmithLLManufacturers { get; set; }
        public virtual DbSet<UserBIMsmithLTManufacturer> UserBIMsmithLTManufacturers { get; set; }
        public virtual DbSet<UserBIMsmithBIMManufacturer> UserBIMsmithBIMManufacturers { get; set; }
        public virtual DbSet<UserBIMsmithProduct> UserBIMsmithProducts { get; set; }
        public virtual DbSet<Address> Addresses { get; set; }
        public virtual DbSet<ReportItem> ReportItems { get; set; }
        public virtual DbSet<Cisfb> Cisfbs { get; set; }
        public virtual DbSet<ProductCisfb> ProductCisfbs { get; set; }
        public virtual DbSet<Masterformat> Masterformats { get; set; }
        public virtual DbSet<Omniclass> Omniclasses { get; set; }
        public virtual DbSet<Uniclass> Uniclasses { get; set; }
        public virtual DbSet<ProductUniclass> ProductUniclasses { get; set; }
        public virtual DbSet<Uniformat> Uniformats { get; set; }
        public virtual DbSet<ProductMasterformat> ProductMasterformats { get; set; }
        public virtual DbSet<ProductOmniclass> ProductOmniclasses { get; set; }
        public virtual DbSet<ProductUniformat> ProductUniformats { get; set; }
        public virtual DbSet<KeyStatUnit> KeyStatUnits { get; set; }
        public virtual DbSet<KeyStatValueList> KeyStatValueLists { get; set; }
        public virtual DbSet<KeyStatUnitRelation> KeyStatUnitRelations { get; set; }
        public virtual DbSet<Localization> Localizations { get; set; }
        public virtual DbSet<News> News { get; set; }
        public virtual DbSet<NewsCategory> NewsCategories { get; set; }
        public virtual DbSet<SearchHistory> SearchHistories { get; set; }
        public virtual DbSet<Setting> Settings { get; set; }
        public virtual DbSet<Synonym> Synonyms { get; set; }
        public virtual DbSet<BlogPost> BlogPosts { get; set; }
        public virtual DbSet<BlogTargetType> BlogTargetTypes { get; set; }
        public virtual DbSet<BlogCategoryTargetType> BlogCategoryTargetTypes { get; set; }
        public virtual DbSet<BlogCategory> BlogCategories { get; set; }
        public virtual DbSet<BlogComment> BlogComments { get; set; }
        public virtual DbSet<BlogMentionedEntry> BlogMentionedEntries { get; set; }
        public virtual DbSet<EmailSubscriber> EmailSubscribers { get; set; }
        public virtual DbSet<ProductLineFile> ProductLineFiles { get; set; }
        public virtual DbSet<ProductLineCertificate> ProductLineCertificates { get; set; }
        public virtual DbSet<ProductLineQualityItem> ProductLineQualityItems { get; set; }
        public virtual DbSet<ProductLineStats> ProductLineStats { get; set; }
        public virtual DbSet<ManufacturerFile> ManufacturerFiles { get; set; }
        public virtual DbSet<ManufacturerPhoto> ManufacturerPhotoes { get; set; }
        public virtual DbSet<ProjectDataType> ProjectDataTypes { get; set; }
        public virtual DbSet<Starter> Starters { get; set; }
        public virtual DbSet<PluginFile> PluginFiles { get; set; }
        public virtual DbSet<Company> Companies { get; set; }
        public virtual DbSet<ULStandardNumber> ULStandardNumbers { get; set; }
        public virtual DbSet<ULRatingSystemSustainableCredit> ULRatingSystemSustainableCredits { get; set; }
        public virtual DbSet<ULCertificate> ULCertificates { get; set; }
        public virtual DbSet<VanityHistory> VanityHistories { get; set; }
        public virtual DbSet<AttachmentOrder> AttachmentOrders { get; set; }
        public virtual DbSet<Event> Events { get; set; }
        public virtual DbSet<Announcement> Announcements { get; set; }
        public virtual DbSet<HelpCategory> HelpCategories { get; set; }
        public virtual DbSet<HelpArticle> HelpArticles { get; set; }
        public virtual DbSet<Detail> Details { get; set; }
        public virtual DbSet<DetailApplication> DetailApplications { get; set; }
        public virtual DbSet<DetailDetailApplication> DetailDetailApplications { get; set; }
        public virtual DbSet<DetailRating> DetailRatings { get; set; }
        public virtual DbSet<DetailFile> DetailFiles { get; set; }
        public virtual DbSet<DetailPhoto> DetailPhotoes { get; set; }
        public virtual DbSet<ProductDetail> ProductDetails { get; set; }
        public virtual DbSet<DetailMasterformat> DetailMasterformats { get; set; }
        public virtual DbSet<RelatedDetail> RelatedDetails { get; set; }
        public virtual DbSet<DetailScale> DetailScales { get; set; }
        public virtual DbSet<FeatureSetting> FeatureSettings { get; set; }
        public virtual DbSet<NewsTarget> NewsTargets { get; set; }
        public virtual DbSet<Price> Prices { get; set; }
        public virtual DbSet<PaymentPlan> PaymentPlans { get; set; }
        public virtual DbSet<ProductPrice> ProductPrices { get; set; }
        public virtual DbSet<UserPaidProduct> UserPaidProducts { get; set; }
        public virtual DbSet<ManufacturerAnnouncement> ManufacturerAnnouncements { get; set; }
        public virtual DbSet<HealthDashboardAccess> HealthDashboardAccesses { get; set; }
        public virtual DbSet<DynamicTranslation> DynamicTranslations { get; set; }
        public virtual DbSet<TranslatableEntity> TranslatableEntities { get; set; }
        public virtual DbSet<TranslatableEntityField> TranslatableEntityFields { get; set; }
        public virtual DbSet<StaticExcelFile> StaticExcelFiles { get; set; }
        public virtual DbSet<StaticExcelProductError> StaticExcelProductErrors { get; set; }
        public virtual DbSet<ManufacturerStyleFile> ManufacturerStyleFiles { get; set; }
        public virtual DbSet<ManufacturerAdditionalFile> ManufacturerAdditionalFiles { get; set; }
        public virtual DbSet<DropboxSetting> DropboxSettings { get; set; }
        public virtual DbSet<CustomCategoryIcon> CustomCategoryIcons { get; set; }
        public virtual DbSet<SalesRepresentative> SalesRepresentatives { get; set; }
        public virtual DbSet<ChangeLog> ChangeLogs { get; set; }
        public virtual DbSet<Note> Notes { get; set; }
        public virtual DbSet<NoteNotificationUser> NoteNotificationUsers { get; set; }
        public virtual DbSet<RequestPricingUser> RequestPricingUsers { get; set; }
        public virtual DbSet<RevitProcess> RevitProcesses { get; set; }
        public virtual DbSet<RevitProcessProduct> RevitProcessProducts { get; set; }
        public virtual DbSet<RevitJobRevitParameterMapping> RevitJobRevitParameterMappings { get; set; }
        public virtual DbSet<RevitJob> RevitJobs { get; set; }
        public virtual DbSet<RevitProcessProjectDataType> RevitProcessProjectDataTypes { get; set; }
        public virtual DbSet<RevitProcessRevitParameterMapping> RevitProcessRevitParameterMappings { get; set; }
        #endregion

        #region views
        public virtual DbSet<PublishedDisplayOrderView> PublishedDisplayOrderViews { get; set; }
        public virtual DbSet<ProductDisplayOrderView> ProductDisplayOrderViews { get; set; }
        #endregion

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<ULCertificate>()
                        .HasOne(m => m.Product)
                        .WithMany(t => t.ULCertificates)
                        .HasForeignKey(m => m.ProductId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ULRatingSystemSustainableCredit>()
                        .HasOne(m => m.Product)
                        .WithMany(t => t.ULRatingSystemsSustainableCredits)
                        .HasForeignKey(m => m.ProductId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ULStandardNumber>()
                        .HasOne(m => m.Product)
                        .WithMany(t => t.ULStandardNumbers)
                        .HasForeignKey(m => m.ProductId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Cisfb>()
                        .HasOne(m => m.Parent)
                        .WithMany(t => t.Children)
                        .HasForeignKey(m => m.ParentId)
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ProductCisfb>()
                        .HasKey(x => new { x.ProductId, x.CisfbId });

            modelBuilder.Entity<ProductCisfb>()
                        .HasOne(m => m.Product)
                        .WithMany(t => t.ProductCisfbs)
                        .HasForeignKey(m => m.ProductId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ProductRating>()
                        .HasOne(m => m.Product)
                        .WithMany(t => t.ProductRatings)
                        .HasForeignKey(m => m.ProductId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Product>()
                        .HasOne(m => m.Manufacturer)
                        .WithMany(t => t.Products)
                        .HasForeignKey(m => m.ManufacturerId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Product>()
                        .HasOne(m => m.CreatedBy)
                        .WithMany(t => t.CreatedProduct)
                        .HasForeignKey(m => m.CreatedById)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Product>()
                        .HasOne(m => m.ModifiedBy)
                        .WithMany(t => t.ModifiedProduct)
                        .HasForeignKey(m => m.ModifiedById)
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<RelatedProduct>()
                        .HasKey(x => new { x.ProductId, x.RelatedProductId });

            modelBuilder.Entity<RelatedProduct>()
                        .HasOne(m => m.Product)
                        .WithMany(t => t.RelatedProducts)
                        .HasForeignKey(m => m.ProductId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ProductCertificate>()
                        .HasOne(m => m.Product)
                        .WithMany(t => t.ProductCertificates)
                        .HasForeignKey(m => m.ProductId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ProductSample>()
                        .HasOne(m => m.Product)
                        .WithMany(t => t.ProductSamples)
                        .HasForeignKey(m => m.ProductId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<CategoryKeyStat>()
                        .HasKey(x => new { x.CategoryId, x.KeyStatId });

            modelBuilder.Entity<CategoryKeyStat>()
                        .HasOne(m => m.Category)
                        .WithMany(t => t.CategoryKeyStats)
                        .HasForeignKey(m => m.CategoryId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Category>()
                        .HasOne(m => m.ParentCategory)
                        .WithMany(t => t.Subcategories)
                        .HasForeignKey(m => m.ParentCategoryId)
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Omniclass>()
                        .HasOne(m => m.Parent)
                        .WithMany(t => t.Children)
                        .HasForeignKey(m => m.ParentId)
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Uniclass>()
                        .HasOne(m => m.Parent)
                        .WithMany(t => t.Children)
                        .HasForeignKey(m => m.ParentId)
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Uniformat>()
                        .HasOne(m => m.Parent)
                        .WithMany(t => t.Children)
                        .HasForeignKey(m => m.ParentId)
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ProductMasterformat>()
                        .HasKey(x => new { x.ProductId, x.ExternalMasterformatId });

            modelBuilder.Entity<ProductMasterformat>()
                        .HasOne(m => m.Product)
                        .WithMany(t => t.ProductMasterformats)
                        .HasForeignKey(m => m.ProductId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ProductOmniclass>()
                        .HasKey(x => new { x.ProductId, x.OmniclassId });

            modelBuilder.Entity<ProductOmniclass>()
                        .HasOne(m => m.Product)
                        .WithMany(t => t.ProductOmniclasses)
                        .HasForeignKey(m => m.ProductId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ProductUniclass>()
                        .HasKey(x => new { x.ProductId, x.UniclassId });

            modelBuilder.Entity<ProductUniclass>()
                        .HasOne(m => m.Product)
                        .WithMany(t => t.ProductUniclasses)
                        .HasForeignKey(m => m.ProductId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ProductUniformat>()
                        .HasKey(x => new { x.ProductId, x.UniformatId });

            modelBuilder.Entity<ProductUniformat>()
                        .HasOne(m => m.Product)
                        .WithMany(t => t.ProductUniformats)
                        .HasForeignKey(m => m.ProductId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ProductCategory>()
                        .HasKey(x => new { x.ProductId, x.CategoryId });

            modelBuilder.Entity<ProductCategory>()
                        .HasOne(m => m.Product)
                        .WithMany(t => t.ProductCategories)
                        .HasForeignKey(m => m.ProductId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<KeyStatUnitRelation>()
                        .HasKey(x => new { x.FromUnitId, x.ToUnitId });

            modelBuilder.Entity<KeyStatUnitRelation>()
                        .HasOne(m => m.FromUnit)
                        .WithMany(t => t.FromUnitRelations)
                        .HasForeignKey(m => m.FromUnitId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<KeyStatUnitRelation>()
                        .HasOne(m => m.ToUnit)
                        .WithMany(t => t.ToUnitRelations)
                        .HasForeignKey(m => m.ToUnitId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);


            modelBuilder.Entity<ProductStats>()
                        .HasOne(m => m.KeyStatUnit)
                        .WithMany(t => t.ProductStats)
                        .HasForeignKey(m => m.KeyStatUnitId)
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ProductStats>()
                        .HasOne(m => m.ConvertKeyStatUnit)
                        .WithMany(t => t.ConvertProductStats)
                        .HasForeignKey(m => m.ConvertKeyStatUnitId)
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<KeyStatValueList>()
                        .HasOne(m => m.KeyStatUnit)
                        .WithMany(t => t.KeyStatValueList)
                        .HasForeignKey(m => m.KeyStatUnitId)
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<KeyStatValueList>()
                        .HasOne(m => m.ConvertKeyStatUnit)
                        .WithMany(t => t.ConvertKeyStatValueList)
                        .HasForeignKey(m => m.ConvertKeyStatUnitId)
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ProductLineFile>()
                        .HasOne(m => m.ProductLine)
                        .WithMany(t => t.ProductLineFiles)
                        .HasForeignKey(m => m.ProductLineId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ProductLineCertificate>()
                        .HasOne(m => m.ProductLine)
                        .WithMany(t => t.ProductLineCertificates)
                        .HasForeignKey(m => m.ProductLineId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ProductLineQualityItem>()
                        .HasKey(x => new { x.ProductLineId, x.QualityItemId });

            modelBuilder.Entity<ProductLineQualityItem>()
                        .HasOne(m => m.ProductLine)
                        .WithMany(t => t.ProductLineQualityItems)
                        .HasForeignKey(m => m.ProductLineId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ProductLineStats>()
                        .HasOne(m => m.ProductLine)
                        .WithMany(t => t.ProductLineStats)
                        .HasForeignKey(m => m.ProductLineId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ProductLineStats>()
                        .HasOne(m => m.KeyStatUnit)
                        .WithMany(t => t.ProductLineStats)
                        .HasForeignKey(m => m.KeyStatUnitId)
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ProductLineStats>()
                        .HasOne(m => m.ConvertKeyStatUnit)
                        .WithMany(t => t.ConvertProductLineStats)
                        .HasForeignKey(m => m.ConvertKeyStatUnitId)
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ManufacturerFile>()
                        .HasOne(m => m.Manufacturer)
                        .WithMany(t => t.ManufacturerFiles)
                        .HasForeignKey(m => m.ManufacturerId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<ManufacturerAdditionalFile>()
                        .HasOne(m => m.Manufacturer)
                        .WithMany(t => t.ManufacturerAdditionalFiles)
                        .HasForeignKey(m => m.ManufacturerId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<ManufacturerPhoto>()
                        .HasOne(m => m.Manufacturer)
                        .WithMany(t => t.ManufacturerPhotos)
                        .HasForeignKey(m => m.ManufacturerId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Price>()
                        .HasOne(m => m.Product)
                        .WithOne(t => t.Price)
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Product>()
                        .HasOne(m => m.Price)
                        .WithOne(t => t.Product)
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ManufacturerAnnouncement>()
                        .HasOne(m => m.Manufacturer)
                        .WithMany(a => a.ManufacturerAnnouncements)
                        .HasForeignKey(m => m.ManufacturerId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<ManufacturerAnnouncement>()
                        .HasOne(m => m.Announcement)
                        .WithMany(a => a.ManufacturerAnnouncements)
                        .HasForeignKey(m => m.AnnouncementId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ProductQualityItem>()
                        .HasKey(x => new { x.ProductId, x.QualityItemId });

            modelBuilder.Entity<ManufacturerAnnouncement>()
                        .HasOne(m => m.Announcement)
                        .WithMany(a => a.ManufacturerAnnouncements)
                        .HasForeignKey(m => m.AnnouncementId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<Detail>()
                        .HasMany(x => x.RelatedDetails)
                        .WithOne(x => x.Detail)
                        .HasForeignKey(x => x.DetailId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ApplicationUser>()
                        .HasMany(x => x.UserPaidProducts)
                        .WithOne(x => x.User)
                        .HasForeignKey(x => x.UserId)
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<AttachmentOrder>()
                        .HasOne(x => x.Manufacturer)
                        .WithMany(x => x.AttachmentOrders)
                        .HasForeignKey(x => x.ManufacturerId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Detail>()
                        .HasOne(x => x.Manufacturer)
                        .WithMany(x => x.Details)
                        .HasForeignKey(x => x.ManufacturerId)
                        .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<Manufacturer>()
                        .HasOne(x => x.Parent)
                        .WithMany(x => x.Children)
                        .HasForeignKey(x => x.ParentId)
                        .OnDelete(DeleteBehavior.NoAction);

            modelBuilder.Entity<ManufacturerAdminUser>()
                        .HasOne(x => x.Manufacturer)
                        .WithMany(x => x.ManufacturerAdminUsers)
                        .HasForeignKey(x => x.ManufacturerId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<ManufacturerStyleFile>()
                        .HasOne(x => x.Manufacturer)
                        .WithMany(x => x.ManufacturerStyleFiles)
                        .HasForeignKey(x => x.ManufacturerId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<StaticExcelFile>()
                        .HasOne(x => x.Manufacturer)
                        .WithMany(x => x.StaticExcelFiles)
                        .HasForeignKey(x => x.ManufacturerId)
                        .OnDelete(DeleteBehavior.SetNull);

            modelBuilder.Entity<UserBIMsmithBIMManufacturer>()
                        .HasOne(x => x.Manufacturer)
                        .WithMany(x => x.UserBIMsmithBIMManufacturers)
                        .HasForeignKey(x => x.ManufacturerId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<UserBIMsmithLLManufacturer>()
                        .HasOne(x => x.Manufacturer)
                        .WithMany(x => x.UserBIMsmithLLManufacturers)
                        .HasForeignKey(x => x.ManufacturerId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<UserBIMsmithLTManufacturer>(entity =>
            {
                entity.HasOne(x => x.Manufacturer)
                    .WithMany(x => x.UserBIMsmithLTManufacturers)
                    .HasForeignKey(x => x.ManufacturerId)
                    .IsRequired()
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(x => x.Product)
                    .WithMany(x => x.UserBIMsmithLTManufacturers)
                    .HasForeignKey(x => x.ProductId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            modelBuilder.Entity<UserBIMsmithManufacturer>()
                        .HasOne(x => x.Manufacturer)
                        .WithMany(x => x.UserBIMsmithManufacturers)
                        .HasForeignKey(x => x.ManufacturerId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<CustomCategoryIcon>(entity =>
            {
                entity.HasOne(x => x.Category)
                    .WithMany(x => x.CustomCategoryIcons)
                    .HasForeignKey(x => x.CategoryId)
                    .IsRequired()
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(x => x.Manufacturer)
                   .WithMany(x => x.CustomCategoryIcons)
                   .HasForeignKey(x => x.ManufacturerId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<SalesRepresentative>()
                        .HasOne(x => x.Manufacturer)
                        .WithMany(x => x.SalesRepresentatives)
                        .HasForeignKey(x => x.ManufacturerId)
                        .IsRequired()
                        .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Note>()
                .HasMany(x => x.NotificationList)
                .WithOne(x => x.Note)
                .HasForeignKey(x => x.NoteId)
                .IsRequired()
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<ProductFile>(entry =>
            {
                entry.ToTable("ProductFiles", tb => tb.HasTrigger("trProductFilesUpdate"));
            });

            modelBuilder.Entity<RequestPricingUser>(entity =>
            {
                entity.HasOne(x => x.Product)
                    .WithMany(x => x.RequestPricingUsers)
                    .HasForeignKey(x => x.ProductId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(x => x.Manufacturer)
                   .WithMany(x => x.RequestPricingUsers)
                   .HasForeignKey(x => x.ManufacturerId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<RevitProcess>(entity =>
            {
                entity.HasOne(x => x.Manufacturer)
                    .WithMany(x => x.RevitProcesses)
                    .HasForeignKey(x => x.ManufacturerId)
                    .IsRequired()
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasMany(x => x.RevitParameterMappings)
                    .WithOne(x => x.RevitProcess)
                    .HasForeignKey(x => x.RevitProcessId)
                    .IsRequired()
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<RevitProcessProduct>(entity =>
            {
                entity.HasOne(x => x.RevitProcess)
                    .WithMany(x => x.RevitProcessProducts)
                    .HasForeignKey(x => x.RevitProcessId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(x => x.Product)
                    .WithMany(x => x.RevitProcessProducts)
                    .HasForeignKey(x => x.ProductId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<RevitProcessProjectDataType>(entity =>
            {
                entity.HasOne(x => x.RevitProcess)
                    .WithMany(x => x.RevitProcessProjectDataTypes)
                    .HasForeignKey(x => x.RevitProcessId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(x => x.ProjectDataType)
                    .WithMany(x => x.RevitProcessProjectDataTypes)
                    .HasForeignKey(x => x.ProjectDataTypeId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<RevitJob>(entity =>
            {
                entity.HasOne(x => x.RevitProcess)
                    .WithMany(x => x.RevitJobs)
                    .HasForeignKey(x => x.RevitProcessId)
                    .IsRequired()
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(x => x.ProductFile)
                    .WithMany(x => x.RevitJobs)
                    .HasForeignKey(x => x.ProductFileId)
                    .IsRequired()
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(x => x.ProjectDataType)
                    .WithMany(x => x.RevitJobs)
                    .HasForeignKey(x => x.ProjectDataTypeId)
                    .IsRequired()
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasMany(x => x.RevitJobRevitParameterMappings)
                    .WithOne(x => x.RevitJob)
                    .HasForeignKey(x => x.RevitJobId)
                    .IsRequired()
                    .OnDelete(DeleteBehavior.Cascade);
            });

            //views
            modelBuilder.Entity<PublishedDisplayOrderView>()
                .ToView("dbo.PublishedDisplayOrderView")
                .HasKey(t => t.ProductId);

            modelBuilder.Entity<ProductDisplayOrderView>()
                .ToView("dbo.ProductDisplayOrderView")
                .HasKey(t => t.ProductId);

            base.OnModelCreating(modelBuilder);

            //Identity tables
            modelBuilder.Entity<ApplicationUser>().ToTable("Users");
            modelBuilder.Entity<ApplicationRole>().ToTable("Roles");
            modelBuilder.Entity<ApplicationUserRole>(entity =>
            {
                entity.HasOne(x => x.User)
                   .WithMany(x => x.Roles)
                   .HasForeignKey(x => x.UserId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(x => x.Role)
                   .WithMany(x => x.UserRoles)
                   .HasForeignKey(x => x.RoleId)
                   .IsRequired()
                   .OnDelete(DeleteBehavior.Cascade);

                entity.ToTable("UserRoles");
            });
            modelBuilder.Entity<ApplicationUserClaim>().ToTable("UserClaims");
            modelBuilder.Entity<ApplicationUserLogin>().ToTable("UserLogins");
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                optionsBuilder
                    .UseLazyLoadingProxies()
                    .UseSqlServer(ConfigurationHelper.GetValue("ConnectionStrings:MarketDBConnection"));

#if DEBUG
                optionsBuilder = optionsBuilder.LogTo(message => Debug.WriteLine(message), LogLevel.Information);
#endif
            }
            base.OnConfiguring(optionsBuilder);
        }
    }
}