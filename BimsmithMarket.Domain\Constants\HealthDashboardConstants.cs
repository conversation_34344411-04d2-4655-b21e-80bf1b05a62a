﻿namespace BIMsmithMarket.Domain.Constants
{
    public static class HealthDashboardConstants
    {
        #region excel captions
        public const string ExcelProductIdCaption = "Product/Product Line/Manufacturer Id";

        public const string ExcelProductNameCaption = "Product Name";

        public const string ExcelAdminPanelProductPageUrlCaption = "Product Page URL";

        public const string ExcelMarketProductPageUrlCaption = "Market Product Page URL";

        public const string ExcelAttachmentTypeCaption = "Type of Attachment";

        public const string ExcelAttachmentSourceUrlCaption = "Attachment Source URL";

        public const string ExcelAttachmentSourceStatusCaption = "Source URL status";

        public const string ExcelFileIdCaption = "File Id";

        public const string ExcelDescriptionCaption = "Description";

        public const string ExcelRevitUploadUrlCaption = "Revit Upload Url";

        public const string ExcelRevitFileNameCaption = "Revit File Name";

        public const string ExcelZipFilesCaption = "Zip files";
        #endregion

        #region dashboard captions
        public const string TotalBlankPhoneNumberDashboardCaption = "TOTAL BLANK PHONE NUMBERS";

        public const string TotalBrokenLinksDashboardCaption = "TOTAL BROKEN LINKS";

        public const string TotalBlankDesriptionsDashboardCaption = "TOTAL BLANK DESCRIPTIONS";

        public const string TotalNoRevitFilesDashboardCaption = "TOTAL NO REVIT FILES";

        public const string TotalMissingExtensionsDashboardCaption = "TOTAL MISSING EXTENSIONS";

        public const string TotalNumberOfProductsWithZipFilesDashboardCaption = "TOTAL NUMBER OF PRODUCTS WITH ZIP FILES";
        #endregion
    }
}