﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using System.Threading.Tasks;
using AddProjectDataTypeModel = BIMsmithMarket.Domain.Models.AddProjectDataTypeModel;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("api/[controller]/[action]/{id?}")]
    public class ProjectDataTypeController : BaseApiController
    {
        private readonly IProjectDataTypeService _projectDataTypeService;

        public ProjectDataTypeController(IProjectDataTypeService projectDataTypeService)
        {
            _projectDataTypeService = projectDataTypeService;
        }

        /// <summary>
        /// Gets the specified identifier.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("Get")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Get(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _projectDataTypeService.GetAsync(unitOfWork, id));
            }
        }

        /// <summary>
        /// Lists this instance.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("List")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> List([FromQuery] bool onlyParents = true)
        {
            using (var unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _projectDataTypeService.ListAsync(unitOfWork, onlyParents));
            }
        }

        /// <summary>
        /// Adds the specified model.
        /// </summary>
        /// <param name="model">The model.</param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Add")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Add([FromBody] AddProjectDataTypeModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var response = await _projectDataTypeService.AddAsync(unitOfWork, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), model);

                CacheHelper.ClearSpecificCache("*/api/ProjectDataType/List*");

                return Ok(response);
            }
        }

        /// <summary>
        /// Updates the specified model.
        /// </summary>
        /// <param name="model">The model.</param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Edit")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Edit([FromBody] EditProjectDataTypeModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var response = await _projectDataTypeService.EditAsync(unitOfWork, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), model);

                CacheHelper.ClearSpecificCache("*/api/ProjectDataType/List*");

                return Ok(response);
            }
        }

        /// <summary>
        /// Deletes the specified identifier.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("Delete")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Delete(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                await _projectDataTypeService.DeleteAsync(unitOfWork, id);
                return Ok();
            }
        }

        /// <summary>
        /// Checks if entity with the specified identifier has relation.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("HasRelations")]
        public async Task<IActionResult> HasRelations(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _projectDataTypeService.HasRelationsAsync(unitOfWork, id));
            }
        }

        /// <summary>
        /// Gets public list.
        /// Used by front app
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("PublicList")]
        public async Task<IActionResult> PublicList()
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _projectDataTypeService.PublicListAsync(unitOfWork));
            }
        }

        /// <summary>
        /// Lists Revit versions available for processing.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("RevitProcessingList")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        [Produces<ProjectDataTypeDto[]>]
        public async Task<IActionResult> RevitProcessingList()
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _projectDataTypeService.RevitProcessingListAsync(unitOfWork));
        }

        #region private methods
        private async Task<bool> HasRelationsAsync(IUnitOfWork unitOfWork, ProjectDataType projectDataType)
        {
            var hasManufacturerFiles = await unitOfWork.ManufacturerFileRepository.GetAll().AnyAsync(x => x.ProjectDataTypeId == projectDataType.Id);
            var hasProductFiles = await unitOfWork.ProductFileRepository.GetAll().AnyAsync(x => x.ProjectDataTypeId == projectDataType.Id);
            var hasProductLineFiles = await unitOfWork.ProductLineFileRepository.GetAll().AnyAsync(x => x.ProjectDataTypeId == projectDataType.Id);
            var hasRelations = hasManufacturerFiles || hasProductFiles || hasProductLineFiles;
            return hasRelations;
        }
        #endregion
    }
}