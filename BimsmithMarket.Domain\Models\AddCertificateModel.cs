﻿using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Models
{
    public class AddCertificateModel
    {
        [Required]
        [StringLength(200)]
        public string Name { get; set; }

        public string Description { get; set; }

        public string Url { get; set; }

        public string IconUrl { get; set; }
    }

    public class EditCertificateModel : AddCertificateModel
    {
        [Required]
        public int Id { get; set; }
    }
}