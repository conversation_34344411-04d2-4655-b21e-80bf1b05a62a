﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class ProductLineCertificate
    {
        [Key]
        public int ProductLineCertificateId { get; set; }

        public int ProductLineId { get; set; }

        public int? ExternalCertificateId { get; set; }

        public string CreatedById { get; set; }

        public DateTime CreatedDate { get; set; }

        /// ------------------------------------------
        [ForeignK<PERSON>("CreatedById")]
        public virtual ApplicationUser CreatedBy { get; set; }

        [ForeignKey("ProductLineId")]
        public virtual ProductLine ProductLine { get; set; }
    }
}