﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace BIMsmithMarket.Services.Search.FullTextSearch
{
    /// <summary>
    /// Class collecting ranked results
    /// </summary>
    public class RankedResults
    {
        public class ProductRank
        {
            public int Id { get; set; }
            public int MajorRank { get; set; }
            public float Score { get; set; }
            public float ScoreCap { get; set; }
            public string Query { get; set; }
            public bool Excluded { get; set; }
            public float Weight { get; set; }
            public float Drift { get; set; }
            public HashSet<string> Explain { get; set; } = new HashSet<string>();
            public HashSet<int> MatchedTokens { get; set; } = new HashSet<int>();

            public float FinalScore => Math.Min(ScoreCap, Score + Drift);
        }

        private Dictionary<int, ProductRank> _ranks = new Dictionary<int, ProductRank>();
        private bool _isNarrowedDown = false;

        public int SecondaryRankCutoff = 0;

        public void AddScore(int id, float score, string explain)
        {
            var rankItem = GetOrCreateRank(id);
            rankItem.Score += score;
            if (explain != null)
                rankItem.Explain.Add(explain);
        }

        public void SetScore(int id, float score, string explain)
        {
            var rankItem = GetOrCreateRank(id);
            rankItem.Score = score;
            if (explain != null)
                rankItem.Explain.Add(explain);
        }

        public void AddScore(IEnumerable<int> ids, float score, string explain)
        {
            foreach (var id in ids)
            {
                AddScore(id, score, explain);
            }
        }

        public void Query(int id, IEnumerable<QueryToken> tokens)
        {
            GetOrCreateRank(id).Query = tokens != null ? string.Join(" ", tokens.Select(t => t.Original)) : GetOrCreateRank(id).Query;
            GetOrCreateRank(id).MatchedTokens.UnionWith(tokens?.Select(t => t.No) ?? new int[0]);
        }

        public void MajorRankAndScore(int id, int rank, float score, string explain)
        {
            if (score != 0 && explain != null)
            {
                explain += $" (+{score:0.##})";
            }
            MajorRank(id, rank, explain);
            AddScore(id, score, null);
        }

        public void MajorRankAndScore(IEnumerable<int> id, int rank, float score, string explain, IEnumerable<QueryToken> tokens)
        {
            if (score != 0 && explain != null)
            {
                explain += $" (+{score:0.##})";
            }
            MajorRank(id, rank, explain);
            AddScore(id, score, null);
            if (tokens != null)
            {
                foreach (var i in id)
                {
                    GetOrCreateRank(i).MatchedTokens.UnionWith(tokens?.Select(t => t.No) ?? new int[0]);
                }
            }
        }

        public void MajorRank(int id, int rank, string explain)
        {
            var rankItem = GetOrCreateRank(id);
            if (rank > rankItem.MajorRank)
            {
                rankItem.MajorRank = rank;
                if (explain != null)
                    explain += $"[R{rank}]";
            }
            if (explain != null)
                rankItem.Explain.Add(explain);
        }

        public void MajorRank(IEnumerable<int> ids, int rank, string explain)
        {
            foreach (var id in ids)
            {
                MajorRank(id, rank, explain);
            }
        }

        public void NarrowDown()
        {
            _isNarrowedDown = true;
        }

        private ProductRank GetOrCreateRank(int id)
        {
            if (!_ranks.ContainsKey(id))
            {
                _ranks.Add(id, new ProductRank { Id = id, Score = 0, Excluded = _isNarrowedDown, ScoreCap = float.PositiveInfinity, MajorRank = 0 });
            }
            return _ranks[id];
        }

        public void Exclude(int id)
        {
            GetOrCreateRank(id).Excluded = true;
        }

        public void ExcludeAll(IEnumerable<int> ids)
        {
            foreach (var id in ids)
            {
                Exclude(id);
            }
        }

        public void IncludeOnly<T>(IEnumerable<KeyValuePair<int, T>> kvp)
        {
            IncludeOnly(kvp.Select(d => d.Key));
        }

        public void CapScore(IEnumerable<int> ids, float score)
        {
            foreach (var id in ids)
            {
                GetOrCreateRank(id).ScoreCap = score;
            }
        }

        public bool IsExcluded(int id)
        {
            return GetOrCreateRank(id).Excluded;
        }

        public IEnumerable<int> PrimaryResultIds()
        {
            return _ranks.Where(r => r.Value.MajorRank > SecondaryRankCutoff && !r.Value.Excluded).Select(r => r.Key);
        }

        public void IncludeOnly(IEnumerable<int> ids)
        {
            var includes = new SortedSet<int>(ids);
            foreach (var rank in _ranks.Values)
            {
                if (!includes.Contains(rank.Id))
                {
                    rank.Excluded = true;
                }
            }
            foreach (var id in ids)
            {
                GetOrCreateRank(id);
            }
            NarrowDown();
        }

        private IEnumerable<ProductRank> IncludedResults()
        {
            return _ranks.Where(r => !r.Value.Excluded && r.Value.FinalScore > 0).Select(r => r.Value);
        }

        [Obsolete]
        public IEnumerable<ProductRank> GetOrderedResults()
        {
            return GetResults(r => true);
        }

        public IEnumerable<ProductRank> GetPrimaryResults()
        {
            return GetResults(r => r.MajorRank > SecondaryRankCutoff);
        }

        public IEnumerable<ProductRank> GetSecondaryResults()
        {
            return GetResults(r => r.MajorRank <= SecondaryRankCutoff);
        }

        private IEnumerable<ProductRank> GetResults(Func<ProductRank, bool> criteria)
        {
            var orderedResults = IncludedResults().Where(criteria).OrderByDescending(r => r.MajorRank).ThenByDescending(r => r.FinalScore).ThenBy(r => r.Id).Select(r => r);

            if (orderedResults.Count() >= 2000)
            {
                // SQL limit
                orderedResults = orderedResults.Take(2000);
            }

            return orderedResults;
        }

        public bool IsNarrowedDown()
        {
            return _isNarrowedDown;
        }

        public ProductRank Get(int id)
        {
            return GetOrCreateRank(id);
        }
    }
}
