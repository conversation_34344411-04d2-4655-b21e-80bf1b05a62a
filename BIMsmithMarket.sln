﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.2.32602.215
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Core", "Core", "{92C20B6C-A297-4F42-AEF9-7628EE54D38D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BIMsmithMarket.Core", "BIMsmithMarket.Core\BIMsmithMarket.Core.csproj", "{7F50DDBB-B94F-45CD-822D-78F26B0CD67A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "DAL", "DAL", "{145D5C38-316F-4D63-9C92-03643C6A9EC6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BIMsmithMarket.Domain", "BIMsmithMarket.Domain\BIMsmithMarket.Domain.csproj", "{A5D278C9-FA00-4E4F-B442-F5621502F3D8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BIMsmithMarket.Services", "BIMsmithMarket.Services\BIMsmithMarket.Services.csproj", "{2E9DAE1C-8132-4E00-8514-5410C65ABDE1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BIMsmithMarket.DataLayer", "BIMsmithMarket.DataLayer\BIMsmithMarket.DataLayer.csproj", "{916D9E68-40C5-4DB6-A601-E479397EA0D4}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{10835C6C-BA8A-400B-B369-87DA4F667977}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BIMsmithMarketApi.LoadTests", "BIMsmith.Market.Api.LoadTests\BIMsmithMarketApi.LoadTests.csproj", "{AA770DDE-7A9A-487F-9166-7ADFA744CF8F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "WebApi", "WebApi", "{39C869D7-965F-457C-8EC0-A84F2D4A0659}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BIMsmithMarket.Api", "BIMsmithMarket.Api\BIMsmithMarket.Api.csproj", "{B12B4865-98E6-45CC-9811-FE1F60AB1009}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "WebSite", "WebSite", "{52AE767F-608E-4FE0-B8B3-93736F82D5F7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BIMsmithBlog", "BIMsmithBlog\BIMsmithBlog.csproj", "{C5B570CD-5E62-4D2E-854D-F95CA963DFE5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BIMsmithNewsroom", "BIMsmithNewsroom\BIMsmithNewsroom.csproj", "{03E6B278-7F30-4F65-A2C2-AB63F4604FCD}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Dev|Any CPU = Dev|Any CPU
		Release|Any CPU = Release|Any CPU
		UAT|Any CPU = UAT|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{7F50DDBB-B94F-45CD-822D-78F26B0CD67A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7F50DDBB-B94F-45CD-822D-78F26B0CD67A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7F50DDBB-B94F-45CD-822D-78F26B0CD67A}.Dev|Any CPU.ActiveCfg = Dev|Any CPU
		{7F50DDBB-B94F-45CD-822D-78F26B0CD67A}.Dev|Any CPU.Build.0 = Dev|Any CPU
		{7F50DDBB-B94F-45CD-822D-78F26B0CD67A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7F50DDBB-B94F-45CD-822D-78F26B0CD67A}.Release|Any CPU.Build.0 = Release|Any CPU
		{7F50DDBB-B94F-45CD-822D-78F26B0CD67A}.UAT|Any CPU.ActiveCfg = UAT|Any CPU
		{7F50DDBB-B94F-45CD-822D-78F26B0CD67A}.UAT|Any CPU.Build.0 = UAT|Any CPU
		{A5D278C9-FA00-4E4F-B442-F5621502F3D8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A5D278C9-FA00-4E4F-B442-F5621502F3D8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A5D278C9-FA00-4E4F-B442-F5621502F3D8}.Dev|Any CPU.ActiveCfg = Dev|Any CPU
		{A5D278C9-FA00-4E4F-B442-F5621502F3D8}.Dev|Any CPU.Build.0 = Dev|Any CPU
		{A5D278C9-FA00-4E4F-B442-F5621502F3D8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A5D278C9-FA00-4E4F-B442-F5621502F3D8}.Release|Any CPU.Build.0 = Release|Any CPU
		{A5D278C9-FA00-4E4F-B442-F5621502F3D8}.UAT|Any CPU.ActiveCfg = UAT|Any CPU
		{A5D278C9-FA00-4E4F-B442-F5621502F3D8}.UAT|Any CPU.Build.0 = UAT|Any CPU
		{2E9DAE1C-8132-4E00-8514-5410C65ABDE1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2E9DAE1C-8132-4E00-8514-5410C65ABDE1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2E9DAE1C-8132-4E00-8514-5410C65ABDE1}.Dev|Any CPU.ActiveCfg = Dev|Any CPU
		{2E9DAE1C-8132-4E00-8514-5410C65ABDE1}.Dev|Any CPU.Build.0 = Dev|Any CPU
		{2E9DAE1C-8132-4E00-8514-5410C65ABDE1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2E9DAE1C-8132-4E00-8514-5410C65ABDE1}.Release|Any CPU.Build.0 = Release|Any CPU
		{2E9DAE1C-8132-4E00-8514-5410C65ABDE1}.UAT|Any CPU.ActiveCfg = UAT|Any CPU
		{2E9DAE1C-8132-4E00-8514-5410C65ABDE1}.UAT|Any CPU.Build.0 = UAT|Any CPU
		{916D9E68-40C5-4DB6-A601-E479397EA0D4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{916D9E68-40C5-4DB6-A601-E479397EA0D4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{916D9E68-40C5-4DB6-A601-E479397EA0D4}.Dev|Any CPU.ActiveCfg = Dev|Any CPU
		{916D9E68-40C5-4DB6-A601-E479397EA0D4}.Dev|Any CPU.Build.0 = Dev|Any CPU
		{916D9E68-40C5-4DB6-A601-E479397EA0D4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{916D9E68-40C5-4DB6-A601-E479397EA0D4}.Release|Any CPU.Build.0 = Release|Any CPU
		{916D9E68-40C5-4DB6-A601-E479397EA0D4}.UAT|Any CPU.ActiveCfg = UAT|Any CPU
		{916D9E68-40C5-4DB6-A601-E479397EA0D4}.UAT|Any CPU.Build.0 = UAT|Any CPU
		{AA770DDE-7A9A-487F-9166-7ADFA744CF8F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AA770DDE-7A9A-487F-9166-7ADFA744CF8F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AA770DDE-7A9A-487F-9166-7ADFA744CF8F}.Dev|Any CPU.ActiveCfg = Dev|Any CPU
		{AA770DDE-7A9A-487F-9166-7ADFA744CF8F}.Dev|Any CPU.Build.0 = Dev|Any CPU
		{AA770DDE-7A9A-487F-9166-7ADFA744CF8F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AA770DDE-7A9A-487F-9166-7ADFA744CF8F}.Release|Any CPU.Build.0 = Release|Any CPU
		{AA770DDE-7A9A-487F-9166-7ADFA744CF8F}.UAT|Any CPU.ActiveCfg = UAT|Any CPU
		{AA770DDE-7A9A-487F-9166-7ADFA744CF8F}.UAT|Any CPU.Build.0 = UAT|Any CPU
		{B12B4865-98E6-45CC-9811-FE1F60AB1009}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B12B4865-98E6-45CC-9811-FE1F60AB1009}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B12B4865-98E6-45CC-9811-FE1F60AB1009}.Dev|Any CPU.ActiveCfg = Dev|Any CPU
		{B12B4865-98E6-45CC-9811-FE1F60AB1009}.Dev|Any CPU.Build.0 = Dev|Any CPU
		{B12B4865-98E6-45CC-9811-FE1F60AB1009}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B12B4865-98E6-45CC-9811-FE1F60AB1009}.Release|Any CPU.Build.0 = Release|Any CPU
		{B12B4865-98E6-45CC-9811-FE1F60AB1009}.UAT|Any CPU.ActiveCfg = UAT|Any CPU
		{B12B4865-98E6-45CC-9811-FE1F60AB1009}.UAT|Any CPU.Build.0 = UAT|Any CPU
		{C5B570CD-5E62-4D2E-854D-F95CA963DFE5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C5B570CD-5E62-4D2E-854D-F95CA963DFE5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C5B570CD-5E62-4D2E-854D-F95CA963DFE5}.Dev|Any CPU.ActiveCfg = Dev|Any CPU
		{C5B570CD-5E62-4D2E-854D-F95CA963DFE5}.Dev|Any CPU.Build.0 = Dev|Any CPU
		{C5B570CD-5E62-4D2E-854D-F95CA963DFE5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C5B570CD-5E62-4D2E-854D-F95CA963DFE5}.Release|Any CPU.Build.0 = Release|Any CPU
		{C5B570CD-5E62-4D2E-854D-F95CA963DFE5}.UAT|Any CPU.ActiveCfg = UAT|Any CPU
		{C5B570CD-5E62-4D2E-854D-F95CA963DFE5}.UAT|Any CPU.Build.0 = UAT|Any CPU
		{03E6B278-7F30-4F65-A2C2-AB63F4604FCD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{03E6B278-7F30-4F65-A2C2-AB63F4604FCD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{03E6B278-7F30-4F65-A2C2-AB63F4604FCD}.Dev|Any CPU.ActiveCfg = Dev|Any CPU
		{03E6B278-7F30-4F65-A2C2-AB63F4604FCD}.Dev|Any CPU.Build.0 = Dev|Any CPU
		{03E6B278-7F30-4F65-A2C2-AB63F4604FCD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{03E6B278-7F30-4F65-A2C2-AB63F4604FCD}.Release|Any CPU.Build.0 = Release|Any CPU
		{03E6B278-7F30-4F65-A2C2-AB63F4604FCD}.UAT|Any CPU.ActiveCfg = UAT|Any CPU
		{03E6B278-7F30-4F65-A2C2-AB63F4604FCD}.UAT|Any CPU.Build.0 = UAT|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{7F50DDBB-B94F-45CD-822D-78F26B0CD67A} = {92C20B6C-A297-4F42-AEF9-7628EE54D38D}
		{A5D278C9-FA00-4E4F-B442-F5621502F3D8} = {145D5C38-316F-4D63-9C92-03643C6A9EC6}
		{2E9DAE1C-8132-4E00-8514-5410C65ABDE1} = {92C20B6C-A297-4F42-AEF9-7628EE54D38D}
		{916D9E68-40C5-4DB6-A601-E479397EA0D4} = {145D5C38-316F-4D63-9C92-03643C6A9EC6}
		{AA770DDE-7A9A-487F-9166-7ADFA744CF8F} = {10835C6C-BA8A-400B-B369-87DA4F667977}
		{B12B4865-98E6-45CC-9811-FE1F60AB1009} = {39C869D7-965F-457C-8EC0-A84F2D4A0659}
		{C5B570CD-5E62-4D2E-854D-F95CA963DFE5} = {52AE767F-608E-4FE0-B8B3-93736F82D5F7}
		{03E6B278-7F30-4F65-A2C2-AB63F4604FCD} = {52AE767F-608E-4FE0-B8B3-93736F82D5F7}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {D60113CC-7442-41EF-A574-4B5E2EBB85A6}
	EndGlobalSection
EndGlobal
