﻿using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface INoteService
    {
        Task<AdminGetNoteDto> AddAsync(AddNoteDto model, string userId, IUnitOfWork unitOfWork);

        Task<AdminGetNoteDto> EditAsync(EditNoteDto model, string userId, IUnitOfWork unitOfWork);

        Task<AdminGetNoteDto> AdminGetAsync(int id, IUnitOfWork unitOfWork);

        Task<PaginationListDto<AdminListNoteDto>> AdminListAsync(
            IUnitOfWork unitOfWork,
            EntityType entityType,
            int entityId,
            bool? isActive = null,
            string query = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int offset = 0,
            int count = 10);

        Task<OperationResultDto> DeleteAsync(int id, IUnitOfWork unitOfWork);
    }
}