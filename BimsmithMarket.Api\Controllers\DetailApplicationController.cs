﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Models.ApplicationDetailModels;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// Detail Application Controller
    /// </summary>
    [Route("api/[controller]/[action]/{id?}")]
    public class DetailApplicationController : BaseApiController
    {
        /// <summary>
        /// Detail Application service
        /// </summary>
        private readonly IDetailApplicationService _detailApplicationService;

        public DetailApplicationController(IDetailApplicationService detailApplicationService)
        {
            _detailApplicationService = detailApplicationService;
        }

        /// <summary>
        /// Adds Detail Application
        /// </summary>
        /// <param name="model">Detail application creation model</param>
        /// <returns></returns> 
        [Authorize(Roles = DbConstants.AdminRole)]
        [HttpPost]
        public async Task<IActionResult> Add(AddDetailApplicationViewModel model)
        {
            var userId = AuthHelper.GetUserInfo(HttpContext.Request, ClaimTypes.NameIdentifier);
            var result = await _detailApplicationService.AddAsync(model, userId);
            return Ok(result);
        }

        /// <summary>
        /// Edits Detail Application
        /// </summary>
        /// <param name="model">Detail application edit model</param>
        /// <returns></returns>
        [Authorize(Roles = DbConstants.AdminRole)]
        [HttpPost]
        public async Task<IActionResult> Edit(EditDetailApplicationViewModel model)
        {
            try
            {
                var userId = AuthHelper.GetUserInfo(HttpContext.Request, ClaimTypes.NameIdentifier);
                var result = await _detailApplicationService.EditAsync(model, userId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message + " " + ex.InnerException);
            }
        }

        /// <summary>
        /// Gets Detail Application with identifier specified
        /// </summary>
        /// <param name="id">Detail application identifier</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> Get(int id)
        {
            var result = await _detailApplicationService.GetAsync(id);
            return Ok(result);
        }

        [HttpGet]
        public async Task<IActionResult> GetByVanityUrl(string vanityUrl)
        {
            var result = await _detailApplicationService.GetByVanityUrlAsync(vanityUrl);
            return Ok(result);
        }

        /// <summary>
        /// Deletes Detail Application with identifier
        /// </summary>
        /// <param name="id">Detail application identifier</param>
        /// <returns></returns>
        [Authorize(Roles = DbConstants.AdminRole)]
        [HttpDelete]
        public async Task<IActionResult> Delete(int id)
        {
            var result = await _detailApplicationService.DeleteAsync(id);
            return Ok(result);
        }

        /// <summary>
        /// Gets Detail Application list
        /// </summary>
        /// <param name="query"></param>
        /// <param name="count"></param>
        /// <param name="offset"></param>
        /// <returns></returns>
        [HttpGet]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> List(string query = null, int count = 10, int offset = 0)
        {
            var result = await _detailApplicationService.ListAsync(query, count, offset);
            return Ok(result);
        }
    }
}