﻿using Azure.Storage.Blobs.Models;
using Azure.Storage.Blobs.Specialized;
using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace BIMsmithMarket.Core.Providers
{
    public class PhotoProvider
    {
        private static readonly int _qualityCompressionPercentage = 70;

        public static async Task CreateProductPhotosAsync(Photo photo, Stream imageStream, string extension, string imageName = null, bool suppressAlphaChannel = true, bool isRevitPhoto = false)
        {
            AzureStorageService azureBlobProvider = new AzureStorageService(
                ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                ConfigurationHelper.GetValue("Environment"),
                bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                false);
            var photosContainer = azureBlobProvider.GetContainerByName(AzureStorageConstants.PhotosContainer);

            if (!FileHelper.IsVectorExtension(extension))
            {
                extension = ".png";
                if (suppressAlphaChannel)
                {
                    extension = ".jpg";
                }
            }

            if (imageName != null)
            {
                Regex rgx = new Regex("[^a-zA-Z0-9 -]");
                var normalImageName = rgx.Replace(imageName, "");
                imageName = normalImageName + photo.Id.ToString();
            }
            else
            {
                imageName = photo.Id.ToString();
            }

            var bigImageBlobName = string.Format("{0}_b{1}", imageName, extension);
            var middleImageBlobName = string.Format("{0}_m{1}", imageName, extension);
            var smallImageBlobName = string.Format("{0}_s{1}", imageName, extension);

            imageStream.Position = 0;

            if (FileHelper.IsVectorExtension(extension))
            {
                BlockBlobClient bigImageBlobUpload = photosContainer.GetBlockBlobClient(bigImageBlobName);
                using (var stream = await bigImageBlobUpload.OpenWriteAsync(true))
                {
                    imageStream.CopyTo(stream);
                }
                await bigImageBlobUpload.SetHttpHeadersAsync(new BlobHttpHeaders { ContentType = MimeTypeProvider.GetMimeType(extension) });
                photo.OriginalImgUrl = bigImageBlobUpload.Uri.ToString();
                photo.MiddleImgUrl = bigImageBlobUpload.Uri.ToString();
                photo.SmallImgUrl = bigImageBlobUpload.Uri.ToString();

                return;
            }

            Image originalImage = Image.FromStream(imageStream);

            double scaleBig = Math.Max(originalImage.Height, originalImage.Width) / 1000d; //max side is 1000px for full image
            double scaleMiddle = Math.Max(originalImage.Height, originalImage.Width) / 330d; //max side is 330px for middle image
            double scaleSmall = Math.Max(originalImage.Height, originalImage.Width) / 30d; //max side is 250px for small image
            if (isRevitPhoto)
            {
                scaleBig = Math.Max(originalImage.Height, originalImage.Width) / 32d; //max side is 32px for full image
                scaleMiddle = Math.Max(originalImage.Height, originalImage.Width) / 24d; //max side is 24px for preview image
                scaleSmall = Math.Max(originalImage.Height, originalImage.Width) / 16d; //max side is 16px for preview image
            }

            Image bigImage = null;

            if (scaleBig > 1) // need to reduce size of image
            {
                bigImage = new Bitmap(originalImage, new Size((int)(originalImage.Width / scaleBig), (int)(originalImage.Height / scaleBig)));
            }
            else
            {
                bigImage = new Bitmap(originalImage);
            }

            Image middleImage = new Bitmap(originalImage, new Size((int)(originalImage.Width / scaleMiddle), (int)(originalImage.Height / scaleMiddle)));

            Image smallImage = new Bitmap(originalImage, new Size((int)(originalImage.Width / scaleSmall), (int)(originalImage.Height / scaleSmall)));

            ImageCodecInfo jgpEncoder = GetEncoder(ImageFormat.Jpeg);

            // Create an Encoder object based on the GUID
            // for the Quality parameter category.
            var myEncoder = Encoder.Quality;

            // Create an EncoderParameters object.
            // An EncoderParameters object has an array of EncoderParameter
            // objects. In this case, there is only one
            // EncoderParameter object in the array.
            EncoderParameter bigImageEncoderParameter = new EncoderParameter(myEncoder, 90L);
            EncoderParameters bigImageEncoderParameters = new EncoderParameters(1);
            bigImageEncoderParameters.Param[0] = bigImageEncoderParameter;

            var bigImageWidth = bigImage.Width;
            var bigImageHeight = bigImage.Height;
            if (isRevitPhoto)
            {
                bigImageWidth = 32;
                bigImageHeight = 32;
            }
            using (var b = new Bitmap(bigImageWidth, bigImageHeight))
            {
                b.SetResolution(bigImage.HorizontalResolution, bigImage.VerticalResolution);
                using (var g = Graphics.FromImage(b))
                {
                    g.Clear(!suppressAlphaChannel || isRevitPhoto ? Color.Transparent : Color.White);
                    int x = 0;
                    int y = 0;
                    if (isRevitPhoto)
                    {
                        x = (int)Math.Round((double)(32 - bigImage.Width) / 2);
                        y = (int)Math.Round((double)(32 - bigImage.Height) / 2);
                    }
                    g.DrawImageUnscaled(bigImage, x, y);
                }
                BlockBlobClient bigImageBlobUpload = photosContainer.GetBlockBlobClient(bigImageBlobName);
                using (var stream = await bigImageBlobUpload.OpenWriteAsync(true))
                {
                    if (suppressAlphaChannel)
                    {
                        b.Save(stream, jgpEncoder, bigImageEncoderParameters);
                    }
                    else
                    {
                        b.Save(stream, ImageFormat.Png);
                    }
                }
                await bigImageBlobUpload.SetHttpHeadersAsync(new BlobHttpHeaders { ContentType = MimeTypeProvider.GetMimeType(extension) });
                photo.OriginalImgUrl = bigImageBlobUpload.Uri.ToString();
            }

            //Options for compression of middle and small images
            EncoderParameters middleAndSmallImageEncoderParameters = new EncoderParameters(1);
            EncoderParameter middleAndSmallImageQualityParameter = new EncoderParameter(Encoder.Quality, _qualityCompressionPercentage);
            middleAndSmallImageEncoderParameters.Param[0] = middleAndSmallImageQualityParameter;

            var middleImageWidth = middleImage.Width;
            var middleImageHeight = middleImage.Height;
            if (isRevitPhoto)
            {
                middleImageWidth = 24;
                middleImageHeight = 24;
            }
            using (var b = new Bitmap(middleImageWidth, middleImageHeight))
            {
                b.SetResolution(middleImage.HorizontalResolution, middleImage.VerticalResolution);

                using (var g = Graphics.FromImage(b))
                {
                    g.Clear(!suppressAlphaChannel || isRevitPhoto ? Color.Transparent : Color.White);
                    int x = 0;
                    int y = 0;
                    if (isRevitPhoto)
                    {
                        x = (int)Math.Round((double)(24 - middleImage.Width) / 2);
                        y = (int)Math.Round((double)(24 - middleImage.Height) / 2);
                    }
                    g.DrawImageUnscaled(middleImage, x, y);
                }
                BlockBlobClient middleImageBlobUpload = photosContainer.GetBlockBlobClient(middleImageBlobName);
                using (var stream = await middleImageBlobUpload.OpenWriteAsync(true))
                {
                    if (suppressAlphaChannel)
                    {
                        b.Save(stream, jgpEncoder, middleAndSmallImageEncoderParameters);
                    }
                    else
                    {
                        b.Save(stream, ImageFormat.Png);
                    }
                }
                await middleImageBlobUpload.SetHttpHeadersAsync(new BlobHttpHeaders { ContentType = MimeTypeProvider.GetMimeType(extension) });
                photo.MiddleImgUrl = middleImageBlobUpload.Uri.ToString();
            }

            var smallImageWidth = smallImage.Width;
            var smallImageHeight = smallImage.Height;
            if (isRevitPhoto)
            {
                smallImageWidth = 16;
                smallImageHeight = 16;
            }
            using (var b = new Bitmap(smallImageWidth, smallImageHeight))
            {
                b.SetResolution(smallImage.HorizontalResolution, smallImage.VerticalResolution);

                using (var g = Graphics.FromImage(b))
                {
                    g.Clear(!suppressAlphaChannel || isRevitPhoto ? Color.Transparent : Color.White);
                    int x = 0;
                    int y = 0;
                    if (isRevitPhoto)
                    {
                        x = (int)Math.Round((double)(16 - smallImage.Width) / 2);
                        y = (int)Math.Round((double)(16 - smallImage.Height) / 2);
                    }
                    g.DrawImageUnscaled(smallImage, x, y);
                }
                BlockBlobClient smallImageBlobUpload = photosContainer.GetBlockBlobClient(smallImageBlobName);
                using (var stream = await smallImageBlobUpload.OpenWriteAsync(true))
                {
                    if (suppressAlphaChannel)
                    {
                        b.Save(stream, jgpEncoder, middleAndSmallImageEncoderParameters);
                    }
                    else
                    {
                        b.Save(stream, ImageFormat.Png);
                    }
                }
                await smallImageBlobUpload.SetHttpHeadersAsync(new BlobHttpHeaders { ContentType = MimeTypeProvider.GetMimeType(extension) });
                photo.SmallImgUrl = smallImageBlobUpload.Uri.ToString();
            }

            bigImage.Dispose();
            middleImage.Dispose();
            smallImage.Dispose();
        }

        public static ImageCodecInfo GetEncoder(ImageFormat format)
        {
            ImageCodecInfo[] codecs = ImageCodecInfo.GetImageDecoders();

            foreach (ImageCodecInfo codec in codecs)
            {
                if (codec.FormatID == format.Guid)
                {
                    return codec;
                }
            }
            return null;
        }        
    }
}