﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class SalesRepresentative : BaseEntity
    {
        [StringLength(100)]
        public string FirstName { get; set; }

        [StringLength(100)]
        public string LastName { get; set; }

        [StringLength(100)]
        public string PhoneNumber { get; set; }

        [StringLength(100)]
        public string WebsiteName { get; set; }

        [StringLength(100)]
        public string WebsiteLink { get; set; }

        [StringLength(100)]
        public string Email { get; set; }

        [StringLength(500)]
        public string LinkendInLink { get; set; }

        public int? ProfilePhotoId { get; set; }

        [ForeignKey("ProfilePhotoId")]
        public virtual Photo ProfilePhoto { get; set; }

        public int ManufacturerId { get; set; }

        [ForeignKey("ManufacturerId")]
        public virtual Manufacturer Manufacturer { get; set; }
    }
}