﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    /// <summary>
    /// Lets talk request to manufacturer
    /// </summary>
    public class UserBIMsmithLTManufacturer
    {
        public int Id { get; set; }

        public int ManufacturerId { get; set; }

        public int Status { get; set; }

        public string AddedById { get; set; }

        public DateTime AddedDate { get; set; }

        public int Timezone { get; set; }

        public int? ProductId { get; set; }

        /// ------------------------------------------
        [<PERSON><PERSON><PERSON>("AddedById")]
        public virtual ApplicationUser AddedBy { get; set; }

        [ForeignKey("ManufacturerId")]
        public virtual Manufacturer Manufacturer { get; set; }

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }
    }
}