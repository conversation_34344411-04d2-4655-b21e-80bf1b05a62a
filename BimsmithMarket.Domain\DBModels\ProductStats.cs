﻿using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    [Index(nameof(Value), Name = "IX_ProductStats_Value")]
    [Index(nameof(ConvertValue), Name = "IX_ProductStats_ConvertValue")]
    public class ProductStats : BaseEntity
    {
        public int ProductId { get; set; }

        public int KeyStatId { get; set; }

        public KeyStatType KeyStatType { get; set; }

        public int Order { get; set; }

        public int? KeyStatUnitId { get; set; }

        public int? ConvertKeyStatUnitId { get; set; }  //New

        /// <summary>
        /// Parsed value as float
        /// </summary>
        public float? FloatValue { get; set; }

        /// <summary>
        /// Parsed min range value as float
        /// </summary>
        public float? FloatMinRangeValue { get; set; }

        /// <summary>
        /// Parsed max range value as float
        /// </summary>
        public float? FloatMaxRangeValue { get; set; }


        [StringLength(200)]
        public string Value { get; set; }

        public string MinRangeValue { get; set; }

        public string MaxRangeValue { get; set; }


        [StringLength(200)]
        public string ConvertValue { get; set; }

        public string ConvertMinRangeValue { get; set; }

        public string ConvertMaxRangeValue { get; set; }

        public string Note { get; set; }

        /// ------------------------------------------

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }

        [ForeignKey("KeyStatId")]
        public virtual KeyStat KeyStat { get; set; }

        [ForeignKey("KeyStatUnitId")]
        public virtual KeyStatUnit KeyStatUnit { get; set; }

        [ForeignKey("ConvertKeyStatUnitId")]
        public virtual KeyStatUnit ConvertKeyStatUnit { get; set; }

        public virtual ICollection<KeyStatValueList> KeyStatValueList { get; set; }

        public ProductStats()
        {
            KeyStatValueList = new List<KeyStatValueList>();
        }
    }

    public enum KeyStatType
    {
        None = 0,
        SingleNumeric = 1,
        NumericRange = 2,
        MultivalueNumeric = 3,
        TextValue = 4,
        TextMultivalue = 5
    }
}