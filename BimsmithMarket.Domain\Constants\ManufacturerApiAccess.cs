﻿using Newtonsoft.Json;
using System.Collections.Generic;
using System.IO;

namespace BIMsmithMarket.Domain.Constants
{
    public class AllowedManufacturersModel
    {
        public List<ManufacturerApiAccess> AllowedManufacturers { get; set; }

        private AllowedManufacturersModel()
        {
            AllowedManufacturers = new List<ManufacturerApiAccess> { };
        }

        public static AllowedManufacturersModel Create(string manufacturersAccessConfigFilePath)
        {
            using (StreamReader r = new StreamReader(manufacturersAccessConfigFilePath))
            {
                string json = r.ReadToEnd();
                var model = JsonConvert.DeserializeObject<AllowedManufacturersModel>(json);
                return model;
            }
        }
    }

    public class ManufacturerApiAccess
    {
        public string ManufacturerEmail { get; set; }
        public string ManufacturerUrl { get; set; }
    }
}