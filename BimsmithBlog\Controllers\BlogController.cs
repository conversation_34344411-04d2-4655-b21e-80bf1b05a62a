﻿using BIMsmithBlog.Models;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using BIMsmithMarket.Domain.Models;
using Flurl;

namespace BIMsmithBlog.Controllers
{
    public class BlogController : Controller
    {
        private readonly IHttpClientFactory _httpClientFactory;

        public BlogController(IHttpClientFactory httpClientFactory)
        {
            _httpClientFactory = httpClientFactory;
        }

        public async Task<ActionResult> Index(int page = 1, string q = null, string categoryVanityId = null)
        {
            return await this.RenderMainView(categoryVanityId, page, q);
        }

        public async Task<ActionResult> Category(string id, int page = 1, string q = null)
        {
            return await this.RenderMainView(id, page, q);
        }

        [HttpPost]
        public async Task<ActionResult> Subscribe()
        {
            var email = this.Request.Form["email"].FirstOrDefault();

            if (email != null)
            {
                using (IUnitOfWork unitOfWork = UnitOfWork.Create())
                {
                    var existingEmail = unitOfWork.EmailSubscriberRepository.GetAll().FirstOrDefault(a => a.Email == email && a.Source == BIMsmithMarket.Domain.DBModels.SubscriberSource.Blog);
                    if (existingEmail != null)
                    {
                        unitOfWork.EmailSubscriberRepository.Delete(existingEmail);
                    }

                    BIMsmithMarket.Domain.DBModels.EmailSubscriber emailSubscriber = new BIMsmithMarket.Domain.DBModels.EmailSubscriber();
                    emailSubscriber.Email = email.ToString();
                    emailSubscriber.Date = DateTime.UtcNow;
                    emailSubscriber.Source = BIMsmithMarket.Domain.DBModels.SubscriberSource.Blog;

                    unitOfWork.EmailSubscriberRepository.Insert(emailSubscriber);
                    unitOfWork.Save();
                }

                //Add the email to Mailchimp list
                var mailChimpManager = new MailChimp.Net.MailChimpManager(ConfigurationHelper.GetValue("MailChimpAPIKey"));
                var mailChimpListId = ConfigurationHelper.GetValue("MailChimpListId"); //"f150d11cd0"
                var mailChimpListCollection = await mailChimpManager.Lists.GetAllAsync();

                if (mailChimpListCollection.Any(a => a.Id == mailChimpListId))
                {
                    var member = new MailChimp.Net.Models.Member { EmailAddress = email, StatusIfNew = MailChimp.Net.Models.Status.Subscribed };
                    member.MergeFields.Add("FNAME", "Subscriber");
                    member.MergeFields.Add("LNAME", "");

                    var res = await mailChimpManager.Members.AddOrUpdateAsync(mailChimpListId, member);
                }

                HttpContext.Session.SetString("notificationInfo", "Thank you for subscribing");
            }
            else
            {
                HttpContext.Session.SetString("notificationInfo", "Error!!! Occurred some problem to edd your email");
            }

            return RedirectToActionPermanent("Index");
        }

        public ActionResult Details(string vanityId)
        {
            if (vanityId == "subscribe") //Return subscribe view
            {
                return SubscribeView();
            }

            var model = new BlogDetailsModel();

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var blogPost = unitOfWork.BlogPostRepository.GetAll().Where(a => a.VanityId == vanityId)
                    .Select(a => new
                    {
                        VanityId = a.VanityId,
                        Title = a.Title,
                        AuthorTitle = a.AuthorTitle,
                        AuthorEmail = a.AuthorEmail,
                        AuthorImage = a.AuthorImage,
                        HtmlBody = a.HtmlBody,
                        PublishedDate = a.PublishedDate,
                        ImageUrlBig = a.ImageUrlBig,
                        ImageUrlSmall = a.ImageUrlSmall,
                        MetaTitle = a.MetaTitle,
                        MetaDescription = a.MetaDescription,
                        MetaKeywords = a.MetaKeywords,
                        BlogCategoryName = a.BlogCategory.Name,
                        BlogCategoryVanityId = a.BlogCategory.VanityId,
                        PublishOption = a.PublishOption,
                        BlogComments = a.BlogComments.Where(x => x.Targettype != TargetBlogType.SwatchBox).Select(c => new Models.BlogComment
                        {
                            Id = c.Id,
                            OwnerName = c.OwnerName,
                            Comment = c.Comment,
                            Date = c.CreatedDate
                        }),
                    }).FirstOrDefault();

                if (blogPost == null)
                {
                    return NotFound("The post not found");
                }

                this.ViewBag.IsDetailPage = true;
                this.ViewBag.NotificationInfo = HttpContext.Session.GetString("notificationInfo");
                this.ViewBag.Title = blogPost.MetaTitle ?? blogPost.Title;
                this.ViewBag.MetaDescription = blogPost.MetaDescription;
                this.ViewBag.MetaKeywords = blogPost.MetaKeywords;
                this.ViewBag.OgImageUrlBig = blogPost.ImageUrlBig;
                this.ViewBag.OgAuthorTitle = blogPost.AuthorTitle;
                
                var isDev = ConfigurationHelper.GetValue("IsDevEnvironment");
                this.ViewBag.IsPrivate = (blogPost.PublishOption == PublishOption.Private || isDev == "true");

                HttpContext.Session.SetString("notificationInfo", string.Empty);

                var authTokenCookies = this.Request.Cookies["authToken"];
                if (authTokenCookies != null && authTokenCookies != null)
                {
                    this.ViewBag.IsLogged = true;
                }
                else
                {
                    this.ViewBag.IsLogged = false;
                }

                var categories = unitOfWork.BlogCategoryRepository.GetAll()
                    .Where(x => x.TargetTypes.Any(a => a.TargetBlogType == TargetBlogType.ForMarketBlog))
                    .Select(a => new CategoryModel
                    {
                        Name = a.Name,
                        VanityId = a.VanityId
                    }).ToList();

                var query = unitOfWork.BlogPostRepository.GetAll().Where(a => a.PublishOption == PublishOption.Published && a.VanityId != vanityId && a.TargetTypes.Any(x => x.TargetBlogType == TargetBlogType.ForMarketBlog))
                    .Select(a => new
                    {
                        odrer = Guid.NewGuid(),
                        vanityId = a.VanityId,
                        category = new { name = a.BlogCategory.Name, vanityId = a.BlogCategory.VanityId },
                        title = a.Title,
                        descriptions = a.Descriptions,
                        publishedDate = a.PublishedDate.Value,
                        imageUrlBig = a.ImageUrlBig,
                        imageUrlSmall = a.ImageUrlSmall,
                    })
                    .ToList()
                    .OrderBy(a => a.odrer)
                    .Take(3)
                    .ToList(); //3 recent blogs

                var blogPosts = query.Select(a => new BlogPreview
                {
                    VanityId = a.vanityId,
                    Category = new CategoryModel { Name = a.category.name, VanityId = a.category.vanityId },
                    Title = a.title,
                    Descriptions = a.descriptions,
                    PublishedDate = a.publishedDate,
                    ImageUrlBig = a.imageUrlBig,
                    ImageUrlSmall = a.imageUrlSmall,
                })
                .ToList();

                model.Categories = categories;
                model.OtherBlogs = blogPosts;
                model.BlogComments = blogPost.BlogComments.ToList();

                model.VanityId = blogPost.VanityId;
                model.Category = new CategoryModel { Name = blogPost.BlogCategoryName, VanityId = blogPost.BlogCategoryVanityId };
                model.Title = blogPost.Title;
                model.AuthorTitle = blogPost.AuthorTitle;
                model.AuthorEmail = blogPost.AuthorEmail;
                model.HtmlBody = blogPost.HtmlBody;
                model.PublishedDate = blogPost.PublishedDate;
                model.ImageUrlBig = blogPost.ImageUrlBig;
                model.ImageUrlSmall = blogPost.ImageUrlSmall;
                model.PublishOption = blogPost.PublishOption;
                model.AuthorImage = blogPost.AuthorImage;
            }

            return View(model);
        }

        private ActionResult SubscribeView()
        {
            var model = new CommonModel();

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var categories = unitOfWork.BlogCategoryRepository.GetAll()
                                           .Where(x => x.TargetTypes.Any(t => t.TargetBlogType == TargetBlogType.All || t.TargetBlogType == TargetBlogType.ForMarketBlog))
                                           .Select(a => new CategoryModel
                                           {
                                               Name = a.Name,
                                               VanityId = a.VanityId
                                           })
                                           .ToList();

                model.Categories = categories;
            }

            return this.View("Subscribe", model);
        }

        [HttpPost]
        public async Task<ActionResult> AddComment()
        {
            var comment = this.Request.Form["comment"].FirstOrDefault();
            var postVanityId = this.Request.Form["vanityId"].FirstOrDefault();
            var captchaResponse = this.Request.Form["g-recaptcha-response"].FirstOrDefault();
            if (!string.IsNullOrWhiteSpace(captchaResponse))
            {
                if (IsReCaptchValid(captchaResponse))
                {
                    if (comment != null && postVanityId != null)
                    {
                        BimsmithUser user = null;

                        string authToken;

                        try
                        {
                            var authTokenCookies = this.Request.Cookies["authToken"];
                            if (authTokenCookies != null)
                            {
                                authToken = authTokenCookies;

                                HttpClient client = _httpClientFactory.CreateClient();
                                {
                                    var userInfo = ConfigurationHelper.GetValue("BimsmithUrl") + "/api/Auth/UserInfo/?authToken=" + authToken;

                                    using (var m = await client.GetAsync(userInfo))
                                    {
                                        if (m.StatusCode == System.Net.HttpStatusCode.OK)
                                        {
                                            var jsonContent = await m.Content.ReadAsStringAsync();

                                            user = JsonConvert.DeserializeObject<BimsmithUser>(jsonContent);
                                        }
                                    }
                                }
                            }
                        }
                        catch (Exception e)
                        {
                            Log.Error(e.Message, e);
                        }

                        using (IUnitOfWork unitOfWork = UnitOfWork.Create())
                        {
                            var post = unitOfWork.BlogPostRepository.GetAll().First(a => a.VanityId == postVanityId);

                            BIMsmithMarket.Domain.DBModels.BlogComment blogComment = new BIMsmithMarket.Domain.DBModels.BlogComment();
                            blogComment.BlogPostId = post.Id;
                            blogComment.Comment = comment;
                            blogComment.OwnerName = user?.FirstName ?? "Guest";
                            blogComment.CreatedById = user?.UserId;
                            blogComment.CreatedDate = DateTime.UtcNow;
                            blogComment.Targettype = TargetBlogType.ForMarketBlog;

                            unitOfWork.BlogCommentRepository.Insert(blogComment);
                            unitOfWork.Save();
#if !DEBUG
                            List<string> destinations = ConfigurationHelper.GetValue("commentAdminEmail").Split(",").ToList();
                            var body = @"<!DOCTYPE html>
                                         <html>
                                         <body>
                                            <h2>Comment information:</h2>
                                            <ul>
                                               <li>Id - " + blogComment.Id.ToString() + @";</li>
                                               <li>OwnerName - " + blogComment.OwnerName + @";</li>
                                               <li>Comment - " + blogComment.Comment + @";</li>
                                               <li>Post id - " + blogComment.BlogPostId + @";</li>
                                               <li>Post title - " + post.Title + @";</li>";

                            foreach (BlogTargetType targetType in post.TargetTypes)
                                body += $@"<li>Comment type - {targetType};</li>";

                            body += @"</ul></body></html>";

                            // null because we bush body to method
                            await EmailNotificationHelper.Create(null).SendEmail(destinations, "Add comment notification", body);
#endif
                        }

                        HttpContext.Session.SetString("notificationInfo", "Your comment has been posted");
                    }
                    else
                    {
                        HttpContext.Session.SetString("notificationInfo", "Error!!! Occurred some problem to post your comment");
                    }
                }
                else
                {
                    HttpContext.Session.SetString("notificationInfo", "Error!!! Captcha is not valid");
                }
            }
            else
            {
                HttpContext.Session.SetString("notificationInfo", "Error!!! Captcha response is empty");
            }

            return RedirectPermanent(Request.GetTypedHeaders().Referer.AbsoluteUri.ToString());
        }


        public bool IsReCaptchValid(string captchaResponse)
        {
            var result = false;
            var secretKey = ConfigurationHelper.GetValue("GoogleRecaptchaSecretKey");
            var apiUrl = ConfigurationHelper.GetValue("GoogleRecaptchaApiUrl") + "?secret={0}&response={1}";
            var requestUri = string.Format(apiUrl, secretKey, captchaResponse);
            var request = (HttpWebRequest)WebRequest.Create(requestUri);

            using (WebResponse response = request.GetResponse())
            {
                using (StreamReader stream = new StreamReader(response.GetResponseStream()))
                {
                    JObject jResponse = JObject.Parse(stream.ReadToEnd());
                    var isSuccess = jResponse.Value<bool>("success");
                    result = (isSuccess) ? true : false;
                }
            }
            return result;
        }

        private async Task<ActionResult> RenderMainView(string categoryVanityId, int page = 1, string q = null)
        {
            this.ViewBag.NotificationInfo = HttpContext.Session.GetString("notificationInfo");
            this.ViewBag.Title = "BIMsmithMarket Blog";
            this.ViewBag.MetaDescription = "-";
            this.ViewBag.MetaKeywords = "-";
            this.ViewBag.IsDetailPage = false;
            
            var isDev = ConfigurationHelper.GetValue("IsDevEnvironment");
            this.ViewBag.isDev = isDev == "true";

            HttpContext.Session.SetString("notificationInfo", string.Empty);

            BlogsList model = new BlogsList
            {
                IsAll = string.IsNullOrWhiteSpace(categoryVanityId)
            };

            var blogInList = 7;

            if (page < 1) page = 1;

            int skip = blogInList * (page - 1);

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var categories = unitOfWork.BlogCategoryRepository.GetAll()
                                            .Where(x => x.TargetTypes.Any(a => a.TargetBlogType == TargetBlogType.ForMarketBlog))
                                            .Select(a => new CategoryModel
                                            {
                                                Name = a.Name,
                                                VanityId = a.VanityId
                                            }).ToList();

                if (!string.IsNullOrWhiteSpace(categoryVanityId))
                {
                    model.IsCategoryPage = true;
                    foreach (var item in categories)
                    {
                        if (item.VanityId == categoryVanityId)
                        {
                            item.IsActive = true;
                        }
                    }
                }

                IQueryable<BIMsmithMarket.Domain.DBModels.BlogPost> query = unitOfWork.BlogPostRepository.GetAll();

                if (!string.IsNullOrWhiteSpace(categoryVanityId))
                {
                    query = unitOfWork.BlogPostRepository.GetAll().Where(a => a.BlogCategory.VanityId == categoryVanityId);
                }

                if (!string.IsNullOrWhiteSpace(q))
                {
                    query = query.Where(a => a.Title.Contains(q) || a.Descriptions.Contains(q));
                }

                query = query.Where(a => a.PublishOption == PublishOption.Published && a.TargetTypes.Any(x => x.TargetBlogType == TargetBlogType.ForMarketBlog));

                var blogsCount = query.Count();

                var blogPosts = query.OrderByDescending(a => a.IsFeatured)
                                     .ThenByDescending(a => a.PublishedDate)
                                     .Skip(skip)
                                     .Take(blogInList) //1 head and 6 recent
                                     .Select(a => new BlogPreview
                                     {
                                         VanityId = a.VanityId,
                                         Category = new CategoryModel { Name = a.BlogCategory.Name, VanityId = a.BlogCategory.VanityId },
                                         Title = a.Title,
                                         Descriptions = a.Descriptions,
                                         PublishedDate = a.PublishedDate.Value,
                                         AuthorTitle = a.AuthorTitle,
                                         AuthorEmail = a.AuthorEmail,
                                         AuthorImage = a.AuthorImage,
                                         ImageUrlBig = a.ImageUrlBig,
                                         ImageUrlSmall = a.ImageUrlSmall,
                                         RegionIds = a.RegionIds
                                     })
                .ToList();
                
                using HttpClient httpClient = new();
                Url getLocationUrl = new Url(ConfigurationHelper.GetValue("AnalyticsWebApiBaseAddress"))
                    .AppendPathSegment("api/Location/GetLocation")
                    .SetQueryParam("ipAddress", HttpContext?.Connection?.RemoteIpAddress?.ToString());
                using HttpResponseMessage locationResponse = await httpClient.GetAsync(getLocationUrl);

                Url urlContinentsList = new Url(ConfigurationHelper.GetValue("CBOBaseAddress"))
                    .AppendPathSegment("api/Continent/List")
                    .SetQueryParam("isActive", true);
                using HttpResponseMessage continentsResponse = await httpClient.GetAsync(urlContinentsList);

                string locationContent = await locationResponse.Content.ReadAsStringAsync();
                var analyticsLocationModel = JsonConvert.DeserializeObject<AnalyticsLocationModel>(locationContent);
                
                var contentContinents = await continentsResponse.Content.ReadAsStringAsync();
                var continents = JsonConvert.DeserializeObject<List<LocationModel.Root>>(contentContinents);

                var allCountries = continents.SelectMany(s => s.countries).Distinct().ToList();
                var currentCountry = allCountries.FirstOrDefault(w => w.name == analyticsLocationModel.Country);

                if (currentCountry != null)
                    blogPosts = blogPosts.Where(w => string.IsNullOrEmpty(w.RegionIds) || w.RegionIds.Split('_').Contains(currentCountry.id.ToString())).ToList();

                model.Categories = categories;
                model.HeadBlog = blogPosts.FirstOrDefault();
                model.RecentBlogs = blogPosts.Skip(1).ToList();
                model.SearchQuery = q;

                model.PagesCount = (int)System.Math.Ceiling(blogsCount / (double)blogInList);
                model.CurrentPage = page;
            }

            return View("Index", model);
        }
    }
}