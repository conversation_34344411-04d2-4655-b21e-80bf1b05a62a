﻿using BIMsmithMarket.Domain.DBModels.PaymentDbModels;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class ApplicationUser : IdentityUser
    {
        [MaxLength(65)]
        public string FirstName { get; set; }

        [MaxLength(65)]
        public string LastName { get; set; }

        public bool Subscribed { get; set; }

        public DateTime CreatedDate { get; set; }

        public virtual ICollection<UserPaidProduct> UserPaidProducts { get; set; }

        public virtual ICollection<Product> CreatedProduct { get; set; }

        public virtual ICollection<Product> ModifiedProduct { get; set; }

        public virtual ICollection<ApplicationUserRole> Roles { get; set; }

        public ApplicationUser()
        {
            UserPaidProducts = new List<UserPaidProduct>();
            CreatedProduct = new List<Product>();
            ModifiedProduct = new List<Product>();
            Roles = new List<ApplicationUserRole>();
        }

        public string GetFullName()
        {
            return string.Concat(FirstName, " ", LastName);
        }
    }

    public class ApplicationRole : IdentityRole
    {
        public bool Hidden { get; set; }

        public virtual ICollection<ApplicationUserRole> UserRoles { get; set; }

        public ApplicationRole()
        {
            UserRoles = new List<ApplicationUserRole>();
        }
    }

    public class ApplicationUserRole : IdentityUserRole<string>
    {
        public new string UserId { get; set; }

        [ForeignKey("UserId")]
        public virtual ApplicationUser User { get; set; }

        public new string RoleId { get; set; }

        [ForeignKey("RoleId")]
        public virtual ApplicationRole Role { get; set; }
    }

    public class ApplicationUserClaim : IdentityUserClaim<string>
    {

    }

    public class ApplicationUserLogin : IdentityUserLogin<string>
    {

    }

    public class ApplicationRoleClaim : IdentityRoleClaim<string>
    {

    }

    public class ApplicationUserToken : IdentityUserToken<string>
    {

    }
}