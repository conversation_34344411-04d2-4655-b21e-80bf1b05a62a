﻿using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IChangeLogExcelService
    {
        Task<string> GetExcelAsync(IUnitOfWork unitOfWork,
            EntityType entityType,
            int entityId,
            DateTime? startDate = null,
            DateTime? endDate = null);
    }
}