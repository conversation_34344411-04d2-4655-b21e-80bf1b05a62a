﻿using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Flurl;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// Upload Photo for using as logo/product photo/etc.
    /// </summary>
    [Route("api/[controller]/[action]")]
    public class UploadController : BaseApiController
    {
        private readonly IUploadFileService _uploadFileService;

        public UploadController(IUploadFileService uploadFileService)
        {
            _uploadFileService = uploadFileService;
        }

        /// <summary>
        /// Upload photo to local storage and get Photo Id. (Mime Multipart Content: Save only first image from content)
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ActionName("PhotoLocal")]
        [SwaggerOperation("FileUpload")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> PhotoLocal()
        {

            if (!(Request.HasFormContentType && Request.Form.Files.Any()))
                return BadRequest("Mime Multipart Content missing");

            var result = await _uploadFileService.PhotoLocalAsync(
                Request.Form.Files[0],
                Url.Content("~/api/photo/get"),
                AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier),
                Path.GetTempPath());

            return Ok(result);
        }

        /// <summary>
        /// Upload photo to Azure BLOB and get Photo Id. (Mime Multipart Content: Save only first image from content)
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Photo")]
        [SwaggerOperation("FileUpload")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Photo(bool suppressAlphaChannel = true, string attachURL = null, bool isRevitIcon = false)
        {
            var isUrlPresent = !string.IsNullOrWhiteSpace(attachURL);
            if (!(Request.HasFormContentType && Request.Form.Files.Any()) && !isUrlPresent)
                return BadRequest("Mime Multipart Content or Attach URL missing");

            IFormFile formFile = null;
            if (!isUrlPresent)
                formFile = Request.Form.Files[0];

            if (!isUrlPresent && formFile == null)
                return BadRequest("Content missing");

            var result = await _uploadFileService.PhotoAsync(formFile, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), suppressAlphaChannel, attachURL, isRevitIcon);
            return Ok(result);
        }

        /// <summary>
        /// Upload file to local storage and get File Id. (Mime Multipart Content: Save only first image from content)
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ActionName("FileLocal")]
        [SwaggerOperation("FileUpload")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> FileLocal()
        {
            if (!(Request.HasFormContentType && Request.Form.Files.Any()))
                return BadRequest("Mime Multipart Content missing");

            var result = await _uploadFileService.FileLocalAsync(
                Request.Form.Files[0],
                Url.Content("~api/file/get"),
                AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier),
                Path.GetTempPath(),
                new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl").AppendPathSegment("/assets/img/default_preview.png").ToString()));
            return Ok(result);
        }

        /// <summary>
        /// Manufacturers the json.
        /// External API
        /// Manufacturers use this endpoint to attach files to Additional Attachments section on manufacturer
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier.</param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("ManufacturerFile")]
        [SwaggerOperation("FileUpload")]
#if !DEBUG
        [Authorize]
#endif
        public async Task<IActionResult> ManufacturerFile(int manufacturerId)
        {
            if (!(Request.HasFormContentType && Request.Form.Files.Any()))
                return BadRequest("Mime Multipart Content missing");

            var result = await _uploadFileService.ManufacturerFileAsync(Request.Form.Files[0], AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), manufacturerId);
            return Ok(result);
        }

        /// <summary>
        /// Upload file to Azure Blob and get File Id. (Mime Multipart Content: Save only first image from content)
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ActionName("File")]
        [SwaggerOperation("FileUpload")]
        public async Task<IActionResult> File(string attachURL = null)
        {
            var isUrlPresent = !string.IsNullOrWhiteSpace(attachURL);
            if (!isUrlPresent && !(Request.HasFormContentType && Request.Form.Files.Any()))
                return BadRequest("Mime Multipart Content or Attach URL missing");

            IFormFile formFile = null;
            if (!isUrlPresent)
            {
                formFile = Request.Form.Files[0];
            }

            if (!isUrlPresent && formFile == null)
                return BadRequest("Content missing");

            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            string folderFiles = Path.GetTempPath();
            string previewUrl = new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment("/assets/img/default_preview.png").ToString();

            var dbFile = await _uploadFileService.AddFileAsync(formFile, userId, folderFiles, previewUrl, attachURL, false);
            if (dbFile == null)
                return BadRequest("Content missing");
            return Ok(dbFile);
        }

        /// <summary>
        /// Upload file to Azure Blob without blocking mime type validation and get File Id. (Mime Multipart Content: Save only first image from content)
        /// </summary>
        /// <param name="attachURL"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("AdditionalFile")]
        [SwaggerOperation("FileUpload")]
        public async Task<IActionResult> AdditionalFile(string attachURL = null)
        {
            var isUrlPresent = !string.IsNullOrWhiteSpace(attachURL);
            if (!isUrlPresent && !(Request.HasFormContentType && Request.Form.Files.Any()))
                return BadRequest("Mime Multipart Content or Attach URL missing");

            IFormFile formFile = null;
            if (!isUrlPresent)
            {
                formFile = Request.Form.Files[0];
            }

            if (!isUrlPresent && formFile == null)
                return BadRequest("Content missing");

            var dbFile = await _uploadFileService.AddFileAsync(formFile, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), Path.GetTempPath(),
                                new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment("/assets/img/default_preview.png").ToString(), attachURL, true);
            if (dbFile == null)
                return BadRequest("Content missing");
            return Ok(dbFile);
        }

        /// <summary>
        /// Upload file to local temp folder
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ActionName("TempFile")]
        [SwaggerOperation("FileUpload")]
        public async Task<IActionResult> TempFile()
        {
            if (!(Request.HasFormContentType && Request.Form.Files.Any()))
                return BadRequest("Mime Multipart Content missing");

            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            string startLinkToFile = Url.Content("~/api/file/get");
            string folderFiles = Path.GetTempPath();

            FileUploadResultModel result = await _uploadFileService.TempFileAsync(Request.Form.Files[0], userId, startLinkToFile, folderFiles);

            return Ok(result);
        }

        /// <summary>
        /// Updates the file with URL.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("UpdateFileWithUrl")]
        public async Task<IActionResult> UpdateFileWithUrl(int fileId)
        {
            return Ok(await _uploadFileService.UpdateFileWithUrlAsync(fileId));
        }

        //TODO: this method is not secured 
        /// <summary>
        /// Upload file to Azure Blob and get File Id for attachments. (Mime Multipart Content: Save only first image from content)
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ActionName("AttachmentFile")]
        [SwaggerOperation("FileUpload")]
        [AllowAnonymous]
        public async Task<IActionResult> AttachmentFile(/*string angularisToken*/)
        {
            if (!(Request.HasFormContentType && Request.Form.Files.Any()))
                return BadRequest("Mime Multipart Content missing");

            var result = await _uploadFileService.AttachmentFileAsync(Request.Form.Files[0], new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment("/assets/img/default_preview.png").ToString());
            return Ok(result);
        }

        /// <summary>
        /// Upload style file to Azure Blob and get File Id
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ActionName("StyleFile")]
        [SwaggerOperation("FileUpload")]
        public async Task<IActionResult> StyleFile()
        {
            if (!(Request.HasFormContentType && Request.Form.Files.Any()))
                return BadRequest("Mime Multipart Content missing");

            var result = await _uploadFileService.UploadStyleFileAsync(Request.Form.Files[0], AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), Path.GetTempPath());
            return Ok(result);
        }
    }
}