﻿using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Quartz;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Jobs
{
    [DisallowConcurrentExecution]
    public class ExternalApiUpdateProductsJob : IJob
    {
        private readonly IExternalApiService _externalApiService;

        public ExternalApiUpdateProductsJob(IExternalApiService externalApiService)
        {
            _externalApiService = externalApiService;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            await _externalApiService.ProcessExternalApiUpdateProductsQueueAsync(unitOfWork);
        }
    }
}