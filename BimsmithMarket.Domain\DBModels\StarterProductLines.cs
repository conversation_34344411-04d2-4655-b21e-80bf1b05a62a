﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class StarterProductLines
    {
        [Key]
        public int Id { get; set; }
        [StringLength(38)]
        public string StarterId { get; set; }

        public int ProductLineId { get; set; }

        [ForeignKey("StarterId")]
        public virtual Starter Starter { get; set; }

        [ForeignKey("ProductLineId")]
        public virtual ProductLine ProductLine { get; set; }
    }
}