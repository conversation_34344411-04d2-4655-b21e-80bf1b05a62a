﻿// <auto-generated />
using System;
using BIMsmithMarket.DataLayer.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace BIMsmithMarket.DataLayer.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20231012124017_Add_Notes_And_ChangeLogs")]
    partial class Add_Notes_And_ChangeLogs
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.12")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder, 1L, 1);

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Address", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Address1")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Address2")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("City")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Country")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Province")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("State")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("Zip")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.ToTable("Addresses");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Announcement", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("BannerColor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("IconUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsGlobal")
                        .HasColumnType("bit");

                    b.Property<string>("Link")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("OverrideColor")
                        .HasColumnType("bit");

                    b.Property<string>("RegionIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("ShowLink")
                        .HasColumnType("bit");

                    b.Property<string>("StateIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Text")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.ToTable("Announcements");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ApplicationRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Hidden")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("Roles", (string)null);
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ApplicationRoleClaim", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("FirstName")
                        .HasMaxLength(65)
                        .HasColumnType("nvarchar(65)");

                    b.Property<string>("LastName")
                        .HasMaxLength(65)
                        .HasColumnType("nvarchar(65)");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Subscribed")
                        .HasColumnType("bit");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("Users", (string)null);
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ApplicationUserClaim", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("UserClaims", (string)null);
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ApplicationUserLogin", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("UserLogins", (string)null);
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ApplicationUserRole", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RoleId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("UserRoles", (string)null);
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ApplicationUserToken", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.AttachmentOrder", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ManufacturerId")
                        .HasColumnType("int");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ManufacturerId");

                    b.ToTable("AttachmentOrders");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.BlogCategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("VanityId")
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex(new[] { "VanityId" }, "IX_BlogCategory_VanityId");

                    b.ToTable("BlogCategories");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.BlogCategoryTargetType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("BlogCategoryId")
                        .HasColumnType("int");

                    b.Property<int>("TargetBlogType")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("BlogCategoryId");

                    b.ToTable("BlogCategoryTargetTypes");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.BlogComment", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("BlogPostId")
                        .HasColumnType("int");

                    b.Property<string>("Comment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("OwnerName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Targettype")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("BlogPostId");

                    b.HasIndex("CreatedById");

                    b.ToTable("BlogComments");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.BlogMentionedEntry", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("BlogPostId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("EntryId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("EntryType")
                        .HasColumnType("int");

                    b.Property<string>("EntryUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("BlogPostId");

                    b.ToTable("BlogMentionedEntries");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.BlogPost", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("AuthorEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AuthorImage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AuthorTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("BlogCategoryId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Descriptions")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HtmlBody")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ImageUrlBig")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ImageUrlSmall")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsFeatured")
                        .HasColumnType("bit");

                    b.Property<string>("MetaDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaKeywords")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("PublishOption")
                        .HasColumnType("int");

                    b.Property<DateTime?>("PublishedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("SchedulePublishDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("Tags")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VanityId")
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<int>("ViewCount")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("BlogCategoryId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex(new[] { "VanityId" }, "IX_BlogPost_VanityId");

                    b.ToTable("BlogPosts");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.BlogTargetType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("BlogPostId")
                        .HasColumnType("int");

                    b.Property<int>("TargetBlogType")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("BlogPostId");

                    b.ToTable("BlogTargetTypes");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Category", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("BimsmithVanityUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IconUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IncludeRevitPlugin")
                        .HasColumnType("bit");

                    b.Property<bool>("IsRoot")
                        .HasColumnType("bit");

                    b.Property<string>("Keywords")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaKeywords")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int?>("ParentCategoryId")
                        .HasColumnType("int");

                    b.Property<int?>("RevitPluginPhotoId")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("Synonyms")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VanityUrl")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<float>("Weight")
                        .HasColumnType("real");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("ParentCategoryId");

                    b.HasIndex("RevitPluginPhotoId");

                    b.HasIndex(new[] { "Name" }, "IX_Category_Name");

                    b.HasIndex(new[] { "VanityUrl" }, "IX_Manufacturer_VanityUrl");

                    b.ToTable("Categories");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.CategoryKeyStat", b =>
                {
                    b.Property<int>("CategoryId")
                        .HasColumnType("int")
                        .HasColumnOrder(1);

                    b.Property<int>("KeyStatId")
                        .HasColumnType("int")
                        .HasColumnOrder(2);

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.HasKey("CategoryId", "KeyStatId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("KeyStatId");

                    b.ToTable("CategoryKeyStats");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ChangeLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("EntityAction")
                        .HasColumnType("int");

                    b.Property<int>("EntityId")
                        .HasColumnType("int");

                    b.Property<int>("EntityType")
                        .HasColumnType("int");

                    b.Property<string>("Field")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("NewValue")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("OldValue")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.ToTable("ChangeLogs");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Cisfb", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Code")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.HasIndex(new[] { "Code" }, "IX_Cisfb_Code");

                    b.HasIndex(new[] { "Title" }, "IX_Cisfb_Title");

                    b.ToTable("Cisfbs");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Company", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CompanyName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("EmailDomain")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("MatchCompanyUsers")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.ToTable("Companies");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.CustomCategoryIcon", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("CategoryId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("IconUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ManufacturerId")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ManufacturerId");

                    b.HasIndex("ModifiedById");

                    b.ToTable("CustomCategoryIcons");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Detail", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<float>("ContentRating")
                        .HasColumnType("real");

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("DetailScaleId")
                        .HasColumnType("int");

                    b.Property<bool>("Exterior")
                        .HasColumnType("bit");

                    b.Property<bool>("Interior")
                        .HasColumnType("bit");

                    b.Property<int?>("ManufacturerId")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Orientation")
                        .HasColumnType("int");

                    b.Property<int?>("PhotoId")
                        .HasColumnType("int");

                    b.Property<float>("ProductRating")
                        .HasColumnType("real");

                    b.Property<bool>("Published")
                        .HasColumnType("bit");

                    b.Property<string>("RegionIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StateIds")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DetailScaleId");

                    b.HasIndex("ManufacturerId");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("PhotoId");

                    b.ToTable("Details");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DetailApplication", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Header")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Keywords")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PageTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Synonyms")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VanityUrl")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.ToTable("DetailApplications");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DetailDetailApplication", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("DetailApplicationId")
                        .HasColumnType("int");

                    b.Property<int>("DetailId")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DetailApplicationId");

                    b.HasIndex("DetailId");

                    b.HasIndex("ModifiedById");

                    b.ToTable("DetailDetailApplications");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DetailFile", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("ContentCheckedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ContentCreatedby")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("DetailId")
                        .HasColumnType("int");

                    b.Property<int>("FileId")
                        .HasColumnType("int");

                    b.Property<string>("FileVersion")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ProjectDataTypeId")
                        .HasColumnType("int");

                    b.Property<string>("RegionIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SoftwareRelease")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StateIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("WasChanged")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DetailId");

                    b.HasIndex("FileId");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("ProjectDataTypeId");

                    b.ToTable("DetailFiles");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DetailMasterformat", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("DetailId")
                        .HasColumnType("int");

                    b.Property<int>("ExternalMasterformatId")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DetailId");

                    b.HasIndex("ModifiedById");

                    b.ToTable("DetailMasterformats");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DetailPhoto", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("DetailId")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("PhotoId")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DetailId");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("PhotoId");

                    b.ToTable("DetailPhotoes");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DetailRating", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Comment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("DetailId")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Rating")
                        .HasColumnType("int");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DetailId");

                    b.HasIndex("ModifiedById");

                    b.ToTable("DetailRatings");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DetailScale", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.ToTable("DetailScales");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DropboxSetting", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("AccessToken")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AccountId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ExpiresIn")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Scope")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TokenType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("Uid")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.ToTable("DropboxSettings");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DynamicTranslationDbModels.DynamicTranslation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("EntityId")
                        .HasColumnType("int");

                    b.Property<string>("LanguageCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("TranslatableEntityFieldId")
                        .HasColumnType("int");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("TranslatableEntityFieldId");

                    b.ToTable("DynamicTranslations");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DynamicTranslationDbModels.TranslatableEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Caption")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("TypeName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.ToTable("TranslatableEntities");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DynamicTranslationDbModels.TranslatableEntityField", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Caption")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FieldName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("TranslatableEntityId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("TranslatableEntityId");

                    b.ToTable("TranslatableEntityFields");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.EmailSubscriber", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Source")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("EmailSubscribers");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Event", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("AuthorEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AuthorImage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AuthorTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CEUCredits")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HtmlBody")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ImageUrlBig")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ImageUrlSmall")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsFeatured")
                        .HasColumnType("bit");

                    b.Property<string>("Link")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaKeywords")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Published")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("PublishedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("Tags")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Time")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("UnpublishAfterDateHasPassed")
                        .HasColumnType("bit");

                    b.Property<string>("VanityUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ViewCount")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.ToTable("Events");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.FeatureSetting", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<bool>("Status")
                        .HasColumnType("bit");

                    b.Property<int>("Target")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique()
                        .HasFilter("[Name] IS NOT NULL");

                    b.ToTable("FeatureSettings");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.File", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CheckSum")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FileName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("MediaType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("NextSyncDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("PreviewUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SyncRevitStatus")
                        .HasColumnType("int");

                    b.Property<int>("SyncStatus")
                        .HasColumnType("int");

                    b.Property<int>("SyncStatusCode")
                        .HasColumnType("int");

                    b.Property<string>("SyncUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UpdatesCount")
                        .HasColumnType("int");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.ToTable("Files");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ForgeProductLineIds", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("ForgeProductLineId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ProductLineId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ProductLineId");

                    b.ToTable("ForgeProductLineIds");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.HealthDashboardAccess", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("EntityId")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("UserId");

                    b.ToTable("HealthDashboardAccesses");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.HelpArticle", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("FAQ")
                        .HasColumnType("bit");

                    b.Property<string>("HelpTags")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HtmlBody")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaKeywords")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("PublishedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int>("ThumbsDownCount")
                        .HasColumnType("int");

                    b.Property<int>("ThumbsUpCount")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VanityUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ViewCount")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.ToTable("HelpArticles");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.HelpCategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("IconUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaKeywords")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ParentCategoryId")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VanityUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<float>("Weight")
                        .HasColumnType("real");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("ParentCategoryId");

                    b.ToTable("HelpCategories");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.KeyStat", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.ToTable("KeyStats");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.KeyStatUnit", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("AFormat")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AUnitName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BFormat")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BUnitName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("GroupName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Relation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int>("UnitMetricType")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.ToTable("KeyStatUnits");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.KeyStatUnitRelation", b =>
                {
                    b.Property<int>("FromUnitId")
                        .HasColumnType("int")
                        .HasColumnOrder(1);

                    b.Property<int>("ToUnitId")
                        .HasColumnType("int")
                        .HasColumnOrder(2);

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<float?>("Multiplicator")
                        .HasColumnType("real");

                    b.Property<string>("Relation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RoundCountDigits")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.HasKey("FromUnitId", "ToUnitId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("ToUnitId");

                    b.ToTable("KeyStatUnitRelations");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.KeyStatValueList", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int?>("ConvertKeyStatUnitId")
                        .HasColumnType("int");

                    b.Property<string>("ConvertMaxRangeValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ConvertMinRangeValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ConvertValue")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<float?>("FloatMaxRangeValue")
                        .HasColumnType("real");

                    b.Property<float?>("FloatMinRangeValue")
                        .HasColumnType("real");

                    b.Property<float?>("FloatValue")
                        .HasColumnType("real");

                    b.Property<int?>("KeyStatUnitId")
                        .HasColumnType("int");

                    b.Property<string>("MaxRangeValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MinRangeValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ProductLineStatsId")
                        .HasColumnType("int");

                    b.Property<int?>("ProductStatsId")
                        .HasColumnType("int");

                    b.Property<string>("Value")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("ConvertKeyStatUnitId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("KeyStatUnitId");

                    b.HasIndex("ProductLineStatsId");

                    b.HasIndex("ProductStatsId");

                    b.HasIndex(new[] { "ConvertValue" }, "IX_KeyStatValueList_ConvertValue");

                    b.HasIndex(new[] { "Value" }, "IX_KeyStatValueList_Value");

                    b.ToTable("KeyStatValueLists");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Localization", b =>
                {
                    b.Property<string>("Name")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Name");

                    b.HasIndex("CreatedById");

                    b.ToTable("Localizations");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Manufacturer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int?>("AddressId")
                        .HasColumnType("int");

                    b.Property<string>("AnalyticsSetting")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CustomMicrositeName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CustomSignInBackgroundColor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CustomSignInDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("CustomSignInLogoImageId")
                        .HasColumnType("int");

                    b.Property<string>("CustomSignInTextColor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DetailsMicrositeSettings")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EmailLandL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EmailLandLCC")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EmailLetsTalk")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EmailLetsTalkCC")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("FooterAdImageId")
                        .HasColumnType("int");

                    b.Property<string>("FooterAdUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ForgeManufacturerId")
                        .HasMaxLength(38)
                        .HasColumnType("nvarchar(38)");

                    b.Property<string>("ForgeUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("HealthCheckStatus")
                        .HasColumnType("int");

                    b.Property<string>("HubVanityURL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ImageId")
                        .HasColumnType("int");

                    b.Property<bool>("IncludeRevitPlugin")
                        .HasColumnType("bit");

                    b.Property<bool>("IsOnForge")
                        .HasColumnType("bit");

                    b.Property<bool>("IsOnMarket")
                        .HasColumnType("bit");

                    b.Property<bool>("IsParentManufacturer")
                        .HasColumnType("bit");

                    b.Property<string>("Keywords")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("LAndL")
                        .HasColumnType("bit");

                    b.Property<bool>("LetsTalk")
                        .HasColumnType("bit");

                    b.Property<string>("LetsTalkSettings")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ManualUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ManufacturerHubSubtitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ManufacturerHubTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MarketUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaKeywords")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("NodeSetting")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("OriginalImageId")
                        .HasColumnType("int");

                    b.Property<string>("OwnerId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("PageSetting")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<string>("PhoneMask")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("PhotoId")
                        .HasColumnType("int");

                    b.Property<int?>("PromotedId")
                        .HasColumnType("int");

                    b.Property<bool>("PublishToPartner")
                        .HasColumnType("bit");

                    b.Property<bool>("Published")
                        .HasColumnType("bit");

                    b.Property<string>("RegionIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("RevitPluginPhotoId")
                        .HasColumnType("int");

                    b.Property<bool>("SendULInfo")
                        .HasColumnType("bit");

                    b.Property<bool>("ShowFooterAd")
                        .HasColumnType("bit");

                    b.Property<string>("Site")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Staging")
                        .HasColumnType("bit");

                    b.Property<string>("StateIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<bool>("SubscribeButton")
                        .HasColumnType("bit");

                    b.Property<string>("SwatchboxManufacturerId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Synonyms")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<bool>("UseCustomLoginRegisterScreen")
                        .HasColumnType("bit");

                    b.Property<string>("VideoUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<float>("Weight")
                        .HasColumnType("real");

                    b.HasKey("Id");

                    b.HasIndex("AddressId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("CustomSignInLogoImageId");

                    b.HasIndex("FooterAdImageId");

                    b.HasIndex("ImageId");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("OwnerId");

                    b.HasIndex("ParentId");

                    b.HasIndex("PhotoId");

                    b.HasIndex("RevitPluginPhotoId");

                    b.HasIndex(new[] { "ForgeManufacturerId" }, "IX_Manufacturer_ForgeManufacturerId");

                    b.HasIndex(new[] { "Name" }, "IX_Manufacturer_Name");

                    b.ToTable("Manufacturers");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ManufacturerAdditionalFile", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CustomFileId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("FileId")
                        .HasColumnType("int");

                    b.Property<bool>("IsAttachment")
                        .HasColumnType("bit");

                    b.Property<int>("ManufacturerId")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ProjectDataTypeId")
                        .HasColumnType("int");

                    b.Property<string>("RegionIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<float>("Weight")
                        .HasColumnType("real");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("FileId");

                    b.HasIndex("ManufacturerId");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("ProjectDataTypeId");

                    b.ToTable("ManufacturerAdditionalFiles");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ManufacturerAdminUser", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("AddedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("AddedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("AdminUserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ManufacturerId")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Roles")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AddedById");

                    b.HasIndex("AdminUserId");

                    b.HasIndex("ManufacturerId");

                    b.HasIndex("ModifiedById");

                    b.ToTable("ManufacturerAdminUsers");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ManufacturerAnnouncement", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("AnnouncementId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ManufacturerId")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("AnnouncementId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ManufacturerId");

                    b.HasIndex("ModifiedById");

                    b.ToTable("ManufacturerAnnouncements");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ManufacturerFile", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CustomFileId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("FileId")
                        .HasColumnType("int");

                    b.Property<bool>("IsAttachment")
                        .HasColumnType("bit");

                    b.Property<int>("ManufacturerId")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ProjectDataTypeId")
                        .HasColumnType("int");

                    b.Property<string>("RegionIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StateIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<float>("Weight")
                        .HasColumnType("real");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("FileId");

                    b.HasIndex("ManufacturerId");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("ProjectDataTypeId");

                    b.ToTable("ManufacturerFiles");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ManufacturerPhoto", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ManufacturerId")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("PhotoId")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ManufacturerId");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("PhotoId");

                    b.ToTable("ManufacturerPhotoes");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ManufacturerStyleFile", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("FileId")
                        .HasColumnType("int");

                    b.Property<string>("FileVersion")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ManufacturerId")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("FileId");

                    b.HasIndex("ManufacturerId");

                    b.HasIndex("ModifiedById");

                    b.ToTable("ManufacturerStyleFiles");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Masterformat", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Code")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.HasIndex(new[] { "Code" }, "IX_Masterformat_Code");

                    b.HasIndex(new[] { "Title" }, "IX_Masterformat_Title");

                    b.ToTable("Masterformats");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.News", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("AuthorTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Descriptions")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HtmlBody")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ImageUrlBig")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ImageUrlSmall")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaKeywords")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("NewsCategoryId")
                        .HasColumnType("int");

                    b.Property<int>("PublishOption")
                        .HasColumnType("int");

                    b.Property<DateTime?>("PublishedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("SchedulePublishDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("Tags")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VanityId")
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<int>("ViewCount")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("NewsCategoryId");

                    b.HasIndex(new[] { "VanityId" }, "IX_News_VanityId");

                    b.ToTable("News");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.NewsCategory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("NewsCategories");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.NewsTarget", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("NewsId")
                        .HasColumnType("int");

                    b.Property<int>("Site")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("NewsId");

                    b.ToTable("NewsTargets");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Note", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateIsEffectiveTill")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateToNotifyUsers")
                        .HasColumnType("datetime2");

                    b.Property<int>("EntityId")
                        .HasColumnType("int");

                    b.Property<int>("EntityType")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.ToTable("Notes");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.NoteNotificationUser", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("NoteId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("NoteId");

                    b.ToTable("NoteNotificationUsers");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Omniclass", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Code")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.HasIndex(new[] { "Code" }, "IX_Omniclass_Code");

                    b.HasIndex(new[] { "Title" }, "IX_Omniclass_Title");

                    b.ToTable("Omniclasses");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.PaymentDbModels.PaymentPlan", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("EndDateUtc")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("PaymentDiscount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("PaymentPriority")
                        .HasColumnType("int");

                    b.Property<DateTime?>("StartDateUtc")
                        .HasColumnType("datetime2");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("PaymentPlans");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.PaymentDbModels.Price", b =>
                {
                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("PriceType")
                        .HasColumnType("int");

                    b.HasKey("ProductId");

                    b.ToTable("Prices");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.PaymentDbModels.ProductPrice", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("PaymentPlanId")
                        .HasColumnType("int");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("PaymentPlanId");

                    b.HasIndex("ProductId");

                    b.ToTable("ProductPrices");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.PaymentDbModels.UserPaidProduct", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("PaidProductId")
                        .HasColumnType("int");

                    b.Property<int>("PriceType")
                        .HasColumnType("int");

                    b.Property<string>("StripeSessionId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("StripeSessionMark")
                        .HasColumnType("datetime2");

                    b.Property<string>("StripeSessionStatus")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("PaidProductId");

                    b.HasIndex("UserId");

                    b.ToTable("UserPaidProducts");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Photo", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("MiddleImgUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OriginalImgUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SmallImgUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SyncStatusCode")
                        .HasColumnType("int");

                    b.Property<int>("UpdatesCount")
                        .HasColumnType("int");

                    b.Property<string>("UploadUrl")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.ToTable("Photos");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.PluginFile", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("FileId")
                        .HasColumnType("int");

                    b.Property<string>("Info")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("PluginType")
                        .HasColumnType("int");

                    b.Property<string>("Updates")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Version")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("FileId");

                    b.HasIndex("ModifiedById");

                    b.ToTable("PluginFiles");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Product", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("AssemblyCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("CategoryId")
                        .HasColumnType("int");

                    b.Property<float>("ContentRating")
                        .HasColumnType("real");

                    b.Property<string>("CreatedById")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("DisplaySwatchboxProductOnMicrosite")
                        .HasColumnType("bit");

                    b.Property<bool>("DisplaySwatchboxProductOnProductPage")
                        .HasColumnType("bit");

                    b.Property<string>("ExternalId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ExternalModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("FooterAdImageId")
                        .HasColumnType("int");

                    b.Property<string>("FooterAdUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ForgeCeilingURL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ForgeFloorURL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ForgeRoofURL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ForgeWallURL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("HideOnMicrosite")
                        .HasColumnType("bit");

                    b.Property<bool>("IsFeatured")
                        .HasColumnType("bit");

                    b.Property<bool>("IsImperialDefault")
                        .HasColumnType("bit");

                    b.Property<string>("Keywords")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ManufacturerId")
                        .HasColumnType("int");

                    b.Property<string>("MetaDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaKeywords")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("PhotoId")
                        .HasColumnType("int");

                    b.Property<int?>("ProductLineId")
                        .HasColumnType("int");

                    b.Property<float>("ProductRating")
                        .HasColumnType("real");

                    b.Property<string>("ProductUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PublishToPartner")
                        .HasColumnType("bit");

                    b.Property<bool>("Published")
                        .HasColumnType("bit");

                    b.Property<string>("RegionIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SketchupId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Staging")
                        .HasColumnType("bit");

                    b.Property<string>("StateIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("SwatchboxOptionId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SwatchboxProductId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ULSyncStatus")
                        .HasColumnType("int");

                    b.Property<string>("ULUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VanityURL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VideoUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<float>("Weight")
                        .HasColumnType("real");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("FooterAdImageId");

                    b.HasIndex("ManufacturerId");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("PhotoId");

                    b.HasIndex("ProductLineId");

                    b.HasIndex(new[] { "Name" }, "IX_Product_Name");

                    b.ToTable("Products");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductCategory", b =>
                {
                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnOrder(1);

                    b.Property<int>("CategoryId")
                        .HasColumnType("int")
                        .HasColumnOrder(2);

                    b.Property<bool>("MainCategory")
                        .HasColumnType("bit");

                    b.Property<float>("Weight")
                        .HasColumnType("real");

                    b.HasKey("ProductId", "CategoryId");

                    b.HasIndex("CategoryId");

                    b.ToTable("ProductCategories");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductCertificate", b =>
                {
                    b.Property<int>("ProductCertificateId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProductCertificateId"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ExternalCertificateId")
                        .HasColumnType("int");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.HasKey("ProductCertificateId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ProductId");

                    b.ToTable("ProductCertificates");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductCisfb", b =>
                {
                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnOrder(1);

                    b.Property<int>("CisfbId")
                        .HasColumnType("int")
                        .HasColumnOrder(2);

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ProductId", "CisfbId");

                    b.HasIndex("CisfbId");

                    b.HasIndex("CreatedById");

                    b.ToTable("ProductCisfbs");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductDetail", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("DetailId")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DetailId");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("ProductId");

                    b.ToTable("ProductDetails");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductDisplayOrderView", b =>
                {
                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProductId"), 1L, 1);

                    b.Property<long>("CRn")
                        .HasColumnType("bigint");

                    b.Property<int>("CategoryId")
                        .HasColumnType("int");

                    b.Property<long>("DisplayPosition")
                        .HasColumnType("bigint");

                    b.Property<long>("MRn")
                        .HasColumnType("bigint");

                    b.Property<int>("ManufacturerId")
                        .HasColumnType("int");

                    b.Property<long?>("PCRn")
                        .HasColumnType("bigint");

                    b.Property<long>("PRn")
                        .HasColumnType("bigint");

                    b.Property<int?>("ParentCategoryId")
                        .HasColumnType("int");

                    b.Property<int?>("ParentParentCategoryId")
                        .HasColumnType("int");

                    b.Property<int>("ResultBlock")
                        .HasColumnType("int");

                    b.Property<float>("Weight")
                        .HasColumnType("real");

                    b.HasKey("ProductId");

                    b.ToView("dbo.ProductDisplayOrderView");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductFile", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("ContentCheckedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ContentCreatedby")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CustomFileId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("FileId")
                        .HasColumnType("int");

                    b.Property<string>("FileVersion")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsAttachment")
                        .HasColumnType("bit");

                    b.Property<bool>("IsULPartnership")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<int?>("ProjectDataTypeId")
                        .HasColumnType("int");

                    b.Property<string>("RegionIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SoftwareRelease")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("SoftwareVersionId")
                        .HasColumnType("int");

                    b.Property<string>("StateIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("WasChanged")
                        .HasColumnType("bit");

                    b.Property<int>("Weight")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("FileId");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("ProductId");

                    b.HasIndex("ProjectDataTypeId");

                    b.HasIndex("SoftwareVersionId");

                    b.ToTable("ProductFiles");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductLine", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ForgeManufacturerId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("HealthCheckStatus")
                        .HasColumnType("int");

                    b.Property<int?>("ManufacturerId")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RegionIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StateIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<float>("Weight")
                        .HasColumnType("real");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ManufacturerId");

                    b.HasIndex("ModifiedById");

                    b.HasIndex(new[] { "Name" }, "IX_ProductLine_Name");

                    b.ToTable("ProductLines");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductLineCertificate", b =>
                {
                    b.Property<int>("ProductLineCertificateId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProductLineCertificateId"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ExternalCertificateId")
                        .HasColumnType("int");

                    b.Property<int>("ProductLineId")
                        .HasColumnType("int");

                    b.HasKey("ProductLineCertificateId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ProductLineId");

                    b.ToTable("ProductLineCertificates");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductLineFile", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("ContentCheckedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ContentCreatedby")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CustomFileId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("FileId")
                        .HasColumnType("int");

                    b.Property<string>("FileVersion")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsAttachment")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ProductLineId")
                        .HasColumnType("int");

                    b.Property<int?>("ProjectDataTypeId")
                        .HasColumnType("int");

                    b.Property<string>("SoftwareRelease")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Weight")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("FileId");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("ProductLineId");

                    b.HasIndex("ProjectDataTypeId");

                    b.ToTable("ProductLineFiles");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductLineQualityItem", b =>
                {
                    b.Property<int>("ProductLineId")
                        .HasColumnType("int")
                        .HasColumnOrder(1);

                    b.Property<int>("QualityItemId")
                        .HasColumnType("int")
                        .HasColumnOrder(2);

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ProductLineId", "QualityItemId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("QualityItemId");

                    b.ToTable("ProductLineQualityItems");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductLineStats", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int?>("ConvertKeyStatUnitId")
                        .HasColumnType("int");

                    b.Property<string>("ConvertMaxRangeValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ConvertMinRangeValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ConvertValue")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<float?>("FloatMaxRangeValue")
                        .HasColumnType("real");

                    b.Property<float?>("FloatMinRangeValue")
                        .HasColumnType("real");

                    b.Property<float?>("FloatValue")
                        .HasColumnType("real");

                    b.Property<int>("KeyStatId")
                        .HasColumnType("int");

                    b.Property<int>("KeyStatType")
                        .HasColumnType("int");

                    b.Property<int?>("KeyStatUnitId")
                        .HasColumnType("int");

                    b.Property<string>("MaxRangeValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MinRangeValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ProductLineId")
                        .HasColumnType("int");

                    b.Property<string>("Value")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("ConvertKeyStatUnitId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("KeyStatId");

                    b.HasIndex("KeyStatUnitId");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("ProductLineId");

                    b.HasIndex(new[] { "ConvertValue" }, "IX_ProductLineStats_ConvertValue");

                    b.HasIndex(new[] { "Value" }, "IX_ProductLineStats_Value");

                    b.ToTable("ProductLineStats");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductMasterformat", b =>
                {
                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnOrder(1);

                    b.Property<int>("ExternalMasterformatId")
                        .HasColumnType("int")
                        .HasColumnOrder(2);

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ProductId", "ExternalMasterformatId");

                    b.HasIndex("CreatedById");

                    b.ToTable("ProductMasterformats");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductOmniclass", b =>
                {
                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnOrder(1);

                    b.Property<int>("OmniclassId")
                        .HasColumnType("int")
                        .HasColumnOrder(2);

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ProductId", "OmniclassId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("OmniclassId");

                    b.ToTable("ProductOmniclasses");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductPhoto", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("PhotoId")
                        .HasColumnType("int");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("PhotoId");

                    b.HasIndex("ProductId");

                    b.ToTable("ProductPhotoes");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductQualityItem", b =>
                {
                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnOrder(1);

                    b.Property<int>("QualityItemId")
                        .HasColumnType("int")
                        .HasColumnOrder(2);

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ProductId", "QualityItemId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("QualityItemId");

                    b.ToTable("ProductQualityItems");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductRating", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Comment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PostedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("PostedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<int>("Rating")
                        .HasColumnType("int");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("PostedById");

                    b.HasIndex("ProductId");

                    b.ToTable("ProductRatings");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductSample", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<int>("SampleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ProductId");

                    b.ToTable("ProductSamples");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductStats", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int?>("ConvertKeyStatUnitId")
                        .HasColumnType("int");

                    b.Property<string>("ConvertMaxRangeValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ConvertMinRangeValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ConvertValue")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<float?>("FloatMaxRangeValue")
                        .HasColumnType("real");

                    b.Property<float?>("FloatMinRangeValue")
                        .HasColumnType("real");

                    b.Property<float?>("FloatValue")
                        .HasColumnType("real");

                    b.Property<int>("KeyStatId")
                        .HasColumnType("int");

                    b.Property<int>("KeyStatType")
                        .HasColumnType("int");

                    b.Property<int?>("KeyStatUnitId")
                        .HasColumnType("int");

                    b.Property<string>("MaxRangeValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MinRangeValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Order")
                        .HasColumnType("int");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<string>("Value")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("ConvertKeyStatUnitId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("KeyStatId");

                    b.HasIndex("KeyStatUnitId");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("ProductId");

                    b.HasIndex(new[] { "ConvertValue" }, "IX_ProductStats_ConvertValue");

                    b.HasIndex(new[] { "Value" }, "IX_ProductStats_Value");

                    b.ToTable("ProductStats");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductUniclass", b =>
                {
                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnOrder(1);

                    b.Property<int>("UniclassId")
                        .HasColumnType("int")
                        .HasColumnOrder(2);

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ProductId", "UniclassId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UniclassId");

                    b.ToTable("ProductUniclasses");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductUniformat", b =>
                {
                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnOrder(1);

                    b.Property<int>("UniformatId")
                        .HasColumnType("int")
                        .HasColumnOrder(2);

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ProductId", "UniformatId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("UniformatId");

                    b.ToTable("ProductUniformats");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProjectDataType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int?>("ActiveImageId")
                        .HasColumnType("int");

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("DefaultImageId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Header")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("IconId")
                        .HasColumnType("int");

                    b.Property<int?>("ImageId")
                        .HasColumnType("int");

                    b.Property<string>("MetaDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaKeyWords")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MetaTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("MicrositeImageId")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VanityUrl")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("ActiveImageId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DefaultImageId");

                    b.HasIndex("IconId");

                    b.HasIndex("ImageId");

                    b.HasIndex("MicrositeImageId");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("ParentId");

                    b.ToTable("ProjectDataTypes");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.PublishedDisplayOrderView", b =>
                {
                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("ProductId"), 1L, 1);

                    b.Property<long>("CRn")
                        .HasColumnType("bigint");

                    b.Property<int>("CategoryId")
                        .HasColumnType("int");

                    b.Property<long>("DisplayPosition")
                        .HasColumnType("bigint");

                    b.Property<long>("MRn")
                        .HasColumnType("bigint");

                    b.Property<int>("ManufacturerId")
                        .HasColumnType("int");

                    b.Property<long?>("PCRn")
                        .HasColumnType("bigint");

                    b.Property<long>("PRn")
                        .HasColumnType("bigint");

                    b.Property<int?>("ParentCategoryId")
                        .HasColumnType("int");

                    b.Property<int?>("ParentParentCategoryId")
                        .HasColumnType("int");

                    b.Property<int>("ResultBlock")
                        .HasColumnType("int");

                    b.Property<float>("Weight")
                        .HasColumnType("real");

                    b.HasKey("ProductId");

                    b.ToView("dbo.PublishedDisplayOrderView");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.QualityItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IconUrl")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.ToTable("QualityItems");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.RelatedDetail", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("DetailId")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("RelatedDetailId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DetailId");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("RelatedDetailId");

                    b.ToTable("RelatedDetails");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.RelatedProduct", b =>
                {
                    b.Property<int>("ProductId")
                        .HasColumnType("int")
                        .HasColumnOrder(1);

                    b.Property<int>("RelatedProductId")
                        .HasColumnType("int")
                        .HasColumnOrder(2);

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("ProductId", "RelatedProductId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("RelatedProductId");

                    b.ToTable("RelatedProducts");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ReportItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Message")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.ToTable("ReportItems");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.RevitProcess", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<DateTime>("DateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateModefided")
                        .HasColumnType("datetime2");

                    b.Property<int>("MarketField")
                        .HasColumnType("int");

                    b.Property<string>("ProcessName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RevitParameter")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("RevitProcesses");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.SalesRepresentative", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FirstName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("LastName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("LinkendInLink")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("ManufacturerId")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("ProfilePhotoId")
                        .HasColumnType("int");

                    b.Property<string>("WebsiteLink")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("WebsiteName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ManufacturerId");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("ProfilePhotoId");

                    b.ToTable("SalesRepresentatives");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.SearchHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("IPAddress")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("NumResults")
                        .HasColumnType("int");

                    b.Property<int>("NumSecondary")
                        .HasColumnType("int");

                    b.Property<string>("Options")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Query")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("QueryDate")
                        .HasColumnType("datetime2");

                    b.Property<long>("SearchTime")
                        .HasColumnType("bigint");

                    b.Property<long>("TotalTime")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("SearchHistories");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Setting", b =>
                {
                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Value")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Name");

                    b.ToTable("Settings");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Starter", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(38)
                        .HasColumnType("nvarchar(38)");

                    b.Property<string>("Brand")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("FIIC")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FSTC")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Fire")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ForgeManufacturerId")
                        .HasMaxLength(38)
                        .HasColumnType("nvarchar(38)");

                    b.Property<string>("IIC")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ManufacturerName")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("NFPA285")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("Orientation")
                        .HasColumnType("int");

                    b.Property<string>("Preview")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Published")
                        .HasColumnType("bit");

                    b.Property<string>("RValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("STC")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Staging")
                        .HasColumnType("bit");

                    b.Property<string>("UL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdateCacheTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("UtcDateCreated")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("UtcDateModified")
                        .HasColumnType("datetime2");

                    b.Property<float>("Weight")
                        .HasColumnType("real");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "Brand" }, "IX_Starter_Brand");

                    b.HasIndex(new[] { "ForgeManufacturerId" }, "IX_Starter_ForgeManufacturerId");

                    b.HasIndex(new[] { "ManufacturerName" }, "IX_Starter_ManufacturerName");

                    b.HasIndex(new[] { "Name" }, "IX_Starter_Name");

                    b.ToTable("Starters");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.StarterProductLines", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("ProductLineId")
                        .HasColumnType("int");

                    b.Property<string>("StarterId")
                        .HasMaxLength(38)
                        .HasColumnType("nvarchar(38)");

                    b.HasKey("Id");

                    b.HasIndex("ProductLineId");

                    b.HasIndex("StarterId");

                    b.ToTable("StarterProductLines");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.StaticExcelFile", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<int>("CancelledProductsCount")
                        .HasColumnType("int");

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ErrorProductsCount")
                        .HasColumnType("int");

                    b.Property<int?>("ManufacturerId")
                        .HasColumnType("int");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int>("SucceedProdutcsCount")
                        .HasColumnType("int");

                    b.Property<int>("TotalProductsCount")
                        .HasColumnType("int");

                    b.Property<string>("Url")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ValidationErrors")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ManufacturerId");

                    b.HasIndex("ModifiedById");

                    b.ToTable("StaticExcelFiles");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.StaticExcelProductError", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Errors")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("RowNumber")
                        .HasColumnType("int");

                    b.Property<int>("StaticExcelFileId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("StaticExcelFileId");

                    b.ToTable("StaticExcelProductErrors");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Synonym", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Alternate")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Keyword")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Synonyms");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ULCertificate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("ProductId");

                    b.ToTable("ULCertificates");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ULRatingSystemSustainableCredit", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("ProductId");

                    b.ToTable("ULRatingSystemSustainableCredits");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ULStandardNumber", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiedById")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ModifiedById");

                    b.HasIndex("ProductId");

                    b.ToTable("ULStandardNumbers");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Uniclass", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Code")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.HasIndex(new[] { "Code" }, "IX_Uniclass_Code");

                    b.HasIndex(new[] { "Title" }, "IX_Uniclass_Title");

                    b.ToTable("Uniclasses");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Uniformat", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("Code")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.HasIndex(new[] { "Code" }, "IX_Uniformat_Code");

                    b.HasIndex(new[] { "Title" }, "IX_Uniformat_Title");

                    b.ToTable("Uniformats");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.UserBIMsmithBIMManufacturer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("AddedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("AddedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ManufacturerId")
                        .HasColumnType("int");

                    b.Property<string>("Message")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProductLink")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProductName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int>("TimeZone")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AddedById");

                    b.HasIndex("ManufacturerId");

                    b.ToTable("UserBIMsmithBIMManufacturers");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.UserBIMsmithLLManufacturer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("AddedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("AddedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ManufacturerId")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AddedById");

                    b.HasIndex("ManufacturerId");

                    b.ToTable("UserBIMsmithLLManufacturers");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.UserBIMsmithLTManufacturer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("AddedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("AddedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ManufacturerId")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int>("Timezone")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AddedById");

                    b.HasIndex("ManufacturerId");

                    b.ToTable("UserBIMsmithLTManufacturers");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.UserBIMsmithManufacturer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("AddedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("AddedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ManufacturerId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AddedById");

                    b.HasIndex("ManufacturerId");

                    b.ToTable("UserBIMsmithManufacturers");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.UserBIMsmithProduct", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("AddedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("AddedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ProductId")
                        .HasColumnType("int");

                    b.Property<string>("StreamId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("AddedById");

                    b.HasIndex("ProductId");

                    b.ToTable("UserBIMsmithProducts");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.VanityHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<string>("CreatedById")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("EntityId")
                        .HasColumnType("int");

                    b.Property<string>("EntityType")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<string>("VanityUrl")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ParentId");

                    b.HasIndex(new[] { "VanityUrl" }, "IX_VanityHistory_VanityUrl");

                    b.ToTable("VanityHistories");
                });

            modelBuilder.Entity("HelpArticleHelpCategory", b =>
                {
                    b.Property<int>("HelpArticlesId")
                        .HasColumnType("int");

                    b.Property<int>("HelpCategoriesId")
                        .HasColumnType("int");

                    b.HasKey("HelpArticlesId", "HelpCategoriesId");

                    b.HasIndex("HelpCategoriesId");

                    b.ToTable("HelpArticleHelpCategory");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Address", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Announcement", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ApplicationRoleClaim", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ApplicationUserClaim", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ApplicationUserLogin", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ApplicationUserRole", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationRole", "Role")
                        .WithMany("UserRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "User")
                        .WithMany("Roles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ApplicationUserToken", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.AttachmentOrder", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Manufacturer", "Manufacturer")
                        .WithMany("AttachmentOrders")
                        .HasForeignKey("ManufacturerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Manufacturer");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.BlogCategory", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.BlogCategoryTargetType", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.BlogCategory", "BlogCategory")
                        .WithMany("TargetTypes")
                        .HasForeignKey("BlogCategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BlogCategory");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.BlogComment", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.BlogPost", "BlogPost")
                        .WithMany("BlogComments")
                        .HasForeignKey("BlogPostId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.Navigation("BlogPost");

                    b.Navigation("CreatedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.BlogMentionedEntry", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.BlogPost", "BlogPost")
                        .WithMany("BlogMentionedEntries")
                        .HasForeignKey("BlogPostId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BlogPost");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.BlogPost", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.BlogCategory", "BlogCategory")
                        .WithMany("BlogPosts")
                        .HasForeignKey("BlogCategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("BlogCategory");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.BlogTargetType", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.BlogPost", "BlogPost")
                        .WithMany("TargetTypes")
                        .HasForeignKey("BlogPostId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BlogPost");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Category", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Category", "ParentCategory")
                        .WithMany("Subcategories")
                        .HasForeignKey("ParentCategoryId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Photo", "RevitPluginPhoto")
                        .WithMany()
                        .HasForeignKey("RevitPluginPhotoId");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("ParentCategory");

                    b.Navigation("RevitPluginPhoto");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.CategoryKeyStat", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.Category", "Category")
                        .WithMany("CategoryKeyStats")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.KeyStat", "KeyStat")
                        .WithMany("CategoryKeyStats")
                        .HasForeignKey("KeyStatId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Category");

                    b.Navigation("CreatedBy");

                    b.Navigation("KeyStat");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ChangeLog", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Cisfb", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.Cisfb", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.CustomCategoryIcon", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.Category", "Category")
                        .WithMany("CustomCategoryIcons")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Manufacturer", "Manufacturer")
                        .WithMany("CustomCategoryIcons")
                        .HasForeignKey("ManufacturerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("Category");

                    b.Navigation("CreatedBy");

                    b.Navigation("Manufacturer");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Detail", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.DetailScale", "DetailScale")
                        .WithMany("Details")
                        .HasForeignKey("DetailScaleId");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Manufacturer", "Manufacturer")
                        .WithMany("Details")
                        .HasForeignKey("ManufacturerId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Photo", "Photo")
                        .WithMany()
                        .HasForeignKey("PhotoId");

                    b.Navigation("CreatedBy");

                    b.Navigation("DetailScale");

                    b.Navigation("Manufacturer");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Photo");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DetailApplication", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DetailDetailApplication", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.DetailApplication", "DetailApplication")
                        .WithMany("DetailDetailApplications")
                        .HasForeignKey("DetailApplicationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Detail", "Detail")
                        .WithMany("DetailDetailApplications")
                        .HasForeignKey("DetailId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("Detail");

                    b.Navigation("DetailApplication");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DetailFile", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Detail", "Detail")
                        .WithMany("DetailFiles")
                        .HasForeignKey("DetailId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.File", "File")
                        .WithMany()
                        .HasForeignKey("FileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ProjectDataType", "ProjectDataType")
                        .WithMany()
                        .HasForeignKey("ProjectDataTypeId");

                    b.Navigation("CreatedBy");

                    b.Navigation("Detail");

                    b.Navigation("File");

                    b.Navigation("ModifiedBy");

                    b.Navigation("ProjectDataType");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DetailMasterformat", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Detail", "Detail")
                        .WithMany("DetailMasterformats")
                        .HasForeignKey("DetailId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("Detail");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DetailPhoto", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Detail", "Detail")
                        .WithMany("DetailPhotos")
                        .HasForeignKey("DetailId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Photo", "Photo")
                        .WithMany()
                        .HasForeignKey("PhotoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Detail");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Photo");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DetailRating", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Detail", "Detail")
                        .WithMany("DetailRatings")
                        .HasForeignKey("DetailId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("Detail");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DetailScale", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DropboxSetting", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DynamicTranslationDbModels.DynamicTranslation", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.DynamicTranslationDbModels.TranslatableEntityField", "TranslatableEntityField")
                        .WithMany("DynamicTranslations")
                        .HasForeignKey("TranslatableEntityFieldId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("TranslatableEntityField");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DynamicTranslationDbModels.TranslatableEntity", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DynamicTranslationDbModels.TranslatableEntityField", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.DynamicTranslationDbModels.TranslatableEntity", "TranslatableEntity")
                        .WithMany("TranslatableEntityFields")
                        .HasForeignKey("TranslatableEntityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("TranslatableEntity");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Event", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.File", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.Navigation("CreatedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ForgeProductLineIds", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ProductLine", "ProductLine")
                        .WithMany("ForgeProductLineIds")
                        .HasForeignKey("ProductLineId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ProductLine");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.HealthDashboardAccess", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("User");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.HelpArticle", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.HelpCategory", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.HelpCategory", "ParentCategory")
                        .WithMany("Subcategories")
                        .HasForeignKey("ParentCategoryId");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("ParentCategory");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.KeyStat", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.KeyStatUnit", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.KeyStatUnitRelation", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.KeyStatUnit", "FromUnit")
                        .WithMany("FromUnitRelations")
                        .HasForeignKey("FromUnitId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.KeyStatUnit", "ToUnit")
                        .WithMany("ToUnitRelations")
                        .HasForeignKey("ToUnitId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("FromUnit");

                    b.Navigation("ModifiedBy");

                    b.Navigation("ToUnit");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.KeyStatValueList", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.KeyStatUnit", "ConvertKeyStatUnit")
                        .WithMany("ConvertKeyStatValueList")
                        .HasForeignKey("ConvertKeyStatUnitId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.KeyStatUnit", "KeyStatUnit")
                        .WithMany("KeyStatValueList")
                        .HasForeignKey("KeyStatUnitId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ProductLineStats", "ProductLineStats")
                        .WithMany("KeyStatValueList")
                        .HasForeignKey("ProductLineStatsId");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ProductStats", "ProductStats")
                        .WithMany("KeyStatValueList")
                        .HasForeignKey("ProductStatsId");

                    b.Navigation("ConvertKeyStatUnit");

                    b.Navigation("CreatedBy");

                    b.Navigation("KeyStatUnit");

                    b.Navigation("ProductLineStats");

                    b.Navigation("ProductStats");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Localization", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.Navigation("CreatedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Manufacturer", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.Address", "Address")
                        .WithMany("Manufacturers")
                        .HasForeignKey("AddressId");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Photo", "CustomSignInLogoImage")
                        .WithMany()
                        .HasForeignKey("CustomSignInLogoImageId");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Photo", "FooterAdImage")
                        .WithMany()
                        .HasForeignKey("FooterAdImageId");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Photo", "Image")
                        .WithMany()
                        .HasForeignKey("ImageId");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "Owner")
                        .WithMany()
                        .HasForeignKey("OwnerId");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Manufacturer", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Photo", "Photo")
                        .WithMany()
                        .HasForeignKey("PhotoId");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Photo", "RevitPluginPhoto")
                        .WithMany()
                        .HasForeignKey("RevitPluginPhotoId");

                    b.Navigation("Address");

                    b.Navigation("CreatedBy");

                    b.Navigation("CustomSignInLogoImage");

                    b.Navigation("FooterAdImage");

                    b.Navigation("Image");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Owner");

                    b.Navigation("Parent");

                    b.Navigation("Photo");

                    b.Navigation("RevitPluginPhoto");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ManufacturerAdditionalFile", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.File", "File")
                        .WithMany()
                        .HasForeignKey("FileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Manufacturer", "Manufacturer")
                        .WithMany("ManufacturerAdditionalFiles")
                        .HasForeignKey("ManufacturerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ProjectDataType", "ProjectDataType")
                        .WithMany()
                        .HasForeignKey("ProjectDataTypeId");

                    b.Navigation("CreatedBy");

                    b.Navigation("File");

                    b.Navigation("Manufacturer");

                    b.Navigation("ModifiedBy");

                    b.Navigation("ProjectDataType");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ManufacturerAdminUser", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "AddedBy")
                        .WithMany()
                        .HasForeignKey("AddedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "AdminUser")
                        .WithMany()
                        .HasForeignKey("AdminUserId");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Manufacturer", "Manufacturer")
                        .WithMany("ManufacturerAdminUsers")
                        .HasForeignKey("ManufacturerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("AddedBy");

                    b.Navigation("AdminUser");

                    b.Navigation("Manufacturer");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ManufacturerAnnouncement", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.Announcement", "Announcement")
                        .WithMany("ManufacturerAnnouncements")
                        .HasForeignKey("AnnouncementId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Manufacturer", "Manufacturer")
                        .WithMany("ManufacturerAnnouncements")
                        .HasForeignKey("ManufacturerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("Announcement");

                    b.Navigation("CreatedBy");

                    b.Navigation("Manufacturer");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ManufacturerFile", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.File", "File")
                        .WithMany()
                        .HasForeignKey("FileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Manufacturer", "Manufacturer")
                        .WithMany("ManufacturerFiles")
                        .HasForeignKey("ManufacturerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ProjectDataType", "ProjectDataType")
                        .WithMany()
                        .HasForeignKey("ProjectDataTypeId");

                    b.Navigation("CreatedBy");

                    b.Navigation("File");

                    b.Navigation("Manufacturer");

                    b.Navigation("ModifiedBy");

                    b.Navigation("ProjectDataType");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ManufacturerPhoto", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Manufacturer", "Manufacturer")
                        .WithMany("ManufacturerPhotos")
                        .HasForeignKey("ManufacturerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Photo", "Photo")
                        .WithMany()
                        .HasForeignKey("PhotoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Manufacturer");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Photo");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ManufacturerStyleFile", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.File", "File")
                        .WithMany()
                        .HasForeignKey("FileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Manufacturer", "Manufacturer")
                        .WithMany("ManufacturerStyleFiles")
                        .HasForeignKey("ManufacturerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("File");

                    b.Navigation("Manufacturer");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Masterformat", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.Masterformat", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.News", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.NewsCategory", "NewsCategory")
                        .WithMany("News")
                        .HasForeignKey("NewsCategoryId");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("NewsCategory");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.NewsTarget", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.News", "News")
                        .WithMany("NewsTargets")
                        .HasForeignKey("NewsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("News");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Note", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.NoteNotificationUser", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Note", "Note")
                        .WithMany("NotificationList")
                        .HasForeignKey("NoteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Note");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Omniclass", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.Omniclass", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.PaymentDbModels.Price", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.Product", "Product")
                        .WithOne("Price")
                        .HasForeignKey("BIMsmithMarket.Domain.DBModels.PaymentDbModels.Price", "ProductId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Product");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.PaymentDbModels.ProductPrice", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.PaymentDbModels.PaymentPlan", "PaymentPlan")
                        .WithMany("ProductPrices")
                        .HasForeignKey("PaymentPlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Product", "Product")
                        .WithMany("ProductPrices")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PaymentPlan");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.PaymentDbModels.UserPaidProduct", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Product", "Product")
                        .WithMany()
                        .HasForeignKey("PaidProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "User")
                        .WithMany("UserPaidProducts")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Product");

                    b.Navigation("User");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Photo", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.Navigation("CreatedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.PluginFile", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.File", "File")
                        .WithMany()
                        .HasForeignKey("FileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("File");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Product", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.Category", "Category")
                        .WithMany("Products")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany("CreatedProduct")
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Photo", "FooterAdImage")
                        .WithMany()
                        .HasForeignKey("FooterAdImageId");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Manufacturer", "Manufacturer")
                        .WithMany("Products")
                        .HasForeignKey("ManufacturerId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany("ModifiedProduct")
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Photo", "Photo")
                        .WithMany()
                        .HasForeignKey("PhotoId");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ProductLine", "ProductLine")
                        .WithMany("Products")
                        .HasForeignKey("ProductLineId");

                    b.Navigation("Category");

                    b.Navigation("CreatedBy");

                    b.Navigation("FooterAdImage");

                    b.Navigation("Manufacturer");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Photo");

                    b.Navigation("ProductLine");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductCategory", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.Category", "Category")
                        .WithMany("ProductCategories")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Product", "Product")
                        .WithMany("ProductCategories")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Category");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductCertificate", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Product", "Product")
                        .WithMany("ProductCertificates")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductCisfb", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.Cisfb", "Cisfb")
                        .WithMany()
                        .HasForeignKey("CisfbId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Product", "Product")
                        .WithMany("ProductCisfbs")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Cisfb");

                    b.Navigation("CreatedBy");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductDetail", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Detail", "Detail")
                        .WithMany("ProductDetails")
                        .HasForeignKey("DetailId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Product", "Product")
                        .WithMany("ProductDetails")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Detail");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductFile", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.File", "File")
                        .WithMany()
                        .HasForeignKey("FileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Product", "Product")
                        .WithMany("ProductFiles")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ProjectDataType", "ProjectDataType")
                        .WithMany()
                        .HasForeignKey("ProjectDataTypeId");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ProjectDataType", "SoftwareVersion")
                        .WithMany()
                        .HasForeignKey("SoftwareVersionId");

                    b.Navigation("CreatedBy");

                    b.Navigation("File");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Product");

                    b.Navigation("ProjectDataType");

                    b.Navigation("SoftwareVersion");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductLine", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Manufacturer", "Manufacturer")
                        .WithMany("ProductLines")
                        .HasForeignKey("ManufacturerId");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("Manufacturer");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductLineCertificate", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ProductLine", "ProductLine")
                        .WithMany("ProductLineCertificates")
                        .HasForeignKey("ProductLineId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("ProductLine");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductLineFile", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.File", "File")
                        .WithMany()
                        .HasForeignKey("FileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ProductLine", "ProductLine")
                        .WithMany("ProductLineFiles")
                        .HasForeignKey("ProductLineId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ProjectDataType", "ProjectDataType")
                        .WithMany()
                        .HasForeignKey("ProjectDataTypeId");

                    b.Navigation("CreatedBy");

                    b.Navigation("File");

                    b.Navigation("ModifiedBy");

                    b.Navigation("ProductLine");

                    b.Navigation("ProjectDataType");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductLineQualityItem", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ProductLine", "ProductLine")
                        .WithMany("ProductLineQualityItems")
                        .HasForeignKey("ProductLineId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.QualityItem", "QualityItem")
                        .WithMany()
                        .HasForeignKey("QualityItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("ProductLine");

                    b.Navigation("QualityItem");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductLineStats", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.KeyStatUnit", "ConvertKeyStatUnit")
                        .WithMany("ConvertProductLineStats")
                        .HasForeignKey("ConvertKeyStatUnitId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.KeyStat", "KeyStat")
                        .WithMany()
                        .HasForeignKey("KeyStatId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.KeyStatUnit", "KeyStatUnit")
                        .WithMany("ProductLineStats")
                        .HasForeignKey("KeyStatUnitId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ProductLine", "ProductLine")
                        .WithMany("ProductLineStats")
                        .HasForeignKey("ProductLineId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("ConvertKeyStatUnit");

                    b.Navigation("CreatedBy");

                    b.Navigation("KeyStat");

                    b.Navigation("KeyStatUnit");

                    b.Navigation("ModifiedBy");

                    b.Navigation("ProductLine");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductMasterformat", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Product", "Product")
                        .WithMany("ProductMasterformats")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductOmniclass", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Omniclass", "Omniclass")
                        .WithMany()
                        .HasForeignKey("OmniclassId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Product", "Product")
                        .WithMany("ProductOmniclasses")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Omniclass");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductPhoto", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Photo", "Photo")
                        .WithMany()
                        .HasForeignKey("PhotoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Product", "Product")
                        .WithMany("ProductPhotos")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Photo");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductQualityItem", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Product", "Product")
                        .WithMany("ProductQualityItems")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.QualityItem", "QualityItem")
                        .WithMany()
                        .HasForeignKey("QualityItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Product");

                    b.Navigation("QualityItem");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductRating", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "PostedBy")
                        .WithMany()
                        .HasForeignKey("PostedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Product", "Product")
                        .WithMany("ProductRatings")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("PostedBy");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductSample", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Product", "Product")
                        .WithMany("ProductSamples")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductStats", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.KeyStatUnit", "ConvertKeyStatUnit")
                        .WithMany("ConvertProductStats")
                        .HasForeignKey("ConvertKeyStatUnitId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.KeyStat", "KeyStat")
                        .WithMany("ProductStats")
                        .HasForeignKey("KeyStatId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.KeyStatUnit", "KeyStatUnit")
                        .WithMany("ProductStats")
                        .HasForeignKey("KeyStatUnitId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Product", "Product")
                        .WithMany("ProductStats")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ConvertKeyStatUnit");

                    b.Navigation("CreatedBy");

                    b.Navigation("KeyStat");

                    b.Navigation("KeyStatUnit");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductUniclass", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Product", "Product")
                        .WithMany("ProductUniclasses")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Uniclass", "Uniclass")
                        .WithMany()
                        .HasForeignKey("UniclassId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Product");

                    b.Navigation("Uniclass");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductUniformat", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Product", "Product")
                        .WithMany("ProductUniformats")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Uniformat", "Uniformat")
                        .WithMany()
                        .HasForeignKey("UniformatId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Product");

                    b.Navigation("Uniformat");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProjectDataType", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.Photo", "ActiveImage")
                        .WithMany()
                        .HasForeignKey("ActiveImageId");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Photo", "DefaultImage")
                        .WithMany()
                        .HasForeignKey("DefaultImageId");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Photo", "Icon")
                        .WithMany()
                        .HasForeignKey("IconId");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Photo", "Image")
                        .WithMany()
                        .HasForeignKey("ImageId");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Photo", "MicrositeImage")
                        .WithMany()
                        .HasForeignKey("MicrositeImageId");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ProjectDataType", "ParentDataType")
                        .WithMany("Children")
                        .HasForeignKey("ParentId");

                    b.Navigation("ActiveImage");

                    b.Navigation("CreatedBy");

                    b.Navigation("DefaultImage");

                    b.Navigation("Icon");

                    b.Navigation("Image");

                    b.Navigation("MicrositeImage");

                    b.Navigation("ModifiedBy");

                    b.Navigation("ParentDataType");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.QualityItem", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.RelatedDetail", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Detail", "Detail")
                        .WithMany("RelatedDetails")
                        .HasForeignKey("DetailId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Detail", "RelatedDetailItem")
                        .WithMany()
                        .HasForeignKey("RelatedDetailId");

                    b.Navigation("CreatedBy");

                    b.Navigation("Detail");

                    b.Navigation("ModifiedBy");

                    b.Navigation("RelatedDetailItem");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.RelatedProduct", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Product", "Product")
                        .WithMany("RelatedProducts")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Product", "RelatedProductItem")
                        .WithMany()
                        .HasForeignKey("RelatedProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Product");

                    b.Navigation("RelatedProductItem");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ReportItem", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.Navigation("CreatedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.SalesRepresentative", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Manufacturer", "Manufacturer")
                        .WithMany("SalesRepresentatives")
                        .HasForeignKey("ManufacturerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Photo", "ProfilePhoto")
                        .WithMany()
                        .HasForeignKey("ProfilePhotoId");

                    b.Navigation("CreatedBy");

                    b.Navigation("Manufacturer");

                    b.Navigation("ModifiedBy");

                    b.Navigation("ProfilePhoto");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.StarterProductLines", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ProductLine", "ProductLine")
                        .WithMany("Starters")
                        .HasForeignKey("ProductLineId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Starter", "Starter")
                        .WithMany("ProductLines")
                        .HasForeignKey("StarterId");

                    b.Navigation("ProductLine");

                    b.Navigation("Starter");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.StaticExcelFile", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Manufacturer", "Manufacturer")
                        .WithMany("StaticExcelFiles")
                        .HasForeignKey("ManufacturerId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("Manufacturer");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.StaticExcelProductError", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.StaticExcelFile", "StaticExcelFile")
                        .WithMany("StaticExcelProductErrors")
                        .HasForeignKey("StaticExcelFileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("StaticExcelFile");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ULCertificate", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Product", "Product")
                        .WithMany("ULCertificates")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ULRatingSystemSustainableCredit", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Product", "Product")
                        .WithMany("ULRatingSystemsSustainableCredits")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ULStandardNumber", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Product", "Product")
                        .WithMany("ULStandardNumbers")
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("ModifiedBy");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Uniclass", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.Uniclass", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Uniformat", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.Uniformat", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.UserBIMsmithBIMManufacturer", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "AddedBy")
                        .WithMany()
                        .HasForeignKey("AddedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Manufacturer", "Manufacturer")
                        .WithMany("UserBIMsmithBIMManufacturers")
                        .HasForeignKey("ManufacturerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AddedBy");

                    b.Navigation("Manufacturer");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.UserBIMsmithLLManufacturer", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "AddedBy")
                        .WithMany()
                        .HasForeignKey("AddedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Manufacturer", "Manufacturer")
                        .WithMany("UserBIMsmithLLManufacturers")
                        .HasForeignKey("ManufacturerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AddedBy");

                    b.Navigation("Manufacturer");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.UserBIMsmithLTManufacturer", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "AddedBy")
                        .WithMany()
                        .HasForeignKey("AddedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Manufacturer", "Manufacturer")
                        .WithMany("UserBIMsmithLTManufacturers")
                        .HasForeignKey("ManufacturerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AddedBy");

                    b.Navigation("Manufacturer");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.UserBIMsmithManufacturer", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "AddedBy")
                        .WithMany()
                        .HasForeignKey("AddedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Manufacturer", "Manufacturer")
                        .WithMany("UserBIMsmithManufacturers")
                        .HasForeignKey("ManufacturerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AddedBy");

                    b.Navigation("Manufacturer");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.UserBIMsmithProduct", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "AddedBy")
                        .WithMany()
                        .HasForeignKey("AddedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AddedBy");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.VanityHistory", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.ApplicationUser", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("BIMsmithMarket.Domain.DBModels.VanityHistory", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId");

                    b.Navigation("CreatedBy");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("HelpArticleHelpCategory", b =>
                {
                    b.HasOne("BIMsmithMarket.Domain.DBModels.HelpArticle", null)
                        .WithMany()
                        .HasForeignKey("HelpArticlesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("BIMsmithMarket.Domain.DBModels.HelpCategory", null)
                        .WithMany()
                        .HasForeignKey("HelpCategoriesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Address", b =>
                {
                    b.Navigation("Manufacturers");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Announcement", b =>
                {
                    b.Navigation("ManufacturerAnnouncements");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ApplicationRole", b =>
                {
                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ApplicationUser", b =>
                {
                    b.Navigation("CreatedProduct");

                    b.Navigation("ModifiedProduct");

                    b.Navigation("Roles");

                    b.Navigation("UserPaidProducts");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.BlogCategory", b =>
                {
                    b.Navigation("BlogPosts");

                    b.Navigation("TargetTypes");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.BlogPost", b =>
                {
                    b.Navigation("BlogComments");

                    b.Navigation("BlogMentionedEntries");

                    b.Navigation("TargetTypes");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Category", b =>
                {
                    b.Navigation("CategoryKeyStats");

                    b.Navigation("CustomCategoryIcons");

                    b.Navigation("ProductCategories");

                    b.Navigation("Products");

                    b.Navigation("Subcategories");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Cisfb", b =>
                {
                    b.Navigation("Children");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Detail", b =>
                {
                    b.Navigation("DetailDetailApplications");

                    b.Navigation("DetailFiles");

                    b.Navigation("DetailMasterformats");

                    b.Navigation("DetailPhotos");

                    b.Navigation("DetailRatings");

                    b.Navigation("ProductDetails");

                    b.Navigation("RelatedDetails");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DetailApplication", b =>
                {
                    b.Navigation("DetailDetailApplications");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DetailScale", b =>
                {
                    b.Navigation("Details");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DynamicTranslationDbModels.TranslatableEntity", b =>
                {
                    b.Navigation("TranslatableEntityFields");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.DynamicTranslationDbModels.TranslatableEntityField", b =>
                {
                    b.Navigation("DynamicTranslations");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.HelpCategory", b =>
                {
                    b.Navigation("Subcategories");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.KeyStat", b =>
                {
                    b.Navigation("CategoryKeyStats");

                    b.Navigation("ProductStats");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.KeyStatUnit", b =>
                {
                    b.Navigation("ConvertKeyStatValueList");

                    b.Navigation("ConvertProductLineStats");

                    b.Navigation("ConvertProductStats");

                    b.Navigation("FromUnitRelations");

                    b.Navigation("KeyStatValueList");

                    b.Navigation("ProductLineStats");

                    b.Navigation("ProductStats");

                    b.Navigation("ToUnitRelations");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Manufacturer", b =>
                {
                    b.Navigation("AttachmentOrders");

                    b.Navigation("Children");

                    b.Navigation("CustomCategoryIcons");

                    b.Navigation("Details");

                    b.Navigation("ManufacturerAdditionalFiles");

                    b.Navigation("ManufacturerAdminUsers");

                    b.Navigation("ManufacturerAnnouncements");

                    b.Navigation("ManufacturerFiles");

                    b.Navigation("ManufacturerPhotos");

                    b.Navigation("ManufacturerStyleFiles");

                    b.Navigation("ProductLines");

                    b.Navigation("Products");

                    b.Navigation("SalesRepresentatives");

                    b.Navigation("StaticExcelFiles");

                    b.Navigation("UserBIMsmithBIMManufacturers");

                    b.Navigation("UserBIMsmithLLManufacturers");

                    b.Navigation("UserBIMsmithLTManufacturers");

                    b.Navigation("UserBIMsmithManufacturers");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Masterformat", b =>
                {
                    b.Navigation("Children");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.News", b =>
                {
                    b.Navigation("NewsTargets");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.NewsCategory", b =>
                {
                    b.Navigation("News");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Note", b =>
                {
                    b.Navigation("NotificationList");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Omniclass", b =>
                {
                    b.Navigation("Children");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.PaymentDbModels.PaymentPlan", b =>
                {
                    b.Navigation("ProductPrices");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Product", b =>
                {
                    b.Navigation("Price");

                    b.Navigation("ProductCategories");

                    b.Navigation("ProductCertificates");

                    b.Navigation("ProductCisfbs");

                    b.Navigation("ProductDetails");

                    b.Navigation("ProductFiles");

                    b.Navigation("ProductMasterformats");

                    b.Navigation("ProductOmniclasses");

                    b.Navigation("ProductPhotos");

                    b.Navigation("ProductPrices");

                    b.Navigation("ProductQualityItems");

                    b.Navigation("ProductRatings");

                    b.Navigation("ProductSamples");

                    b.Navigation("ProductStats");

                    b.Navigation("ProductUniclasses");

                    b.Navigation("ProductUniformats");

                    b.Navigation("RelatedProducts");

                    b.Navigation("ULCertificates");

                    b.Navigation("ULRatingSystemsSustainableCredits");

                    b.Navigation("ULStandardNumbers");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductLine", b =>
                {
                    b.Navigation("ForgeProductLineIds");

                    b.Navigation("ProductLineCertificates");

                    b.Navigation("ProductLineFiles");

                    b.Navigation("ProductLineQualityItems");

                    b.Navigation("ProductLineStats");

                    b.Navigation("Products");

                    b.Navigation("Starters");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductLineStats", b =>
                {
                    b.Navigation("KeyStatValueList");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProductStats", b =>
                {
                    b.Navigation("KeyStatValueList");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.ProjectDataType", b =>
                {
                    b.Navigation("Children");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Starter", b =>
                {
                    b.Navigation("ProductLines");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.StaticExcelFile", b =>
                {
                    b.Navigation("StaticExcelProductErrors");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Uniclass", b =>
                {
                    b.Navigation("Children");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.Uniformat", b =>
                {
                    b.Navigation("Children");
                });

            modelBuilder.Entity("BIMsmithMarket.Domain.DBModels.VanityHistory", b =>
                {
                    b.Navigation("Children");
                });
#pragma warning restore 612, 618
        }
    }
}
