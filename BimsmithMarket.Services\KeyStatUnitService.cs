﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class KeyStatUnitService : IKeyStatUnitService
    {
        public async Task<object> GroupsAsync(IUnitOfWork unitOfWork)
        {
            var query = unitOfWork.KeyStatUnitRepository.GetAll();

            var list = await query
                .OrderBy(a => a.GroupName)
                .GroupBy(a => a.GroupName)
                .Select(u => u.Key)
                .AsNoTracking()
                .ToListAsync();

            return list;
        }

        public async Task<object> ListAsync(IUnitOfWork unitOfWork, string q, string group, int count, int offset)
        {
            var query = unitOfWork.KeyStatUnitRepository.GetAll();

            if (!string.IsNullOrEmpty(q))
            {
                query = query.Where(a => a.BUnitName.Contains(q) || a.AUnitName.Contains(q) || a.Description.Contains(q));
            }

            if (!string.IsNullOrEmpty(group))
            {
                query = query.Where(a => a.GroupName == group);
            }

            var countOfData = await query.CountAsync();

            var list = await query
                .OrderBy(a => a.BUnitName)
                .Select(u => new
                {
                    id = u.Id,
                    groupName = u.GroupName,
                    aUnitName = u.AUnitName,
                    aFormat = u.AFormat,
                    bUnitName = u.BUnitName,
                    description = u.Description,
                    unitMetricType = u.UnitMetricType,
                    createdDate = u.CreatedDate
                })
                .AsNoTracking()
                .ToListAsync();

            return new
            {
                count = countOfData,
                data = list,
            };
        }

        public async Task<object> GetAsync(IUnitOfWork unitOfWork, int id)
        {
            var item = await unitOfWork.KeyStatUnitRepository.GetAll()
                    .Where(a => a.Id == id)
                    .Select(u => new
                    {
                        id = u.Id,
                        groupName = u.GroupName,
                        aUnitName = u.AUnitName,
                        aFormat = u.AFormat,
                        bUnitName = u.BUnitName,
                        description = u.Description,
                        unitMetricType = u.UnitMetricType,
                        relations = u.FromUnitRelations.Select(r => new
                        {
                            toKeyStatUnitId = r.ToUnitId,
                            groupName = r.ToUnit.GroupName,
                            aUnitName = r.ToUnit.AUnitName,
                            aFormat = r.ToUnit.AFormat,
                            bUnitName = r.ToUnit.BUnitName,
                            description = r.ToUnit.Description,
                            relation = r.Relation,
                            isDefault = r.IsDefault,
                            roundCountDigits = r.RoundCountDigits,
                        }),
                        createdDate = u.CreatedDate
                    })
                    .FirstOrDefaultAsync();

            if (item == null)
            {
                throw new InvalidInputException("Not found the Key stat unit");
            }

            return item;
        }

        public async Task<object> AddAsync(IUnitOfWork unitOfWork, AddKeyStatUnitModel model, string userId)
        {
            unitOfWork.BeginTransaction();

            KeyStatUnit keyStatUnit = new KeyStatUnit();
            keyStatUnit.GroupName = model.GroupName;
            keyStatUnit.AUnitName = model.AUnitName;
            keyStatUnit.AFormat = model.AFormat;
            keyStatUnit.BUnitName = model.BUnitName;
            keyStatUnit.Description = model.Description;
            keyStatUnit.UnitMetricType = model.UnitMetricType;
            keyStatUnit.CreatedById = userId;
            keyStatUnit.CreatedDate = DateTime.UtcNow;

            unitOfWork.KeyStatUnitRepository.Insert(keyStatUnit);
            await unitOfWork.SaveAsync();

            unitOfWork.KeyStatUnitRepository.Edit(keyStatUnit);

            if (model.Relations != null)
            {
                foreach (var relationModel in model.Relations)
                {
                    KeyStatUnitRelation relation = new KeyStatUnitRelation();
                    relation.FromUnitId = keyStatUnit.Id;
                    relation.ToUnitId = relationModel.ToKeyStatUnitId;
                    relation.Relation = relationModel.Relation;
                    relation.IsDefault = relationModel.IsDefault;
                    relation.RoundCountDigits = relationModel.RoundCountDigits;
                    relation.CreatedById = userId;
                    relation.CreatedDate = DateTime.UtcNow;

                    unitOfWork.KeyStatUnitRelationRepository.Insert(relation);
                }
            }

            await unitOfWork.SaveAsync();

            unitOfWork.CommitTransaction();

            return new
            {
                id = keyStatUnit.Id,
            };
        }

        public async Task EditAsync(IUnitOfWork unitOfWork, EditKeyStatUnitModel model, string userId)
        {
            unitOfWork.BeginTransaction();

            KeyStatUnit keyStatUnit = await unitOfWork.KeyStatUnitRepository.GetByIdAsync(model.Id);
            keyStatUnit.GroupName = model.GroupName;
            keyStatUnit.AUnitName = model.AUnitName;
            keyStatUnit.AFormat = model.AFormat;
            keyStatUnit.BUnitName = model.BUnitName;
            keyStatUnit.Description = model.Description;
            keyStatUnit.UnitMetricType = model.UnitMetricType;
            keyStatUnit.ModifiedById = userId;
            keyStatUnit.ModifiedDate = DateTime.UtcNow;

            var existingRelations = await unitOfWork.KeyStatUnitRelationRepository.GetAll().Where(a => a.FromUnitId == keyStatUnit.Id).ToListAsync();
            if (model.Relations != null)
            {
                foreach (var relationModel in model.Relations)
                {
                    KeyStatUnitRelation relation = existingRelations.FirstOrDefault(a => a.ToUnitId == relationModel.ToKeyStatUnitId);

                    if (relation == null) //add new
                    {
                        relation = new KeyStatUnitRelation();
                        relation.FromUnitId = keyStatUnit.Id;
                        relation.ToUnitId = relationModel.ToKeyStatUnitId;
                        relation.Relation = relationModel.Relation;
                        relation.IsDefault = relationModel.IsDefault;
                        relation.RoundCountDigits = relationModel.RoundCountDigits;
                        relation.CreatedById = userId;
                        relation.CreatedDate = DateTime.UtcNow;
                        unitOfWork.KeyStatUnitRelationRepository.Insert(relation);
                    }
                    else
                    {
                        existingRelations.Remove(relation);

                        if (relation.Relation != relationModel.Relation || relation.Relation != relationModel.Relation || relation.RoundCountDigits != relationModel.RoundCountDigits)
                        {
                            relation.Relation = relationModel.Relation;
                            relation.IsDefault = relationModel.IsDefault;
                            relation.RoundCountDigits = relationModel.RoundCountDigits;
                            relation.ModifiedById = userId;
                            relation.ModifiedDate = DateTime.UtcNow;
                            unitOfWork.KeyStatUnitRelationRepository.Edit(relation);
                        }
                    }
                }
            }

            if (existingRelations.Any()) //delete old relations
            {
                unitOfWork.KeyStatUnitRelationRepository.Delete(existingRelations);
            }

            unitOfWork.KeyStatUnitRepository.Edit(keyStatUnit);

            await unitOfWork.SaveAsync();

            unitOfWork.CommitTransaction();
        }

        public async Task DeleteAsync(IUnitOfWork unitOfWork, int id)
        {
            unitOfWork.BeginTransaction();

            var keyStatUnit = await unitOfWork.KeyStatUnitRepository.GetByIdAsync(id);

            var keyStatValues = keyStatUnit.KeyStatValueList.ToList();

            if (keyStatValues.Count > 0)
            {
                unitOfWork.KeyStatValueListRepository.Delete(keyStatValues);
            }

            var productStats = keyStatUnit.ProductStats.ToList();
            if (productStats.Count > 0)
            {
                unitOfWork.ProductStatsRepository.Delete(productStats);
            }
            unitOfWork.KeyStatUnitRepository.Delete(keyStatUnit);

            await unitOfWork.SaveAsync();

            unitOfWork.CommitTransaction();
        }
    }
}
