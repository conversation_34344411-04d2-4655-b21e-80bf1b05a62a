﻿using BIMsmithMarket.Domain.Dto;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IStarterService
    {
        Task RefreshStartersFromForgeAsync(string forgeManufacturerId = null);
        Task<PaginationListDto<StarterListDto>> ListAsync(int manufacturerId = -1, bool? published = null, bool? staging = null, string q = null, int offset = 0, int count = 10);
        Task SetPublishStatusAsync(SetStarterStatusDto model);
        Task SetStagingStatusAsync(SetStarterStatusDto model);
        Task UpdateWeightsAsync(UpdateWeightsDto model);
    }
}
