﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class ProductCertificate
    {
        [Key]
        public int ProductCertificateId { get; set; }

        public int ProductId { get; set; }

        public int? ExternalCertificateId { get; set; }

        public string CreatedById { get; set; }

        public DateTime CreatedDate { get; set; }

        /// ------------------------------------------
        [ForeignKey("CreatedById")]
        public virtual ApplicationUser CreatedBy { get; set; }

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }
    }
}