﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using Mapster;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public static class EventMap
    {
        public static void Register(TypeAdapterConfig config)
        {
            config.ForType<Event, AdminListEventDto>()
                .Map(d => d.UserName, s => s.CreatedBy.FirstName + " " + s.CreatedBy.LastName);

            config.ForType<AddEditEventDto, Event>()
                .Map(d => d.Title, s => s.Title.Trim());

            config.ForType<Event, PublicListEventDto>()
                .Map(d => d.Date, s => s.Date.ToString("yyyy-MM-dd"));
        }
    }
}