﻿using BIMsmithMarket.Domain.Enums.RevitProcessing;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels.RevitProcessing
{
    public class RevitProcess : BaseEntity
    {
        public RevitProcessType Type { get; set; }

        public RevitProcessStatus Status { get; set; }

        public DateTime? FinishDate { get; set; }

        public int ManufacturerId { get; set; }

        [ForeignKey("ManufacturerId")]
        public virtual Manufacturer Manufacturer { get; set; }

        public virtual ICollection<RevitProcessProduct> RevitProcessProducts { get; set; }

        public virtual ICollection<RevitProcessRevitParameterMapping> RevitParameterMappings { get; set; }

        public virtual ICollection<RevitProcessProjectDataType> RevitProcessProjectDataTypes { get; set; }

        public virtual ICollection<RevitJob> RevitJobs { get; set; }

        public RevitProcess()
        {
            RevitProcessProducts = new List<RevitProcessProduct>();
            RevitParameterMappings = new List<RevitProcessRevitParameterMapping>();
            RevitProcessProjectDataTypes = new List<RevitProcessProjectDataType>();
            RevitJobs = new List<RevitJob>();
        }
    }
}