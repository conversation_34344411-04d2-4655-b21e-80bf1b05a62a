﻿using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Dto.ProductLineExcelDto
{
    public class ProductLineExcelDto : BaseExcelDto
    {
        public int? Id { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public string Notes { get; set; }

        public IEnumerable<ProductLineExternalAttachmentDto> ExternalAttachments { get; set; }

        public IEnumerable<int> CertificateIds { get; set; }

        public IEnumerable<int> QualityItemIds { get; set; }

        public IEnumerable<string> ForgeProductLineIds { get; set; }

        public ProductLineExcelDto()
        {
            ExternalAttachments = new List<ProductLineExternalAttachmentDto>();
            CertificateIds = new List<int>();
            QualityItemIds = new List<int>();
            ForgeProductLineIds = new List<string>();
            Errors = new List<string>();
        }
    }

    public class ProductLineExternalAttachmentDto
    {
        public string SyncUrl { get; set; }

        public string Title { get; set; }
    }
}