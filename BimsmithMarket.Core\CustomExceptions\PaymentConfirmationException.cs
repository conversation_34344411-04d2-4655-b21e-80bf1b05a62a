﻿using System;

namespace BIMsmithMarket.Core.CustomExceptions
{
    public class PaymentConfirmationException : Exception
    {
        public PaymentConfirmationException()
        {
        }
        public PaymentConfirmationException(string message) : base(message)
        {
        }

        public PaymentConfirmationException(string message, Exception inner)
            : base(message, inner)
        {
        }
    }
}