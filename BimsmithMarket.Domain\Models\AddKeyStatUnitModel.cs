﻿using BIMsmithMarket.Domain.DBModels;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Models
{
    public class AddKeyStatUnitModel
    {
        [Required]
        public string GroupName { get; set; }

        public string AUnitName { get; set; }

        public string AFormat { get; set; }

        public string BUnitName { get; set; }


        [Required(AllowEmptyStrings = true)]
        public string Description { get; set; }

        [Required]
        public UnitMetricType UnitMetricType { get; set; }

        public List<KeyStatUnitRelationModel> Relations { get; set; }
    }

    public class EditKeyStatUnitModel : AddKeyStatUnitModel
    {
        [Required]
        public int Id { get; set; }
    }

    public class KeyStatUnitTranslation
    {
        [Required]
        public string LangCode { get; set; }

        [Required]
        public string Name { get; set; }
    }

    public class KeyStatUnitRelationModel
    {
        public int ToKeyStatUnitId { get; set; }

        public string Relation { get; set; }

        public bool IsDefault { get; set; }

        public int RoundCountDigits { get; set; }
    }
}