﻿using Azure.Storage.Blobs.Specialized;
using Azure.Storage.Queues.Models;
using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Quartz;
using Serilog;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Jobs
{
    [DisallowConcurrentExecution]
    public class ULPartnershipJob : IJob
    {
        private readonly string FolderFiles = Path.Combine(Path.GetTempPath(), ConfigurationHelper.GetValue("Environment"), "Files");
        private const string SourceName = "BIMsmith";
        private const string OmniclassGroupName = "Omniclass";
        private const string MasterSpecGroupName = "MasterSpec";
        private IMasterformatService _masterformatService;
        private readonly string _jobName = "UL Partnership Job";
        private readonly IAzureStorageService _azureStorageService;
        private readonly IHttpClientFactory _httpClientFactory;

        public ULPartnershipJob(
            IMasterformatService masterformatService,
            IAzureStorageService azureStorageService,
            IHttpClientFactory httpClientFactory)
        {
            _masterformatService = masterformatService;
            _azureStorageService = azureStorageService;
            _httpClientFactory = httpClientFactory;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            Log.Information($"[{_jobName}] started");

            if (!Directory.Exists(FolderFiles))
                Directory.CreateDirectory(FolderFiles);

            try
            {
                var ulProductsQueue = _azureStorageService.GetQueueByName(AzureStorageConstants.UlProductsQueue);

                if (await _azureStorageService.GetMessageCountAsync(ulProductsQueue.Name) == 0)
                    return;

                var filesContainer = _azureStorageService.GetContainerByName(AzureStorageConstants.FilesContainer);

                HttpClient httpClient = _httpClientFactory.CreateClient();

                var partnershipUrl = "https://spot.ul.com";
                var clientToken = Base64Encode("#vaEtY*MuZ:cbZTwjufkgzRFQ^HdU$a");
                var aid = "5cbe169165d99ae94fca0877";
                var passwrod = "zvW7nVOdH!s4";
                if (IsDevEnvironment())
                {
                    partnershipUrl = "https://spot.ul-dev.com";
                    aid = "5c7ee34d79374f85332e527d ";
                    clientToken = "I3ZhRXRZKk11WjpjYlpUd2p1ZmtnelJGUV5IZFUkYQ==";
                    passwrod = "xvr$G1^6W#t#";
                }

                httpClient.BaseAddress = new Uri(partnershipUrl);
                httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                httpClient.DefaultRequestHeaders.Add("AID", aid);

                while (await _azureStorageService.GetMessageCountAsync(ulProductsQueue.Name) > 0)
                {
                    QueueMessage message = ulProductsQueue.ReceiveMessage().Value;
                    if (message != null)
                    {
                        using (IUnitOfWork unitOfWork = UnitOfWork.Create())
                        {
                            try
                            {
                                int productId = -1;
                                if (int.TryParse(message.MessageText, out productId))
                                {
                                    unitOfWork.BeginTransaction();
                                    var product = unitOfWork.ProductRepository.GetAll().Include(a => a.Manufacturer).Where(a => a.Id == productId).FirstOrDefault();
                                    if (product != null && !string.IsNullOrWhiteSpace(product.ULUrl))
                                    {
                                        try
                                        {
                                            var partnerId = new Uri(product.ULUrl).AbsolutePath.Split('/').Last();  //"5ad1ea1a55b0e82d946a37c8"; prod // "5acf0b874b4cdb8b4c467551"; dev
                                            using (var authRequest = new HttpRequestMessage(HttpMethod.Post, "data/spot/api/v1/token"))
                                            {
                                                authRequest.Headers.Add("Authorization", "Basic " + clientToken);
                                                var contentDictionary = new Dictionary<string, string>();
                                                contentDictionary.Add("grant_type", "password");
                                                contentDictionary.Add("username", "<EMAIL>");
                                                contentDictionary.Add("password", passwrod);

                                                authRequest.Content = new FormUrlEncodedContent(contentDictionary);

                                                var authResponse = await httpClient.SendAsync(authRequest);
                                                if (authResponse.StatusCode == HttpStatusCode.OK)
                                                {
                                                    var authResponseModel = System.Text.Json.JsonSerializer.Deserialize<dynamic>(await authResponse.Content.ReadAsStringAsync());
                                                    var token = $"{authResponseModel.token_type} {authResponseModel.access_token}";

                                                    using (var detailRequest = new HttpRequestMessage(HttpMethod.Post, "data/spot/api/v1/en/products/detail"))
                                                    {
                                                        detailRequest.Headers.Add("Authorization", token);

                                                        var detailBodyContent = JsonConvert.SerializeObject(new
                                                        {
                                                            productId = partnerId,
                                                            lang = "en",
                                                            keywords = string.Empty,
                                                            filter = string.Empty
                                                        });
                                                        detailRequest.Content = new StringContent(detailBodyContent, Encoding.UTF8, "application/json");

                                                        var detailResponse = await httpClient.SendAsync(detailRequest);
                                                        if (detailResponse.StatusCode == HttpStatusCode.OK)
                                                        {
                                                            var detailResponseModel = System.Text.Json.JsonSerializer.Deserialize<dynamic>(await detailResponse.Content.ReadAsStringAsync());
                                                            List<dynamic> documents = ConvertToList(detailResponseModel.documents);

                                                            var files = unitOfWork.ProductRepository.GetAll()
                                                                .Where(a => a.Id == product.Id)
                                                                .SelectMany(a => a.ProductFiles)
                                                                .Where(a => a.IsULPartnership)
                                                                .Select(a => a.File)
                                                                .Distinct().ToList();
                                                            unitOfWork.FileRepository.Delete(files);

                                                            var productfiles = unitOfWork.ProductRepository.GetAll()
                                                                .Where(a => a.Id == product.Id)
                                                                .SelectMany(a => a.ProductFiles)
                                                                .Where(a => a.IsULPartnership)
                                                                .Distinct().ToList();
                                                            unitOfWork.ProductFileRepository.Delete(productfiles);
                                                            unitOfWork.Save();

                                                            foreach (var document in documents)
                                                            {
                                                                string attachUrl = document.url;
                                                                var fileName = document.type + ".pdf";
                                                                Domain.DBModels.File dbFile = new Domain.DBModels.File();
                                                                dbFile.FileName = fileName;
                                                                dbFile.Title = document.name;
                                                                dbFile.MediaType = "application/pdf";
                                                                dbFile.CreatedById = "00000000-0000-1234-0000-000000009999";
                                                                dbFile.CreatedDate = DateTime.UtcNow;
                                                                dbFile.PreviewUrl = "https://market.bimsmith.com/assets/img/default_preview.png";
                                                                dbFile.NextSyncDateTime = DateTime.UtcNow.Date.AddMonths(1);
                                                                dbFile.SyncStatusCode = (int)HttpStatusCode.OK;
                                                                dbFile.UpdatesCount = 0;
                                                                unitOfWork.FileRepository.Insert(dbFile);
                                                                unitOfWork.Save();

                                                                string blobName = dbFile.Id.ToString() + Path.GetExtension(fileName);
                                                                BlockBlobClient fileBlobUpload = filesContainer.GetBlockBlobClient(blobName);

                                                                //Save local to create preview and then delete this local file
                                                                string filePath = Path.Combine(FolderFiles, blobName);

                                                                try
                                                                {
                                                                    int redirectsCount = 0;
                                                                StartRedirect:
                                                                    if (attachUrl.StartsWith("https://"))
                                                                    {
                                                                        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
                                                                    }

                                                                    HttpClient filehttpClient = _httpClientFactory.CreateClient();
                                                                    {
                                                                        filehttpClient.Timeout = TimeSpan.FromMinutes(5);

                                                                        using (var fileResponse = await filehttpClient.GetAsync(attachUrl))
                                                                        {
                                                                            if (fileResponse.StatusCode == HttpStatusCode.OK ||
                                                                                fileResponse.StatusCode == HttpStatusCode.Accepted ||
                                                                                fileResponse.StatusCode == HttpStatusCode.Found)
                                                                            {
                                                                                using (Stream fileStream = await fileResponse.Content.ReadAsStreamAsync())
                                                                                {
                                                                                    using (var file = System.IO.File.Create(filePath))
                                                                                    {
                                                                                        fileStream.CopyTo(file);
                                                                                    }
                                                                                }
                                                                            }
                                                                            else if ((fileResponse.StatusCode == HttpStatusCode.Moved || fileResponse.StatusCode == HttpStatusCode.MovedPermanently || fileResponse.StatusCode == HttpStatusCode.Found) &&
                                                                                !string.IsNullOrWhiteSpace(fileResponse.Headers.Location?.ToString()) &&
                                                                                redirectsCount < 3)
                                                                            {
                                                                                redirectsCount++;
                                                                                attachUrl = fileResponse.Headers.Location?.ToString();
                                                                                fileResponse.Dispose();
                                                                                goto StartRedirect;
                                                                            }
                                                                        }

                                                                        var thumbnail = ThumbnailProvider.GetAndSaveThumbnail(filePath, dbFile.MediaType, dbFile.Id);
                                                                        if (thumbnail.HasError == false) //Upalod preview on file server
                                                                        {
                                                                            var previewBlobName = Path.GetFileName(thumbnail.FilePath);
                                                                            BlockBlobClient previewBlobUpload = filesContainer.GetBlockBlobClient(previewBlobName);
                                                                            using (FileStream fs = System.IO.File.OpenRead(thumbnail.FilePath))
                                                                                previewBlobUpload.Upload(fs);
                                                                            dbFile.PreviewUrl = previewBlobUpload.Uri.ToString();
                                                                            System.IO.File.Delete(thumbnail.FilePath);//delete local file
                                                                        }
                                                                        else
                                                                        {
                                                                            Log.Error($"[{_jobName}] " + thumbnail.Error);
                                                                        }
                                                                        using (FileStream fs = System.IO.File.OpenRead(filePath))
                                                                            fileBlobUpload.Upload(fs);
                                                                        dbFile.Url = fileBlobUpload.Uri.ToString();

                                                                        FileInfo fi = new FileInfo(filePath);

                                                                        dbFile.FileSize = fi.Length;
                                                                        using (StreamReader streamReader = new StreamReader(filePath))
                                                                        {
                                                                            dbFile.CheckSum = HashProvider.CalculateMD5Hash(streamReader.BaseStream);
                                                                        }

                                                                        System.IO.File.Delete(filePath);//delete local file

                                                                        dbFile.SyncUrl = attachUrl;
                                                                        unitOfWork.FileRepository.Edit(dbFile);
                                                                        unitOfWork.Save();
                                                                    }
                                                                }
                                                                catch (Exception e)
                                                                {
                                                                    Log.Error($"[{_jobName}] " + e.Message, e);
                                                                    dbFile.SyncStatusCode = 500;
                                                                    unitOfWork.FileRepository.Edit(dbFile);
                                                                    unitOfWork.Save();
                                                                }

                                                                var productFile = new ProductFile
                                                                {
                                                                    ProductId = product.Id,
                                                                    FileId = dbFile.Id,
                                                                    IsULPartnership = true,
                                                                    CreatedById = "00000000-0000-1234-0000-000000009999",
                                                                    CreatedDate = DateTime.UtcNow
                                                                };
                                                                unitOfWork.ProductFileRepository.Insert(productFile);
                                                                unitOfWork.Save();
                                                            }

                                                            List<dynamic> categories = ConvertToList(detailResponseModel.categories);
                                                            List<dynamic> ratingSystemsSustainableCredits = categories.Where(a => a.group == "Rating Systems/Sustainable Credits").ToList();
                                                            List<dynamic> certificates = categories.Where(a => a.group == "Certification").ToList();
                                                            List<dynamic> standardNumbers = categories.Where(a => a.group == "Standard Number").ToList();

                                                            var ulRatingSystemSustainableCredits = unitOfWork.ULRatingSystemSustainableCreditRepository.GetAll().Where(a => a.ProductId == product.Id).ToList();
                                                            unitOfWork.ULRatingSystemSustainableCreditRepository.Delete(ulRatingSystemSustainableCredits);

                                                            var ulStandardNumbers = unitOfWork.ULStandardNumberRepository.GetAll().Where(a => a.ProductId == product.Id).ToList();
                                                            unitOfWork.ULStandardNumberRepository.Delete(ulStandardNumbers);

                                                            var ulCertificates = unitOfWork.ULCertificateRepository.GetAll().Where(a => a.ProductId == product.Id).ToList();
                                                            unitOfWork.ULCertificateRepository.Delete(ulCertificates);
                                                            unitOfWork.Save();

                                                            foreach (var ratingSystemsSustainableCredit in ratingSystemsSustainableCredits)
                                                            {
                                                                var ratingModel = new ULRatingSystemSustainableCredit
                                                                {
                                                                    ProductId = product.Id,
                                                                    Name = ratingSystemsSustainableCredit.name,
                                                                    CreatedById = "00000000-0000-1234-0000-000000009999",
                                                                    CreatedDate = DateTime.UtcNow
                                                                };
                                                                unitOfWork.ULRatingSystemSustainableCreditRepository.Insert(ratingModel);
                                                            }
                                                            unitOfWork.Save();

                                                            foreach (var certificate in certificates)
                                                            {
                                                                var certificateModel = new ULCertificate
                                                                {
                                                                    ProductId = product.Id,
                                                                    Name = certificate.name,
                                                                    CreatedById = "00000000-0000-1234-0000-000000009999",
                                                                    CreatedDate = DateTime.UtcNow
                                                                };
                                                                unitOfWork.ULCertificateRepository.Insert(certificateModel);
                                                            }
                                                            unitOfWork.Save();

                                                            foreach (var standardNumber in standardNumbers)
                                                            {
                                                                var standardNumberModel = new ULStandardNumber
                                                                {
                                                                    ProductId = product.Id,
                                                                    Name = standardNumber.name,
                                                                    CreatedById = "00000000-0000-1234-0000-000000009999",
                                                                    CreatedDate = DateTime.UtcNow
                                                                };
                                                                unitOfWork.ULStandardNumberRepository.Insert(standardNumberModel);
                                                            }
                                                            unitOfWork.Save();
                                                        }
                                                    }
                                                }
                                            }

                                            if (product.Manufacturer.SendULInfo)
                                            {
                                                using (var authRequest = new HttpRequestMessage(HttpMethod.Post, "data/spot/api/v1/token"))
                                                {
                                                    authRequest.Headers.Add("Authorization", "Basic I3ZhRXRZKk11WjpjYlpUd2p1ZmtnelJGUV5IZFUkYQ==");
                                                    var contentDictionary = new Dictionary<string, string>();
                                                    contentDictionary.Add("grant_type", "password");
                                                    contentDictionary.Add("username", "<EMAIL>");
                                                    contentDictionary.Add("password", "xvr$G1^6W#t#");

                                                    authRequest.Content = new FormUrlEncodedContent(contentDictionary);

                                                    var authResponse = await httpClient.SendAsync(authRequest);
                                                    if (authResponse.StatusCode == HttpStatusCode.OK)
                                                    {
                                                        var authResponseModel = System.Text.Json.JsonSerializer.Deserialize<dynamic>(await authResponse.Content.ReadAsStringAsync());
                                                        var token = $"{authResponseModel.token_type} {authResponseModel.access_token}";
                                                        using (var postInfoRequest = new HttpRequestMessage(HttpMethod.Post, $"data/spot/api/v1/en/partners/products/{partnerId}/facets"))
                                                        {
                                                            postInfoRequest.Headers.Add("Authorization", token);

                                                            var omniclassesGroup = new List<FacetItem>();
                                                            foreach (var omniclass in product.ProductOmniclasses.Select(a => a.Omniclass).Distinct())
                                                            {
                                                                var searchPatern = omniclass.Code;
                                                                if (searchPatern.EndsWith(" 00 00 00"))
                                                                {
                                                                    searchPatern = searchPatern.Substring(0, searchPatern.Length - 9);
                                                                }
                                                                else if (searchPatern.EndsWith(" 00 00"))
                                                                {
                                                                    searchPatern = searchPatern.Substring(0, searchPatern.Length - 6);
                                                                }
                                                                else if (searchPatern.EndsWith(" 00"))
                                                                {
                                                                    searchPatern = searchPatern.Substring(0, searchPatern.Length - 3);
                                                                }
                                                                var childOmniclass = unitOfWork.OmniclassRepository.GetAll()
                                                                    .Where(o => o.Code != omniclass.Code && o.Code.StartsWith(searchPatern)).OrderBy(o => o.Code).ToList().LastOrDefault();

                                                                var facet = new FacetItem { name = $"{omniclass.Code} - {omniclass.Title}" };
                                                                if (childOmniclass != null)
                                                                {
                                                                    facet.children = new FacetItem[1];
                                                                    facet.children[0] = new FacetItem { name = $"{childOmniclass.Code} - {childOmniclass.Title}" };
                                                                }
                                                                omniclassesGroup.Add(facet);
                                                            }

                                                            var postInfoModel = new PostFacet
                                                            {
                                                                source = SourceName,
                                                                facet = new Facet
                                                                {
                                                                    group = OmniclassGroupName,
                                                                    items = omniclassesGroup.ToArray()
                                                                }
                                                            };

                                                            var postInfoBodyContent = JsonConvert.SerializeObject(postInfoModel);
                                                            postInfoRequest.Content = new StringContent(postInfoBodyContent, Encoding.UTF8, "application/json");

                                                            var postInfoResponse = await httpClient.SendAsync(postInfoRequest);
                                                            if (postInfoResponse.StatusCode != HttpStatusCode.OK)
                                                            {
                                                                var omniclassResponse = System.Text.Json.JsonSerializer.Deserialize<dynamic>(await postInfoResponse.Content.ReadAsStringAsync());
                                                                Log.Error($"[{_jobName}] " + omniclassResponse);
                                                            }
                                                        }

                                                        using (var postInfoRequest = new HttpRequestMessage(HttpMethod.Post, $"data/spot/api/v1/en/partners/products/{partnerId}/facets"))
                                                        {
                                                            var allMasterformats = await _masterformatService.GetBackofficeMasterformatsAsync();
                                                            postInfoRequest.Headers.Add("Authorization", token);

                                                            var masterformatsGroup = new List<FacetItem>();
                                                            foreach (var masterformatId in product.ProductMasterformats.Select(a => a.ExternalMasterformatId).Distinct())
                                                            {
                                                                var masterformat = allMasterformats.FirstOrDefault(x => x.Id == masterformatId);
                                                                var searchPatern = masterformat?.Code;
                                                                if (searchPatern.EndsWith(" 00 00 00"))
                                                                {
                                                                    searchPatern = searchPatern.Substring(0, searchPatern.Length - 9);
                                                                }
                                                                else if (searchPatern.EndsWith(" 00 00"))
                                                                {
                                                                    searchPatern = searchPatern.Substring(0, searchPatern.Length - 6);
                                                                }
                                                                else if (searchPatern.EndsWith(" 00"))
                                                                {
                                                                    searchPatern = searchPatern.Substring(0, searchPatern.Length - 3);
                                                                }
                                                                var childMasterformat = allMasterformats
                                                                    .Where(o => o.Code != masterformat.Code && o.Code.StartsWith(searchPatern)).OrderBy(o => o.Code).LastOrDefault();

                                                                var facet = new FacetItem { name = $"{masterformat.Code} - {masterformat.Title}" };
                                                                if (childMasterformat != null)
                                                                {
                                                                    facet.children = new FacetItem[1];
                                                                    facet.children[0] = new FacetItem { name = $"{childMasterformat.Code} - {childMasterformat.Title}" };
                                                                }
                                                                masterformatsGroup.Add(facet);
                                                            }

                                                            var postInfoModel = new PostFacet
                                                            {
                                                                source = SourceName,
                                                                facet = new Facet
                                                                {
                                                                    group = MasterSpecGroupName,
                                                                    items = masterformatsGroup.ToArray()
                                                                }
                                                            };

                                                            var postInfoBodyContent = JsonConvert.SerializeObject(postInfoModel);
                                                            postInfoRequest.Content = new StringContent(postInfoBodyContent, Encoding.UTF8, "application/json");

                                                            var postInfoResponse = await httpClient.SendAsync(postInfoRequest);
                                                            if (postInfoResponse.StatusCode != HttpStatusCode.OK)
                                                            {
                                                                var masterSpecResponse = System.Text.Json.JsonSerializer.Deserialize<dynamic>(await postInfoResponse.Content.ReadAsStringAsync());
                                                                Log.Error($"[{_jobName}] " + masterSpecResponse);
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            Debug.WriteLine(ex.Message);
                                            Log.Error($"[{_jobName}] " + "Error for product Id: {fileId}", ex);
                                        }
                                        finally
                                        {

                                            product.ULSyncStatus = ULSyncStatus.Free;
                                            unitOfWork.ProductRepository.Edit(product);
                                            unitOfWork.Save();
                                            unitOfWork.CommitTransaction();
                                            ulProductsQueue.DeleteMessage(message.MessageId, message.PopReceipt);
                                            Log.Information($"[{_jobName}] " + $"Passed for product id: {productId} at " + DateTime.UtcNow.ToString());
                                            Debug.WriteLine($"Passed for product id: {productId} at " + DateTime.UtcNow.ToString());
                                        }
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine(ex.Message);
                                Log.Error($"[{_jobName}] " + "Error for product Id: {fileId}", ex);
                                ulProductsQueue.DeleteMessage(message.MessageId, message.PopReceipt);
                                unitOfWork.RollbackTransaction();
                                continue;
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Debug.WriteLine(e.Message);
                Log.Error($"[{_jobName}] " + e.Message, e);
            }
        }

        private static List<dynamic> ConvertToList(dynamic objects)
        {
            var result = new List<dynamic>();
            if (objects != null)
            {
                foreach (var objectItem in objects)
                {
                    result.Add(objectItem);
                }
            }
            return result;
        }

        private static bool IsDevEnvironment()
        {
            var result = true;
            var connectionString = ConfigurationHelper.GetValue("ConnectionStrings:MarketDBConnection");
            result = !connectionString.Contains("Initial Catalog=bimsmith-market-db-VM;");

            return result;
        }

        private class PostFacet
        {
            public string source { get; set; }
            public Facet facet { get; set; }
        }

        private class Facet
        {
            public string group { get; set; }
            public FacetItem[] items { get; set; }
        }

        private class FacetItem
        {
            public string name { get; set; }
            public FacetItem[] children { get; set; }
        }

        private static string Base64Encode(string plainText)
        {
            var plainTextBytes = System.Text.Encoding.UTF8.GetBytes(plainText);
            return System.Convert.ToBase64String(plainTextBytes);
        }

        private static string Base64Decode(string base64EncodedData)
        {
            var base64EncodedBytes = System.Convert.FromBase64String(base64EncodedData);
            return System.Text.Encoding.UTF8.GetString(base64EncodedBytes);
        }
    }
}