﻿using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Dto.SalesRepresentative;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    public class SalesRepresentativeController : BaseApiController
    {
        private readonly ISalesRepresentativeService _salesRepresentativeService;

        public SalesRepresentativeController(ISalesRepresentativeService salesRepresentativeService)
        {
            _salesRepresentativeService = salesRepresentativeService;
        }

        /// <summary>
        /// Adds new model
        /// </summary>
        /// <param name="model">The model</param>
        /// <returns></returns>
        [HttpPost]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Add(AddSalesRepresentativeDto model)
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _salesRepresentativeService.AddAsync(model, userId, unitOfWork));
        }

        /// <summary>
        /// Updates the model
        /// </summary>
        /// <param name="model">The model</param>
        /// <returns></returns>
        [HttpPost]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Edit(EditSalesRepresentativeDto model)
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _salesRepresentativeService.EditAsync(model, userId, unitOfWork));
        }

        /// <summary>
        /// Gets model for admin panel
        /// </summary>
        /// <param name="id">The model identifier</param>
        /// <returns></returns>
        [HttpGet]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> AdminGet(int id)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _salesRepresentativeService.AdminGetAsync(id, unitOfWork));
        }

        /// <summary>
        /// Gets list for admin panel
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier</param>
        /// <param name="query">The search query</param>
        /// <param name="offset">The offset to skip</param>
        /// <param name="count">The count to take</param>
        /// <returns></returns>
        [HttpGet]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> AdminList(
            int manufacturerId,
            string query = null,
            int offset = 0,
            int count = 10
            )
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _salesRepresentativeService.AdminListAsync(unitOfWork, manufacturerId, query, offset, count));
        }

        /// <summary>
        /// Deletes the model
        /// </summary>
        /// <param name="id">The identifier</param>
        /// <returns></returns>
        [HttpDelete]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Delete(int id)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _salesRepresentativeService.DeleteAsync(id, unitOfWork));
        }

        /// <summary>
        /// Get list for public
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> PublicList(int manufacturerId)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _salesRepresentativeService.PublicListAsync(manufacturerId, unitOfWork));
        }
    }
}