﻿using BIMsmithMarket.Domain.DBModels;
using DocumentFormat.OpenXml.Spreadsheet;
using System;

namespace BIMsmithMarket.Domain.Models.ExcelModels.DynamicExcel
{
    public class DynamicExcelMappingModel
    {
        public string FieldName { get; set; }

        public string Caption { get; set; }

        public CellValues DataType { get; set; }

        public uint StyleIndex { get; set; }

        public Action<DynamicExcelHandleImportModel, Product, string, string> HandleFieldImportFunction { get; set; }
    }
}