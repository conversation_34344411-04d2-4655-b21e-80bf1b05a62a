﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Dto.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.IdentityModel.Tokens;
using Microsoft.Net.Http.Headers;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Web;

namespace BIMsmithMarket.Core.Helpers
{
    public static class AuthHelper
    {
        public static List<string> GetUserClaimsFromToken(JwtSecurityToken securityToken, string claimType)
        {
            return securityToken.Claims.Where(claim => claim.Type == claimType).Select(x => x.Value).ToList();
        }

        public static string GetToken(HttpRequest httpRequest)
        {
            var token = httpRequest.Cookies[AuthorizationConstants.CookieName];

            if (string.IsNullOrWhiteSpace(token))
                token = httpRequest.Headers[HeaderNames.Authorization].ToString()?.Replace("Bearer ", string.Empty, StringComparison.OrdinalIgnoreCase);

            if (string.IsNullOrWhiteSpace(token))
                token = httpRequest.Query[AuthorizationConstants.QueryParameterName];

            if (!string.IsNullOrWhiteSpace(token) && token == "null")
                token = null;

            if (!string.IsNullOrWhiteSpace(token))
                token = HttpUtility.UrlDecode(token);

            return token;
        }

        public static string GetUserInfo(HttpRequest httpRequest, string claimType)
        {
            string userId = null;

            try
            {
                userId = GetUserClaimsFromToken(ParseSecurityToken(GetToken(httpRequest)), claimType)?.FirstOrDefault();
            }
            catch { }

            return userId;
        }

        public static string GenerateJwtToken(ClaimsIdentity identity, int liveTimeInSeconds)
        {
            string domain = ConfigurationHelper.GetValue("Domain");
            string secret = ConfigurationHelper.GetValue("Secret");
            DateTime now = DateTime.UtcNow;
            JwtSecurityToken jwt = new JwtSecurityToken(
                    issuer: domain,
                    audience: domain,
                    notBefore: now,
                    claims: identity.Claims,
                    expires: now.Add(TimeSpan.FromSeconds(liveTimeInSeconds)),
                    signingCredentials: new SigningCredentials(AuthOptions.GetSymmetricSecurityKey(secret), SecurityAlgorithms.HmacSha256));
            string encodedJwt = new JwtSecurityTokenHandler().WriteToken(jwt);

            return encodedJwt;
        }

        public static JwtSecurityToken ParseSecurityToken(string token)
        {
            if (string.IsNullOrWhiteSpace(token))
                throw new InvalidInputException("Token cannot be null");

            JwtSecurityTokenHandler tokenHandler = new();
            TokenValidationParameters validationParameters = GetTokenValidationParameters();
            SecurityToken validatedToken;
            tokenHandler.ValidateToken(token, validationParameters, out validatedToken);
            JwtSecurityToken securityToken = validatedToken as JwtSecurityToken;
            return securityToken;
        }

        #region private methods
        private static TokenValidationParameters GetTokenValidationParameters()
        {
            return new TokenValidationParameters()
            {
                LifetimeValidator = (notBefore, expires, securityToken, validationParameters) =>
                {
                    return securityToken.ValidTo > DateTime.UtcNow;
                },
                ValidIssuer = ConfigurationHelper.GetValue("Domain"),
                ValidAudience = ConfigurationHelper.GetValue("Domain"),
                IssuerSigningKey = AuthOptions.GetSymmetricSecurityKey(ConfigurationHelper.GetValue("Secret"))
            };
        }
        #endregion
    }
}