﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BIMsmithMarket.DataLayer.Migrations
{
    /// <inheritdoc />
    public partial class Adjust_RevitProcessing_Tables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ReportInternal",
                table: "RevitJobs");

            migrationBuilder.AlterColumn<int>(
                name: "ProjectDataTypeId",
                table: "RevitJobs",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "ProjectDataTypeId",
                table: "RevitJobs",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddColumn<string>(
                name: "ReportInternal",
                table: "RevitJobs",
                type: "nvarchar(max)",
                nullable: true);
        }
    }
}
