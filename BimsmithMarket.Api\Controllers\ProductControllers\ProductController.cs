using Azure.Storage.Blobs.Specialized;
using Azure.Storage.Sas;
using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Api.Providers;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.DBModels.RevitProcessing;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.SwatchboxDto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Domain.Models.ExcelModels.DynamicExcel;
using BIMsmithMarket.Domain.Models.ExcelModels.StaticExcel;
using BIMsmithMarket.Services.Interfaces;
using BIMsmithMarket.Services.Providers;
using BIMsmithMarket.Services.Search;
using BIMsmithMarket.Services.Search.FullTextSearch;
using Flurl;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Threading.Tasks;

//TODO: Rework static excel to not use dynamic objects
[assembly: InternalsVisibleTo("BIMsmithMarket.Services")]
namespace BIMsmithMarket.Api.Controllers.ProductControllers
{
    /// <summary>
    /// Product Controller
    /// </summary>
    [Route("api/[controller]/[action]/{id?}")]
    public class ProductController : BaseApiController
    {
        /// <summary>
        /// The revit files share directory name
        /// </summary>
        private const string RevitFilesShareDirectoryName = "marketrevitfiles";

        /// <summary>
        /// The interns revit files share directory name
        /// </summary>
        private const string InternsRevitFilesShareDirectoryName = "internsrevitfiles";

        private readonly IMongoRepository<ProductMongoDto> _productMongoRepository;
        private readonly IFileService _fileService;
        private readonly IMasterformatService _masterformatService;
        private readonly IProductService _productService;
        private readonly IFeatureSettingService _featureSettingService;
        private readonly IManufacturerBackupService _manufacturerBackupService;
        private readonly IHealthDashboardService _healthDashboardService;
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly SlackWebHook _slackWebHook;

        public ProductController(
            IMasterformatService masterformatService,
            IFileService fileService,
            IProductService productService,
            IFeatureSettingService featureSettingService,
            IManufacturerBackupService manufacturerBackupService,
            IHealthDashboardService healthDashboardService,
            IMongoRepository<ProductMongoDto> productMongoRepository,
            IWebHostEnvironment webHostEnvironment,
            SlackWebHook slackWebHook)
        {
            _masterformatService = masterformatService;
            _fileService = fileService;
            _productService = productService;
            _featureSettingService = featureSettingService;
            _manufacturerBackupService = manufacturerBackupService;
            _healthDashboardService = healthDashboardService;
            _productMongoRepository = productMongoRepository;
            _webHostEnvironment = webHostEnvironment;
            _slackWebHook = slackWebHook;
        }

        /// <summary>
        /// Return true if manufacturer type is Generic
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("IsManufacturerTypeGeneric")]
        public async Task<IActionResult> IsManufacturerTypeGeneric(int productId)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _productService.IsManufacturerTypeGenericAsync(productId, unitOfWork));
            }
        }

        /// <summary>
        /// Return true if manufacturer type is Generic in batch
        /// </summary>
        /// <param name="model">The model</param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("BatchIsManufacturerTypeGeneric")]
        public async Task<IActionResult> IsManufacturerTypeGeneric(IsManufacturerTypeGenericRequestDto model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _productService.BatchIsManufacturerTypeGenericAsync(model, unitOfWork));
            }
        }

        /// <summary>
        /// Get available product project types
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("/api/Product/GetProjectTypes")]
        public async Task<IActionResult> GetProjectTypes()
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var items = await unitOfWork.ProjectDataTypeRepository.GetAll().Select(a => new
                {
                    typeId = a.Id,
                    title = a.Title,
                    icon = a.Icon.OriginalImgUrl
                })
                .AsNoTracking()
                .ToListAsync();

                return Ok(items);
            }
        }

        /// <summary>
        /// Get list of all products from database + filters
        /// </summary>
        /// <param name="manufacturerId">Optional: Filter by manufacturer</param>
        /// <param name="categoryId">Optional: Filter by category</param>
        /// <param name="productLineId">Optional: Filter by product line</param>
        /// <param name="projectTypeIds">Optional: Filter by project content types Exp: 1_3_5</param>
        /// <param name="externalCertificateIds">The external certificate ids.</param>
        /// <param name="q">Optional: Search query</param>
        /// <param name="offset">Offset for pagination</param>
        /// <param name="count">Count of data in result</param>
        /// <param name="featured">Optional: True if need show only Featured products</param>
        /// <param name="published">If true - return only pablished items</param>
        /// <param name="staging">Return products that are Published and Staging</param>
        /// <param name="includeHideOnMicrosite">if set to <c>true</c> [include hide on microsite].</param>
        /// <param name="regionId">The region identifier.</param>
        /// <param name="stateId">The state identifier.</param>
        /// <param name="sortType">SortType: Relevant = 0, ContentRating= 1, ProductRating = 2, LatestProducts = 3, LatestModified = 4, Id = 5, AlphabeticallyDistinct = 6 ,Alphabetically = 7</param>
        /// <param name="langCode">The language code for translation</param>
        /// <param name="includeHealthCheckStatus">If true method returns check health status</param>
        /// <param name="searchManufacturerChildProducts">If true search perfoms across child products</param>
        /// <param name="includeLastModifier">If true add info about last modifier</param>
        /// <returns>
        /// List of products with total count
        /// </returns>
        [HttpGet]
        [AllowAnonymous]
        [ActionName("List")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> List(
            int manufacturerId = -1,
            int categoryId = -1,
            int productLineId = -1,
            string projectTypeIds = null,
            string externalCertificateIds = null,
            string q = null,
            int offset = 0,
            int count = 10,
            bool featured = false,
            bool published = true,
            bool staging = false,
            bool includeHideOnMicrosite = true,
            string regionId = null,
            string stateId = null,
            ProductSortType sortType = ProductSortType.Relevant,
            string langCode = null,
            bool? includeHealthCheckStatus = false,
            bool? searchManufacturerChildProducts = false,
            bool? includeLastModifier = false)
        {
            Stopwatch requestTime = new Stopwatch();
            requestTime.Start();

            if (regionId == "null")
                regionId = null;

            if (stateId == "null")
                stateId = null;

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var productSearchOptions = new ProductSearchOptions
                {
                    Query = q,
                    Featured = featured,
                    Published = published,
                    Staging = staging,
                    CategoryId = categoryId,
                    ProductLineId = productLineId,
                    ManufacturerIds = manufacturerId != -1 ? new List<int> { manufacturerId } : new(),
                    ExternalCertificateIds = !string.IsNullOrEmpty(externalCertificateIds) ? externalCertificateIds.Split('_').Select(a => int.Parse(a)).ToList() : null,
                    ProductFileTypeIds = !string.IsNullOrEmpty(projectTypeIds) ? projectTypeIds.Split('_').Select(a => int.Parse(a)).ToList() : null,
                    RegionId = regionId,
                    StateId = stateId,
                    SortType = sortType,
                    Take = count,
                    Skip = offset,
                    IncludeHideOnMicrosite = includeHideOnMicrosite,
                    SearchManufacturerChildProducts = searchManufacturerChildProducts,
                    LangCode = langCode,
                    SearchSource = SearchSource.Market
                };

                var searchCache = SearchCache.Get(unitOfWork);
                var results = ProductSearch.Search(productSearchOptions, unitOfWork.ProductRepository.GetAll(), null, searchCache, unitOfWork);

#if (DEBUG)
                Debug.WriteLine(string.Format("ProductController::List, Step 1 at {0}ms", requestTime.ElapsedMilliseconds));
#endif
                var productData = await results.AllResults(unitOfWork).AsSplitQuery().AsNoTracking().Select(a => new
                {
                    group = "g",
                    product = a,
                    projectDataTypes = a.ProductFiles.Where(f => !f.IsAttachment).Select(f => new
                    {
                        id = f.ProjectDataTypeId,
                        title = f.ProjectDataType.Title,
                        header = f.ProjectDataType.Header,
                    }),
                    externalCertificates = a.ProductCertificates.Where(b => b.ExternalCertificateId != null).Select(b => b.ExternalCertificateId.Value),
                    cisfbs = a.ProductCisfbs.Select(c => new
                    {
                        id = c.CisfbId,
                        code = c.Cisfb.Code,
                        title = c.Cisfb.Title
                    }),
                    externalMasterformatIds = a.ProductMasterformats.Select(x => x.ExternalMasterformatId),
                    omniclasses = a.ProductOmniclasses.Select(c => new
                    {
                        id = c.OmniclassId,
                        code = c.Omniclass.Code,
                        title = c.Omniclass.Title
                    }),
                    uniclasses = a.ProductUniclasses.Select(c => new
                    {
                        id = c.UniclassId,
                        code = c.Uniclass.Code,
                        title = c.Uniclass.Title
                    }),
                    uniformats = a.ProductUniformats.Select(c => new
                    {
                        id = c.UniformatId,
                        code = c.Uniformat.Code,
                        title = c.Uniformat.Title
                    })
                }).GroupBy(a => a.group)
                   .Select(a => new
                   {
                       countOfProduct = a.Count(),
                       selectionProjectTypes = a.Select(b => b.projectDataTypes).SelectMany(b => b).Distinct(),
                       selectionExternalCertificates = a.Select(b => b.externalCertificates).SelectMany(b => b).Distinct(),
                       selectionCisfbs = a.Select(b => b.cisfbs).SelectMany(b => b).Distinct(),
                       selectionExternalMasterformatIds = a.Select(b => b.externalMasterformatIds).SelectMany(b => b).Distinct(),
                       selectionOmniclasses = a.Select(b => b.omniclasses).SelectMany(b => b).Distinct(),
                       selectionUniclasses = a.Select(b => b.uniclasses).SelectMany(b => b).Distinct(),
                       selectionUniformats = a.Select(b => b.uniformats).SelectMany(b => b).Distinct(),
                       selectionMaterialLibrary = a.Select(b => b.product.ProductQualityItems.Any(qi => qi.QualityItem.Id == 16))
                   })
                   .FirstOrDefaultAsync();

#if (DEBUG)
                Debug.WriteLine(string.Format("ProductController::List, Step 2 at {0}ms", requestTime.ElapsedMilliseconds));
#endif
                var products = results.PrimaryResults(offset, count, unitOfWork, a => new ProductSearchResult<ProductListProjectionDto>
                {
                    ProductId = a.Id,
                    Projection = new ProductListProjectionDto
                    {
                        id = a.Id,
                        name = a.Name,
                        externalProductId = a.ExternalId,
                        description = a.Description.Length > 150 ? a.Description.Substring(0, 150) + "..." : a.Description,
                        weight = a.Weight,
                        productRating = a.ProductRating,
                        contentRating = a.ContentRating,
                        ulSyncStatus = a.ULSyncStatus,
                        ulURL = a.ULUrl,
                        isFeatured = a.IsFeatured,
                        published = a.Published,
                        publishToPartner = a.PublishToPartner,
                        staging = a.Staging,
                        regionIds = a.RegionIds,
                        stateId = a.StateIds,
                        productLine = a.ProductLineId == null ? null : new
                        {
                            id = a.ProductLineId,
                            name = a.ProductLine.Name
                        },
                        category = new
                        {
                            id = a.CategoryId,
                            name = a.Category.Name,
                            vanityUrl = a.Category.VanityUrl
                        },
                        categories = a.ProductCategories.Select(c => new
                        {
                            id = c.CategoryId,
                            name = c.Category.Name,
                            vanityUrl = c.Category.VanityUrl
                        }),
                        externalCertificates = a.ProductCertificates.Where(b => b.ExternalCertificateId != null).Select(b => b.ExternalCertificateId.Value),
                        qualityItems = a.ProductQualityItems.Select(r => new
                        {
                            id = r.QualityItemId,
                            name = r.QualityItem.Name,
                            iconUrl = r.QualityItem.IconUrl
                        }),
                        photoUrl = a.PhotoId != null ? a.Photo.MiddleImgUrl : a.ProductPhotos.FirstOrDefault().Photo.MiddleImgUrl,
                        manufacture = a.Manufacturer.Name,
                        manufacturerId = a.Manufacturer.Id,
                        manufacturerVanityURL = a.Manufacturer.HubVanityURL,
                        manufactureLogo = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.MiddleImgUrl : null,
                        updateDate = a.ModifiedDate ?? a.CreatedDate,
                        displaySwatchboxProductOnProductPage = a.DisplaySwatchboxProductOnProductPage,
                        displaySwatchboxProductOnMicrosite = a.DisplaySwatchboxProductOnMicrosite,
                        externalMasterformatIds = a.ProductMasterformats.Select(x => x.ExternalMasterformatId),
                        healthCheckStatus = HealthCheckStatus.Ok
                    }
                }, null).Select(r => r.Projection);

#if (DEBUG)
                Debug.WriteLine(string.Format("ProductController::List, Step 3 at {0}ms", requestTime.ElapsedMilliseconds));
#endif
                requestTime.Stop();

                if (string.IsNullOrEmpty(q) == false && offset == 0)
                {
                    try
                    {
                        var userName = AuthHelper.GetUserInfo(Request, ClaimTypes.Email);
                        var backgroundTask = Task.Run(async () =>
                        {
                            using (IUnitOfWork localUnitOfWork = UnitOfWork.Create())
                            {
                                await CommonController.LogSearchAsync(productSearchOptions, productData?.countOfProduct ?? 0, 0, userName, HttpContext?.Connection?.RemoteIpAddress.ToString(), localUnitOfWork, results.SearchTime, requestTime.ElapsedMilliseconds);
                            }
                            await _slackWebHook.SendSearchMessage(userName, q, null, null, null, null, null, null, null, null, null, null);
                        });
                    }
                    catch (Exception e)
                    {
                        Log.Error(e.Message, e);
                        Trace.TraceError(e.Message);
                    };
                }

                if (includeHealthCheckStatus.HasValue && includeHealthCheckStatus.Value)
                {
                    foreach (var product in products)
                    {
                        product.healthCheckStatus = await _healthDashboardService.CheckHealthForProductAsync(product.id, unitOfWork);
                    }
                }

                if (includeLastModifier.HasValue && includeLastModifier.Value)
                {
                    int[] productIds = products.Select(x => x.id).ToArray();
                    if (productIds.Any())
                    {
                        Dictionary<int, string> lastModifiers = await _productService.GetLastMofifierAsync(productIds, unitOfWork);

                        foreach (var product in products)
                        {
                            product.userName = lastModifiers[product.id];
                        }
                    }
                }

                if (productData != null)
                {
                    var result = new
                    {
                        count = productData.countOfProduct,
                        data = products,
                        productData.selectionProjectTypes,
                        productData.selectionExternalCertificates,
                        productData.selectionCisfbs,
                        productData.selectionExternalMasterformatIds,
                        productData.selectionOmniclasses,
                        productData.selectionUniclasses,
                        productData.selectionUniformats,
                        requestTime = requestTime.ElapsedMilliseconds
                    };
                    return Ok(result);
                }
                else
                {
                    var result = new
                    {
                        count = 0,
                        data = products,
                        selectionProjectTypes = new List<dynamic>(),
                        selectionExternalCertificates = new List<int>(),
                        selectionCertificates = new List<dynamic>(),
                        selectionCisfbs = new List<dynamic>(),
                        selectionExternalMasterformats = new List<int>(),
                        selectionExternalMasterformatIds = new List<int>(),
                        selectionOmniclasses = new List<dynamic>(),
                        selectionUniclasses = new List<dynamic>(),
                        selectionUniformats = new List<dynamic>(),
                        requestTime = requestTime.ElapsedMilliseconds
                    };
                    return Ok(result);
                }
            }
        }

        /// <summary>
        /// Lists products for admin panel
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier</param>
        /// <param name="productLineId">The product line identifier</param>
        /// <param name="query">The search query</param>
        /// <param name="sortType">The sort type</param>
        /// <param name="offset">The offset to skip</param>
        /// <param name="count">The count to take</param>
        /// <returns></returns>
        [HttpGet]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> AdminList(
            int? manufacturerId = null,
            int? productLineId = null,
            string query = null,
            ProductSortType sortType = ProductSortType.Relevant,
            int offset = 0,
            int count = 50
            )
        {
            IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _productService.AdminListAsync(unitOfWork, manufacturerId, productLineId, query, sortType, offset, count));
        }

        /// <summary>
        /// Get list of all products from database + filters
        /// </summary>
        /// <param name="manufacturerName">Filter by manufacturer name</param>
        /// <param name="vanityUrlPart">Filter by end part of vanity url</param>
        /// <param name="categoryId">Optional: Filter by category</param>
        /// <param name="productLineId">Optional: Filter by product line</param>
        /// <param name="q">Optional: Search query</param>
        /// <param name="offset">Offset for pagination</param>
        /// <param name="count">Count of data in result</param>
        /// <param name="published">If true - return only pablished items</param>
        /// <returns></returns>
        [HttpGet]
        [Route("/api/Product/Microsite/List")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> MicrositeList(
            string manufacturerName = null,
            string vanityUrlPart = null,
            string q = null,
            int categoryId = -1,
            int productLineId = -1,
            int offset = 0,
            int count = 10,
            bool published = false)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var productLink = new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment("/product/").ToString();

                IQueryable<Product> query = null;

                if (manufacturerName != null)
                {
                    query = unitOfWork.ProductRepository.GetAll()
                        .Where(a => a.Manufacturer.Name.ToLower() == manufacturerName.ToLower());
                }
                else if (vanityUrlPart != null)
                {
                    query = unitOfWork.ProductRepository.GetAll()
                        .Where(a => a.Manufacturer.HubVanityURL.ToLower() == vanityUrlPart.ToLower());
                }
                else
                {
                    throw new ArgumentNullException();
                }

                //Exclude hide for microsites Row327
                query = query.Where(x => !x.HideOnMicrosite);

                if (published)
                {
                    query = query.Where(a => a.Published == true && a.Manufacturer.Published == true);
                }

                if (!string.IsNullOrEmpty(q))
                {
                    q = q.Trim().ToLower();
                    var keyword = " " + q + ", ";

                    //if (q.Length > 3)
                    {
                        query = query.Where(e =>
                                   e.Manufacturer.Name.ToLower().Contains(q) ||
                                   e.ProductLine.Name.ToLower().Contains(q) ||
                                   e.Name.ToLower().Contains(q) ||
                                   e.Keywords.ToLower().Contains(keyword) ||
                                   e.Category.Name.ToLower().Contains(q));
                    }
                }

                if (categoryId != -1)
                {
                    query = query.Where(e =>
                            e.CategoryId == categoryId || e.Category.ParentCategoryId == categoryId || e.Category.ParentCategoryId != null && e.Category.ParentCategory.ParentCategoryId == categoryId //Filter by main category
                             ||
                            e.ProductCategories.Any(c => c.CategoryId == categoryId || c.Category.ParentCategoryId == categoryId || c.Category.ParentCategoryId != null && c.Category.ParentCategory.ParentCategoryId == categoryId)); //Filter by additional categories
                }

                if (productLineId != -1)
                {
                    query = query.Where(e =>
                            e.ProductLineId == productLineId);
                }

                var countOfData = await query.CountAsync();
                var products = await query
                            .OrderBy(a => a.Id)
                            .Skip(offset)
                            .Take(count)
                            .Select(a => new
                            {
                                id = a.Id,
                                name = a.Name,
                                ulSyncStatus = a.ULSyncStatus,
                                ulURL = a.ULUrl,
                                productSiteUrl = a.ProductUrl ?? string.Concat(productLink, a.Id),
                                photoUrl = a.PhotoId != null ? a.Photo.MiddleImgUrl : a.ProductPhotos.FirstOrDefault().Photo.MiddleImgUrl,
                                productLine = a.ProductLineId == null ? null : new
                                {
                                    id = a.ProductLineId,
                                    name = a.ProductLine.Name
                                },
                                category = new
                                {
                                    id = a.CategoryId,
                                    name = a.Category.Name,
                                    parent = a.Category.ParentCategoryId == null ? null : new
                                    {
                                        id = a.Category.ParentCategoryId,
                                        name = a.Category.ParentCategory.Name,
                                        parent = a.Category.ParentCategory.ParentCategoryId == null ? null : new
                                        {
                                            id = a.Category.ParentCategory.ParentCategoryId,
                                            name = a.Category.ParentCategory.ParentCategory.Name,
                                        }
                                    }
                                },
                                projectFiles = a.ProductFiles.Where(f => !f.IsAttachment).Select(r => new
                                {
                                    id = r.FileId,
                                    customFileId = r.CustomFileId,
                                    projectDataType = new
                                    {
                                        id = r.ProjectDataTypeId,
                                        title = r.ProjectDataType.Title,
                                        header = r.ProjectDataType.Header
                                    },
                                    title = r.File.Title,
                                    fileName = r.File.FileName,
                                    fileSize = r.File.FileSize,
                                    mimeType = r.File.MediaType,
                                    url = r.File.Url,
                                    preview = r.File.PreviewUrl,
                                }),
                                displaySwatchboxProductOnProductPage = a.DisplaySwatchboxProductOnProductPage,
                                displaySwatchboxProductOnMicrosite = a.DisplaySwatchboxProductOnMicrosite,
                                externalMasterformatIds = a.ProductMasterformats.Select(x => x.ExternalMasterformatId)
                            })
                            .AsNoTracking()
                            .ToListAsync();


                if (offset == 0 && (string.IsNullOrEmpty(q) == false || categoryId != -1))
                {
                    try
                    {
                        var userName = AuthHelper.GetUserInfo(Request, ClaimTypes.Email); AuthHelper.GetUserInfo(Request, ClaimTypes.Email);
                        var backgroundTask = Task.Run(async () =>
                        {
                            using (IUnitOfWork localUnitOfWork = UnitOfWork.Create())
                            {
                                var categoryName = localUnitOfWork.CategoryRepository.GetAll().FirstOrDefault(a => a.Id == categoryId)?.Name;

                                await _slackWebHook.SendSearchMessage(userName,
                                    q,
                                    manufacturerName ?? vanityUrlPart,
                                    categoryName,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null);
                            }
                        });
                    }
                    catch (Exception e)
                    {
                        Log.Error(e.Message, e);
                        Trace.TraceError(e.Message);
                    }
                }

                var result = new
                {
                    count = countOfData,
                    data = products
                };
                return Ok(result);
            }
        }


        /// <summary>
        /// Get list of all products and starters from database + filters
        /// </summary>
        /// <param name="manufacturerName">Name of the manufacturer.</param>
        /// <param name="vanityUrlPart">The vanity URL part.</param>
        /// <param name="q">The q.</param>
        /// <param name="projectTypeIds">The project type ids.</param>
        /// <param name="categoryId">The category identifier.</param>
        /// <param name="productLineId">The product line identifier.</param>
        /// <param name="offset">The offset.</param>
        /// <param name="count">The count.</param>
        /// <param name="published">if set to <c>true</c> [published].</param>
        /// <param name="withStarter">if set to <c>true</c> [with starter].</param>
        /// <param name="startersPosition">The starters position.</param>
        /// <param name="sortType">Type of the sort.</param>
        /// <param name="includeHideOnMicrosite">if set to <c>true</c> [include hide on microsite].</param>
        /// <param name="regionId">The product region ids.</param>
        /// <param name="stateId">The state identifier</param>
        /// <returns></returns>
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        [HttpGet]
        [Route("/api/ProductAndStarter/Microsite/List")]
        public async Task<IActionResult> MicrositeListWithStarter(
                        string manufacturerName = null,
                        string vanityUrlPart = null,
                        string q = null,
                        string projectTypeIds = null,
                        int categoryId = -1,
                        int productLineId = -1,
                        int offset = 0,
                        int count = 10,
                        bool published = false,
                        bool withStarter = true,
                        StartersPosition startersPosition = StartersPosition.EndWithStarters,
                        ProductSortType sortType = ProductSortType.Relevant,
                        bool includeHideOnMicrosite = false,
                        string regionId = null,
                        string stateId = null)
        {
            if (regionId == "null")
                regionId = null;

            if (stateId == "null")
                stateId = null;

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var productLink = new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment("/product/").ToString();

                var manufacturer = ManufacturerSearch.Search(new ManufacturerSearchOptions
                {
                    ManufacturerName = manufacturerName,
                    HubVanityURL = vanityUrlPart,
                    Published = false,
                    Query = null,
                    RegionId = regionId,
                    StateId = stateId,
                }, unitOfWork.ManufacturerRepository.GetAll(), unitOfWork).Select(a => new { a.Id, a.ForgeManufacturerId }).FirstOrDefault();

                if (manufacturer == null)
                {
                    return Ok(new
                    {
                        count = 0,
                        data = new List<string>(),
                        availableProjectDataTypes = new List<string>()
                    });
                }

                List<dynamic> resultData = new List<dynamic>();

                int countOfProducts = 0;
                int countOfStarters = 0;
                int skip = Math.Max(offset, 0);
                var productSearchOptions = new ProductSearchOptions
                {
                    Query = q,
                    Published = published,
                    CategoryId = categoryId,
                    ProductLineId = productLineId,
                    ManufacturerIds = new List<int> { manufacturer.Id },
                    SortType = startersPosition == StartersPosition.Weighted ? ProductSortType.Relevant : sortType,
                    ProductFileTypeIds = !string.IsNullOrEmpty(projectTypeIds) ? projectTypeIds.Split('_').Select(a => int.Parse(a)).ToList() : null,
                    Skip = skip,
                    Take = count,
                    StartersPosition = startersPosition,
                    IncludeHideOnMicrosite = includeHideOnMicrosite,
                    RegionId = regionId,
                    StateId = stateId,
                    SearchSource = SearchSource.Microsites
                };

                var searchCache = SearchCache.Get(unitOfWork);
                ProductSearchResults results = ProductSearch.Search(productSearchOptions, unitOfWork.ProductRepository.GetAll(), withStarter ? unitOfWork.StarterRepository.GetAll() : null, searchCache, unitOfWork);

                categoryId = productSearchOptions.CategoryId; // could have been recognized from text query

                var primaryResults = results.PrimaryResults(skip, count, unitOfWork, a => new ProductSearchResult<object>
                {
                    ProductId = a.Id,
                    ItemType = "product",
                    Projection = new
                    {
                        id = a.Id,
                        name = a.Name,
                        productSiteUrl = a.ProductUrl ?? string.Concat(productLink, a.Id),
                        photoUrl = a.PhotoId != null ? a.Photo.OriginalImgUrl : a.ProductPhotos.FirstOrDefault().Photo.OriginalImgUrl,
                        productLineId = a.ProductLineId,
                        categoryId = a.CategoryId,
                        swatchboxProductId = a.SwatchboxProductId,
                        displaySwatchboxProductOnProductPage = a.DisplaySwatchboxProductOnProductPage,
                        displaySwatchboxProductOnMicrosite = a.DisplaySwatchboxProductOnMicrosite,
                        projectFileIds = a.ProductFiles.Where(f => !f.IsAttachment && !f.IsULPartnership).Select(r => r.FileId),
                        productFileIds = a.ProductFiles.Where(f => f.IsAttachment && !f.IsULPartnership).Select(r => r.FileId),
                        externalCertificationsIds = a.ProductCertificates.Where(b => b.ExternalCertificateId != null).Select(b => b.ExternalCertificateId.Value),
                        externalMasterformatIds = a.ProductMasterformats.Select(x => x.ExternalMasterformatId),
                        description = a.Description
                    }
                }, a => withStarter ? new ProductSearchResult<object>
                {
                    ProductId = 0,
                    ItemType = "starter",
                    Projection = new
                    {
                        id = a.Id,
                        name = a.Name,
                        manufacturerName = a.ManufacturerName,
                        brand = a.Brand,
                        orientation = a.Orientation,
                        preview = a.Preview,
                        stc = a.STC,
                        fstc = a.FSTC,
                        rvalue = a.RValue,
                        fire = a.Fire,
                        ul = a.UL,
                        iic = a.IIC,
                        fiic = a.FIIC,
                        nfpa285 = a.NFPA285
                    }
                } : null
                ).Select(r => new
                {
                    itemType = r.ItemType,
                    item = r.Projection
                });

                resultData.AddRange(primaryResults.ToList());
                countOfProducts = results.CountOfProducts;
                countOfStarters = results.CountOfStarters;

                if (offset == 0 && (!string.IsNullOrEmpty(q) || categoryId != -1))
                {
                    try
                    {
                        var userName = AuthHelper.GetUserInfo(Request, ClaimTypes.Email);
                        var backgroundTask = Task.Run(async () =>
                        {
                            using (IUnitOfWork localUnitOfWork = UnitOfWork.Create())
                            {
                                var categoryName = (await localUnitOfWork.CategoryRepository.GetAll().FirstOrDefaultAsync(a => a.Id == categoryId))?.Name;

                                await _slackWebHook.SendSearchMessage(userName,
                                    q,
                                    manufacturerName ?? vanityUrlPart,
                                    categoryName,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null,
                                    null);
                            }
                        });
                    }
                    catch (Exception e)
                    {
                        Log.Error(e.Message, e);
                        Trace.TraceError(e.Message);
                    }
                }

                var availableProjectDataTypes = await results.AllResults(unitOfWork).SelectMany(a => a.ProductFiles)
                                                                  .Where(a => !a.IsAttachment && a.ProjectDataTypeId != null && a.ProjectDataType.Title != null)
                                                                  .Select(a => new
                                                                  {
                                                                      id = a.ProjectDataTypeId,
                                                                      title = a.ProjectDataType.Title
                                                                  }).Distinct().AsNoTracking().ToListAsync();

                var result = new
                {
                    count = countOfProducts + countOfStarters,
                    data = resultData,
                    availableProjectDataTypes
                };
                return Ok(result);
            }
        }

        /// <summary>
        /// Get list of all products and starters for microsites from database
        /// </summary>
        /// <param name="model">The filter model.</param>        
        /// <returns></returns>
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        [HttpPost]
        [Route("/api/ProductAndStarter/Microsite/List")]
        public async Task<IActionResult> MicrositeListWithStarter(MicrositeListWithStarterFiltersDto model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _productService.MicrositeListWithStarterAsync(
                    model,
                    AuthHelper.GetUserInfo(Request, ClaimTypes.Email),
                    unitOfWork));
            }
        }

        /// <summary>
        /// Get filters of all products and starters for microsites from database
        /// </summary>
        /// <param name="searchId">The search identifier.</param>        
        /// <returns></returns>
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        [HttpGet]
        [Route("/api/ProductAndStarter/Microsite/GetFilters")]
        public async Task<IActionResult> MicrositeListWithStarterGetFilters(string searchId)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _productService.MicrositeListWithStarterGetFiltersAsync(
                    searchId,
                    unitOfWork));
            }
        }

        /// <summary>
        /// Get random featured products
        /// </summary>
        /// <param name="manufacturerId"></param>
        /// <param name="count">Count of result</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("Featured")]
        public async Task<IActionResult> Featured(int manufacturerId = -1, int count = 5)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                IQueryable<Product> query = unitOfWork.ProductRepository.GetAll()
                    .Where(a => a.Published && a.Manufacturer.Published && a.IsFeatured);

                int countOfProduct = 0;

                if (manufacturerId != -1)
                {
                    query = query.Where(e => e.ManufacturerId == manufacturerId);
                }

                countOfProduct = await query.CountAsync();

                var featuredProducts = await query.Select(x => new { x.Id, x.ManufacturerId }).ToListAsync();
                if (manufacturerId == -1)
                    featuredProducts = featuredProducts.GroupBy(x => x.ManufacturerId).Select(x => x.FirstOrDefault()).ToList();

                var featuredProductIds = featuredProducts.Select(a => a.Id).ToList();

                var rndProductIds = featuredProductIds.OrderBy(a => Guid.NewGuid()).Take(count);

                var data = await query
                            .Where(a => rndProductIds.Contains(a.Id))
                            .Select(a => new
                            {
                                id = a.Id,
                                name = a.Name,
                                ulSyncStatus = a.ULSyncStatus,
                                ulURL = a.ULUrl,
                                description = a.Description.Length > 150 ? a.Description.Substring(0, 150) + "..." : a.Description,
                                productRating = a.ProductRating,
                                productRatingCount = a.ProductRatings.Where(b => b.Type == ProductRatingType.ProductRating).Count(),
                                contentRating = a.ContentRating,
                                contentRatingCount = a.ProductRatings.Where(b => b.Type == ProductRatingType.ContentRating).Count(),
                                isFeatured = a.IsFeatured,
                                productLine = a.ProductLineId == null ? null : new
                                {
                                    id = a.ProductLineId,
                                    name = a.ProductLine.Name
                                },
                                category = new
                                {
                                    id = a.CategoryId,
                                    name = a.Category.Name,
                                },
                                externalCertificates = a.ProductCertificates.Where(b => b.ExternalCertificateId != null).Select(b => b.ExternalCertificateId.Value),
                                qualityItems = a.ProductQualityItems.Select(r => new
                                {
                                    id = r.QualityItemId,
                                    name = r.QualityItem.Name,
                                    iconUrl = r.QualityItem.IconUrl
                                }).ToList(),
                                photoUrl = a.PhotoId != null ? a.Photo.MiddleImgUrl : a.ProductPhotos.FirstOrDefault().Photo.MiddleImgUrl,
                                manufacture = a.Manufacturer.Name,
                                manufactureLogo = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.MiddleImgUrl : null,
                                updateDate = a.ModifiedDate ?? a.CreatedDate,
                                displaySwatchboxProductOnProductPage = a.DisplaySwatchboxProductOnProductPage,
                                displaySwatchboxProductOnMicrosite = a.DisplaySwatchboxProductOnMicrosite
                            })
                            .AsNoTracking()
                            .ToListAsync();

                var result = new
                {
                    count = countOfProduct,
                    data
                };

                return Ok(result);
            }
        }


        /// <summary>
        /// Set product staging field
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <param name="staging">if set to <c>true</c> [staging].</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("SetProductStagingAsync")]
        public async Task<IActionResult> SetProductStagingAsync([FromQuery] int id, bool staging)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                await unitOfWork.ProductRepository.GetAll()
                    .Where(x => x.Id == id)
                    .ExecuteUpdateAsync(x => x.SetProperty(p => p.Staging, staging));

                await _productService.SaveProductToMongoAsync(id, true, unitOfWork);

                CacheHelper.ClearSpecificCache("*/api/Product/List*");
                CacheHelper.ClearSpecificCache("*/api/Product/AdminList*");
            }
            return Ok();
        }

        /// <summary>
        /// Gets the json.
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier.</param>
        /// <param name="productId">The product identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        [ActionName("GetProductInfo")]
        public async Task<IActionResult> GetProductInfo(int manufacturerId, int? productId = null)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var masterformats = new List<MasterformatModel>();
                var hostUrl = ConfigurationHelper.GetValue("MarketApiBaseUrl");
                var downloadMethodUrl = "api/File/Download";
                var fileUrlPart = "?fileId=";
                var productUrlPart = "&productId=";
                var manufacturerUrlPart = "&manufacturerId=";

                var projectDataTypes = await unitOfWork.ProjectDataTypeRepository.GetAll()
                    .Select(x => new
                    {
                        x.Id,
                        x.Title
                    })
                    .ToListAsync();
                var revitId = projectDataTypes.First(a => a.Title.ToLower() == "revit").Id;
                var autocadId = projectDataTypes.First(a => a.Title.ToLower() == "autocad").Id;
                var iesId = projectDataTypes.First(a => a.Title.ToLower() == "ies").Id;

                var query = unitOfWork.ProductRepository.GetAll();
                if (productId != null)
                {
                    query = query.Where(a => a.Id == productId.Value);
                }
                else
                {
                    query = query.Where(a => a.ManufacturerId == manufacturerId);
                }

                var items = await query.AsSplitQuery().Select(a => new GetAddExternalProduct
                {
                    ProductId = a.Id,
                    Published = a.Published,
                    Staging = a.Staging,
                    ExternalProductId = a.ExternalId,
                    ProductLine = a.ProductLine.Name,
                    Name = a.Name,
                    Description = a.Description,
                    VideoUrl = a.VideoUrl,
                    ProductUrl = a.ProductUrl,
                    ULUrl = a.ULUrl,
                    CategoryNames = a.ProductCategories.Select(b => new ExternalNewProductName
                    {
                        Name = b.Category.Name
                    }).ToList(),
                    CisfbIds = a.ProductCisfbs.Select(b => new ExternalNewProductCode
                    {
                        Code = b.Cisfb.Code
                    }).ToList(),
                    ExternalMasterformatIds = a.ProductMasterformats.Select(x => x.ExternalMasterformatId).ToList(),
                    OmniclassIds = a.ProductOmniclasses.Select(b => new ExternalNewProductCode
                    {
                        Code = b.Omniclass.Code
                    }).ToList(),
                    UniclassIds = a.ProductUniclasses.Select(b => new ExternalNewProductCode
                    {
                        Code = b.Uniclass.Code
                    }).ToList(),
                    UniformatIds = a.ProductUniformats.Select(b => new ExternalNewProductCode
                    {
                        Code = b.Uniformat.Code
                    }).ToList(),
                    Photos = a.ProductPhotos.Select(b => new ExternalNewProductPhoto
                    {
                        PhotoURL = b.Photo.OriginalImgUrl
                    }).ToList(),
                    Documents = a.ProductFiles.Where(b => b.IsAttachment && b.File.Url != null && b.File.Url != "").Select(b => new ExternalNewProductDocuments
                    {
                        Name = b.File.FileName,
                        Type = b.File.Title,
                        CustomId = b.CustomFileId,
                        Url = hostUrl + downloadMethodUrl + fileUrlPart + b.File.Id + productUrlPart + a.Id + manufacturerUrlPart + a.ManufacturerId
                    }).ToList(),
                    ProductStats = a.ProductStats.Select(b => new ExternalNewProductStat
                    {
                        Name = b.KeyStat.Name,
                        KeyStatType = b.KeyStatType.ToString(),
                        KeyStatUnit = b.KeyStatUnitId == null ? null : new ExternalNewProductStatUnit
                        {
                            BUnitName = b.KeyStatUnit.BUnitName,
                            GroupName = b.KeyStatUnit.GroupName
                        },
                        Note = b.Note,
                        Value = b.Value,
                        MinRangeValue = b.MinRangeValue,
                        MaxRangeValue = b.MaxRangeValue,
                        MultipleValues = b.KeyStatValueList.Select(c => new ExternalNewProductStatMultiple
                        {
                            KeyStatUnit = c.KeyStatUnitId == null ? null : new ExternalNewProductStatUnit
                            {
                                BUnitName = b.KeyStatUnit.BUnitName,
                                GroupName = b.KeyStatUnit.GroupName
                            },
                            Value = c.Value == null ? c.MinRangeValue : c.Value,
                            Note = c.Note,
                            MaxRangeValue = c.MaxRangeValue
                        }).ToList()
                    }).ToList(),
                    RevitFiles = a.ProductFiles.Where(b => !b.IsAttachment && b.File.Url != null && b.File.Url != "" && b.ProjectDataTypeId == revitId).Select(b => new ExternalNewProductDocuments
                    {
                        Name = b.File.FileName,
                        Type = revitId.ToString(),
                        CustomId = b.CustomFileId,
                        Url = hostUrl + downloadMethodUrl + fileUrlPart + b.File.Id + productUrlPart + a.Id + manufacturerUrlPart + a.ManufacturerId
                    }).ToList(),
                    AutocadFiles = a.ProductFiles.Where(b => !b.IsAttachment && b.File.Url != null && b.File.Url != "" && b.ProjectDataTypeId == autocadId).Select(b => new ExternalNewProductDocuments
                    {
                        Name = b.File.FileName,
                        Type = autocadId.ToString(),
                        CustomId = b.CustomFileId,
                        Url = hostUrl + downloadMethodUrl + fileUrlPart + b.File.Id + productUrlPart + a.Id + manufacturerUrlPart + a.ManufacturerId
                    }).ToList(),
                    IESFiles = a.ProductFiles.Where(b => !b.IsAttachment && b.File.Url != null && b.File.Url != "" && b.ProjectDataTypeId == iesId).Select(b => new ExternalNewProductDocuments
                    {
                        Name = b.File.FileName,
                        Type = autocadId.ToString(),
                        CustomId = b.CustomFileId,
                        Url = hostUrl + downloadMethodUrl + fileUrlPart + b.File.Id + productUrlPart + a.Id + manufacturerUrlPart + a.ManufacturerId
                    }).ToList()
                })
                .AsNoTracking()
                .ToListAsync();

                var externalMasterfomatIds = items.SelectMany(x => x.ExternalMasterformatIds).Distinct().ToList();
                masterformats = await _masterformatService.GetBackofficeMasterformatsAsync(externalMasterfomatIds);

                var productIds = items.Select(a => a.ProductId).ToList();
                var mainCategories = await unitOfWork.ProductRepository.GetAll().Where(a => productIds.Contains(a.Id)).Select(a => new { productId = a.Id, mainCategory = a.Category.Name }).Distinct().AsNoTracking().ToListAsync();

                foreach (var item in items)
                {
                    if (item.CategoryNames == null)
                    {
                        item.CategoryNames = new List<ExternalNewProductName>();
                    }
                    var mainCategory = mainCategories.FirstOrDefault(a => a.productId == item.ProductId)?.mainCategory;
                    if (!string.IsNullOrWhiteSpace(mainCategory))
                    {
                        item.CategoryNames.Insert(0, new ExternalNewProductName { Name = mainCategory });
                    }
                    if (masterformats.Any())
                    {
                        item.MasterformatIds = masterformats.Where(x => item.ExternalMasterformatIds.Contains(x.Id))
                            .Select(x => new ExternalNewProductCode
                            {
                                Code = x.Code
                            }).ToList();
                    }
                    else
                    {
                        item.MasterformatIds = new List<ExternalNewProductCode>();
                    }
                }

                return Ok(items);
            }
        }

        /// <summary>
        /// Get detailed information about Product by Id
        /// </summary>
        /// <param name="id">Id of product</param>
        /// <returns></returns>
        [HttpGet]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [ActionName("Get")]
        public async Task<IActionResult> Get(int id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _productService.GetProductForAdminPanelAsync(id, unitOfWork));
            }
        }

        /// <summary>
        /// Get detailed information about Product by Id from public
        /// </summary>
        /// <param name="id">Id of product</param>
        /// <param name="vanityUrl">Or vanityUrl</param>
        /// <param name="langCode">The language code for translation</param>
        /// <param name="regionId">The region identifier</param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        [ActionName("PublicGet")]
        public async Task<IActionResult> PublicGet(int? id = null, string vanityUrl = null, string langCode = null, string regionId = null)
        {
            if (regionId == "null")
                regionId = null;

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                if (!string.IsNullOrWhiteSpace(regionId))
                {
                    var productInfo = await unitOfWork.ProductRepository.GetAll()
                        .Where(x => x.Id == id)
                        .Select(x => new
                        {
                            ProductRegionIds = x.RegionIds ?? string.Empty,
                            ProductLineRegionIds = x.ProductLine.RegionIds ?? string.Empty,
                            ManufacturerRegionIds = x.Manufacturer.RegionIds ?? string.Empty
                        })
                        .FirstOrDefaultAsync();

                    if (productInfo == null)
                        return NotFound("Not found the product");

                    bool isInRegion = productInfo.ProductRegionIds.Split("_").Contains(regionId)
                        || (string.IsNullOrWhiteSpace(productInfo.ProductRegionIds) && string.IsNullOrWhiteSpace(productInfo.ProductLineRegionIds) && string.IsNullOrWhiteSpace(productInfo.ManufacturerRegionIds))
                        || (string.IsNullOrWhiteSpace(productInfo.ProductRegionIds) && (productInfo.ProductLineRegionIds.Split("_").Contains(regionId) || productInfo.ManufacturerRegionIds.Split("_").Contains(regionId)));

                    if (!isInRegion)
                        return NotFound("Not found the product");
                }

                if (!id.HasValue && !string.IsNullOrWhiteSpace(vanityUrl))
                {
                    id = await unitOfWork.ProductRepository.GetAll().Where(x => x.VanityURL.ToLower() == vanityUrl.ToLower()).Select(x => x.Id).FirstOrDefaultAsync();
                }

                string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);

                bool isInMyBIMSmith = false;

                if (userId != null)
                {
                    isInMyBIMSmith = await unitOfWork.UserBIMsmithProductRepository.GetAll().AnyAsync(a => a.ProductId == id && a.AddedById == userId);
                }

                ProductMongoDto product = null;

                if (id.HasValue)
                {
                    product = await _productMongoRepository.GetByIdAsync(id.Value);
                }

                if (product == null)
                    return NotFound("Not found the product");

                product.IsInMyBIMSmith = isInMyBIMSmith;
                return Ok(product);
            }
        }

        [HttpGet]
        [ActionName("GetRevitFileIds")]
        public async Task<IActionResult> GetRevitFileIds([FromQuery] int[] ids)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var revitId = (await unitOfWork.ProjectDataTypeRepository.GetAll().AsNoTracking().FirstAsync(a => a.Title == "Revit")).Id;
                var query = await unitOfWork.ProductRepository.GetAll().Where(a => ids.Contains(a.Id))
                    .Select(a => new
                    {
                        id = a.Id,
                        revitFileIds = a.ProductFiles.Where(b => b.ProjectDataTypeId == revitId && b.File.Title.EndsWith("rfa")).Select(b => b.FileId).Distinct()
                    })
                    .AsNoTracking()
                    .ToListAsync();

                return Ok(query);
            }
        }

        /// <summary>
        /// Get key stats list
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("GetKeyStats")]
        public async Task<IActionResult> GetKeyStats(int id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var query = unitOfWork.ProductStatsRepository.GetAll().Where(a => a.ProductId == id);

                var productKeyStatData = await query.OrderBy(r => r.Order).Select(r => new
                {
                    keyStatId = r.KeyStatId,
                    name = r.KeyStat.Name,
                    note = r.Note,
                    keyStatType = r.KeyStatType,
                    order = r.Order,
                    keyStatUnitId = r.KeyStatUnitId,
                    keyStatUnit = r.KeyStatUnit == null ? null : new
                    {
                        aUnitName = r.KeyStatUnit.AUnitName,
                        bUnitName = r.KeyStatUnit.BUnitName,
                        unitMetricType = r.KeyStatUnit.UnitMetricType
                    },
                    convertKeyStatUnitId = r.ConvertKeyStatUnitId,
                    convertKeyStatUnit = r.ConvertKeyStatUnit == null ? null : new
                    {
                        aUnitName = r.ConvertKeyStatUnit.AUnitName,
                        bUnitName = r.ConvertKeyStatUnit.BUnitName,
                        unitMetricType = r.ConvertKeyStatUnit.UnitMetricType
                    },
                    singleValue = r.Value,
                    minRangeValue = r.MinRangeValue,
                    maxRangeValue = r.MaxRangeValue,
                    convertSingleValue = r.ConvertValue,
                    convertMinRangeValue = r.ConvertMinRangeValue,
                    convertMaxRangeValue = r.ConvertMaxRangeValue,
                    multipleValues = r.KeyStatValueList.Select(k => new
                    {
                        keyStatUnitId = k.KeyStatUnitId,
                        keyStatUnit = k.KeyStatUnit == null ? null : new
                        {
                            aUnitName = k.KeyStatUnit.AUnitName,
                            bUnitName = k.KeyStatUnit.BUnitName,
                            unitMetricType = k.KeyStatUnit.UnitMetricType
                        },
                        note = k.Note,
                        convertKeyStatUnitId = k.ConvertKeyStatUnitId,
                        convertKeyStatUnit = k.ConvertKeyStatUnit == null ? null : new
                        {
                            aUnitName = k.ConvertKeyStatUnit.AUnitName,
                            bUnitName = k.ConvertKeyStatUnit.BUnitName,
                            unitMetricType = k.ConvertKeyStatUnit.UnitMetricType
                        },
                        value = !(k.Value == null || k.Value.Trim() == string.Empty) ? k.Value : k.MinRangeValue,
                        maxValue = k.MaxRangeValue,
                        convertValue = k.ConvertValue ?? k.ConvertMinRangeValue,
                        convertMaxValue = k.ConvertMaxRangeValue
                    })
                })
                .AsNoTracking()
                .ToListAsync();

                List<int> keyStatUnitIds = new List<int>();
                foreach (var productKeyStat in productKeyStatData)
                {
                    if (productKeyStat.keyStatUnitId.HasValue)
                    {
                        keyStatUnitIds.Add(productKeyStat.keyStatUnitId.Value);
                    }
                    if (productKeyStat.convertKeyStatUnitId.HasValue)
                    {
                        keyStatUnitIds.Add(productKeyStat.convertKeyStatUnitId.Value);
                    }

                    foreach (var item in productKeyStat.multipleValues)
                    {
                        if (item.keyStatUnitId.HasValue)
                        {
                            keyStatUnitIds.Add(item.keyStatUnitId.Value);
                        }
                        if (item.convertKeyStatUnitId.HasValue)
                        {
                            keyStatUnitIds.Add(item.convertKeyStatUnitId.Value);
                        }
                    }
                }
                keyStatUnitIds = keyStatUnitIds.Distinct().ToList();

                var relations = await unitOfWork.KeyStatUnitRelationRepository.GetAll().Where(a => keyStatUnitIds.Contains(a.FromUnitId)).AsNoTracking().ToListAsync();

                //Create new Json
                List<dynamic> jsonData = new List<dynamic>();

                foreach (var productKeyStat in productKeyStatData)
                {
                    string convertSingleValueCalculated = null;
                    string convertMinRangeValueCalculated = null;
                    string convertMaxRangeValueCalculated = null;
                    bool convertedSuccessfully = false;

                    List<dynamic> multipleValues = new List<dynamic>();

                    foreach (var multipleValue in productKeyStat.multipleValues)
                    {
                        convertSingleValueCalculated = null;
                        convertMaxRangeValueCalculated = null;

                        convertedSuccessfully = false;
                        if (multipleValue.keyStatUnitId != null && multipleValue.convertKeyStatUnitId != null) //Need to conver value
                        {
                            var relation = relations.FirstOrDefault(a => a.FromUnitId == multipleValue.keyStatUnitId && a.ToUnitId == multipleValue.convertKeyStatUnitId);
                            if (relation != null)
                            {
                                convertSingleValueCalculated = multipleValue.value != null ? KeyStatUnitController.TryToConvertValue(multipleValue.value, relation) : null;
                                convertMaxRangeValueCalculated = multipleValue.maxValue != null ? KeyStatUnitController.TryToConvertValue(multipleValue.maxValue, relation) : null;
                                convertedSuccessfully = true;
                            }
                        }

                        var multipleValueItem = new
                        {
                            multipleValue.keyStatUnitId,
                            multipleValue.keyStatUnit,
                            convertKeyStatUnitId = convertedSuccessfully ? multipleValue.convertKeyStatUnitId : null,
                            convertKeyStatUnit = convertedSuccessfully ? multipleValue.convertKeyStatUnit : null,
                            multipleValue.value,
                            multipleValue.maxValue,
                            multipleValue.convertValue,
                            multipleValue.convertMaxValue,
                            multipleValue.note,
                            convertValueCalculated = convertSingleValueCalculated,
                            convertMaxValueCalculated = convertMaxRangeValueCalculated,
                        };

                        multipleValues.Add(multipleValueItem);
                    }

                    convertedSuccessfully = false;
                    if (productKeyStat.keyStatUnitId != null && productKeyStat.convertKeyStatUnitId != null) //Need to conver value
                    {
                        var relation = relations.FirstOrDefault(a => a.FromUnitId == productKeyStat.keyStatUnitId && a.ToUnitId == productKeyStat.convertKeyStatUnitId);
                        if (relation != null)
                        {
                            convertSingleValueCalculated = productKeyStat.singleValue != null ? KeyStatUnitController.TryToConvertValue(productKeyStat.singleValue, relation) : null;
                            convertMinRangeValueCalculated = productKeyStat.minRangeValue != null ? KeyStatUnitController.TryToConvertValue(productKeyStat.minRangeValue, relation) : null;
                            convertMaxRangeValueCalculated = productKeyStat.maxRangeValue != null ? KeyStatUnitController.TryToConvertValue(productKeyStat.maxRangeValue, relation) : null;

                            convertedSuccessfully = true;
                        }
                    }

                    var item = new
                    {
                        productKeyStat.keyStatId,
                        productKeyStat.name,
                        productKeyStat.keyStatType,
                        productKeyStat.keyStatUnitId,
                        productKeyStat.keyStatUnit,
                        convertKeyStatUnitId = convertedSuccessfully ? productKeyStat.convertKeyStatUnitId : null,
                        convertKeyStatUnit = convertedSuccessfully ? productKeyStat.convertKeyStatUnit : null,
                        productKeyStat.singleValue,
                        productKeyStat.minRangeValue,
                        productKeyStat.maxRangeValue,
                        productKeyStat.convertSingleValue,
                        productKeyStat.convertMinRangeValue,
                        productKeyStat.convertMaxRangeValue,
                        productKeyStat.note,
                        convertSingleValueCalculated,
                        convertMinRangeValueCalculated,
                        convertMaxRangeValueCalculated,

                        multipleValues
                    };

                    jsonData.Add(item);
                }

                return Ok(jsonData);
            }
        }

        /// <summary>
        /// Get key stats list for many
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("GetKeyStatsForMany")]
        public async Task<IActionResult> GetKeyStatsForMany([FromBody] int[] ids)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var query = unitOfWork.ProductStatsRepository.GetAll().Where(a => ids.Contains(a.ProductId));

                var productKeyStatData = await query.OrderBy(r => r.Order)
                    .GroupBy(r => r.ProductId)
                    .Select(g => new ProductKeyStatForManyDto
                    {
                        ProductId = g.Key,
                        KeyStats = g.Select(r => new KeyStatForManyDto
                        {
                            KeyStatId = r.KeyStatId,
                            Name = r.KeyStat.Name,
                            SingleValue = r.Value,
                            KeyStatType = r.KeyStatType,
                            MultipleValues = r.KeyStatValueList.Select(k => new KeyStatForManyMultipleValueDto
                            {
                                Value = !(k.Value == null || k.Value.Trim() == string.Empty) ? k.Value : k.MinRangeValue,
                            }).ToList()
                        }).ToList()
                    })
                    .ToArrayAsync();

                return Ok(productKeyStatData);
            }
        }

        /// <summary>
        /// Get list of prodcuts with detailed information
        /// </summary>
        /// <param name="q">The q.</param>
        /// <param name="manufacturerId">The manufacturer identifier.</param>
        /// <param name="categoryId">The category identifier.</param>
        /// <param name="offset">The offset.</param>
        /// <param name="count">The count.</param>
        /// <param name="IncludeHideOnMicrosite">if set to <c>true</c> [include hide on microsite].</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("DetailedList")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public IActionResult DetailedList(
            string q = null,
            int manufacturerId = -1,
            int categoryId = -1,
            int offset = 0,
            int count = 10,
            bool IncludeHideOnMicrosite = true)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var result = new
                {
                    count = 0,
                    data = Array.Empty<dynamic>(),
                };

                return Ok(result);
            }
        }

        /// <summary>
        /// Get related product for product
        /// </summary>
        /// <param name="id"></param>
        /// <param name="regionId"></param>
        /// <param name="count"></param>
        /// <param name="langCode">The language code</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("GetRelatedProducts")]
        public async Task<IActionResult> GetRelatedProducts(int id, string regionId = null, int count = 5, string langCode = null)
        {
            if (regionId == "null")
                regionId = null;

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var product = await unitOfWork.ProductRepository.GetAll()
                    .Where(a => a.Id == id)
                    .Select(a => new
                    {
                        id = a.Id,
                        productLineId = a.ProductLineId,
                        categoryId = a.CategoryId,
                        manufacturerId = a.ManufacturerId,
                        relatedProducts = a.RelatedProducts.Where(r => r.RelatedProductItem.Published && r.RelatedProductItem.Manufacturer.Published).Select(p => p.RelatedProductItem).OrderBy(p => p.ModifiedDate).AsEnumerable(),
                    })
                    .AsNoTracking()
                    .FirstOrDefaultAsync();

                if (product == null) return NotFound("Product not found");
                //Get saved related products
                var relatedProducts = product.relatedProducts.ToList();
                var relatedProductsIds = product.relatedProducts.Select(a => a.Id);

                //Get related products from product line
                var lineRelatedProducts = unitOfWork.ProductRepository.GetAll()
                     .Where(a => a.Published &&
                                 a.Manufacturer.Published &&
                                 a.ProductLineId == product.productLineId &&
                                 a.ManufacturerId == product.manufacturerId &&
                                 a.IsFeatured &&
                                 (regionId == null || string.IsNullOrEmpty(a.RegionIds) || a.RegionIds.Contains(regionId)) &&
                                 !relatedProductsIds.Contains(a.Id))
                     .OrderBy(a => a.ModifiedDate)
                     .ToList();

                var lineRelatedProductsIds = lineRelatedProducts.Select(a => a.Id).Concat(relatedProductsIds).ToList();

                //Get related products from category
                var categoryRelatedProducts = unitOfWork.ProductRepository.GetAll()
                        .Where(a => a.Published &&
                                 a.Manufacturer.Published &&
                                 a.CategoryId == product.categoryId &&
                                 a.ManufacturerId == product.manufacturerId &&
                                 a.IsFeatured &&
                                 (regionId == null || string.IsNullOrEmpty(a.RegionIds) || a.RegionIds.Contains(regionId)) &&
                                 !lineRelatedProductsIds.Contains(a.Id))
                        .OrderBy(a => a.ModifiedDate)
                        .ToList();

                var categoryRelatedProductsIds = categoryRelatedProducts.Select(a => a.Id).Concat(lineRelatedProductsIds).ToList();

                //Get related products from manufacturer
                var manufacturerRelatedProducts = unitOfWork.ProductRepository.GetAll()
                        .Where(a => a.Published &&
                                 a.Manufacturer.Published &&
                                 a.ManufacturerId == product.manufacturerId &&
                                 a.IsFeatured &&
                                 (regionId == null || string.IsNullOrEmpty(a.RegionIds) || a.RegionIds.Contains(regionId)) &&
                                 !categoryRelatedProductsIds.Contains(a.Id))
                        .OrderBy(a => a.ModifiedDate)
                        .ToList();

                var products = relatedProducts.Concat(lineRelatedProducts.Concat(categoryRelatedProducts.Concat(manufacturerRelatedProducts)))
                    .Where(a => a.Id != id && !relatedProductsIds.Contains(a.Id)) //exclude preset
                    .Select((a, i) => new
                    {
                        index = i,
                        product = a,
                    })
                    .OrderBy(a => a.index)
                    .Take(count)
                    .Select((a) => new
                    {
                        productId = a.product.Id,
                        productLineId = a.product.ProductLineId,
                        name = a.product.Name,
                        categoryName = a.product.Category.Name,
                        manufacturerName = a.product.Manufacturer.Name,
                        photoUrl = a.product.PhotoId != null ? a.product.Photo.MiddleImgUrl : a.product.ProductPhotos.FirstOrDefault().Photo.MiddleImgUrl,
                    })
                    .ToList();

                var diff = count - products.Count;
                if (diff > 0)
                {
                    var productIds = products.Select(a => a.productId).ToList();
                    products.AddRange(
                        await unitOfWork.ProductRepository.GetAll()
                        .Where(a => a.Published &&
                                    a.Manufacturer.Published &&
                                    a.ManufacturerId == product.manufacturerId &&
                                    a.Id != id &&
                                    (regionId == null || string.IsNullOrEmpty(a.RegionIds) || a.RegionIds.Contains(regionId)) &&
                                    !relatedProductsIds.Contains(a.Id) &&
                                    !productIds.Contains(a.Id))
                        .OrderBy(a => Guid.NewGuid())
                        .Select((a) => new
                        {
                            productId = a.Id,
                            productLineId = a.ProductLineId,
                            name = a.Name,
                            categoryName = a.Category.Name,
                            manufacturerName = a.Manufacturer.Name,
                            photoUrl = a.PhotoId != null ? a.Photo.MiddleImgUrl : a.ProductPhotos.FirstOrDefault().Photo.MiddleImgUrl,
                        })
                        .Take(diff)
                        .AsNoTracking()
                        .ToListAsync());
                }

                return Ok(products);
            }
        }

        /// <summary>
        /// Add new Product: Role-ADMIN
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Add")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Add([FromBody] AddProductModel model)
        {
            if (!await _featureSettingService.GetSettingStatusAsync(DbConstants.FeatureAddProductViaAdminPanel))
            {
                return BadRequest("The feature is disabled");
            }

            var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            var result = await _productService.AddAsync(model, userId);

            CacheHelper.ClearSpecificCache("*/api/Product/List*");
            CacheHelper.ClearSpecificCache("*/api/Product/AdminList*");
            CacheHelper.ClearSpecificCache("*/api/Product/ListWithRevitFiles*");
            return Ok(result);
        }

        /// <summary>
        /// Clones the specified product identifier.
        /// </summary>
        /// <param name="productId">The product identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("Clone")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Clone(int productId)
        {
            if (!await _featureSettingService.GetSettingStatusAsync(DbConstants.FeatureCloneProductViaAdminPanel))
                return BadRequest("The feature is disabled");

            var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            var result = await _productService.CloneAsync(productId, userId);
            CacheHelper.ClearSpecificCache("*/api/Product/List*");
            CacheHelper.ClearSpecificCache("*/api/Product/AdminList*");
            CacheHelper.ClearSpecificCache("*/api/Product/ListWithRevitFiles*");
            return Ok(result);
        }

        /// <summary>
        /// Edit new Product: Role-ADMIN
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Edit")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Edit([FromBody] EditProductModel model)
        {
            if (!await _featureSettingService.GetSettingStatusAsync(DbConstants.FeatureUpdateProductViaAdminPanel))
                return BadRequest("The feature is disabled");

            var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            var result = await _productService.EditAsync(model, userId);
            CacheHelper.ClearSpecificCache("*/api/Product/List*");
            CacheHelper.ClearSpecificCache("*/api/Product/AdminList*");
            CacheHelper.ClearSpecificCache("*/api/Product/Get*");
            CacheHelper.ClearSpecificCache("*/api/Product/PublicGet*");
            CacheHelper.ClearSpecificCache("*/api/Product/ListWithRevitFiles*");

            return Ok(result);
        }

        /// <summary>
        /// Edits the product region ids.
        /// </summary>
        /// <param name="model">The model.</param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("EditProductRegionIds")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> EditProductRegionIds([FromBody] EditProduct model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                if (!await unitOfWork.ProductRepository.GetAll().AnyAsync(p => p.Id == model.ProductId))
                    return NotFound("Not found the Product");

                await unitOfWork.ProductRepository.GetAll()
                    .Where(x => x.Id == model.ProductId)
                    .ExecuteUpdateAsync(x => x.SetProperty(p => p.RegionIds, model.RegionIds).SetProperty(p => p.StateIds, model.StateIds));

                await _productService.SaveProductToMongoAsync(model.ProductId, true, unitOfWork);
                CacheHelper.ClearSpecificCache("*/api/Product/List*");
                CacheHelper.ClearSpecificCache("*/api/Product/AdminList*");
                return Ok();
            }
        }

        /// <summary>
        /// Delete Product: Role-ADMIN
        /// </summary>
        /// <param name="id">Product Id</param>
        /// <returns></returns>
        [HttpDelete]
        [ActionName("Delete")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Delete(int id)
        {
            if (!await _featureSettingService.GetSettingStatusAsync(DbConstants.FeatureDeleteProductViaAdminPanel))
                return BadRequest("The feature is disabled");

            using (var unitOfWork = UnitOfWork.Create())
            {
                unitOfWork.BeginTransaction();
                await _productService.DeleteProductsByIds(unitOfWork, new List<int> { id });
                unitOfWork.CommitTransaction();
                CacheHelper.ClearSpecificCache("*/api/Product/List*");
                CacheHelper.ClearSpecificCache("*/api/Product/AdminList*");
                CacheHelper.ClearSpecificCache("*/api/Product/ListWithRevitFiles*");
            }
            return Ok();
        }

        /// <summary>
        /// Add rating to product
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("PostRating")]
        [Authorize]
        public async Task<IActionResult> PostRating([FromBody] PostRatingModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                bool needToUpdate = true;
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);

                unitOfWork.BeginTransaction();

                ProductRating productRating = await unitOfWork.ProductRatingRepository.GetAll()
                                                              .FirstOrDefaultAsync(a => a.PostedById == userId && a.ProductId == model.ProductId
                                                                                     && a.Type == model.Type);

                if (productRating == null)
                {
                    productRating = new ProductRating();
                    productRating.ProductId = model.ProductId;
                    needToUpdate = false;
                }

                productRating.Rating = model.Rating;
                productRating.Type = model.Type;
                productRating.Comment = model.Comment;
                productRating.PostedById = userId;
                productRating.PostedDate = DateTime.UtcNow;

                if (needToUpdate)
                {
                    unitOfWork.ProductRatingRepository.Edit(productRating);
                }
                else
                {
                    unitOfWork.ProductRatingRepository.Insert(productRating);
                }

                await unitOfWork.SaveAsync();

                var query = unitOfWork.ProductRatingRepository.GetAll()
                    .Where(a => a.ProductId == model.ProductId && a.Type == model.Type);

                int countOfRatings = await query.CountAsync();
                int sumOfRatings = await query.SumAsync(a => a.Rating);
                float newRatingValue = (float)sumOfRatings / countOfRatings;

                var product = await unitOfWork.ProductRepository.GetByIdAsync(model.ProductId);

                switch (model.Type)
                {
                    case ProductRatingType.ProductRating:
                        product.ProductRating = newRatingValue;
                        break;
                    case ProductRatingType.ContentRating:
                        product.ContentRating = newRatingValue;
                        break;
                    default:
                        throw new NotImplementedException();
                }

                await unitOfWork.SaveAsync();
                unitOfWork.CommitTransaction();

                await _productService.SaveProductToMongoAsync(product.Id, true, unitOfWork);

                var result = new
                {
                    productId = model.ProductId,
                    productRating = product.ProductRating,
                    contentRating = product.ContentRating,
                };

                return Ok(result);
            }
        }

        /// <summary>
        /// Sends content comment
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("SendContentComment")]
        [Authorize]
        public async Task<IActionResult> SendContentComment([FromBody] ContentCommentModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
                var user = await unitOfWork.UserRepository.GetAll()
                    .Where(x => x.Id == userId)
                    .Select(x => new
                    {
                        x.FirstName,
                        x.LastName,
                        x.Email
                    })
                    .FirstOrDefaultAsync();
                var product = await unitOfWork.ProductRepository.GetAll()
                    .Where(x => x.Id == model.ProductId)
                    .Select(x => new
                    {
                        x.Name,
                        x.ManufacturerId,
                        ManufacturerName = x.Manufacturer.Name
                    })
                    .FirstOrDefaultAsync();
                var hostUrl = Request.Host.ToString();
                var productLink = hostUrl + "/product/" + model.ProductId;
                var manufacturerLink = hostUrl + "/manufacturer/" + product?.ManufacturerId;

                var emailsPath = Path.Combine(_webHostEnvironment.WebRootPath, "Emails");
                await EmailNotificationHelper.Create(emailsPath).SendProductContentCommentEmail(
                    model.Comment,
                    model.Rating,
                    product?.Name,
                    productLink,
                    product?.ManufacturerName,
                    manufacturerLink,
                    user?.FirstName,
                    user?.LastName,
                    user?.Email);
            }

            return Ok();
        }

        /// <summary>
        /// Change Featured flag for Product: Role-ADMIN
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("ChangeFeatured")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> ChangeFeatured([FromBody] FeaturedProductModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                await unitOfWork.ProductRepository.GetAll()
                    .Where(x => x.Id == model.Id)
                    .ExecuteUpdateAsync(x => x.SetProperty(p => p.IsFeatured, model.IsFeatured));

                await _productService.SaveProductToMongoAsync(model.Id, true, unitOfWork);

                CacheHelper.ClearSpecificCache("*/api/Product/List*");
                CacheHelper.ClearSpecificCache("*/api/Product/AdminList*");
                CacheHelper.ClearSpecificCache("*/api/Product/Get*");
                CacheHelper.ClearSpecificCache("*/api/Product/PublicGet*");

                return Ok();
            }
        }

        /// <summary>
        /// Change Published flag for Product: Role-ADMIN
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("ChangePublished")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> ChangePublished([FromBody] PublishedProductModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                await unitOfWork.ProductRepository.GetAll()
                    .Where(x => x.Id == model.Id)
                    .ExecuteUpdateAsync(x => x.SetProperty(p => p.Published, model.IsPublished));

                await _productService.SaveProductToMongoAsync(model.Id, true, unitOfWork);

                CacheHelper.ClearSpecificCache("*/api/Product/List*");
                CacheHelper.ClearSpecificCache("*/api/Product/AdminList*");
                CacheHelper.ClearSpecificCache("*/api/Product/Get*");
                CacheHelper.ClearSpecificCache("*/api/Product/PublicGet*");

                return Ok();
            }
        }

        /// <summary>
        /// Change Published flag for Product: Role-ADMIN
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("ChangePublishedOnCustomMicrosite")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> ChangePublishedOnCustomMicrosite([FromBody] PublishedProductModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                await unitOfWork.ProductRepository.GetAll()
                    .Where(x => x.Id == model.Id)
                    .ExecuteUpdateAsync(x => x.SetProperty(p => p.PublishedOnCustomMicrosite, model.IsPublished));

                await _productService.SaveProductToMongoAsync(model.Id, true, unitOfWork);

                CacheHelper.ClearSpecificCache("*/api/Product/List*");
                CacheHelper.ClearSpecificCache("*/api/Product/AdminList*");
                CacheHelper.ClearSpecificCache("*/api/Product/Get*");
                CacheHelper.ClearSpecificCache("*/api/Product/PublicGet*");

                return Ok();
            }
        }

        /// <summary>
        /// Update weights for passed products
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("UpdateWeights")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> UpdateWeights([FromBody] ProductUpdateWeightsDto model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var productIds = model.Weights.Select(a => a.Id).ToArray();
                var products = await unitOfWork.ProductRepository.GetAll().Where(a => productIds.Contains(a.Id)).ToArrayAsync();

                foreach (var product in products)
                {
                    product.Weight = model.Weights.First(a => a.Id == product.Id).Weight;
                    unitOfWork.ProductRepository.Edit(product);
                }

                await unitOfWork.SaveAsync();

                foreach (int productId in productIds)
                    await _productService.SaveProductToMongoAsync(productId, true, unitOfWork);

                CacheHelper.ClearSpecificCache("*/api/Product/List*");
                CacheHelper.ClearSpecificCache("*/api/Product/AdminList*");

                return Ok();
            }
        }

        /// <summary>
        /// Gets the product files download links.
        /// </summary>
        /// <param name="model">The model with product identifiers</param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("GetProductFilesDownloadLinks")]
        public async Task<IActionResult> GetProductFilesDownloadLinks(EntityIdsDto model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var productFiles = await unitOfWork.ProductFileRepository.GetAll().Where(a => model.Ids.Contains(a.ProductId) && a.IsAttachment).Select(r => new
                {
                    Id = r.FileId,
                    r.File.FileName,
                    r.File.Url
                })
                .AsNoTracking()
                .ToListAsync();

                var productLineFiles = await unitOfWork.ProductLineFileRepository.GetAll().Where(a => a.ProductLine.Products.Any(p => model.Ids.Contains(p.Id)) && a.IsAttachment).Select(r => new
                {
                    Id = r.FileId,
                    r.File.FileName,
                    r.File.Url
                })
                .AsNoTracking()
                .ToListAsync();

                var manufacturerFiles = await unitOfWork.ManufacturerFileRepository.GetAll().Where(a => a.Manufacturer.Products.Any(p => model.Ids.Contains(p.Id)) && a.IsAttachment).Select(r => new
                {
                    Id = r.FileId,
                    r.File.FileName,
                    r.File.Url
                })
                .AsNoTracking()
                .ToListAsync();

                var files = new List<FileWithDownloadModel>();
                foreach (var file in productFiles)
                {
                    files.Add(new FileWithDownloadModel { Id = file.Id, DownloadUrl = GetDownloadLink(file.Url, file.FileName) });
                }

                foreach (var file in productLineFiles)
                {
                    files.Add(new FileWithDownloadModel { Id = file.Id, DownloadUrl = GetDownloadLink(file.Url, file.FileName) });
                }

                foreach (var file in manufacturerFiles)
                {
                    files.Add(new FileWithDownloadModel { Id = file.Id, DownloadUrl = GetDownloadLink(file.Url, file.FileName) });
                }
                return Ok(files.Select(a => new { fileId = a.Id, downloadUrl = a.DownloadUrl }));
            }
        }

        /// <summary>
        /// Deletes the sample.
        /// </summary>
        /// <param name="sampleId">The sample identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("DeleteSample")]
        public async Task<IActionResult> DeleteSample(int sampleId)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var productIds = await unitOfWork.ProductSampleRepository.GetAll()
                    .Where(a => a.SampleId == sampleId)
                    .Select(x => x.ProductId)
                    .ToArrayAsync();

                await unitOfWork.ProductSampleRepository.GetAll()
                    .Where(a => a.SampleId == sampleId)
                    .ExecuteDeleteAsync();

                foreach (int productId in productIds)
                    await _productService.SaveProductToMongoAsync(productId, true, unitOfWork);

                return Ok();
            }
        }

        /// <summary>
        /// Gets the product names.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("GetProductNames")]
        public async Task<IActionResult> GetProductNames()
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var productNames = await unitOfWork.ProductRepository.GetAll()
                    .Select(a => new
                    {
                        name = a.Name,
                        description = a.Description
                    })
                    .AsNoTracking()
                    .ToListAsync();

                return Ok(productNames);
            }
        }

        /// <summary>
        /// Gets the product info.
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        [ActionName("GetProductsInfo")]
        public async Task<IActionResult> GetProductsInfo(int manufacturerId = -1)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var query = unitOfWork.ProductRepository.GetAll();
                if (manufacturerId != -1)
                    query = query.Where(a => a.ManufacturerId == manufacturerId);

                var productsInfo = await query.Select(a => new
                {
                    id = a.Id,
                    name = a.Name,
                    description = a.Description,
                    productUrl = a.ProductUrl,
                    productLine = a.ProductLineId != null ? new
                    {
                        id = a.ProductLineId,
                        name = a.ProductLine.Name,
                        description = a.ProductLine.Description,
                        note = a.ProductLine.Note,
                    } : null,
                    published = a.Published,
                    staging = a.Staging,
                    createdDate = a.CreatedDate,
                    modifiedDate = a.ModifiedDate
                })
                .AsNoTracking()
                .ToListAsync();

                return Ok(productsInfo);
            }
        }

        /// <summary>
        /// Loads the partner ship data.
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier.</param>
        /// <param name="productId">The product identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("LoadPartnerShipData")]
        public async Task<IActionResult> LoadPartnerShipData(int? manufacturerId = null, int? productId = null)
        {
            if (manufacturerId == null && productId == null)
                return BadRequest("Parameters are required");

            AzureStorageService azureBlobProvider = new AzureStorageService(
                ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                ConfigurationHelper.GetValue("Environment"),
                bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                false);
            var ulProducts = azureBlobProvider.GetQueueByName(AzureStorageConstants.UlProductsQueue);
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var query = unitOfWork.ProductRepository.GetAll().Where(a =>
                    a.ULSyncStatus != ULSyncStatus.WaitForSync &&
                    a.ULUrl != null && a.ULUrl != "");

                if (manufacturerId != null)
                {
                    query = query.Where(a => a.ManufacturerId == manufacturerId.Value);
                }

                if (productId != null)
                {
                    query = query.Where(a => a.Id == productId.Value);
                }

                var products = await query.ToListAsync();

                foreach (var product in products)
                {
                    ulProducts.SendMessage(product.Id.ToString());
                    product.ULSyncStatus = ULSyncStatus.WaitForSync;
                    unitOfWork.ProductRepository.Edit(product);
                }
                await unitOfWork.SaveAsync();
                return Ok();
            }
        }

        [HttpGet]
        [ActionName("CountByCertificates")]
        public async Task<IActionResult> CountByCertificates(string externalCertificateIds)
        {
            var externalCertificateIdsInt = !string.IsNullOrEmpty(externalCertificateIds) ? externalCertificateIds.Split('_').Where(a => !string.IsNullOrWhiteSpace(a)).Select(a => int.Parse(a)).ToList() : null;
            if (externalCertificateIdsInt.Count > 0)
            {
                using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
                {
                    var result = await unitOfWork.ProductCertificateRepository.GetAll()
                        .Where(c =>
                                c.ExternalCertificateId != null &&
                                externalCertificateIdsInt.Contains(c.ExternalCertificateId.Value)
                        ).Select(a => new
                        {
                            productId = a.ProductId,
                            certificateId = a.ExternalCertificateId
                        })
                        .GroupBy(a => a.certificateId)
                        .Select(a => new
                        {
                            certificateId = a.Key,
                            countOfProducts = a.Select(b => b.productId).Distinct().Count()
                        })
                        .AsNoTracking()
                        .ToListAsync();

                    return Ok(result);
                }
            }
            else
            {
                return BadRequest();
            }
        }

        [HttpGet]
        [ActionName("manufacturerProductsIds")]
        public async Task<IActionResult> GetManufacturerProductIdsAsync(int manufacturerId)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var result = await unitOfWork.ProductRepository.GetAll()
                    .AsNoTracking()
                    .Where(x => x.ManufacturerId == manufacturerId && x.Published && !x.Staging)
                    .Select(x => x.Id)
                    .ToListAsync();

                return Ok(result);
            }
        }

        [HttpPost]
        [ActionName("manufacturerProducts")]
        public async Task<IActionResult> GetManufacturerProductsAsync([FromBody] List<int> ids)
        {
            if (ids == null || ids.Count == 0)
            {
                return Ok();
            }

            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var products = await unitOfWork.ProductRepository.GetAll()
                    .Where(x => ids.Contains(x.Id)).Select(x => new
                    {
                        x.Id,
                        x.CategoryId,
                        x.ManufacturerId,
                        x.Name,
                        ImageUrl = x.PhotoId != null ? x.Photo.OriginalImgUrl : null
                    })
                    .AsNoTracking()
                    .ToListAsync();

                return Ok(products);
            }
        }

        ///// <summary>
        ///// Gets the related products.
        ///// </summary>
        ///// <returns></returns>
        /// <summary>
        /// Exports to excel.
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier.</param>
        /// <param name="categoryId">The category identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [Route("/api/Product/export/excel")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> ExportToExcel(int manufacturerId = -1, int categoryId = -1)
        {
            if (manufacturerId == -1 && categoryId == -1)
            {
                return BadRequest("Enter please manufacturer or category id");
            }

            string tempFolder = Path.GetTempPath();

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                ProductsExcelModel model = new ProductsExcelModel();

                if (manufacturerId != -1)
                {
                    var manufacturer = await unitOfWork.ManufacturerRepository.GetByIdAsync(manufacturerId);
                    model.FileName = manufacturer.Name;
                }
                else if (categoryId != -1)
                {
                    var category = await unitOfWork.CategoryRepository.GetByIdAsync(categoryId);
                    model.FileName = category.Name;
                }
                model.QualityItems = await unitOfWork.QualityItemRepository.GetAll().Select(a => new ProductQualityItemsModel { Id = a.Id, Name = a.Name }).ToListAsync();

                var revitProjectDataTypeId = (await unitOfWork.ProjectDataTypeRepository.GetAll().FirstOrDefaultAsync(a => a.Title == "Revit")).Id;
                var dbProducts = await unitOfWork.ProductRepository.GetAll().Where(a => a.ManufacturerId == manufacturerId || a.CategoryId == categoryId)
                                                                    .Include(x => x.ProductCategories)
                                                                    .Include(x => x.ProductPhotos)
                                                                    .Include(x => x.ProductFiles)
                                                                    .Include(x => x.ProductStats)
                                                                    .Include(x => x.RelatedProducts)
                                                                    .Include(x => x.ProductCertificates)
                                                                    .Include(x => x.ProductQualityItems)
                                                                    .Include(x => x.ProductCisfbs)
                                                                    .Include(x => x.ProductMasterformats)
                                                                    .Include(x => x.ProductUniformats)
                                                                    .ToListAsync();

                var products = dbProducts.Select(a => new ProductModel
                {
                    Id = a.Id,
                    Name = a.Name,
                    ManufacturerId = a.ManufacturerId,
                    CategoryId = a.CategoryId,
                    ProductCategoryIds = a.ProductCategories.Select(b => b.CategoryId).ToList(),
                    ProductLineId = a.ProductLineId,
                    ProductUrl = a.ProductUrl,
                    ULUrl = a.ULUrl,
                    PhotoId = a.PhotoId,
                    PhotoURLs = a.ProductPhotos.Where(pp => pp.Photo != null && pp.Photo.UploadUrl != null).Select(pp => pp.Photo.UploadUrl).ToList(),
                    Description = a.Description,
                    VideoUrl = a.VideoUrl,
                    VanityURL = a.VanityURL,
                    MetaTitle = a.MetaTitle,
                    MetaDescription = a.MetaDescription,
                    MetaKeywords = a.MetaKeywords,
                    Keywords = a.Keywords,
                    Published = a.Published,
                    Staging = a.Staging,
                    ExternalId = a.ExternalId,
                    PublishToPartner = a.PublishToPartner,
                    ForgeWallURL = a.ForgeWallURL,
                    ForgeFloorURL = a.ForgeFloorURL,
                    ForgeCeilingURL = a.ForgeCeilingURL,
                    ForgeRoofURL = a.ForgeRoofURL,
                    Weight = a.Weight,
                    RegionIds = a.RegionIds,
                    Attachments = a.ProductFiles.Where(pf => pf.File != null && pf.File.SyncUrl != null && pf.File.SyncUrl != "" && pf.IsAttachment)
                        .Select(p => new ProductFileModel
                        {
                            CustomFileId = p.CustomFileId,
                            FileSyncUrl = p.File.SyncUrl,
                            FileTitle = p.File.Title
                        }).ToList(),
                    ProjectFiles = a.ProductFiles.Where(pf =>
                        pf.File != null &&
                        pf.File.SyncUrl != null &&
                        pf.File.SyncUrl != "" &&
                        !pf.IsAttachment &&
                        pf.ProjectDataTypeId != null &&
                        !pf.File.SyncUrl.Contains(RevitFilesShareDirectoryName) &&
                        !pf.File.SyncUrl.Contains(InternsRevitFilesShareDirectoryName))
                        .Select(p => new ProductFileModel
                        {
                            CustomFileId = p.CustomFileId,
                            FileSyncUrl = p.File.SyncUrl,
                            FileTitle = p.File.Title,
                            FileType = p.SoftwareVersionId.HasValue ? new ProjectDataTypeModel
                            {
                                Title = p.SoftwareVersion.Title
                            }
                            :
                            new ProjectDataTypeModel
                            {
                                Title = p.ProjectDataType.Title
                            }
                        }).ToList(),
                    RevitFiles = a.ProductFiles.Where(pf => !pf.IsAttachment
                        && pf.ProjectDataTypeId == revitProjectDataTypeId
                        && string.IsNullOrWhiteSpace(pf.File.SyncUrl))
                        .Select(x => x.File.Title).Distinct().ToList(),
                    ProductStats = a.ProductStats.Select(p => new ProductProductStatModel
                    {
                        KeyStatName = p.KeyStat.Name,
                        KeyStatType = p.KeyStatType,
                        Note = p.Note,
                        KeyStatUnit = p.KeyStatUnit == null ? null : new ProductStatKeyStatUnitModel
                        {
                            GroupName = p.KeyStatUnit.GroupName,
                            BUnitName = p.KeyStatUnit.BUnitName
                        },
                        Value = p.Value,
                        MinRangeValue = p.MinRangeValue,
                        MaxRangeValue = p.MaxRangeValue,
                        KeyStatValueList = p.KeyStatValueList.Select(ksvl => new ProductStatKeyStatValueListModel
                        {
                            Value = ksvl.Value,
                            MinRangeValue = ksvl.MinRangeValue,
                            MaxRangeValue = ksvl.MaxRangeValue,
                            Note = ksvl.Note,
                            KeyStatUnit = ksvl.KeyStatUnit == null ? null : new ProductStatKeyStatUnitModel
                            {
                                BUnitName = ksvl.KeyStatUnit.BUnitName,
                                GroupName = ksvl.KeyStatUnit.GroupName
                            }
                        }).ToList(),
                    }).Where(ps => ps.KeyStatType != KeyStatType.None).ToList(),
                    RelatedProductsIds = a.RelatedProducts.Select(p => p.RelatedProductId).ToList(),
                    ProductCertificates = a.ProductCertificates.Where(p => p.ExternalCertificateId != null).Select(p => p.ExternalCertificateId.Value).ToList(),
                    ProductQualityItems = a.ProductQualityItems.Select(p => p.QualityItem.Name).ToList(),
                    ProductCisfbs = a.ProductCisfbs.Select(p => p.Cisfb.Code).ToList(),
                    ProductExternalMasterformatIds = a.ProductMasterformats.Select(x => x.ExternalMasterformatId).ToList(),
                    ProductOmniclasses = a.ProductOmniclasses.Select(p => p.Omniclass.Code).ToList(),
                    ProductUniclasses = a.ProductUniclasses.Select(p => p.Uniclass.Code).ToList(),
                    ProductUniformats = a.ProductUniformats.Select(p => p.Uniformat.Code).ToList(),
                    ProductExternalMasterformatCodes = new List<string>(),
                    AttachmentFileNames = a.ProductFiles.Where(x => x.IsAttachment && string.IsNullOrWhiteSpace(x.File.SyncUrl)).Select(x => x.File.FileName).ToList(),
                    ProjectFileNames = a.ProductFiles.Where(x => !x.IsAttachment && string.IsNullOrWhiteSpace(x.File.SyncUrl)).Select(x => x.File.FileName).ToList(),
                    PublishedOnCustomMicrosite = a.PublishedOnCustomMicrosite
                })
                    .OrderBy(x => x.Id)
                    .ToList();

                var allMasterformats = await _masterformatService.GetBackofficeMasterformatsAsync(products.SelectMany(x => x.ProductExternalMasterformatIds).ToList());
                if (allMasterformats.Any())
                {
                    foreach (var product in products)
                    {
                        if (product.ProductExternalMasterformatIds.Any())
                        {
                            product.ProductExternalMasterformatCodes = allMasterformats.Where(x => product.ProductExternalMasterformatIds.Contains(x.Id)).Select(x => x.Code).ToList();
                        }
                    }
                }

                model.Products = products;

                model.RevitProjectTypes = await unitOfWork.ProjectDataTypeRepository.GetAll()
                                                          .Where(x => x.ParentId == revitProjectDataTypeId)
                                                          .Select(x => new ProjectDataTypeModel
                                                          {
                                                              Title = x.Title
                                                          })
                                                          .ToListAsync();

                var excelPath = ExcelProductsProvider.GetExcel(model, tempFolder);
                return PhysicalFile(excelPath, "text/csv", $"{model.FileName}.xlsx");
            }
        }

        /// <summary>
        /// Imports the form excel.
        /// </summary>
        /// <param name="fileId">The file identifier.</param>
        /// <param name="manufacturerId">The manufacturer identifier.</param>
        /// <param name="categoryId">The category identifier.</param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException">Passed not implemented KeyStatType</exception>
        [HttpGet]
        [Route("/api/Product/import/excel")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> ImportFromExcel(int fileId, int manufacturerId = -1, int categoryId = -1)
        {
            if (!await _featureSettingService.GetSettingStatusAsync(DbConstants.FeatureStaticProductsImport))
            {
                return BadRequest("The feature is disabled");
            }
            if (manufacturerId == -1 && categoryId == -1)
            {
                return BadRequest("Enter please manufacturer or category id");
            }

            string tempFolder = Path.GetTempPath();

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var file = await unitOfWork.FileRepository.GetByIdAsync(fileId);
                var filePath = Path.Combine(Path.GetTempPath(), file.Id.ToString() + Path.GetExtension(file.FileName));
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier) ?? DbConstants.AdminUserId;
                var quilityItems = await unitOfWork.QualityItemRepository.GetAll().Select(a => new { a.Id, Name = a.Name.Trim() }).ToListAsync();
                var cisfbs = await unitOfWork.CisfbRepository.GetAll().Select(a => new { a.Id, Code = a.Code.Trim() }).ToListAsync();
                var masterformats = await _masterformatService.GetBackofficeMasterformatsAsync();
                var omniclasses = await unitOfWork.OmniclassRepository.GetAll().Select(a => new { a.Id, Code = a.Code.Trim() }).ToListAsync();
                var uniclasses = await unitOfWork.UniclassRepository.GetAll().Select(a => new { a.Id, Code = a.Code.Trim() }).ToListAsync();
                var uniformats = await unitOfWork.UniformatRepository.GetAll().Select(a => new { a.Id, Code = a.Code.Trim() }).ToListAsync();
                var keyStats = await unitOfWork.KeyStatRepository.GetAll().Select(a => new { a.Id, Name = a.Name.Trim() }).ToListAsync();
                var keyStatUnits = await unitOfWork.KeyStatUnitRepository.GetAll().Select(a => new { a.Id, GroupName = a.GroupName.Trim(), BUnitName = a.BUnitName.Trim() }).ToListAsync();
                var projectDataTypes = await unitOfWork.ProjectDataTypeRepository.GetAll().ToListAsync();

                ProductsExcelParseModel excelParseData = await ExcelProductsProvider.ParseExcel(filePath, quilityItems.Select(a => a.Name).Distinct().ToList());

                var revitFileIdsQueue = new List<string>();
                var caterogyIds = excelParseData.Products.Select(a => a.CategoryId).Distinct().ToList();
                var manufacturerIds = excelParseData.Products.Select(a => a.ManufacturerId).Distinct().ToList();

                var categories = await unitOfWork.CategoryRepository.GetAll().Where(a => caterogyIds.Contains(a.Id)).ToListAsync();
                var manufacturers = await unitOfWork.ManufacturerRepository.GetAll().Where(a => manufacturerIds.Contains(a.Id)).ToListAsync();

                //Create manufacturer info backup if backup for current date is not created already
#if !DEBUG
                foreach (var manufacturer in manufacturers)
                {
                    try
                    {
                        if (!await _manufacturerBackupService.ManufacturerHasBackupForCurrentDataAsync(manufacturer.Id))
                        {
                            await _manufacturerBackupService.CreateManufacturerBackupAsync(manufacturer.Id);
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex.GetAllMessages(), ex);
                    }
                }
#endif

                var attachmentOrdersByManufacturer = unitOfWork.AttachmentOrderRepository.GetAll()
                    .Where(a => manufacturerIds.Contains(a.ManufacturerId))
                    .AsEnumerable()
                    .GroupBy(a => a.ManufacturerId)
                    .ToDictionary(a => a.Key, a => a.OrderBy(b => b.Order)
                    .Select(b => b.Type)
                    .ToList());

                if (!excelParseData.Errors.Any())
                {
                    List<string> quilityItemNames = quilityItems.Select(a => a.Name.Trim()).ToList();
                    List<string> cisfbsCodes = cisfbs.Select(a => a.Code.Trim()).ToList();
                    List<string> externalMasterformatsCodes = masterformats.Select(a => a.Code).ToList();
                    List<string> omniclassesCodes = omniclasses.Select(a => a.Code.Trim()).ToList();
                    List<string> uniclassesCodes = uniclasses.Select(a => a.Code.Trim()).ToList();
                    List<string> uniformatsCodes = uniformats.Select(a => a.Code.Trim()).ToList();
                    List<string> projectDataTypeTitles = projectDataTypes.Select(a => a.Title).ToList();
                    List<string> keyStatNames = keyStats.Select(a => a.Name).Distinct().ToList();

                    //New logic
                    foreach (ProductModel excelProduct in excelParseData.Products)
                    {
                        string id = excelProduct.Id.HasValue ? excelProduct.Id.Value.ToString() : string.Empty;

                        if (excelProduct.Id.HasValue)
                        {
                            Product productdb = unitOfWork.ProductRepository.GetById(excelProduct.Id.Value);
                            if (productdb is null)
                            {
                                excelProduct.ExcelParseErrors.Add($"Product Id: {id}. Can`t find product with current Id in Database");
                            }
                        }

                        if (string.IsNullOrWhiteSpace(excelProduct.Name))
                        {
                            excelProduct.ExcelParseErrors.Add($"Product Id: {id}. Product Name can`t be empty");
                        }

                        //check quility names
                        foreach (string name in excelProduct.ProductQualityItems)
                        {
                            if (!quilityItemNames.Contains(name))
                            {
                                excelProduct.ExcelParseErrors.Add($"Wrong name of quility item: '{name}'");
                            }
                        }
                        //check cisfb codes
                        foreach (string code in excelProduct.ProductCisfbs)
                        {
                            if (!cisfbsCodes.Contains(code))
                            {
                                excelProduct.ExcelParseErrors.Add($"Wrong code of cisfb: '{code}'");
                            }
                        }
                        //check masterformat codes
                        foreach (string masterformatCode in excelProduct.ProductExternalMasterformatCodes)
                        {
                            if (!externalMasterformatsCodes.Contains(masterformatCode))
                            {
                                excelProduct.ExcelParseErrors.Add($"Wrong code of masterformat: '{masterformatCode}'");
                            }
                        }
                        //check omniclass codes
                        foreach (string code in excelProduct.ProductOmniclasses)
                        {
                            if (!omniclassesCodes.Contains(code))
                            {
                                excelProduct.ExcelParseErrors.Add($"Wrong code of omniclass: '{code}'");
                            }
                        }
                        //check uniclass codes
                        foreach (string code in excelProduct.ProductUniclasses)
                        {
                            if (!uniclassesCodes.Contains(code))
                            {
                                excelProduct.ExcelParseErrors.Add($"Wrong code of uniclass: '{code}'");
                            }
                        }
                        //check uniclass codes
                        foreach (string code in excelProduct.ProductUniformats)
                        {
                            if (!uniformatsCodes.Contains(code))
                            {
                                excelProduct.ExcelParseErrors.Add($"Wrong code of uniformat: '{code}'");
                            }
                        }
                        //check project data type titles
                        foreach (string title in excelProduct.ProjectFiles.Select(x => x.FileType.Title))
                        {
                            if (!projectDataTypeTitles.Contains(title))
                            {
                                excelProduct.ExcelParseErrors.Add($"Wrong title of project data type: '{title}'");
                            }
                        }
                        //check keyStat names
                        foreach (string productStatName in excelProduct.ProductStats.Select(x => x.KeyStatName).Distinct())
                        {
                            if (!keyStatNames.Contains(productStatName))
                            {
                                excelProduct.ExcelParseErrors.Add($"Wrong name of KeyStat: '{productStatName}'");
                            }
                        }

                        //check keyStatUnits
                        foreach (ProductStatKeyStatUnitModel productStatKeyStatUnit in excelProduct.ProductStats.Where(ps => ps.KeyStatUnit != null).Select(ps => ps.KeyStatUnit).Distinct())
                        {
                            if (keyStatUnits.FirstOrDefault(ksu => ksu.GroupName.Trim() == productStatKeyStatUnit.GroupName && ksu.BUnitName.Trim() == productStatKeyStatUnit.BUnitName) == null)
                            {
                                excelProduct.ExcelParseErrors.Add($"Wrong KeyStatUnit: GroupName='{productStatKeyStatUnit.GroupName}'; BUnitName='{productStatKeyStatUnit.BUnitName}'");
                            }
                        }
                    }
                }

                List<string> productParseErrors = excelParseData.Products.SelectMany(x => x.ExcelParseErrors).ToList();
                // take product without error for update
                excelParseData.Products = excelParseData.Products.Where(x => !x.ExcelParseErrors.Any()).ToList();

                if (!excelParseData.Errors.Any())
                {
                    excelParseData = await ExcelProductsProvider.ImportProducts(unitOfWork,
                        manufacturerIds,
                        excelParseData,
                        manufacturers,
                        categories,
                        userId,
                        quilityItems,
                        cisfbs,
                        attachmentOrdersByManufacturer,
                        projectDataTypes,
                        masterformats,
                        omniclasses,
                        uniclasses,
                        uniformats,
                        keyStats,
                        keyStatUnits,
                        revitFileIdsQueue);
                }

                productParseErrors.AddRange(excelParseData.Products.SelectMany(x => x.ExcelParseErrors).ToList());
                excelParseData.Errors.AddRange(productParseErrors);

                CacheHelper.ClearSpecificCache("*/api/Product/List*");
                CacheHelper.ClearSpecificCache("*/api/Product/AdminList*");
                CacheHelper.ClearSpecificCache("*/api/Product/ListWithRevitFiles*");

                return Ok(excelParseData);
            }
        }

        /// <summary>
        /// Gets the products list for audit
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        [ActionName("AuditList")]
        public async Task<IActionResult> AuditList()
        {
            using (var unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var products = await unitOfWork.ProductRepository.GetAll()
                    .Select(x => new
                    {
                        id = x.Id,
                        text = x.Name
                    })
                    .AsNoTracking()
                    .ToListAsync();

                return Ok(products);
            }
        }

        #region dynamic excel
        /// <summary>
        /// Exports dynamic selected fields for products.
        /// </summary>
        /// <param name="exportModel">The excel export model</param>
        /// <returns></returns>
        [HttpPost]
        [Route("/api/Product/dynamic/export/excel")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> DynamicExportToExcel(DynamicExportExcelModel exportModel)
        {
            if (exportModel.ManufacturerId == -1 && exportModel.CategoryId == -1)
                return BadRequest("Enter please manufacturer or category id");

            var serverAppDataPath = Path.GetTempPath();

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var model = new DynamicExcelProductsModel();

                if (exportModel.ManufacturerId != -1)
                {
                    var manufacturer = await unitOfWork.ManufacturerRepository.GetByIdAsync(exportModel.ManufacturerId);
                    model.FileName = manufacturer.Name;
                }
                else if (exportModel.CategoryId != -1)
                {
                    var category = await unitOfWork.CategoryRepository.GetByIdAsync(exportModel.CategoryId);
                    model.FileName = category.Name;
                }
                model.QualityItems = await unitOfWork.QualityItemRepository.GetAll().Select(a => new ProductQualityItemsModel { Id = a.Id, Name = a.Name }).ToListAsync();

                var products = await DynamicExcelProvider.GetProductsForDynamicExcelAsync(exportModel.ManufacturerId, exportModel.CategoryId, unitOfWork);
                model.Products = products;

                var excelPath = DynamicExcelProvider.GetDynamicExcel(model, exportModel, serverAppDataPath);
                var fileName = "Dynamic Export Template";
                var fileExtension = "xlsx";
                var fullFileName = string.Concat(fileName, ".", fileExtension);

                var azureBlobProvider = new AzureStorageService(
                    ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                    ConfigurationHelper.GetValue("Environment"),
                    bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                    false);
                var filesContainer = azureBlobProvider.GetContainerByName(AzureStorageConstants.TempFilesContainer);
                BlockBlobClient blobFile = filesContainer.GetBlockBlobClient(fullFileName);
                using (FileStream fs = System.IO.File.OpenRead(excelPath))
                    await blobFile.UploadAsync(fs);

                var sasToken = _fileService.GenerateSasTokenForFile(fileName, fileExtension, blobFile);
                var fileUrl = string.Concat(blobFile.Uri.AbsoluteUri, sasToken);

                return Ok(new { fileUrl });
            }
        }

        /// <summary>
        /// Returns dynamic excel column list
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("/api/Product/dynamic/excel/columnlist")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> GetDynamicExcelColumns()
        {
            return Ok(await Task.Run(() => DynamicExcelProvider.GetDynamicExcelColumnList()));
        }

        /// <summary>
        /// Imports the dynamic form excel.
        /// </summary>
        /// <param name="fileId">The file identifier.</param>
        /// <param name="manufacturerId">The manufacturer identifier.</param>
        /// <param name="categoryId">The category identifier.</param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException">Passed not implemented KeyStatType</exception>
        [HttpGet]
        [Route("/api/Product/dynamic/import/excel")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> DynamicImportFromExcel(int fileId, int manufacturerId = -1, int categoryId = -1)
        {
            if (!await _featureSettingService.GetSettingStatusAsync(DbConstants.FeatureDynamicProductsImport))
                return BadRequest("The feature is disabled");

            string hostUrl = Request.Host.ToString();

            if (manufacturerId == -1 && categoryId == -1)
                return BadRequest("Enter please manufacturer or category id");

            var serverAppDataPath = Path.GetTempPath();
#if !DEBUG
            //Create manufacturer info backup if backup for current date is not created already
            try
            {
                if (!await _manufacturerBackupService.ManufacturerHasBackupForCurrentDataAsync(manufacturerId))
                    await _manufacturerBackupService.CreateManufacturerBackupAsync(manufacturerId);
            }
            catch (Exception ex)
            {
                Log.Error(ex.GetAllMessages(), ex);
            }
#endif
            var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier) ?? DbConstants.AdminUserId;
            var filePath = DynamicExcelProvider.GetFilePath(serverAppDataPath, fileId);
            var errors = await DynamicExcelProvider.ImportDynamicExcelAsync(filePath, manufacturerId, categoryId, userId, hostUrl);
            return Ok(new
            {
                errors
            });
        }

        #endregion

        /// <summary>
        /// Lookups product from external API
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier</param>
        /// <param name="name">The product name</param>
        /// <param name="externalId">The external identifier</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("Lookup")]
        public async Task<IActionResult> Lookup(int manufacturerId, string name = null, string externalId = null)
        {
            var result = await _productService.LookupAsync(manufacturerId, name, externalId) ?? new { };
            return Ok(result);
        }

        /// <summary>
        /// Swatchbox uses this method
        /// Returns products related to Swatchbox products and options 
        /// </summary>
        /// <param name="model">The request model</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost]
        [Route("/api/Product/SwatchboxList")]
        public async Task<IActionResult> SwatchboxList(SwatchboxProductsRequestDto model)
        {
            if (model.AccessKey == null || model.AccessKey != ConfigurationHelper.GetValue("SwatchboxMarketAccessKey"))
                return NotFound();

            return Ok(await _productService.SwatchboxListAsync(model));
        }

        [HttpPost]
        [ActionName("GetProjectFilesList")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> GetProjectFilesList([FromBody] EntityIdsDto model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _productService.GetProjectFilesAsync(model, unitOfWork));
            }
        }

        [HttpPost]
        [ActionName("GetProductFilesList")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> GetProductFilesList([FromBody] EntityIdsDto model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _productService.GetProductFilesAsync(model, unitOfWork));
            }
        }

        [HttpPost]
        [ActionName("ChangePublishedAll")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> ChangePublishedAll(ChangeProductFlagAllDto model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                await _productService.ChangePublishedAllAsync(model, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), unitOfWork);

                await _productService.UpdateMongoProductsAsync(model.ManufacturerId, unitOfWork);

                CacheHelper.ClearSpecificCache("*/api/Product/List*");
                CacheHelper.ClearSpecificCache("*/api/Product/AdminList*");
                CacheHelper.ClearSpecificCache("*/api/Product/Get*");
                CacheHelper.ClearSpecificCache("*/api/Product/PublicGet*");

                return Ok();
            }
        }

        [HttpGet]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> CheckProductsWithTheSameProjectFilesAndDifferentPriceType(int productId)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _productService.CheckProductsWithTheSameProjectFilesAndDifferentPriceTypeAsync(productId, unitOfWork));
        }

        [HttpPost]
        [ActionName("ChangePublishedOnCustomMicrositeAll")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> ChangePublishedOnCustomMicrositeAll(ChangeProductFlagAllDto model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                await _productService.ChangePublishedAllOnCustomMicrositeAsync(model, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), unitOfWork);

                await _productService.UpdateMongoProductsAsync(model.ManufacturerId, unitOfWork);

                CacheHelper.ClearSpecificCache("*/api/Product/List*");
                CacheHelper.ClearSpecificCache("*/api/Product/AdminList*");
                CacheHelper.ClearSpecificCache("*/api/Product/Get*");
                CacheHelper.ClearSpecificCache("*/api/Product/PublicGet*");

                return Ok();
            }
        }

        [HttpPost]
        [ActionName("ChangeStagingAll")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> ChangeStagingAll(ChangeProductFlagAllDto model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                await _productService.ChangeStagingAllAsync(model, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), unitOfWork);

                await _productService.UpdateMongoProductsAsync(model.ManufacturerId, unitOfWork);

                CacheHelper.ClearSpecificCache("*/api/Product/List*");
                CacheHelper.ClearSpecificCache("*/api/Product/AdminList*");
                CacheHelper.ClearSpecificCache("*/api/Product/Get*");
                CacheHelper.ClearSpecificCache("*/api/Product/PublicGet*");
                CacheHelper.ClearSpecificCache("*/api/Product/ListWithRevitFiles*");

                return Ok();
            }
        }

        /// <summary>
        /// Returns relevant product names by search query
        /// </summary>
        /// <param name="filter">The filter</param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        [ActionName("RelevantSearchList")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> RelevantSearchList(RelevantSearchListFilterDto filter)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _productService.RelevantSearchListAsync(filter, unitOfWork));
            }
        }

        [HttpPost]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        [Produces<PaginationListDto<BaseNameListDto>>]
        public async Task<IActionResult> RevitProcessingList(RevitProcessingProductFilterDto model)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _productService.RevitProcessingListAsync(model, unitOfWork));
        }

        #region Private methods

        /// <summary>
        /// Gets the download link.
        /// </summary>
        /// <param name="fileUrl">The file URL.</param>
        /// <param name="fileName">Name of the file.</param>
        /// <returns></returns>
        private static string GetDownloadLink(string fileUrl, string fileName)
        {
            AzureStorageService azureBlobProvider = new AzureStorageService(
                ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                ConfigurationHelper.GetValue("Environment"),
                bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                false);
            var filesContainer = azureBlobProvider.GetContainerByName(AzureStorageConstants.FilesContainer);
            if (fileUrl[fileUrl.Length - 1] == '/')
            {
                fileUrl = fileUrl.Remove(fileUrl.Length - 1);
            }
            var blobName = Path.GetFileName(fileUrl);
            BlockBlobClient blockBlob = filesContainer.GetBlockBlobClient(blobName);
            if (!blockBlob.Exists())
            {
                return fileUrl;
            }

            var blobSasBuilder = new BlobSasBuilder
            {
                ExpiresOn = DateTime.UtcNow.AddHours(2),
                ContentDisposition = string.Format("attachment;filename=\"{0}\"", fileName),
            };
            blobSasBuilder.SetPermissions(BlobAccountSasPermissions.Read);

            return blockBlob.GenerateSasUri(blobSasBuilder).AbsoluteUri;
        }

        private string CloneVanityUrl(string currentVanityUrl, List<string> existingVanityUrls)
        {
            currentVanityUrl = currentVanityUrl + "-2";
            if (existingVanityUrls.Contains(currentVanityUrl))
            {
                return CloneVanityUrl(currentVanityUrl, existingVanityUrls);
            }
            else
            {
                return currentVanityUrl;
            }
        }
        #endregion
    }
}