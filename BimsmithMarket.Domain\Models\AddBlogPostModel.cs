﻿using BIMsmithMarket.Domain.DBModels;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Models
{
    public class AddBlogPostModel
    {
        [Required]
        [StringLength(200)]
        public string VanityId { get; set; }

        [Required]
        public int? BlogCategoryId { get; set; }

        [Required]
        public string Title { get; set; }

        [Required]
        public string HtmlBody { get; set; }

        [Required]
        public string ImageUrlBig { get; set; }

        [Required]
        public string ImageUrlSmall { get; set; }

        [Required]
        public string AuthorTitle { get; set; }

        //[Required]
        public string AuthorEmail { get; set; }

        public string AuthorImageUrl { get; set; }

        public string Tags { get; set; }

        [Required]
        public PublishOption PublishOption { get; set; }

        [Required]
        public List<TargetBlogType> TargetTypes { get; set; }

        public DateTime? PublishedDate { get; set; }

        public string MetaDescription { get; set; }

        public string MetaKeywords { get; set; }

        public string MetaTitle { get; set; }

        public List<BlogMentionedEntryModel> MentionedEntries;

        public DateTime? SchedulePublishDate { get; set; }
    }

    public class EditBlogPostModel : AddBlogPostModel
    {
        [Required]
        public int Id { get; set; }
    }
    
    public class EditBlog
    {
        public int BlogId { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }
    }

    public class AddBlogCategoryModel
    {
        [Required]
        [StringLength(200)]
        public string VanityId { get; set; }

        [Required]
        public string Name { get; set; }

        public int Status { get; set; }

        [Required]
        public List<TargetBlogType> TargetTypes { get; set; }
    }


    public class EditBlogCategoryModel : AddBlogCategoryModel
    {
        [Required]
        public int Id { get; set; }
    }

    public class BlogMentionedEntryModel
    {
        public string Name { get; set; }

        public string EntryId { get; set; }

        public BlogMentionedEntryType EntryType { get; set; }

        public string EntryUrl { get; set; }
    }

    public class AddBlogCommentModel
    {
        public string Comment { get; set; }

        public string PostVanityId { get; set; }
    }

    public class AddSwatchboxBlogCommentModel : AddBlogCommentModel
    {
        [Required]
        public string AngularisToken { get; set; }

        [Required]
        public string CaptchaResponse { get; set; }
    }

}