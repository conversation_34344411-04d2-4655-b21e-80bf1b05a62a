﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class HelpCategory
    {
        [Key]
        public int Id { get; set; }

        public string Title { get; set; }

        public HelpCategoryStatus Status { get; set; }

        public float Weight { get; set; }

        public string VanityUrl { get; set; }

        public string MetaDescription { get; set; }

        public string MetaKeywords { get; set; }

        public string MetaTitle { get; set; }

        public int? ParentCategoryId { get; set; }

        public string CreatedById { get; set; }

        public DateTime CreatedDate { get; set; }

        public string ModifiedById { get; set; }

        public DateTime? ModifiedDate { get; set; }

        public string IconUrl { get; set; }

        public virtual ICollection<HelpArticle> HelpArticles { get; set; }

        [ForeignKey("ParentCategoryId")]
        public virtual HelpCategory ParentCategory { get; set; }

        public virtual ICollection<HelpCategory> Subcategories { get; set; }

        [ForeignKey("CreatedById")]
        public virtual ApplicationUser CreatedBy { get; set; }

        [ForeignKey("ModifiedById")]
        public virtual ApplicationUser ModifiedBy { get; set; }

        public HelpCategory()
        {
            HelpArticles = new List<HelpArticle>();
            Subcategories = new List<HelpCategory>();
        }
    }

    public enum HelpCategoryStatus
    {
        Unpublished = 0,
        Published = 1
    }
}