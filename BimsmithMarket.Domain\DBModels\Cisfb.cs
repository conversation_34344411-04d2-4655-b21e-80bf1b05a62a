﻿using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{

    [Index(nameof(Code), Name = "IX_Cisfb_Code")]
    [Index(nameof(Title), Name = "IX_Cisfb_Title")]
    public class Cisfb
    {
        public int Id { get; set; }

        public int? ParentId { get; set; }

        [StringLength(200)]
        public string Code { get; set; }

        [StringLength(200)]
        public string Title { get; set; }

        [ForeignKey("ParentId")]
        public virtual Cisfb Parent { get; set; }

        public virtual ICollection<Cisfb> Children { get; set; }

        public Cisfb()
        {
            Children = new List<Cisfb>();
        }
    }
}