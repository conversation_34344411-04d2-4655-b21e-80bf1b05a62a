﻿using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.RevitProcessing;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces.RevitProcessing
{
    public interface IRevitProcessingService
    {
        RevitProcessTypeDto[] RevitProcessTypeList();

        MarketFieldDto[] MarketFieldList();

        Task<OperationResultDto> StartRevitProcessAsync(StartRevitProcessDto model, string userId, IUnitOfWork unitOfWork);

        Task<PaginationListDto<RevitProcessListDto>> RevitProcessListAsync(IUnitOfWork unitOfWork, int? manufacturerId = null, int offset = 0, int count = 10);

        Task<OperationResultDto> CancelRevitProcessAsync(int id, string userId, IUnitOfWork unitOfWork);

        Task<byte[]> GenerateReportAsync(int id, IUnitOfWork unitOfWork);

        Task<OperationResultDto> HandleRevitJobResultAsync(RevitJobResultDto model, IUnitOfWork unitOfWork);

        Task<string[]> GetAssemblyCodesAsync(IUnitOfWork unitOfWork);
    }
}