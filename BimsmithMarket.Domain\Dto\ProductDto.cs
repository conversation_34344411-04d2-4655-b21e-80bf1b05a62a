﻿using BIMsmithMarket.Domain.Enums;
using System;

namespace BIMsmithMarket.Domain.Dto
{
    public class AdminListProductDto
    {
        public int Id { get; set; }

        public bool IsFeatured { get; set; }

        public bool Published { get; set; }

        public bool Staging { get; set; }

        public string Name { get; set; }

        public float Weight { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        public DateTime UpdateDate { get; set; }

        public string UserName { get; set; }

        public string PhotoUrl { get; set; }

        public bool PublishedOnCustomMicrosite { get; set; }

        public HealthCheckStatus HealthCheckStatus { get; set; }

        public AdminListProductLineDto ProductLine { get; set; }
    }

    public class AdminListProductLineDto
    {
        public int Id { get; set; }

        public string Name { get; set; }
    }
}