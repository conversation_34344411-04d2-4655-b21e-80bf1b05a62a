﻿using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Specialized;
using Azure.Storage.Sas;
using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.File;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.GeneralModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Helpers;
using BIMsmithMarket.Services.Interfaces;
using FileSignatures;
using HtmlAgilityPack;
using Ionic.Zip;
using Ionic.Zlib;
using Microsoft.EntityFrameworkCore;
using MimeTypes;
using PdfSharpCore.Pdf;
using PdfSharpCore.Pdf.IO;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class FileService : IFileService
    {
        /// <summary>
        /// The log manager
        /// </summary>
        private readonly List<string> _urlWithPdfFrames = new List<string> { "https://www.behr.com/binaries/content/assets/behrdotcom/web/tds" };
        private readonly AzureStorageService _azureBlobProvider = new AzureStorageService(
            ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
            ConfigurationHelper.GetValue("Environment"),
            bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
            false);
        private readonly List<string> _urlsToIgnoreDownload = new List<string>
        {
            "http://productmasterspec.com",
            "https://productmasterspec.com",
            "http://www.productmasterspec.com",
            "https://www.productmasterspec.com"
        };
        private readonly string downloadFolder = Path.Combine(Path.GetTempPath(), ConfigurationHelper.GetValue("Environment"), "Files");
        private readonly string _fileSystemPathPattern = @"^[a-zA-Z]{1}[:]{1}.{0,}$";
        private readonly FileFormatInspector _fileFormatInspector = new FileFormatInspector();

        public FileService()
        {
            if (!Directory.Exists(downloadFolder))
                Directory.CreateDirectory(downloadFolder);
        }

        public async Task<InitDownloadZipResult> InitDownloadZipAsync(
            int[] fileIds,
            int? productId = null,
            int? fileType = null,
            string localFileFolder = null,
            bool downloadConfirmation = false)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                IQueryable<ProductFile> productFileQuery = unitOfWork.ProductFileRepository.GetAll();

                if (fileIds != null && fileIds.Any())
                {
                    productFileQuery = productFileQuery.Where(x => fileIds.Contains(x.FileId));
                }

                if (productId.HasValue)
                {
                    productFileQuery = productFileQuery.Where(x => x.ProductId == productId);
                }

                if (fileType.HasValue)
                {
                    productFileQuery = productFileQuery.Where(x => x.ProjectDataTypeId == fileType);
                }

                if (fileIds == null || !fileIds.Any())
                {
                    fileIds = await productFileQuery.Select(a => a.FileId).ToArrayAsync();
                }

                if (!fileIds.Any())
                    throw new InvalidInputException($"Files not found, check input parameters");

                string fileZipName = HashProvider.CalculateMD5Hash(string.Join("", fileIds)) + ".zip";
                var productFiles = await productFileQuery.ToListAsync();

                // if any file contains in locked product(not free for download) delete container
                bool paidContent = productFiles.Any(x => x.Product.Price?.PriceType != PriceType.Free);

                string archiveContainerName = paidContent ? AzureStorageConstants.LockedArchiveContainer : AzureStorageConstants.ArchivesContainer;
                BlobContainerClient archivesContainer = _azureBlobProvider.GetContainerByName(archiveContainerName);
                BlockBlobClient blobZipFile = archivesContainer.GetBlockBlobClient(fileZipName);

                if (paidContent && !downloadConfirmation)
                {
                    if (await blobZipFile.ExistsAsync())
                        await blobZipFile.DeleteAsync();

                    throw new PaymentConfirmationException("Paid content detected without downloadConfirmation");
                }

                // Check product files
                // If any product file was changed - delete old zip and create new one
                var productFileWasChanged = productFiles.Any(x => x.WasChanged);
                if (productFileWasChanged && await blobZipFile.ExistsAsync())
                {
                    await blobZipFile.DeleteAsync();
                    productFiles.ForEach(x => x.WasChanged = false);
                    await unitOfWork.SaveAsync();
                    blobZipFile = archivesContainer.GetBlockBlobClient(fileZipName);
                }

                //Check detail files
                // If any detail file was changed - delete old zip and create new one
                var detailFiles = unitOfWork.DetailFileRepository.GetAll().Where(x => fileIds.Contains(x.FileId)).ToList();
                var detailFileWasChanged = detailFiles.Any(x => x.WasChanged);
                if (detailFileWasChanged && await blobZipFile.ExistsAsync())
                {
                    await blobZipFile.DeleteAsync();
                    detailFiles.ForEach(x => x.WasChanged = false);
                    await unitOfWork.SaveAsync();
                    blobZipFile = archivesContainer.GetBlockBlobClient(fileZipName);
                }

                //Check product line files
                // If any product line file was changed - delete old zip and create new one
                var productLineFiles = await unitOfWork.ProductLineFileRepository.GetAll().Where(x => fileIds.Contains(x.FileId)).ToListAsync();
                var productLineFilesWasChanged = productLineFiles.Any(x => x.WasChanged);
                if (productLineFilesWasChanged && await blobZipFile.ExistsAsync())
                {
                    await blobZipFile.DeleteAsync();
                    productLineFiles.ForEach(x => x.WasChanged = false);
                    await unitOfWork.SaveAsync();
                    blobZipFile = archivesContainer.GetBlockBlobClient(fileZipName);
                }

                //Check manufacturer files
                // If any manufacturer file was changed - delete old zip and create new one
                var manufacturerFiles = await unitOfWork.ManufacturerFileRepository.GetAll().Where(x => fileIds.Contains(x.FileId)).ToListAsync();
                var manufacturerFilesWasChanged = manufacturerFiles.Any(x => x.WasChanged);
                if (manufacturerFilesWasChanged && await blobZipFile.ExistsAsync())
                {
                    await blobZipFile.DeleteAsync();
                    manufacturerFiles.ForEach(x => x.WasChanged = false);
                    await unitOfWork.SaveAsync();
                    blobZipFile = archivesContainer.GetBlockBlobClient(fileZipName);
                }

                if (await blobZipFile.ExistsAsync())
                {
                    return new InitDownloadZipResult
                    {
                        File = blobZipFile,
                        FilesSkipped = false
                    };
                }

                bool isProduct = productFiles.Any() || productLineFiles.Any() || manufacturerFiles.Any(); //Product or Detail

                using (var lockPoint = await LockAsync.Create(fileZipName))
                {
                    BlobContainerClient paidContainer = _azureBlobProvider.GetContainerByName(AzureStorageConstants.LockedProductFilesContainer);
                    BlobContainerClient freeContainer = _azureBlobProvider.GetContainerByName(AzureStorageConstants.FilesContainer);
                    var dbFiles = unitOfWork.FileRepository.GetAll().Where(a => fileIds.Contains(a.Id)).ToList();
                    if (!dbFiles.Any())
                        return null;

                    bool filesSkipped = false;

                    using (ZipFile zip = new ZipFile())
                    {
                        using (var ms = new MemoryStream())
                        {
                            zip.CompressionLevel = CompressionLevel.BestCompression;
                            zip.ParallelDeflateThreshold = -1;


                            List<string> addedFileNames = new List<string>();
                            bool manyVersionsPresent = productFiles.Select(x => x.SoftwareVersionId).Distinct().Count() > 1;
                            var entries = new List<ZipDataModel>();

                            foreach (Domain.DBModels.File dbFile in dbFiles)
                            {
                                FileLevel fileLevel = GetFileLevel(dbFile.Id, productFiles, productLineFiles, manufacturerFiles, detailFiles);
                                bool isProductAttachment = false;

                                string itemFileName;
                                if (isProduct)
                                {
                                    var productFile = productFiles.FirstOrDefault(x => x.FileId == dbFile.Id);

                                    if (productFile != null)
                                    {
                                        isProductAttachment = productFile.IsAttachment;
                                        itemFileName = productFile.IsAttachment ? dbFile.FileName : dbFile.Title;

                                        //if itemFileName is empty try to get the opposite value
                                        if (string.IsNullOrWhiteSpace(itemFileName))
                                            itemFileName = productFile.IsAttachment ? dbFile.Title : dbFile.FileName;

                                        if (string.IsNullOrWhiteSpace(itemFileName))
                                            itemFileName = Path.GetFileName(dbFile.Url);

                                        //Add subfolder with revit Versions 
                                        if (productFile != null &&
                                            productFile.SoftwareVersionId != null &&
                                            manyVersionsPresent)
                                        {
                                            itemFileName = $"{productFile.SoftwareVersion.Title}/{itemFileName}";
                                        }
                                    }
                                    else
                                    {
                                        itemFileName = dbFile.FileName;

                                        if (string.IsNullOrWhiteSpace(itemFileName))
                                            itemFileName = Path.GetFileName(dbFile.Url);
                                    }
                                }
                                else
                                {
                                    var detailFile = detailFiles.FirstOrDefault(x => x.FileId == dbFile.Id);
                                    itemFileName = dbFile.Title;
                                }

                                while (addedFileNames.Contains(itemFileName))
                                {
                                    if (addedFileNames.Contains(itemFileName))
                                    {
                                        itemFileName =
                                            Path.GetFileNameWithoutExtension(itemFileName) + "_c" +
                                            Path.GetExtension(itemFileName);
                                    }
                                }

                                var blobName = Path.GetFileName(dbFile.Url);
                                if (!string.IsNullOrWhiteSpace(blobName))
                                {
                                    BlobContainerClient container = paidContent && fileLevel == FileLevel.Product && !isProductAttachment ? paidContainer : freeContainer;
                                    BlobClient blobFile = container.GetBlobClient(blobName);
                                    if (await blobFile.ExistsAsync()) //Try to find file on Azure blob
                                    {
                                        using (MemoryStream strm = new MemoryStream())
                                        {
                                            await blobFile.DownloadToAsync(strm);
                                            entries.Add(new ZipDataModel
                                            {
                                                EntryData = strm.ToArray(),
                                                EntryName = itemFileName
                                            });
                                            addedFileNames.Add(itemFileName);

                                        }
                                    }
                                    else
                                    {
                                        filesSkipped = true;
                                    }
                                }
                            }

                            foreach (var entry in entries)
                            {
                                zip.AddEntry(entry.EntryName, entry.EntryData);
                            }
                            zip.Save(ms);
                            ms.Seek(0, SeekOrigin.Begin);
                            ms.Flush();

                            await blobZipFile.UploadAsync(ms);
                        }
                    }
                    return new InitDownloadZipResult
                    {
                        File = blobZipFile,
                        FilesSkipped = filesSkipped
                    };
                }
            }
        }

        public async Task MoveProductFilesToLockedBlob(int productId, IUnitOfWork unitOfWork)
        {
            BlobContainerClient filesContainer = _azureBlobProvider.GetContainerByName(AzureStorageConstants.FilesContainer);
            BlobContainerClient lockedContainer = _azureBlobProvider.GetContainerByName(AzureStorageConstants.LockedProductFilesContainer);
            await MoveToContainer(productId, filesContainer, lockedContainer, unitOfWork);
        }

        public async Task MoveProductFilesToUnLockedBlob(int productId, IUnitOfWork unitOfWork)
        {
            BlobContainerClient lockedContainer = _azureBlobProvider.GetContainerByName(AzureStorageConstants.LockedProductFilesContainer);
            BlobContainerClient filesContainer = _azureBlobProvider.GetContainerByName(AzureStorageConstants.FilesContainer);
            await MoveToContainer(productId, lockedContainer, filesContainer, unitOfWork, true);
        }

        public string FixFileName(string fileName, string defaultName = "archive")
        {
            string regexSearch = new string(Path.GetInvalidFileNameChars()) + new string(Path.GetInvalidPathChars());
            Regex r = new Regex(string.Format("[{0}]", Regex.Escape(regexSearch)));
            fileName = r.Replace(fileName, "");

            //Remove Unicode Characters using Regex
            fileName = Regex.Replace(fileName, @"[^\u0000-\u007F]", String.Empty);

            if (fileName.Length > 250)
            {
                fileName = fileName.Substring(0, 250);
            }

            if (string.IsNullOrWhiteSpace(fileName))
            {
                fileName = defaultName;
            }

            return fileName;
        }

        /// <summary>
        /// Checks if pdf is inside of iframe. If it is parse pdf document link and update fileUrl
        /// </summary>
        /// <param name="fileUrl"></param>
        /// <returns></returns>
        public string FindFileUrlForPdfFrame(string fileUrl)
        {
            var pageUrl = fileUrl;
            var web = new HtmlWeb();
            var htmlDoc = web.Load(pageUrl);
            var originalPageLink = htmlDoc.DocumentNode.SelectSingleNode("//body/object")?.Attributes.Where(x => x.Name == "data").FirstOrDefault()?.Value;
            if (!string.IsNullOrWhiteSpace(originalPageLink))
            {
                htmlDoc = web.Load(originalPageLink);
                var docUrlNodeString = htmlDoc.DocumentNode.SelectNodes("//head/script").Where(x => x.InnerText.Contains("window.viewerPdfUrl")).FirstOrDefault()?.InnerHtml;
                pageUrl = docUrlNodeString.Substring(docUrlNodeString.IndexOf('\'') + 1, docUrlNodeString.LastIndexOf('\'') - docUrlNodeString.IndexOf('\'') - 1);
            }
            return pageUrl;
        }

        public bool IsUrlWithPdfIframe(string fileUrl)
        {
            return _urlWithPdfFrames.Any(x => fileUrl.Contains(x));
        }

        public async Task DownloadFileFromUrlAsync(Domain.DBModels.File file, string attachUrl, BlobContainerClient filesContainer, IUnitOfWork unitOfWork, bool forceUpdate = false)
        {
            int redirectsCount = 0;
        StartRedirect:

            if (!attachUrl.StartsWith("http"))
            {
                attachUrl = new Uri(file.SyncUrl).GetLeftPart(UriPartial.Authority) + attachUrl;
            }

            if (attachUrl.StartsWith("https://"))
            {
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;
                ServicePointManager.ServerCertificateValidationCallback += (sender, certificate, chain, sslPolicyErrors) => true;
            }
            using (HttpClient httpClient = new())
            {
                httpClient.Timeout = TimeSpan.FromMinutes(5);

                if (attachUrl[attachUrl.Length - 1] == '/')
                {
                    attachUrl = attachUrl.Remove(attachUrl.Length - 1);
                }

                using (var response = await httpClient.GetAsync(attachUrl))
                {
                    file.SyncStatusCode = (int)response.StatusCode;

                    var redirectStatus = response.StatusCode == HttpStatusCode.Moved || response.StatusCode == HttpStatusCode.MovedPermanently || response.StatusCode == HttpStatusCode.Found;
                    if (redirectStatus && redirectsCount < 3)
                    {
                        redirectsCount++;
                        attachUrl = response.Headers.Location?.ToString();
                        response.Dispose();
                        goto StartRedirect;
                    }
                    else if (redirectStatus)
                    {
                        var newUrl = response.Headers.Location?.ToString();
                        if (string.IsNullOrWhiteSpace(newUrl))
                        {
                            newUrl = response.RequestMessage.RequestUri.ToString();
                        }
                        file.SyncUrl = newUrl;
                        file.UpdatesCount = 0;
                        file.NextSyncDateTime = DateTime.UtcNow.Date.AddMonths(1);
                    }
                    else if (response.Content.Headers.ContentType.MediaType.Contains("text/html"))
                    {
                        file.SyncStatusCode = 415;
                        file.UpdatesCount = 3;
                    }
                    else if (response.StatusCode == HttpStatusCode.OK || response.StatusCode == HttpStatusCode.Accepted)
                    {
                        if (_urlsToIgnoreDownload.Any(x => x.ToUpper() == attachUrl.ToUpper())
                            || (_urlsToIgnoreDownload.Any(x => attachUrl.ToUpper().Contains(x.ToUpper())) && response.Content.Headers.ContentType.MediaType.Contains("text/html")))
                        {
                            file.SyncStatusCode = (int)HttpStatusCode.NotFound;
                            file.UpdatesCount = 3;
                        }
                        else
                        {
                            if (!string.IsNullOrWhiteSpace(file.SyncUrl) && file.SyncUrl != attachUrl)
                            {
                                file.SyncUrl = attachUrl;
                                unitOfWork.FileRepository.Edit(file);
                            }

                            file.UpdatesCount = 0;
                            file.NextSyncDateTime = DateTime.UtcNow.Date.AddMonths(1);
                            if (string.IsNullOrWhiteSpace(file.FileName))
                            {
                                file.FileName = file.Id.ToString();
                            }

                            using (Stream stream = await response.Content.ReadAsStreamAsync())
                            {
                                string headerMediaType = response.Content.Headers.ContentType.MediaType;
                                string fileNameExtent = Path.GetExtension(file.FileName);
                                string extensionMediaType = MimeTypeProvider.GetMimeType(fileNameExtent);
                                string resultMediaType = headerMediaType != "application/octet-stream" ? headerMediaType : extensionMediaType;

                                bool mediaTypeMismatch = headerMediaType.ToLower() != extensionMediaType.ToLower();
                                if (mediaTypeMismatch)
                                {
                                    fileNameExtent = MimeTypeMap.GetExtension(resultMediaType, false);
                                    //if MimeTypeMap.GetExtension did not find anything take filename from path
                                    if (string.IsNullOrWhiteSpace(fileNameExtent))
                                        fileNameExtent = Path.GetExtension(file.FileName);
                                }
                                var blobName = file.Id.ToString() + fileNameExtent;

                                //Save local to create preview and then delete this local file
                                string filePath = Path.Combine(downloadFolder, blobName);
                                using (var newFile = System.IO.File.Create(filePath))
                                {
                                    await stream.CopyToAsync(newFile);
                                }

                                FileFormat format = _fileFormatInspector.DetermineFileFormat(stream);
                                string mediaType = format?.MediaType ?? resultMediaType;

                                //If document is pdf check its metadata for file system path and replace it
                                if (mediaType.Contains("pdf"))
                                    CheckPdfMetadataAndSave(filePath);

                                file.MediaType = mediaType;

                                if (fileNameExtent.Length > 5 || file.MediaType == "application/octet-stream")
                                {
                                    var extention = MimeTypeMap.GetExtension(mediaType);

                                    if (
                                        file.MediaType == "application/octet-stream" &&
                                        extention == ".doc" &&
                                        (file.SyncUrl.Contains("zurn.com") || file.SyncUrl.Contains("bimsmithstorage.blob.core.windows.net"))
                                       )
                                    {
                                        var fileNameExtention = "." + file.SyncUrl.Substring(file.SyncUrl.Length - 3);
                                        if (fileNameExtention != extention)
                                        {
                                            mediaType = MimeTypeMap.GetMimeType(fileNameExtention);
                                            extention = fileNameExtention;
                                        }
                                    }

                                    file.MediaType = mediaType;
                                    if (Path.GetExtension(filePath).Length != 4)
                                    {
                                        var newfilePath = filePath + extention;
                                        System.IO.File.Move(filePath, newfilePath);
                                        filePath = newfilePath;

                                        var newfileName = file.FileName + extention;
                                        file.FileName = newfileName.Replace("?", "");
                                    }
                                }

                                BlockBlobClient fileBlobUpload = filesContainer.GetBlockBlobClient(blobName);
                                string newFileCheckSum = string.Empty;
                                using (StreamReader streamReader = new StreamReader(filePath))
                                {
                                    newFileCheckSum = HashProvider.CalculateMD5Hash(streamReader.BaseStream);
                                }

                                bool needUpdate = file.CheckSum != newFileCheckSum || mediaTypeMismatch || forceUpdate;
                                if (needUpdate)
                                {
                                    if (await fileBlobUpload.ExistsAsync())
                                    {
                                        // Archive old file
                                        var oldBlobName = "OLD_" + blobName;
                                        BlockBlobClient oldFileBlobUpload = filesContainer.GetBlockBlobClient(oldBlobName);
                                        await oldFileBlobUpload.StartCopyFromUriAsync(fileBlobUpload.Uri);
                                    }

                                    file.SyncStatusCode = (int)HttpStatusCode.Accepted;
                                    if (file.MediaType.StartsWith("image") || Constants.ThumbnailMediaTypes.Contains(file.MediaType))
                                    {
                                        var thumbnail = ThumbnailProvider.GetAndSaveThumbnail(filePath, file.MediaType, file.Id);

                                        //Upload preview on file server
                                        if (!thumbnail.HasError)
                                        {
                                            var previewBlobName = Path.GetFileName(thumbnail.FilePath);
                                            BlockBlobClient previewBlobUpload = filesContainer.GetBlockBlobClient(previewBlobName);
                                            using (FileStream fs = System.IO.File.OpenRead(thumbnail.FilePath))
                                                await previewBlobUpload.UploadAsync(fs);

                                            file.PreviewUrl = previewBlobUpload.Uri.ToString();

                                            //delete local file
                                            System.IO.File.Delete(thumbnail.FilePath);
                                        }
                                    }
                                    using (FileStream fs = System.IO.File.OpenRead(filePath))
                                        await fileBlobUpload.UploadAsync(fs);
                                    file.Url = fileBlobUpload.Uri.ToString();
                                    FileInfo fi = new FileInfo(filePath);
                                    file.FileSize = fi.Length;
                                    file.CheckSum = newFileCheckSum;
                                    file.FileName = fi.Name;
                                }
                                //delete local file
                                System.IO.File.Delete(filePath);
                            }
                        }
                    }
                    else
                    {
                        file.UpdatesCount++;
                        file.NextSyncDateTime = DateTime.UtcNow.Date.AddDays(7);
                    }
                }
            }
        }

        public void CheckPdfMetadataAndSave(string filePath)
        {
            try
            {
                PdfDocument document = PdfReader.Open(filePath, PdfDocumentOpenMode.Modify);
                string documentTitle = document.Info.Title;
                if (!string.IsNullOrWhiteSpace(documentTitle) && Regex.IsMatch(documentTitle, _fileSystemPathPattern))
                {
                    document.Info.Title = Path.GetFileName(documentTitle) ?? "PDF";
                    document.Save(filePath);
                }
            }
            catch (PdfReaderException) { }
        }

        public async Task<BlockBlobClient> GetManufacturerProjectFilesZipAsync(
            DownloadManufacturerProjectFilesDto model,
            IUnitOfWork unitOfWork)
        {
            int[] fileIds = await unitOfWork.ProductFileRepository.GetAllAsNoTracking()
                                            .Where(x => x.Product.ManufacturerId == model.ManufacturerId
                                                    && !x.IsAttachment
                                                    && ((x.ProjectDataTypeId != null && model.ProjectTypeIds.Contains(x.ProjectDataTypeId.Value))
                                                    || (x.ProjectDataTypeId != null && x.ProjectDataType.ParentId != null && model.ProjectTypeIds.Contains(x.ProjectDataType.ParentId.Value))
                                                    || (x.SoftwareVersionId != null && model.ProjectTypeIds.Contains(x.SoftwareVersionId.Value))))
                                            .Select(x => x.FileId)
                                            .Distinct()
                                            .ToArrayAsync();

            InitDownloadZipResult initDownloadZipResult = await InitDownloadZipAsync(fileIds, downloadConfirmation: true);
            return initDownloadZipResult.File;
        }

        #region SAS tokens
        public string GenerateSasTokenForFile(string fileName, string extension, BlockBlobClient blobZipFile)
        {
            var blobSasBuilder = new BlobSasBuilder
            {
                ExpiresOn = DateTime.UtcNow.AddHours(1),
                ContentDisposition = string.Format("attachment;filename=\"{0}\"", fileName + "." + extension)
            };

            blobSasBuilder.SetPermissions(BlobSasPermissions.Read);

            var sasToken = blobZipFile.GenerateSasUri(blobSasBuilder);
            return sasToken.Query;
        }


        public string GenerateSasTokenForArchive(string fileName, BlockBlobClient blobZipFile)
        {
            var blobSasBuilder = new BlobSasBuilder
            {
                ExpiresOn = DateTime.UtcNow.AddHours(1),
                ContentDisposition = string.Format("attachment;filename=\"{0}\"", FixFileName(fileName) + ".zip")
            };
            blobSasBuilder.SetPermissions(BlobAccountSasPermissions.Read);

            var sasToken = blobZipFile.GenerateSasUri(blobSasBuilder);
            return sasToken.Query;
        }

        public static Uri GetSharedAccessUri(string blobName, BlobContainerClient container)
        {
            DateTime toDateTime = DateTime.UtcNow.AddMinutes(60);

            BlockBlobClient blob = container.GetBlockBlobClient(blobName);

            var blobSasBuilder = new BlobSasBuilder
            {
                ExpiresOn = new DateTimeOffset(toDateTime)
            };
            blobSasBuilder.SetPermissions(BlobAccountSasPermissions.Read);
            return blob.GenerateSasUri(blobSasBuilder);
        }

        public async Task<string> GenerateSharedAccessSignatureForFileAsync(int fileId, string containerName, TimeSpan liveWindow, IUnitOfWork unitOfWork)
        {
            var file = await unitOfWork.FileRepository.GetAll()
                .Where(x => x.Id == fileId)
                .Select(x => new
                {
                    x.Id,
                    x.Url
                })
            .FirstAsync();

            string blobName = Path.GetFileName(file.Url);
            BlobContainerClient container = await _azureBlobProvider.GetContainerByNameAsync(containerName);
            BlockBlobClient blob = container.GetBlockBlobClient(blobName);
            BlobSasBuilder blobSasBuilder = new BlobSasBuilder
            {
                StartsOn = DateTime.UtcNow,
                ExpiresOn = DateTime.UtcNow.Add(liveWindow),
                ContentDisposition = string.Format("attachment;filename=\"{0}\"", FixFileName(blobName))
            };
            blobSasBuilder.SetPermissions(BlobAccountSasPermissions.Read);
            Uri sasUrl = blob.GenerateSasUri(blobSasBuilder);

            return string.Concat(blob.Uri.AbsoluteUri, sasUrl.Query);
        }
        #endregion

        #region Private methods
        private async Task MoveToContainer(
            int productId,
            BlobContainerClient sourceContainer,
            BlobContainerClient destinationContainer,
            IUnitOfWork unitOfWork,
            bool fromLocked = false)
        {
            var productFiles = unitOfWork.ProductFileRepository.GetAll().Where(x => x.ProductId == productId && !x.IsAttachment).ToList();
            if (!productFiles.Any())
                return;

            foreach (var pFile in productFiles)
            {
                var dbFile = pFile?.File;
                if (dbFile == null)
                    throw new InvalidInputException($"File was not found in productfile {pFile.Id}");

                var sourceBlobName = Path.GetFileName(dbFile?.Url);
                if (string.IsNullOrEmpty(sourceBlobName))
                    throw new NoRecommendationsException($"Invalid file name for productFile {pFile.Id}");

                BlockBlobClient sourceBlob = sourceContainer.GetBlockBlobClient(sourceBlobName);
                if (!await sourceBlob.ExistsAsync())
                    continue;

                string newBlobName = sourceBlob.Name;

                BlockBlobClient destBlob = destinationContainer.GetBlockBlobClient(newBlobName);
                await destBlob.StartCopyFromUriAsync(GetSharedAccessUri(sourceBlob.Name, sourceContainer));
                //Compare blobs and delete old one
                var sourceProp = await sourceBlob.GetPropertiesAsync();
                var destProp = await destBlob.GetPropertiesAsync();
                //if (sourceBlob.Properties.Length == destBlob.Properties.Length)
                if (sourceProp.Value.ContentHash.SequenceEqual(destProp.Value.ContentHash))
                    await sourceBlob.DeleteAsync();
                else
                    throw new ModifiedOperationException("Blobs were not copy success");

                dbFile.Url = destBlob.Uri.AbsoluteUri;
                unitOfWork.FileRepository.Edit(dbFile);

                pFile.WasChanged = true;
                unitOfWork.ProductFileRepository.Edit(pFile);

                await unitOfWork.SaveAsync();
            }
        }

        private FileLevel GetFileLevel(int fileId,
            IEnumerable<ProductFile> productFiles,
            IEnumerable<ProductLineFile> productLineFiles,
            IEnumerable<ManufacturerFile> manufacturerFiles,
            IEnumerable<DetailFile> detailFiles)
        {
            if (productFiles != null && productFiles.Any(x => x.FileId == fileId))
                return FileLevel.Product;
            else if (productLineFiles != null && productLineFiles.Any(x => x.FileId == fileId))
                return FileLevel.ProductLine;
            else if (manufacturerFiles != null && manufacturerFiles.Any(x => x.FileId == fileId))
                return FileLevel.Manufacturer;
            else if (detailFiles != null && detailFiles.Any())
                return FileLevel.Detail;
            else return FileLevel.Unknown;
        }
        #endregion
    }
}