﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("api/[controller]/[action]/{id?}")]
    public class ManufacturerAdminUserController : BaseApiController
    {
        private readonly IManufacturerAdminUserService _manufacturerAdminUserService;

        public ManufacturerAdminUserController(IManufacturerAdminUserService manufacturerAdminUserService)
        {
            _manufacturerAdminUserService = manufacturerAdminUserService;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Add")]

#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Add([FromBody] AddManufacturerAdminUserModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var result = await _manufacturerAdminUserService.AddAsync(unitOfWork, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), model);

                CacheHelper.ClearSpecificCache("*/api/ManufacturerAdminUser/List*");

                return Ok(result);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Edit")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> Edit([FromBody] EditManufacturerAdminUserModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                await _manufacturerAdminUserService.EditAsync(unitOfWork, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), model);

                CacheHelper.ClearSpecificCache("*/api/ManufacturerAdminUser/List*");

                return Ok();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [ActionName("Delete")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> Delete(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                await _manufacturerAdminUserService.DeleteAsync(unitOfWork, id);

                CacheHelper.ClearSpecificCache("*/api/ManufacturerAdminUser/List*");

                return Ok();
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="manufacturerId"></param>
        /// <param name="dateFrom"></param>
        /// <param name="dateTo"></param>
        /// <param name="count"></param>
        /// <param name="offset"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("List")]
        [Authorize(Roles = DbConstants.AdminRole)]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> List(int manufacturerId, DateTime? dateFrom = null, DateTime? dateTo = null, int count = 100, int offset = 0)
        {
            using (var unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _manufacturerAdminUserService.ListAsync(unitOfWork, manufacturerId, dateFrom, dateTo, count, offset));
            }
        }
    }
}