﻿using BIMsmithMarket.Domain.Enums;
using System;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Dto.PaymentsDto
{
    public class UserPaidProductDto
    {
        public int? Id { get; set; }
        [Required]
        public int PaidProductId { get; set; }
        [Required]
        public string UserId { get; set; }
        public PriceType PriceType { get; set; }
        public string StripeSessionStatus { get; set; }
        public string StripeSessionId { get; set; }
        public bool Paid { get => StripeSessionStatus == "paid"; }
        public DateTime? StripeSessionMark { get; set; }
        public string Key { get; set; }
    }


    public class UserPaidProductCreateByAdmin
    {
        [Required]
        public int PaidProductId { get; set; }
        [Required]
        public string UserId { get; set; }
    }
}
