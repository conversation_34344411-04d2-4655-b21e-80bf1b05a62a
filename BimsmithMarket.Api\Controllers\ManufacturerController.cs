﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.Manufacturer;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Domain.Models.MassTransit.Events;
using BIMsmithMarket.Services.Interfaces;
using BIMsmithMarket.Services.Providers;
using BIMsmithMarket.Services.Search;
using Mapster;
using MassTransit;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// Manufacturer Controller
    /// </summary>
    [Route("api/[controller]/[action]/{id?}")]
    public class ManufacturerController : BaseApiController
    {
        /// <summary>
        /// The file service
        /// </summary>
        private readonly IManufacturerService _manufacturerService;
        private readonly IFeatureSettingService _featureSettingService;
        private readonly IProductService _productService;
        private readonly IManufacturerBackupService _manufacturerBackupService;
        private readonly IManufacturerBackupRestoreService _manufacturerBackupRestoreService;
        private readonly IWebHostEnvironment _webHostEnvironment;
        /// <summary>
        /// The style file service
        /// </summary>
        private readonly IStyleFileService _styleFileService;
        private readonly IHealthDashboardService _healthDashboardService;
        private readonly SlackWebHook _slackWebHook;
        private readonly IUserService _userService;
        private readonly IManufacturerExcelService _manufacturerExcelService;
        private readonly IAnalyticsService _analyticsService;
        private readonly IPublishEndpoint _publishEndpoint;

        public ManufacturerController(
            IManufacturerService manufacturerService,
            IFeatureSettingService featureSettingService,
            IProductService productService,
            IManufacturerBackupService manufacturerBackupService,
            IManufacturerBackupRestoreService manufacturerBackupRestoreService,
            IStyleFileService styleFileService,
            IHealthDashboardService healthDashboardService,
            IWebHostEnvironment webHostEnvironment,
            SlackWebHook slackWebHook,
            IUserService userService,
            IManufacturerExcelService manufacturerExcelService,
            IAnalyticsService analyticsService,
            IPublishEndpoint publishEndpoint)
        {
            _manufacturerService = manufacturerService;
            _featureSettingService = featureSettingService;
            _productService = productService;
            _manufacturerBackupService = manufacturerBackupService;
            _manufacturerBackupRestoreService = manufacturerBackupRestoreService;
            _styleFileService = styleFileService;
            _healthDashboardService = healthDashboardService;
            _webHostEnvironment = webHostEnvironment;
            _slackWebHook = slackWebHook;
            _userService = userService;
            _manufacturerExcelService = manufacturerExcelService;
            _analyticsService = analyticsService;
            _publishEndpoint = publishEndpoint;
        }

        /// <summary>
        /// Get list of all manufacturers from database
        /// </summary>
        /// <param name="q">Search query</param>
        /// <param name="published">If true - return only pablished items</param>
        /// <param name="regionId">The region identifier.</param>
        /// <param name="stateId">The state indentifier.</param>
        /// <param name="manufacturerId"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("List")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> List(
            string q = null,
            bool published = false,
            string regionId = null,
            string stateId = null,
            int manufacturerId = -1)
        {
            if (regionId == "null")
                regionId = null;

            if (stateId == "null")
                stateId = null;

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var manufacturers = ManufacturerSearch.Search(new ManufacturerSearchOptions
                {
                    ListQuery = q,
                    Published = published,
                    ManufacturerIds = manufacturerId != -1 ? new List<int> { manufacturerId } : new()
                }, unitOfWork.ManufacturerRepository.GetAll(), unitOfWork);

                if (!string.IsNullOrWhiteSpace(regionId))
                {
                    manufacturers = manufacturers.Where(m => m.RegionIds.Contains(regionId) ||
                                                             m.RegionIds == null || m.RegionIds == "" ||
                                                             m.Products.Any(p => p.RegionIds.Contains(regionId)) ||
                                                             m.ProductLines.Any(p => p.RegionIds.Contains(regionId)));
                }

                if (!string.IsNullOrWhiteSpace(stateId))
                {
                    manufacturers = manufacturers.Where(m => m.StateIds.Contains(stateId) ||
                                                             m.StateIds == null || m.StateIds == "" ||
                                                             m.Products.Any(p => p.StateIds.Contains(stateId)) ||
                                                             m.ProductLines.Any(p => p.StateIds.Contains(stateId)));
                }

                return Ok(await manufacturers.AsNoTracking()
                    .OrderBy(a => a.Name)
                    .ProjectToType<ManufacturerListDto>()
                    .ToArrayAsync());
            }
        }

        /// <summary>
        /// Get detailed information about Manufacturer by Id
        /// </summary>
        /// <param name="id">Id of manufacturer</param>
        /// <param name="name">Name of manufacturer</param>
        /// <param name="vanityUrl">Name of manufacturer</param>
        /// <param name="customMicrositeName">Custom microsite name</param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        [ActionName("Get")]
        [WebApiOutputCache(CacheConstants.ServerExpiration, true)]
        public async Task<IActionResult> Get(
            int? id = null,
            string name = null,
            string vanityUrl = null,
            string customMicrositeName = null,
            bool? onlyPublishedChildManufacturers = null)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                bool isSubscribed = false;
                bool isLetsTalk = false;
                bool isLunchLearn = false;

                IQueryable<Manufacturer> query = null;

                if (id != null)
                {
                    query = unitOfWork.ManufacturerRepository.GetAll().Where(a => a.Id == id);
                }
                else if (name != null)
                {
                    name = name.TrimEnd('_');
                    query = unitOfWork.ManufacturerRepository.GetAll().Where(a => a.Name.Replace("&", "") == name);
                }
                else if (vanityUrl != null)
                {
                    vanityUrl = vanityUrl.TrimEnd('_');
                    query = unitOfWork.ManufacturerRepository.GetAll().Where(a => a.HubVanityURL == vanityUrl);
                }
                else if (customMicrositeName != null)
                {
                    customMicrositeName = customMicrositeName.Trim().ToUpper();
                    query = unitOfWork.ManufacturerRepository.GetAll().Where(a => a.CustomMicrositeName.ToUpper() == customMicrositeName);
                }

                string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);

                id = (await query.FirstOrDefaultAsync())?.Id ?? -1;

                if (userId != null && id != -1)
                {
                    isSubscribed = await unitOfWork.UserBIMsmithManufacturerRepository.GetAll().AnyAsync(a => a.ManufacturerId == id && a.AddedById == userId);
                    isLetsTalk = await unitOfWork.UserBIMsmithLTManufacturerRepository.GetAll().AnyAsync(a => a.ManufacturerId == id && a.AddedById == userId);
                    isLunchLearn = await unitOfWork.UserBIMsmithLLManufacturerRepository.GetAll().AnyAsync(a => a.ManufacturerId == id && a.AddedById == userId);
                }

                var item = await query
                    .AsSplitQuery()
                    .Select(a => new GetManufacturerDto
                    {
                        id = a.Id,
                        forgeManufacturerId = a.ForgeManufacturerId,
                        name = a.Name,
                        manufacturerHubTitle = a.ManufacturerHubTitle,
                        manufacturerHubSubtitle = a.ManufacturerHubSubtitle,
                        description = a.Description,
                        site = a.Site,
                        synonyms = a.Synonyms,
                        regionIds = a.RegionIds,
                        stateIds = a.StateIds,
                        note = a.Note,
                        type = (int)a.Type,
                        phoneMask = a.PhoneMask,
                        phoneNumber = a.PhoneNumber,
                        pageSetting = a.PageSetting,
                        analyticsSetting = a.AnalyticsSetting,
                        nodeSetting = a.NodeSetting,
                        metaDescription = a.MetaDescription,
                        metaKeywords = a.MetaKeywords,
                        swatchboxManufacturerId = a.SwatchboxManufacturerId,
                        metaTitle = a.MetaTitle,
                        keywords = a.Keywords == null ? null : a.Keywords.Trim(),
                        address = a.AddressId == null ? null : new AddressDto
                        {
                            Address1 = a.Address.Address1,
                            Address2 = a.Address.Address2,
                            Country = a.Address.Country,
                            State = a.Address.State,
                            City = a.Address.City,
                            Province = a.Address.Province,
                            Zip = a.Address.Zip
                        },
                        isOnForge = a.IsOnForge,
                        isOnMarket = a.IsOnMarket,
                        lAndL = a.LAndL,
                        letsTalk = a.LetsTalk,
                        sendULInfo = a.SendULInfo,
                        letsTalkSettings = a.LetsTalkSettings,
                        subscribeButton = a.SubscribeButton,
                        emailLandL = a.EmailLandL,
                        emailLetsTalk = a.EmailLetsTalk,
                        emailLandLCC = a.EmailLandLCC,
                        emailLetsTalkCC = a.EmailLetsTalkCC,
                        videoUrl = a.VideoUrl,
                        image = a.ImageId == null ? null : new GetManufacturerImageDto
                        {
                            id = a.ImageId,
                            small = a.ImageId != null ? a.Image.SmallImgUrl : null,
                            big = a.ImageId != null ? a.Image.OriginalImgUrl : null
                        },
                        originalImage = a.OriginalImageId == null ? null : new GetManufacturerOriginalImageDto
                        {
                            id = a.OriginalImageId,
                            original = "",// a.OriginalImageId != null ? a.Or.OriginalImgUrl : null
                        },
                        hubVanityURL = a.HubVanityURL,
                        forgeUrl = a.ForgeUrl,
                        marketUrl = a.MarketUrl,
                        ownerId = a.OwnerId,
                        logo = new GetManufacturerImageDto
                        {
                            id = a.PhotoId,
                            small = a.PhotoId != null ? a.Photo.SmallImgUrl : null,
                            big = a.PhotoId != null ? a.Photo.OriginalImgUrl : null,
                        },
                        productLines = a.ProductLines.Select(p => new GetManufacturerProductLineDto
                        {
                            id = p.Id,
                            name = p.Name,
                            regionIds = p.RegionIds,
                            stateIds = p.StateIds,
                            manufacturerId = p.ManufacturerId,
                            manufacturerName = p.Manufacturer.Name,
                            description = p.Description
                        }),
                        updateDate = a.ModifiedDate ?? a.CreatedDate,
                        published = a.Published,
                        publishToPartner = a.PublishToPartner,
                        staging = a.Staging,
                        manualUrl = a.ManualUrl,
                        includeRevitPlugin = a.IncludeRevitPlugin,
                        revitIcon = a.RevitPluginPhotoId == null ? null : new GetManufacturerRevitImageDto
                        {
                            id = a.RevitPluginPhotoId,
                            smallUrl = a.RevitPluginPhoto.SmallImgUrl,
                            originalUrl = a.RevitPluginPhoto.OriginalImgUrl
                        },
                        isLetsTalk = isLetsTalk,
                        isSubscribed = isSubscribed,
                        isLunchLearn = isLunchLearn,
                        productFiles = a.ManufacturerFiles.Where(f => f.IsAttachment).Select(r => new GetManufacturerManufacturerFileDto
                        {
                            id = r.FileId,
                            customFileId = r.CustomFileId,
                            title = r.File.Title,
                            fileName = r.File.FileName,
                            fileSize = r.File.FileSize,
                            updatesCount = r.File.UpdatesCount,
                            fileSyncStatusCode = r.File.SyncStatusCode,
                            fileSyncUrl = r.File.SyncUrl,
                            mimeType = r.File.MediaType,
                            url = r.File.Url,
                            preview = r.File.PreviewUrl,
                            weight = r.Weight
                        })
                        .OrderByDescending(x => x.weight)
                        .AsEnumerable(),
                        detailsMicrositeSettings = a.DetailsMicrositeSettings,
                        styleFiles = a.ManufacturerStyleFiles.Select(x => new GetManufacturerManufacturerStyleFileDto
                        {
                            fileId = x.FileId,
                            title = x.Title,
                            fileName = x.File.FileName,
                            fileSize = x.File.FileSize,
                            mimeType = x.File.MediaType,
                            url = x.File.Url,
                            fileVersion = x.FileVersion,
                            type = (int)x.Type
                        }),
                        additionalFiles = a.ManufacturerAdditionalFiles.Where(f => f.IsAttachment).Select(r => new GetManufacturerManufacturerFileDto
                        {
                            id = r.FileId,
                            customFileId = r.CustomFileId,
                            title = r.File.Title,
                            fileName = r.File.FileName,
                            fileSize = r.File.FileSize,
                            updatesCount = r.File.UpdatesCount,
                            fileSyncStatusCode = r.File.SyncStatusCode,
                            fileSyncUrl = r.File.SyncUrl,
                            mimeType = r.File.MediaType,
                            url = r.File.Url,
                            preview = r.File.PreviewUrl,
                            weight = r.Weight
                        })
                        .OrderByDescending(x => x.weight)
                        .AsEnumerable(),
                        showFooterAd = a.ShowFooterAd,
                        footerAdUrl = a.FooterAdUrl,
                        footerAdImageId = a.FooterAdImageId,
                        footerAdImage = a.FooterAdImageId == null ? null : new GetManufacturerImageDto
                        {
                            id = a.FooterAdImageId,
                            small = a.FooterAdImageId != null ? a.FooterAdImage.SmallImgUrl : null,
                            big = a.FooterAdImageId != null ? a.FooterAdImage.OriginalImgUrl : null
                        },
                        useCustomLoginRegisterScreen = a.UseCustomLoginRegisterScreen,
                        customSignInBackgroundColor = a.CustomSignInBackgroundColor,
                        customSignInTextColor = a.CustomSignInTextColor,
                        customSignInDescription = a.CustomSignInDescription,
                        customSignInLogoImageId = a.CustomSignInLogoImageId,
                        customSignInLogoImage = a.CustomSignInLogoImageId == null ? null : new GetManufacturerImageDto
                        {
                            id = a.CustomSignInLogoImageId,
                            small = a.CustomSignInLogoImageId != null ? a.CustomSignInLogoImage.SmallImgUrl : null,
                            big = a.CustomSignInLogoImageId != null ? a.CustomSignInLogoImage.OriginalImgUrl : null
                        },
                        isParentManufacturer = a.IsParentManufacturer,
                        CustomMicrositeName = a.CustomMicrositeName,
                        Weight = a.Weight,
                        RequestPricingEnabled = a.RequestPricingEnabled,
                        RequestPricingEmail = a.RequestPricingEmail,
                        RequestPricingEmailCC = a.RequestPricingEmailCC
                    })
                    .FirstOrDefaultAsync();

                if (item == null)
                    return NotFound("Not found the Manufacturer");

                if (id != -1)
                {
                    IQueryable<Manufacturer> childManufacturersQuery = unitOfWork.ManufacturerRepository.GetAll().Where(x => x.ParentId == id);

                    item.childManufacturers = await childManufacturersQuery.ProjectToType<GetManufacturerChildDto>().ToListAsync();
                }

                return Ok(item);
            }
        }

        /// <summary>
        /// Gets the files.
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("GetFiles")]
        public async Task<IActionResult> GetFiles(int manufacturerId)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var query = unitOfWork.ManufacturerFileRepository.GetAll().Where(a => a.ManufacturerId == manufacturerId);

                var list = await query
                    .OrderByDescending(a => a.CreatedDate)
                    .Select(a => new
                    {
                        fileId = a.FileId,
                        mediaType = a.File.MediaType,
                        title = a.File.Title,
                        fileName = a.File.FileName,
                        fileSize = a.File.FileSize,
                        url = a.File.Url,
                        createdDate = a.CreatedDate
                    })
                    .AsNoTracking()
                    .ToListAsync();

                return Ok(list);
            }
        }

        /// <summary>
        /// Gets the forge manufacturers video urls.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("GetForgeManufacturersVideoUrls")]
        public async Task<IActionResult> GetForgeManufacturersVideoUrls()
        {
            dynamic result;
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var query = unitOfWork.ManufacturerRepository.GetAll().Where(a => a.ForgeManufacturerId != null)
                    .Select(a => new
                    {
                        forgeId = a.ForgeManufacturerId,
                        videoUrl = a.VideoUrl
                    });

                result = await query.AsNoTracking().ToListAsync();
            }
            return Ok(result);
        }

        /// <summary>
        /// Add new manufacturer: Role-ADMIN
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Add")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> Add([FromBody] AddManufacturerDto model)
        {
            if (!await _featureSettingService.GetSettingStatusAsync(DbConstants.FeatureAddManufacturerViaAdminPanel))
                return BadRequest("The feature is disabled");

            if (model.LAndL && string.IsNullOrWhiteSpace(model.EmailLandL))
                ModelState.AddModelError("emailLandL", "Email for LandL is required");

            if (model.LetsTalk && string.IsNullOrWhiteSpace(model.EmailLetsTalk))
                ModelState.AddModelError("emailLetsTalk", "Email for LetsTalk is required");

            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            AddManufacturerResult result = await _manufacturerService.AddAsync(model, userId, unitOfWork);

            ClearManufacturerListCache();

            return Ok(result);
        }

        /// <summary>
        /// Edit manufacturer: Role-ADMIN
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Edit")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Edit([FromBody] EditManufacturerDto model)
        {
            if (!await _featureSettingService.GetSettingStatusAsync(DbConstants.FeatureUpdateManufacturerViaAdminPanel))
                return BadRequest("The feature is disabled");

            if (model.LAndL && string.IsNullOrWhiteSpace(model.EmailLandL))
                ModelState.AddModelError("emailLandL", "Email for LandL is required");

            if (model.LetsTalk && string.IsNullOrWhiteSpace(model.EmailLetsTalk))
                ModelState.AddModelError("emailLetsTalk", "Email for LetsTalk is required");

            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            EditManufacturerResult result = await _manufacturerService.EditAsync(model, userId, unitOfWork);

            await _publishEndpoint.Publish(new ManufacturerChangedEvent
            {
                ManufacturerId = model.Id
            });

            CacheHelper.ClearSpecificCache($"*/api/Manufacturer/Get*");
            CacheHelper.ClearSpecificCache("*/api/Product/AdminList*");
            ClearManufacturerListCache();

            return Ok(result);
        }

        /// <summary>
        /// Update manufacturer RegionIds
        /// </summary>
        /// <param name="id">Id of Manufacturer</param>
        /// <param name="regionIds">Ids of regions (id1_id2_id3_..._idN) </param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("UpdateRegionIds")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> UpdateRegionIds(int id, string regionIds)
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            var result = await _manufacturerService.UpdateRegionIdsAsync(id, regionIds, userId, unitOfWork);
            CacheHelper.ClearSpecificCache($"*/api/Manufacturer/Get*");
            ClearManufacturerListCache();
            return Ok(result);
        }

        /// <summary>
        /// Update manufacturer RegionIds
        /// </summary>
        /// <param name="id">Id of Manufacturer</param>
        /// <param name="stateIds">Ids of states (id1_id2_id3_..._idN) </param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("UpdateStateIds")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> UpdateStateIds(int id, string stateIds)
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            var result = await _manufacturerService.UpdateStateIdsAsync(id, stateIds, userId, unitOfWork);
            CacheHelper.ClearSpecificCache($"*/api/Manufacturer/Get*");
            ClearManufacturerListCache();
            return Ok(result);
        }

        /// <summary>
        /// Get info for delete
        /// </summary>
        /// <param name="id">Id of Manufacturer</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("GetDeleteInfo")]
        public async Task<IActionResult> GetDeleteInfo(int id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var manufacturer = await unitOfWork.ManufacturerRepository.GetByIdAsync(id);

                var countOfProducts = await unitOfWork.ProductRepository.GetAll().Where(a => a.ManufacturerId == manufacturer.Id).CountAsync();

                var result = new
                {
                    countOfProducts = countOfProducts,
                };

                return Ok(result);
            }
        }

        /// <summary>
        /// Delete Manufacturer and all products from of the Manufacturer: Role-ADMIN
        /// </summary>
        /// <param name="model">Manufacturer Id</param>
        /// <returns></returns>
        [HttpDelete]
        [ActionName("Delete")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> Delete([FromBody] DeleteManufacturerModel model)
        {
            if (!await _featureSettingService.GetSettingStatusAsync(DbConstants.FeatureDeleteManufacturerViaAdminPanel))
                return BadRequest("The feature is disabled");

            using (var unitOfWork = UnitOfWork.Create())
            {
                var manufacturer = await unitOfWork.ManufacturerRepository.GetByIdAsync(model.Id);

                if (manufacturer == null)
                {
                    return NotFound("Not Found");
                }

                var productIds = await unitOfWork.ProductRepository.GetAll().Where(a => a.ManufacturerId == manufacturer.Id).Select(a => a.Id).ToListAsync();

                unitOfWork.BeginTransaction();

                //Delete product lines 
                //---------------------------------------
                var productLines = manufacturer.ProductLines.ToList();
                var productLineIds = productLines.Select(a => a.Id).ToList();
                //Update product 
                //-------------------
                var productsToUpdate = await unitOfWork.ProductRepository.GetAll().Where(a => a.ProductLineId != null && productLineIds.Contains(a.ProductLineId.Value)).ToListAsync();

                foreach (var product in productsToUpdate)
                {
                    product.ProductLineId = null;
                    unitOfWork.ProductRepository.Edit(product);
                }
                //------------------
                var productLineFiles = await unitOfWork.ProductLineFileRepository.GetAll().Where(a => productLineIds.Contains(a.ProductLineId)).ToListAsync();
                unitOfWork.ProductLineFileRepository.Delete(productLineFiles);

                unitOfWork.ProductLineRepository.Delete(productLines);
                //---------------------------------------


                if (model.MoveToManufacturerId == null)
                {
                    //Delete all products
                    await _productService.DeleteProductsByIds(unitOfWork, productIds);
                }
                else
                {
                    //Update products 
                    var products = await unitOfWork.ProductRepository.GetAll().Where(a => a.ManufacturerId == model.Id).ToListAsync();
                    foreach (var product in products)
                    {
                        product.ManufacturerId = model.MoveToManufacturerId.Value;
                        unitOfWork.ProductRepository.Edit(product);
                    }
                }

                await unitOfWork.SaveAsync();
                //update child manufacturers
                await unitOfWork.ManufacturerRepository.GetAll()
                    .Where(x => x.ParentId == manufacturer.Id)
                    .ExecuteUpdateAsync(x => x.SetProperty(m => m.ParentId, m => null));

                //Delete manufacturer
                unitOfWork.ManufacturerRepository.Delete(manufacturer);

                await unitOfWork.SaveAsync();

                unitOfWork.CommitTransaction();

                ClearManufacturerListCache();

                return Ok();
            }
        }

        /// <summary>
        /// Get a list of photos from bulk upload
        /// </summary>
        /// <param name="manufacturerId"></param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("PhotoBankList")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> PhotoBankList(int manufacturerId, int offset = 0, int count = 10)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var query = unitOfWork.ManufacturerPhotoRepository.GetAll().Where(a => a.ManufacturerId == manufacturerId);

                var countOfData = await query.CountAsync();

                var list = await query
                    .OrderByDescending(a => a.CreatedDate)
                    .Skip(offset)
                    .Take(count)
                    .Select(a => new
                    {
                        photoId = a.PhotoId,
                        small = a.Photo.SmallImgUrl,
                        middle = a.Photo.OriginalImgUrl.Replace("_b", "_m"), // try to get middle size
                        big = a.Photo.OriginalImgUrl,
                        name = a.Photo.Name,
                        cratedDate = a.CreatedDate
                    })
                    .AsNoTracking()
                    .ToListAsync();

                var result = new
                {
                    count = countOfData,
                    data = list
                };

                return Ok(result);
            }
        }

        /// <summary>
        /// Get a list of files from bulk upload
        /// </summary>
        /// <param name="manufacturerId"></param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("FileBankList")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> FileBankList(int manufacturerId, int offset = 0, int count = 10)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var query = unitOfWork.ManufacturerFileRepository.GetAll().Where(a => a.ManufacturerId == manufacturerId);

                var countOfData = await query.CountAsync();

                var list = await query
                    .OrderByDescending(a => a.CreatedDate)
                    .Skip(offset)
                    .Take(count)
                    .Select(a => new
                    {
                        fileId = a.FileId,
                        projectType = new
                        {
                            id = a.ProjectDataTypeId,
                            title = a.ProjectDataType.Title,
                            header = a.ProjectDataType.Header
                        },
                        title = a.File.Title,
                        fileName = a.File.FileName,
                        fileSize = a.File.FileSize,
                        mimeType = a.File.MediaType,
                        url = a.File.Url,
                        preview = a.File.PreviewUrl,
                        createdDate = a.CreatedDate
                    })
                    .AsNoTracking()
                    .ToListAsync();

                var result = new
                {
                    count = countOfData,
                    data = list
                };

                return Ok(result);
            }
        }

        /// <summary>
        /// add photos and files to manufacturer file nabk
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("AddBulkFilesToBank")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> AddBulkFilesToBank([FromBody] AddManufacturerBulkFiles model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);

                if (model.PhotoIds != null)
                {
                    foreach (var photoId in model.PhotoIds)
                    {
                        ManufacturerPhoto manufacturerPhoto = new ManufacturerPhoto();
                        manufacturerPhoto.ManufacturerId = model.ManufacturerId;
                        manufacturerPhoto.PhotoId = photoId;
                        manufacturerPhoto.CreatedDate = DateTime.UtcNow;
                        manufacturerPhoto.CreatedById = userId;
                        unitOfWork.ManufacturerPhotoRepository.Insert(manufacturerPhoto);
                    }
                }

                if (model.FileIds != null)
                {
                    foreach (var fileId in model.FileIds)
                    {
                        ManufacturerFile manufacturerFile = new ManufacturerFile();
                        manufacturerFile.ManufacturerId = model.ManufacturerId;
                        manufacturerFile.FileId = fileId;
                        manufacturerFile.CreatedDate = DateTime.UtcNow;
                        manufacturerFile.CreatedById = userId;
                        manufacturerFile.WasChanged = true;
                        unitOfWork.ManufacturerFileRepository.Insert(manufacturerFile);
                    }
                }

                await unitOfWork.SaveAsync();

                ClearManufacturerListCache();

                return Ok();
            }
        }

        /// <summary>
        /// Report about missing name of manufacturer in system: Role Customer | Admin
        /// </summary>
        /// <param name="message">The message.</param>
        /// <param name="manufacturerName">Name of the manufacturer.</param>
        /// <param name="sourceUrl">The source URL.</param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("ReportNotExist")]
        [Authorize()]
        public async Task<IActionResult> ReportNotExist(
            string message,
            string manufacturerName = null,
            string sourceUrl = null
        )
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
                var user = unitOfWork.UserRepository.GetAll().FirstOrDefault(a => a.Email == User.Identity.Name);
                var reportMessage = $"{manufacturerName ?? string.Empty} - {message}";
                ReportItem reportItem = new ReportItem();
                reportItem.Message = reportMessage;
                reportItem.CreatedById = user.Id;
                reportItem.CreatedDate = DateTime.UtcNow;

                unitOfWork.ReportItemRepository.Insert(reportItem);

                await unitOfWork.SaveAsync();
                await _slackWebHook.SendBIMRequestMessage(user.Email, user.FirstName, user.LastName, message, sourceUrl, manufacturerName);

                var emailsPath = Path.Combine(_webHostEnvironment.WebRootPath, "Emails");
                await EmailNotificationHelper.Create(emailsPath).SendBIMRequestEmail(user.Email, user.FirstName, user.LastName, message, sourceUrl, manufacturerName);

                var result = new
                {
                    id = reportItem.Id
                };

                ClearManufacturerListCache();

                return Ok(result);
            }
        }

        /// <summary>
        /// Get all report items with not founded names 
        /// </summary>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("ReportList")]
        [Authorize(Roles = DbConstants.AdminRole)]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> ReportList(int offset = 0, int count = 10)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var query = unitOfWork.ReportItemRepository.GetAll();

                var countOfData = await query.CountAsync();

                var list = await query
                    .OrderByDescending(a => a.CreatedDate)
                    .Skip(offset)
                    .Take(count)
                    .Select(a => new
                    {
                        id = a.Id,
                        userEmail = a.CreatedBy.Email,
                        message = a.Message,
                        createdDate = a.CreatedDate
                    })
                    .AsNoTracking()
                    .ToListAsync();

                var result = new
                {
                    count = countOfData,
                    data = list,
                };

                return Ok(result);
            }
        }

        /// <summary>
        /// Delete report by id: Role Admin
        /// </summary>
        [HttpDelete]
        [ActionName("DeleteReport")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> DeleteReport(int reportId)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var reportItem = await unitOfWork.ReportItemRepository.GetByIdAsync(reportId);

                unitOfWork.ReportItemRepository.Delete(reportItem);
                await unitOfWork.SaveAsync();

                CacheHelper.ClearSpecificCache("*/api/Manufacturer/Reportlist*");

                return Ok();
            }
        }

        /// <summary>
        /// Get list of subscribed users to manufacturer(s)
        /// </summary>
        /// <param name="id">Filter by manufacturer id (Not required)</param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("SubscribedUsers")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> SubscribedUsers(int id = -1, int offset = 0, int count = 10)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var query = unitOfWork.UserBIMsmithManufacturerRepository.GetAll();

                if (id != -1)
                {
                    query = query.Where(a => a.ManufacturerId == id);
                }

                var countOfData = await query.CountAsync();

                var items = await query
                            .OrderBy(a => a.Id)
                            .Skip(offset)
                            .Take(count)
                            .Select(a => new
                            {
                                id = a.Id,
                                user = new
                                {
                                    id = a.AddedById,
                                    email = a.AddedBy.Email,
                                    firstName = a.AddedBy.FirstName,
                                    lastName = a.AddedBy.LastName,
                                },
                                manufacturer = new
                                {
                                    id = a.ManufacturerId,
                                    name = a.Manufacturer.Name,
                                    logo = new
                                    {
                                        id = a.Manufacturer.PhotoId,
                                        small = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.SmallImgUrl : null,
                                        big = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.OriginalImgUrl : null
                                    }
                                },
                                addedDate = a.AddedDate
                            })
                            .AsNoTracking()
                            .ToListAsync();

                var result = new
                {
                    count = countOfData,
                    data = items
                };
                return Ok(result);
            }
        }

        /// <summary>
        /// Download CSV report with Subscribed Users listing
        /// </summary>
        /// <param name="id">Filter by manufacturer id (Not required)</param>
        /// <returns></returns>
        [HttpGet]
        [Route("/api/Manufacturer/SubscribedUsers/csv")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> SubscribedUsersCSV(int id = -1)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var query = unitOfWork.UserBIMsmithManufacturerRepository.GetAll();

                if (id != -1)
                {
                    query = query.Where(a => a.ManufacturerId == id);
                }

                var countOfData = await query.CountAsync();

                var items = await query
                            .OrderBy(a => a.Id)
                            .Select(a => new
                            {
                                id = a.Id,
                                userId = a.AddedById,
                                email = a.AddedBy.Email,
                                firstName = a.AddedBy.FirstName,
                                lastName = a.AddedBy.LastName,
                                manufacturerId = a.ManufacturerId,
                                manufacturerName = a.Manufacturer.Name,
                                addedDate = a.AddedDate
                            })
                            .AsNoTracking()
                            .ToListAsync();

                var sb = new StringBuilder();

                //Header
                sb.AppendLine("UserId, Email, FirstName, LastName, ManufacturerId, ManufacturerName, Date");

                //Rows
                foreach (var item in items)
                {
                    sb.AppendLine(string.Format("{0}, {1}, {2}, {3}, {4}, {5}, {6}", item.userId, item.email, item.firstName, item.lastName, item.manufacturerId, item.manufacturerName.Replace(",", ""), item.addedDate));
                }

                Stream fileStream = new MemoryStream(Encoding.UTF8.GetBytes(sb.ToString()));
                return File(fileStream, "text/csv", "Export.csv");
            }
        }

        /// <summary>
        /// Get list of LetsTalk request users to manufacturer(s)
        /// </summary>
        /// <param name="id">Filter by manufacturer id (Not required)</param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("LetsTalkUsers")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> LetsTalkUsers(int id = -1, int offset = 0, int count = 10)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var query = unitOfWork.UserBIMsmithLTManufacturerRepository.GetAll();

                if (id != -1)
                {
                    query = query.Where(a => a.ManufacturerId == id);
                }

                var countOfData = await query.CountAsync();

                var items = await query
                            .AsSplitQuery()
                            .OrderBy(a => a.Id)
                            .Skip(offset)
                            .Take(count)
                            .Select(a => new
                            {
                                id = a.Id,
                                user = new
                                {
                                    id = a.AddedById,
                                    email = a.AddedBy.Email,
                                    firstName = a.AddedBy.FirstName,
                                    lastName = a.AddedBy.LastName,
                                },
                                manufacturer = new
                                {
                                    id = a.ManufacturerId,
                                    name = a.Manufacturer.Name,
                                    logo = new
                                    {
                                        id = a.Manufacturer.PhotoId,
                                        small = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.SmallImgUrl : null,
                                        big = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.OriginalImgUrl : null
                                    }
                                },
                                product = a.ProductId == null ? null : new
                                {
                                    id = a.Product.Id,
                                    name = a.Product.Name
                                },
                                addedDate = a.AddedDate,
                                addedTimezone = a.Timezone
                            })
                            .ToListAsync();

                var result = new
                {
                    count = countOfData,
                    data = items
                };
                return Ok(result);
            }
        }

        /// <summary>
        /// Download CSV report with Lets Talk Users listing
        /// </summary>
        /// <param name="id">Filter by manufacturer id (Not required)</param>
        /// <returns></returns>
        [HttpGet]
        [Route("/api/Manufacturer/LetsTalkUsers/csv")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> LetsTalkUsersCSV(int id = -1)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var query = unitOfWork.UserBIMsmithLTManufacturerRepository.GetAll();

                if (id != -1)
                {
                    query = query.Where(a => a.ManufacturerId == id);
                }

                var countOfData = await query.CountAsync();

                var items = await query
                            .OrderBy(a => a.Id)
                            .Select(a => new
                            {
                                userId = a.AddedById,
                                email = a.AddedBy.Email,
                                firstName = a.AddedBy.FirstName,
                                lastName = a.AddedBy.LastName,
                                manufacturerId = a.ManufacturerId,
                                manufacturerName = a.Manufacturer.Name,
                                addedDate = a.AddedDate,
                                addedTimezone = a.Timezone
                            })
                            .AsNoTracking()
                            .ToListAsync();

                var sb = new StringBuilder();

                //Header
                sb.AppendLine("UserId, Email, FirstName, LastName, ManufacturerId, ManufacturerName, Date");

                //Rows
                foreach (var item in items)
                {
                    sb.AppendLine(string.Format("{0}, {1}, {2}, {3}, {4}, {5}, {6}", item.userId, item.email, item.firstName, item.lastName, item.manufacturerId, item.manufacturerName.Replace(",", ""), item.addedDate));
                }

                Stream fileStream = new MemoryStream(Encoding.UTF8.GetBytes(sb.ToString()));
                return File(fileStream, "text/csv", "Export.csv");
            }
        }

        /// <summary>
        /// Get list of LunchAndLearn request users to manufacturer(s)
        /// </summary>
        /// <param name="id">Filter by manufacturer id (Not required)</param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("LunchAndLearnUsers")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> LunchAndLearnUsers(int id = -1, int offset = 0, int count = 10)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var query = unitOfWork.UserBIMsmithLLManufacturerRepository.GetAll();

                if (id != -1)
                {
                    query = query.Where(a => a.ManufacturerId == id);
                }

                var countOfData = await query.CountAsync();

                var items = await query
                            .OrderBy(a => a.Id)
                            .Skip(offset)
                            .Take(count)
                            .Select(a => new
                            {
                                id = a.Id,
                                user = new
                                {
                                    id = a.AddedById,
                                    email = a.AddedBy.Email,
                                    firstName = a.AddedBy.FirstName,
                                    lastName = a.AddedBy.LastName,
                                },
                                manufacturer = new
                                {
                                    id = a.ManufacturerId,
                                    name = a.Manufacturer.Name,
                                    logo = new
                                    {
                                        id = a.Manufacturer.PhotoId,
                                        small = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.SmallImgUrl : null,
                                        big = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.OriginalImgUrl : null
                                    }
                                },
                                addedDate = a.AddedDate
                            })
                            .AsNoTracking()
                            .ToListAsync();

                var result = new
                {
                    count = countOfData,
                    data = items
                };
                return Ok(result);
            }
        }

        /// <summary>
        /// Get list of BIM requests for manufacturer(s)
        /// </summary>
        /// <param name="id">Filter by manufacturer id (Not required)</param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("BIMRequests")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> BIMRequests(int id = -1, int offset = 0, int count = 10)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var query = unitOfWork.UserBIMsmithBIMManufacturerRepository.GetAll();

                if (id != -1)
                {
                    query = query.Where(a => a.ManufacturerId == id);
                }

                var countOfData = await query.CountAsync();

                var items = await query
                            .OrderBy(a => a.Id)
                            .Skip(offset)
                            .Take(count)
                            .Select(a => new
                            {
                                id = a.Id,
                                message = a.Message,
                                productLink = a.ProductLink,
                                productName = a.ProductName,
                                user = new
                                {
                                    id = a.AddedById,
                                    email = a.AddedBy.Email,
                                    firstName = a.AddedBy.FirstName,
                                    lastName = a.AddedBy.LastName,
                                },
                                manufacturer = new
                                {
                                    id = a.ManufacturerId,
                                    name = a.Manufacturer.Name,
                                    logo = new
                                    {
                                        id = a.Manufacturer.PhotoId,
                                        small = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.SmallImgUrl : null,
                                        big = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.OriginalImgUrl : null
                                    }
                                },
                                addedDate = a.AddedDate
                            })
                            .AsNoTracking()
                            .ToListAsync();

                var result = new
                {
                    count = countOfData,
                    data = items
                };
                return Ok(result);
            }
        }

        /// <summary>
        /// Delete LuchAndLearn User Request
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [ActionName("DeleteLuchAndLearnUserRequest")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> DeleteLuchAndLearnUserRequest(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);

                var llManufacturer = await unitOfWork.UserBIMsmithLLManufacturerRepository.GetAll()
                                                     .FirstOrDefaultAsync(a => a.Id == id);

                if (llManufacturer == null)
                {
                    return BadRequest("There is no Lunch And Learn with such ID.");
                }
                else
                {
                    unitOfWork.UserBIMsmithLLManufacturerRepository.Delete(llManufacturer);
                    await unitOfWork.SaveAsync();

                    return Ok();
                }
            }
        }

        /// <summary>
        /// Delete LetsTalk User Request
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [ActionName("DeleteLetsTalkUserRequest")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> DeleteLetsTalkUserRequest(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);

                var ltManufacturer = await unitOfWork.UserBIMsmithLTManufacturerRepository.GetAll()
                                                     .FirstOrDefaultAsync(a => a.Id == id);

                if (ltManufacturer == null)
                {
                    return BadRequest("There is no Lets Talk request with such ID.");
                }
                else
                {
                    unitOfWork.UserBIMsmithLTManufacturerRepository.Delete(ltManufacturer);
                    await unitOfWork.SaveAsync();

                    return Ok();
                }
            }
        }

        /// <summary>
        /// Delete Subscribe User Request
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [ActionName("DeleteSubscribeUserRequest")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> DeleteSubscribeUserRequest(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);

                var sudscribeRequest = await unitOfWork.UserBIMsmithManufacturerRepository.GetAll()
                                                       .FirstOrDefaultAsync(a => a.Id == id);

                if (sudscribeRequest == null)
                {
                    return BadRequest("There is no Subscribe request with such ID.");
                }
                else
                {
                    unitOfWork.UserBIMsmithManufacturerRepository.Delete(sudscribeRequest);
                    await unitOfWork.SaveAsync();

                    return Ok();
                }
            }
        }

        /// <summary>
        /// Delete BIM Request
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [ActionName("DeleteBIMRequest")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> DeleteBIMRequest(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);

                var bimManufacturer = await unitOfWork.UserBIMsmithBIMManufacturerRepository.GetAll()
                                                      .FirstOrDefaultAsync(a => a.Id == id);

                if (bimManufacturer == null)
                {
                    return BadRequest("There is no BIM request with such ID.");
                }
                else
                {
                    unitOfWork.UserBIMsmithBIMManufacturerRepository.Delete(bimManufacturer);
                    await unitOfWork.SaveAsync();

                    return Ok();
                }
            }
        }

        /// <summary>
        /// Download CSV report with LunchAndLearn Users listing
        /// </summary>
        /// <param name="id">Filter by manufacturer id (Not required)</param>
        /// <returns></returns>
        [HttpGet]
        [Route("/api/Manufacturer/LunchAndLearnUsers/csv")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> LunchAndLearnUsersCSV(int id = -1)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var query = unitOfWork.UserBIMsmithLLManufacturerRepository.GetAll();

                if (id != -1)
                {
                    query = query.Where(a => a.ManufacturerId == id);
                }

                var countOfData = await query.CountAsync();

                var items = await query
                            .OrderBy(a => a.Id)
                            .Select(a => new
                            {
                                userId = a.AddedById,
                                email = a.AddedBy.Email,
                                firstName = a.AddedBy.FirstName,
                                lastName = a.AddedBy.LastName,
                                manufacturerId = a.ManufacturerId,
                                manufacturerName = a.Manufacturer.Name,
                                addedDate = a.AddedDate
                            })
                            .AsNoTracking()
                            .ToListAsync();

                var sb = new StringBuilder();

                //Header
                sb.AppendLine("UserId, Email, FirstName, LastName, ManufacturerId, ManufacturerName, Date");

                //Rows
                foreach (var item in items)
                {
                    sb.AppendLine(string.Format("{0}, {1}, {2}, {3}, {4}, {5}, {6}", item.userId, item.email, item.firstName, item.lastName, item.manufacturerId, item.manufacturerName.Replace(",", ""), item.addedDate));
                }

                Stream fileStream = new MemoryStream(Encoding.UTF8.GetBytes(sb.ToString()));
                return File(fileStream, "text/csv", "Export.csv");
            }
        }

        /// <summary>
        /// Get MailChimp list info
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> MailChimpList()
        {
            var mailChimpManager = new MailChimp.Net.MailChimpManager(ConfigurationHelper.GetValue("MailChimpAPIKey"));

            var mailChimpListCollection = await mailChimpManager.Lists.GetAllAsync();

            var list = mailChimpListCollection.Select(a => new
            {
                id = a.Id,
                name = a.Name,
                visibility = a.Visibility
            });

            return Ok(list);
        }

        /// <summary>
        /// Send subscribers to MailChimp list
        /// </summary>
        /// <param name="manufacturerId"></param>
        /// <param name="listId"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> AddSubscribersToMailChimpList(int manufacturerId, string listId)
        {
            var mailChimpManager = new MailChimp.Net.MailChimpManager(ConfigurationHelper.GetValue("MailChimpAPIKey"));

            var members = await mailChimpManager.Members.GetAllAsync(listId);

            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var query = unitOfWork.UserBIMsmithManufacturerRepository.GetAll().Where(a => a.ManufacturerId == manufacturerId);

                var subscribers = await query
                            .OrderBy(a => a.Id)
                            .Select(a => new
                            {
                                a = a.Id,
                                userId = a.AddedById,
                                email = a.AddedBy.Email,
                                firstName = a.AddedBy.FirstName,
                                lastName = a.AddedBy.LastName
                            })
                            .AsNoTracking()
                            .ToListAsync();

                foreach (var item in subscribers)
                {
                    try
                    {
                        var member = new MailChimp.Net.Models.Member { EmailAddress = item.email, StatusIfNew = MailChimp.Net.Models.Status.Subscribed };
                        member.MergeFields.Add("FNAME", item.firstName);
                        member.MergeFields.Add("LNAME", item.lastName);

                        var res = await mailChimpManager.Members.AddOrUpdateAsync(listId, member);
                    }
                    catch (Exception e)
                    {
                        Log.Error(e.GetAllMessages(), e);
                    }
                }

                return Ok();
            }
        }

        /// <summary>
        /// Analyticses the manufacturer information.
        /// </summary>
        /// <param name="secret">The secret.</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("ManufacturerInfo")]
        public async Task<IActionResult> AnalyticsManufacturerInfo(string secret)
        {
            if (secret != "Analytics4Ever")
                return Unauthorized();

            using (var unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var data = await unitOfWork.ManufacturerRepository.GetAll()
                    .Select(x => new
                    {
                        Id = x.Id,
                        Name = x.Name
                    })
                    .AsNoTracking()
                    .ToListAsync();

                return Ok(data);
            }
        }

        [HttpPost]
        [ActionName("SetConvertUnitKeyStat")]
        public async Task<IActionResult> SetConvertUnitKeyStat(int manufacturerId)
        {
            int correctedCount = 0;
            using (var unitOfWork = UnitOfWork.Create())
            {
                var manufacturer = await unitOfWork.ManufacturerRepository.GetAll().FirstOrDefaultAsync(x => x.Id == manufacturerId);
                if (manufacturer == null)
                    return NotFound("Manufacturer was not found");
                var products = manufacturer.Products.ToList();

                unitOfWork.BeginTransaction();

                foreach (var product in products)
                {
                    var productStats = product.ProductStats.ToList();
                    if (!productStats.Any())
                        continue;
                    foreach (var productStat in productStats)
                    {
                        if (productStat.KeyStatType == KeyStatType.MultivalueNumeric)
                        {
                            var multyKeystatValues = productStat.KeyStatValueList.ToList();

                            foreach (var multyKeystat in multyKeystatValues)
                            {
                                if (!multyKeystat.ConvertKeyStatUnitId.HasValue)
                                {
                                    var mUnitId = multyKeystat?.KeyStatUnit?.FromUnitRelations.FirstOrDefault(x => x.IsDefault)?.ToUnitId;
                                    if (mUnitId.HasValue)
                                    {
                                        multyKeystat.ConvertKeyStatUnitId = mUnitId;
                                        unitOfWork.KeyStatValueListRepository.Edit(multyKeystat);

                                        try
                                        {
                                            await unitOfWork.SaveAsync();
                                            correctedCount++;
                                        }
                                        catch (Exception ex)
                                        {
                                            unitOfWork.RollbackTransaction();
                                            return BadRequest(ex.GetAllMessages() + ex.StackTrace);
                                        }
                                    }
                                }
                            }
                        }

                        if (productStat.ConvertKeyStatUnitId.HasValue)
                            continue;

                        var unitId = productStat.KeyStatUnit?.FromUnitRelations.FirstOrDefault(x => x.IsDefault)?.ToUnitId;
                        if (!unitId.HasValue)
                            continue;

                        productStat.ConvertKeyStatUnitId = unitId;
                        unitOfWork.ProductStatsRepository.Edit(productStat);

                        try
                        {
                            await unitOfWork.SaveAsync();
                            correctedCount++;
                        }
                        catch (Exception ex)
                        {
                            unitOfWork.RollbackTransaction();
                            return BadRequest(ex.GetAllMessages());
                        }
                    }
                }
                unitOfWork.CommitTransaction();
                return Ok(correctedCount);
            }
        }

        /// <summary>
        /// Change Published flag for Manufacturer: Role-ADMIN
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("ChangePublished")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> ChangePublished([FromBody] PublishedManufacturerModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                Manufacturer manufacturer = await unitOfWork.ManufacturerRepository.GetByIdAsync(model.Id);

                manufacturer.Published = model.IsPublished.Value;

                unitOfWork.ManufacturerRepository.Edit(manufacturer);

                await unitOfWork.SaveAsync();

                ClearManufacturerListCache();

                return Ok();
            }
        }

        /// <summary>
        /// Get market manufacturer ids by forge IDs 
        /// </summary>
        /// <param name="forgeIds">Forge's IDs separated by comma</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("RetrieveIdsForForge")]
        public async Task<IActionResult> RetrieveIdsForForge(string forgeIds)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var forgeIdsList = forgeIds.Split(',').ToList();

                var marketIds = await unitOfWork.ManufacturerRepository.GetAll().Where(a => forgeIdsList.Contains(a.ForgeManufacturerId))
                    .Select(a =>
                     new
                     {
                         forgeId = a.ForgeManufacturerId,
                         marketId = a.Id
                     })
                    .AsNoTracking()
                    .ToListAsync();

                List<dynamic> result = new List<dynamic>();

                foreach (var forgeId in forgeIdsList)
                {
                    var marketId = marketIds.Where(a => a.forgeId == forgeId).Select(a => a.marketId).DefaultIfEmpty(-1).First();

                    result.Add(new
                    {
                        forgeId = forgeId,
                        marketId = marketId
                    });
                }

                return Ok(result);
            }
        }

        /// <summary>
        /// Updates the thumbnail.
        /// </summary>
        /// <param name="id">The manufacturer identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("UpdateThumbnail")]
        public async Task<IActionResult> UpdateThumbnail(int id)
        {
            await _manufacturerService.UpdateThumbnailAsync(id);
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            await _productService.UpdateMongoProductsAsync(id, unitOfWork);

            CacheHelper.ClearSpecificCache("*/api/Product/List*");
            CacheHelper.ClearSpecificCache("*/api/Product/AdminList*");
            ClearManufacturerListCache();

            return Ok();
        }

        /// <summary>
        /// Gets the manufacturer product files.
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("GetProductFiles")]
        public async Task<IActionResult> GetProductFiles(int manufacturerId)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var productFiles = await unitOfWork.ManufacturerFileRepository.GetAll()
                    .Where(x => x.ManufacturerId == manufacturerId && x.IsAttachment)
                    .Select(x => new
                    {
                        id = x.FileId,
                        customFileId = x.CustomFileId,
                        title = x.File.Title,
                        fileName = x.File.FileName,
                        fileSize = x.File.FileSize,
                        updatesCount = x.File.UpdatesCount,
                        fileSyncStatusCode = x.File.SyncStatusCode,
                        fileSyncUrl = x.File.SyncUrl,
                        mimeType = x.File.MediaType,
                        url = x.File.Url,
                        preview = x.File.PreviewUrl,
                        weight = x.Weight
                    })
                    .OrderByDescending(x => x.weight)
                    .AsNoTracking()
                    .ToListAsync();

                return Ok(productFiles);
            }
        }

        /// <summary>
        /// Gets the manufacturer products info.
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> GetManufacturerProductsInfo(ManufacturerProductsInfoViewModel model)
        {
            dynamic result;
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var query = unitOfWork.ManufacturerRepository.GetAll().Where(a => model.ManufacturerIds.Contains(a.Id))
                    .Select(x => new
                    {
                        ManufacturerId = x.Id,
                        LiveProductsCount = x.Products.Where(p => p.Published).Count()
                    });

                result = await query.AsNoTracking().ToListAsync();
            }
            return Ok(result);
        }

        /// <summary>
        /// Get detailed information about model files that were uploaded to manufacturer
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("GetAudit")]
        public async Task<IActionResult> GetAudit(int manufacturerId)
        {
            var excelPath = ExcelProductsProvider.GetProductFilesAudit(manufacturerId, Path.GetTempPath());
            return PhysicalFile(excelPath, "text/csv", $"Project Files Audit.xlsx");
        }

        /// <summary>
        /// Creates backup for specified manufacturer
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier</param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        public async Task<IActionResult> CreateManufacturerBackup(int manufacturerId)
        {
            var result = await _manufacturerBackupService.CreateManufacturerBackupAsync(manufacturerId);
            return Ok(result);
        }

        /// <summary>
        /// Lists manufacturer backups for specified manufacturer
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier</param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        public async Task<IActionResult> GetManufacturerBackupsList(int manufacturerId)
        {
            var result = await _manufacturerBackupService.GetManufacturerBackupsListAsync(manufacturerId);
            return Ok(result);
        }

        /// <summary>
        /// Restores backup for specified manufacturer
        /// </summary>
        /// <param name="model">The model for backup restore</param>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        public async Task<IActionResult> RestoreManufacturerFromBackup(ManufacturerRestoreBackupViewModel model)
        {
            var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            var result = await _manufacturerBackupRestoreService.RestoreManufacturerFromBackupAsync(model, userId);
            return Ok(result);
        }

        /// <summary>
        /// Get detailed information about Manufacturer by Swatchbox Id
        /// </summary>
        /// <param name="model">The model</param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        [ActionName("GetBySwatchboxId")]
        public async Task<IActionResult> GetBySwatchboxId(MarketManufacturerInfoRequestModel model)
        {
            string swatchboxKey = ConfigurationHelper.GetValue("SwatchboxMarketAccessKey");
            var result = _manufacturerService.GetBySwatchboxId(model, swatchboxKey);
            return Ok(result);
        }

        /// <summary>
        /// Lists style files content for manufacturer specified
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier</param>
        /// <param name="type">The file type</param>
        [HttpGet]
        public async Task<IActionResult> GetManufacturerStyleFilesContent(int manufacturerId, StyleFileType type)
        {
            var result = await _styleFileService.GetManufacturerStyleFilesContentAsync(manufacturerId, type);
            return Ok(result);
        }

        /// <summary>
        /// Lists style files types
        /// </summary>
        [HttpGet]
        public IActionResult GetManufacturerStyleFileTypes()
        {
            var result = _styleFileService.GetManufacturerStyleFileTypes();
            return Ok(result);
        }

        [HttpGet]
        public async Task<IActionResult> GetIdsByParentId(string token, int manufacturerId = -1)
        {
            if (token != ConfigurationHelper.GetValue("ApiAccessToken"))
                return BadRequest("API Access token is invalid");

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var ids = new List<int>();

                if (manufacturerId != -1)
                {
                    IQueryable<Manufacturer> query = unitOfWork.ManufacturerRepository.GetAll().Where(x => x.ParentId == manufacturerId);
                    ids = await query.Select(x => x.Id).ToListAsync();
                }

                return Ok(ids);
            }
        }

        //Also used by Analytics
        [HttpGet]
        public async Task<IActionResult> BIMRequestsByManufacturer(string token, int manufacturerId = -1, int offset = 0, int count = 10)
        {
            if (token != ConfigurationHelper.GetValue("ApiAccessToken"))
                return BadRequest("API Access token is invalid");

            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var query = unitOfWork.UserBIMsmithBIMManufacturerRepository.GetAll();

                if (manufacturerId != -1)
                {
                    query = query.Where(a => a.ManufacturerId == manufacturerId);
                }

                var countOfData = await query.CountAsync();

                var items = await query
                            .OrderBy(a => a.Id)
                            .Skip(offset)
                            .Take(count)
                            .Select(a => new
                            {
                                id = a.Id,
                                message = a.Message,
                                productLink = a.ProductLink,
                                productName = a.ProductName,
                                user = new
                                {
                                    id = a.AddedById,
                                    email = a.AddedBy.Email,
                                    firstName = a.AddedBy.FirstName,
                                    lastName = a.AddedBy.LastName,
                                },
                                manufacturer = new
                                {
                                    id = a.ManufacturerId,
                                    name = a.Manufacturer.Name,
                                    logo = new
                                    {
                                        id = a.Manufacturer.PhotoId,
                                        small = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.SmallImgUrl : null,
                                        big = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.OriginalImgUrl : null
                                    }
                                },
                                addedDate = a.AddedDate
                            })
                            .AsNoTracking()
                            .ToListAsync();

                var result = new
                {
                    count = countOfData,
                    data = items
                };
                return Ok(result);
            }
        }

        //Also used by Analytics
        [HttpGet]
        public async Task<IActionResult> LunchAndLearnUsersByManufacturer(string token, int manufacturerId = -1, int offset = 0, int count = 10)
        {
            if (token != ConfigurationHelper.GetValue("ApiAccessToken"))
                return BadRequest("API Access token is invalid");

            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var query = unitOfWork.UserBIMsmithLLManufacturerRepository.GetAll();

                if (manufacturerId != -1)
                {
                    query = query.Where(a => a.ManufacturerId == manufacturerId);
                }

                var countOfData = await query.CountAsync();

                var items = await query
                            .OrderBy(a => a.Id)
                            .Skip(offset)
                            .Take(count)
                            .Select(a => new
                            {
                                id = a.Id,
                                user = new
                                {
                                    id = a.AddedById,
                                    email = a.AddedBy.Email,
                                    firstName = a.AddedBy.FirstName,
                                    lastName = a.AddedBy.LastName,
                                },
                                manufacturer = new
                                {
                                    id = a.ManufacturerId,
                                    name = a.Manufacturer.Name,
                                    logo = new
                                    {
                                        id = a.Manufacturer.PhotoId,
                                        small = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.SmallImgUrl : null,
                                        big = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.OriginalImgUrl : null
                                    }
                                },
                                addedDate = a.AddedDate
                            })
                            .AsNoTracking()
                            .ToListAsync();

                var result = new
                {
                    count = countOfData,
                    data = items
                };
                return Ok(result);
            }
        }

        //Also used by Analytics
        [HttpGet]
        public async Task<IActionResult> SubscribedUsersByManufacturer(string token, int manufacturerId = -1, int offset = 0, int count = 10)
        {
            if (token != ConfigurationHelper.GetValue("ApiAccessToken"))
                return BadRequest("API Access token is invalid");

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var query = unitOfWork.UserBIMsmithManufacturerRepository.GetAll();

                if (manufacturerId != -1)
                {
                    query = query.Where(a => a.ManufacturerId == manufacturerId);
                }

                var countOfData = await query.CountAsync();

                var items = await query
                    .OrderBy(a => a.Id)
                    .Skip(offset)
                    .Take(count)
                    .Select(a => new
                    {
                        id = a.Id,
                        user = new
                        {
                            id = a.AddedById,
                            email = a.AddedBy.Email,
                            firstName = a.AddedBy.FirstName,
                            lastName = a.AddedBy.LastName,
                        },
                        manufacturer = new
                        {
                            id = a.ManufacturerId,
                            name = a.Manufacturer.Name,
                            logo = new
                            {
                                id = a.Manufacturer.PhotoId,
                                small = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.SmallImgUrl : null,
                                big = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.OriginalImgUrl : null
                            }
                        },
                        addedDate = a.AddedDate
                    })
                    .AsNoTracking()
                    .ToListAsync();

                var result = new
                {
                    count = countOfData,
                    data = items
                };
                return Ok(result);
            }
        }

        //Also used by Analytics
        [HttpGet]
        public async Task<IActionResult> GetLetsTalkByManufacturer(string token, int manufacturerId = -1, int offset = 0, int count = 10)
        {
            if (token != ConfigurationHelper.GetValue("ApiAccessToken"))
                return BadRequest("API Access token is invalid");

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var ids = new List<int>();

                if (manufacturerId != -1)
                {
                    var query = unitOfWork.UserBIMsmithLTManufacturerRepository.GetAll();

                    if (manufacturerId != -1)
                    {
                        query = query.Where(a => a.ManufacturerId == manufacturerId);
                    }

                    var countOfData = await query.CountAsync();

                    var items = await query
                        .OrderBy(a => a.Id)
                        .Skip(offset)
                        .Take(count)
                        .Select(a => new
                        {
                            id = a.Id,
                            user = new
                            {
                                id = a.AddedById,
                                email = a.AddedBy.Email,
                                firstName = a.AddedBy.FirstName,
                                lastName = a.AddedBy.LastName,
                            },
                            manufacturer = new
                            {
                                id = a.ManufacturerId,
                                name = a.Manufacturer.Name,
                                logo = new
                                {
                                    id = a.Manufacturer.PhotoId,
                                    small = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.SmallImgUrl : null,
                                    big = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.OriginalImgUrl : null
                                }
                            },
                            product = a.ProductId == null ? null : new
                            {
                                id = a.Product.Id,
                                name = a.Product.Name
                            },
                            addedDate = a.AddedDate,
                            addedTimezone = a.Timezone
                        })
                        .AsNoTracking()
                        .ToListAsync();

                    var result = new
                    {
                        count = countOfData,
                        data = items
                    };

                    return Ok(result);
                }

                return Ok(ids);
            }
        }

        [HttpGet]
        public async Task<IActionResult> VanityUrlList()
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _manufacturerService.VanityUrlListAsync(unitOfWork));
            }
        }

        /// <summary>
        /// Updates products in Mongo
        /// </summary>
        /// <param name="id">The manufacturer identifier.</param>
        /// <returns></returns>
#if (!DEBUG)
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        [ActionName("UpdateProducts")]
        public async Task<IActionResult> UpdateProducts(int manufacturerId)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            await _productService.UpdateMongoProductsAsync(manufacturerId, unitOfWork);

            return Ok();
        }

#if !DEBUG
        [Authorize]
#endif
        [HttpGet]
        [ActionName("FollowList")]
        public async Task<IActionResult> FollowList()
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            return Ok(await _manufacturerService.FollowListAsync(userId, unitOfWork));
        }

        /// <summary>
        /// Updates manufacturer weight
        /// </summary>
        /// <param name="model">The model</param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        [ActionName("UpdateWeight")]
        public async Task<IActionResult> UpdateWeight(ManufacturerWeightDto model)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);

            OperationResultDto result = await _manufacturerService.UpdateWeightAsync(model, userId, unitOfWork);

            CacheHelper.ClearSpecificCache($"*/api/Manufacturer/Get*");
            ClearManufacturerListCache();

            return Ok(result);
        }

        /// <summary>
        /// Gets list of popular brands
        /// </summary>
        /// <param name="regionId">The region identifier</param>
        /// <param name="stateId">The state identifier</param>
        /// <param name="categoryId">The category identifier</param>
        /// <param name="categoryVanityUrl">The category vanity url</param>
        /// <param name="externalMasterformatId">The external masterformat identifier</param>
        /// <param name="masterformatCode">The masterformat code</param>
        /// <param name="masterformatName">The masterformat name</param>
        /// <param name="offset">The offset to skip</param>
        /// <param name="count">The count to take</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("PopularList")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> PopularList(
            string regionId = null,
            string stateId = null,
            int? categoryId = null,
            string categoryVanityUrl = null,
            int? externalMasterformatId = null,
            string masterformatCode = null,
            string masterformatName = null,
            int offset = 0,
            int count = 10)
        {
            if (regionId == "null")
                regionId = null;

            if (stateId == "null")
                stateId = null;

            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _manufacturerService.PopularListAsync(unitOfWork, regionId, stateId, categoryId, categoryVanityUrl, externalMasterformatId, masterformatCode, masterformatName, offset, count));
        }

        /// <summary>
        /// Sends pricing request
        /// </summary>
        /// <param name="model">The model</param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("RequestPricing")]
        [Authorize]
        public async Task<IActionResult> RequestPricing(RequestPricingDto model)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            string emailsPath = Path.Combine(_webHostEnvironment.WebRootPath, "Emails");
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            string userEmail = AuthHelper.GetUserInfo(Request, ClaimTypes.Email);
            string ip = HttpContext?.Connection?.RemoteIpAddress?.ToString();
            string referer = Request.GetTypedHeaders()?.Referer?.AbsoluteUri?.ToString();
            return Ok(await _manufacturerService.RequestPricingAsync(model, emailsPath, userId, userEmail, ip, referer, _userService, _analyticsService, _slackWebHook, unitOfWork));
        }

        /// <summary>
        /// Lists the request pricing users
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier</param>
        /// <param name="offset">The offset to skip</param>
        /// <param name="count">The count to take</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("RequestPricingUserList")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> RequestPricingUserList(
            int manufacturerId,
            int offset = 0,
            int count = 10)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _manufacturerService.RequestPricingUserListAsync(manufacturerId, unitOfWork, offset, count));
        }

        /// <summary>
        /// Deletes Request Pricing User
        /// </summary>
        /// <param name="id">The identifier</param>
        /// <returns></returns>
        [HttpDelete]
        [ActionName("DeleteRequestPricingUser")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> DeleteRequestPricingUser(int id)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _manufacturerService.DeleteRequestPricingUserAsync(id, unitOfWork));
        }

        /// <summary>
        /// Exports all request pricing users for manufacturer specified
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier</param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        [ActionName("RequestPricingUserExcelExport")]
        public async Task<IActionResult> RequestPricingUserExcelExport(int manufacturerId)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                string excelPath = await _manufacturerExcelService.GetRequestPricingUserExcelAsync(manufacturerId, unitOfWork);
                return PhysicalFile(excelPath, "text/csv", $"Pricing Requests.xlsx");
            }
        }

        /// <summary>
        /// Lists manufacturers from Revit microsites
        /// </summary>
        /// <param name="query">The search query</param>
        /// <param name="regionId">The region identifier</param>
        /// <param name="stateId">The state identifier</param>
        /// <<returns></returns>   >     
        [HttpGet]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        [Produces<PaginationListDto<ManufacturerRevitMicrositesPublicListDto>>]
        public async Task<IActionResult> RevitMicrositesPublicList(
            string query = null,
            string regionId = null,
            string stateId = null)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _manufacturerService.RevitMicrositesPublicListAsync(unitOfWork, query, regionId, stateId));
        }

        #region private methods
        private void ClearManufacturerListCache()
        {
            CacheHelper.ClearSpecificCache("*/api/Manufacturer/List*");
            CacheHelper.ClearSpecificCache("*/api/Manufacturer/PopularList*");
            CacheHelper.ClearSpecificCache("*/api/Manufacturer/RevitMicrositePublicList*");
        }
        #endregion
    }
}