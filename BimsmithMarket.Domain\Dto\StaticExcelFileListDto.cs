﻿using BIMsmithMarket.Domain.Enums;
using System;
using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Dto
{
    public class StaticExcelFileListDto
    {
        public int Id { get; set; }

        public DateTime CreatedDate { get; set; }

        public DateTime? ModifiedDate { get; set; }

        public ExcelProcessingStatus Status { get; set; }

        public List<string> ValidationErrors { get; set; }

        public int TotalProductsCount { get; set; }

        public int SucceedProdutcsCount { get; set; }

        public int ErrorProductsCount { get; set; }

        public int CancelledProductsCount { get; set; }

        public int? ManufacturerId { get; set; }

        public List<StaticExcelProductErrorListDto> StaticExcelProductErrors { get; set; }
    }

    public class StaticExcelProductErrorListDto
    {
        public int RowNumber { get; set; }

        public List<string> Errors { get; set; }
    }
}