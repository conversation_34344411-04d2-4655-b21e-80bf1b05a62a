﻿using System;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.DBModels
{
    public class Company
    {
        [Key]
        public int Id { get; set; }

        public string CompanyName { get; set; }

        public string EmailDomain { get; set; }

        public bool MatchCompanyUsers { get; set; }

        public DateTime CreatedDate { get; set; }

        public Company()
        {
            CreatedDate = DateTime.UtcNow;
        }
    }
}