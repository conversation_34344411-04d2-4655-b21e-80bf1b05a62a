﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Models
{
    public class PromoteManufacturersModel
    {
        public ICollection<ManufacturerToPromote> ManufacturersToPromote { get; set; }
    }

    public class ManufacturerToPromote
    {
        [Required]
        public int Id { get; set; }

        public string Name { get; set; }

        [Required]
        public int Order { get; set; }
    }
}