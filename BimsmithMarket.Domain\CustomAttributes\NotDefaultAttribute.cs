﻿using System;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.CustomAttributes
{
    public class NotDefaultAttribute : ValidationAttribute
    {
        public const string DefaultErrorMessage = "The {0} field must not have the default value";

        public NotDefaultAttribute() : base(DefaultErrorMessage)
        {

        }

        public override bool IsValid(object value)
        {
            if (value is null)
                return true;

            Type type = value.GetType();

            if (type.IsValueType)
            {
                object defaultValue = Activator.CreateInstance(type);
                return !value.Equals(defaultValue);
            }

            return true;
        }
    }
}