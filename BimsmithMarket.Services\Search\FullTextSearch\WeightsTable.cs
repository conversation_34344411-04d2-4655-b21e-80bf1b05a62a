﻿namespace BIMsmithMarket.Services.Search.FullTextSearch
{
    public enum TokenMatchType : int
    {
        None = 0,
        Exact = 1,
        Word = 2,
        Prefix = 3,
        Free = 4
    }

    public class WeightsTable
    {
        // score tables: { unused, Exact Match, Word Match, Prefix Match, Freetext Match }
        public static readonly string[] Matches = { "n/a", "Exact", "Word", "Prefix", "Free" };

        public float[] PrdName = { 0, 0.2f, 0.1f, 0.2f, 0 }; // 1pt for match on name
        public float[] PrdModelNum = { 0, 20, 10, 2, 0 }; // match on suspected  model number in product name
        public float[] PrdDesc = { 0, 0, 0.1f, 0.1f, 0 }; // almost ignore product description
        public float[] PrdKwrd = { 0, 15, 2, 2, 0 }; // product keyword
        public float[] CatName = { 0, 10, 9, 1, 0 }; // category name - strong match
        public float[] CatKwrd = { 0, 9, 4, 2, 0 }; // category keywords
        public float[] ParCatName = { 0, 8, 7, 1, 0 }; // parent category name
        public float[] ParCatKwrd = { 0, 7, 2, 0.5f, 0 }; // parent keywords
        public float[] PrdLineName = { 0, 17, 1, 1, 0 }; // product line name
        public float[] MfnName = { 0, 16, 5, 1, 0 }; // manufacturer name
        public float[] MfnKwrd = { 0, 15, 5, 1, 0 }; // manufacturer keyword
        public float[] PrdStat = { 0, 11, 5, 2, 0 }; // high hit on stat
        public float[] Omncls = { 0, 10, 3, 0.1f, 0 }; // high hit on omnicls
        public float[] Mstfmt = { 0, 10, 3, 0.1f, 0 }; // high hit on mstfmt
        public float[] PrdCert = { 0, 0.2f, 0.1f, 0.2f, 0 }; // certificate name hit
        public float[] MulCatName = { 0, 5, 1.5f, 0.1f, 0 }; // multiple categories (names)
        public float[] MulCatKwrd = { 0, 5, 1.5f, 0.1f, 0 }; // multiple categories (keywords)        

        public float WeightScore = 0.5f; // maximum impact of product weight
        public float UnitMatchScore = 20.0f;

        public static readonly float ManufacturerBeelineThreshold = 1.0f;
        public static readonly float CategoryBeelineThreshold = 1.0f;
        public static readonly float DetailBeelineThreshold = 1.0f;
    }
}