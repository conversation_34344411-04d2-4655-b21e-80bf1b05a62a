﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    /// <seealso cref="BIMsmithMarket.Api.Controllers.BaseApiController" />
    [Route("api/[controller]/[action]")]
    public class RevitPluginController : BaseApiController
    {
        private readonly IRevitPluginService _revitPluginService;
        private readonly ICategoryService _categoryService;

        public RevitPluginController(
            IRevitPluginService revitPluginService, 
            ICategoryService categoryService)
        {
            _revitPluginService = revitPluginService;
            _categoryService = categoryService;
        }

        /// <summary>
        /// Gets the plugin items.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("GetPluginList")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> GetPluginItems()
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _revitPluginService.GetPluginItemsAsync(unitOfWork, _categoryService));
            }
        }

        [HttpGet]
        [ActionName("GetPromotedManufacturers")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> GetPromotedManufacturers(int count = 10)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _revitPluginService.GetPromotedManufacturers(unitOfWork, count));
        }

        [HttpPost]
        [Authorize(Roles = DbConstants.AdminRole)]
        [ActionName("PromoteManufacturers")]
        public async Task<IActionResult> PromoteManufacturers(PromoteManufacturersModel manufacturers)
        {
            if (manufacturers != null && manufacturers.ManufacturersToPromote.Count < 11 && ModelState.IsValid)
            {
                try
                {
                    using (IUnitOfWork unitOfWork = UnitOfWork.Create())
                    {
                        return Ok(await _revitPluginService.PromoteManufacturersAsync(unitOfWork, manufacturers));
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex.Message, ex);
                    return BadRequest(ex.Message + " " + ex.StackTrace);
                }
            }
            else
            {
                return BadRequest(ModelState);
            }
        }
    }
}