﻿using System.Collections.Generic;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface ICacheService
    {
        T Get<T>(string key);

        T Set<T>(string key, T value, int secondsToExpire);

        bool Contains(string key);

        void Remove(string key);

        void RemoveAll();

        void RemoveAllByPattern(string pattern);

        ICollection<string> GetKeys(string pattern = "*");
    }
}