﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IStyleFileService
    {
        Task<List<string>> GetManufacturerStyleFilesContentAsync(int manufacturerId, StyleFileType type);

        ICollection<StyleFileTypeViewModel> GetManufacturerStyleFileTypes();
    }
}