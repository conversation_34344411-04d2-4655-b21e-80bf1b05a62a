﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;

using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Services
{
    public class DetailScaleService : IDetailScaleService
    {
        public async Task<dynamic> AddAsync(AddDetailScaleViewModel model, string userId)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var detailScale = new DetailScale();
                detailScale.CreatedById = userId;
                detailScale.CreatedDate = DateTime.UtcNow;
                detailScale.Name = model.Name;
                unitOfWork.DetailScaleRepository.Insert(detailScale);

                await unitOfWork.SaveAsync();

                CacheHelper.ClearSpecificCache("*/api/DetailScale/List*");

                return await GetAsync(detailScale.Id);
            }
        }

        public async Task<dynamic> EditAsync(EditDetailScaleViewModel model, string userId)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var detailScale = await unitOfWork.DetailScaleRepository.GetByIdAsync(model.Id);
                if (detailScale == null) throw new Exception("Detail Scale Not Found");
                detailScale.ModifiedById = userId;
                detailScale.ModifiedDate = DateTime.UtcNow;
                detailScale.Name = model.Name;
                unitOfWork.DetailScaleRepository.Edit(detailScale);

                await unitOfWork.SaveAsync();
                return await GetAsync(detailScale.Id);
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                unitOfWork.BeginTransaction();
                var detailScale = await unitOfWork.DetailScaleRepository.GetByIdAsync(id);
                if (detailScale == null) throw new Exception("Detail Scale Not Found");

                var details = await unitOfWork.DetailRepository.GetAll().Where(x => x.DetailScaleId == id).ToListAsync();
                foreach (var detail in details)
                {
                    detail.DetailScaleId = null;
                    unitOfWork.DetailRepository.Edit(detail);
                }

                unitOfWork.DetailScaleRepository.Delete(detailScale);

                await unitOfWork.SaveAsync();
                unitOfWork.CommitTransaction();

                CacheHelper.ClearSpecificCache("*/api/DetailScale/List*");

                return true;
            }
        }

        public async Task<dynamic> GetAsync(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var detailScale = await unitOfWork.DetailScaleRepository.GetByIdAsync(id);

                if (detailScale == null) throw new Exception("Detail Scale not found");

                return new
                {
                    id = detailScale.Id,
                    createdDate = detailScale.CreatedDate,
                    modifiedDate = detailScale.ModifiedDate,
                    name = detailScale.Name
                };
            }
        }

        public async Task<dynamic> ListAsync(string query, int count, int offset)
        {
            using (var unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var detailScaleQuery = unitOfWork.DetailScaleRepository.GetAll();

                if (!string.IsNullOrWhiteSpace(query))
                    detailScaleQuery = detailScaleQuery.Where(x => x.Name.ToUpper().Contains(query.ToUpper()));

                var totatCount = await detailScaleQuery.CountAsync();

                var result = await detailScaleQuery.OrderBy(x => x.Id).Select(x => new
                {
                    id = x.Id,
                    name = x.Name
                })
                .Skip(offset)
                .Take(count)
                .AsNoTracking()
                .ToListAsync();

                return new
                {
                    totalCount = totatCount,
                    data = result
                };
            }
        }
    }
}