﻿using BIMsmithMarket.Domain.Enums;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class HealthDashboardAccess : BaseEntity
    {
        public string UserId { get; set; }

        public HealthDashboardAccessType Type { get; set; }

        public int EntityId { get; set; }

        [ForeignKey("UserId")]
        public virtual ApplicationUser User { get; set; }
    }
}