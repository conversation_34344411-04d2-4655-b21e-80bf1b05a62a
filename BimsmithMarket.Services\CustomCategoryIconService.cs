﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class CustomCategoryIconService : ICustomCategoryIconService
    {
        public async Task<AdminListCustomCategoryIconDto> AddAsync(AddCustomCategoryIconDto model, string userId, IUnitOfWork unitOfWork)
        {
            if (!await unitOfWork.ManufacturerRepository.GetAll()
                .AnyAsync(x => x.Id == model.ManufacturerId))
                throw new InvalidInputException("Manufacturer does not exist");

            if (!await unitOfWork.CategoryRepository.GetAll()
                .AnyAsync(x => x.Id == model.CategoryId))
                throw new InvalidInputException("Category does not exist");

            if (await unitOfWork.CustomCategoryIconRepository.GetAll()
                .AnyAsync(x => x.CategoryId == model.CategoryId && x.ManufacturerId == model.ManufacturerId))
                throw new InvalidInputException("Category already has custom icon for this manufacturer");

            CustomCategoryIcon customCategoryIcon = model.Adapt<CustomCategoryIcon>();
            customCategoryIcon.CreatedById = userId;

            unitOfWork.CustomCategoryIconRepository.Insert(customCategoryIcon);
            await unitOfWork.SaveAsync();

            return customCategoryIcon.Adapt<AdminListCustomCategoryIconDto>();
        }

        public async Task<AdminListCustomCategoryIconDto> EditAsync(EditCustomCategoryIconDto model, string userId, IUnitOfWork unitOfWork)
        {
            CustomCategoryIcon customCategoryIcon = await unitOfWork.CustomCategoryIconRepository.GetByIdAsync(model.Id);

            if (customCategoryIcon == null)
                throw new InvalidInputException("Custom category icon does not exist");

            model.Adapt(customCategoryIcon);
            customCategoryIcon.ModifiedById = userId;
            customCategoryIcon.ModifiedDate = DateTime.UtcNow;
            unitOfWork.CustomCategoryIconRepository.Edit(customCategoryIcon);
            await unitOfWork.SaveAsync();

            return customCategoryIcon.Adapt<AdminListCustomCategoryIconDto>();
        }

        public async Task<OperationResultDto> DeleteAsync(int id, IUnitOfWork unitOfWork)
        {
            CustomCategoryIcon customCategoryIcon = await unitOfWork.CustomCategoryIconRepository.GetByIdAsync(id);

            if (customCategoryIcon == null)
                throw new InvalidInputException("Custom category icon does not exist");

            unitOfWork.CustomCategoryIconRepository.Delete(customCategoryIcon);
            await unitOfWork.SaveAsync();

            return new OperationResultDto { Status = OperationResultStatus.Succeed };
        }

        public async Task<PaginationListDto<AdminListCustomCategoryIconDto>> AdminListAsync(int categoryId, IUnitOfWork unitOfWork, int offset = 0, int count = 10)
        {
            IQueryable<CustomCategoryIcon> query = unitOfWork.CustomCategoryIconRepository.GetAll()
                .Where(x => x.CategoryId == categoryId);

            int totalCount = await query.CountAsync();

            AdminListCustomCategoryIconDto[] items = await query
                .Skip(offset)
                .Take(count)
                .ProjectToType<AdminListCustomCategoryIconDto>()
                .ToArrayAsync();

            return new PaginationListDto<AdminListCustomCategoryIconDto>
            {
                Count = totalCount,
                Data = items
            };
        }

        public async Task<PublicListCustomCategoryIconDto[]> PublicListAsync(int manufacturerId, IUnitOfWork unitOfWork)
        {
            return await unitOfWork.CustomCategoryIconRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId)
                .ProjectToType<PublicListCustomCategoryIconDto>()
                .ToArrayAsync();
        }
    }
}