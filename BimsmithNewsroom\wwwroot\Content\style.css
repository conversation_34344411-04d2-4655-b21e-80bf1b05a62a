/* fonts*/

/* latin-ext */
@font-face {
  font-family: 'Lato';
  font-style: italic;
  font-weight: 300;
  src: local('Lato Light Italic'), local('Lato-LightItalic'), url(https://fonts.gstatic.com/s/lato/v14/XNVd6tsqi9wmKNvnh5HNEBJtnKITppOI_IvcXXDNrsc.woff2) format('woff2');
  unicode-range: U+0100-024F, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Lato';
  font-style: italic;
  font-weight: 300;
  src: local('Lato Light Italic'), local('Lato-LightItalic'), url(https://fonts.gstatic.com/s/lato/v14/2HG_tEPiQ4Z6795cGfdivFtXRa8TVwTICgirnJhmVJw.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2212, U+2215;
}
/* latin-ext */
@font-face {
  font-family: 'Lato';
  font-style: normal;
  font-weight: 400;
  src: local('Lato Regular'), local('Lato-Regular'), url(https://fonts.gstatic.com/s/lato/v14/UyBMtLsHKBKXelqf4x7VRQ.woff2) format('woff2');
  unicode-range: U+0100-024F, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Lato';
  font-style: normal;
  font-weight: 400;
  src: local('Lato Regular'), local('Lato-Regular'), url(https://fonts.gstatic.com/s/lato/v14/1YwB1sO8YE1Lyjf12WNiUA.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2212, U+2215;
}
/* latin-ext */
@font-face {
  font-family: 'Lato';
  font-style: normal;
  font-weight: 700;
  src: local('Lato Bold'), local('Lato-Bold'), url(https://fonts.gstatic.com/s/lato/v14/ObQr5XYcoH0WBoUxiaYK3_Y6323mHUZFJMgTvxaG2iE.woff2) format('woff2');
  unicode-range: U+0100-024F, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Lato';
  font-style: normal;
  font-weight: 700;
  src: local('Lato Bold'), local('Lato-Bold'), url(https://fonts.gstatic.com/s/lato/v14/H2DMvhDLycM56KNuAtbJYA.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2212, U+2215;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 400;
  src: local('Source Sans Pro Italic'), local('SourceSansPro-Italic'), url(https://fonts.gstatic.com/s/sourcesanspro/v11/M2Jd71oPJhLKp0zdtTvoMzH9_sidkFTxRpx-nVOZt1g.woff2) format('woff2');
  unicode-range: U+0460-052F, U+20B4, U+2DE0-2DFF, U+A640-A69F;
}
/* cyrillic */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 400;
  src: local('Source Sans Pro Italic'), local('SourceSansPro-Italic'), url(https://fonts.gstatic.com/s/sourcesanspro/v11/M2Jd71oPJhLKp0zdtTvoMywVDUTj8DgJRSuHEKdySfs.woff2) format('woff2');
  unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 400;
  src: local('Source Sans Pro Italic'), local('SourceSansPro-Italic'), url(https://fonts.gstatic.com/s/sourcesanspro/v11/M2Jd71oPJhLKp0zdtTvoM7Y_mfr7j0PatIrZBoJCV9E.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 400;
  src: local('Source Sans Pro Italic'), local('SourceSansPro-Italic'), url(https://fonts.gstatic.com/s/sourcesanspro/v11/M2Jd71oPJhLKp0zdtTvoM2qPyaMejTZ925Ro5CnyJwc.woff2) format('woff2');
  unicode-range: U+0370-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 400;
  src: local('Source Sans Pro Italic'), local('SourceSansPro-Italic'), url(https://fonts.gstatic.com/s/sourcesanspro/v11/M2Jd71oPJhLKp0zdtTvoM7YHq4FgHI02B8rPccK0FJQ.woff2) format('woff2');
  unicode-range: U+0102-0103, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 400;
  src: local('Source Sans Pro Italic'), local('SourceSansPro-Italic'), url(https://fonts.gstatic.com/s/sourcesanspro/v11/M2Jd71oPJhLKp0zdtTvoM40tgx99jmYGv_xzYuwd1rU.woff2) format('woff2');
  unicode-range: U+0100-024F, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: italic;
  font-weight: 400;
  src: local('Source Sans Pro Italic'), local('SourceSansPro-Italic'), url(https://fonts.gstatic.com/s/sourcesanspro/v11/M2Jd71oPJhLKp0zdtTvoMxgy2Fsj5sj3EzlXpqVXRKo.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2212, U+2215;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: local('Source Sans Pro Regular'), local('SourceSansPro-Regular'), url(https://fonts.gstatic.com/s/sourcesanspro/v11/ODelI1aHBYDBqgeIAH2zlAC5S7WFEeHRqL6ObGQGT8o.woff2) format('woff2');
  unicode-range: U+0460-052F, U+20B4, U+2DE0-2DFF, U+A640-A69F;
}
/* cyrillic */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: local('Source Sans Pro Regular'), local('SourceSansPro-Regular'), url(https://fonts.gstatic.com/s/sourcesanspro/v11/ODelI1aHBYDBqgeIAH2zlMgmx_L9kV4w6g8dYQOLFUI.woff2) format('woff2');
  unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: local('Source Sans Pro Regular'), local('SourceSansPro-Regular'), url(https://fonts.gstatic.com/s/sourcesanspro/v11/ODelI1aHBYDBqgeIAH2zlMODs9238LZG2v64UiBIjng.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: local('Source Sans Pro Regular'), local('SourceSansPro-Regular'), url(https://fonts.gstatic.com/s/sourcesanspro/v11/ODelI1aHBYDBqgeIAH2zlFjqPhnWKseBf12Mt9_m7kc.woff2) format('woff2');
  unicode-range: U+0370-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: local('Source Sans Pro Regular'), local('SourceSansPro-Regular'), url(https://fonts.gstatic.com/s/sourcesanspro/v11/ODelI1aHBYDBqgeIAH2zlNOAHFN6BivSraYkjhveRHY.woff2) format('woff2');
  unicode-range: U+0102-0103, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: local('Source Sans Pro Regular'), local('SourceSansPro-Regular'), url(https://fonts.gstatic.com/s/sourcesanspro/v11/ODelI1aHBYDBqgeIAH2zlC2Q8seG17bfDXYR_jUsrzg.woff2) format('woff2');
  unicode-range: U+0100-024F, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Source Sans Pro';
  font-style: normal;
  font-weight: 400;
  src: local('Source Sans Pro Regular'), local('SourceSansPro-Regular'), url(https://fonts.gstatic.com/s/sourcesanspro/v11/ODelI1aHBYDBqgeIAH2zlNV_2ngZ8dMf8fLgjYEouxg.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2212, U+2215;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 300;
  src: local('Ubuntu Light'), local('Ubuntu-Light'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCv6KVjbNBYlgoC1CzjvWyNL4U.woff2) format('woff2');
  unicode-range: U+0460-052F, U+20B4, U+2DE0-2DFF, U+A640-A69F;
}
/* cyrillic */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 300;
  src: local('Ubuntu Light'), local('Ubuntu-Light'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCv6KVjbNBYlgoC1CzjtGyNL4U.woff2) format('woff2');
  unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 300;
  src: local('Ubuntu Light'), local('Ubuntu-Light'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCv6KVjbNBYlgoC1CzjvGyNL4U.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 300;
  src: local('Ubuntu Light'), local('Ubuntu-Light'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCv6KVjbNBYlgoC1Czjs2yNL4U.woff2) format('woff2');
  unicode-range: U+0370-03FF;
}
/* latin-ext */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 300;
  src: local('Ubuntu Light'), local('Ubuntu-Light'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCv6KVjbNBYlgoC1CzjvmyNL4U.woff2) format('woff2');
  unicode-range: U+0100-024F, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 300;
  src: local('Ubuntu Light'), local('Ubuntu-Light'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCv6KVjbNBYlgoC1CzjsGyN.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2212, U+2215;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 400;
  src: local('Ubuntu Regular'), local('Ubuntu-Regular'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCs6KVjbNBYlgoKcg72j00.woff2) format('woff2');
  unicode-range: U+0460-052F, U+20B4, U+2DE0-2DFF, U+A640-A69F;
}
/* cyrillic */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 400;
  src: local('Ubuntu Regular'), local('Ubuntu-Regular'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCs6KVjbNBYlgoKew72j00.woff2) format('woff2');
  unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 400;
  src: local('Ubuntu Regular'), local('Ubuntu-Regular'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCs6KVjbNBYlgoKcw72j00.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 400;
  src: local('Ubuntu Regular'), local('Ubuntu-Regular'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCs6KVjbNBYlgoKfA72j00.woff2) format('woff2');
  unicode-range: U+0370-03FF;
}
/* latin-ext */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 400;
  src: local('Ubuntu Regular'), local('Ubuntu-Regular'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCs6KVjbNBYlgoKcQ72j00.woff2) format('woff2');
  unicode-range: U+0100-024F, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 400;
  src: local('Ubuntu Regular'), local('Ubuntu-Regular'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCs6KVjbNBYlgoKfw72.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2212, U+2215;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 500;
  src: local('Ubuntu Medium'), local('Ubuntu-Medium'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCv6KVjbNBYlgoCjC3jvWyNL4U.woff2) format('woff2');
  unicode-range: U+0460-052F, U+20B4, U+2DE0-2DFF, U+A640-A69F;
}
/* cyrillic */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 500;
  src: local('Ubuntu Medium'), local('Ubuntu-Medium'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCv6KVjbNBYlgoCjC3jtGyNL4U.woff2) format('woff2');
  unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 500;
  src: local('Ubuntu Medium'), local('Ubuntu-Medium'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCv6KVjbNBYlgoCjC3jvGyNL4U.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 500;
  src: local('Ubuntu Medium'), local('Ubuntu-Medium'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCv6KVjbNBYlgoCjC3js2yNL4U.woff2) format('woff2');
  unicode-range: U+0370-03FF;
}
/* latin-ext */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 500;
  src: local('Ubuntu Medium'), local('Ubuntu-Medium'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCv6KVjbNBYlgoCjC3jvmyNL4U.woff2) format('woff2');
  unicode-range: U+0100-024F, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 500;
  src: local('Ubuntu Medium'), local('Ubuntu-Medium'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCv6KVjbNBYlgoCjC3jsGyN.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2212, U+2215;
}
/* cyrillic-ext */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 700;
  src: local('Ubuntu Bold'), local('Ubuntu-Bold'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCv6KVjbNBYlgoCxCvjvWyNL4U.woff2) format('woff2');
  unicode-range: U+0460-052F, U+20B4, U+2DE0-2DFF, U+A640-A69F;
}
/* cyrillic */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 700;
  src: local('Ubuntu Bold'), local('Ubuntu-Bold'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCv6KVjbNBYlgoCxCvjtGyNL4U.woff2) format('woff2');
  unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 700;
  src: local('Ubuntu Bold'), local('Ubuntu-Bold'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCv6KVjbNBYlgoCxCvjvGyNL4U.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 700;
  src: local('Ubuntu Bold'), local('Ubuntu-Bold'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCv6KVjbNBYlgoCxCvjs2yNL4U.woff2) format('woff2');
  unicode-range: U+0370-03FF;
}
/* latin-ext */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 700;
  src: local('Ubuntu Bold'), local('Ubuntu-Bold'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCv6KVjbNBYlgoCxCvjvmyNL4U.woff2) format('woff2');
  unicode-range: U+0100-024F, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Ubuntu';
  font-style: normal;
  font-weight: 700;
  src: local('Ubuntu Bold'), local('Ubuntu-Bold'), url(https://fonts.gstatic.com/s/ubuntu/v11/4iCv6KVjbNBYlgoCxCvjsGyN.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2212, U+2215;
}



/*
Theme Name: BIMsmithMarket
Theme URI: http://falgunidesai.com/work/bimsmith-a-wordpress-theme/
Author: Falguni Desai
Author URI: http://falgunidesai.com/
Description: BIMsmithMarket is a new fully responsive and translation ready theme that allows you to create stunning blogs and websites. Theme is well suited for travel, photography, recipe, design, art, personal and any other creative websites and blogs. The theme is developed using Bootstrap 3 that makes it mobile and tablets friendly.  It has various options in WordPress Customizer to change look of the theme. Theme customizer can be used to add your own header image of flexible height, background image, to set background color, header text color and accent color(Supports seven different accent colors).  Theme supports eight post formats, such as: Video, Image, Aside, Status, Audio, Quote, Link and Gallery.
Version: 1.2.6
License: GNU General Public License v2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
Tags: pink,orange,blue,green,yellow, gray, white,black,light,two-columns, right-sidebar, responsive-layout, theme-options, custom-background, custom-header, custom-colors, custom-menu, featured-images,  post-formats, sticky-post, translation-ready, rtl-language-support,threaded-comments
Text Domain: bimsmith

This theme is licensed under the GPL.

*/


/**
 * Table of Contents:
 *
 * 1.0 - Reset
 * 2.0 Repeatable Patterns
 * 3.0 Header
 *     3.1 Site Navigation Bar
 *     3.2 Site Header
 * 4.0 Content
 *     4.1 Post-Content
 *     4.2 Page and Post Navigation links
 *     4.3 Post Formats
 *     4.4 Gallery
 *     4.5 Post-Comments
 * 5.0 Sidebar - Widgets
 * 6.0 Site Footer
 * 7.0 Media Queries
 * 8.0 Print
 *
**/

/**
 * 1.0 Reset
  * -----------------------------------------------------------------------------
 */


.sticky-post{
      background: #009688;
      color:white;
  }

  .entry-title a:hover,
  .entry-title a:focus{
      color: #009688;
  }

  .entry-header .entry-meta::after{
      background: #009688;
  }

  .fa {
    color: #009688;
  }

  .btn-default{
    border-bottom: 1px solid #009688;
  }

  .btn-default:hover, .btn-default:focus{
      border-bottom: 1px solid #009688;
      background-color: #009688;
  }

  .nav-previous:hover, .nav-next:hover{
      border: 1px solid #009688;
      background-color: #009688;
  }

  .next-post a:hover,.prev-post a:hover{
      color: #009688;
  }

  .posts-navigation .next-post a:hover .fa, .posts-navigation .prev-post a:hover .fa{
      color: #009688;
  }


  #secondary .widget-title::after{
    background-color: #009688;
      content: "";
      position: absolute;
      width: 50px;
      display: block;
      height: 4px;
      bottom: -15px;
  }

  #secondary .widget a:hover,
  #secondary .widget a:focus{
    color: #009688;
  }

  #secondary .widget_calendar tbody a {
      background-color: #009688;
      color: #fff;
      padding: 0.2em;
  }

  #secondary .widget_calendar tbody a:hover{
      background-color: #009688;
      color: #fff;
      padding: 0.2em;
  }

body {
    background-color: #fff;
}

body,
button,
input,
select,
textarea {
    color: #797979;
    font-size: 18px;
    font-weight: 400;
    font-family: 'Ubuntu', sans-serif;
}


h1,h2,h3,h4,h5,h6 {
    clear: both;
    color: #000;
    font-family: 'Ubuntu', sans-serif;
    font-style: normal;
    font-weight: 400;
}

p{
    margin-bottom: 1.5em;
    line-height: 1.7em;
}


a{
    color: #000;
    text-decoration: none;
}

a:visited {
    color: #78909c;
    text-decoration: underline;
}

a:hover,
a:active { /* Improves readability when focused and also mouse hovered in all browsers people.opera.com/patrickl/experiments/keyboard/test */
    outline: 0;
}

a:hover{
    text-decoration: none;
}

a img {
    border: 0;
}

dd {
    margin: 0 1.5em 1.5em;
}

dfn, cite, em, i {
    font-style: italic;
}

blockquote {
    border-left: 5px solid #455A64;
    background: #F5f5f5;
    font-size: 18px;
    font-style: italic;
    margin-left: 0.5em;
}

blockquote p{
    font-size: 18px;
}

code {
    white-space: normal;
    color: #666;
}

table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
}

table caption {
    text-align: center;
    text-transform: uppercase;
}

thead{
    background-color: #fff;
    color:#212121;
}

table,th,td{
     border: 1px solid #ddd;
}

th, td {
  padding: 0.7em;
  text-align: left;

}
ol,ul{
    padding: 0;
}
tbody tr:nth-child(odd) {
  background: #eee;
}

button,
input,
select,
textarea {
	font-size: 100%;
	margin: 0;
	max-width: 100%;
	vertical-align: baseline;
}

embed,
iframe,
object {
    max-width: 100%;
}

iframe{
    width: 100%;
}


/**
 * 2.0 Repeatable Patterns
 * ----------------------------------------------------------------------------
 */


/* Text meant only for screen readers. */
.screen-reader-text {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
}

.screen-reader-text:hover,
.screen-reader-text:active,
.screen-reader-text:focus {
    background-color: #f1f1f1;
    border-radius: 3px;
    box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
    clip: auto !important;
    color: #21759b;
    display: block;
    font-size: 14px;
    font-size: 0.875rem;
    font-weight: bold;
    height: auto;
    left: 5px;
    line-height: normal;
    padding: 15px 23px 14px;
    text-decoration: none;
    top: 5px;
    width: auto;
    z-index: 100000; /* Above WP toolbar. */
}

button,
input,
textarea {
	font-family: inherit;
	padding: 5px;
}

input,
textarea {
	color: #727272;
    border: 1px solid #aaa1a8;
}

input:focus,
textarea:focus {
	outline: 0;
}

input:hover,
textarea:hover {
	outline: 0;
}

button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
    border: 1px solid #455A64;
    border-radius: 2px;
    background-color:    #929497;
    color:#fff;
}

button:hover,
html input[type="button"]:hover,
input[type="reset"]:hover,
input[type="submit"]:hover {
    border: 1px solid #455A64;
    border-radius: 2px;
    background:    -moz-linear-gradient(#607d8b, #455a64);
    background:    -o-linear-gradient(#607d8b, #455a64);
    background:    -webkit-linear-gradient(#607d8b, #455a64);
    background:    linear-gradient(#607d8b, #455a64);
    color:#fff;
}

button:focus,
html input[type="button"]:focus,
input[type="reset"]:focus,
input[type="submit"]:focus,
button:active,
html input[type="button"]:active,
input[type="reset"]:active,
input[type="submit"]:active {
    border: 1px solid #455A64;
    border-radius: 2px;
    background:    -moz-linear-gradient(#607d8b, #455a64);
    background:    -o-linear-gradient(#607d8b, #455a64);
    background:    -webkit-linear-gradient(#607d8b, #455a64);
    background:    linear-gradient(#607d8b, #455a64);
    color:#fff;
}

input[type=text],
input[type=email],
textarea {
    color: #727272
}
input[type=text]:focus,
input[type=email]:focus,
textarea:focus {
    color: #727272;
}


input[type="search"].search-field {
    border-radius: 2px 0 0 2px;
    width: -webkit-calc(100% - 42px);
    width: calc(100% - 42px);
}

.search-form {
    position: relative;
}

.search-form label{
    width: 100%;
    margin-bottom: 0px;
}

.search-submit:before {
    content: "\f002";
    font-family: FontAwesome;
    font-size: 16px;
    left: 2px;
    line-height: 42px;
    position: relative;
    width: 40px;
}

.search-submit {
    border-radius: 0 2px 2px 0;
    bottom: 0;
    overflow: hidden;
    padding: 0;
    position: absolute;
    right: 0;
    top: 0;
    width: 42px;
}

.alignnone {
    margin: 5px 20px 20px 0;
}

.aligncenter,
div.aligncenter {
    display: block;
    margin: 5px auto 5px auto;
}

.alignright {
    float:right;
    margin: 5px 0 20px 20px;
}

.alignleft {
    float: left;
    margin: 5px 20px 20px 0;
}

a img.alignright {
    float: right;
    margin: 5px 0 20px 20px;
}

a img.alignnone {
    margin: 5px 20px 20px 0;
}

a img.alignleft {
    float: left;
    margin: 5px 20px 20px 0;
}

a img.aligncenter {
    display: block;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 20px;
}

.wp-caption {
    background: #fff;
    max-width: 96%; /* Image does not overflow the content area */
    padding: 5px 3px 10px;
    text-align: center;
}

.wp-caption.aligncenter,
.wp-caption.alignnone{
        margin: 5px auto 20px;
}

.wp-caption.alignleft{
    margin: 5px 20px 20px 0;
}
.wp-caption.alignright{
    margin: 5px 0px 20px 20px;
}


.p2hc-post-text.full-width {
    width: 100%;
}

.p2hc-post-image {
    float: left;
    width: 25%;

}

.p2hc-post-text {
    float: left;
    width: 72%;
    margin-left: 3%;
}


.wp-caption img {
    border: 0 none;
    height: auto;
    margin: 0;
    max-width: 98.5%;
    padding: 0;
    width: auto;
}

.wp-caption-text {
    font-size: 14px;
    text-align: center;
}
.wp-caption .wp-caption-text {
    margin: 0.8075em 0;
}

.wp-caption p.wp-caption-text {
    font-size: 14px;
    line-height: 17px;
    margin: 0;
    padding: 0 4px 5px;
}

.size-auto,
.size-full,
.size-large,
.size-medium,
.size-thumbnail {
    max-width: 100%;
    height: auto;
}

.entry-content img,
.comment-content img,
.widget img {
    max-width: 100%; /* Fluid images for posts, comments, and widgets */
}

.entry-content img,
img[class*="align"],
img[class*="wp-image-"] {
    max-width: 100%;
    height: auto; /* Make sure images with WordPress-added height and width attributes are scaled correctly */
}

.entry-content img,
img.size-full {
    max-height: 100%;
    display: block;
    max-width: 100%;
    width: auto; /* Prevent stretching of full-size images with height and width attributes in IE8 */
}


.entry-content img.wp-smiley,
.comment-content img.wp-smiley {
    border: none;
    margin-bottom: 0;
    margin-top: 0;
    padding: 0;
}

img.wp-post-image{
    max-width: 100%;
    width: auto;
}

img.wp-post-image{
    height: auto;
}

.image-attachment{
    margin-left: auto;
    margin-right: auto;
}

.featured-image img{
    display: block;
    margin:0 auto;
}



/**
 * 3.0 Header
 * ----------------------------------------------------------------------------
 */

/**
 * 3.1 Site Navigation Bar
 * ----------------------------------------------------------------------------
 */
 .nav{
    display: block;
}

 .navbar{
    margin-bottom: 0px;
 }
 .home .entry-header {
    display: none;
}
.home .breads {

}
.home .breads > section > span:first-child{
    text-indent: 45px;
}
.home .breads span:last-child {
    border-right: 1px solid #b0b0b0;
}
 .navbar-fixed-top,
.navbar-fixed-bottom {
  position: fixed;
  top:0;
  z-index: 1030; }
  @media (min-width: 768px) {
    .navbar-fixed-top,
    .navbar-fixed-bottom {
      border-radius: 0; } }

.navbar-fixed-top {
  top: 0;
  border-width: 0 0 1px; }

.admin-bar .navbar-fixed-top {
  top: 32px; }

@media (max-width: 782px) {
    .admin-bar .navbar-fixed-top{
    top: 46px;
    position: absolute;
  }

}

.navbar{
    font-size: 14px;
    font-weight: 400;
    font-style: normal;
    text-transform: uppercase;
    border:none;
}

.navbar-brand{
    font-size: 20px;
    width: 230px;
    font-weight: 600;
    text-indent: 5000px;
    background-image: url('../images/bimsmith.png');
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 230px auto;
    height: 80px;
}

.navbar-nav{
    float: left;
}
.navbar-nav > li > a {
    padding-top: 11px;
    padding-bottom: 11px;
}
.nav .open > a, .nav .open > a:hover, .nav .open > a:focus {
    background-color: #fff;
    border-color: #fff;
}
.navbar-default {
    background-color: #ffffff;
    box-shadow: 0px 1px 1px rgba(100,100,0, 0.1);
    padding: 20px 0;
}
.navbar-default .navbar-brand {
    color: white;
    text-indent: -9999px;
}
.navbar-default .navbar-brand:hover, .navbar-default .navbar-brand:focus {
    color: #000000;
}
.navbar-default .navbar-text {
    color: #333333;
}
.navbar-default .navbar-nav > li > a {
    border-top: 4px solid #fff;
    border-bottom: 4px solid #fff;
}
.navbar-default .navbar-nav > li > a {
    color: #333333;
}
.navbar-default .navbar-nav > li > .dropdown-menu {
background-color: #ffffff;
}
.navbar-default .navbar-nav > li > .dropdown-menu > li > a {
color: #333333;
}
.navbar-default .navbar-nav > li > .dropdown-menu > li > a:hover,
.navbar-default .navbar-nav > li > .dropdown-menu > li > a:focus {
color: #fff;
}
.navbar-default .navbar-nav > li > .dropdown-menu > li > .divider {
background-color: #ffffff;
}
.navbar-default .navbar-nav > .active > a, .navbar-default .navbar-nav > .active > a:hover, .navbar-default .navbar-nav > .active > a:focus {
background: #fff;
}
.navbar-default .navbar-nav > .open > a, .navbar-default .navbar-nav > .open > a:hover, .navbar-default .navbar-nav > .open > a:focus {
background: #fff;
}
.nav .open > a, .nav .open > a:hover, .nav .open > a:focus {
    background-color: #fff;
    border-color: #fff;
}

.navbar-default .navbar-toggle {
border-color: #455A64;
}
.navbar-default .navbar-toggle:hover, .navbar-default .navbar-toggle:focus {
background-color: #455A64;
color: #fff;
}
.navbar-default .navbar-toggle .icon-bar {
background-color: #333;
}
.navbar-default .navbar-toggle:hover .icon-bar,.navbar-default .navbar-toggle:focus .icon-bar{
background-color: #fff;
}

.navbar-default .navbar-link {
color: #333333;
}
.navbar-default .navbar-link:hover {
color: #000000;
}

.dropdown-menu > .active > a, .dropdown-menu > .active > a:hover, .dropdown-menu > .active > a:focus {
    color: #FFF;
    text-decoration: none;
    outline: 0px none;
}


@media (max-width: 767px) {
	.navbar-default .navbar-nav .open .dropdown-menu > li > a {
	color: #333333;
	}
    .navbar-fixed-top .navbar-collapse, .navbar-fixed-bottom .navbar-collapse {
       max-height: 600px;
    }
}

@media (min-width: 768px) {
  ul.nav li.dropdown:hover > ul.dropdown-menu {
    display: block;
  }
}

/**
 * 3.2 Site Header
 * ----------------------------------------------------------------------------
 */

.site-header .site-branding {
    margin: 0;
   position: absolute;
    top: 50%;
    left: 50%;
    margin-right: -50%;
    -webkit-transform: translate(-50%,-50%);
    -ms-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    text-align: center;
}

.site-title {
    color: #fff;
    padding: 20px 50px;
    font: 500 48px/54px Ubuntu, Helvetica, sans-serif;
    background: #3db28e;
}

.site-description {
    color: #fff;
    font-size: 1em;
    font-weight: 400;
    padding: 0.2em 0em;
    letter-spacing: 5px;
    text-transform: capitalize;
    margin-top: 0px;
    margin-bottom: 0px;
    text-shadow:rgba(255,255,255,.1) -1px -1px 1px,rgba(0,0,0,.5) 1px 1px 1px;
}

.site-title a,
.site-description a{
    text-decoration: none;
}

.home-link{
     text-decoration: none;
}


/**
 * 4.0 Main Content
 * ----------------------------------------------------------------------------
 */

 /* 4.1 Post-Content
 * ----------------------------------------------------------------------------
 */

 .sticky {

}

.sticky-post{
    position: absolute;
    top:0;
    left: 15px;
    padding: 5px 15px;
}

.post-content,.single-post-content,.post-comments,.comments-area{
    clear: both;
}

.featured-image{
    padding: 0.5em 0.5em;
}

.single-post-content{
    padding: 0 4em;
}

.comments-area{
    padding: 1.5em 4em;
    background: white;
    overflow: hidden;
}

.post-comments{
    border-radius: 0px;
    border: none;
    padding: 2em;
    overflow: hidden;
}

.post-comments .comments-area{
    background: white;
    padding: 1em 2em;
    overflow: hidden;
    box-shadow: none;
}

.post-content hr{
    margin-top: 20px;
    margin-bottom: 20px;
    border-color: #B6B6B6;
    border-style: dotted;
    width: 100%;
}

.entry-content,.entry-header,.entry-meta,.entry-title,
.entry-summary,.entry-footer{
    width: 100%;
    word-wrap:break-word;
    overflow: hidden;
}

.entry-summary,
.entry-content{
    color: #424242;
}

.home .entry-header{
    display: none;
}

.entry-title{
    margin-bottom: 14px;
}

.entry-header .entry-title a{
    color: #212121;
}

.entry-meta{
    clear: both;
    text-transform: capitalize;

}

.entry-meta a{
    clear: both;
    color: #727272;
    text-transform: capitalize;
    line-height: 1.7;
}

.entry-meta h5{
    margin-top: 0px;
    margin-bottom: 0px;
}



.taxonomy-description{
    color: #455A64;
    text-align: center;
}


.byline{
    text-transform: capitalize;
}

.byline a{
    color: #727272;
}

.single .byline,
.group-blog .byline {
    display: inline;
}

.entry-footer{
    word-spacing: 3px;
    padding-bottom: 2.5em;
    display: none;
}

#category{
    margin-top: 1em;
    margin-bottom: 1em;
}

.cat-links a, .tags-links a{
    color: #727272;
    text-transform: capitalize;
}

.archive-page-title{
    text-transform: capitalize;
}

.search-page-header,.archive-page-header{
    margin-bottom: 1.5em;
}

.search-page-title,.archive-page-title{
    color: #525252;
    text-align: center;
    letter-spacing: 2px;
}

.attachment img{
    margin-left: auto;
    margin-right: auto;
}

.entry-summary,.entry-content{
    text-align: initial;
}

/**Buttons**/
.btn-default{
    border: none;
    border-radius: 0px;
    color: #212121;
}

.btn-default:hover, .btn-default:focus{
    color: #fff;
}

.btn-default:visited{
    color:#212121;
}

/*Image attachment*/
.image-attachment .entry-meta a{
    color: #607D8B;
    text-decoration: underline;
}

.image-attachment .entry-content{
    text-align: left;
}

/*
* 4.2 Page and Post Navigation links
* --------------------------------------------------------------
*/


.page-links {
    clear: both;
    margin: 0 0 1.5em;
}

.nav-links{
    background: #fff;
    box-shadow: 0px 0px 1px rgba(0, 0, 0, 0.15);
    padding: 15px;
    text-align: center;
    margin-bottom: 2em;
}

.nav-links .fa{
    color: #727272;
}

.nav-previous,.nav-next,.next-post,.prev-post{
    background: #fff;
    color: #727272;
    font-weight: 400;
    text-transform: capitalize;
    padding: 1em;
}

.posts-navigation .next-post a,.posts-navigation .prev-post a{
    font-size: 18px;
}


.next-post a,.prev-post a{
    color: #424242;
}

.next-prev-text{
    color: #727272;
    font-size: 14px;
}


/*
* 4.3 Post Formats
* -------------------------------------
*/



/* Post Format = Status----------------------------------------------- */

.blog .format-status .entry-content,
.archive .format-staus .entry-content{
    background-color: #CFD8DC;
    padding-top: 2em;
    font-size: 16px;
}

.blog .format-status .entry-footer,
.archive .format-staus .entry-footer{
    background-color: #CFD8DC;
}

/*
* 4.4 Gallery
* -----------------------------------------
*/

.gallery {
    margin-bottom: 1.6em;
}

.gallery-item {
    display: inline-block;
    padding: 1.79104477%;
    text-align: center;
    vertical-align: top;
    width: 100%;
}

.gallery-columns-2 .gallery-item {
    max-width: 50%;
}

.gallery-columns-3 .gallery-item {
    max-width: 33.33%;
}

.gallery-columns-4 .gallery-item {
    max-width: 25%;
}

.gallery-columns-5 .gallery-item {
    max-width: 20%;
}

.gallery-columns-6 .gallery-item {
    max-width: 16.66%;
}

.gallery-columns-7 .gallery-item {
    max-width: 14.28%;
}

.gallery-columns-8 .gallery-item {
    max-width: 12.5%;
}

.gallery-columns-9 .gallery-item {
    max-width: 11.11%;
}

.gallery-icon img {
    margin: 0 auto;
}

.gallery-caption {
    color: #707070;
    color: rgba(51, 51, 51, 0.7);
    display: block;
    font-size: 11px;
    font-size: 1.1rem;
    line-height: 1.5;
    padding: 0.5em 0;
}

.gallery-columns-6 .gallery-caption,
.gallery-columns-7 .gallery-caption,
.gallery-columns-8 .gallery-caption,
.gallery-columns-9 .gallery-caption {
    display: none;
}


/*
* 4.5 Post-Comments
* ------------------------------
**/


.comments-author .url{
    text-transform: uppercase;
}

.avatar{
    height: 50px;
    width: 50px;
    border-radius: 25px;
}

.comment-respond label {
    display: block;
    font-weight: normal;
}

.comment-metadata{
    font-size: 0.8em;
}

.comment-list{
    list-style: none;
}

.comment-list .depth-1{
    margin: 20px 0px;
    border-bottom: 1px dotted #DDD;
    padding: 5px 0px 18px;
}

.comment-list .children{
    list-style: none;
}

.comment-content{
    margin-bottom: 0.3em;
}

.comment-respond #submit{
    background-color: #929497;
    color: white;
    border:none;
    border-radius: 4px;
    padding: 5px 10px;
}

.comment-respond{
    padding-top: 5px;
}

.comment-metadata time{
    color: #aaa;
}

.bypostauthor {

}
/**
 * 5.0 Sidebar - Widgets
 * ----------------------------------------------------------------------------
 */
#secondary .widget{
    background-color: white;
    color: #727272;
    font-size: 16px;
    margin-bottom: 0;
    margin-left: auto;
    margin-right: auto;
    overflow: hidden;
    padding: 24px 24px 24px;
    box-shadow: 0px 0px 1px rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0 0 1px rgba(0, 0, 0, 0.15);
    -webkit-box-shadow: 0 0 1px rgba(0, 0, 0, 0.15);
    -o-box-shadow: 0 0 1px rgba(0, 0, 0, 0.15);
}

#secondary .widget-title{
    font-weight: 400;
    position: relative;
}

#secondary .widget-title{
    margin-top: -24px;
    margin-left: -24px;
    margin-right: -24px;
    margin-bottom: 2em;
    padding: 1.5em 1.5em 0;
    text-transform: uppercase;
}


#secondary .widget-title::after{
    content: "";
    position: absolute;
    width: 50px;
    display: block;
    height: 4px;
    bottom: -15px;
}

#secondary .widget a{
    color: #727272;
}

#secondary .widget .widget-title .rsswidget{
    color: #212121;
}

.widget_rss li{
    padding-top: 0.5em;
    padding-bottom: 0.5em;
}

#recentcomments li{
    padding-top: 0.5em;
    padding-bottom: 0.5em;
}


.widget ul{
    list-style: none;
}

#secondary .widget li{
    border-bottom: 1px solid #ddd;
    padding: 0.5em 0;
}

#secondary .widget li:last-child{
    border-bottom: none;
}

.widget ul ul {
    padding: 0 0 0 1em;
}


.widget th, .widget td {
  padding: 0.2em;
  text-align: left;
}


.widget select {
    max-width: 100%;
}

#secondary .widget hr{
    border-color: #ddd;
}

#secondary .widget table caption{
    color: #455A64;
}

#secondary .widget #recentcomments a{
    color: #607D8B;
}

#secondary .widget .rsswidget{
    color: #607D8B;
}

#secondary .widget .rsswidget img{
    margin-top: 12px;
}

.widget_text img{
    max-width: 100%;
}

.widget input[type="search"].search-field {
        width: -webkit-calc(100% - 35px);
        width: calc(100% - 35px);
}

.widget .search-submit:before {
        font-size: 16px;
        left: 1px;
        line-height: 35px;
        width: 34px;
}

.widget button.search-submit {
        padding: 0;
        width: 35px;
}


/*
* 6.0 Site Footer
* --------------------------------------------------------------------
*/

.site-footer{
    min-height: 70px;
    overflow: hidden;
}

.site-footer a{
    color: #333;
}

/*
* 7.0 Media Queries
* --------------------------------------------------------------------
*/

@media (max-width: 900px){
    body{
        font-size: 16px
    }

    h1{
        font-size: 26px !important;
    }
    h2{
        font-size: 22px;
    }
    h3{
        font-size: 18px;
    }
    h4{
        font-size: 16px;
    }
    h5{
        font-size: 14px;
    }
    h6{
        font-size: 12px;
    }

    blockquote{
        margin-left: 0.3em;
    }
    table {
      -ms-word-break: break-all;
     word-break: break-all;

    }
    .navbar-nav{
        float: none;
    }

    .sticky-post{
        padding: 3px 15px;
    }

    .entry-header,.entry-content,.entry-summary,.entry-footer{
        padding: 1em 1.5em;
    }

    .entry-footer{
        padding-bottom: 1em;
    }

    .featured-image{
        padding: 0.5em 0.5em;
    }

    .single-post-content{
        padding: 0 1.5em;
    }

    .post-comments,.comments-area{
        border: none;
        box-shadow: none;
        padding: 1.2em 1em;
        overflow: hidden;
        width: 100%;
    }

    .comment-content table{
        overflow: hidden;
    }
    .comment-list {
        padding: 10px;
    }

    .comment-list ol{
        padding-left: 10px;
    }
    input,textarea,iframe{
        width: 100%;
    }

    #submit,.search-submit{
        width: auto;
    }

    th, td {
      padding: 0.2em;
      text-align: left;
      border: 1px solid #B6B6B6;
    }

  }
@media (max-width: 767px) {
    body{
        font-size: 16px
    }
    .site-header{
        position: relative;
        width: 100%;
        min-height: 170px;
    }

    .site-title {
        font-size: 2.1em;
        letter-spacing: 10px;
    }

    .site-description {
        font-size: 14px;
        letter-spacing: 1px;
    }

    .site-footer{
        font-size: 14px;
    }
    .navbar-default .navbar-nav > .active > a, .navbar-default .navbar-nav > .active > a:hover, .navbar-default .navbar-nav > .active > a:focus {
        border-top: none;
    }

    .p2hc-post-image {
        float: left;
        width: 100%;
    }

    .p2hc-post-text {
        float: left;
        width: 100%;
    }
}
@media (max-width: 550px) {
    h1 {
        font-size: 20px !important;
        line-height: 28px !important;
        text-align: center;
    }
}
@media (max-width: 359px) {
    .site-header{
        position: relative;
        width: 100%;
        min-height: 100px;
        margin-bottom: 1.5em;
    }

    .site-title {
        font-size: 2em;
        letter-spacing: 2px;
        margin-top: 5px;
        margin-bottom: 3px;
    }

    .site-title ::after{
        margin: 2px auto;
    }

    .site-description {
        font-size: 8px;
        letter-spacing: 0px;
        margin-top: 1px;
        margin-bottom: 2px;
    }
}


/**
 * 8.0 Print
 * ----------------------------------------------------------------------------
 */


@media print {
    body {
        background: none !important;
        color: #000;
        font-size: 10pt;
    }


    .site {
        max-width: 98%;
    }

    .site-header {
        background-image: none !important;
    }

    .site-header .home-link {
        max-width: none;
        min-height: 0;
    }

    .site-title {
        color: #000;
        font-size: 21pt;
    }

    .site-description {
        font-size: 10pt;
    }

    .author-avatar,
    .site-footer,
    .comment-respond,
    .comments-area .comment-edit-link,
    .comments-area .reply,
    .comments-link,
    .entry-meta .edit-link,
    .page-links,
    .site-content nav,
    .widget-area,
    .main-navigation,
    .navbar,
    .more-link {
        display: none;
    }

    .entry-header,
    .entry-content,
    .entry-summary,
    .entry-meta {
        margin: 0;
        width: 100%;
    }

    .page-title,
    .entry-title {
        font-size: 21pt;
    }

    .entry-meta,
    .entry-meta a {
        color: #444;
        font-size: 10pt;
    }

    .entry-content img.alignleft,
    .entry-content .wp-caption.alignleft {
        margin-left: 0;
    }

    .entry-content img.alignright,
    .entry-content .wp-caption.alignright {
        margin-right: 0;
    }

    .format-image .entry-content .size-full {
        margin: 0;
    }

    /* Remove colors from post formats */
    .hentry {
        background-color: #fff;
    }

    /* Comments */
    .comments-area > li.comment {
        background: none;
        position: relative;
        width: auto;
    }

    .comment-metadata {
        float: none;
    }

    .comment-author .fn,
    .comment-reply-link,
    .comment-reply-login {
        color: #333;
    }
}

.sep {
    padding: 0px 20px;
}

.footerLogo {
    width: 200px;
    padding-right: 50px;
    margin-top: 10px;
}

.social .footerSocialMediaLink {
    width: 30px;
    margin: 5px 8px 5px 0;
}

.post-content.main-page {
    padding: 20px 40px;
}

h2.widget-title {
    text-align: center;
    font-size: 40px;
    color: #555;
    text-transform: uppercase;
    text-shadow: 1px 1px 3px silver;
    margin-bottom: 35px;
    position: relative;
}

/*.widget_p2hc_widget .p2hc-post .image img {*/
    /*float: left;*/
    /*width: 200px;*/
    /*max-width: 200px;*/
/*}*/

h2.widget-title::after {
    background-color: #85C4BA;
    content: "";
    position: absolute;
    width: 79px;
    display: block;
    height: 4px;
    bottom: -8px;
    left: 45%;
}

body #secondary .widget-title::after {
    background-color: #85C4BA;
}

.social img:hover {
    opacity: 0.8;
}

figure.image {
    width: 100%;
    float: left;
}

.entry-footer a.post-edit-link {
    float: right;
    margin-top: -45px;
}

.entry-meta .byline {
    display: none;
}

.entry-meta .fa-comments-o {
    margin-left: 20px;
}


.single-post .cattegories {
    display: none;
}

.comment-form-author, .comment-form-url {
    display: none;
}

a.post-edit-link {
    font-family: 'Source Sans Pro', sans-serif;
    font-size: 18px;
    font-weight: 400;
}

.widget_listcategorypostswidget ul li a {
    width: 100%;
    float: left;
    font-size: 16px;
}

.widget_listcategorypostswidget ul li {
    font-size: 10px;
}

#si_captcha_code label {
    font-size: 14px;
}


/* styles */

.def-btn{
    color: #000 !important;
  display: inline-block;
  vertical-align: middle;
  font-weight: bold;
  text-transform: uppercase;
  -webkit-transition: 0.2s all 0s;
  -moz-transition: 0.2s all 0s;
  -o-transition: 0.2s all 0s;
  transition: 0.2s all 0s;
}
.def-btn:hover{
  color: #3db28e !important;
}
.g-btn{
  display: inline-block;
  height: 44px;
  padding: 0 30px;
  border-radius: 20px;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border: 2px solid #3db28e;
  color: #3db28e !important;
  font: bold 14px/40px Ubuntu, Helvetica, sans-serif;
  text-transform: uppercase;
  -webkit-transition: 0.2s all 0s;
  -moz-transition: 0.2s all 0s;
  -o-transition: 0.2s all 0s;
  transition: 0.2s all 0s;
  vertical-align: middle;
}
.g-btn:hover{
  background: #3db28e;
  color: #fff !important;
}
.sign-in{
  float: right;
  margin: 0;
  position: relative;
}
.sign-in .def-btn{
  margin: 0 25px 0 0;
  font-size: 16px;
}

#menu-primary a {
    font-size: 16px;
    line-height: 1.42857143;
}
.navbar-default .navbar-nav {
    margin: 10px 0 0 40px;
    max-width: 650px;
    text-align: center;
}
.navbar-default .navbar-nav li {
    display: inline-block;
    float: none;
    margin: 0 40px 0 0;
}
.navbar-default .navbar-nav li a {
    padding: 0;
    border: none;
    color: #000;
    text-transform: uppercase;
    font: 700 14px/24px Ubuntu, Helvetica, sans-serif;
}
.navbar-default .navbar-nav li a:hover,
.navbar-default .navbar-nav > .active > a,
.navbar-default .navbar-nav > .active > a:hover,
.navbar-default .navbar-nav > .active > a:focus {
    color: #3db28e;
}
.site-header{
    position: relative;
    width: 100%;
    overflow: hidden;
    min-height: 325px;
    background: url("../images/header-bg.jpg") center center no-repeat;
    background-size: cover;
}
.home .widget-title{
    display: block;
}
.home #primary {
    /*width: 80%;*/
}
.home #secondary{
    display: none;
}
.home #primary .post-content.main-page{
    padding: 0;
}
.site-main{

}
.p2hc-post .image{
    margin: 0;
}
.pt-cv-content-item{
    overflow: hidden;
    margin: 0 0 50px 0;
    padding-bottom:50px;
    border-bottom: 1px solid #efefef;
}
    .pt-cv-content-item .pt-cv-ifield {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-flex-direction: row;
        -ms-flex-direction: row;
        flex-direction: row;
        position: relative;
        min-height: 220px;
    }
.pt-cv-content-item:last-child {
    border: none;
    margin-bottom: 0;
}
.pt-cv-content-item .pt-cv-href-thumbnail{
    width: 400px;
    left: 0;
    top: 0;
    height: 220px;
    background-size: cover;
    background-position: center center;
    margin-right: 20px;
}
.pt-cv-content-item .pt-cv-title {
    position: relative;
    font: 500 24px/28px Ubuntu, Helvetica, sans-serif;
    margin: 0 0 10px;
    -webkit-order: 1;
    -ms-flex-order: 1;
        order: 1;
}

.pt-cv-content-item .pt-cv-title a{
    color: #000;
}
.pt-cv-content-item .pt-cv-meta-fields{
    -webkit-order: 2;
    -ms-flex-order: 2;
        order: 2;
        margin: 0 0 10px;
}
.pt-cv-content-item .pt-cv-meta-fields span {
    position: static;
    background: none;
    padding: 0;
    color: #cacaca;
    font: 400 14px/18px Ubuntu, Helvetica, sans-serif;
    text-align: left;
}
.pt-cv-content-item .pt-cv-title a:hover{
    text-decoration: underline;
}
.pt-cv-content-item .pt-cv-content{
    color: #797979;
    font: 400 16px/24px Ubuntu, Helvetica, sans-serif;
    margin: 0;
    -webkit-order: 3;
    -ms-flex-order: 3;
        order: 3;
}
.pt-cv-readmore.btn{
    margin: 30px 0 0 0;
    -moz-border-radius: 25px;
    -webkit-border-radius: 25px;
    border-radius: 25px;
    border: 1px solid #000;
    height: 48px;
    display: inline-block;
    font: 700 14px/48px Ubuntu, Helvetica, sans-serif;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #3db28e !important;
    padding: 0 20px;
    text-transform: uppercase;
    background: transparent;
}
.pt-cv-readmore.btn:hover{
    background: #3db28e;
    border: 1px solid #3db28e;
    color: #fff !important;
}
.brands-block{
  width: 100%;
  padding: 60px 0;
  text-align: center;
  background: #f5f5f5;
  vertical-align: middle;
  line-height: 0;
  font-size: 0;
}
.brands-block p{
  display: inline-block;
  font: 500 24px/28px Ubuntu, Helvetica, sans-serif;
  color: #000;
  vertical-align: middle;
  margin: 0;
}
.brands-block ul{
  display: inline-block;
  vertical-align: middle;
  margin: 0;
}
.brands-block ul li{
  display: inline-block;
  vertical-align: middle;
  margin: 0 20px;
}
.footer-holder{
  width: 100%;
  background: #191919 url("../images/footer-bg.png") center bottom no-repeat;
  background-size: cover;
  border-bottom: 11px solid #129899;
}
.footer{
  max-width: 1170px;
  padding: 60px 20px;
  margin: 0 auto;
  position: relative;
}
.footer:before{
  display: block;
  position: absolute;
  background: url("../images/footer-man.png") center bottom no-repeat;
  width: 184px;
  height: 128px;
  position: absolute;
  left: 100%;
  top: 45px;
  content: "";
}
.footer:after{
  display: block;
  content: "";
  clear: both;
}
.footer .info{
  width: 35%;
  float: left;
}
.footer .info:after{
  display: block;
  content: "";
  clear: both;
}
.footer .info .logo{
  margin: 0 0 20px;
  float: left;
}
.footer .info .address{
  margin: 0 0 20px;
  float: left;
  width: 100%;
}
.footer .info .address span{
  display: block;
  font: 400 14px/18px Ubuntu, Helvetica, sans-serif;
}
.footer .info .contacts{
  float: left;
  width: 100%;
}
.footer .info .contacts .tel{
  background: url("../images/tel-ico.png") 3px 3px no-repeat;
  padding: 0 0 0 25px;
}
.footer .info .contacts .mail{
  background: url("../images/mail-ico.png") left 7px no-repeat;
}
.footer .info .contacts a{
  display: inline-block;
  font: 400 14px/22px Ubuntu, Helvetica, sans-serif;
  color: #fff;
  padding: 0 0 0 25px;
}
.footer .info .contacts a:hover{
  color: #7aeac8;
}
.footer .links{
  width: 65%;
  float: left;
  line-height: 0;
  font-size: 0;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-justify-content: space-between;
  -ms-flex-pack: justify;
  justify-content: space-between;
}
.footer .links .col{

}
.footer .links p{
  color: #fff;
  font: 700 16px/20px Ubuntu, Helvetica, sans-serif;
  text-transform: uppercase;
  margin: 0 0 20px;
}
.footer .links ul{

}
.footer .links ul li{
  margin: 0 0 10px;
}
.footer .links ul li a{
  display: inline-block;
  color: #919191;
  font: 400 14px/18px Ubuntu, Helvetica, sans-serif;
}
.footer .links ul li a:hover{
  color: #7aeac8;
}
.footer .f-bot{
  width: 100%;
  float: left;
  margin: 30px 0 0 0;
}
.footer .f-bot .social{
  float: left;
  margin: 0;
}
.footer .f-bot .social li{
  margin: 0 10px 0 0;
  display: inline-block;
  vertical-align: top;
}
.footer .f-bot .social li a{
  width: 41px;
  height: 41px;
  -moz-border-radius: 100%;
  -webkit-border-radius: 100%;
  border-radius: 100%;
  background: #555555;
  -webkit-transition: 0.2s all 0s;
  -moz-transition: 0.2s all 0s;
  -o-transition: 0.2s all 0s;
  transition: 0.2s all 0s;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
}
.footer .f-bot .social li a:hover{
  background: #3db28e;
}
.footer .f-bot .social li a img{
  margin: auto;
  width: 16px;
  height: 16px;
}
.footer .f-bot .copy{
  float: right;
  margin: 10px 0 0 0;
  font-size: 16px;
  color: #919191;
}
.footer .f-bot .copy a{
    color: #fff;
}
.footer .f-bot .copy a:hover{
    color: #3db28e;
}
.pt-cv-pagination-wrapper{
    margin: 0;
}

ul.pagination{
  float: right;
  text-align: center;
}
ul.pagination li{
  display: inline-block !important;
  width: 48px;
  height: 48px;
  margin: 0 0 0 10px;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
ul.pagination li a{
  color: #000 !important;
  display: block;
  font: 400 14px/36px Ubuntu, Helvetica, sans-serif !important;
  -webkit-transition: 0.2s all 0s;
  -moz-transition: 0.2s all 0s;
  -o-transition: 0.2s all 0s;
  transition: 0.2s all 0s;
  border: none;
  background: #f2f2f2 !important;
  border: 1px solid #f2f2f2 !important;
  width: 48px;
  height: 48px;
  -moz-border-radius: 0 !important;
  -webkit-border-radius: 0 !important;
  border-radius: 0 !important;
}
ul.pagination li.active a,
ul.pagination li:hover a{
  background: #fff !important;
  border: 1px solid #f2f2f2 !important;
}
ul.pagination li a[title="Go to previous page"],
ul.pagination li a[title="Go to next page"]{
  text-indent: -9999px;
  background: #f2f2f2 url("../images/arrow.png") center center no-repeat !important;
}
ul.pagination li:hover a[title="Go to previous page"],
ul.pagination li:hover a[title="Go to next page"]{
  text-indent: -9999px;
  background: #fff url("../images/arrow.png") center center no-repeat !important;
}
ul.pagination li a[title="Go to previous page"]{
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
}
.back{
    margin: 0 auto 20px;
    max-width: 1140px;
    padding: 0 15px;
    font: 400 14px/18px Ubuntu, Helvetica, sans-serif;
    padding: 0 0 20px;
    border-bottom: 1px solid #efefef;
}
.back a{
    color: #3db28e;
}
.back a:hover{
    opacity: 0.7;
}
.breads {
    background: #f6f6f6 none repeat scroll 0 0;
    margin: 0 0 30px;
    padding: 15px 0;
}
.breads {
    font-size: 0;
    list-style: outside none none;
}
.breads > section {
    margin: 0 auto;
    max-width: 1170px;
    padding: 0 15px;
}
.breads > section > span {
    border-right: 1px solid #b0b0b0;
    display: inline-block;
    margin: 0 12px 0 0;
    padding: 0 12px 0 0;
    vertical-align: middle;
}
.breads > section > span:first-child {
    background: rgba(0, 0, 0, 0) url("../images/home-ico.png") no-repeat scroll left center;
    text-indent: -9999px;
    width: 32px;
}
.breads span:last-child {
    border: medium none;
}
.breads span a, .breads span span {
    color: #000000;
    display: block;
    font: 500 14px/1 Ubuntu,Helvetica,sans-serif;
}
.breads span a:visited, .breads span a:hover, .breads span span:hover {
    color: #3db28e;
}
.post-comments,
.post-navigation{
    display: none;
}
.entry-meta i,
.entry-meta span,
.entry-meta .comments-link{
    display: none;
}
.single-post .post-content {
   display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-direction: column;
        -ms-flex-direction: column;
            flex-direction: column;
    max-width: 730px;
    padding-bottom: 40px;
}
.single-post .post-content .featured-image{
    width: 730px;
    height: 400px;
    max-width: 100%;
    max-height: 100%;
    background-size: cover;
    background-position: center center;
    margin: 35px auto;
    padding: 15px;
    -webkit-order: 3;
        -ms-flex-order: 3;
            order: 3;
}
.single-post .post-content .entry-header{
    -webkit-order: 1;
        -ms-flex-order: 1;
            order: 1;
}
.single-post .post-content .entry-excerpt{
    -webkit-order: 2;
        -ms-flex-order: 2;
            order: 2;
            margin: 30px 0 0 0;
}
.single-post .post-content .entry-excerpt p{
    margin: 0 0 15px;
    font: 400 24px/32px Ubuntu,Helvetica,sans-serif;
    color: #797979;
}
.single-post .post-content .entry-excerpt p:last-child{
    margin: 0;
}
.single-post .post-content .entry-content{
    -webkit-order: 4;
        -ms-flex-order: 4;
            order: 4;
}
.single-post .post-content .entry-content p{
    margin: 0 0 30px;
    font: 400 16px/24px Ubuntu,Helvetica,sans-serif;
    color: #797979;
}
.single-post .post-content .entry-content p strong{
    color: #000;
}
.single-post .post-content .entry-header h1{
    font: 500 36px/40px Ubuntu, Helvetica, sans-serif;
    margin: 0 0 10px;
}
.single-post .post-content .entry-header .entry-date a{
    color: #cacaca;
    font: 500 14px/18px Ubuntu, Helvetica, sans-serif;
}
.widget_recent_entries{
    padding: 0 !important;
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
    box-shadow: none !important;
}
.widget_recent_entries h4{
    font: 700 18px/22px Ubuntu, Helvetica, sans-serif !important;
    text-transform: uppercase;
    padding: 0 !important;
    margin: 0 0 20px !important;
}
.widget_recent_entries h4:after{
    display: none !important;
}
.widget_recent_entries ul{
    list-style: none;
    padding: 0;
}
.widget_recent_entries ul li{
    color: #3db28e;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    padding: 10px 0;
    border-bottom: 1px solid #efefef;
}
.widget_recent_entries ul li:first-child{
    border-top: 1px solid #efefef;
}
.widget_recent_entries ul li a{
    color: #3db28e !important;
    font: 500 14px/18px Ubuntu, Helvetica, sans-serif;
}
.widget_recent_entries ul li a:hover{
    opacity: 0.6;
}
.widget_recent_entries .pt-cv-readmore.btn{
    margin: 20px 0;
}
#secondary .pt-cv-readmore.btn{
    margin-top: 10px;
}
#user-name{
    color: #00b898;
    font-size: 18px;
    margin: 10px 20px -2px 0;
    text-transform: none;
    vertical-align: middle;
    cursor: pointer;
    background: url("../images/select-arrow.png") right center no-repeat;
    padding: 0 15px 0 0;
}
#settings-name{
    color: #00b898;
}
.head-drop{
  padding: 20px;
  border: 1px solid #ebebeb;
  background: #fff;
  max-width: 250px;
  width: 100%;
  box-sizing: border-box;
  position: absolute;
  right: 104px;
  top: 50px;
}
.head-drop:after{
  content: "";
  background: url("../images/drop-arr.jpg") no-repeat;
  width: 21px;
  height: 10px;
  position: absolute;
  top: -10px;
  right: 30px;
}
.head-drop .av{
  display: flex;
  align-items: center;
  padding: 0 0 15px;
}
.head-drop .av img{
  display: inline-block;
  vertical-align: middle;
  width: 65px;
  height: 65px;
  border-radius: 100%;
  padding: 0 0px 0 0;
  margin-right: 15px;
    box-sizing: content-box;
}
.head-drop .av div{
  display: inline-block;
  vertical-align: middle;
}
.head-drop .settings{
  border-bottom: 1px solid #ebebeb;
}
.head-drop .settings a{
  color: #000;
  font: 14px/18px Ubuntu,Helvetica,sans-serif;
  display: table;
  margin: 0 0 10px;
}
.head-drop .settings a:hover{
  opacity: 0.7;
}
.head-drop .links{
  margin: 15px 0 0;
}
.head-drop .links a{
  color: #000;
  font: 14px/18px Ubuntu,Helvetica,sans-serif;
  display: block;
  margin: 0 0 10px;
  height: 40px;
  border-radius: 20px;
  font: 700 14px/36px Ubuntu,Helvetica,sans-serif;
  text-transform: uppercase;
  text-align: center;
}
.head-drop .links a:nth-child(1){
  border: 2px solid #d5731a;
}
.head-drop .links a:nth-child(2){
  border: 2px solid #f9b61e;
}
.head-drop .links a:nth-child(3){
  border: 2px solid #3db28e;
}
.head-drop .links a:hover{
  opacity: 0.7;
  text-decoration: none;
}
#mob-login  {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #ccc;
}

.main-details-img {
    margin-top: 50px;
}
.main-details-img img {
    width: auto;
    max-width: 100%;
    height: auto;
}

.head-drop.show-menu {
    display: block !important;
}
@media (max-width: 1600px) {

}
@media (max-width: 1240px) {
.footer::before {
    display: none;
}
.back{
    max-width: 100%;
    margin: 0 15px 20px;
}
.navbar-default .navbar-nav {
    float: left;
    margin: 15px 0;
    max-width: 100%;
    text-align: left;
    width: 100%;
}
.brands-block {
    padding: 40px 30px 20px;
}
.brands-block p {
    display: block;
    padding: 0 0 20px;
}
.brands-block ul li {
    margin: 20px;
}
.footer {
    padding: 60px 30px;
}
}
@media (max-width: 1000px) {
.footer .info {
    display: flex;
    justify-content: space-between;
    padding: 0 0 30px;
    width: 100%;
}
.footer .info::after {
    display: none;
}
.footer .info .logo {
    margin: 0;
}
.footer .info > div {
    width: auto !important;
}
.footer .links {
    width: 100%;
}
}
@media (max-width: 991px){
    #secondary { margin-bottom: 30px; }
}
@media (max-width: 767px) {

    .details .site-header, .details .breads {
        display: none;
    }
    .details .featured-image {
        margin-top: 0px !important;
    }

.navbar-header {
    padding: 0 15px;
}
.navbar-toggle {
    margin: 5px 0 0;
}
.sign-in {
    display: none;
    float: left;
    margin: 0;
}
.navbar-collapse {
    box-sizing: border-box;
    margin: 0 !important;
    width: 100%;
}
.navbar-default .navbar-nav li {
    display: block;
}
.pt-cv-wrapper .pull-left {
    float: none;
}
    .main-details-img {
        margin-top: 10px;
    }
}
@media only screen and (max-width: 700px) {
.footer .info .logo {
    display: inline-block;
    float: none;
    margin: 0 auto 20px;
}
.footer .info {
    display: block;
    text-align: center;
}
.footer .info > div {
    float: none;
    width: 100% !important;
}
.footer .links {
    display: block;
    text-align: center;
}
.footer .links .col {
    padding: 0 0 30px;
    width: 100%;
}
.footer .links .col:last-child {
    padding: 0;
}
.footer .f-bot {
    text-align: center;
}
.footer .f-bot .social {
    float: none;
    width: 100%;
}
.footer .f-bot .social li {
    margin: 0 5px 20px;
}
.footer .f-bot .copy {
    float: none;
    width: 100%;
}
}
@media (max-width: 500px) {
    .navbar-brand {
        background-position: center center;
        background-size: 100% auto;
        width: 200px;
    }
}

@media (min-width: 768px) {
    #mob-login, #mob-regis {
        display: none;
    }
}

.def-btn.logout-button {
	margin-top: 13px;
}


/* ---- */
.white {
    color: #fff;
}
#navigation_menu{
    max-width: 1070px;
    padding: 0;
}
.navbar > .container .navbar-brand,
.navbar > .container-fluid .navbar-brand{
    margin: 0;
    width: 228px;
    height: 45px;
    background-size: 100% auto;
}

.navbar-default{
    padding: 25px 0;
}
@media only screen and (max-width: 767px) {
    .navbar-default{
        padding-left: 15px;
        padding-right: 15px;
    }
}
.tag-block {
    display: flex;
    flex-wrap: wrap;
    margin-top: 10px;
    margin-bottom: 10px;
}

.tag {
    padding: 5px;
    margin-right: 3px;
    margin-bottom: 3px;
    border: 1px solid #eee;
    cursor: pointer;
    font-size: 14px;
}

.tag-header {
    color: #797979;
}

.home .sidebar {
    /*width: 20%;*/
}

.sampleTag {
    min-width: 100px;
    height: 100%;
    line-height: 20px;
    display: inline-flex;
    z-index: 1;
    align-items: center;
    margin-left: -13px;
    border-left: 1px solid #b0b0b0;
    font-size: 17px;
    color: #000000 !important;
    padding-left: 10px;
    font: 500 14px/1 Ubuntu,Helvetica,sans-serif;
}

.sampleTag:hover {
    color: #3db28e;
}

.home .breads > section {
    display: flex;
}

.widget_recent_entries ul li.list-tag {
    padding: 3px 0px;
}

.handle-container {
    margin: 0px 5%;
}

.news-block {
    display: flex;
    flex-direction: column;
    width: calc(100% - 400px);
}

@media only screen and (max-width: 1024px) {
    .pt-cv-ifield {
        flex-direction: column !important;
        align-items: center;
    }

    .news-block {
        width: 100%;
    }

    a._self.pt-cv-href-thumbnail.pt-cv-thumb-default {
        margin-bottom: 20px;
    }
}

.site-content .row {
    margin: 0px auto;
    max-width: 1280px;
}