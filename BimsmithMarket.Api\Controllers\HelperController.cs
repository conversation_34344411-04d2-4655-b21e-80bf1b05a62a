﻿using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    public class HelperController : BaseApiController
    {
        private readonly IHelperService _helperService;
        public HelperController(IHelperService helperService)
        {
            _helperService = helperService;
        }

        [HttpGet]
        public async Task<IActionResult> GetStatsFromFile(string path)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _helperService.GetStatsFromFileAsync(unitOfWork, path));
            }
        }
    }
}