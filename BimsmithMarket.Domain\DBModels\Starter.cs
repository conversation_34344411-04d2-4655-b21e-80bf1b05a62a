﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.DBModels
{

    [Index(nameof(ForgeManufacturerId), Name = "IX_Starter_ForgeManufacturerId")]
    [Index(nameof(Name), Name = "IX_Starter_Name")]
    [Index(nameof(Brand), Name = "IX_Starter_Brand")]
    [Index(nameof(ManufacturerName), Name = "IX_Starter_ManufacturerName")]
    public class Starter
    {
        [Key]
        [StringLength(38)]
        public string Id { get; set; }

        [StringLength(38)]
        public string ForgeManufacturerId { get; set; }

        [StringLength(200)]
        public string Name { get; set; }

        [StringLength(200)]
        public string Brand { get; set; }

        [StringLength(200)]
        public string ManufacturerName { get; set; }

        public float Weight { get; set; }

        public StarterOrientation Orientation { get; set; }

        public string Preview { get; set; }

        public DateTime UtcDateCreated { get; set; }

        public DateTime? UtcDateModified { get; set; }

        public DateTime UpdateCacheTime { get; set; }

        public string STC { get; set; }

        public string FSTC { get; set; }

        public string RValue { get; set; }

        public string Fire { get; set; }

        public string UL { get; set; }

        public string IIC { get; set; }

        public string FIIC { get; set; }

        public string NFPA285 { get; set; }

        public bool Published { get; set; }

        public bool Staging { get; set; }

        public virtual ICollection<StarterProductLines> ProductLines { get; set; }

        public Starter()
        {
            ProductLines = new List<StarterProductLines>();
        }
    }

    public enum StarterOrientation
    {
        Undefined = 0,
        Walls = 1,
        Flooring = 2,
        Ceilings = 3,
        Roofing = 4
    }
}