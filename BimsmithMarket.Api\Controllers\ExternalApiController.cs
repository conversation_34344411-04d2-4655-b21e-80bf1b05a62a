using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Dto.ExternalApi;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    public class ExternalApiController : BaseApiController
    {
        private readonly IExternalApiService _externalApiService;
        private readonly IFeatureSettingService _featureSettingService;
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly IProductService _productService;
        private readonly IManufacturerBackupService _manufacturerBackupService;

        public ExternalApiController(
            IExternalApiService externalApiService,
            IFeatureSettingService featureSettingService,
            IWebHostEnvironment webHostEnvironment,
            IProductService productService,
            IManufacturerBackupService manufacturerBackupService)
        {
            _externalApiService = externalApiService;
            _featureSettingService = featureSettingService;
            _webHostEnvironment = webHostEnvironment;
            _productService = productService;
            _manufacturerBackupService = manufacturerBackupService;
        }

        #region INEFAM
        [HttpGet]
#if !DEBUG
        [Authorize]
#endif
        [ActionName("Search")]
        public async Task<IActionResult> Search(
            string query = null,
            bool isMasterspec = false,
            bool onlyFree = false,
            string regionId = null,
            int offset = 0,
            int count = 10)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            var result = await _externalApiService.SearchProductsAsync(query, isMasterspec, onlyFree, count, offset, regionId, unitOfWork);
            return Ok(result);
        }

        [HttpGet]
#if !DEBUG
        [Authorize]
#endif
        [ActionName("DownloadProjectFiles")]
        public async Task<IActionResult> DownloadProjectFiles([Required] int productId, string fileName = "archive")
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            var blobZipFile = await _externalApiService.DownloadProjectFilesAsync(productId, Request, Request.Headers.Referer, Request.GetDisplayUrl(), unitOfWork);
            return File(await blobZipFile.OpenReadAsync(), "application/zip", $"{fileName}.zip");
        }

        [HttpGet]
#if !DEBUG
        [Authorize]
#endif
        [ActionName("DownloadTechnicalDocs")]
        public async Task<IActionResult> DownloadTechnicalDocs([Required] int productId, string fileName = "archive")
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            var blobZipFile = await _externalApiService.DownloadTechnicalDocsAsync(productId, Request, Request.Headers.Referer, Request.GetDisplayUrl(), unitOfWork);
            return File(await blobZipFile.OpenReadAsync(), "application/zip", $"{fileName}.zip");
        }

        [HttpGet]
#if !DEBUG
        [Authorize]
#endif
        [ActionName("TechnicalDocsMapping")]
        public async Task<IActionResult> TechnicalDocsMapping([Required] int productId)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _externalApiService.TechnicalDocsMappingAsync(productId, unitOfWork));
        }
        #endregion

        #region USG
        /// <summary>
        /// Add product from the external source.
        /// </summary>
        /// <param name="models">The model.</param>
        /// <returns></returns>
        [HttpPost]
        [Route("~/api/Product/AddExternal")]
        [ManufacturersAuthorize]
        public async Task<IActionResult> AddExternal([FromBody] AddExternalProduct[] models)
        {
            LogHelper.LogExternalApiCall(JsonConvert.SerializeObject(models), "api/Product/AddExternal");

            if (!await _featureSettingService.GetSettingStatusAsync(DbConstants.FeatureAddUpdateProductViaExternalApi))
                return BadRequest("The feature is disabled");

            //Create manufacturer info backup if backup for current date is not created already
            string manufacturersAccessConfigFile = Path.Combine(_webHostEnvironment.WebRootPath, "AllowedManufacturers.json");
            using (var unitOfWork = UnitOfWork.Create())
            {
                var validatedUserModel = _productService.ValidateUser(unitOfWork, Request.Host.ToString(), AllowedManufacturersModel.Create(manufacturersAccessConfigFile), Request);

                if (!await _manufacturerBackupService.ManufacturerHasBackupForCurrentDataAsync(validatedUserModel.Manufacturer.Id))
                    await _manufacturerBackupService.CreateManufacturerBackupAsync(validatedUserModel.Manufacturer.Id);
            }

            var result = await _productService.AddExternalAsync(
                    models,
                    Request.Host.ToString(),
                    AllowedManufacturersModel.Create(manufacturersAccessConfigFile),
                    Request);

            CacheHelper.ClearSpecificCache("*/api/Product/List*");
            CacheHelper.ClearSpecificCache("*/api/Product/AdminList*");

            if (result.Errors != null && result.Errors.Any())
            {
                foreach (var errorWithException in result.Errors.Where(x => x.Exception != null))
                {
                    errorWithException.ErrorMessage += errorWithException.Exception.GetAllMessages();
                }
            }
            return Ok(new
            {
                processedProducts = result.ProcessedProducts,
                errors = result.Errors != null ? result.Errors.Select(x => x.ErrorMessage).ToArray() : Array.Empty<string>()
            });
        }
        #endregion

        [HttpPost]
#if !DEBUG
        [Authorize(Roles = DbConstants.ExternalApiRole)]
#endif
        public async Task<IActionResult> UpdateProducts(ExternalApiUpdateProductsDto model)
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _externalApiService.InitProductsUpdateAsync(model, userId, unitOfWork));
        }
    }
}