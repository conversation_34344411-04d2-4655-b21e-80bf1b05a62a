﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class Detail : BaseEntity
    {
        public string Name { get; set; }

        public DetailOrientation Orientation { get; set; }

        public bool Interior { get; set; }

        public bool Exterior { get; set; }

        public int? DetailScaleId { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        public int? ManufacturerId { get; set; }

        public string Description { get; set; }

        public int? PhotoId { get; set; }

        public float ProductRating { get; set; }

        public float ContentRating { get; set; }

        public bool Published { get; set; }

        [ForeignKey("ManufacturerId")]
        public virtual Manufacturer Manufacturer { get; set; }

        [ForeignKey("PhotoId")]
        public virtual Photo Photo { get; set; }

        [ForeignKey("DetailScaleId")]
        public virtual DetailScale DetailScale { get; set; }

        public virtual ICollection<DetailDetailApplication> DetailDetailApplications { get; set; }

        public virtual ICollection<DetailMasterformat> DetailMasterformats { get; set; }

        public virtual ICollection<DetailRating> DetailRatings { get; set; }

        public virtual ICollection<DetailPhoto> DetailPhotos { get; set; }

        public virtual ICollection<DetailFile> DetailFiles { get; set; }

        public virtual ICollection<ProductDetail> ProductDetails { get; set; }

        public virtual ICollection<RelatedDetail> RelatedDetails { get; set; }

        public Detail()
        {
            DetailDetailApplications = new List<DetailDetailApplication>();
            DetailMasterformats = new List<DetailMasterformat>();
            DetailRatings = new List<DetailRating>();
            DetailPhotos = new List<DetailPhoto>();
            DetailFiles = new List<DetailFile>();
            ProductDetails = new List<ProductDetail>();
            RelatedDetails = new List<RelatedDetail>();
        }
    }

    public enum DetailOrientation
    {
        Plan = 1,
        Section = 2,
        Elevation = 3,
        ThreeDAxon = 4
    }
}