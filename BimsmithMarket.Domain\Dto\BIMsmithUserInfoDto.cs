﻿using System.Collections.Generic;
using System.Net;

namespace BIMsmithMarket.Domain.Dto
{
    public class BIMsmithUserInfoDto
    {
        public HttpStatusCode Code { get; set; }

        public string UserId { get; set; }

        public string FirstName { get; set; }

        public string LastName { get; set; }

        public string Email { get; set; }

        public List<string> Roles { get; set; }

        public string Streams { get; set; }

        public string UserType { get; set; }

        public string City { get; set; }

        public string State { get; set; }

        public string Country { get; set; }

        public string Company { get; set; }

        public string Image { get; set; }

        public string UsertypeOther { get; set; }

        public int? DefaultProjectLocation { get; set; }

        public List<BIMsmithUserInfoFileVersionsDto> DefaultFileVersions { get; set; }
    }

    public class BIMsmithUserInfoFileVersionsDto
    {
        public int ParentDataTypeId { get; set; }

        public List<int> ChildrenDataTypeIds { get; set; }
    }
}