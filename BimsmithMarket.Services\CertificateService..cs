﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Flurl;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class CertificateService : ICertificateService
    {
        private readonly ICacheService _cacheService;

        private readonly string _cacheKey = "CBOCertificates";

        public CertificateService(ICacheService cacheService)
        {
            _cacheService = cacheService;
        }

        public async Task<List<CertificateModel>> GetBackofficeCertificatesAsync()
        {
            if (_cacheService.Contains(_cacheKey))
                return _cacheService.Get<List<CertificateModel>>(_cacheKey);

            var httpClient = HttpClientFactory.GetClient();
            {
                Url url = new Url(ConfigurationHelper.GetValue("CBOBaseAddress"))
                              .AppendPathSegment("api/Certificate/List");

                using HttpResponseMessage result = await httpClient.GetAsync(url);
                if (result.IsSuccessStatusCode)
                {
                    List<CertificateModel> certificates = JsonConvert.DeserializeObject<List<CertificateModel>>(await result.Content.ReadAsStringAsync());
                    _cacheService.Set(_cacheKey, certificates, CacheConstants.OneDay);
                    return certificates;
                }
            }
            return new List<CertificateModel>();
        }
    }
}