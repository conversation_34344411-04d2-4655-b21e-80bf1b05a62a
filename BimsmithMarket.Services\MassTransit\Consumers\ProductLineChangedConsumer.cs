﻿using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models.MassTransit.Events;
using BIMsmithMarket.Services.HealthDashboardServices;
using BIMsmithMarket.Services.PaymentServices;
using BIMsmithMarket.Services.ProductServices;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.MassTransit.Consumers
{
    public class ProductLineChangedConsumer : IConsumer<ProductLineChangedEvent>
    {
        private readonly ProductService _productService = new ProductService(new PriceService(new FileService()), new MongoRepository<ProductMongoDto>(), new CacheService(), new SlackWebHook(), new HealthDashboardService(), new FileService());

        public async Task Consume(ConsumeContext<ProductLineChangedEvent> context)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            int[] productIds = await unitOfWork.ProductRepository.GetAll()
                .Where(x => x.ProductLineId == context.Message.ProductLineId)
                .Select(x => x.Id)
                .ToArrayAsync();

            foreach (int productId in productIds)
                await _productService.SaveProductToMongoAsync(productId, true, unitOfWork);
        }
    }
}