﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class HelpArticle
    {
        [Key]
        public int Id { get; set; }

        public string Name { get; set; }

        public string VanityUrl { get; set; }

        public string MetaDescription { get; set; }

        public string MetaKeywords { get; set; }

        public string Title { get; set; }

        public string HelpTags { get; set; }

        public bool FAQ { get; set; }

        public string HtmlBody { get; set; }

        public HelpCategoryStatus Status { get; set; }

        public int ThumbsUpCount { get; set; }

        public int ThumbsDownCount { get; set; }

        public int ViewCount { get; set; }

        public DateTime? PublishedDate { get; set; }

        public string CreatedById { get; set; }

        public DateTime CreatedDate { get; set; }

        public string ModifiedById { get; set; }

        public DateTime? ModifiedDate { get; set; }

        public virtual ICollection<HelpCategory> HelpCategories { get; set; }

        [ForeignKey("CreatedById")]
        public virtual ApplicationUser CreatedBy { get; set; }

        [ForeignKey("ModifiedById")]
        public virtual ApplicationUser ModifiedBy { get; set; }

        public HelpArticle()
        {
            HelpCategories = new List<HelpCategory>();
        }
    }
}