﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BIMsmithMarket.DataLayer.Migrations
{
    public partial class Add_RequestPricingUsers : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "RequestPricingUsers",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ManufacturerId = table.Column<int>(type: "int", nullable: false),
                    ProductId = table.Column<int>(type: "int", nullable: true),
                    CreatedById = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ModifiedById = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RequestPricingUsers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RequestPricingUsers_Manufacturers_ManufacturerId",
                        column: x => x.ManufacturerId,
                        principalTable: "Manufacturers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RequestPricingUsers_Products_ProductId",
                        column: x => x.ProductId,
                        principalTable: "Products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_RequestPricingUsers_Users_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "Users",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RequestPricingUsers_Users_ModifiedById",
                        column: x => x.ModifiedById,
                        principalTable: "Users",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_RequestPricingUsers_CreatedById",
                table: "RequestPricingUsers",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RequestPricingUsers_ManufacturerId",
                table: "RequestPricingUsers",
                column: "ManufacturerId");

            migrationBuilder.CreateIndex(
                name: "IX_RequestPricingUsers_ModifiedById",
                table: "RequestPricingUsers",
                column: "ModifiedById");

            migrationBuilder.CreateIndex(
                name: "IX_RequestPricingUsers_ProductId",
                table: "RequestPricingUsers",
                column: "ProductId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "RequestPricingUsers");
        }
    }
}
