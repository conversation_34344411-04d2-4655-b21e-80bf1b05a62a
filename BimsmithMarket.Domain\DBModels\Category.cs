﻿using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{

    [Index(nameof(Name), Name = "IX_Category_Name")]
    [Index(nameof(VanityUrl), Name = "IX_Manufacturer_VanityUrl")]
    public class Category : BaseEntity
    {
        public int? ParentCategoryId { get; set; }

        public bool IsRoot { get; set; }

        [StringLength(200)]
        public string Name { get; set; }

        public string IconUrl { get; set; }

        public string Keywords { get; set; }

        public int Status { get; set; }

        public string Synonyms { get; set; }

        public float Weight { get; set; }

        public string MetaDescription { get; set; }

        public string MetaKeywords { get; set; }

        public string MetaTitle { get; set; }

        public string Description { get; set; }

        public bool IncludeRevitPlugin { get; set; }

        public int? RevitPluginPhotoId { get; set; }

        public string BimsmithVanityUrl { get; set; }

        [ForeignKey("RevitPluginPhotoId")]
        public virtual Photo RevitPluginPhoto { get; set; }

        [StringLength(200)]
        public string VanityUrl { get; set; }

        /// ------------------------------------------

        [ForeignKey("ParentCategoryId")]
        public virtual Category ParentCategory { get; set; }

        public virtual ICollection<Category> Subcategories { get; set; }

        public virtual ICollection<Product> Products { get; set; }

        public virtual ICollection<ProductCategory> ProductCategories { get; set; }

        public virtual ICollection<CategoryKeyStat> CategoryKeyStats { get; set; }

        public virtual ICollection<CustomCategoryIcon> CustomCategoryIcons { get; set; }

        public Category()
        {
            Subcategories = new List<Category>();
            Products = new List<Product>();
            ProductCategories = new List<ProductCategory>();
            CategoryKeyStats = new List<CategoryKeyStat>();
            CustomCategoryIcons = new List<CustomCategoryIcon>();
        }
    }
}