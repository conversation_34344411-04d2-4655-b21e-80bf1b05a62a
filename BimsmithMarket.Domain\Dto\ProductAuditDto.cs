﻿using System.Collections.Generic;
using System.Linq;

namespace BIMsmithMarket.Domain.Dto
{
    public class ProductAuditDto
    {
        public int ProductId { get; set; }

        public string ProductName { get; set; }

        public string ManufacturerName { get; set; }

        public string CategoryName { get; set; }

        public IEnumerable<ProductFileAuditDto> ProductFiles { get; set; }

        public IEnumerable<IGrouping<string, ProductFileAuditDto>> ProductFileGroups { get; set; }
    }

    public class ProductFileAuditDto
    {
        public string FileTitle { get; set; }

        public string ProjectDataType { get; set; }
    }
}