﻿using BIMsmithMarket.Domain.DBModels;
using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Dto.DetailDto
{
    public class DetailFilterDto
    {
        public bool? Published { get; set; }

        public string Query { get; set; }

        public List<string> RegionIds { get; set; }

        public List<string> StateIds { get; set; }

        public List<int> ManufacturerIds { get; set; }

        public List<int> ProjectDataTypeIds { get; set; }

        public bool? Interior { get; set; }

        public bool? Exterior { get; set; }

        public List<DetailOrientation> Orientations { get; set; }

        public List<int> ApplicationIds { get; set; }

        public List<int> ExternalMasterformatIds { get; set; }

        public List<string> ExternalMasterformatCodes { get; set; }

        public List<int> AssociatedProductIds { get; set; }

        public List<int> DetailScaleIds { get; set; }

        public int Count { get; set; } = 10;

        public int Offset { get; set; } = 0;

        public bool UseRotation { get; set; }
    }
}