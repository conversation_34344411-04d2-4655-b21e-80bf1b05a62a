﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto.PluginFileDto;
using Mapster;
using System;
using System.Linq;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public static class PluginFileMap
    {
        public static void Register(TypeAdapterConfig config)
        {
            config.ForType<PluginFile, PluginFileVersionDto>()
                .Map(d => d.ReleaseDate, s => s.CreatedDate)
                .Map(d => d.Url, s => s.File.Url)
                .Map(d => d.Updates, s => s.Updates != null && s.Updates.Any() ? s.Updates.Split(new string[] { ";;" }, StringSplitOptions.None) : Array.Empty<string>());

            config.ForType<PluginFile, PluginFileVersionUpdateInternalDto>()
                .Map(d => d.ReleaseDate, s => s.CreatedDate)
                .Map(d => d.DownloadUrl, s => s.File.Url);

            config.ForType<PluginFileVersionUpdateInternalDto, PluginFileVersionUpdateDto>()
                .Map(d => d.Updates, s => s.Updates != null && s.Updates.Any() ? s.Updates.Split(new string[] { ";;" }, StringSplitOptions.None) : Array.Empty<string>());

            config.ForType<PluginFile, PluginFileVersionUpdateDto>()
                .Map(d => d.ReleaseDate, s => s.CreatedDate)
                .Map(d => d.DownloadUrl, s => s.File.Url)
                .Map(d => d.Updates, s => s.Updates != null && s.Updates.Any() ? s.Updates.Split(new string[] { ";;" }, StringSplitOptions.None) : Array.Empty<string>());

            config.ForType<PluginFile, EditPluginFileDto>()
                .Map(d => d.Updates, s => s.Updates != null && s.Updates.Any() ? s.Updates.Split(new string[] { ";;" }, StringSplitOptions.None) : Array.Empty<string>());

            config.ForType<EditPluginFileDto, PluginFile>()
                .Map(d => d.Updates, s => s.Updates != null && s.Updates.Any() ? string.Join(";;", s.Updates.Where(x => !string.IsNullOrEmpty(x))) : null);
        }
    }
}