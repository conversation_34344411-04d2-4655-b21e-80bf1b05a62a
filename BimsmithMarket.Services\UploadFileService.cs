﻿using Azure.Storage.Blobs.Specialized;
using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using FileSignatures;
using Mapster;
using Microsoft.AspNetCore.Http;
using MimeTypes;
using Serilog;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class UploadFileService : IUploadFileService
    {
        /// <summary>
        /// The octet file extentions
        /// </summary>
        private readonly List<string> octetFileExtentions = new List<string>() { "dwg", "dxf", "rfa", "rvt" };
        /// <summary>
        /// The file service
        /// </summary>
        private readonly IFileService _fileService;

        private readonly List<string> blockedFileExtensions = new List<string>() { "text/html" };

        private readonly FileFormatInspector _fileFormatInspector = new FileFormatInspector();

        public UploadFileService(IFileService fileService)
        {
            _fileService = fileService;
        }


        public async Task<Domain.Models.FileUploadResultModel> AddFileAsync(IFormFile formFile,
            string userId,
            string folderFiles,
            string previewUrl = null,
            string attachURL = null,
            bool allowAllMediaTypes = false,
            string customFileName = null)
        {
            var isUrlPresent = !string.IsNullOrWhiteSpace(attachURL);

            if (!Directory.Exists(folderFiles))
                Directory.CreateDirectory(folderFiles);

            AzureStorageService azureBlobProvider = new AzureStorageService(
                ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                ConfigurationHelper.GetValue("Environment"),
                bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")), 
                false);
            var filesContainer = azureBlobProvider.GetContainerByName(AzureStorageConstants.FilesContainer);

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                unitOfWork.BeginTransaction();

                string fileName = string.Empty;
                bool isUrlWithPdfFrames = false;
                if (!isUrlPresent)
                    fileName = !string.IsNullOrWhiteSpace(customFileName) ? customFileName: formFile.FileName.Replace("\"", string.Empty);
                else
                {
                    isUrlWithPdfFrames = _fileService.IsUrlWithPdfIframe(attachURL);
                    if (isUrlWithPdfFrames && attachURL.Contains(".html"))
                        fileName = Path.GetFileName(attachURL.Substring(0, attachURL.IndexOf(".html") - 1));
                    else
                    {
                        HttpResponseHeadersInfo headers = await WepApiProvider.GetHeadersAsync(attachURL);
                        fileName = headers.FileName;
                        if (string.IsNullOrWhiteSpace(fileName))
                            fileName = MimeTypeProvider.GetFileNameFromUrl(attachURL);
                    }
                }

                string mediaType = MimeTypeProvider.GetMimeType(fileName);

                if (!allowAllMediaTypes && blockedFileExtensions.Contains(mediaType))
                    throw new BlockedFileExtensionException("This file type is not allowed");

                Domain.DBModels.File dbFile = new Domain.DBModels.File
                {
                    FileName = fileName,
                    MediaType = mediaType,
                    CreatedById = userId,
                    CreatedDate = DateTime.UtcNow,
                    PreviewUrl = previewUrl,
                    NextSyncDateTime = DateTime.UtcNow.Date.AddMonths(1),
                    SyncStatusCode = (int)HttpStatusCode.OK,
                    UpdatesCount = 0,
                    SyncUrl = attachURL
                };
                unitOfWork.FileRepository.Insert(dbFile);
                await unitOfWork.SaveAsync();

                var blobName = dbFile.Id + Path.GetExtension(fileName);
                BlockBlobClient fileBlobUpload = filesContainer.GetBlockBlobClient(blobName);

                //Save local to create preview and then delete this local file
                string filePath = Path.Combine(folderFiles, blobName);

                if (isUrlPresent)
                {
                    try
                    {
                        //check if pdf is inside of iframe and if it is parse pdf document link
                        if (isUrlWithPdfFrames)
                            attachURL = _fileService.FindFileUrlForPdfFrame(attachURL);

                        if (!attachURL.StartsWith("http"))
                            attachURL = "http://" + attachURL;

                        if (attachURL.StartsWith("https://") && attachURL.Contains("deltabreez.com"))
                            attachURL = attachURL.Replace("https://", "http://");

                        int redirectsCount = 0;
                    StartRedirect:

                        if (attachURL.StartsWith("https://"))
                        {
                            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;
                            ServicePointManager.ServerCertificateValidationCallback += (sender, certificate, chain, sslPolicyErrors) => true;
                        }

                        if (attachURL[attachURL.Length - 1] == '/')
                            attachURL = attachURL.Remove(attachURL.Length - 1);

                        using (HttpClient httpClient = new())
                        {
                            httpClient.Timeout = TimeSpan.FromMinutes(5);

                            using (var fileResponse = await httpClient.GetAsync(attachURL))
                            {
                                if (fileResponse.StatusCode == HttpStatusCode.OK ||
                                    fileResponse.StatusCode == HttpStatusCode.Accepted ||
                                    fileResponse.StatusCode == HttpStatusCode.Found)
                                {
                                    using (Stream fileStream = await fileResponse.Content.ReadAsStreamAsync())
                                    {
                                        filePath = filePath.Replace(Path.GetFileName(filePath), _fileService.FixFileName(Path.GetFileName(filePath), "attachment"));
                                        using (var file = System.IO.File.Create(filePath))
                                            await fileStream.CopyToAsync(file);

                                        //If document is pdf check its metadata for file system path and replace it
                                        if (mediaType.Contains("pdf"))
                                            _fileService.CheckPdfMetadataAndSave(filePath);

                                        if (mediaType == "application/octet-stream" && octetFileExtentions.Where(a => fileName.EndsWith(a)).Count() == 0)
                                        {
                                            FileFormat format = _fileFormatInspector.DetermineFileFormat(fileStream);
                                            mediaType = format?.MediaType ?? fileResponse?.Content?.Headers?.ContentType?.MediaType ?? MimeTypeMap.GetMimeType(Path.GetExtension(attachURL));
                                            dbFile.MediaType = mediaType;

                                            var extention = MimeTypeProvider.MimeTypesInBytes.Keys.Where(x => fileName.Contains(x)).Max();
                                            extention = string.IsNullOrEmpty(extention) ? MimeTypeMap.GetExtension(mediaType) : extention;

                                            var newfilePath = filePath + extention;

                                            System.IO.File.Move(filePath, newfilePath);
                                            filePath = newfilePath;

                                            if (fileName == string.Empty)
                                                fileName = dbFile.Id.ToString();

                                            var newfileName = fileName + extention;
                                            dbFile.FileName = fileName = newfileName;
                                            fileBlobUpload = filesContainer.GetBlockBlobClient(dbFile.Id.ToString() + extention);
                                        }
                                    }
                                }
                                else if ((fileResponse.StatusCode == HttpStatusCode.Moved ||
                                    fileResponse.StatusCode == HttpStatusCode.MovedPermanently ||
                                    fileResponse.StatusCode == HttpStatusCode.Found) &&
                                    !string.IsNullOrWhiteSpace(fileResponse.Headers.Location?.ToString()) &&
                                    redirectsCount < 3)
                                {
                                    redirectsCount++;
                                    attachURL = fileResponse.Headers.Location?.ToString();
                                    fileResponse.Dispose();
                                    goto StartRedirect;
                                }
                                else
                                {
                                    throw new Exception("Bad URL response");
                                }
                            }
                        }
                    }
                    catch (Exception)
                    {
                        dbFile.SyncStatusCode = 500;
                        unitOfWork.FileRepository.Edit(dbFile);
                        await unitOfWork.SaveAsync();
                    }
                }
                else
                {
                    using (var fileStream = formFile.OpenReadStream())
                    {
                        if (mediaType.StartsWith("image") || Constants.ThumbnailMediaTypes.Contains(mediaType))
                        {
                            using (var file = System.IO.File.Create(filePath))
                            {
                                await fileStream.CopyToAsync(file);
                            }
                            //If document is pdf check its metadata for file system path and replace it
                            if (mediaType.Contains("pdf"))
                                _fileService.CheckPdfMetadataAndSave(filePath);
                        }
                        else
                        {
                            await fileBlobUpload.UploadAsync(fileStream);
                            dbFile.Url = fileBlobUpload.Uri.ToString();
                            dbFile.FileSize = fileStream.Length;
                            dbFile.CheckSum = HashProvider.CalculateMD5Hash(fileStream);
                        }
                    }
                }

                if (mediaType.StartsWith("image") || Constants.ThumbnailMediaTypes.Contains(mediaType))
                {
                    var thumbnail = ThumbnailProvider.GetAndSaveThumbnail(filePath, mediaType, dbFile.Id);
                    if (thumbnail.HasError) //Upalod preview on file server
                        Log.Error(thumbnail.Error);
                    else
                    {
                        var previewBlobName = Path.GetFileName(thumbnail.FilePath);
                        BlockBlobClient previewBlobUpload = filesContainer.GetBlockBlobClient(previewBlobName);
                        using (FileStream fs = System.IO.File.OpenRead(thumbnail.FilePath))
                            await previewBlobUpload.UploadAsync(fs);
                        dbFile.PreviewUrl = previewBlobUpload.Uri.ToString();
                        System.IO.File.Delete(thumbnail.FilePath);//delete local file
                    }

                }

                if (isUrlPresent || mediaType.StartsWith("image") || Constants.ThumbnailMediaTypes.Contains(mediaType))
                {
                    using (FileStream fs = System.IO.File.OpenRead(filePath))
                        await fileBlobUpload.UploadAsync(fs);
                    dbFile.Url = fileBlobUpload.Uri.ToString();

                    FileInfo fi = new FileInfo(filePath);

                    dbFile.FileSize = fi.Length;
                    using (StreamReader streamReader = new StreamReader(filePath))
                        dbFile.CheckSum = HashProvider.CalculateMD5Hash(streamReader.BaseStream);

                    System.IO.File.Delete(filePath);//delete local file
                }

                unitOfWork.FileRepository.Edit(dbFile);
                await unitOfWork.SaveAsync();

                unitOfWork.CommitTransaction();

                return new Domain.Models.FileUploadResultModel()
                {
                    Id = dbFile.Id,
                    FileName = dbFile.FileName,
                    FileSize = dbFile.FileSize,
                    MimeType = dbFile.MediaType,
                    Preview = dbFile.PreviewUrl,
                    Url = dbFile.Url
                };
            }
        }

        public async Task<object> UpdateFileWithUrlAsync(int fileId)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var azureBlobProvider = new AzureStorageService(
                    ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                    ConfigurationHelper.GetValue("Environment"),
                    bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                    false);
                var filesContainer = azureBlobProvider.GetContainerByName(AzureStorageConstants.FilesContainer);

                unitOfWork.BeginTransaction();

                var file = unitOfWork.FileRepository.GetAll().FirstOrDefault(f => f.Id == fileId);
                if (file == null)
                    throw new Exception("File not found");

                file.SyncStatus = SyncFileStatus.Free;

                var attachUrl = file.SyncUrl;

                if (string.IsNullOrWhiteSpace(attachUrl))
                    throw new Exception("Sync Url is empty");

                if (!attachUrl.StartsWith("http"))
                    attachUrl = "http://" + attachUrl;

                try
                {
                    await _fileService.DownloadFileFromUrlAsync(file, attachUrl, filesContainer, unitOfWork);
                }
                catch (Exception e)
                {
                    if (e is WebException && ((WebException)e).Response is HttpWebResponse)
                    {
                        file.SyncStatusCode = (int)((HttpWebResponse)((WebException)e).Response).StatusCode;
                    }
                    else
                    {
                        file.SyncStatusCode = (int)HttpStatusCode.NotFound;
                    }
                    file.SyncStatus = SyncFileStatus.Free;
                    file.UpdatesCount++;
                    file.NextSyncDateTime = DateTime.UtcNow.Date.AddMonths(1);
                }
                finally
                {
                    unitOfWork.FileRepository.Edit(file);
                    await unitOfWork.SaveAsync();
                    unitOfWork.CommitTransaction();
                }

                return new
                {
                    syncStatusCode = file.SyncStatusCode,
                    updatesCount = file.UpdatesCount
                };
            }
        }

        public async Task<FileUploadResultModel> FileLocalAsync(IFormFile formFile,
            string startLinkToFile,
            string userId,
            string folderFiles,
            string previewUrl
            )
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                unitOfWork.BeginTransaction();

                string fileName = formFile.FileName.Replace("\"", string.Empty);
                string mediaType = formFile.ContentType;

                Domain.DBModels.File dbFile = new Domain.DBModels.File
                {
                    FileName = fileName,
                    MediaType = mediaType,
                    CreatedById = userId,
                    CreatedDate = DateTime.UtcNow
                };
                unitOfWork.FileRepository.Insert(dbFile);
                await unitOfWork.SaveAsync();

                using (var fileStream = formFile.OpenReadStream())
                {
                    if (!Directory.Exists(folderFiles))
                        Directory.CreateDirectory(folderFiles);

                    string filePath = Path.Combine(folderFiles, dbFile.Id.ToString() + Path.GetExtension(fileName));

                    using (var file = System.IO.File.Create(filePath))
                    {
                        await fileStream.CopyToAsync(file);
                        dbFile.FileSize = fileStream.Length;
                    }

                    dbFile.Url = string.Concat(startLinkToFile, "/", dbFile.Id);

                    var thumbnail = ThumbnailProvider.GetAndSaveThumbnail(filePath, mediaType, dbFile.Id);
                    if (thumbnail.HasError == false)
                        dbFile.PreviewUrl = string.Concat(startLinkToFile, "/", dbFile.Id, "?type=preview");
                    else
                        dbFile.PreviewUrl = previewUrl;

                    unitOfWork.FileRepository.Edit(dbFile);
                    await unitOfWork.SaveAsync();

                    unitOfWork.CommitTransaction();

                    return new FileUploadResultModel
                    {
                        Id = dbFile.Id,
                        FileName = dbFile.FileName,
                        FileSize = dbFile.FileSize,
                        MimeType = dbFile.MediaType,
                        Url = dbFile.Url,
                        Preview = dbFile.PreviewUrl
                    };
                }
            }
        }

        public async Task<PhotoDto> PhotoAsync(IFormFile formFile,
            string userId,
            bool suppressAlphaChannel = true,
            string attachURL = null,
            bool isRevitIcon = false)
        {
            var isUrlPresent = !string.IsNullOrWhiteSpace(attachURL);

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                unitOfWork.BeginTransaction();

                string fileName = string.Empty;
                if (isUrlPresent)
                {
                    if (!attachURL.StartsWith("http"))
                        attachURL = "http://" + attachURL;
                    if (attachURL[attachURL.Length - 1] == '/')
                        attachURL = attachURL.Remove(attachURL.Length - 1);
                    fileName = Path.GetFileName(attachURL);
                }
                else
                    fileName = formFile.FileName.Replace("\"", string.Empty);

                string fileExtension = Path.GetExtension(fileName).ToLower();

                suppressAlphaChannel = suppressAlphaChannel && fileExtension != ".png";

                Photo photo = new Photo();
                photo.CreatedById = userId;
                photo.CreatedDate = DateTime.UtcNow;
                photo.Name = fileName;
                photo.UploadUrl = attachURL?.Trim();
                unitOfWork.PhotoRepository.Insert(photo);

                unitOfWork.Save();

                Stream imageStream = new MemoryStream();
                if (isUrlPresent)
                {
                    if (attachURL.StartsWith("https://"))
                    {
                        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;
                        ServicePointManager.ServerCertificateValidationCallback += (sender, certificate, chain, sslPolicyErrors) => true;
                    }

                    int redirectsCount = 0;
                StartRedirect:
                    using (HttpClient httpClient = new())
                    {
                        using (var photoResponse = await httpClient.GetAsync(attachURL))
                        {
                            if (photoResponse.StatusCode == HttpStatusCode.OK || photoResponse.StatusCode == HttpStatusCode.Accepted)
                            {
                                using (Stream photoResponseStream = await photoResponse.Content.ReadAsStreamAsync())
                                {
                                    photoResponseStream.CopyTo(imageStream);
                                }
                                photo.SyncStatusCode = (int)HttpStatusCode.OK;
                                photo.UpdatesCount = 0;
                                unitOfWork.PhotoRepository.Edit(photo);
                                unitOfWork.Save();
                            }
                            else if ((photoResponse.StatusCode == HttpStatusCode.Moved || photoResponse.StatusCode == HttpStatusCode.MovedPermanently || photoResponse.StatusCode == HttpStatusCode.Found) &&
                                !string.IsNullOrWhiteSpace(photoResponse.Headers.Location?.ToString()) &&
                                redirectsCount < 1)
                            {
                                redirectsCount++;
                                attachURL = photoResponse.Headers.Location?.ToString();
                                photoResponse.Dispose();
                                goto StartRedirect;
                            }
                            else
                            {
                                throw new Exception("Bad URL response");
                            }
                        }
                    }
                }
                else
                {
                    using (var fileStream = formFile.OpenReadStream())
                    {
                        fileStream.CopyTo(imageStream);
                    }
                }
                if (imageStream.Length != 0)
                {
                    await PhotoProvider.CreateProductPhotosAsync(photo, imageStream, fileExtension, null, suppressAlphaChannel, isRevitIcon);

                    unitOfWork.PhotoRepository.Edit(photo);
                    unitOfWork.Save();

                    imageStream.Dispose();
                    unitOfWork.CommitTransaction();

                    return photo.Adapt<PhotoDto>();
                }
                else
                {
                    throw new Exception("Content missing");
                }
            }
        }

        public async Task<object> PhotoLocalAsync(IFormFile formFile,
            string startLinkToLogo,
            string userId,
            string folderImages)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                unitOfWork.BeginTransaction();

                Photo photo = new Photo();
                photo.CreatedById = userId;
                photo.CreatedDate = DateTime.UtcNow;
                photo.SyncStatusCode = (int)HttpStatusCode.OK;
                photo.UpdatesCount = 0;
                unitOfWork.PhotoRepository.Insert(photo);
                await unitOfWork.SaveAsync();

                using (var fileStream = formFile.OpenReadStream())
                {
                    using (Image originalImage = Image.FromStream(fileStream))
                    {
                        if (!Directory.Exists(folderImages))
                            Directory.CreateDirectory(folderImages);

                        var bigPhotoPath = Path.Combine(folderImages, string.Format("{0}_b.jpg", photo.Id));
                        var smallPhotoPath = Path.Combine(folderImages, string.Format("{0}_s.jpg", photo.Id));

                        double scaleBig = Math.Max(originalImage.Height, originalImage.Width) / 1000d; //max side is 1200px for full image
                        double scaleSmall = Math.Max(originalImage.Height, originalImage.Width) / 200d; //max side is 200px for preview image

                        Image smallBig = null;

                        if (scaleBig > 1) // need to reduce size of image
                            smallBig = new Bitmap(originalImage, new Size((int)(originalImage.Width / scaleBig), (int)(originalImage.Height / scaleBig)));
                        else
                            smallBig = new Bitmap(originalImage);

                        Image smallImage = new Bitmap(originalImage, new Size((int)(originalImage.Width / scaleSmall), (int)(originalImage.Height / scaleSmall)));

                        ImageCodecInfo jgpEncoder = PhotoProvider.GetEncoder(ImageFormat.Jpeg);

                        // Create an Encoder object based on the GUID
                        // for the Quality parameter category.
                        Encoder myEncoder = Encoder.Quality;

                        // Create an EncoderParameters object.
                        // An EncoderParameters object has an array of EncoderParameter
                        // objects. In this case, there is only one
                        // EncoderParameter object in the array.
                        EncoderParameter myEncoderParameter = new EncoderParameter(myEncoder, 90L);
                        EncoderParameters myEncoderParameters = new EncoderParameters(1);
                        myEncoderParameters.Param[0] = myEncoderParameter;

                        smallBig.Save(bigPhotoPath, jgpEncoder, myEncoderParameters);
                        smallImage.Save(smallPhotoPath, jgpEncoder, myEncoderParameters);

                        smallBig.Dispose();
                        smallImage.Dispose();

                        var resultJson = new
                        {
                            id = photo.Id,
                            small = string.Concat(startLinkToLogo, "/", photo.Id, "?s=s"),
                            big = string.Concat(startLinkToLogo, "/", photo.Id, "?s=b")
                        };

                        photo.SmallImgUrl = resultJson.small;
                        photo.OriginalImgUrl = resultJson.big;
                        unitOfWork.PhotoRepository.Edit(photo);
                        await unitOfWork.SaveAsync();

                        unitOfWork.CommitTransaction();

                        return resultJson;
                    }
                }
            }
        }

        public async Task<object> ManufacturerFileAsync(IFormFile formFile,
            string userId,
            int manufacturerId
            )
        {

            AzureStorageService azureBlobProvider = new AzureStorageService(
                ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                ConfigurationHelper.GetValue("Environment"),
                bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                false);
            var filesContainer = azureBlobProvider.GetContainerByName(AzureStorageConstants.FilesContainer);
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                unitOfWork.BeginTransaction();

                var manufacturer = unitOfWork.ManufacturerRepository.GetById(manufacturerId);
                if (manufacturer == null)
                    throw new Exception("Cannot find manufacturer with id: " + manufacturerId);

                string fileName = formFile.FileName.Replace("\"", string.Empty);
                string mediaType = MimeTypeProvider.GetMimeType(fileName);

                if (blockedFileExtensions.Contains(mediaType))
                    throw new Exception("This file type is not allowed");

                Domain.DBModels.File dbFile = new Domain.DBModels.File
                {
                    FileName = fileName,
                    MediaType = mediaType,
                    CreatedById = userId,
                    NextSyncDateTime = DateTime.UtcNow.AddYears(100),
                    CreatedDate = DateTime.UtcNow
                };
                unitOfWork.FileRepository.Insert(dbFile);
                unitOfWork.Save();

                var blobName = dbFile.Id.ToString() + Path.GetExtension(fileName);
                BlockBlobClient fileBlobUpload = filesContainer.GetBlockBlobClient(blobName);

                using (var fileStream = formFile.OpenReadStream())
                {
                    await fileBlobUpload.UploadAsync(fileStream);
                    dbFile.Url = fileBlobUpload.Uri.ToString();
                }

                unitOfWork.FileRepository.Edit(dbFile);
                unitOfWork.Save();

                var manufacturerAdditionalFile = new ManufacturerAdditionalFile
                {
                    ManufacturerId = manufacturer.Id,
                    FileId = dbFile.Id,
                    CreatedDate = DateTime.UtcNow,
                    CreatedById = userId,
                    IsAttachment = true
                };
                unitOfWork.ManufacturerAdditionalFileRepository.Insert(manufacturerAdditionalFile);
                unitOfWork.Save();
                unitOfWork.CommitTransaction();

                return new
                {
                    id = dbFile.Id,
                    fileName,
                    downloadUrl = dbFile.Url
                };
            }
        }

        public async Task<FileUploadResultModel> TempFileAsync(IFormFile formFile,
            string userId,
            string startLinkToFile,
            string folderFiles)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                unitOfWork.BeginTransaction();

                string fileName = formFile.FileName.Replace("\"", string.Empty);
                string mediaType = formFile.ContentType;

                var dbFile = new Domain.DBModels.File();
                dbFile.FileName = fileName;
                dbFile.MediaType = mediaType;
                dbFile.CreatedById = userId;
                dbFile.CreatedDate = DateTime.UtcNow;
                dbFile.NextSyncDateTime = DateTime.MaxValue;
                unitOfWork.FileRepository.Insert(dbFile);
                await unitOfWork.SaveAsync();

                using (var fileStream = formFile.OpenReadStream())
                {
                    if (!Directory.Exists(folderFiles))
                        Directory.CreateDirectory(folderFiles);

                    string filePath = Path.Combine(folderFiles, dbFile.Id.ToString() + Path.GetExtension(fileName));

                    using (var file = System.IO.File.Create(filePath))
                    {
                        await fileStream.CopyToAsync(file);
                        dbFile.FileSize = fileStream.Length;
                    }

                    dbFile.Url = string.Concat(startLinkToFile, "/", dbFile.Id);

                    unitOfWork.FileRepository.Edit(dbFile);
                    await unitOfWork.SaveAsync();

                    var resultJson = new FileUploadResultModel
                    {
                        Id = dbFile.Id,
                        FileName = dbFile.FileName,
                        FileSize = dbFile.FileSize,
                        MimeType = dbFile.MediaType,
                        Url = dbFile.Url,
                        Preview = dbFile.PreviewUrl
                    };

                    unitOfWork.CommitTransaction();

                    return resultJson;
                }
            }
        }

        public async Task<FileUploadResultModel> AttachmentFileAsync(IFormFile formFile, string previewUrl)
        {
            AzureStorageService azureBlobProvider = new AzureStorageService(
                ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                ConfigurationHelper.GetValue("Environment"),
                bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                false);
            var filesContainer = azureBlobProvider.GetContainerByName(AzureStorageConstants.AttachmentsContainer);

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                unitOfWork.BeginTransaction();

                string fileName = formFile.FileName.Replace("\"", string.Empty);

                string mediaType = "application/octet-stream";
                try
                {
                    mediaType = formFile.ContentType;
                }
                catch (Exception e)
                {
                    Log.Error(e.Message, e);
                }

                Domain.DBModels.File dbFile = new Domain.DBModels.File
                {
                    FileName = fileName,
                    MediaType = mediaType,
                    CreatedById = DbConstants.AdminUserId,
                    CreatedDate = DateTime.UtcNow,
                    NextSyncDateTime = DateTime.MaxValue,
                    PreviewUrl = previewUrl
                };
                unitOfWork.FileRepository.Insert(dbFile);
                await unitOfWork.SaveAsync();

                using (var fileStream = formFile.OpenReadStream())
                {
                    var blobName = dbFile.Id.ToString() + Path.GetExtension(fileName);
                    BlockBlobClient fileBlobUpload = filesContainer.GetBlockBlobClient(blobName);

                    await fileBlobUpload.UploadAsync(fileStream);

                    dbFile.Url = fileBlobUpload.Uri.ToString();
                    dbFile.FileSize = fileStream.Length;

                    unitOfWork.FileRepository.Edit(dbFile);
                    await unitOfWork.SaveAsync();

                    var resultJson = new FileUploadResultModel
                    {
                        Id = dbFile.Id,
                        FileName = dbFile.FileName,
                        FileSize = dbFile.FileSize,
                        MimeType = dbFile.MediaType,
                        Url = dbFile.Url,
                    };

                    unitOfWork.CommitTransaction();

                    return resultJson;
                }
            }
        }

        public async Task<FileUploadResultModel> UploadStyleFileAsync(IFormFile formFile, string userId, string folderFiles)
        {
            var styleFileLimit = 4194304; //4 MiB
            var acceptedTypes = new List<string> { "text/css" };

            var fileName = formFile.FileName.Replace("\"", string.Empty);
            var mediaType = formFile.ContentType ?? MimeTypeProvider.GetMimeType(fileName);

            if (!acceptedTypes.Contains(mediaType))
                throw new Exception("Only css accepted");

            var stream = formFile.OpenReadStream();
            if (stream.Length > styleFileLimit)
                throw new Exception("Too big style file size");

            var streamReader = new StreamReader(stream);
            var contentString = streamReader.ReadToEnd();
            stream.Position = 0;

            var detailedErrors = new List<string>();
            if (!ValidateStyleContent(contentString, detailedErrors))
                throw new Exception("Style file contains potential malicious content. "
                    + Environment.NewLine
                    + "Potential malicious tags: "
                    + string.Join(Environment.NewLine, detailedErrors));

            if (!Directory.Exists(folderFiles))
                Directory.CreateDirectory(folderFiles);

            var azureBlobProvider = new AzureStorageService(
                ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                ConfigurationHelper.GetValue("Environment"),
                bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                false);
            var filesContainer = azureBlobProvider.GetContainerByName(AzureStorageConstants.FilesContainer);
            using (var unitOfWork = UnitOfWork.Create())
            {
                unitOfWork.BeginTransaction();

                var dbFile = new Domain.DBModels.File();
                dbFile.FileName = fileName;
                dbFile.MediaType = mediaType;
                dbFile.CreatedById = userId;
                dbFile.CreatedDate = DateTime.UtcNow;
                dbFile.NextSyncDateTime = DateTime.UtcNow.Date.AddMonths(1);
                dbFile.SyncStatusCode = (int)HttpStatusCode.OK;
                dbFile.UpdatesCount = 0;
                unitOfWork.FileRepository.Insert(dbFile);
                await unitOfWork.SaveAsync();

                var blobName = dbFile.Id + Path.GetExtension(fileName);
                var fileBlobUpload = filesContainer.GetBlockBlobClient(blobName);

                using (var fileStream = formFile.OpenReadStream())
                {
                    await fileBlobUpload.UploadAsync(fileStream);
                    dbFile.Url = fileBlobUpload.Uri.ToString();
                    dbFile.FileSize = fileStream.Length;
                    dbFile.CheckSum = HashProvider.CalculateMD5Hash(fileStream);
                }

                unitOfWork.FileRepository.Edit(dbFile);
                await unitOfWork.SaveAsync();

                var result = new FileUploadResultModel
                {
                    Id = dbFile.Id,
                    FileName = dbFile.FileName,
                    FileSize = dbFile.FileSize,
                    MimeType = dbFile.MediaType,
                    Url = dbFile.Url,
                    Preview = dbFile.PreviewUrl
                };

                unitOfWork.CommitTransaction();

                return result;
            }
        }

        #region private methods
        private bool ValidateStyleContent(string content, List<string> detailedErrors)
        {
            var scriptTagTemplate = new Regex(@"<script([\s\S]*?)script>");
            var scriptTagMatches = scriptTagTemplate.Matches(content);
            foreach (Match match in scriptTagMatches)
            {
                detailedErrors.Add(match.Value);
            }
            var blockedExtensions = new List<string> { ".js", ".php", ".jsp", ".asp", ".aspx" };
            var urlTagTemplate = new Regex(@"url\(([\s\S]*?)\)");
            var urlTagMatches = urlTagTemplate.Matches(content);
            foreach (Match match in urlTagMatches)
            {
                var extension = GetExtension(match.Value);
                if (blockedExtensions.Contains(extension))
                {
                    detailedErrors.Add(match.Value);
                }
            }
            if (detailedErrors.Any())
            {
                return false;
            }
            return true;
        }

        private string GetExtension(string urlString)
        {
            if (urlString.ToLower().Contains("data:image/jpeg;base64"))
            {
                return string.Empty;
            }
            if (urlString.Contains("."))
            {
                urlString = urlString.Substring(urlString.LastIndexOf('.'));
            }
            if (urlString.Contains("?"))
            {
                urlString = urlString.Substring(0, urlString.LastIndexOf('?'));
            }
            urlString = urlString.Replace(@"'", string.Empty).Replace("(", string.Empty).Replace(")", string.Empty);
            return urlString;
        }

        private string GetStyleFileTypeTitle(StyleFileType type)
        {
            var result = string.Empty;
            switch (type)
            {
                case StyleFileType.Microsite: result = "Microsite"; break;
                case StyleFileType.ProductPageViaMicrosite: result = "Product Page Within Microsite"; break;
            }
            return result;
        }
        #endregion
    }
}
