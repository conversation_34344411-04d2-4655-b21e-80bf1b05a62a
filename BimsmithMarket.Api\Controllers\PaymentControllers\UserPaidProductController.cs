﻿using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.PaymentsDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers.PaymentControllers
{
    [Route("api/UserPaidProduct")]
    public class UserPaidProductController : BaseApiController
    {
        private readonly IUserPaidProductService _userPaidProductService;

        public UserPaidProductController(IUserPaidProductService userPaidProductService)
        {
            _userPaidProductService = userPaidProductService;
        }

#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        [Route("Create")]
        public async Task<IActionResult> Create(UserPaidProductCreateByAdmin model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var inputModel = model.Adapt<UserPaidProductDto>();
                var result = await _userPaidProductService.AddEditProduct(inputModel, unitOfWork);
                return Ok(result);
            }
        }

#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpDelete]
        [Route("Delete")]
        public async Task<IActionResult> Delete([FromQuery] int Id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                await _userPaidProductService.DeleteAsync(Id, unitOfWork);
                return Ok();
            }
        }

#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        [Route("GetUserProducts/{userId}")]
        public async Task<IActionResult> GetUserProducts([FromQuery] string userId)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var result = await _userPaidProductService.GetUserProductsAsync(userId, unitOfWork);
                return Ok(result);
            }
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("CheckProductPresent")]
        public async Task<IActionResult> CheckUserProduct([FromQuery] int productId, string userId)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var result = await _userPaidProductService.CheckUserProductAsync(userId, productId, unitOfWork);
                return Ok(result);
            }
        }

        [HttpPost]
        [AllowAnonymous]
        [Route("BatchCheckProductPresent")]
        public async Task<IActionResult> CheckUserProduct(CheckUserProductRequestDto model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var result = await _userPaidProductService.BatchCheckUserProductAsync(model, unitOfWork);
                return Ok(result);
            }
        }

        /// <summary>
        /// Checks if user already bought specified products
        /// </summary>
        /// <param name="model">The model with product identifiers</param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        [Route("CheckProductsPresent")]
        public async Task<IActionResult> CheckUserProducts(EntityIdsDto model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
                return Ok(await _userPaidProductService.CheckUserProductsAsync(model, userId, unitOfWork));
            }
        }
    }
}