﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels.PaymentDbModels
{
    /// <summary>
    /// Many to many ralation between product and PaymentPlan 
    /// that shows product current prices with discounts and aditional payment palnes
    /// </summary>
    public class ProductPrice
    {
        [Key]
        public int Id { get; set; }

        [NotMapped]
        public decimal ProductAmount { get => Product?.Price?.Amount ?? 0; }

        public int PaymentPlanId { get; set; }
        [ForeignKey("PaymentPlanId")]
        public virtual PaymentPlan PaymentPlan { get; set; }


        public int ProductId { get; set; }
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }
    }

}