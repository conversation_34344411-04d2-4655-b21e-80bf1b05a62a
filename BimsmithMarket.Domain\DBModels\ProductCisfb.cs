﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class ProductCisfb
    {
        [Key]
        [Column(Order = 1)]
        public int ProductId { get; set; }

        [Key]
        [Column(Order = 2)]
        public int CisfbId { get; set; }

        public string CreatedById { get; set; }

        public DateTime CreatedDate { get; set; }

        /// ------------------------------------------
        [ForeignKey("CreatedById")]
        public virtual ApplicationUser CreatedBy { get; set; }

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }

        [ForeignKey("CisfbId")]
        public virtual Cisfb Cisfb { get; set; }
    }
}