﻿using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IRevitPluginService
    {
        Task<object> GetPluginItemsAsync(IUnitOfWork unitOfWork, ICategoryService categoryService);
        Task<object> GetPromotedManufacturers(IUnitOfWork unitOfWork, int count);
        Task<object> PromoteManufacturersAsync(IUnitOfWork unitOfWork, PromoteManufacturersModel manufacturers);
    }
}