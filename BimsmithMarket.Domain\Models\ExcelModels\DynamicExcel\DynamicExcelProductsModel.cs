﻿using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Models.ExcelModels.DynamicExcel
{
    using BIMsmithMarket.Domain.Models.ExcelModels.StaticExcel;

    public class DynamicExcelProductsModel
    {
        public string FileName { get; set; }

        public ICollection<DynamicExcelProductModel> Products { get; set; }

        public List<ProductQualityItemsModel> QualityItems { get; set; }

        public DynamicExcelProductsModel()
        {
            Products = new List<DynamicExcelProductModel>();
            QualityItems = new List<ProductQualityItemsModel>();
        }
    }
}