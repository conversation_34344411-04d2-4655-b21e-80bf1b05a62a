﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Configurations>Debug;Release;Dev;UAT</Configurations>
	</PropertyGroup>

	<PropertyGroup>
		<ServerGarbageCollection>true</ServerGarbageCollection>
		<ConcurrentGarbageCollection>true</ConcurrentGarbageCollection>
	</PropertyGroup>

	<ItemGroup>
	  <Content Remove="bundleconfig.json" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Models\" />
		<Folder Include="Views\" />
		<Folder Include="wwwroot\Bundles\" />
	</ItemGroup>

	<ItemGroup>
	  <None Include="bundleconfig.json" />
	  <None Include="wwwroot\Content\bootstrap.css" />
	  <None Include="wwwroot\Content\froala_editor.pkgd.min.css" />
	  <None Include="wwwroot\Content\froala_style.min.css" />
	  <None Include="wwwroot\favicon.ico.png" />
	  <None Include="wwwroot\favicon.png" />
	  <None Include="wwwroot\images\bimsmith.png" />
	  <None Include="wwwroot\images\brand-img01.png" />
	  <None Include="wwwroot\images\brand-img02.png" />
	  <None Include="wwwroot\images\brand-img03.png" />
	  <None Include="wwwroot\images\brand-img05.png" />
	  <None Include="wwwroot\images\drop-arr.jpg" />
	  <None Include="wwwroot\images\footer-bg.png" />
	  <None Include="wwwroot\images\footer-man.png" />
	  <None Include="wwwroot\images\header-bg.jpg" />
	  <None Include="wwwroot\images\home-ico.png" />
	  <None Include="wwwroot\images\img01.jpg" />
	  <None Include="wwwroot\images\logo-footer.png" />
	  <None Include="wwwroot\images\mail-ico.png" />
	  <None Include="wwwroot\images\select-arrow.png" />
	  <None Include="wwwroot\images\soc-fb.png" />
	  <None Include="wwwroot\images\soc-in.png" />
	  <None Include="wwwroot\images\soc-p.png" />
	  <None Include="wwwroot\images\soc-tw.png" />
	  <None Include="wwwroot\images\soc-youtube.png" />
	  <None Include="wwwroot\images\tel-ico.png" />
	  <None Include="wwwroot\Content\Site.css" />
	  <None Include="wwwroot\Content\style.css" />
	  <None Include="wwwroot\Scripts\bimsmith.js" />
	  <None Include="wwwroot\Scripts\bootstrap.js" />
	  <None Include="wwwroot\Scripts\bootstrap.min.js" />
	  <None Include="wwwroot\Scripts\jquery-1.10.2.intellisense.js" />
	  <None Include="wwwroot\Scripts\jquery-1.10.2.js" />
	  <None Include="wwwroot\Scripts\jquery-1.10.2.min.js" />
	  <None Include="wwwroot\Scripts\jquery-1.10.2.min.map" />
	  <None Include="wwwroot\Scripts\jquery-3.2.1.min.js" />
	  <None Include="wwwroot\Scripts\jquery.js" />
	  <None Include="wwwroot\Scripts\jquery.validate-vsdoc.js" />
	  <None Include="wwwroot\Scripts\jquery.validate.js" />
	  <None Include="wwwroot\Scripts\jquery.validate.min.js" />
	  <None Include="wwwroot\Scripts\main.js" />
	  <None Include="wwwroot\Scripts\modernizr-2.6.2.js" />
	  <None Include="wwwroot\Scripts\notifications.js" />
	  <None Include="wwwroot\Scripts\respond.js" />
	  <None Include="wwwroot\Scripts\respond.matchmedia.addListener.js" />
	  <None Include="wwwroot\Scripts\respond.matchmedia.addListener.min.js" />
	  <None Include="wwwroot\Scripts\respond.min.js" />
	</ItemGroup>

	<ItemGroup>
	  <PackageReference Include="BuildBundlerMinifier" Version="3.2.449" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\BIMsmithMarket.Core\BIMsmithMarket.Core.csproj" />
		<ProjectReference Include="..\BIMsmithMarket.DataLayer\BIMsmithMarket.DataLayer.csproj" />
		<ProjectReference Include="..\BIMsmithMarket.Domain\BIMsmithMarket.Domain.csproj" />
		<ProjectReference Include="..\BIMsmithMarket.Services\BIMsmithMarket.Services.csproj" />
	</ItemGroup>

</Project>
