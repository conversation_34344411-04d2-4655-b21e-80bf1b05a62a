﻿using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class NewsTarget : BaseEntity
    {
        public NewsTargetSite Site { get; set; }

        public int NewsId { get; set; }

        [ForeignKey("NewsId")]
        public virtual News News { get; set; }
    }

    public enum NewsTargetSite
    {
        Market = 0,
        Anguleris = 1
    }
}