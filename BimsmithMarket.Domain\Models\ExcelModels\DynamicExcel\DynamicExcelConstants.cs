﻿namespace BIMsmithMarket.Domain.Models.ExcelModels.DynamicExcel
{
    public static class DynamicExcelConstants
    {
        #region fields

        public const string IdField = "Id";

        public const string NameField = "Name";

        public const string ProductUrlField = "ProductUrl";

        public const string ULUrlField = "ULUrl";

        public const string ManufacturerIdField = "ManufacturerId";

        public const string CategoryIdField = "CategoryId";

        public const string ProductCategoryIdsField = "ProductCategoryIds";

        public const string ProductLineIdField = "ProductLineId";

        public const string PhotoIdField = "PhotoId";

        public const string PhotoURLsField = "PhotoURLs";

        public const string DescriptionField = "Description";

        public const string VideoUrlField = "VideoUrl";

        public const string VanityURLField = "VanityURL";

        public const string MetaTitleField = "MetaTitle";

        public const string MetaDescriptionField = "MetaDescription";

        public const string MetaKeywordsField = "MetaKeywords";

        public const string KeywordsField = "Keywords";

        public const string PublishToPartnerField = "PublishToPartner";

        public const string ExternalIdField = "ExternalId";

        public const string ForgeWallURLField = "ForgeWallURL";

        public const string ForgeFloorURLField = "ForgeFloorURL";

        public const string ForgeCeilingURLField = "ForgeCeilingURL";

        public const string ForgeRoofURLField = "ForgeRoofURL";

        public const string ProductCertificatesField = "ProductCertificates";

        public const string ProductCisfbsField = "ProductCisfbs";

        public const string ProductExternalMasterformatsField = "ProductExternalMasterformats";

        public const string ProductOmniclassesField = "ProductOmniclasses";

        public const string ProductUniclassesField = "ProductUniclasses";

        public const string ProductUniformatsField = "ProductUniformats";

        public const string ProductCutSheetUrlsField = "ProductCutSheetUrls";

        public const string PartSpecUrlsField = "PartSpecUrls";

        public const string ProductBrochureUrlsField = "ProductBrochureUrls";

        public const string ImageUrlsField = "ImageUrls";

        public const string SubmittalUrlsField = "SubmittalUrls";

        public const string CatalogUrlsField = "CatalogUrls";

        public const string WarrantyUrlsField = "WarrantyUrls";

        public const string TestingDataUrlsField = "TestingDataUrls";

        public const string SafetyDataSheetUrlsField = "SafetyDataSheetUrls";

        public const string InstallationGuideUrlsField = "InstallationGuideUrls";

        public const string HpdUrlsField = "HpdUrls";

        public const string MasterPartSpecUrlsField = "MasterPartSpecUrls";

        public const string PublishedField = "Published";

        public const string PublishedOnCustomMicrositeField = "PublishedOnCustomMicrosite";

        public const string StagingField = "Staging";

        public const string RevitFilesField = "RevitFiles";

        public const string WeightField = "Weight";

        public const string LocalizationField = "RegionIds";

        public const string EpdUrlsField = "EpdUrls";

        public const string AttachmentFileNamesField = "AttachmentFileNames";

        public const string ProjectFileNamesField = "ProjectFileNames";
        #endregion

        #region captions
        public const string IdCaption = "Id";

        public const string NameCaption = "Name";

        public const string ProductUrlCaption = "Product URL";

        public const string ULUrlCaption = "UL SPOT URL";

        public const string ManufacturerIdCaption = "ManufacturerId";

        public const string CategoryIdCaption = "CategoryId";

        public const string ProductCategoryIdsCaption = "Additional Category IDs";

        public const string ProductLineIdCaption = "ProductLineId";

        public const string PhotoIdCaption = "PhotoId";

        public const string PhotoURLsCaption = "Photo URLs";

        public const string DescriptionCaption = "Description";

        public const string VideoUrlCaption = "VideoUrl";

        public const string VanityURLCaption = "VanityURL";

        public const string MetaTitleCaption = "MetaTitle";

        public const string MetaDescriptionCaption = "MetaDescription";

        public const string MetaKeywordsCaption = "MetaKeywords";

        public const string KeywordsCaption = "Keywords";

        public const string PublishToPartnerCaption = "Publish To Partner";

        public const string ExternalIdCaption = "External Id";

        public const string ForgeWallURLCaption = "Forge Wall Url";

        public const string ForgeFloorURLCaption = "Forge Floor Url";

        public const string ForgeCeilingURLCaption = "Forge Ceiling Url";

        public const string ForgeRoofURLCaption = "Forge Roof Url";

        public const string ProductCertificatesCaption = "ProductCertificates";

        public const string ProductCisfbsCaption = "ProductCisfbs";

        public const string ProductExternalMasterformatsCaption = "ProductMasterformats";

        public const string ProductOmniclassesCaption = "ProductOmniclasses";

        public const string ProductUniclassesCaption = "ProductUniclasses";

        public const string ProductUniformatsCaption = "ProductUniformats";

        public const string ProductCutSheetUrlsCaption = "Product Cutsheet URLs";

        public const string PartSpecUrlsCaption = "3 Part Spec URLs";

        public const string ProductBrochureUrlsCaption = "Product Brochure URLs";

        public const string ImageUrlsCaption = "Image URLs";

        public const string SubmittalUrlsCaption = "Submittal URLs";

        public const string CatalogUrlsCaption = "Catalog URLs";

        public const string WarrantyUrlsCaption = "Warranty URLs";

        public const string TestingDataUrlsCaption = "Testing Data URLs";

        public const string SafetyDataSheetUrlsCaption = "Safety Data Sheet URLs";

        public const string InstallationGuideUrlsCaption = "Installation Guide URLs";

        public const string HpdUrlsCaption = "HPD URLs";

        public const string MasterPartSpecUrlsCaption = "3-Part Specification - Masterspec URLs";

        public const string PublishedCaption = "Published";

        public const string PublishedOnCustomMicrositeCaption = "Published On Custom Microsite";

        public const string StagingCaption = "Staging";

        public const string RevitFilesCaption = "Revit files (not for Upload)";

        public const string WeightCaption = "Product Weight";

        public const string LocalizationCaption = "Localization";

        public const string EpdUrlsCaption = "EPD Urls";

        public const string AttachmentFileNamesCaption = "Attachments (not for upload)";

        public const string ProjectFileNamesCaption = "Content files (not for upload)";
        #endregion
    }
}