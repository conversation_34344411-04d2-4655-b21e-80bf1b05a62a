﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Security.Claims;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// Help Controller
    /// </summary>
    [Route("api/[controller]/[action]/{id?}")]
    public class HelpController : BaseApiController
    {
        private readonly IHelpService _helpService;

        private readonly IUploadService _uploadService;

        public HelpController(
            IHelpService helpService,
            IUploadService uploadService)
        {
            _helpService = helpService;
            _uploadService = uploadService;
        }

        #region Help Category CRUD operations
        /// <summary>
        /// Add new Help category
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Authorize(Roles = DbConstants.AdminRole)]
        [HttpPost]
        [ActionName("AddCategory")]
        public async Task<IActionResult> AddCategory([FromBody] AddEditHelpCategoryModel model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
                if (!string.IsNullOrEmpty(model.VanityUrl))
                {
                    if (await unitOfWork.HelpCategoryRepository.GetAll().AnyAsync(a => a.VanityUrl.ToLower() == model.VanityUrl.ToLower()) ||
                        await unitOfWork.VanityHistoryRepository.GetAll().AnyAsync(a => a.EntityType == "helpcategory" && a.VanityUrl.ToLower() == model.VanityUrl.ToLower()))
                    {
                        ModelState.AddModelError("vanityURL", "This Vanity Url occupied by another help category, please enter another url");
                        return BadRequest(ModelState);
                    }

                    if (!Regex.IsMatch(model.VanityUrl, BIMsmithMarket.Domain.Constants.Constants.UrlVanityRegax))
                    {
                        ModelState.AddModelError("vanityURL", "This Vanity Url contains not accessible characters. Available next characters '[A-Za-z]|[0-9]|_|-|()'");
                        return BadRequest(ModelState);
                    }
                }

                var result = await _helpService.AddCategoryAsync(unitOfWork, model, userId);

                CacheHelper.ClearSpecificCache("*/api/help/CategoryList*");

                return Ok(result);
            }
        }

        /// <summary>
        /// Edit Help category
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [Authorize(Roles = DbConstants.AdminRole)]
        [HttpPost]
        [ActionName("EditCategory")]
        public async Task<IActionResult> EditCategory([FromBody] AddEditHelpCategoryModel model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
                if (!string.IsNullOrEmpty(model.VanityUrl))
                {
                    if (await unitOfWork.HelpCategoryRepository.GetAll().AnyAsync(a => a.VanityUrl.ToLower() == model.VanityUrl.ToLower() && a.Id != model.Id) ||
                        await unitOfWork.VanityHistoryRepository.GetAll().AnyAsync(a => a.EntityType == "helpcategory" && a.VanityUrl.ToLower() == model.VanityUrl.ToLower() && a.EntityId != model.Id))
                    {
                        ModelState.AddModelError("vanityURL", "This Vanity Url occupied by another help category, please enter another url");
                        return BadRequest(ModelState);
                    }

                    if (!Regex.IsMatch(model.VanityUrl, BIMsmithMarket.Domain.Constants.Constants.UrlVanityRegax))
                    {
                        ModelState.AddModelError("vanityURL", "This Vanity Url contains not accessible characters. Available next characters '[A-Za-z]|[0-9]|_|-|()'");
                        return BadRequest(ModelState);
                    }
                }

                var result = await _helpService.EditCategoryAsync(unitOfWork, model, userId);

                CacheHelper.ClearSpecificCache("*/api/help/CategoryList*");

                return Ok(result);
            }
        }

        /// <summary>
        /// Delete Help category
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Authorize(Roles = DbConstants.AdminRole)]
        [HttpDelete]
        [ActionName("DeleteCategory")]
        public async Task<IActionResult> DeleteCategory(int id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                await _helpService.DeleteCategoryAsync(unitOfWork, id);

                CacheHelper.ClearSpecificCache("*/api/help/CategoryList*");

                return Ok();
            }
        }

        /// <summary>
        /// Get Help categories list
        /// </summary>
        /// <param name="count"></param>
        /// <param name="offset"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("CategoryList")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> CategoryList(int count = 10, int offset = 0)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _helpService.CategoryListAsync(unitOfWork, count, offset));
            }
        }

        /// <summary>
        /// Get specified Help category
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("GetCategory")]
        public async Task<IActionResult> GetCategory(int id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _helpService.GetCategoryAsync(unitOfWork, id));
            }
        }
        #endregion

        #region Help Article CRUD operations
        /// <summary>
        /// Add new Help article
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        [ActionName("AddArticle")]
        public async Task<IActionResult> AddArticle([FromBody] AddEditHelpArticleModel model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
                var result = await _helpService.AddArticleAsync(unitOfWork, model, userId);

                CacheHelper.ClearSpecificCache("*/api/help/ArticleList*");

                return Ok(result);
            }
        }

        /// <summary>
        /// Edit Help article
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        [ActionName("EditArticle")]
        public async Task<IActionResult> EditArticle([FromBody] AddEditHelpArticleModel model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
                var result = await _helpService.EditArticleAsync(unitOfWork, model, userId);

                CacheHelper.ClearSpecificCache("*/api/help/ArticleList*");

                return Ok(result);
            }
        }

        /// <summary>
        /// Delete Help article
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpDelete]
        [ActionName("DeleteArticle")]
        public async Task<IActionResult> DeleteArticle(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
                await _helpService.DeleteArticleAsync(unitOfWork, id, userId);

                CacheHelper.ClearSpecificCache("*/api/help/ArticleList*");

                return Ok();
            }
        }

        /// <summary>
        /// Get Help articles list
        /// </summary>
        /// <param name="count"></param>
        /// <param name="offset"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("ArticleList")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> ArticleList(int count = 10, int offset = 0)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _helpService.ArticleListAsync(unitOfWork, count, offset));
            }
        }

        /// <summary>
        /// Get specified Help article
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("GetArticle")]
        public async Task<IActionResult> GetArticle(int id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _helpService.GetArticleAsync(unitOfWork, id));
            }
        }

#endregion

        /// <summary>
        /// Get article feedback
        /// </summary>
        /// <param name="count"></param>
        /// <param name="offset"></param>
        /// <returns></returns>
        [Authorize(Roles = DbConstants.AdminRole)]
        [HttpGet]
        [ActionName("ArticleFeedback")]
        public async Task<IActionResult> ArticleFeedback(int count = 10, int offset = 0)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _helpService.ArticleFeedbackAsync(unitOfWork, count, offset));
            }
        }

#region bimsmith website requests

        /// <summary>
        /// Add help article reaction
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("WebsiteAddArticleReaction")]
        public async Task<IActionResult> WebsiteAddArticleReaction(HelpAtricleReactionModel model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _helpService.WebsiteAddArticleReactionAsync(unitOfWork, model));
            }
        }

        /// <summary>
        /// Get Article list for Website main help page
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("WebsiteCategoryList")]
        public async Task<IActionResult> WebsiteCategoryList()
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _helpService.WebsiteCategoryListAsync(unitOfWork));
            }
        }

        /// <summary>
        /// Get specified category for Website main help page
        /// </summary>
        /// <param name="categoryUrl"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("WebsiteCategory")]
        public async Task<IActionResult> WebsiteCategory(string categoryUrl)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _helpService.WebsiteCategoryAsync(unitOfWork, categoryUrl));
            }
        }

        /// <summary>
        /// Get specified Article for website
        /// </summary>
        /// <param name="articleUrl"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("WebsiteArticle")]
        public async Task<IActionResult> WebsiteArticle(string articleUrl)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _helpService.WebsiteArticleAsync(unitOfWork, articleUrl));
            }
        }

        /// <summary>
        /// Get articles list for website
        /// </summary>
        /// <param name="categoryId"></param>
        /// <param name="count"></param>
        /// <param name="offset"></param> 
        /// <param name="query"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("WebsiteArticleList")]
        public async Task<IActionResult> WebsiteArticleList(int? categoryId = null, int count = 10, int offset = 0, string query = null)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _helpService.WebsiteArticleListAsync(unitOfWork, categoryId, count, offset, query));
            }
        }

#endregion

        /// <summary>
        /// Upload Help Category Icon
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ActionName("UploadIcon")]
        public async Task<IActionResult> UploadIcon()
        {
            if (!(Request.HasFormContentType && Request.Form.Files.Any()))
                return BadRequest("Mime Multipart Content missing");

            var iconUrl = await _uploadService.SaveIcoToBlobAsync(Request.Form.Files[0]);
            if (string.IsNullOrEmpty(iconUrl))
            {
                return BadRequest("Something wrong ");
            }

            return Ok(new { iconUrl });
        }
    }
}