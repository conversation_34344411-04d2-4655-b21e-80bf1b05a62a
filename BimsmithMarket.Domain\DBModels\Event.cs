﻿using System;

namespace BIMsmithMarket.Domain.DBModels
{
    public class Event : BaseEntity
    {
        public string Title { get; set; }

        public string Description { get; set; }

        public string HtmlBody { get; set; }

        public string ImageUrlBig { get; set; }

        public string ImageUrlSmall { get; set; }

        public string AuthorTitle { get; set; }

        public string AuthorEmail { get; set; }

        public string AuthorImage { get; set; }

        public string Tags { get; set; }

        public string MetaDescription { get; set; }

        public string MetaKeywords { get; set; }

        public string MetaTitle { get; set; }

        public int ViewCount { get; set; }

        public bool Published { get; set; }

        public DateTime? PublishedDate { get; set; }

        public bool IsFeatured { get; set; }

        public int Status { get; set; }

        public string VanityUrl { get; set; }

        public DateTime Date { get; set; }

        public string Time { get; set; }

        public string Link { get; set; }

        public string CEUCredits { get; set; }

        public bool UnpublishAfterDateHasPassed { get; set; }
    }
}