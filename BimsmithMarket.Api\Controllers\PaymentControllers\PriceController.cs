﻿using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.PaymentsDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers.PaymentControllers
{
#if !DEBUG
    [Authorize(Roles = DbConstants.AdminRole)]
#endif
    [Route("api/[controller]/[action]/{id?}")]
    public class PriceController : BaseApiController
    {
        private readonly IPriceService _priceService;

        public PriceController(IPriceService priceService)
        {
            _priceService = priceService;
        }

        /// <summary>
        /// On creation will be checked price type 
        /// if it is not free product - all files will be moved to lock blob
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] PriceDto model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var result = await _priceService.CreateAsync(model, unitOfWork);
                return Ok(result);
            }
        }


        [HttpGet]
        public async Task<IActionResult> GetPriceTypes()
        {
            return Ok(_priceService.GetPricetypes());
        }

        /// <summary>
        /// On update will be moving files from lock to free and reverse
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPut]
        public async Task<IActionResult> Update([FromBody] PriceDto model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var result = await _priceService.UpdateAsync(model, unitOfWork);
                return Ok(result);
            }
        }

        [HttpDelete]
        public async Task<IActionResult> Delete(int id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                await _priceService.DeleteAsync(id, unitOfWork);
                return Ok(new { success = true });
            }
        }

        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> GetByProduct(int id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var price = await _priceService.GetByProductAsync(id, unitOfWork);
                return Ok(price);
            }
        }

        /// <summary>
        /// Gets prices for specified products
        /// </summary>
        /// <param name="model">The model with product identifiers</param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public async Task<IActionResult> GetByProducts(EntityIdsDto model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var price = await _priceService.GetPriceByProductsAsync(model, unitOfWork);
                return Ok(price);
            }
        }
    }
}