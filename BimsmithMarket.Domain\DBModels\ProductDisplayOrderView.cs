﻿namespace BIMsmithMarket.Domain.DBModels
{
    public class ProductDisplayOrderView
    {
        public int ProductId { get; set; }
        public int ManufacturerId { get; set; }
        public int CategoryId { get; set; }
        public int? ParentCategoryId { get; set; }
        public int? ParentParentCategoryId { get; set; }
        public long PRn { get; set; }
        public long CRn { get; set; }
        public long? PCRn { get; set; }
        public long MRn { get; set; }
        public float Weight { get; set; }
        public long DisplayPosition { get; set; }
        public int ResultBlock { get; set; }
    }
}