﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.SalesRepresentative;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class SalesRepresentativeService : ISalesRepresentativeService
    {
        public async Task<AdminGetSalesRepresentativeDto> AddAsync(AddSalesRepresentativeDto model, string userId, IUnitOfWork unitOfWork)
        {
            SalesRepresentative salesRepresentative = model.Adapt<SalesRepresentative>();
            salesRepresentative.CreatedById = userId;
            unitOfWork.SalesRepresentativeRepository.Insert(salesRepresentative);
            await unitOfWork.SaveAsync();

            return salesRepresentative.Adapt<AdminGetSalesRepresentativeDto>();
        }

        public async Task<AdminGetSalesRepresentativeDto> EditAsync(EditSalesRepresentativeDto model, string userId, IUnitOfWork unitOfWork)
        {
            SalesRepresentative salesRepresentative = await unitOfWork.SalesRepresentativeRepository.GetByIdAsync(model.Id);

            if (salesRepresentative == null)
                throw new InvalidInputException("Sales representative does not exist");

            model.Adapt(salesRepresentative);
            salesRepresentative.ModifiedById = userId;
            salesRepresentative.ModifiedDate = DateTime.UtcNow;

            unitOfWork.SalesRepresentativeRepository.Edit(salesRepresentative);
            await unitOfWork.SaveAsync();

            return salesRepresentative.Adapt<AdminGetSalesRepresentativeDto>();
        }

        public async Task<AdminGetSalesRepresentativeDto> AdminGetAsync(int id, IUnitOfWork unitOfWork)
        {
            SalesRepresentative salesRepresentative = await unitOfWork.SalesRepresentativeRepository.GetByIdAsync(id);

            if (salesRepresentative == null)
                throw new DbItemNotFoundException("Sales representative does not exist");

            return salesRepresentative.Adapt<AdminGetSalesRepresentativeDto>();
        }

        public async Task<PaginationListDto<AdminListSalesRepresentativeDto>> AdminListAsync(IUnitOfWork unitOfWork, int manufacturerId, string query = null, int offset = 0, int count = 10)
        {
            IQueryable<SalesRepresentative> dbQuery = unitOfWork.SalesRepresentativeRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId);

            if (!string.IsNullOrWhiteSpace(query))
            {
                string normalizedQuery = query.ToUpper();
                dbQuery = dbQuery.Where(x => x.Email.ToUpper().Contains(normalizedQuery)
                    || x.FirstName.ToUpper().Contains(normalizedQuery)
                    || x.LastName.ToUpper().Contains(normalizedQuery)
                    || x.PhoneNumber.ToUpper().Contains(normalizedQuery)
                    || x.WebsiteName.ToUpper().Contains(normalizedQuery));
            }

            int totalCount = await dbQuery.CountAsync();

            AdminListSalesRepresentativeDto[] items = await dbQuery.ProjectToType<AdminListSalesRepresentativeDto>()
                .Skip(offset)
                .Take(count)
                .ToArrayAsync();

            return new PaginationListDto<AdminListSalesRepresentativeDto>
            {
                Count = totalCount,
                Data = items
            };
        }

        public async Task<OperationResultDto> DeleteAsync(int id, IUnitOfWork unitOfWork)
        {
            SalesRepresentative salesRepresentative = await unitOfWork.SalesRepresentativeRepository.GetByIdAsync(id);

            if (salesRepresentative == null)
                throw new InvalidInputException("Sales representative does not exist");

            unitOfWork.SalesRepresentativeRepository.Delete(salesRepresentative);
            await unitOfWork.SaveAsync();

            return new OperationResultDto
            {
                Status = OperationResultStatus.Succeed
            };
        }

        public async Task<PublicListSalesRepresentativeDto[]> PublicListAsync(int manufacturerId, IUnitOfWork unitOfWork)
        {
            return await unitOfWork.SalesRepresentativeRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId)
                .ProjectToType<PublicListSalesRepresentativeDto>()
                .ToArrayAsync();
        }
    }
}