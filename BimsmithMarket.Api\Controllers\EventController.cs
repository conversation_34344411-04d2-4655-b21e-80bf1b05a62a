﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using System;
using System.IO;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// Events controller
    /// </summary>
    [Route("api/[controller]/[action]/{id?}")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
    public class EventController : BaseApiController
    {
        private readonly IEventService _eventService;
        private readonly IWebHostEnvironment _webHostEnvironment;

        public EventController(
            IEventService eventService,
            IWebHostEnvironment webHostEnvironment
            )
        {
            _eventService = eventService;
            _webHostEnvironment = webHostEnvironment;
        }

        /// <summary>
        /// Create new event
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Add")]
        public async Task<IActionResult> Add([FromBody] AddEditEventDto model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
                var result = await _eventService.AddAsync(unitOfWork, model, userId);

                CacheHelper.ClearSpecificCache("*/api/Event/AdminList*");
                CacheHelper.ClearSpecificCache("*/api/Event/PublicList*");

                return Ok(result);
            }
        }

        /// <summary>
        /// Updates event
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Edit")]
        public async Task<IActionResult> Edit([FromBody] AddEditEventDto model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
                var result = await _eventService.EditAsync(unitOfWork, model, userId);

                CacheHelper.ClearSpecificCache("*/api/Event/AdminList*");
                CacheHelper.ClearSpecificCache("*/api/Event/PublicList*");

                return Ok(result);
            }
        }

        /// <summary>
        /// Deletes event
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [ActionName("Delete")]
        public async Task<IActionResult> Delete(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                await _eventService.DeleteAsync(unitOfWork, id);

                CacheHelper.ClearSpecificCache("*/api/Event/AdminList*");
                CacheHelper.ClearSpecificCache("*/api/Event/PublicList*");

                return Ok();
            }
        }

        /// <summary>
        /// Get detailed event
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("Get")]
        public async Task<IActionResult> Get(int id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var item = await _eventService.GetAsync(unitOfWork, id);
                if (item == null)
                    return NotFound("Not found the event");

                return Ok(item);
            }
        }

        /// <summary>
        /// Get admin list of events
        /// </summary>
        /// <param name="q"></param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("AdminList")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> AdminList(string q = null, int offset = 0, int count = 10)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _eventService.AdminListAsync(unitOfWork, q, offset, count));
        }

        /// <summary>
        /// Get public list of events
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("PublicList")]
        [AllowAnonymous]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> PublicList()
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _eventService.PublicListAsync(unitOfWork));
        }

        [HttpPost]
        [ActionName("ScheduleSessionRequest")]
        [AllowAnonymous]
        public async Task<IActionResult> ScheduleSessionRequest(ScheduleSessionRequestModel model)
        {
            var additionalInfo = "";

            if (model.HasCEUPresentation) additionalInfo += "<br>I have a CEU presentation I would like to schedule";
            if (model.InterestedInBIMsmithHelp) additionalInfo += "<br>I am interested in BIMsmith helping create a CEU for me";
            if (model.ReceiveNews) additionalInfo += "<br>I would like to receive periodic articles, insider tips, and email updates from BIMsmith";

            var emailsPath = Path.Combine(_webHostEnvironment.WebRootPath, "Emails");
            await EmailNotificationHelper.Create(emailsPath).SendScheduleSessionRequestEmail(
                model.FirstName,
                model.LastName,
                model.Company,
                model.CompanyEmail,
                model.Comments,
                additionalInfo);

            CacheHelper.ClearSpecificCache("*/api/Event/AdminList*");
            CacheHelper.ClearSpecificCache("*/api/Event/PublicList*");

            return Ok();
        }

        [HttpPost]
        [ActionName("ProductRequest")]
        [AllowAnonymous]
        public async Task<IActionResult> ProductRequest(ProductRequestModel model)
        {
            try
            {
                var additionalInfo = "";

                if (model.HasBIMContent) additionalInfo += "<br>I have BIM content already";
                if (model.InterestedInBIMsmithHelp) additionalInfo += "<br>I am interested in BIMsmith helping create my BIM content";
                if (model.ReceiveNews) additionalInfo += "<br>I would like to receive periodic articles, insider tips, and email updates from BIMsmith";

                var emailsPath = Path.Combine(_webHostEnvironment.WebRootPath, "Emails");
                await EmailNotificationHelper.Create(emailsPath).SendProductRequestEmail(
                    model.FirstName,
                    model.LastName,
                    model.Company,
                    model.CompanyEmail,
                    model.Comments,
                    additionalInfo);

                CacheHelper.ClearSpecificCache("*/api/Event/AdminList*");
                CacheHelper.ClearSpecificCache("*/api/Event/PublicList*");

                return Ok();
            }
            catch (Exception e)
            {
                return BadRequest(e.Message);
            }
        }

        [HttpPost]
        [ActionName("JoinClub")]
        [AllowAnonymous]
        public async Task<IActionResult> JoinClub(JoinClubModel model)
        {
            var mailChimpManager = new MailChimp.Net.MailChimpManager(ConfigurationHelper.GetValue("MailChimpCofeeClubAPIKey"));
            var coffeeClubListId = ConfigurationHelper.GetValue("MailchimpCoffeeClubListId");

            var member = new MailChimp.Net.Models.Member { EmailAddress = model.EmailAddress, StatusIfNew = MailChimp.Net.Models.Status.Subscribed };
            member.MergeFields.Add("MMERGE3", model.AIAMemberNumber ?? "");
            member.MergeFields.Add("MMERGE4", model.Company ?? "");
            member.MergeFields.Add("FNAME", model.FirstName ?? "");
            member.MergeFields.Add("LNAME", model.LastName ?? "");
            member.MergeFields.Add("MMERGE5", GetRequestUrl());

            var result = await mailChimpManager.Members.AddOrUpdateAsync(coffeeClubListId, member);

            CacheHelper.ClearSpecificCache("*/api/Event/AdminList*");
            CacheHelper.ClearSpecificCache("*/api/Event/PublicList*");

            return Ok(result);
        }

        #region private methods
        private string GetRequestUrl()
        {
            var requestUrl = Request.GetDisplayUrl();
            if (string.IsNullOrWhiteSpace(requestUrl)) requestUrl = Request.Host + "/coffee-club";
            if (string.IsNullOrWhiteSpace(requestUrl)) requestUrl = "Unknown";
            return requestUrl;
        }
        #endregion
    }
}