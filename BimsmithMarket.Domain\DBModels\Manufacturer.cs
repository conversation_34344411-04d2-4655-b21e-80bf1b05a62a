﻿using BIMsmithMarket.Domain.DBModels.RevitProcessing;
using BIMsmithMarket.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    [Index(nameof(Name), Name = "IX_Manufacturer_Name")]
    [Index(nameof(ForgeManufacturerId), Name = "IX_Manufacturer_ForgeManufacturerId")]
    public class Manufacturer : BaseEntity
    {
        public int? AddressId { get; set; }

        [StringLength(200)]
        public string Name { get; set; }

        public string ManufacturerHubTitle { get; set; }

        public string ManufacturerHubSubtitle { get; set; }

        [StringLength(38)]
        public string ForgeManufacturerId { get; set; }

        public string Site { get; set; }

        public string Note { get; set; }

        public string Synonyms { get; set; }

        public string Description { get; set; }

        public int? PhotoId { get; set; }

        public int? ImageId { get; set; }

        public bool PublishToPartner { get; set; }

        public int? OriginalImageId { get; set; }

        public string PhoneMask { get; set; }

        public string PhoneNumber { get; set; }

        public string SwatchboxManufacturerId { get; set; }

        public bool IsOnForge { get; set; }

        public ManufacturerType Type { get; set; }

        public bool IsOnMarket { get; set; }

        public bool LAndL { get; set; }

        public bool LetsTalk { get; set; }

        public bool SendULInfo { get; set; }

        public string EmailLandL { get; set; }

        public string EmailLetsTalk { get; set; }

        public string LetsTalkSettings { get; set; }

        public string EmailLandLCC { get; set; }

        public string EmailLetsTalkCC { get; set; }

        public bool SubscribeButton { get; set; }

        public string VideoUrl { get; set; }

        public string HubVanityURL { get; set; }

        public string ForgeUrl { get; set; }

        public string MarketUrl { get; set; }

        public string MetaDescription { get; set; }

        public string MetaKeywords { get; set; }

        public string MetaTitle { get; set; }

        public string Keywords { get; set; }

        public bool Published { get; set; }

        public bool Staging { get; set; }

        public float Weight { get; set; }

        public string PageSetting { get; set; }

        public string AnalyticsSetting { get; set; }

        public string NodeSetting { get; set; }

        public int Status { get; set; }

        public string OwnerId { get; set; }

        public bool IncludeRevitPlugin { get; set; }

        public int? PromotedId { get; set; }

        public int? RevitPluginPhotoId { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        public string ManualUrl { get; set; }

        public string DetailsMicrositeSettings { get; set; }

        public bool ShowFooterAd { get; set; }

        public string FooterAdUrl { get; set; }

        public int? FooterAdImageId { get; set; }

        [ForeignKey("RevitPluginPhotoId")]
        public virtual Photo RevitPluginPhoto { get; set; }

        public int? ParentId { get; set; }

        [ForeignKey("ParentId")]
        public virtual Manufacturer Parent { get; set; }

        public bool IsParentManufacturer { get; set; }

        #region customized Login screen and Register screen fields
        public bool UseCustomLoginRegisterScreen { get; set; }

        public string CustomSignInBackgroundColor { get; set; }

        public string CustomSignInTextColor { get; set; }

        public string CustomSignInDescription { get; set; }

        public int? CustomSignInLogoImageId { get; set; }
        #endregion

        public HealthCheckStatus HealthCheckStatus { get; set; }

        [StringLength(200)]
        public string CustomMicrositeName { get; set; }

        public bool RequestPricingEnabled { get; set; }

        [StringLength(100)]
        public string RequestPricingEmail { get; set; }

        [StringLength(100)]
        public string RequestPricingEmailCC { get; set; }

        /// ------------------------------------------

        [ForeignKey("OwnerId")]
        public virtual ApplicationUser Owner { get; set; }

        [ForeignKey("PhotoId")]
        public virtual Photo Photo { get; set; }

        [ForeignKey("ImageId")]
        public virtual Photo Image { get; set; }

        [ForeignKey("AddressId")]
        public virtual Address Address { get; set; }

        public virtual ICollection<ManufacturerAnnouncement> ManufacturerAnnouncements { get; set; }

        [ForeignKey("FooterAdImageId")]
        public virtual Photo FooterAdImage { get; set; }

        [ForeignKey("CustomSignInLogoImageId")]
        public virtual Photo CustomSignInLogoImage { get; set; }

        public virtual ICollection<Product> Products { get; set; }

        public virtual ICollection<ProductLine> ProductLines { get; set; }

        public virtual ICollection<ManufacturerPhoto> ManufacturerPhotos { get; set; }

        public virtual ICollection<ManufacturerFile> ManufacturerFiles { get; set; }

        public virtual ICollection<Detail> Details { get; set; }

        public virtual ICollection<ManufacturerStyleFile> ManufacturerStyleFiles { get; set; }

        public virtual ICollection<ManufacturerAdditionalFile> ManufacturerAdditionalFiles { get; set; }

        public virtual ICollection<AttachmentOrder> AttachmentOrders { get; set; }

        public virtual ICollection<Manufacturer> Children { get; set; }

        public virtual ICollection<ManufacturerAdminUser> ManufacturerAdminUsers { get; set; }

        public virtual ICollection<StaticExcelFile> StaticExcelFiles { get; set; }

        public virtual ICollection<UserBIMsmithBIMManufacturer> UserBIMsmithBIMManufacturers { get; set; }

        public virtual ICollection<UserBIMsmithLLManufacturer> UserBIMsmithLLManufacturers { get; set; }

        public virtual ICollection<UserBIMsmithLTManufacturer> UserBIMsmithLTManufacturers { get; set; }

        public virtual ICollection<UserBIMsmithManufacturer> UserBIMsmithManufacturers { get; set; }

        public virtual ICollection<CustomCategoryIcon> CustomCategoryIcons { get; set; }

        public virtual ICollection<SalesRepresentative> SalesRepresentatives { get; set; }

        public virtual ICollection<RequestPricingUser> RequestPricingUsers { get; set; }

        public virtual ICollection<RevitProcess> RevitProcesses { get; set; }

        public Manufacturer()
        {
            Products = new List<Product>();
            ProductLines = new List<ProductLine>();
            ManufacturerFiles = new List<ManufacturerFile>();
            ManufacturerPhotos = new List<ManufacturerPhoto>();
            Details = new List<Detail>();
            ManufacturerStyleFiles = new List<ManufacturerStyleFile>();
            ManufacturerAdditionalFiles = new List<ManufacturerAdditionalFile>();
            AttachmentOrders = new List<AttachmentOrder>();
            Children = new List<Manufacturer>();
            ManufacturerAdminUsers = new List<ManufacturerAdminUser>();
            ManufacturerAnnouncements = new List<ManufacturerAnnouncement>();
            StaticExcelFiles = new List<StaticExcelFile>();
            UserBIMsmithBIMManufacturers = new List<UserBIMsmithBIMManufacturer>();
            UserBIMsmithLLManufacturers = new List<UserBIMsmithLLManufacturer>();
            UserBIMsmithLTManufacturers = new List<UserBIMsmithLTManufacturer>();
            UserBIMsmithManufacturers = new List<UserBIMsmithManufacturer>();
            CustomCategoryIcons = new List<CustomCategoryIcon>();
            SalesRepresentatives = new List<SalesRepresentative>();
            RequestPricingUsers = new List<RequestPricingUser>();
            RevitProcesses = new List<RevitProcess>();
        }
    }
}