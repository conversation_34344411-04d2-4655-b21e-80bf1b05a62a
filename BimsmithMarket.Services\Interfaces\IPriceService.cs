﻿using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.PaymentsDto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IPriceService
    {
        List<PriceTypeDto> GetPricetypes();

        Task<PriceDto> CreateAsync(PriceDto model, IUnitOfWork unitOfWork);

        Task<PriceDto> UpdateAsync(PriceDto model, IUnitOfWork unitOfWork);

        Task DeleteAsync(int priceId, IUnitOfWork unitOfWork);

        Task<PriceDto> GetByProductAsync(int productId, IUnitOfWork unitOfWork);

        Task<IEnumerable<PriceDto>> GetPriceByProductsAsync(EntityIdsDto model, IUnitOfWork unitOfWork);

        Task SetDefaultPriceToProductAsync(int productId, IUnitOfWork unitOfWork);

        Task MoveProductFilesBetweenPaidAndFreeContainerAsync(
            int productId,
            PriceType oldPriceType,
            PriceType newPriceType,
            IUnitOfWork unitOfWork,
            bool forceMove = false);
    }
}