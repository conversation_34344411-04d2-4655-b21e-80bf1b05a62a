﻿@using BIMsmithMarket.Core.Helpers
@model BIMsmithNewsroom.Models.NewsList
@{
    Layout = null;
    ViewBag.Title = "BIMsmith News Room";
}
<!DOCTYPE html>

<html lang="en-US" prefix="og: http://ogp.me/ns#">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    @if (ViewBag.isDev)
    {
        <meta name="robots" content="noindex">
    }

    <link rel="icon" type="image/x-icon" href="~/favicon.png">
    <meta name="viewport" content="width=device-width">
    <meta name="description" content="Building Product Data for Revit and beyond is at your fingertips. Revit Families, AutoCAD Details, 3 Part Specs.">
    <meta name="keywords" content="Keywords: revit families, BIM, revit content, BIM content, BIM objects, building product data">


    <meta name="google-site-verification" content="S931UZF3IQ9ehCHUtrDeFGsTowM-hC7cX-ar3sRWYVI" />

    <meta name="msvalidate.01" content="5533F7259691FCA7EF63EFB3CDED200F" />



    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=***********-2"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', '***********-2');
    </script>

    <script type='text/javascript' src='https://cdnjs.cloudflare.com/ajax/libs/js-cookie/2.1.4/js.cookie.min.js'></script>

    <title>@ViewBag.Title</title>
    <link rel="stylesheet" href="~/Bundles/contentcssbundle.min.css" />

</head>
<body class="home page-template-default page page-id-11">
    <div id="page" class="hfeed site">
        <header id="masthead" role="banner">
            <nav class="navbar navbar-default navbar-fixed-top navbar-left" role="navigation">
                <!-- Brand and toggle get grouped for better mobile display -->
                <div class="container" id="navigation_menu">
                    <div class="navbar-header">
                        <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-ex1-collapse">
                            <span class="sr-only">Toggle navigation</span>
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                        </button>
                        <a class="navbar-brand" href="@ConfigurationHelper.GetValue("BIMsmithUrl")">Newsroom</a>
                    </div>
                    <div class="sign-in">
                        <div style="display: none;" id="user-name"></div>
                        <div style="display: none;" id="head-drop" class="head-drop">
                            <div class="av">
                                <img id="profile-image" alt="" src="/images/img01.jpg" height="383" width="383">
                                <div id="settings-name" class="name"></div>
                            </div>
                            <div class="links">
                                <a href="@ConfigurationHelper.GetValue("MarketUrl")/">Market</a>
                                <a href="@ConfigurationHelper.GetValue("ForgeUrl")/">forge</a>
                                <a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/NewMyBIMSmith/settings">MyBIMsmith</a>
                            </div>
                        </div>
                        <a id="logout-button" style="display: none;" class="def-btn logout-button" href="javascript:void(0)">Log out</a>
                        <a id="login-button" style="display: inline-block;" class="def-btn" href="@ConfigurationHelper.GetValue("BIMsmithUrl")/Account/Login/">Log in</a>
                        <a id="signup-button" style="display: inline-block;" class="g-btn" href="@ConfigurationHelper.GetValue("BIMsmithUrl")/Account/Register">Sign up</a>
                    </div>
                    <div class="collapse navbar-collapse navbar-ex1-collapse">
                        <ul id="menu-menu-1" class="nav navbar-nav">
                            <li id="menu-item-101" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-101"><a title="Newsroom" href="/">NewsRoom</a></li>
                            <li id="menu-item-101" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-101"><a title="Blog" href="@ConfigurationHelper.GetValue("BlogUrl")">Blog</a></li>
                            <li id="menu-item-151" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-151"><a title="Contact" href="@ConfigurationHelper.GetValue("BIMsmithUrl")/Contact">Contact</a></li>
                            <li id="mob-login"><a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/Account/Login/">Sign In</a></li>
                            <li id="mob-regis"><a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/Account/Register">Sign Up</a></li>
                        </ul>
                    </div>
                </div>
                <!--#container-->
            </nav>
            <div id="cc_spacer" style="height: 45px;"></div>
            <!-- used to clear fixed navigation by the themes js -->
            <div class="site-header">
                <div class="site-branding">
                    <a class="home-link" href="/" title="Newsroom" rel="home">
                        <h1 class="site-title">Newsroom</h1>
                        <!-- <h2 class="site-description">BIMsmithMarket</h2> -->
                    </a>
                </div>
                <!--.site-branding-->
            </div>
            <!--.site-header-->
        </header>
        <div id="content" class="site-content">
            <div class="breads">
                @if (Model.IsTagList == false)
                {
                    <section class="widget widget_breadcrumb_navxt" id="bcn_widget-2">
                        <!-- Breadcrumb NavXT 5.6.0 -->
                        <span typeof="ListItem" property="itemListElement">
                            <span property="name">Newsroom</span>
                            <meta content="1" property="position">
                        </span>
                    </section>
                }
                else
                {
                    <section id="bcn_widget-2" class="widget widget_breadcrumb_navxt">
                        <!-- Breadcrumb NavXT 5.6.0 -->
                        <span property="itemListElement" typeof="ListItem">
                            <a property="item" typeof="WebPage" title="Go to Newsroom." href="/" class="home">
                                <span property="name"></span>
                            </a>
                            <meta property="position" content="1">
                        </span>
                        &gt;
                        <a class="sampleTag" href="/">Newsroom</a>
                        <div class="sampleTag">@Model.TagId</div>
                    </section>
                }
            </div>
            <div class="handle-container">
                <div class="row">

                    <div id="primary" class="col-md-10 content-area")>

                        <main id="main" class="site-main" role="main">

                            <article id="post-11" class="post-content post-11 page type-page status-publish hentry">

                                <header class="entry-header">
                                    <span class="screen-reader-text">News</span>
                                    <h1 class="entry-title">News</h1>
                                    <div class="entry-meta"></div><!-- .entry-meta -->
                                </header><!-- .entry-header -->

                                <div class="entry-content">
                                    <div class="pt-cv-wrapper">
                                        <div class="pt-cv-view pt-cv-grid pt-cv-colsys" id="pt-cv-view-8f04f39p9v">
                                            <div data-id="pt-cv-page-1" class="pt-cv-page" data-cvc="1">
                                                @foreach (var news in Model.News)
                                                {
                                                    <div class="col-md-12 col-sm-12 col-xs-12 pt-cv-content-item pt-cv-1-col">
                                                        <div class="pt-cv-ifield">
                                                            <a href="/@news.VanityId" class="_self pt-cv-href-thumbnail pt-cv-thumb-default" target="_self" style="background-image: url(&quot;@news.ImageUrl&quot;);"><img width="300" height="196" src="@news.ImageUrl" class="pt-cv-thumbnail" alt="" srcset="@news.ImageUrl 300w,@news.ImageUrl 768w, @news.ImageUrl 1024w, @news.ImageUrl 413w, @news.ImageUrl 1500w" sizes="(max-width: 300px) 100vw, 300px" style="display: none;"></a>
                                                            <div class="news-block">
                                                                <h4 class="pt-cv-title"><a href="/@news.VanityId" class="_self" target="_self">@news.Title</a></h4>
                                                                <div class="pt-cv-content">
                                                                    @news.Descriptions
                                                                    <br>
                                                                    <a href="/@news.VanityId" class="_self pt-cv-readmore btn btn-success" target="_self">Read More</a>
                                                                </div>
                                                                <div class="pt-cv-meta-fields"><span class="entry-date"> <time datetime="2017-03-07T20:07:23+00:00">@news.PublishedDate.Value.ToString("d")</time></span></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div><!-- .entry-content -->

                                @if (Model.PagesCount > 1)
                                {
                                    <nav aria-label="Page navigation" class="center-block">
                                        <ul class="pagination">

                                            @if (Model.CurrentPage > 1)
                                            {
                                                <li>
                                                    <a href="/?page=@(Model.CurrentPage - 1)" aria-label="Previous">
                                                        <span aria-hidden="true">&laquo;</span>
                                                    </a>
                                                </li>
                                            }
                                            else
                                            {
                                                <li class="disabled">
                                                    <a href="#" aria-label="Previous">
                                                        <span aria-hidden="true">&laquo;</span>
                                                    </a>
                                                </li>
                                            }

                                            @for (int i = 0; i < Model.PagesCount; i++)
                                            {
                                                if ((i + 1) == Model.CurrentPage)
                                                {
                                                    <li class="active"><a href="/?page=@(i+1)">@(i + 1)</a></li>
                                                }
                                                else
                                                {
                                                    <li><a href="/?page=@(i+1)">@(i + 1)</a></li>
                                                }
                                            }

                                            @if (Model.CurrentPage != Model.PagesCount)
                                            {
                                                <li>
                                                    <a href="/?page=@(Model.CurrentPage + 1)" aria-label="Next">
                                                        <span aria-hidden="true">&raquo;</span>
                                                    </a>
                                                </li>
                                            }
                                            else
                                            {
                                                <li class="disabled">
                                                    <a href="#" aria-label="Next">
                                                        <span aria-hidden="true">&raquo;</span>
                                                    </a>
                                                </li>
                                            }
                                        </ul>
                                    </nav>
                                }

                                <footer class="entry-footer"></footer><!-- .entry-footer -->
                            </article><!-- #post-## -->
                        </main><!-- #main -->

                    </div><!-- #primary -->

                    <div class="col-md-2 sidebar widget-area" role="complementary">
                        <aside id="recent-posts-2" class="widget widget_recent_entries">
                            <h4 class="widget-title">TAGS</h4>
                            <ul>
                                @foreach (var tag in Model.Tags)
                                {
                                    <li class="list-tag">
                                        <a href="/news/tag/@tag">@tag</a>
                                    </li>
                                }
                            </ul>
                        </aside>
                    </div>

                </div> <!--.row-->
            </div><!--.container-->
            <!--.container-->
        </div>
        <!-- #content -->
        <footer id="colophon" class="site-footer" role="contentinfo">
            <div class="row site-info">
                <div class="brands-block">
                    <p>Chosen by: </p>
                    <ul>
                        <li>
                            <a href="http://market.bimsmith.com/Canon">
                                <img src="/images/brand-img01.png" alt="">
                            </a>
                        </li>
                        <li>
                            <a href="http://market.bimsmith.com/SherwinWilliams">
                                <img src="/images/brand-img02.png" alt="">
                            </a>
                        </li>
                        <li>
                            <a href="http://market.bimsmith.com/Moen">
                                <img src="/images/brand-img03.png" alt="">
                            </a>
                        </li>
                        <li>
                            <a href="http://market.bimsmith.com/Dupont-na">
                                <img src="/images/brand-img05.png" alt="">
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="footer-holder">
                    <footer class="footer">
                        <div class="info">
                            <a class="logo" href="/"><img src="/images/logo-footer.png" alt=""></a>
                            <div class="address">
                                <span class="white">BIMsmith Headquarters</span>
                                <span>68 S. Grove Avenue, Elgin, IL 60120</span>
                            </div>
                            <div class="contacts">
                                <div>
                                    <a class="tel" href="tel:+12244848896">+1 (224) 484 - 8896</a>
                                </div>
                                <div>
                                    <div>
                                        <a class="mail" href="mailto:<EMAIL>"><EMAIL></a>
                                    </div>
                                    <div>
                                        <a href="mailto:<EMAIL>"><EMAIL></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="links">
                            <div class="col">
                                <p>Products</p>
                                <ul>
                                    <li><a href="@ConfigurationHelper.GetValue("MarketUrl")/">Market</a></li>
                                    <li><a href="@ConfigurationHelper.GetValue("ForgeUrl")/">Forge</a></li>
                                    <li><a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/">MyBIMsmith</a></li>
                                    <li><a href="https://anguleris.com/bim-strategy/bim-content-creation">BIM Content Creation</a></li>
                                </ul>
                            </div>
                            <div class="col">
                                <p>Community</p>
                                <ul>
                                    <li><a href="/">Newsroom</a></li>
                                    <li><a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/Contact">Support</a></li>
                                </ul>
                            </div>
                            <div class="col">
                                <p>Legal</p>
                                <ul>
                                    <li><a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/legal/terms-and-conditions">Terms and conditions</a></li>
                                    <li><a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/legal/privacy-policy">Privacy Policy</a></li>
                                    <li><a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/legal/patents-and-intellectual-property">Patents and Intellectual Property</a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="f-bot">
                            <ul class="social">
                                <li><a href="https://www.facebook.com/thebimsmith/"><img src="/images/soc-fb.png" alt=""></a></li>
                                <li><a href="https://twitter.com/thebimsmith"><img src="/images/soc-tw.png" alt=""></a></li>
                                <li><a href="https://www.youtube.com/channel/UCAPJyryHrdzN5nTxYSvFqNQ"><img src="/images/soc-youtube.png" alt=""></a></li>
                                <li><a href="https://www.linkedin.com/company/10901027?trk=tyah&amp;trkInfo=clickedVertical%3Ashowcase%2CclickedEntityId%3A10901027%2Cidx%3A1-1-1%2CtarId%3A1478971756516%2Ctas%3ABIMsmi"><img src="/images/soc-in.png" alt=""></a></li>
                                <li><a href="https://www.pinterest.com/thebimsmith/"><img src="/images/soc-p.png" alt=""></a></li>
                            </ul>
                            <div class="copy">© @(DateTime.UtcNow.Year) Anguleris Technologies</div>
                        </div>
                    </footer>
                </div>
            </div>
            <!-- .site-info -->
        </footer>
        <!-- #colophon -->
    </div>
    <!-- #page -->

    <script src="~/Bundles/jquerybundle.min.js"></script>
    <script src="~/Bundles/bimsmithbundle.min.js"></script>
    <script src="~/Bundles/bootstrapjsbundle.min.js"></script>

</body>
</html>
