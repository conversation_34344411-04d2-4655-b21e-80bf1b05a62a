﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Api.Providers;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using BIMsmithMarket.Services.Search;
using BIMsmithMarket.Services.Search.FullTextSearch;
using Flurl;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// Common controller
    /// </summary>
    [Route("api/[controller]/[action]")]
    public class CommonController : BaseApiController
    {
        private readonly int _selectionMaterialLibraryId = 16; // Id in DB of MaterialLibrary
        private readonly IMasterformatService _masterformatService;
        private readonly ICacheService _cacheService;
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly SlackWebHook _slackWebHook;

        public CommonController(
            IMasterformatService masterformatService,
            ICacheService cacheService,
            IWebHostEnvironment webHostEnvironment,
            IHttpClientFactory httpClientFactory,
            SlackWebHook slackWebHook
            )
        {
            _masterformatService = masterformatService;
            _cacheService = cacheService;
            _webHostEnvironment = webHostEnvironment;
            _httpClientFactory = httpClientFactory;
            _slackWebHook = slackWebHook;
        }

        /// <summary>
        /// Get list of category icon urls
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("CategoryIcons")]
        public IActionResult CategoryIcons()
        {
            string folderImages = Path.Combine(_webHostEnvironment.WebRootPath, "assets/img/categoryIcons");

            var list = Directory.GetFiles(folderImages).Select(a => "/assets/img/categoryIcons/" + Path.GetFileName(a));

            return Ok(list);
        }


        /// <summary>
        /// Get list of all available localizations
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("GetAllLocalizations")]
        public IActionResult GetAllLocalizations()
        {
            List<CultureInfo> localizations = CultureInfo.GetCultures(CultureTypes.AllCultures).ToList();

            var result = localizations.Select(a => new
            {
                code = a.Name,
                displayName = a.DisplayName,
                englishName = a.EnglishName
            });

            return Ok(result);
        }

        /// <summary>
        /// Get list of supported localizations
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("GetLocalizations")]
        public IActionResult GetLocalizations()
        {
            List<CultureInfo> localizations = new List<CultureInfo>();
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var names = unitOfWork.LocalizationRepository.GetAll().Select(a => a.Name).AsNoTracking().ToList();

                foreach (var name in names)
                {
                    try
                    {
                        localizations.Add(new CultureInfo(name));
                    }
                    catch (Exception e)
                    {
                        Log.Error(e.Message, e);
                        Debug.WriteLine(e.Message);
                    }
                }
            }
            var result = localizations.Select(a => new
            {
                code = a.Name,
                displayName = a.DisplayName,
                englishName = a.EnglishName
            });

            return Ok(result);
        }

        /// <summary>
        /// Update list of available localizations
        /// </summary>
        /// <param name="names">List of names</param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(Roles = DbConstants.AdminRole)]
        [ActionName("UpdateLocalizations")]
        public async Task<IActionResult> UpdateLocalizations([FromBody] List<string> names)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);

                var existingLocalizations = await unitOfWork.LocalizationRepository.GetAll().ToListAsync();

                foreach (var name in names)
                {
                    var existing = existingLocalizations.FirstOrDefault(a => a.Name == name);
                    if (existing != null)
                    {
                        existingLocalizations.Remove(existing);
                    }
                    else
                    {
                        Localization newLocalization = new Localization();
                        newLocalization.Name = name;
                        newLocalization.CreatedById = userId;
                        newLocalization.CreatedDate = DateTime.UtcNow;
                        unitOfWork.LocalizationRepository.Insert(newLocalization);
                    }
                }

                if (existingLocalizations.Count > 0) // delete old 
                {
                    unitOfWork.LocalizationRepository.Delete(existingLocalizations);
                }

                await unitOfWork.SaveAsync();

                return Ok();
            }
        }

        /// <summary>
        /// Gets the filters.
        /// </summary>
        /// <param name="searchId">The search identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("GetFilters")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> GetFilters(string searchId, CancellationToken cancellationToken)
        {
#if DEBUG
            Stopwatch requestTime = new Stopwatch();
            requestTime.Start();
#endif
            List<int> selectionProjectTypeIds = new List<int>();
            List<int> selectionExternalCertificates = new List<int>();
            dynamic selectionCisfbs = new List<dynamic>();
            List<int> selectionExternalMasterformatIds = new List<int>();
            dynamic selectionOmniclasses = new List<dynamic>();
            bool selectionMaterialLibrary = false;
            bool selectionSamplesAvailable = false;
            dynamic selectionUniclasses = new List<dynamic>();
            dynamic selectionUniformats = new List<dynamic>();
            dynamic selectionQualityItems = new List<dynamic>();
            bool selectionMasterSpecEnabled = false;

#if DEBUG
            Debug.WriteLine($"Get filters started {requestTime.ElapsedMilliseconds}");
#endif

            if (cancellationToken.IsCancellationRequested)
                return Conflict();

            var productIds = _cacheService.Get<List<int>>($"productIds{searchId}");
#if DEBUG
            Debug.WriteLine($"Get filters product ids get {requestTime.ElapsedMilliseconds}");
#endif

            if (cancellationToken.IsCancellationRequested)
                return Conflict();

            if (productIds != null && productIds.Any())
            {
                using (IUnitOfWork unitOfWork = UnitOfWork.Create())
                {
                    SearchCache searchCache = SearchCache.Get(unitOfWork); // ensure cache is loaded, this should happen whenever user first opens the market page

                    if (cancellationToken.IsCancellationRequested)
                        return Conflict();
#if DEBUG
                    Debug.WriteLine($"Get filters search cache get {requestTime.ElapsedMilliseconds}");
#endif

                    var allMasterformats = await _masterformatService.GetBackofficeMasterformatsAsync(searchCache.ExternalMasterformatIds);

                    if (cancellationToken.IsCancellationRequested)
                        return Conflict();
#if DEBUG
                    Debug.WriteLine($"Get filters masterformats get {requestTime.ElapsedMilliseconds}");
#endif
                    selectionMasterSpecEnabled = searchCache.ProductMasterspecEnabledIds.Intersect(productIds).Any();
#if DEBUG
                    Debug.WriteLine($"Get filters selectionMasterSpecEnabled get {requestTime.ElapsedMilliseconds}");
#endif


                    List<int> projectDataTypeIds = searchCache.ProductProjectDataTypes.AsParallel().Where(x => productIds.Contains(x.ProductId)).SelectMany(x => x.ProjectDataTypeIds).Distinct().ToList();
#if DEBUG
                    Debug.WriteLine($"Get filters project data types get {requestTime.ElapsedMilliseconds}");
#endif
                    var externalCertificates = searchCache.ProductCertificates.AsParallel().Where(x => productIds.Contains(x.ProductId)).Select(b => b.ExternalCertificateId).Distinct().ToList();
#if DEBUG
                    Debug.WriteLine($"Get filters certificates get {requestTime.ElapsedMilliseconds}");
#endif
                    var cisfbsIds = searchCache.ProductCisfbs.AsParallel().Where(x => productIds.Contains(x.ProductId)).Select(x => x.CisfbId).Distinct().ToList();
#if DEBUG
                    Debug.WriteLine($"Get filters cisfbsIds get {requestTime.ElapsedMilliseconds}");
#endif
                    var cisfbs = searchCache.Cisfbs.AsParallel().Where(x => cisfbsIds.Contains(x.Id)).Select(c => new
                    {
                        id = c.Id,
                        code = c.Code,
                        title = c.Title,
                        parentId = c.ParentId
                    })
                    .ToList();

#if DEBUG
                    Debug.WriteLine($"Get filters cisfbs get {requestTime.ElapsedMilliseconds}");
#endif

                    var externalMasterformatIds = searchCache.ProductMasterformats.AsParallel().Where(x => productIds.Contains(x.ProductId)).Select(x => x.ExternalMasterformatId).Distinct().ToList();

#if DEBUG
                    Debug.WriteLine($"Get filters external masterformats get {requestTime.ElapsedMilliseconds}");
#endif
                    var omniclassesIds = searchCache.ProductOmniclasses.AsParallel().Where(x => productIds.Contains(x.ProductId)).Select(x => x.OmniclassId).Distinct().ToList();
#if DEBUG
                    Debug.WriteLine($"Get filters omniclassesIds get {requestTime.ElapsedMilliseconds}");
#endif 
                    var uniclassesIds = searchCache.ProductUniclasses.AsParallel().Where(x => productIds.Contains(x.ProductId)).Select(x => x.UniclassId).Distinct().ToList();
#if DEBUG
                    Debug.WriteLine($"Get filters uniclassesIds get {requestTime.ElapsedMilliseconds}");
#endif
                    var uniclasses = searchCache.Uniclasses.AsParallel().Where(x => uniclassesIds.Contains(x.Id)).Select(c => new
                    {
                        id = c.Id,
                        code = c.Code,
                        title = c.Title,
                        parentId = c.ParentId
                    })
                    .ToList();

#if DEBUG
                    Debug.WriteLine($"Get filters uniclasses get {requestTime.ElapsedMilliseconds}");
#endif
                    var uniformatsIds = searchCache.ProductUniformats.AsParallel().Where(x => productIds.Contains(x.ProductId)).Select(x => x.UniformatId).Distinct().ToList();
#if DEBUG
                    Debug.WriteLine($"Get filters uniformatsIds get {requestTime.ElapsedMilliseconds}");
#endif
                    var uniformats = searchCache.Uniformats.AsParallel().Where(x => uniformatsIds.Contains(x.Id)).Select(c => new
                    {
                        id = c.Id,
                        code = c.Code,
                        title = c.Title,
                        parentId = c.ParentId
                    })
                    .ToList();

#if DEBUG
                    Debug.WriteLine($"Get filters product uniformats get {requestTime.ElapsedMilliseconds}");
#endif
                    var qualityItemIds = searchCache.ProductQualityItems.AsParallel().Where(x => productIds.Contains(x.ProductId)).Select(x => x.QualityItemId).Distinct().ToList();
                    var qualityItems = searchCache.QualityItems.AsParallel().Where(x => qualityItemIds.Contains(x.Id)).Select(x => new
                    {
                        x.Id,
                        x.Name,
                        x.IconUrl
                    })
                    .ToList();

#if DEBUG
                    Debug.WriteLine($"Get filters product quality items get {requestTime.ElapsedMilliseconds}");
#endif

                    var productData = new
                    {
                        selectionProjectTypeIds = projectDataTypeIds,
                        selectionExternalCertificates = externalCertificates,
                        selectionCisfbs = cisfbs,
                        selectionExternalMasterformatIds = externalMasterformatIds,
                        selectionOmniclassesIds = omniclassesIds,
                        selectionUniclasses = uniclasses,
                        selectionUniformats = uniformats,
                        selectionMaterialLibrary = searchCache.ProductQualityItems.AsParallel().Where(x => productIds.Contains(x.ProductId)).Any(x => x.QualityItemId == _selectionMaterialLibraryId),
                        selectionSamplesAvailable = searchCache.ProductSwatchboxEnabledIds.Intersect(productIds).Any(),
                        selectionQualityItems = qualityItems
                    };

                    selectionMaterialLibrary = productData?.selectionMaterialLibrary ?? false;
                    selectionSamplesAvailable = productData?.selectionSamplesAvailable ?? false;
                    selectionQualityItems = productData?.selectionQualityItems ?? new();
#if DEBUG
                    Debug.WriteLine(String.Format("Get filters for search in {0}ms", requestTime.ElapsedMilliseconds));
#endif                    
                    if (productData != null)
                    {
                        if (cancellationToken.IsCancellationRequested)
                            return Conflict();

                        selectionProjectTypeIds = productData.selectionProjectTypeIds.ToList();
                        selectionExternalCertificates = productData.selectionExternalCertificates.ToList();

                        //Get recursive allroot parent cisfbs
                        {
                            var currentcisfbesItems = productData.selectionCisfbs.ToList();
                            var rootParentItems = currentcisfbesItems.Where(a => a.parentId == null).ToList();
                            {

                                while (true)
                                {
                                    var existParentIds = rootParentItems.Select(a => a.id).ToList();
                                    var parentIds = currentcisfbesItems.Where(a => a.parentId != null).Select(a => a.parentId.Value).ToList();

                                    var retriveParentIds = parentIds.Except(existParentIds).ToList();

                                    if (!retriveParentIds.Any())
                                        break;

                                    currentcisfbesItems = searchCache.Cisfbs.AsParallel().Where(a => retriveParentIds.Contains(a.Id)).Select(c => new
                                    {
                                        id = c.Id,
                                        code = c.Code,
                                        title = c.Title,
                                        parentId = c.ParentId
                                    }).ToList();

                                    rootParentItems.AddRange(currentcisfbesItems.Where(a => a.parentId == null));
                                }
                            }

                            var selectioncisfbItems = new List<dynamic>();
                            selectioncisfbItems.AddRange(productData.selectionCisfbs);
                            selectioncisfbItems.AddRange(rootParentItems);
                            selectionCisfbs = selectioncisfbItems.OrderBy(a => a.code).Distinct().ToList();
                        }

                        if (cancellationToken.IsCancellationRequested)
                            return Conflict();

                        //Get recursive allroot parent masterformats
                        {
                            HashSet<int> selectionExternalMasterformatIdsItems = new HashSet<int>();
                            foreach (int masterformatId in productData.selectionExternalMasterformatIds)
                            {
                                selectionExternalMasterformatIdsItems.Add(masterformatId);

                                MasterformatModel masterformat = allMasterformats.FirstOrDefault(x => x.Id == masterformatId);

                                while (masterformat.Parent != null)
                                {
                                    selectionExternalMasterformatIdsItems.Add(masterformat.Parent.Id);
                                    masterformat = masterformat.Parent;
                                }
                            }

                            selectionExternalMasterformatIds = selectionExternalMasterformatIdsItems.Distinct().ToList();
                        }

                        if (cancellationToken.IsCancellationRequested)
                            return Conflict();

                        //Get recursive allroot parent omniclasses
                        {
                            HashSet<int> selectionOmniclassIds = new HashSet<int>();
                            foreach (int omniclassId in productData.selectionOmniclassesIds)
                            {
                                selectionOmniclassIds.Add(omniclassId);

                                int? parentId = searchCache.Omniclasses.FirstOrDefault(x => x.Id == omniclassId).ParentId;

                                while (parentId != null)
                                {
                                    selectionOmniclassIds.Add(parentId.Value);
                                    parentId = searchCache.Omniclasses.FirstOrDefault(x => x.Id == parentId).ParentId;
                                }
                            }

                            selectionOmniclasses = searchCache.Omniclasses.Where(x => selectionOmniclassIds.Contains(x.Id))
                                .Select(x => new
                                {
                                    id = x.Id,
                                    code = x.Code,
                                    title = x.Title,
                                    parentId = x.ParentId
                                })
                                .OrderBy(a => a.code)
                                .ToList();
                        }

                        if (cancellationToken.IsCancellationRequested)
                            return Conflict();

                        //Get recursive allroot parent uniclasses
                        {
                            var currentUniclassesItems = productData.selectionUniclasses.ToList();
                            var rootParentItems = currentUniclassesItems.Where(a => a.parentId == null).ToList();
                            {
                                while (true)
                                {
                                    var existParentIds = rootParentItems.Select(a => a.id).ToList();
                                    var parentIds = currentUniclassesItems.Where(a => a.parentId != null).Select(a => a.parentId.Value).ToList();

                                    var retriveParentIds = parentIds.Except(existParentIds).ToList();

                                    if (!retriveParentIds.Any())
                                        break;

                                    currentUniclassesItems = searchCache.Uniclasses.AsParallel().Where(a => retriveParentIds.Contains(a.Id)).Select(c => new
                                    {
                                        id = c.Id,
                                        code = c.Code,
                                        title = c.Title,
                                        parentId = c.ParentId
                                    }).ToList();

                                    rootParentItems.AddRange(currentUniclassesItems.Where(a => a.parentId == null));
                                }
                            }

                            var selectionUniclassItems = new List<dynamic>();
                            selectionUniclassItems.AddRange(productData.selectionUniclasses);
                            selectionUniclassItems.AddRange(rootParentItems);
                            selectionUniclasses = selectionUniclassItems.OrderBy(a => a.code).Distinct().ToList();
                        }

                        if (cancellationToken.IsCancellationRequested)
                            return Conflict();

                        //Get recursive allroot parent uniformates
                        {
                            var currentUniformatItems = productData.selectionUniformats.ToList();
                            var rootParentItems = currentUniformatItems.Where(a => a.parentId == null).ToList();
                            {
                                while (true)
                                {
                                    var existParentIds = rootParentItems.Select(a => a.id).ToList();
                                    var parentIds = currentUniformatItems.Where(a => a.parentId != null).Select(a => a.parentId.Value).ToList();

                                    var retriveParentIds = parentIds.Except(existParentIds).ToList();

                                    if (!retriveParentIds.Any())
                                        break;

                                    currentUniformatItems = searchCache.Uniformats.AsParallel().Where(a => retriveParentIds.Contains(a.Id)).Select(c => new
                                    {
                                        id = c.Id,
                                        code = c.Code,
                                        title = c.Title,
                                        parentId = c.ParentId
                                    }).ToList();

                                    rootParentItems.AddRange(currentUniformatItems.Where(a => a.parentId == null));
                                }
                            }

                            var selectionUniformatItems = new List<dynamic>();
                            selectionUniformatItems.AddRange(productData.selectionUniformats);
                            selectionUniformatItems.AddRange(rootParentItems);
                            selectionUniformats = selectionUniformatItems.OrderBy(a => a.code).Distinct().ToList();
                        }

                        if (cancellationToken.IsCancellationRequested)
                            return Conflict();
                    }
                }
            }

#if DEBUG
            Debug.WriteLine(String.Format("Build response with filters for search in {0}ms", requestTime.ElapsedMilliseconds));
#endif
            var result = new
            {
                selectionProjectTypeIds = selectionProjectTypeIds.Distinct().ToList(),
                selectionExternalCertificates = selectionExternalCertificates.Distinct().ToList(),
                selectionCisfbs = selectionCisfbs,
                selectionExternalMasterformatIds = selectionExternalMasterformatIds.Distinct().ToList(),
                selectionOmniclasses = selectionOmniclasses,
                selectionUniclasses = selectionUniclasses,
                selectionUniformats = selectionUniformats,
                selectionMaterialLibrary = selectionMaterialLibrary,
                selectionMasterSpecEnabled = selectionMasterSpecEnabled,
                selectionSamplesAvailable = selectionSamplesAvailable,
                selectionQualityItems = selectionQualityItems,
                searchId = searchId
#if DEBUG
                ,
                requestTime = requestTime.ElapsedMilliseconds,
#endif
            };

            return Ok(result);
        }

        /// <summary>
        /// General Search products and manufacturer by full name
        /// </summary>
        /// <param name="q">Optional: Search query</param>
        /// <param name="keyword">Optional: Search by keyword</param>
        /// <param name="manufacturerId">Optional: Filter by manufacturer</param>
        /// <param name="categoryId">Optional: Filter by category</param>
        /// <param name="productLineId">Optional: Filter by product line</param>
        /// <param name="categoryVanityUrl">Optional: Filter by category</param>
        /// <param name="manufacturerVanityUrl">The manufacturer vanity URL.</param>
        /// <param name="projectTypeIds">Optional: Filter by project content types Exp: 1_3_5</param>
        /// <param name="externalCertificateIds">The external certificate ids.</param>
        /// <param name="certificateNames">The certificate names.</param>
        /// <param name="qualityItemIds">Optional: Filter by quality ids Exp: 1_3_55</param>
        /// <param name="omniclassId">Optional: Filter by omniclass</param>
        /// <param name="omniclassCode">The omniclass code.</param>
        /// <param name="uniclassId">The uniclass identifier.</param>
        /// <param name="uniclassCode">The uniclass code.</param>
        /// <param name="uniformatId">The uniformat identifier.</param>
        /// <param name="uniformatCode">The uniformat code.</param>
        /// <param name="detailId">The product detail identifier.</param>
        /// <param name="detailCode">The product detail code.</param>
        /// <param name="externalMasterformatId">Optional: Filter by masterformat</param>
        /// <param name="masterformatCode">The masterformat code.</param>
        /// <param name="cisfbId">The cisfb identifier.</param>
        /// <param name="cisfbCode">The cisfb code.</param>
        /// <param name="offset">Offset for pagination</param>
        /// <param name="count">Count of data in result</param>
        /// <param name="featured">Optional: True if need show only Featured products</param>
        /// <param name="published">If true - return only published items</param>
        /// <param name="sampleAvailable">If true - return only items with samples available</param>
        /// <param name="returnFilters">if set to <c>true</c> [return filters].</param>
        /// <param name="attachUrl">The attach URL.</param>
        /// <param name="regionId">The product region ids.</param>
        /// <param name="stateId">The product state ids</param>
        /// <param name="secondarySkip">The secondary skip.</param>
        /// <param name="secondaryTake">The secondary take.</param>
        /// <param name="relatedTake">Length of swimlanes for related results.</param>
        /// <param name="startersPosition">The starters position.</param>
        /// <param name="sortType">SortType: Relevant = 0, ContentRating= 1, ProductRating = 2, LatestProducts = 3</param>
        /// <param name="langCode">The language code</param>
        /// <param name="noCache">Disable search results cache</param>
        /// <returns>
        /// List of products with total count
        /// </returns>
        [HttpGet]
        [ActionName("GeneralSearch")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> GeneralSearch(
            CancellationToken cancellationToken,
            string q = null,
            string keyword = null,
            int manufacturerId = -1,
            string manufacturerIds = null,
            int categoryId = -1,
            int productLineId = -1,
            string categoryVanityUrl = null,
            string manufacturerVanityUrl = null,
            string projectTypeIds = null,
            string externalCertificateIds = null,
            string certificateNames = null,
            string qualityItemIds = null,
            int omniclassId = -1,
            string omniclassCode = null,
            int uniclassId = -1,
            string uniclassCode = null,
            int uniformatId = -1,
            string uniformatCode = null,
            int detailId = -1,
            string detailCode = null,
            int externalMasterformatId = -1,
            string masterformatCode = null,
            string masterformatName = null,
            int cisfbId = -1,
            string cisfbCode = null,
            int offset = 0,
            int count = 10,
            bool featured = false,
            bool published = false,
            bool sampleAvailable = false,
            string attachUrl = null,
            string regionId = null,
            string stateId = null,
            int secondarySkip = -1,
            int secondaryTake = -1,
            int relatedTake = 5,
            StartersPosition startersPosition = StartersPosition.EndWithStarters,
            ProductSortType sortType = ProductSortType.Relevant,
            string langCode = null,
            bool noCache = false)
        {
            Stopwatch requestTime = new Stopwatch();
            requestTime.Start();

            if (regionId == "null")
                regionId = null;

            if (stateId == "null")
                stateId = null;

            bool? searchManufacturerChildProducts = null;

            if (cancellationToken.IsCancellationRequested)
                return Conflict();

            List<int> intManufacturerIds = new();

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var searchCache = SearchCache.Get(unitOfWork); // ensure cache is loaded, this should happen whenever user first opens the market page

                if (manufacturerId == -1 && !string.IsNullOrWhiteSpace(manufacturerVanityUrl))
                {
                    manufacturerId = searchCache.Manufacturers.Values.FirstOrDefault(m => m.HubVanityURL == manufacturerVanityUrl)?.Id ?? -1;
                    searchManufacturerChildProducts = true; // include child manufacturers on Hub page
                }

                if (manufacturerId != -1)
                    intManufacturerIds.Add(manufacturerId);

                if (!string.IsNullOrWhiteSpace(manufacturerIds))
                {
                    string[] manufacturerIdStrings = manufacturerIds.Split("_").ToArray();
                    foreach (string manufacturerIdString in manufacturerIdStrings)
                    {
                        if (int.TryParse(manufacturerIdString, out int parsedManufacturerId))
                            intManufacturerIds.Add(parsedManufacturerId);
                    }
                }

                if (omniclassId == -1 && !string.IsNullOrWhiteSpace(omniclassCode))
                {
                    omniclassId = searchCache.Omniclasses.FirstOrDefault(m => m.Code == omniclassCode)?.Id ?? -1;
                }

                if (uniclassId == -1 && !string.IsNullOrWhiteSpace(uniclassCode))
                {
                    uniclassId = searchCache.Uniclasses.FirstOrDefault(m => m.Code == uniclassCode)?.Id ?? -1;
                }

                if (uniformatId == -1 && !string.IsNullOrWhiteSpace(uniformatCode))
                {
                    uniformatId = searchCache.Uniformats.FirstOrDefault(m => m.Code == uniformatCode)?.Id ?? -1;
                }

                if (detailId == -1 && !string.IsNullOrWhiteSpace(detailCode))
                {
                    detailId = (await unitOfWork.DetailRepository.GetAll().FirstOrDefaultAsync(m => m.Name.StartsWith(detailCode)))?.Id ?? -1;
                }

                if (externalMasterformatId == -1 && !string.IsNullOrWhiteSpace(masterformatCode))
                {
                    externalMasterformatId = searchCache.ExternalMasterformats.FirstOrDefault(m => m.Code.ToLower() == masterformatCode.ToLower())?.Id ?? -1;
                }
                if (externalMasterformatId == -1 && !string.IsNullOrWhiteSpace(masterformatName))
                {
                    externalMasterformatId = searchCache.ExternalMasterformats.FirstOrDefault(m => m.Title.ToLower() == masterformatName.ToLower())?.Id ?? -1;
                }

                if (cisfbId == -1 && !string.IsNullOrWhiteSpace(cisfbCode))
                {
                    cisfbId = searchCache.Cisfbs.FirstOrDefault(m => m.Code == cisfbCode)?.Id ?? -1;
                }

                List<int> externalCertificateIdsInt = null;
                if (!string.IsNullOrWhiteSpace(externalCertificateIds))
                {
                    externalCertificateIdsInt = externalCertificateIds.Split('_').Select(a => int.Parse(a)).ToList();
                }

                if (cancellationToken.IsCancellationRequested)
                    return Conflict();

                IQueryable<Manufacturer> queryManufacturers = null;
                var countOfManufacturers = 0;
                if (!string.IsNullOrWhiteSpace(q) || intManufacturerIds.Any())
                {
                    queryManufacturers = ManufacturerSearch.Search(new ManufacturerSearchOptions
                    {
                        ManufacturerIds = intManufacturerIds,
                        Published = published,
                        Query = q,
                        RegionId = regionId,
                        StateId = stateId
                    }, unitOfWork.ManufacturerRepository.GetAll().AsNoTracking(), unitOfWork);
                }
#if (DEBUG)
                Debug.WriteLine(String.Format("CommonController::GeneralSearch, Step 1 at {0}ms", requestTime.ElapsedMilliseconds));
#endif

                if (cancellationToken.IsCancellationRequested)
                    return Conflict();

                IQueryable<Detail> queryDetails = null;
                var countOfDetails = 0;
                if (!string.IsNullOrWhiteSpace(q) || intManufacturerIds.Any() || detailId != 1)
                {
                    try
                    {
                        queryDetails = DetailSearch.Search(new DetailSearchOptions
                        {
                            DetailId = detailId,
                            Published = published,
                            ManufacturerIds = intManufacturerIds,
                            RegionId = regionId,
                            StateId = stateId,
                            Query = q
                        }, unitOfWork.DetailRepository.GetAll().AsNoTracking(), unitOfWork);
                    }
                    catch (Exception)
                    { }
                }
#if (DEBUG)
                Debug.WriteLine(String.Format("CommonController::GeneralSearch, Step 1.1 at {0}ms", requestTime.ElapsedMilliseconds));
#endif

                if (cancellationToken.IsCancellationRequested)
                    return Conflict();

                List<dynamic> resultData = new List<dynamic>();
                var secondaryResults = new List<object>();
                dynamic relatedResults = null;

                if (queryManufacturers != null)
                {
                    countOfManufacturers = await queryManufacturers.CountAsync();
                    if (offset < countOfManufacturers) //Need to show founded manufacturers
                    {
                        var takeManufacturers = (countOfManufacturers - offset) < count ? (countOfManufacturers - offset) : count;
                        var manufacturers = queryManufacturers
                            //.OrderBy(a => a.Name) - ordered by search (by weight)
                            .Skip(offset)
                            .Take(takeManufacturers)
                            .Select(a => new
                            {
                                id = a.Id,
                                name = a.Name,
                                site = a.Site,
                                vanityUrl = a.HubVanityURL,
                                logo = new
                                {
                                    id = a.PhotoId,
                                    middleImgUrl = a.PhotoId != null ? a.Photo.MiddleImgUrl : null
                                },
                            }).ToList();

                        resultData.AddRange(manufacturers.Select(a => new
                        {
                            itemType = "manufacturer",
                            item = a,
                            weigth = 0
                        }));
                    }
                }
#if (DEBUG)
                Debug.WriteLine(String.Format("CommonController::GeneralSearch, Step 1.2 at {0}ms", requestTime.ElapsedMilliseconds));
#endif

                if (cancellationToken.IsCancellationRequested)
                    return Conflict();

                var detailResults = new List<dynamic>();
                if (queryDetails != null && !string.IsNullOrWhiteSpace(q))
                {
                    try
                    {
                        var resultDetails = await queryDetails.AsNoTracking().ToListAsync();
                        countOfDetails = resultDetails.Count;
                        if (countOfDetails > 0) //Need to show founded details
                        {
                            var details = resultDetails
                                //.Skip(?)
                                .Take(count)
                                .Select(a => new
                                {
                                    id = a.Id,
                                    name = a.Name,
                                    logo = new
                                    {
                                        id = a.PhotoId,
                                        middleImgUrl = a.PhotoId != null ? a.Photo.MiddleImgUrl : null
                                    },
                                }).ToList();

                            detailResults.AddRange(details.Select(a => new
                            {
                                itemType = "detail",
                                item = a,
                                weigth = 0
                            }));
                        }
                    }
                    catch (Exception)
                    { }
                }
#if (DEBUG)
                Debug.WriteLine(String.Format("CommonController::GeneralSearch, Step 2 at {0}ms", requestTime.ElapsedMilliseconds));
#endif

                if (cancellationToken.IsCancellationRequested)
                    return Conflict();

                var productIds = new List<int>();

                int countOfProducts = 0;
                int countOfStarters = 0;
                var take = (count - resultData.Count) < 0 ? 0 : (count - resultData.Count);
                if (take > 0)
                {
                    const int s_cachedResultsCount = 1000; // how many results to cache; make this configuration?

                    int skip = Math.Max(offset - countOfManufacturers, 0);

                    // not cached beyond this
                    if (take + skip > s_cachedResultsCount)
                    {
                        noCache = true;
                    }

                    var productSearchOptions = new ProductSearchOptions
                    {
                        CategoryVanityUrl = categoryVanityUrl,
                        Query = q,
                        Keyword = keyword,
                        CisfbId = cisfbId,
                        ExternalMasterformatId = externalMasterformatId,
                        OmniclassId = omniclassId,
                        UniclassId = uniclassId,
                        UniformatId = uniformatId,
                        DetailId = detailId,
                        IncludeHideOnMicrosite = true,
                        Featured = featured,
                        Published = published,
                        SamplesAvailable = sampleAvailable,
                        CategoryId = categoryId,
                        ProductLineId = productLineId,
                        ManufacturerIds = intManufacturerIds,
                        ExternalCertificateIds = !string.IsNullOrEmpty(externalCertificateIds) ? externalCertificateIdsInt : null,
                        ProductFileTypeIds = !string.IsNullOrEmpty(projectTypeIds) ? projectTypeIds.Split('_').Select(a => int.Parse(a)).ToList() : null,
                        QualityItemIds = !string.IsNullOrEmpty(qualityItemIds) ? qualityItemIds.Split('_').Select(a => int.Parse(a)).ToList() : null,
                        RegionId = regionId,
                        StateId = stateId,
                        AttachUrl = attachUrl,
                        SortType = startersPosition == StartersPosition.Weighted ? ProductSortType.Relevant : sortType,
                        StartersPosition = startersPosition,
                        Skip = noCache ? skip : 0,
                        Take = noCache ? take : s_cachedResultsCount,
                        SearchManufacturerChildProducts = searchManufacturerChildProducts,
                        LangCode = langCode,
                        SearchSource = SearchSource.Market
                    };

                    IProductSearchResults results;

                    var cacheKey = noCache ? string.Empty : JsonConvert.SerializeObject(productSearchOptions);
                    ProductSearchResultsCached cachedResults = _cacheService.Get<ProductSearchResultsCached>($"cachedResults{cacheKey}");
                    if (!noCache && cachedResults != null)
                    {
                        results = cachedResults;
#if (DEBUG)
                        Debug.WriteLine(String.Format("CommonController::GeneralSearch, Step 3a (from cache) at {0}ms", requestTime.ElapsedMilliseconds));
#endif
                    }
                    else
                    {
                        // call search algorithm
                        var databaseResults = ProductSearch.Search(productSearchOptions, unitOfWork.ProductRepository.GetAll().AsNoTracking(), unitOfWork.StarterRepository.GetAll().AsNoTracking(), searchCache, unitOfWork);
#if (DEBUG)
                        Debug.WriteLine(String.Format("CommonController::GeneralSearch, Step 3b (from database) at {0}ms", requestTime.ElapsedMilliseconds));
#endif

                        if (noCache)
                        {
                            // use original database results object
                            results = databaseResults;
                        }
                        else
                        {
                            // cache results and use cached version now as well
                            cachedResults = databaseResults.GetForCache();
                            _cacheService.Set($"cachedResults{cacheKey}", cachedResults, CacheConstants.ServerExpiration);
                            cachedResults.SearchTime = databaseResults.SearchTime;
                            results = cachedResults;
                        }
                    }

                    if (cancellationToken.IsCancellationRequested)
                        return Conflict();

                    categoryId = productSearchOptions.CategoryId; // could have been recognized from text query

#if (DEBUG)
                    Debug.WriteLine(String.Format("CommonController::GeneralSearch, Step 4 at {0}ms", requestTime.ElapsedMilliseconds));
#endif

#if (DEBUG)
                    // for performance testing - measure time it takes to retrieve just the Ids of all results to see how long the search itself took
                    /*
                    var primaryProductIds = results.PrimaryResults(skip, take, unitOfWork, p => new ProductSearchResult<object> { ProductId = p.Id }, s => new ProductSearchResult<object> { ProductId = 0 }).ToList();
                    Debug.WriteLine(String.Format("CommonController::GeneralSearch, Step 4.1 at {0}ms", requestTime.ElapsedMilliseconds));

                    var secondaryProductIds = results.SecondaryResults(p => new ProductSearchResult<object> { ProductId = p.Id }, secondarySkip, secondaryTake, unitOfWork).ToList();
                    Debug.WriteLine(String.Format("CommonController::GeneralSearch, Step 4.2 at {0}ms", requestTime.ElapsedMilliseconds));
                    */
#endif

                    productIds = results.AllResultsIds;

#if (DEBUG)
                    Debug.WriteLine(String.Format("CommonController::GeneralSearch, Step 4.3 at {0}ms", requestTime.ElapsedMilliseconds));
#endif

                    countOfProducts = results.CountOfProducts;
#if (DEBUG)
                    Debug.WriteLine(String.Format("CommonController::GeneralSearch, Step 5 at {0}ms", requestTime.ElapsedMilliseconds));
#endif
                    var primaryResults = results.PrimaryResults(skip, take, unitOfWork, a => new ProductSearchResult<object>
                    {
                        ProductId = a.Id,
                        StarterId = null,
                        ItemType = "product",
                        Projection = new
                        {
                            id = a.Id,
                            name = a.Name,
                            description = a.Description,
                            productRating = a.ProductRating,
                            contentRating = a.ContentRating,
                            externalCertificates = a.ProductCertificates.Where(b => b.ExternalCertificateId != null).Select(b => b.ExternalCertificateId.Value),
                            externalMasterformatIds = a.ProductMasterformats.Select(b => b.ExternalMasterformatId),
                            qualityItems = a.ProductQualityItems.Select(r => new
                            {
                                id = r.QualityItemId,
                                name = r.QualityItem.Name,
                                iconUrl = r.QualityItem.IconUrl
                            }),
                            projectFiles = a.ProductFiles.Where(f => !f.IsAttachment).Select(r => new
                            {
                                id = r.FileId,
                                fileName = r.File.FileName,
                                projectDataType = r.ProjectDataTypeId == null ? null : new
                                {
                                    id = r.ProjectDataTypeId,
                                },
                            }),
                            photoUrl = a.PhotoId != null ? a.Photo.MiddleImgUrl : a.ProductPhotos.FirstOrDefault().Photo.MiddleImgUrl,
                            manufacture = a.Manufacturer.Name,
                            manufacturerVanityURL = a.Manufacturer.HubVanityURL,
                            manufactureLogo = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.MiddleImgUrl : null,
                            categoryId = a.CategoryId,
                            category = a.Category.VanityUrl,
                            categoryName = a.Category.Name,
                        }
                    }, a => new ProductSearchResult<object>
                    {
                        ProductId = 0,
                        StarterId = a.Id,
                        ItemType = "starter",
                        Projection = new
                        {
                            id = a.Id,
                            name = a.Name,
                            manufacturerName = a.ManufacturerName,
                            brand = a.Brand,
                            orientation = a.Orientation,
                            preview = a.Preview,
                            stc = a.STC,
                            fstc = a.FSTC,
                            rvalue = a.RValue,
                            fire = a.Fire,
                            ul = a.UL,
                            iic = a.IIC,
                            fiic = a.FIIC,
                            nfpa285 = a.NFPA285
                        }
                    }
                    ).Select(r => new
                    {
                        itemType = r.ItemType,
                        item = r.Projection
                    });

                    if (cancellationToken.IsCancellationRequested)
                        return Conflict();

                    var secondaryCount = results.CountOfSecondaryResults;
                    secondaryResults = results.SecondaryResults(a => new ProductSearchResult<object>
                    {
                        ProductId = a.Id,
                        ItemType = "product",
                        Projection = new
                        {
                            id = a.Id,
                            name = a.Name,
                            photoUrl = a.PhotoId != null ? a.Photo.MiddleImgUrl : a.ProductPhotos.FirstOrDefault().Photo.MiddleImgUrl,
                            manufacture = a.Manufacturer.Name,
                            manufacturerVanityURL = a.Manufacturer.HubVanityURL,
                        }
                    }, secondarySkip, secondaryTake, unitOfWork
                    ).Select(r => new
                    {
                        itemType = r.ItemType,
                        item = r.Projection
                    } as object).ToList();

                    if (cancellationToken.IsCancellationRequested)
                        return Conflict();

                    var relatedByPhrase = results.SecondaryResultsByPhrase();
                    if (relatedByPhrase.Any())
                    {
                        var relatedIds = relatedByPhrase.SelectMany(f => f.Value.Take(relatedTake)).ToList();
                        var relatedProducts = (await unitOfWork.ProductRepository.GetAll().AsNoTracking().Where(p => relatedIds.Contains(p.Id)).Select(a => new ProductSearchResult<object>
                        {
                            ProductId = a.Id,
                            ItemType = "product",
                            Projection = new
                            {
                                id = a.Id,
                                name = a.Name,
                                photoUrl = a.PhotoId != null ? a.Photo.MiddleImgUrl : a.ProductPhotos.FirstOrDefault().Photo.MiddleImgUrl,
                                manufacture = a.Manufacturer.Name,
                                manufacturerVanityURL = a.Manufacturer.HubVanityURL,
                            }
                        }).ToListAsync())
                        .ToDictionary(p => p.ProductId, p => p);

                        var relatedLanes = relatedByPhrase.ToDictionary(k => k.Key, k => k.Value.Take(relatedTake).Select(p => relatedProducts[p]).Select(r => new
                        {
                            itemType = r.ItemType,
                            item = r.Projection
                        }).ToList());

                        relatedResults = new
                        {
                            originalQuery = q,
                            subQueries = relatedLanes.Select(r => new
                            {
                                subQuery = r.Key,
                                results = r.Value
                            })
                        };
                    }

                    if (cancellationToken.IsCancellationRequested)
                        return Conflict();
#if (DEBUG)
                    Debug.WriteLine(String.Format("CommonController::GeneralSearch, Step 5 at {0}ms", requestTime.ElapsedMilliseconds));
#endif
                    resultData.AddRange(primaryResults.ToList());
                    countOfProducts = results.CountOfProducts;
                    countOfStarters = results.CountOfStarters;

                    requestTime.Stop();

                    if (cancellationToken.IsCancellationRequested)
                        return Conflict();

                    if (offset == 0 && (!string.IsNullOrEmpty(q) ||
                        intManufacturerIds.Any() ||
                        categoryId != -1 ||
                        cisfbId != -1 ||
                        uniformatId != -1 ||
                        uniclassId != -1 ||
                        externalMasterformatId != -1 ||
                        omniclassId != -1 ||
                        projectTypeIds != null ||
                        qualityItemIds != null)
                    )
                    {
                        try
                        {
                            var userName = AuthHelper.GetUserInfo(Request, ClaimTypes.Email);

                            var backgroundTask = Task.Run(async () =>
                            {
                                using (IUnitOfWork localUnitOfWork = UnitOfWork.Create())
                                {
                                    await LogSearchAsync(productSearchOptions, countOfProducts, secondaryCount, userName, HttpContext?.Connection?.RemoteIpAddress.ToString(), localUnitOfWork, results.SearchTime, requestTime.ElapsedMilliseconds);

                                    string manufacturerNames = string.Empty;
                                    if (productSearchOptions.ManufacturerIds != null && productSearchOptions.ManufacturerIds.Any())
                                    {
                                        manufacturerNames = string.Join(',', searchCache.Manufacturers.Values.Where(a => productSearchOptions.ManufacturerIds.Contains(a.Id)).Select(x => x.Name));
                                    }
                                    var categoryName = searchCache.Categories.Values.FirstOrDefault(a => a.Id == productSearchOptions.CategoryId || (productSearchOptions.CategoryVanityUrl != null && a.VanityUrl == productSearchOptions.CategoryVanityUrl))?.Name;
                                    var cisfbTitle = searchCache.Cisfbs.FirstOrDefault(a => a.Id == productSearchOptions.CisfbId)?.Title;
                                    var masterformatTitle = searchCache.ExternalMasterformats.FirstOrDefault(a => a.Id == productSearchOptions.ExternalMasterformatId)?.Title;
                                    var omniclassTitle = searchCache.Omniclasses.FirstOrDefault(a => a.Id == productSearchOptions.OmniclassId)?.Title;
                                    var uniclassTitle = searchCache.Uniclasses.FirstOrDefault(a => a.Id == productSearchOptions.UniclassId)?.Title;
                                    var uniformatTitle = searchCache.Uniformats.FirstOrDefault(a => a.Id == productSearchOptions.UniformatId)?.Title;

                                    string cartificatesNames = null;

                                    string productFileTypes = null;
                                    if (productSearchOptions.ProductFileTypeIds != null)
                                    {
                                        var poductFileTypeList = productSearchOptions.ProductFileTypeIds.Select(a => a.ToString()).ToList();
                                        productFileTypes = poductFileTypeList.Count > 0 ? string.Join(", ", poductFileTypeList) : null;
                                    }

                                    string qualityItemNames = null;
                                    if (productSearchOptions.QualityItemIds != null)
                                    {
                                        var qualityItemList = await localUnitOfWork.QualityItemRepository.GetAll().AsNoTracking().Where(a => productSearchOptions.QualityItemIds.Contains(a.Id)).Select(a => a.Name).ToListAsync();
                                        qualityItemNames = qualityItemList.Count > 0 ? string.Join(", ", qualityItemList) : null;
                                    }

                                    if (countOfProducts == 0)
                                    {
                                        var productLineName = (await localUnitOfWork.ProductLineRepository.GetAll().FirstOrDefaultAsync(a => a.Id == productSearchOptions.ProductLineId))?.Name;
                                        var user = await localUnitOfWork.UserRepository.GetAll().FirstOrDefaultAsync(a => userName != null && a.Email == userName);

                                        await _slackWebHook.SendEmptySearchMessage(
                                            userName,
                                            user?.FirstName,
                                            user?.LastName,
                                            q,
                                            manufacturerNames,
                                            categoryName,
                                            productLineName,
                                            masterformatTitle,
                                            omniclassTitle,
                                            uniclassTitle,
                                            uniformatTitle,
                                            cartificatesNames,
                                            productFileTypes,
                                            cisfbTitle,
                                            qualityItemNames,
                                            Request.GetDisplayUrl()
                                        );
                                    }

                                    await _slackWebHook.SendSearchMessage(
                                        userName,
                                        q,
                                        manufacturerNames,
                                        categoryName,
                                        cisfbTitle,
                                        masterformatTitle,
                                        omniclassTitle,
                                        uniclassTitle,
                                        uniformatTitle,
                                        cartificatesNames,
                                        productFileTypes,
                                        qualityItemNames
                                    );
                                }
                            });
                        }
                        catch (Exception e)
                        {
                            Log.Error(e.Message, e);
                            Trace.TraceError(e.Message);
                        }
                    }
                }

                var searchId = Guid.NewGuid().ToString();
                _cacheService.Set($"productIds{searchId}", productIds, CacheConstants.ServerExpiration);

                var countTotal = countOfManufacturers + countOfStarters + countOfProducts;
                var result = new
                {
                    count = countTotal,
                    data = resultData,
                    relatedResults = relatedResults,
                    secondaryResults = secondaryResults,
                    detailResults = detailResults,
#if (DEBUG)
                    requestTime = requestTime.ElapsedMilliseconds,
#endif
                    searchId = searchId
                };

                return Ok(result);
            }
        }

        /// <summary>
        /// Detailed information about product search results
        /// </summary>
        /// <param name="q">Mandatory: Search query</param>
        /// <returns>
        /// Search explanation
        /// </returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        [ActionName("GeneralSearchExplain")]
        public async Task<IActionResult> GeneralSearchExplain(string q = null, int rotationMethod = -1, string langCode = null, int categoryId = -1)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var searchCache = SearchCache.Get(unitOfWork);
                var productSearchOptions = new ProductSearchOptions
                {
                    Query = q,
                    Skip = 0,
                    Published = true,
                    Take = 100,
                    GenerateExplain = true,
                    ManufacturerIds = null,
                    CategoryId = categoryId,
                    ProductLineId = -1,
                    CisfbId = -1,
                    ExternalMasterformatId = -1,
                    OmniclassId = -1,
                    UniclassId = -1,
                    UniformatId = -1,
                    DetailId = -1,
                    RotationMethod = rotationMethod,
                    LangCode = langCode,
                    SearchSource = SearchSource.Market
                };

                var results = ProductSearch.Search(productSearchOptions, unitOfWork.ProductRepository.GetAll().AsNoTracking(), null, searchCache, unitOfWork);

                var primaryResults = results.PrimaryResults(0, 100, unitOfWork, a => new ProductSearchResult<ProductExplain>
                {
                    ProductId = a.Id,
                    ItemType = "product",
                    Projection = new ProductExplain { ProductId = a.Id, Product = a.Name }
                }, null).ToList();

                int position = 1;
                foreach (var result in primaryResults)
                {
                    result.Projection.Position = position++;

                    var rank = results.SearchResults?.Rank(result.ProductId);
                    if (rank != null)
                    {
                        result.Projection.Rank = rank.MajorRank;
                        result.Projection.Score = (decimal)Math.Round(rank.Score, 2); // before rotation drift
                        result.Projection.Explain = string.Join(", ", rank.Explain);
                    }
                }

                return Ok(new
                {
                    searchLog = results.SearchResults.SearchExplain,
                    results = primaryResults.Select(p => p.Projection),
                    secondary = results.SecondaryResultsByPhrase().ToDictionary(r => r.Key, r => r.Value.Count)
                });
            }
        }

        /// <summary>
        /// Get summary statistics
        /// </summary>
        /// <param name="dateFrom"></param>
        /// <param name="dateTo"></param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        [ActionName("GetStatistics")]
        public async Task<IActionResult> GetStatistics(DateTime dateFrom, DateTime dateTo)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                dateFrom = dateFrom.Date;
                dateTo = dateTo.Date;

                var totalManufacturers = await unitOfWork.ManufacturerRepository.GetAll().CountAsync();
                var totalProducts = await unitOfWork.ProductRepository.GetAll().CountAsync();
                var totalCertifications = 0;
                var httpClient = _httpClientFactory.CreateClient();
                {
                    var url = new Flurl.Url(ConfigurationHelper.GetValue("CBOBaseAddress")).AppendPathSegment("api/Certificate/Count");
                    using var certificationCountResult = await httpClient.GetAsync(url);
                    if (certificationCountResult.IsSuccessStatusCode)
                    {
                        totalCertifications = JsonConvert.DeserializeObject<int>(await certificationCountResult.Content.ReadAsStringAsync());
                    }
                }
                var totalCategories = await unitOfWork.CategoryRepository.GetAll().CountAsync();
                var totalUsers = 0;                
                {
                    Url bimsmithUrl = new Url(ConfigurationHelper.GetValue("BimsmithApiUrl"))
                        .AppendPathSegment("Users/Statistics")
                        .SetQueryParam("accessToken", ConfigurationHelper.GetValue("BimsmithApiToken"));

                    using HttpResponseMessage bimsmithResult = await httpClient.GetAsync(bimsmithUrl);
                    if (bimsmithResult.IsSuccessStatusCode)
                    {
                        BIMsmithUserStatisticsDto bimsmithUserStatisticsDto = JsonConvert.DeserializeObject<BIMsmithUserStatisticsDto>(await bimsmithResult.Content.ReadAsStringAsync());
                        totalUsers = bimsmithUserStatisticsDto.TotalUsersCount;
                    }
                }
                var totalKeyStats = await unitOfWork.KeyStatRepository.GetAll().CountAsync();

                //TODO change to entity model
                var totalFiles = await unitOfWork.ProductFileRepository.GetAll().CountAsync();
                var totalAttachmentFiles = await unitOfWork.ProductFileRepository.GetAll().Where(a => a.IsAttachment).CountAsync();
                var totalRevitFiles = await unitOfWork.ProductFileRepository.GetAll().Where(a => !a.IsAttachment && a.ProjectDataTypeId != null && a.ProjectDataType.Title == "Revit").CountAsync();
                var totalAutoCADFiles = await unitOfWork.ProductFileRepository.GetAll().Where(a => !a.IsAttachment && a.ProjectDataTypeId != null && a.ProjectDataType.Title == "AutoCAD").CountAsync();
                var totalSketchupFiles = await unitOfWork.ProductFileRepository.GetAll().Where(a => !a.IsAttachment && a.ProjectDataTypeId != null && a.ProjectDataType.Title == "Sketchup").CountAsync();
                var totalIFCFiles = await unitOfWork.ProductFileRepository.GetAll().Where(a => !a.IsAttachment && a.ProjectDataTypeId != null && a.ProjectDataType.Title == "IFC").CountAsync();
                var totalMax3dFiles = await unitOfWork.ProductFileRepository.GetAll().Where(a => !a.IsAttachment && a.ProjectDataTypeId != null && a.ProjectDataType.Title == "3ds Max").CountAsync();
                var totalARCHICADFiles = await unitOfWork.ProductFileRepository.GetAll().Where(a => !a.IsAttachment && a.ProjectDataTypeId != null && a.ProjectDataType.Title == "ARCHICAD").CountAsync();
                var totalIESFiles = await unitOfWork.ProductFileRepository.GetAll().Where(a => !a.IsAttachment && a.ProjectDataTypeId != null && a.ProjectDataType.Title == "IES").CountAsync();
                var totalBentleyFiles = await unitOfWork.ProductFileRepository.GetAll().Where(a => !a.IsAttachment && a.ProjectDataTypeId != null && a.ProjectDataType.Title == "Bentley").CountAsync();
                var totalVectorworksFiles = await unitOfWork.ProductFileRepository.GetAll().Where(a => !a.IsAttachment && a.ProjectDataTypeId != null && a.ProjectDataType.Title == "Vectorworks").CountAsync();

                var manufactureresByDate = await unitOfWork.ManufacturerRepository.GetAll().Where(a => a.CreatedDate >= dateFrom && a.CreatedDate <= dateTo)
                         .GroupBy(a => a.CreatedDate.Date)
                         .Select(a => new
                         {
                             date = a.Key,
                             count = a.Count()
                         })
                         .AsNoTracking()
                         .ToListAsync();

                var productsByDate = await unitOfWork.ProductRepository.GetAll().Where(a => a.CreatedDate >= dateFrom && a.CreatedDate <= dateTo)
                         .GroupBy(a => a.CreatedDate.Date)
                         .Select(a => new
                         {
                             date = a.Key,
                             count = a.Count()
                         })
                         .AsNoTracking()
                         .ToListAsync();

                var filesByDate = await unitOfWork.FileRepository.GetAll().Where(a => a.CreatedDate >= dateFrom && a.CreatedDate <= dateTo)
                     .GroupBy(a => a.CreatedDate.Date)
                     .Select(a => new
                     {
                         date = a.Key,
                         count = a.Count()
                     })
                     .AsNoTracking()
                     .ToListAsync();

                var usersByDate = await unitOfWork.UserRepository.GetAll().Where(a => a.CreatedDate >= dateFrom && a.CreatedDate <= dateTo)
                             .GroupBy(a => a.CreatedDate.Date)
                             .Select(a => new
                             {
                                 date = a.Key,
                                 count = a.Count()
                             })
                             .AsNoTracking()
                             .ToListAsync();

                List<dynamic> data = new List<dynamic>();
                var dateFromIterator = dateFrom;
                while (dateFromIterator <= dateTo)
                {
                    var productData = productsByDate.FirstOrDefault(a => a.date == dateFromIterator);
                    var manufacturerData = manufactureresByDate.FirstOrDefault(a => a.date == dateFromIterator);
                    var filesData = filesByDate.FirstOrDefault(a => a.date == dateFromIterator);
                    var usersData = usersByDate.FirstOrDefault(a => a.date == dateFromIterator);

                    data.Add(new
                    {
                        date = dateFromIterator,
                        products = productData == null ? 0 : productData.count,
                        manufacturers = manufacturerData == null ? 0 : manufacturerData.count,
                        files = filesData == null ? 0 : filesData.count,
                        users = usersData == null ? 0 : usersData.count,
                    });

                    dateFromIterator = dateFromIterator.AddDays(1);
                }

                var result = new
                {
                    totalManufacturers = totalManufacturers,
                    totalProducts = totalProducts,
                    totalCertifications = totalCertifications,
                    totalCategories = totalCategories,
                    totalUsers = totalUsers,
                    totalKeyStats = totalKeyStats,
                    productFiles = new
                    {
                        totalFiles = totalFiles,
                        totalAttachmentFiles = totalAttachmentFiles,
                        totalRevitFiles = totalRevitFiles,
                        totalAutoCADFiles = totalAutoCADFiles,
                        totalSketchupFiles = totalSketchupFiles,
                        totalIFCFiles = totalIFCFiles,
                        totalMax3dFiles = totalMax3dFiles,
                        totalARCHICADFiles = totalARCHICADFiles,
                        totalIESFiles = totalIESFiles,
                        totalBentleyFiles = totalBentleyFiles,
                        totalVectorworksFiles = totalVectorworksFiles
                    },
                    chartData = data
                };

                return Ok(result);
            }
        }

        /// <summary>
        /// Get all email subscribers
        /// </summary>
        /// <returns></returns>
        [Authorize(Roles = DbConstants.AdminRole)]
        [HttpGet]
        [ActionName("GetEmailSubscribers")]
        public async Task<IActionResult> GetEmailSubscribers(int offset = 0, int count = 10, SubscriberSource source = SubscriberSource.All)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var emailSubscribersQuery = unitOfWork.EmailSubscriberRepository.GetAll();

                var countOfData = await emailSubscribersQuery.CountAsync();

                var emailSubscribers = await emailSubscribersQuery.OrderByDescending(a => a.Date)
                    .Skip(offset)
                    .Take(count)
                    .Select(a => new
                    {
                        email = a.Email,
                        date = a.Date,
                        source = a.Source
                    })
                    .AsNoTracking()
                    .ToListAsync();

                var result = new
                {
                    count = countOfData,
                    data = emailSubscribers,
                };

                return Ok(result);
            }
        }

        /// <summary>
        /// Log search query in history
        /// </summary>
        public static async Task LogSearchAsync(
            ProductSearchOptions options,
            int numResults,
            int numSecondary,
            string userName,
            string ipAddress,
            IUnitOfWork unitOfWork,
            long searchTime,
            long totalTime)
        {
            unitOfWork.SearchHistoryRepository.Insert(new SearchHistory
            {
                Email = QueryToken.Trim(userName, 100),
                IPAddress = QueryToken.Trim(ipAddress, 50),
                Query = QueryToken.Trim(options.Query ?? options.Keyword, 100),
                QueryDate = DateTime.UtcNow,
                NumResults = numResults,
                NumSecondary = numSecondary,
                SearchTime = searchTime,
                TotalTime = totalTime,
                Options = JsonConvert.SerializeObject(options, new JsonSerializerSettings
                {
                    NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore,
                    DefaultValueHandling = DefaultValueHandling.Ignore
                })
            });
            await unitOfWork.SaveAsync();
        }

        /// <summary>
        /// Get search history report in Excel
        /// </summary>
        /// <param name="dateFrom">Start date for export</param>
        /// <param name="dateTo">End date for export</param>
        /// <param name="f">1 to include fulfilled searches (with results), 0 otherwise</param>
        /// <param name="u">1 to include unfulfilled searches (no results), 0 otherwise</param>
        /// <param name="combine">True to combine similar searches to one row, false otherwise. Default value is false</param>
        /// <param name="authToken">The authorization token</param>
        /// <returns>Excel file attachment</returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        [ActionName("GetSearchHistory")]
        public async Task<IActionResult> GetSearchHistory(DateTime dateFrom, DateTime dateTo, int f = 1, int u = 1, bool combine = false, string authToken = null)
        { 
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var startTime = dateFrom;
                var endTime = dateTo.AddDays(1);
                byte[] excelFile;

                if (combine)
                {
                    var historyGroups = unitOfWork.SearchHistoryRepository.GetAll().Where(s => s.QueryDate >= startTime && s.QueryDate < endTime &&
                        ((u == 1 && s.NumResults == 0) || (f == 1 && s.NumResults > 0))).AsEnumerable().GroupBy(x => x.Query?.Trim()).OrderByDescending(x => x.Count()).ToList();
                    excelFile = await ExcelSearchHistoryProvider.GetCombinedExcelAsync(historyGroups, unitOfWork);
                }
                else
                {
                    var histories = unitOfWork.SearchHistoryRepository
                        .GetAll()
                        .Where(s => s.QueryDate >= startTime && s.QueryDate < endTime);
                    if (u == 1)
                    {
                        histories = histories.Where(w => w.NumResults == 0);
                    }
                    if (f == 1)
                    {
                        histories = histories.Where(w => w.NumResults > 0);
                    }
                    var historiesList = await histories.OrderByDescending(q => q.QueryDate).AsNoTracking().ToListAsync();
                    excelFile = await ExcelSearchHistoryProvider.GetExcelAsync(historiesList, unitOfWork);
                }

                return File(excelFile, "text/csv", $"Market Search History {dateFrom:yyyyMMdd} - {dateTo:yyyyMMdd}.xlsx");
            }
        }

        /// <summary>
        /// Clears the cache.
        /// </summary>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        [ActionName("ClearCache")]
        public IActionResult ClearCache()
        {
            CacheHelper.ClearAllCacheAndInvalidateSearch();
            return Ok();
        }

        /// <summary>
        /// Retrieves the cache for introspection.
        /// </summary>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        [ActionName("GetCache")]
        public IActionResult GetCache()
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var searchCache = SearchCache.Get(unitOfWork);
                return Ok(searchCache.Dump());
            }
        }
    }
}
