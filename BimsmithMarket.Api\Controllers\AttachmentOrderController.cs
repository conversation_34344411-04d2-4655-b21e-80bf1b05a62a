﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("api/[controller]/[action]")]
    public class AttachmentOrderController : BaseApiController
    {
        /// <summary>
        /// The log manager
        /// </summary>
        private readonly IAttachmentOrderService _attachmentOrderService;

        public AttachmentOrderController(IAttachmentOrderService attachmentOrderService)
        {
            _attachmentOrderService = attachmentOrderService;
        }

        /// <summary>
        /// Get list of all categories from database
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier.</param>
        /// <param name="q">The q.</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("List")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> List(int manufacturerId, string q = null)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _attachmentOrderService.ListAsync(unitOfWork, manufacturerId, q));
            }
        }

        /// <summary>
        /// Get detailed information about Category by Id
        /// </summary>
        /// <param name="id">Id of Category</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("Get")]
        public async Task<IActionResult> Get(int id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _attachmentOrderService.GetAsync(unitOfWork, id));
            }
        }

        /// <summary>
        /// Add new Category to Group: Role-ADMIN
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier.</param>
        /// <param name="model">The model.</param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("AddEdit")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> AddEdit([FromQuery] int manufacturerId, [FromBody] AddAttachmentOrderModel[] model)
        {
            var response = new List<dynamic>();
            using (var unitOfWork = UnitOfWork.Create())
            {
                var responce = await _attachmentOrderService.AddEditAsync(unitOfWork, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), manufacturerId, model);

                CacheHelper.ClearSpecificCache("*/api/AttachmentOrder/List*");

                return Ok(response);
            }
        }


        /// <summary>
        /// Get detailed information about Category by Id
        /// </summary>
        /// <param name="id">Id of Category</param>
        /// <param name="manufacturerId">The manufacturer identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("Delete")]
        public async Task<IActionResult> Delete(int? id = null, int? manufacturerId = null)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                await _attachmentOrderService.DeleteAsync(unitOfWork, id, manufacturerId);

                CacheHelper.ClearSpecificCache("*/api/AttachmentOrder/List*");

                return Ok();
            }
        }
    }
}