﻿using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface ICompanyService
    {
        Task<object> ListAsync(IUnitOfWork unitOfWork, int offset, int count);
        Task<object> AddAsync(IUnitOfWork unitOfWork, AddCompanyModel model);
        Task<object> EditAsync(IUnitOfWork unitOfWork, EditCompanyModel model);
        Task<object> GetExcelDataAsync(IUnitOfWork unitOfWork, int fileId);
        Task<object> SyncAsync(IUnitOfWork unitOfWork, string documentDBAuthKey, string bimsmith, string controller, int id);
        Task<object> CompanyInfoAsync(IUnitOfWork unitOfWork);
    }
}
