﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class ChangeLogService : IChangeLogService
    {
        public async Task<PaginationListDto<AdminListChangeLogDto>> AdminListAsync(
            IUnitOfWork unitOfWork,
            EntityType entityType,
            int entityId,
            string query = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int offset = 0,
            int count = 10)
        {
            IQueryable<ChangeLog> dbQuery = unitOfWork.ChangeLogRepository.GetAll()
                .Where(x => x.EntityType == entityType
                         && x.EntityId == entityId);

            if (!string.IsNullOrWhiteSpace(query))
                dbQuery = dbQuery.Where(x => x.Field.ToLower().Contains(query.ToLower()));

            if (startDate != null)
                dbQuery = dbQuery.Where(x => x.CreatedDate.Date >= startDate.Value.Date);

            if (endDate != null)
                dbQuery = dbQuery.Where(x => x.CreatedDate.Date <= endDate.Value.Date);

            int totalCount = await dbQuery.CountAsync();

            AdminListChangeLogDto[] items = await dbQuery
                .OrderByDescending(x => x.Id)
                .ProjectToType<AdminListChangeLogDto>()
                .Skip(offset)
                .Take(count)
                .ToArrayAsync();

            return new PaginationListDto<AdminListChangeLogDto>
            {
                Count = totalCount,
                Data = items
            };
        }

        public async Task<ChangeLogExcelDto[]> ExcelListAsync(
            IUnitOfWork unitOfWork,
            EntityType entityType,
            int entityId,
            DateTime? startDate = null,
            DateTime? endDate = null)
        {
            IQueryable<ChangeLog> dbQuery = unitOfWork.ChangeLogRepository.GetAll()
                .Where(x => x.EntityType == entityType
                         && x.EntityId == entityId);

            if (startDate != null)
                dbQuery = dbQuery.Where(x => x.CreatedDate.Date >= startDate.Value.Date);

            if (endDate != null)
                dbQuery = dbQuery.Where(x => x.CreatedDate.Date <= endDate.Value.Date);

            return await dbQuery
                .OrderByDescending(x => x.Id)
                .ProjectToType<ChangeLogExcelDto>()
                .ToArrayAsync();
        }

        public async Task TrackAndLogChangesAsync<T>(T currentModel, T newModel, EntityType entityType, int entityId, string userId, IUnitOfWork unitOfWork)
        {
            List<ChangeDto> changes = TrackChanges(currentModel, newModel);
            await LogChanges(changes, entityType, entityId, userId, unitOfWork);
        }

        public async Task LogEntityActionAsync(EntityType entityType, int entityId, EntityAction entityAction, string userId, IUnitOfWork unitOfWork)
        {
            ChangeLog changeLog = new ChangeLog
            {
                CreatedById = userId,
                EntityAction = entityAction,
                EntityId = entityId,
                EntityType = entityType
            };

            unitOfWork.ChangeLogRepository.Insert(changeLog);

            await unitOfWork.SaveAsync();
        }

        #region private methods
        private List<ChangeDto> TrackChanges<T>(T oldObject, T newObject)
        {
            if (oldObject == null || newObject == null)
                return new List<ChangeDto>();

            List<ChangeDto> changes = new();
            Type type = typeof(T);

            foreach (PropertyInfo field in type.GetProperties(BindingFlags.Instance | BindingFlags.Public))
            {
                object oldObjectValue = field.GetValue(oldObject);
                object newObjectValue = field.GetValue(newObject);

                string oldObjectStringValue = JsonConvert.SerializeObject(oldObjectValue ?? string.Empty);
                string newObjectStringValue = JsonConvert.SerializeObject(newObjectValue ?? string.Empty);

                if (!oldObjectStringValue.Equals(newObjectStringValue))
                    changes.Add(
                        new ChangeDto
                        {
                            Field = field.Name,
                            OldValue = oldObjectStringValue,
                            NewValue = newObjectStringValue
                        });
            }

            return changes;
        }

        private async Task LogChanges(List<ChangeDto> changes, EntityType entityType, int entityId, string userId, IUnitOfWork unitOfWork)
        {
            foreach (ChangeDto change in changes)
            {
                ChangeLog changeLog = new ChangeLog
                {
                    CreatedById = userId,
                    EntityAction = EntityAction.Update,
                    EntityId = entityId,
                    EntityType = entityType,
                    Field = change.Field,
                    OldValue = change.OldValue,
                    NewValue = change.NewValue
                };

                unitOfWork.ChangeLogRepository.Insert(changeLog);
            }

            await unitOfWork.SaveAsync();
        }
        #endregion
    }
}