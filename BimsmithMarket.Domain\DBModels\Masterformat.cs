﻿using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    [Index(nameof(Code), Name = "IX_Masterformat_Code")]
    [Index(nameof(Title), Name = "IX_Masterformat_Title")]
    public class Masterformat
    {
        public int Id { get; set; }

        public int? ParentId { get; set; }

        [StringLength(200)]
        public string Code { get; set; }

        [StringLength(200)]
        public string Title { get; set; }

        [ForeignKey("ParentId")]
        public virtual Masterformat Parent { get; set; }

        public virtual ICollection<Masterformat> Children { get; set; }

        public Masterformat()
        {
            Children = new List<Masterformat>();
        }
    }
}