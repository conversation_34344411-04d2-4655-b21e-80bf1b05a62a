﻿using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Dto
{
    public class MicrositeListWithStarterFiltersResultDto
    {
        public ICollection<dynamic> ProjectDataTypes { get; set; }

        public ICollection<MicrositeListWithStarterFiltersResultKeyStatDto> KeyStats { get; set; }
    }

    public class MicrositeListWithStarterFiltersResultKeyStatDto
    {
        public int KeyStatId { get; set; }

        public string KeyStatName { get; set; }

        public IEnumerable<MicrositeListWithStarterFiltersResultKeyStatValueDto> Values { get; set; }

        public MicrositeListWithStarterFiltersResultKeyStatDto()
        {
            Values = new List<MicrositeListWithStarterFiltersResultKeyStatValueDto>();
        }
    }

    public class MicrositeListWithStarterFiltersResultKeyStatValueDto : MicrositeListWithStarterFiltersKeyStatValueDto
    {
        public string KeyStatUnitDescription { get; set; }

        public string KeyStatUnitAName { get; set; }

        public string KeyStatUnitBName { get; set; }
    }

    public class MicrositeListWithStarterFiltersResultKeyStatValueDtoComparer : IEqualityComparer<MicrositeListWithStarterFiltersResultKeyStatValueDto>
    {
        public bool Equals(MicrositeListWithStarterFiltersResultKeyStatValueDto x, MicrositeListWithStarterFiltersResultKeyStatValueDto y)
        {
            return x.Value == y.Value
                && x.MaxRangeValue == y.MaxRangeValue
                && x.KeyStatType == y.KeyStatType
                && x.KeyStatUnitId == y.KeyStatUnitId
                && x.KeyStatUnitDescription == y.KeyStatUnitDescription
                && x.KeyStatUnitAName == y.KeyStatUnitAName
                && x.KeyStatUnitBName == y.KeyStatUnitBName;
        }

        public int GetHashCode(MicrositeListWithStarterFiltersResultKeyStatValueDto obj)
        {
            return (obj.Value == null ? 0 : obj.Value.GetHashCode())
                + (obj.MaxRangeValue == null ? 0 : obj.MaxRangeValue.GetHashCode())
                + obj.KeyStatType.GetHashCode()
                + (obj.KeyStatUnitId == null ? 0 : obj.KeyStatUnitId.GetHashCode())
                + (obj.KeyStatUnitDescription == null ? 0 : obj.KeyStatUnitDescription.GetHashCode())
                + (obj.KeyStatUnitAName == null ? 0 : obj.KeyStatUnitAName.GetHashCode())
                + (obj.KeyStatUnitBName == null ? 0 : obj.KeyStatUnitBName.GetHashCode());
        }
    }
}