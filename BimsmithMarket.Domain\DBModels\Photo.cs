﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class Photo
    {
        public int Id { get; set; }

        public string SmallImgUrl { get; set; }

        public string MiddleImgUrl { get; set; }

        public string OriginalImgUrl { get; set; }

        public string Name { get; set; }

        public string UploadUrl { get; set; }

        public string CreatedById { get; set; }

        public DateTime CreatedDate { get; set; }

        public int SyncStatusCode { get; set; }

        public int UpdatesCount { get; set; }

        /// ------------------------------------------
        [<PERSON><PERSON><PERSON>("CreatedById")]
        public virtual ApplicationUser CreatedBy { get; set; }
    }
}