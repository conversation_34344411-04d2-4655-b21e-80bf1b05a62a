﻿using BIMsmithMarket.Domain.CustomAttributes;
using BIMsmithMarket.Domain.Enums.RevitProcessing;
using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Dto.RevitProcessing
{
    public class RevitJobResultDto
    {
        [RequiredNotDefault]
        public int Id { get; set; }

        public IFormFile File { get; set; }

        [Required]
        public string Report { get; set; }

        [RequiredNotDefault]
        public RevitJobStatus Status { get; set; }
    }
}