﻿using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Models
{
    public class AddProjectDataTypeModel
    {
        [Required]
        public string Title { get; set; }
        public int? ParentId { get; set; }

        [Required]
        public string Description { get; set; }

        [Required]
        public string VanityUrl { get; set; }

        public string MetaTitle { get; set; }

        public string MetaDescription { get; set; }

        public string MetaKeyWords { get; set; }

        public string Header { get; set; }

        public int? ImageId { get; set; }

        public int? MicrositeImageId { get; set; }

        public int? IconId { get; set; }

        public int? ActiveImageId { get; set; }

        public int? DefaultImageId { get; set; }
    }

    public class EditProjectDataTypeModel : AddProjectDataTypeModel
    {
        [Required]
        public int Id { get; set; }
    }
}