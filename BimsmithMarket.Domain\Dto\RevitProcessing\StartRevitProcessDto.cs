﻿using BIMsmithMarket.Domain.CustomAttributes;
using BIMsmithMarket.Domain.Enums.RevitProcessing;

namespace BIMsmithMarket.Domain.Dto.RevitProcessing
{
    public class StartRevitProcessDto
    {
        [RequiredNotDefault]
        public RevitProcessType Type { get; set; }

        [RequiredNotDefault]
        public int ManufacturerId { get; set; }

        [RequiredNotDefault]
        public int[] ProductIds { get; set; }

        public RevitProcessRevitParameterMappingDto[] RevitParametersMappings { get; set; }

        public int[] ProjectDataTypeIds { get; set; }
    }
}