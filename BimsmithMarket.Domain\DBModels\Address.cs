﻿using System.Collections.Generic;

namespace BIMsmithMarket.Domain.DBModels
{
    public class Address : BaseEntity
    {
        public string Address1 { get; set; }

        public string Address2 { get; set; }

        public string Country { get; set; }

        public string State { get; set; }

        public string City { get; set; }

        public string Province { get; set; }

        public string Zip { get; set; }

        public int Status { get; set; }

        public virtual ICollection<Manufacturer> Manufacturers { get; set; }

        public Address()
        {
            Manufacturers = new List<Manufacturer>();
        }
    }
}