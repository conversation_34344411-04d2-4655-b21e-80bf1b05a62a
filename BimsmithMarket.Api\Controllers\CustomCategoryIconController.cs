﻿using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
#if !DEBUG
    [Authorize(Roles = DbConstants.AdminRole)]
#endif
    [Route("api/[controller]/[action]")]
    public class CustomCategoryIconController : BaseApiController
    {
        private readonly ICustomCategoryIconService _customCategoryIconService;

        public CustomCategoryIconController(ICustomCategoryIconService customCategoryIconService)
        {
            _customCategoryIconService = customCategoryIconService;
        }

        /// <summary>
        /// Adds custom category icon
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> Add(AddCustomCategoryIconDto model)
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _customCategoryIconService.AddAsync(model, userId, unitOfWork));
        }

        /// <summary>
        /// Deletes custom category icon
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPut]
        public async Task<IActionResult> Edit(EditCustomCategoryIconDto model)
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _customCategoryIconService.EditAsync(model, userId, unitOfWork));
        }

        /// <summary>
        /// Deletes custom category icon
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpDelete]
        public async Task<IActionResult> Delete(int id)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _customCategoryIconService.DeleteAsync(id, unitOfWork));
        }

        /// <summary>
        /// Lists custom category icons for specific category for admin panel
        /// </summary>
        /// <param name="categoryId">The category identifier</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> AdminList(int categoryId, int offset = 0, int count = 10)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _customCategoryIconService.AdminListAsync(categoryId, unitOfWork, offset, count));
        }

        /// <summary>
        /// Lists custom category icons for specific manufacturer for custom microsites
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        public async Task<IActionResult> PublicList(int manufacturerId)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _customCategoryIconService.PublicListAsync(manufacturerId, unitOfWork));
        }
    }
}