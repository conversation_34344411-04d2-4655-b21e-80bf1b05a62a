﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Core.Extentions;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Helpers;
using BIMsmithMarket.Services.Interfaces;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Services
{
    /// <summary>
    /// Service for categories operations
    /// </summary>
    public class CategoryService : ICategoryService
    {
        private IHttpClientFactory _httpClientFactory;

        public CategoryService(IHttpClientFactory httpClientFactory)
        {
            _httpClientFactory = httpClientFactory;
        }

        public async Task<dynamic> AddAsync(AddCategoryModel model, string webRootPath, string userId, IUnitOfWork unitOfWork)
        {
            //validate VanityUrl 
            if (!string.IsNullOrEmpty(model.VanityUrl))
            {
                if (await unitOfWork.CategoryRepository.GetAll().AnyAsync(a => a.VanityUrl.ToLower() == model.VanityUrl.ToLower()) ||
                    await unitOfWork.VanityHistoryRepository.GetAll().AnyAsync(a => a.EntityType == "category" && a.VanityUrl.ToLower() == model.VanityUrl.ToLower()))
                    throw new InvalidInputException("This Vanity Url occupied by another category, please enter another url");

                if (!Regex.IsMatch(model.VanityUrl, BIMsmithMarket.Domain.Constants.Constants.UrlVanityRegax))
                    throw new InvalidInputException("This Vanity Url contains not accessible characters. Available next characters '[A-Za-z]|[0-9]|_|-|()'");
            }

            string keywords = null;
            if (!string.IsNullOrWhiteSpace(model.Keywords))
                keywords = " " + model.Keywords.Trim(' ', ',').Replace(",", ", ").Replace("  ", " ") + ", ";

            Category category = new Category();
            category.Name = model.Name.Trim();
            category.ParentCategoryId = model.ParentCategoryId;
            category.IconUrl = model.IconUrl;
            category.IncludeRevitPlugin = model.IncludeRevitPlugin;
            category.RevitPluginPhotoId = model.RevitPluginPhotoId;
            Photo photo = new Photo();
            //frontend does not send this flag on category creation so this block will never be hit
            if (model.IncludeRevitPlugin && model.RevitPluginPhotoId == null)
            {
                photo.CreatedById = userId;
                photo.CreatedDate = DateTime.UtcNow;
                var iconUrl = model.IconUrl;
                if (string.IsNullOrWhiteSpace(iconUrl) || iconUrl == "./assets/img/lux.png")
                {
                    iconUrl = "/assets/img/categoryIcons/ico_cube_default.png";
                }
                if (iconUrl.StartsWith("."))
                {
                    iconUrl = iconUrl.Substring(1);
                }
                photo.Name = Path.GetFileName(iconUrl);
                photo.SyncStatusCode = (int)HttpStatusCode.OK;
                photo.UpdatesCount = 0;
                unitOfWork.PhotoRepository.Insert(photo);
                await unitOfWork.SaveAsync();

                MemoryStream originalImage = new MemoryStream();
                using (var fileStream = System.IO.File.OpenRead(Path.Combine(webRootPath, iconUrl)))
                {
                    fileStream.CopyTo(originalImage);
                }

                await PhotoProvider.CreateProductPhotosAsync(photo, originalImage, Path.GetExtension(iconUrl), null, false, true);

                unitOfWork.PhotoRepository.Edit(photo);
                unitOfWork.Save();
                category.RevitPluginPhotoId = photo.Id;
            }
            category.Keywords = keywords;
            category.CreatedById = userId;
            category.CreatedDate = DateTime.UtcNow;
            category.MetaDescription = model.MetaDescription;
            category.MetaKeywords = model.MetaKeywords;
            category.MetaTitle = model.MetaTitle;
            category.Description = model.Description;
            category.Synonyms = model.Synonyms;
            category.VanityUrl = model.VanityUrl;
            category.Weight = model.Weight;

            var metaData = MetaDataHelper.GetCategoryMetaData(category.Name);
            if (string.IsNullOrWhiteSpace(category.MetaTitle))
            {
                category.MetaTitle = metaData.Title;
            }
            if (string.IsNullOrWhiteSpace(category.MetaDescription))
            {
                category.MetaDescription = metaData.Description;
            }
            if (string.IsNullOrWhiteSpace(category.MetaKeywords))
            {
                category.MetaKeywords = metaData.Keywords;
            }
            var bimsmithVanityUrlAlreadyTaken = !string.IsNullOrWhiteSpace(model.BimsmithVanityUrl) && await unitOfWork.CategoryRepository.GetAll().AnyAsync(x => x.BimsmithVanityUrl.ToLower() == model.BimsmithVanityUrl.ToLower());
            if (bimsmithVanityUrlAlreadyTaken)
                throw new InvalidInputException("This bimsmith vanity url already taken");
            category.BimsmithVanityUrl = model.BimsmithVanityUrl;

            unitOfWork.CategoryRepository.Insert(category);

            await unitOfWork.SaveAsync();

            await AddOrUpdateKeyStatsAsync(category, model.KeyStatIds, userId, unitOfWork);

            await UpdateKeyStatsOrderAsync(category, unitOfWork);

            await unitOfWork.SaveAsync();

            return new
            {
                id = category.Id,
                name = category.Name,
                isRoot = category.ParentCategoryId == null,
                parentCategoryId = category.ParentCategoryId,
                revitIconUrl = photo.SmallImgUrl
            };
        }

        public async Task<OperationResultDto> EditAsync(EditCategoryModel model, string webRootPath, string userId, IUnitOfWork unitOfWork)
        {
            //validate VanityUrl 
            if (!string.IsNullOrEmpty(model.VanityUrl))
            {
                if (await unitOfWork.CategoryRepository.GetAll().AnyAsync(a => a.Id != model.Id && a.VanityUrl.ToLower() == model.VanityUrl.ToLower()) ||
                    await unitOfWork.VanityHistoryRepository.GetAll().AnyAsync(a => a.EntityType == "category" && a.VanityUrl.ToLower() == model.VanityUrl.ToLower() && a.EntityId != model.Id))
                    throw new InvalidInputException("This Vanity Url occupied by another category, please enter another url");

                if (!Regex.IsMatch(model.VanityUrl, BIMsmithMarket.Domain.Constants.Constants.UrlVanityRegax))
                    throw new InvalidInputException("This Vanity Url contains not accessible characters. Available next characters '[A-Za-z]|[0-9]|_|-|()'");
            }

            string keywords = null;
            if (!string.IsNullOrWhiteSpace(model.Keywords))
            {
                keywords = " " + model.Keywords.Trim(' ', ',').Replace(",", ", ").Replace("  ", " ") + ", ";
            }

            Category category = await unitOfWork.CategoryRepository.GetByIdAsync(model.Id);

            if (category == null)
                throw new InvalidInputException($"Category {model.Id} not found");

            category.Name = model.Name.Trim();
            category.ParentCategoryId = model.ParentCategoryId;
            category.IconUrl = model.IconUrl;
            category.IncludeRevitPlugin = model.IncludeRevitPlugin;
            category.RevitPluginPhotoId = model.RevitPluginPhotoId;
            Photo photo = new Photo();
            //this flag was added manually for Revit plugin needs
            if (model.IncludeRevitPlugin && model.RevitPluginPhotoId == null)
            {
                string iconUrl = model.IconUrl;
                MemoryStream originalImage = new MemoryStream();
                try
                {
                    HttpClient httpClient = _httpClientFactory.CreateClient();
                    using (var fileStream = await httpClient.GetStreamAsync(iconUrl))
                    {
                        fileStream.CopyTo(originalImage);
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex.Message, ex);
                    iconUrl = Path.Combine(webRootPath, "Img\\category_cube_ico.png");
                    using (var fileStream = System.IO.File.OpenRead(iconUrl))
                    {
                        fileStream.CopyTo(originalImage);
                    }
                }

                photo.CreatedById = userId;
                photo.CreatedDate = DateTime.UtcNow;

                photo.SyncStatusCode = (int)HttpStatusCode.OK;
                photo.UpdatesCount = 0;

                photo.Name = Path.GetFileName(iconUrl);
                unitOfWork.PhotoRepository.Insert(photo);
                await unitOfWork.SaveAsync();

                await PhotoProvider.CreateProductPhotosAsync(photo, originalImage, Path.GetExtension(iconUrl), null, false, true);
                unitOfWork.PhotoRepository.Edit(photo);
                await unitOfWork.SaveAsync();
                category.RevitPluginPhotoId = photo.Id;
            }
            category.Keywords = keywords;
            category.ModifiedById = userId;
            category.ModifiedDate = DateTime.UtcNow;
            category.MetaDescription = model.MetaDescription;
            category.MetaKeywords = model.MetaKeywords;
            category.MetaTitle = model.MetaTitle;
            category.Description = model.Description;
            category.Synonyms = model.Synonyms;

            if (category.VanityUrl != model.VanityUrl.AsVanityUrl())
            {
                var oldVanityHistory = await unitOfWork.VanityHistoryRepository.GetAll().Where(a => a.VanityUrl == category.VanityUrl).OrderByDescending(a => a.Id).FirstOrDefaultAsync();
                if (oldVanityHistory == null)
                {
                    oldVanityHistory = new VanityHistory
                    {
                        EntityId = category.Id,
                        EntityType = "category",
                        VanityUrl = category.VanityUrl,
                        CreatedById = userId,
                        CreatedDate = DateTime.UtcNow
                    };
                    unitOfWork.VanityHistoryRepository.Insert(oldVanityHistory);
                    await unitOfWork.SaveAsync();
                }

                var vanityHistory = new VanityHistory
                {
                    EntityId = category.Id,
                    ParentId = oldVanityHistory.Id,
                    EntityType = "category",
                    VanityUrl = model.VanityUrl.AsVanityUrl(),
                    CreatedById = userId,
                    CreatedDate = DateTime.UtcNow
                };
                unitOfWork.VanityHistoryRepository.Insert(vanityHistory);
                await unitOfWork.SaveAsync();
            }

            category.VanityUrl = model.VanityUrl.AsVanityUrl();

            var metaData = MetaDataHelper.GetCategoryMetaData(category.Name);
            if (string.IsNullOrWhiteSpace(category.MetaTitle))
            {
                category.MetaTitle = metaData.Title;
            }
            if (string.IsNullOrWhiteSpace(category.MetaDescription))
            {
                category.MetaDescription = metaData.Description;
            }
            if (string.IsNullOrWhiteSpace(category.MetaKeywords))
            {
                category.MetaKeywords = metaData.Keywords;
            }

            var bimsmithVanityUrlAlreadyTaken = !string.IsNullOrWhiteSpace(model.BimsmithVanityUrl) && await unitOfWork.CategoryRepository.GetAll().AnyAsync(x => x.BimsmithVanityUrl.ToLower() == model.BimsmithVanityUrl.ToLower() && x.Id != model.Id);
            if (bimsmithVanityUrlAlreadyTaken)
                throw new InvalidInputException("This bimsmith vanity url already taken");
            category.BimsmithVanityUrl = model.BimsmithVanityUrl;
            category.Weight = model.Weight;

            unitOfWork.CategoryRepository.Edit(category);

            await AddOrUpdateKeyStatsAsync(category, model.KeyStatIds, userId, unitOfWork);

            await UpdateKeyStatsOrderAsync(category, unitOfWork);

            await unitOfWork.SaveAsync();

            return new OperationResultDto
            {
                Status = OperationResultStatus.Succeed
            };
        }

        /// <summary>
        /// Returns mappings for market vanity url and bimsmith vanity url
        /// </summary>
        /// <returns></returns>
        public async Task<dynamic> GetBimsmithVanityUrlPairs()
        {
            using (var unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var categoriesWithBimsmithVanityUrl = await unitOfWork.CategoryRepository.GetAll()
                    .Where(x => !string.IsNullOrEmpty(x.VanityUrl)
                    && !string.IsNullOrEmpty(x.BimsmithVanityUrl))
                    .Select(x => new
                    {
                        marketVanityUrl = "category/" + x.VanityUrl,
                        bimsmithVanityUrl = x.BimsmithVanityUrl
                    })
                    .AsNoTracking()
                    .ToListAsync();

                return categoriesWithBimsmithVanityUrl;
            }
        }

        /// <summary>
        /// Returns market vanity url for category that is mapped to bimsmith vanity url from parameter
        /// </summary>
        /// <param name="bimsmithUrl"></param>
        /// <returns></returns>
        public async Task<CategorySEODto> GetVanityUrlByBimsmithUrl(string bimsmithUrl)
        {
            using (var unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var categoriesWithBimsmithVanityUrl = await unitOfWork.CategoryRepository.GetAll()
                    .Where(x => !string.IsNullOrEmpty(x.VanityUrl)
                             && x.BimsmithVanityUrl.ToLower() == bimsmithUrl.ToLower())
                    .Select(x => new CategorySEODto
                    {
                        MarketVanityUrl = "category/" + x.VanityUrl
                    })
                    .FirstOrDefaultAsync();

                if (categoriesWithBimsmithVanityUrl == null)
                    throw new DbItemNotFoundException("Category not found");

                return categoriesWithBimsmithVanityUrl;
            }
        }

        public async Task<BaseNameListDto[]> GetNamesListAsync(EntityIdsDto model, IUnitOfWork unitOfWork)
        {
            return await unitOfWork.CategoryRepository.GetAll()
                .Where(x => model.Ids.Distinct().Contains(x.Id))
                .ProjectToType<BaseNameListDto>()
                .ToArrayAsync();
        }

        public async Task<string> ExcelExportAsync(IUnitOfWork unitOfWork)
        {
            IEnumerable<CategoryExcelDto> categories = await GetCategoriesForExcelAsync(unitOfWork);
            string filePath = Path.Combine(Path.GetTempPath(), $"CategoriesTemplateExport_{Guid.NewGuid()}.xlsx");
            SpreadsheetDocument spreadsheetDocument;
            WorkbookPart workbookpart;
            WorksheetPart worksheetPart;
            CommonExcelProvider.CreateDocument(filePath, out spreadsheetDocument, out workbookpart, out worksheetPart);

            //columns
            Columns columns = new Columns();
            uint headerColumnIndex = 1;
            int columnsCount = CategoryExcelConstants.ColumnsCount;
            for (int i = 0; i < columnsCount; i++)
                columns.AppendChild(new Column { Min = headerColumnIndex, Max = headerColumnIndex++, Width = 25, CustomWidth = true });
            worksheetPart.Worksheet.AppendChild(columns);
            workbookpart.Workbook.Save();

            CommonExcelProvider.CreateSheet(spreadsheetDocument, workbookpart, worksheetPart, "Categories");

            // Get the sheetData cell table.
            SheetData sheetData = worksheetPart.Worksheet.AppendChild(new SheetData());
            int rowIndex = 1;
            Row rowHeader = new Row();
            rowHeader.Height = 90;
            rowHeader.CustomHeight = true;
            sheetData.AppendChild(rowHeader);

            int columnIndex = 1;

            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(CategoryExcelConstants.IdCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(CategoryExcelConstants.NameCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(CategoryExcelConstants.DescriptionCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(CategoryExcelConstants.KeywordsCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(CategoryExcelConstants.SynonymsCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(CategoryExcelConstants.MetaKeywordsCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(CategoryExcelConstants.MetaTitleCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(CategoryExcelConstants.MetaDescriptionCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(CategoryExcelConstants.VanityUrlCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(CategoryExcelConstants.BIMsmithVanityUrlCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(CategoryExcelConstants.IconUrlCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(CategoryExcelConstants.ParentCategoryIdCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(CategoryExcelConstants.KeyStatNamesCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(CategoryExcelConstants.WeightCaption, CellValues.String, columnIndex++, rowIndex, 2));

            rowIndex++;

            foreach (CategoryExcelDto category in categories)
            {
                Row row = new Row();
                sheetData.AppendChild(row);

                columnIndex = 1;

                row.AppendChild(CommonExcelProvider.ConstructCell(category.Id.ToString(), CellValues.Number, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(category.Name, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(category.Description, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(category.Keywords, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(category.Synonyms, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(category.MetaKeywords, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(category.MetaTitle, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(category.MetaDescription, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(category.VanityUrl, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(category.BimsmithVanityUrl, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(category.IconUrl, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(category.ParentCategoryId?.ToString(), CellValues.Number, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(CategoryExcelConstants.ItemsDelimeterString, category.KeyStatNames), CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(category.Weight.ToString(), CellValues.Number, columnIndex++, rowIndex));

                rowIndex++;
            }

            workbookpart.Workbook.Save();

            spreadsheetDocument.Dispose();

            return filePath;
        }

        public async Task<CategoryExcelImportResultDto> ImportExcelAsync(IFormFile formFile, string userId, IUnitOfWork unitOfWork)
        {
            List<CategoryExcelDto> categories = await ParseCategoriesAsync(formFile);
            List<string> errors = categories.SelectMany(x => x.Errors).ToList();
            List<CategoryExcelDto> parsedCategoriesWithoutErrors = categories.Where(x => !x.Errors.Any()).ToList();
            List<BaseNameListDto> allKeyStats = await unitOfWork.KeyStatRepository.GetAllAsNoTracking()
                .ProjectToType<BaseNameListDto>()
                .ToListAsync();

            foreach (CategoryExcelDto parsedCategory in parsedCategoriesWithoutErrors)
            {
                await ImportSingleCategoryAsync(parsedCategory, allKeyStats, errors, userId, unitOfWork);
            }

            return new()
            {
                Errors = errors
            };
        }

        public async Task<OperationResultDto> DeleteAsync(DeleteCategoryModel model, string userId, IProductService productService, IUnitOfWork unitOfWork)
        {
            CategoryTreeDto[] allCategories = await unitOfWork.CategoryRepository.GetAll()
                    .ProjectToType<CategoryTreeDto>()
                    .ToArrayAsync();

            if (!allCategories.Any(x => x.Id == model.Id))
                throw new InvalidInputException("Category not found");

            if (model.MoveToCategoryId != null && !allCategories.Any(x => x.Id == model.MoveToCategoryId))
                throw new InvalidInputException("Category not found");

            List<int> categoryIds = GetAllCategoryHierarchyIds(model.Id, allCategories);
            int[] productIds = await unitOfWork.ProductRepository.GetAll().Where(a => categoryIds.Contains(a.CategoryId)).Select(a => a.Id).ToArrayAsync();
            unitOfWork.BeginTransaction();

            //Delete category key stats
            CategoryKeyStat[] categoryKeyStats = await unitOfWork.CategoryKeyStatRepository.GetAll().Where(a => categoryIds.Contains(a.CategoryId)).ToArrayAsync();
            if (categoryKeyStats.Any())
            {
                unitOfWork.CategoryKeyStatRepository.Delete(categoryKeyStats);
            }

            if (model.MoveToCategoryId == null)
            {
                //Delete all products
                await productService.DeleteProductsByIds(unitOfWork, productIds);

                var productCategories = await unitOfWork.ProductCategoryRepository.GetAll().Where(a => categoryIds.Contains(a.CategoryId)).ToListAsync();
                unitOfWork.ProductCategoryRepository.Delete(productCategories);

                //Delete category and subcategories
                Category[] allCategoriesInTree = await unitOfWork.CategoryRepository.GetAll()
                    .Where(x => categoryIds.Contains(x.Id))
                    .ToArrayAsync();

                unitOfWork.CategoryRepository.Delete(allCategoriesInTree);
            }
            else
            {
                //Update products 
                Product[] products = await unitOfWork.ProductRepository.GetAll().Where(a => a.CategoryId == model.Id).ToArrayAsync();
                foreach (Product product in products)
                {
                    product.CategoryId = model.MoveToCategoryId.Value;
                    unitOfWork.ProductRepository.Edit(product);
                }

                //Update subcategories 
                Category[] subcategories = await unitOfWork.CategoryRepository.GetAll()
                   .Where(x => x.ParentCategoryId == model.Id)
                   .ToArrayAsync();

                foreach (Category subcategory in subcategories)
                {
                    subcategory.ParentCategoryId = model.MoveToCategoryId.Value;
                    unitOfWork.CategoryRepository.Edit(subcategory);
                }

                //Update product category

                ProductCategory[] existingProductCategories = await unitOfWork.ProductCategoryRepository.GetAll().Where(a => a.CategoryId == model.Id).ToArrayAsync();
                ProductCategory[] newProductCategories = existingProductCategories
                    .Select(x => new ProductCategory
                    {
                        CategoryId = model.MoveToCategoryId.Value,
                        MainCategory = x.MainCategory,
                        ProductId = x.ProductId,
                        Weight = x.Weight
                    })
                    .ToArray();

                unitOfWork.ProductCategoryRepository.Delete(existingProductCategories);
                unitOfWork.ProductCategoryRepository.InsertRange(newProductCategories);

                await unitOfWork.SaveAsync();

                //Delete category 
                Category category = await unitOfWork.CategoryRepository.GetByIdAsync(model.Id);
                unitOfWork.CategoryRepository.Delete(category);
            }

            await unitOfWork.SaveAsync();

            unitOfWork.CommitTransaction();

            foreach (int id in productIds)
                await productService.SaveProductToMongoAsync(id, true, unitOfWork);

            return new OperationResultDto
            {
                Status = Domain.Enums.OperationResultStatus.Succeed
            };
        }

        public async Task<List<dynamic>> TreeAsync(
            IUnitOfWork unitOfWork,
            int manufacturerId = -1,
            string manufacturerName = null,
            bool isStaging = false,
            string langCode = null)
        {
            var allCategories = await unitOfWork.CategoryRepository.GetAllAsNoTracking()
                    .Select(x => new
                    {
                        categoryId = x.Id,
                        categoryName = x.Name,
                        categoryIconUrl = x.IconUrl,
                        parentCategoryId = x.ParentCategoryId,
                        parentParentCategoryId = x.ParentCategory.ParentCategoryId,
                        vanityUrl = x.VanityUrl,
                        weight = x.Weight,
                        description = x.Description,
                        countOfProducts = x.ProductCategories.Select(c => c.ProductId).Concat(x.Products.Select(x => x.Id)).Distinct().Count()
                    })
                    .OrderBy(a => a.categoryName)
                    .ToListAsync();

            var productsQuery = unitOfWork.ProductRepository.GetAll();
            var additionalCategoriesQuery = unitOfWork.ProductCategoryRepository.GetAll();

            if (isStaging)
            {
                productsQuery = productsQuery.Where(x =>
                    (x.Published && x.Manufacturer.Published) ||
                    (x.Staging && x.Manufacturer.Staging));

                additionalCategoriesQuery = additionalCategoriesQuery.Where(a => (a.Product.Published && a.Product.Manufacturer.Published) || (a.Product.Staging && a.Product.Manufacturer.Staging));
            }
            else
            {
                productsQuery = productsQuery.Where(x => x.Published && x.Manufacturer.Published);
                additionalCategoriesQuery = additionalCategoriesQuery.Where(a => a.Product.Published && a.Product.Manufacturer.Published);
            }

            if (manufacturerId != -1)
            {
                productsQuery = productsQuery.Where(x => x.ManufacturerId == manufacturerId);
                additionalCategoriesQuery = additionalCategoriesQuery.Where(a => a.Product.ManufacturerId == manufacturerId);
            }
            else if (manufacturerName != null)
            {
                productsQuery = productsQuery.Where(x => x.Manufacturer.Name == manufacturerName);
                additionalCategoriesQuery = additionalCategoriesQuery.Where(a => a.Product.Manufacturer.Name == manufacturerName);
            }

            var categoryIds = await productsQuery.Select(x => x.CategoryId)
                .Concat(additionalCategoriesQuery.Select(x => x.CategoryId))
                .Distinct()
                .ToListAsync();

            categoryIds.AddRange(
                allCategories.Where(x => categoryIds.Contains(x.categoryId) && x.parentCategoryId != null).Select(x => x.parentCategoryId.Value).ToList());

            categoryIds.AddRange(
                allCategories.Where(x => categoryIds.Contains(x.categoryId) && x.parentParentCategoryId != null).Select(x => x.parentParentCategoryId.Value).ToList());

            var treeCategories = allCategories.Where(x => categoryIds.Contains(x.categoryId)).ToList();
            var parentCategories = treeCategories.Where(x => categoryIds.Contains(x.categoryId) && x.parentCategoryId == null).ToList();

            var grouppedParentCategories = parentCategories.GroupBy(a => new { a.categoryId, a.categoryName, a.categoryIconUrl, a.vanityUrl, a.description }).ToList();

            List<dynamic> categoriesJson = new List<dynamic>();

            foreach (var parentCategoryGroup in grouppedParentCategories)
            {
                var subCategories = treeCategories.Where(a => a.parentCategoryId == parentCategoryGroup.Key.categoryId).OrderByDescending(a => a.weight).ThenBy(a => a.categoryName).ToList();

                int totalCountOfProducts = subCategories.Sum(a => a.countOfProducts) + parentCategoryGroup.Sum(a => a.countOfProducts);

                List<dynamic> subcategoriesJson = new List<dynamic>();
                foreach (var subCategory in subCategories)
                {
                    var subSubCategories = treeCategories.Where(a => a.parentCategoryId == subCategory.categoryId).OrderByDescending(a => a.weight).ThenBy(a => a.categoryName).ToList();

                    totalCountOfProducts = subSubCategories.Sum(a => a.countOfProducts) + totalCountOfProducts;

                    List<dynamic> subsubcategoriesJson = new List<dynamic>();
                    foreach (var subSubCategory in subSubCategories)
                    {
                        var s = new
                        {
                            id = subSubCategory.categoryId,
                            name = subSubCategory.categoryName,
                            iconUrl = subSubCategory.categoryIconUrl,
                            vanityUrl = subSubCategory.vanityUrl,
                            weight = subSubCategory.weight,
                            description = subSubCategory.description,
                            count = subSubCategory.countOfProducts
                        };
                        subsubcategoriesJson.Add(s);
                    }

                    var subCat = await unitOfWork.CategoryRepository.GetByIdAsync(parentCategoryGroup.Key.categoryId);
                    var subMetaKeywords = subCat.MetaKeywords;
                    var subMetaTitle = subCat.MetaTitle;
                    var subMetaDescription = subCat.MetaDescription;

                    var r = new
                    {
                        id = subCategory.categoryId,
                        name = subCategory.categoryName,
                        iconUrl = subCategory.categoryIconUrl,
                        vanityUrl = subCategory.vanityUrl,
                        count = subCategory.countOfProducts + subSubCategories.Sum(a => a.countOfProducts),
                        weight = subCategory.weight,
                        description = subCategory.description,
                        subsubcategories = subsubcategoriesJson,
                        metaKeywords = subMetaKeywords,
                        metaTitle = subMetaTitle,
                        metaDescription = subMetaDescription
                    };
                    subcategoriesJson.Add(r);
                }

                var category = await unitOfWork.CategoryRepository.GetByIdAsync(parentCategoryGroup.Key.categoryId);
                var metaKeywords = category.MetaKeywords;
                var metaTitle = category.MetaTitle;
                var metaDescription = category.MetaDescription;

                var carR = new
                {
                    id = parentCategoryGroup.Key.categoryId,
                    name = parentCategoryGroup.Key.categoryName,
                    iconUrl = parentCategoryGroup.Key.categoryIconUrl,
                    vanityUrl = parentCategoryGroup.Key.vanityUrl,
                    description = parentCategoryGroup.Key.description,
                    count = totalCountOfProducts,
                    subcategories = subcategoriesJson,
                    metaKeywords = metaKeywords,
                    metaTitle = metaTitle,
                    metaDescription = metaDescription
                };
                categoriesJson.Add(carR);
            }

            var result = categoriesJson.OrderBy(a => a.name).ToList();
            return result;
        }

        public List<int> GetAllCategoryHierarchyIds(int categoryId, CategoryTreeDto[] allCategories)
        {
            List<int> result = new List<int>();
            result.Add(categoryId);
            List<CategoryTreeDto> subCategories = allCategories.Where(x => x.ParentCategoryId == categoryId).ToList();
            foreach (CategoryTreeDto child in subCategories)
            {
                result.AddRange(GetAllCategoryHierarchyIds(child.Id, allCategories));
            }
            return result;
        }

        #region private methods
        private async Task<IEnumerable<CategoryExcelDto>> GetCategoriesForExcelAsync(IUnitOfWork unitOfWork)
        {
            return await unitOfWork.CategoryRepository.GetAllAsNoTracking()
                .ProjectToType<CategoryExcelDto>()
                .ToListAsync();
        }

        private async Task<List<CategoryExcelDto>> ParseCategoriesAsync(IFormFile formFile)
        {
            using (Stream stream = formFile.OpenReadStream())
            {
                SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Open(stream, false);
                WorkbookPart workbookpart = spreadsheetDocument.WorkbookPart;
                Workbook workbook = workbookpart.Workbook;
                IEnumerable<Sheet> sheets = workbook.Descendants<Sheet>();
                WorksheetPart worksheetPart = (WorksheetPart)workbookpart.GetPartById(sheets.First().Id);
                SharedStringTablePart sharedStringPart = workbookpart.SharedStringTablePart;

                List<Row> rows = worksheetPart.Worksheet.Descendants<Row>().ToList();
                Row headerRow = rows.FirstOrDefault();
                if (headerRow == null)
                    throw new InvalidInputException("Can't to found Header row");

                List<string> errors = new();
                List<Cell> headerCells = headerRow.Elements<Cell>().ToList();
                int cellIndex = 0;
                CommonExcelProvider.ValidCellValue(headerCells[cellIndex++], CategoryExcelConstants.IdCaption, sharedStringPart, errors, $"Wrong position of column with name '{CategoryExcelConstants.IdCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells[cellIndex++], CategoryExcelConstants.NameCaption, sharedStringPart, errors, $"Wrong position of column with name '{CategoryExcelConstants.NameCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells[cellIndex++], CategoryExcelConstants.DescriptionCaption, sharedStringPart, errors, $"{CategoryExcelConstants.DescriptionCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells[cellIndex++], CategoryExcelConstants.KeywordsCaption, sharedStringPart, errors, $"Wrong position of column with name '{CategoryExcelConstants.KeywordsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells[cellIndex++], CategoryExcelConstants.SynonymsCaption, sharedStringPart, errors, $"Wrong position of column with name '{CategoryExcelConstants.SynonymsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells[cellIndex++], CategoryExcelConstants.MetaKeywordsCaption, sharedStringPart, errors, $"Wrong position of column with name '{CategoryExcelConstants.MetaKeywordsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells[cellIndex++], CategoryExcelConstants.MetaTitleCaption, sharedStringPart, errors, $"Wrong position of column with name '{CategoryExcelConstants.MetaTitleCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells[cellIndex++], CategoryExcelConstants.MetaDescriptionCaption, sharedStringPart, errors, $"Wrong position of column with name '{CategoryExcelConstants.MetaDescriptionCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells[cellIndex++], CategoryExcelConstants.VanityUrlCaption, sharedStringPart, errors, $"Wrong position of column with name '{CategoryExcelConstants.VanityUrlCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells[cellIndex++], CategoryExcelConstants.BIMsmithVanityUrlCaption, sharedStringPart, errors, $"{CategoryExcelConstants.BIMsmithVanityUrlCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells[cellIndex++], CategoryExcelConstants.IconUrlCaption, sharedStringPart, errors, $"Wrong position of column with name '{CategoryExcelConstants.IconUrlCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells[cellIndex++], CategoryExcelConstants.ParentCategoryIdCaption, sharedStringPart, errors, $"Wrong position of column with name '{CategoryExcelConstants.ParentCategoryIdCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells[cellIndex++], CategoryExcelConstants.KeyStatNamesCaption, sharedStringPart, errors, $"Wrong position of column with name '{CategoryExcelConstants.KeyStatNamesCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells[cellIndex++], CategoryExcelConstants.WeightCaption, sharedStringPart, errors, $"Wrong position of column with name '{CategoryExcelConstants.WeightCaption}'");

                if (errors.Any())
                    throw new InvalidInputException(string.Join(Environment.NewLine, errors));

                List<CategoryExcelDto> categories = new();
                int rowIndex = 2;
                foreach (Row row in rows.Skip(1)) // skip header row
                {
                    cellIndex = 1;
                    List<Cell> cells = row.Elements<Cell>().ToList();

                    if (!cells.Any() || cells.All(x => !CommonExcelProvider.HasCellValue(x, sharedStringPart)))
                    {
                        rowIndex++;
                        continue;
                    }

                    CategoryExcelDto category = new();
                    category.RowNumber = rowIndex;
                    category.Id = CommonExcelProvider.GetCellNullIntValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart, errors);
                    cellIndex++;
                    category.Name = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                    cellIndex++;

                    if (string.IsNullOrWhiteSpace(category.Name))
                        category.Errors.Add($"Category Name must have value Row Index {category.RowNumber}");

                    category.Description = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                    cellIndex++;
                    category.Keywords = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                    cellIndex++;
                    category.Synonyms = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                    cellIndex++;
                    category.MetaKeywords = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                    cellIndex++;
                    category.MetaTitle = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                    cellIndex++;
                    category.MetaDescription = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                    cellIndex++;
                    category.VanityUrl = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                    cellIndex++;
                    category.BimsmithVanityUrl = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                    cellIndex++;
                    category.IconUrl = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                    cellIndex++;
                    category.ParentCategoryId = CommonExcelProvider.GetCellNullIntValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart, errors);
                    cellIndex++;
                    string keyStatNamesString = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);

                    if (!string.IsNullOrWhiteSpace(keyStatNamesString))
                        category.KeyStatNames = keyStatNamesString.Split(CategoryExcelConstants.ItemsDelimeterString).ToList();

                    cellIndex++;
                    category.Weight = CommonExcelProvider.GetCellIntValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart, errors);
                    cellIndex++;

                    rowIndex++;
                    categories.Add(category);
                }

                return categories;
            }
        }

        private async Task ImportSingleCategoryAsync(CategoryExcelDto parsedCategory, IEnumerable<BaseNameListDto> allKeyStats, List<string> errors, string userId, IUnitOfWork unitOfWork)
        {
            try
            {
#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Started processing for category with row index {parsedCategory.RowNumber}");
#endif
                unitOfWork.BeginTransaction();
                bool isNewCategory = !parsedCategory.Id.HasValue;
                Category category = new();
                if (isNewCategory)
                {
                    AddCategoryModel addCategoryModel = parsedCategory.Adapt<AddCategoryModel>();
                    addCategoryModel.KeyStatIds = ConvertKeyStatNameToIds(parsedCategory.KeyStatNames, allKeyStats);
                    await AddAsync(addCategoryModel, ConfigurationHelper.GetValue("MarketApiBaseUrl"), userId, unitOfWork);
                }
                else
                {
                    EditCategoryModel editCategoryModel = parsedCategory.Adapt<EditCategoryModel>();
                    editCategoryModel.KeyStatIds = ConvertKeyStatNameToIds(parsedCategory.KeyStatNames, allKeyStats);
                    await EditAsync(editCategoryModel, ConfigurationHelper.GetValue("MarketApiBaseUrl"), userId, unitOfWork);
                }
                unitOfWork.CommitTransaction();
#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Passed insert or update for category with row index {parsedCategory.RowNumber}");
#endif
            }
            catch (Exception ex)
            {
#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Failed insert or update for category with row index {parsedCategory.RowNumber}");
#endif
                unitOfWork.RollbackTransaction();
                errors.Add($"Error to create or update category with Row Index {parsedCategory.RowNumber}: {ex.GetAllMessages()}");
            }
        }

        private List<int> ConvertKeyStatNameToIds(IEnumerable<string> keyStatNames, IEnumerable<BaseNameListDto> allKeyStats)
        {
            List<int> keyStatIds = new List<int>();
            foreach (string keyStatName in keyStatNames)
            {
                BaseNameListDto keyStat = allKeyStats.FirstOrDefault(x => x.Name.ToLower() == keyStatName.ToLower());

                if (keyStat == null)
                    continue;

                keyStatIds.Add(keyStat.Id);
            }

            return keyStatIds;
        }

        private async Task AddOrUpdateKeyStatsAsync(Category category, IEnumerable<int> addedKeyStatIds, string userId, IUnitOfWork unitOfWork)
        {
            if (addedKeyStatIds == null) addedKeyStatIds = new List<int>();
            if (category.CategoryKeyStats == null) category.CategoryKeyStats = new List<CategoryKeyStat>();
            var existingItemIds = category.CategoryKeyStats.Select(x => x.KeyStatId).ToList();
            var itemToAddIds = addedKeyStatIds.Except(existingItemIds).ToList();
            var itemToDeleteIds = existingItemIds.Except(addedKeyStatIds).ToList();
            foreach (int id in itemToAddIds)
            {
                CategoryKeyStat categoryKeyStat = new();
                categoryKeyStat.CategoryId = category.Id;
                categoryKeyStat.KeyStatId = id;
                categoryKeyStat.CreatedById = userId;
                categoryKeyStat.CreatedDate = DateTime.UtcNow;
                unitOfWork.CategoryKeyStatRepository.Insert(categoryKeyStat);
            }
            List<CategoryKeyStat> itemsToDelete = await unitOfWork.CategoryKeyStatRepository.GetAll()
                .Where(x => x.CategoryId == category.Id && itemToDeleteIds.Contains(x.KeyStatId))
                .ToListAsync();
            itemsToDelete.ForEach(x => unitOfWork.CategoryKeyStatRepository.Delete(x));
            await unitOfWork.SaveAsync();
        }

        private async Task UpdateKeyStatsOrderAsync(Category category, IUnitOfWork unitOfWork)
        {
            int order = 1;
            foreach (CategoryKeyStat categoryKeyStat in category.CategoryKeyStats)
            {
                categoryKeyStat.Order = order++;
                unitOfWork.CategoryKeyStatRepository.Edit(categoryKeyStat);
            }
            await unitOfWork.SaveAsync();
        }
        #endregion
    }
}