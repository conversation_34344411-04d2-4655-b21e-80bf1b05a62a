﻿using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services;
using BIMsmithMarket.Services.Interfaces;
using BIMsmithMarket.Services.Search;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Providers
{
    public static class ExcelSearchHistoryProvider
    {
        private static readonly IMasterformatService _masterformatService = new MasterformatService(new CacheService());

        public static async Task<byte[]> GetExcelAsync(IEnumerable<SearchHistory> histories, IUnitOfWork unitOfWork)
        {
            var allManufacturers = unitOfWork.ManufacturerRepository.GetAll().ToDictionary(m => m.Id, m => m.Name);
            var allCategories = unitOfWork.CategoryRepository.GetAll().ToDictionary(c => c.Id, c => c.Name);
            var allCertificates = unitOfWork.ProductCertificateRepository.GetAll().Select(c => c.ExternalCertificateId).ToList();
            var allCategoryVanityUrls = unitOfWork.CategoryRepository.GetAll().Where(c => c.VanityUrl != null).Select(x => new { x.Name, x.VanityUrl }).AsEnumerable().GroupBy(c => c.VanityUrl).ToDictionary(c => c.Key, c => c.First().Name);
            var allCisfbs = unitOfWork.CisfbRepository.GetAll().ToDictionary(c => c.Id, c => c.Title);
            var allExternalMasterformats = await _masterformatService.GetBackofficeMasterformatsAsync();
            var allOmniclasses = unitOfWork.OmniclassRepository.GetAll().ToDictionary(c => c.Id, c => c.Title);
            var allUniclasses = unitOfWork.UniclassRepository.GetAll().ToDictionary(c => c.Id, c => c.Title);
            var allUniformats = unitOfWork.UniformatRepository.GetAll().ToDictionary(c => c.Id, c => c.Title);
            var allProductLines = unitOfWork.ProductLineRepository.GetAll().ToDictionary(c => c.Id, c => c.Name);
            var allQualityItems = unitOfWork.QualityItemRepository.GetAll().ToDictionary(c => c.Id, c => c.Name);

            using (var ms = new MemoryStream())
            using (var spreadsheetDocument = SpreadsheetDocument.Create(ms, SpreadsheetDocumentType.Workbook))
            {
                // Add a WorkbookPart to the document.
                WorkbookPart workbookpart = spreadsheetDocument.AddWorkbookPart();
                workbookpart.Workbook = new Workbook();

                // Add a WorksheetPart to the WorkbookPart.
                WorksheetPart worksheetPart = workbookpart.AddNewPart<WorksheetPart>();
                worksheetPart.Worksheet = new Worksheet();

                Fonts fonts = new Fonts(
                    new Font( // Index 0 - default
                        new FontSize() { Val = 10 }
                    ));

                Fills fills = new Fills(
                        new Fill(new PatternFill() { PatternType = PatternValues.None }), // Index 0 - default
                        new Fill(new PatternFill(new ForegroundColor { Rgb = new HexBinaryValue() { Value = "FFE7E6E6" } }) { PatternType = PatternValues.Solid }), // Index 1 - default
                        new Fill(new PatternFill(new ForegroundColor { Rgb = new HexBinaryValue() { Value = "FFBDD7EE" } }) { PatternType = PatternValues.Solid }) // Index 2 - header
                    );

                Borders borders = new Borders(
                        new Border() // index 0 default
                    );

                CellFormats cellFormats = new CellFormats(
                        new CellFormat(), // default
                        new CellFormat { FontId = 0, FillId = 1, BorderId = 0, ApplyFill = true }, // body
                        new CellFormat { FontId = 0, FillId = 2, BorderId = 0, ApplyFill = true }, // header
                        new CellFormat { ApplyNumberFormat = true, NumberFormatId = 22 }
                    );

                Stylesheet styleSheet = new Stylesheet(fonts, fills, borders, cellFormats);

                // Adding style
                WorkbookStylesPart stylePart = workbookpart.AddNewPart<WorkbookStylesPart>();
                stylePart.Stylesheet = styleSheet;
                stylePart.Stylesheet.Save();

                //columns
                Columns columns = new Columns();
                columns.AppendChild(new Column { Min = 1, Max = 1, Width = 8, CustomWidth = true });
                columns.AppendChild(new Column { Min = 2, Max = 2, Width = 17, CustomWidth = true });
                columns.AppendChild(new Column { Min = 3, Max = 3, Width = 25, CustomWidth = true });
                columns.AppendChild(new Column { Min = 4, Max = 4, Width = 15, CustomWidth = true });
                columns.AppendChild(new Column { Min = 5, Max = 5, Width = 12, CustomWidth = true });
                columns.AppendChild(new Column { Min = 6, Max = 6, Width = 12, CustomWidth = true });
                columns.AppendChild(new Column { Min = 7, Max = 7, Width = 12, CustomWidth = true });
                columns.AppendChild(new Column { Min = 8, Max = 8, Width = 12, CustomWidth = true });
                columns.AppendChild(new Column { Min = 9, Max = 9, Width = 20, CustomWidth = true });
                columns.AppendChild(new Column { Min = 10, Max = 10, Width = 20, CustomWidth = true });
                columns.AppendChild(new Column { Min = 11, Max = 11, Width = 20, CustomWidth = true });
                columns.AppendChild(new Column { Min = 12, Max = 12, Width = 20, CustomWidth = true });
                columns.AppendChild(new Column { Min = 13, Max = 13, Width = 20, CustomWidth = true });
                columns.AppendChild(new Column { Min = 14, Max = 14, Width = 20, CustomWidth = true });
                columns.AppendChild(new Column { Min = 15, Max = 15, Width = 20, CustomWidth = true });
                columns.AppendChild(new Column { Min = 16, Max = 16, Width = 20, CustomWidth = true });
                columns.AppendChild(new Column { Min = 17, Max = 17, Width = 20, CustomWidth = true });
                columns.AppendChild(new Column { Min = 18, Max = 18, Width = 20, CustomWidth = true });
                columns.AppendChild(new Column { Min = 19, Max = 19, Width = 20, CustomWidth = true });
                columns.AppendChild(new Column { Min = 20, Max = 20, Width = 20, CustomWidth = true });
                worksheetPart.Worksheet.AppendChild(columns);
                workbookpart.Workbook.Save();

                //Sheets
                Sheets sheets = workbookpart.Workbook.AppendChild(new Sheets());

                //Append a new worksheet and associate it with the workbook.
                Sheet sheet = new Sheet()
                {
                    Id = spreadsheetDocument.WorkbookPart.
                    GetIdOfPart(worksheetPart),
                    SheetId = 1,
                    Name = "Search History"
                };
                sheets.Append(sheet);
                workbookpart.Workbook.Save();

                // Get the sheetData cell table.
                SheetData sheetData = worksheetPart.Worksheet.AppendChild(new SheetData());

                int rowIndex = 1;
                int columnIndex = 1;
                Row rowHeader = new Row();
                sheetData.AppendChild(rowHeader);

                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Id", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Date/Time", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Email", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("IP Address", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("# Results", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("# Secondary", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Search Time", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Total Time", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Query", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Category", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Manufacturer", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("ProductLine", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Cisfb", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Masterformat", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Omniclass", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Uniclass", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Uniformat", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Certificates", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("FileTypes", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("QualityItems", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Published", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Featured", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("SortType", CellValues.String, columnIndex++, rowIndex, 2));
                rowIndex++;

                foreach (var history in histories)
                {
                    try
                    {
                        columnIndex = 1;

                        Row row = new Row();
                        row.AppendChild(CommonExcelProvider.ConstructCell(history.Id.ToString(), CellValues.Number, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(history.QueryDate.ToOADate().ToString(), CellValues.Number, columnIndex++, rowIndex, 3));
                        row.AppendChild(CommonExcelProvider.ConstructCell(history.Email, CellValues.String, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(history.IPAddress, CellValues.String, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(history.NumResults.ToString(), CellValues.Number, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(history.NumSecondary.ToString(), CellValues.Number, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(history.SearchTime != 0 ? history.SearchTime.ToString() : string.Empty, CellValues.Number, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(history.TotalTime != 0 ? history.TotalTime.ToString() : string.Empty, CellValues.Number, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(history.Query, CellValues.String, columnIndex++, rowIndex));

                        var options = JsonConvert.DeserializeObject<ProductSearchOptions>(history.Options);
                        if (options != null)
                        {
                            var category = options.CategoryId > 0 && allCategories.ContainsKey(options.CategoryId) ? allCategories[options.CategoryId] : string.Empty;
                            if (string.IsNullOrWhiteSpace(category) && !string.IsNullOrWhiteSpace(options.CategoryVanityUrl) && allCategoryVanityUrls.ContainsKey(options.CategoryVanityUrl))
                            {
                                category = allCategoryVanityUrls[options.CategoryVanityUrl];
                            }
                            string manufacturers = string.Empty;
                            if (options.ManufacturerIds != null && options.ManufacturerIds.Any())
                            {
                                manufacturers = string.Join(",", allManufacturers.Where(x => options.ManufacturerIds.Contains(x.Key)).Select(x => x.Value));
                            }
                            var productLine = options.ProductLineId > 0 && allProductLines.ContainsKey(options.ProductLineId) ? allProductLines[options.ProductLineId] : string.Empty;
                            var cisfbs = options.CisfbId > 0 && allCisfbs.ContainsKey(options.CisfbId) ? allCisfbs[options.CisfbId] : string.Empty;
                            var masterformat = options.ExternalMasterformatId > 0 && allExternalMasterformats.Select(x => x.Id).Contains(options.ExternalMasterformatId) ? allExternalMasterformats.FirstOrDefault(x => x.Id == options.ExternalMasterformatId)?.Code : string.Empty;
                            var omniclass = options.OmniclassId > 0 && allOmniclasses.ContainsKey(options.OmniclassId) ? allOmniclasses[options.OmniclassId] : string.Empty;
                            var uniclass = options.UniclassId > 0 && allUniclasses.ContainsKey(options.UniclassId) ? allUniclasses[options.UniclassId] : string.Empty;
                            var uniformats = options.UniformatId > 0 && allUniformats.ContainsKey(options.UniformatId) ? allUniformats[options.UniformatId] : string.Empty;
                            var certificates = options.ExternalCertificateIds != null ? string.Join(", ", options.ExternalCertificateIds.Select(c => allCertificates.Contains(c) ? c.ToString() : "?")) : string.Empty;
                            var fileTypes = options.ProductFileTypeIds != null ? string.Join(", ", options.ProductFileTypeIds.Select(f => f.ToString())) : string.Empty;
                            var qualityItems = options.QualityItemIds != null ? string.Join(", ", options.QualityItemIds.Select(c => allQualityItems.ContainsKey(c) ? allQualityItems[c] : "?")) : string.Empty;

                            row.AppendChild(CommonExcelProvider.ConstructCell(category, CellValues.String, columnIndex++, rowIndex));
                            row.AppendChild(CommonExcelProvider.ConstructCell(manufacturers, CellValues.String, columnIndex++, rowIndex));
                            row.AppendChild(CommonExcelProvider.ConstructCell(productLine, CellValues.String, columnIndex++, rowIndex));
                            row.AppendChild(CommonExcelProvider.ConstructCell(cisfbs, CellValues.String, columnIndex++, rowIndex));
                            row.AppendChild(CommonExcelProvider.ConstructCell(masterformat, CellValues.String, columnIndex++, rowIndex));
                            row.AppendChild(CommonExcelProvider.ConstructCell(omniclass, CellValues.String, columnIndex++, rowIndex));
                            row.AppendChild(CommonExcelProvider.ConstructCell(uniclass, CellValues.String, columnIndex++, rowIndex));
                            row.AppendChild(CommonExcelProvider.ConstructCell(uniformats, CellValues.String, columnIndex++, rowIndex));
                            row.AppendChild(CommonExcelProvider.ConstructCell(certificates, CellValues.String, columnIndex++, rowIndex));
                            row.AppendChild(CommonExcelProvider.ConstructCell(fileTypes, CellValues.String, columnIndex++, rowIndex));
                            row.AppendChild(CommonExcelProvider.ConstructCell(qualityItems, CellValues.String, columnIndex++, rowIndex));
                            row.AppendChild(CommonExcelProvider.ConstructCell(options.Published.ToString().ToUpper(), CellValues.String, columnIndex++, rowIndex));
                            row.AppendChild(CommonExcelProvider.ConstructCell(options.Featured.ToString().ToUpper(), CellValues.String, columnIndex++, rowIndex));
                            row.AppendChild(CommonExcelProvider.ConstructCell(options.SortType.ToString(), CellValues.String, columnIndex++, rowIndex));
                        }

                        sheetData.AppendChild(row);
                        rowIndex++;
                    }
                    catch (Exception ex)
                    {
                        Log.Warning("Error exporting search history {0}: {1}", history.Id, ex.Message);
                    }
                }

                workbookpart.Workbook.Save();

                spreadsheetDocument.Dispose();

                return ms.ToArray();
            }
        }

        public static async Task<byte[]> GetCombinedExcelAsync(IEnumerable<IGrouping<string, SearchHistory>> historyGroups, IUnitOfWork unitOfWork)
        {
            var allManufacturers = unitOfWork.ManufacturerRepository.GetAll().ToDictionary(m => m.Id, m => m.Name);
            var allCategories = unitOfWork.CategoryRepository.GetAll().ToDictionary(c => c.Id, c => c.Name);
            var allCategoryVanityUrls = unitOfWork.CategoryRepository.GetAll().Where(c => c.VanityUrl != null).Select(x => new { x.Name, x.VanityUrl }).AsEnumerable().GroupBy(c => c.VanityUrl).ToDictionary(c => c.Key, c => c.First().Name);
            var allCertificates = unitOfWork.ProductCertificateRepository.GetAll().Select(c => c.ExternalCertificateId).ToList();
            var allCisfbs = unitOfWork.CisfbRepository.GetAll().ToDictionary(c => c.Id, c => c.Title);
            var allMasterformats = await _masterformatService.GetBackofficeMasterformatsAsync();
            var allOmniclasses = unitOfWork.OmniclassRepository.GetAll().ToDictionary(c => c.Id, c => c.Title);
            var allUniclasses = unitOfWork.UniclassRepository.GetAll().ToDictionary(c => c.Id, c => c.Title);
            var allUniformats = unitOfWork.UniformatRepository.GetAll().ToDictionary(c => c.Id, c => c.Title);
            var allProductLines = unitOfWork.ProductLineRepository.GetAll().ToDictionary(c => c.Id, c => c.Name);
            var allQualityItems = unitOfWork.QualityItemRepository.GetAll().ToDictionary(c => c.Id, c => c.Name);

            using (var ms = new MemoryStream())
            using (var spreadsheetDocument = SpreadsheetDocument.Create(ms, SpreadsheetDocumentType.Workbook))
            {
                // Add a WorkbookPart to the document.
                WorkbookPart workbookpart = spreadsheetDocument.AddWorkbookPart();
                workbookpart.Workbook = new Workbook();

                // Add a WorksheetPart to the WorkbookPart.
                WorksheetPart worksheetPart = workbookpart.AddNewPart<WorksheetPart>();
                worksheetPart.Worksheet = new Worksheet();

                Fonts fonts = new Fonts(
                    new Font( // Index 0 - default
                        new FontSize() { Val = 10 }
                    ));

                Fills fills = new Fills(
                        new Fill(new PatternFill() { PatternType = PatternValues.None }), // Index 0 - default
                        new Fill(new PatternFill(new ForegroundColor { Rgb = new HexBinaryValue() { Value = "FFE7E6E6" } }) { PatternType = PatternValues.Solid }), // Index 1 - default
                        new Fill(new PatternFill(new ForegroundColor { Rgb = new HexBinaryValue() { Value = "FFBDD7EE" } }) { PatternType = PatternValues.Solid }) // Index 2 - header
                    );

                Borders borders = new Borders(
                        new Border() // index 0 default
                    );

                CellFormats cellFormats = new CellFormats(
                        new CellFormat(), // default
                        new CellFormat { FontId = 0, FillId = 1, BorderId = 0, ApplyFill = true }, // body
                        new CellFormat { FontId = 0, FillId = 2, BorderId = 0, ApplyFill = true }, // header
                        new CellFormat { ApplyNumberFormat = true, NumberFormatId = 22 }
                    );

                Stylesheet styleSheet = new Stylesheet(fonts, fills, borders, cellFormats);

                // Adding style
                WorkbookStylesPart stylePart = workbookpart.AddNewPart<WorkbookStylesPart>();
                stylePart.Stylesheet = styleSheet;
                stylePart.Stylesheet.Save();

                var columnCount = 21;
                var width = 20;
                //columns
                Columns columns = new Columns();
                for (uint i = 1; i < columnCount + 1; i++)
                {
                    columns.AppendChild(new Column { Min = i, Max = i, Width = width, CustomWidth = true });
                }
                worksheetPart.Worksheet.AppendChild(columns);
                workbookpart.Workbook.Save();

                //Sheets
                Sheets sheets = workbookpart.Workbook.AppendChild(new Sheets());

                //Append a new worksheet and associate it with the workbook.
                Sheet sheet = new Sheet()
                {
                    Id = spreadsheetDocument.WorkbookPart.
                    GetIdOfPart(worksheetPart),
                    SheetId = 1,
                    Name = "Search History"
                };
                sheets.Append(sheet);
                workbookpart.Workbook.Save();

                // Get the sheetData cell table.
                SheetData sheetData = worksheetPart.Worksheet.AppendChild(new SheetData());

                int rowIndex = 1;
                int columnIndex = 1;
                Row rowHeader = new Row();
                sheetData.AppendChild(rowHeader);

                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Id", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Date/Time", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Email", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("IP Address", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("# Results", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("# Secondary", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Search Time", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Total Time", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Query", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Category", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Manufacturer", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("ProductLine", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Cisfb", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Masterformat", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Omniclass", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Uniclass", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Uniformat", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Certificates", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("FileTypes", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("QualityItems", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Published", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Featured", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("SortType", CellValues.String, columnIndex++, rowIndex, 2));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Count", CellValues.String, columnIndex++, rowIndex, 2));
                rowIndex++;

                foreach (var historyGroup in historyGroups)
                {
                    try
                    {
                        columnIndex = 1;

                        var ids = string.Join(";", historyGroup.Select(x => x.Id.ToString()).Distinct());
                        var dates = string.Join(";", historyGroup.Select(x => x.QueryDate.ToString()).Distinct());
                        var emails = string.Join(";", historyGroup.Select(x => x.Email).Distinct());
                        var ipAddresses = string.Join(";", historyGroup.Select(x => x.IPAddress).Distinct());
                        var numResults = string.Join(";", historyGroup.Select(x => x.NumResults).Distinct());
                        var numSecondary = string.Join(";", historyGroup.Select(x => x.NumSecondary).Distinct());
                        var searchTimes = string.Join(";", historyGroup.Select(x => x.SearchTime.ToString()).Distinct());
                        var totalTimes = string.Join(";", historyGroup.Select(x => x.TotalTime.ToString()).Distinct());

                        Row row = new Row();

                        row.AppendChild(CommonExcelProvider.ConstructCell(ids, CellValues.String, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(dates, CellValues.String, columnIndex++, rowIndex, 3));
                        row.AppendChild(CommonExcelProvider.ConstructCell(emails, CellValues.String, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(ipAddresses, CellValues.String, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(numResults, CellValues.String, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(numSecondary, CellValues.String, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(searchTimes, CellValues.String, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(totalTimes, CellValues.String, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(historyGroup.Key, CellValues.String, columnIndex++, rowIndex));

                        List<string> categories = new List<string>();
                        List<string> manufacturers = new List<string>();
                        List<string> productLines = new List<string>();
                        List<string> cisfbses = new List<string>();
                        List<string> masterformats = new List<string>();
                        List<string> omniclasses = new List<string>();
                        List<string> uniclasses = new List<string>();
                        List<string> uniformatsList = new List<string>();
                        List<string> certificatesList = new List<string>();
                        List<string> fileTypesList = new List<string>();
                        List<string> qualityItemsList = new List<string>();
                        List<string> publishedList = new List<string>();
                        List<string> featuredList = new List<string>();
                        List<string> sortTypeList = new List<string>();
                        foreach (var history in historyGroup)
                        {
                            var options = JsonConvert.DeserializeObject<ProductSearchOptions>(history.Options);
                            if (options != null)
                            {
                                var category = options.CategoryId > 0 && allCategories.ContainsKey(options.CategoryId) ? allCategories[options.CategoryId] : string.Empty;
                                if (string.IsNullOrWhiteSpace(category) && !string.IsNullOrWhiteSpace(options.CategoryVanityUrl) && allCategoryVanityUrls.ContainsKey(options.CategoryVanityUrl))
                                {
                                    category = allCategoryVanityUrls[options.CategoryVanityUrl];
                                }
                                categories.Add(category);
                                if (options.ManufacturerIds != null && options.ManufacturerIds.Any())
                                {
                                    manufacturers = allManufacturers.Where(x => options.ManufacturerIds.Contains(x.Key)).Select(x => x.Value).ToList();
                                }
                                var productLine = options.ProductLineId > 0 && allProductLines.ContainsKey(options.ProductLineId) ? allProductLines[options.ProductLineId] : string.Empty;
                                productLines.Add(productLine);
                                var cisfbs = options.CisfbId > 0 && allCisfbs.ContainsKey(options.CisfbId) ? allCisfbs[options.CisfbId] : string.Empty;
                                cisfbses.Add(cisfbs);
                                var masterformat = options.ExternalMasterformatId > 0 && allMasterformats.Select(x => x.Id).Contains(options.ExternalMasterformatId) ? allMasterformats.FirstOrDefault(x => x.Id == options.ExternalMasterformatId)?.Code : string.Empty;
                                masterformats.Add(masterformat);
                                var omniclass = options.OmniclassId > 0 && allOmniclasses.ContainsKey(options.OmniclassId) ? allOmniclasses[options.OmniclassId] : string.Empty;
                                omniclasses.Add(omniclass);
                                var uniclass = options.UniclassId > 0 && allUniclasses.ContainsKey(options.UniclassId) ? allUniclasses[options.UniclassId] : string.Empty;
                                uniclasses.Add(uniclass);
                                var uniformats = options.UniformatId > 0 && allUniformats.ContainsKey(options.UniformatId) ? allUniformats[options.UniformatId] : string.Empty;
                                uniformatsList.Add(uniformats);
                                var certificates = options.ExternalCertificateIds != null ? options.ExternalCertificateIds.Select(c => allCertificates.Contains(c) ? c.ToString() : "?") : new List<string>();
                                certificatesList.AddRange(certificates);
                                var fileTypes = options.ProductFileTypeIds != null ? options.ProductFileTypeIds.Select(f => f.ToString()) : new List<string>();
                                fileTypesList.AddRange(fileTypes);
                                var qualityItems = options.QualityItemIds != null ? options.QualityItemIds.Select(c => allQualityItems.ContainsKey(c) ? allQualityItems[c] : "?") : new List<string>();
                                qualityItemsList.AddRange(qualityItems);
                                publishedList.Add(options.Published.ToString().ToUpper());
                                featuredList.Add(options.Featured.ToString().ToUpper());
                                sortTypeList.Add(options.SortType.ToString());
                            }
                        }

                        row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(";", categories.Distinct()), CellValues.String, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(";", manufacturers.Distinct()), CellValues.String, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(";", productLines.Distinct()), CellValues.String, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(";", cisfbses.Distinct()), CellValues.String, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(";", masterformats.Distinct()), CellValues.String, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(";", omniclasses.Distinct()), CellValues.String, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(";", uniclasses.Distinct()), CellValues.String, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(";", uniformatsList.Distinct()), CellValues.String, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(";", certificatesList.Distinct()), CellValues.String, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(";", fileTypesList.Distinct()), CellValues.String, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(";", qualityItemsList.Distinct()), CellValues.String, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(";", publishedList.Distinct()), CellValues.String, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(";", featuredList.Distinct()), CellValues.String, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(";", sortTypeList.Distinct()), CellValues.String, columnIndex++, rowIndex));
                        row.AppendChild(CommonExcelProvider.ConstructCell(historyGroup.Count().ToString(), CellValues.String, columnIndex++, rowIndex));
                        sheetData.AppendChild(row);
                        rowIndex++;
                    }
                    catch (Exception ex)
                    {
                        Log.Warning("Error exporting search history {0}: {1}", historyGroup.Key, ex.Message);
                    }
                }

                workbookpart.Workbook.Save();

                spreadsheetDocument.Dispose();

                return ms.ToArray();
            }
        }
    }
}