﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class CboCertificateService : ICboCertificateService
    {
        public async Task<object> ChangeProductCertificates(IUnitOfWork unitOfWork, string userId, ChangeProductCertificatesModel model)
        {
            var products = await unitOfWork.ProductRepository.GetAll().Where(x => x.ProductCertificates
                                                                                .FirstOrDefault(a => a.ExternalCertificateId == model.RemoveCertificateId) != null
                                                                          ).Distinct().ToListAsync();
            if (!products.Any() && !model.AddCertificatesIds.Any())
            {
                return "Success checked.";
            };

            var allProductCert = products.SelectMany(x => x.ProductCertificates).ToList();

            var removeProductCertificates = allProductCert.Where(x => x.ExternalCertificateId == model.RemoveCertificateId)
                                                          .ToList();

            unitOfWork.BeginTransaction();

            //add new crtificates
            if (model.AddCertificatesIds.Any())
            {
                foreach (var product in products)
                {
                    foreach (var exCertificate in model.AddCertificatesIds)
                    {
                        if (product.ProductCertificates.Any(x => x.ExternalCertificateId == exCertificate))
                            continue;

                        ProductCertificate newProductCertificate = new ProductCertificate
                        {
                            ExternalCertificateId = exCertificate,
                            ProductId = product.Id,
                            CreatedDate = DateTime.UtcNow,
                            CreatedById = userId
                        };
                        unitOfWork.ProductCertificateRepository.Insert(newProductCertificate);
                    }
                }
            }
            if (removeProductCertificates.Any())
            {
                unitOfWork.ProductCertificateRepository.Delete(removeProductCertificates);
            }

            try
            {
                await unitOfWork.SaveAsync();
                unitOfWork.CommitTransaction();
            }
            catch (Exception ex)
            {
                unitOfWork.RollbackTransaction();
                throw new InvalidInputException(ex.Message);
            }

            return new
            {
                certificateIdRemoved = model.RemoveCertificateId,
                certificateidsAdded = model.AddCertificatesIds,
                productsEditedCount = products?.Count()
            };
        }
    }
}
