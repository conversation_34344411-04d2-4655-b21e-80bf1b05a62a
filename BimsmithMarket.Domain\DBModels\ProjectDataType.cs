﻿using BIMsmithMarket.Domain.DBModels.RevitProcessing;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class ProjectDataType : BaseEntity
    {
        [Required]
        public string Title { get; set; }

        [Required]
        public string Description { get; set; }

        [Required]
        public string VanityUrl { get; set; }

        public string MetaTitle { get; set; }

        public string MetaDescription { get; set; }

        public string MetaKeyWords { get; set; }

        public string Header { get; set; }

        public int? ImageId { get; set; }

        [ForeignKey("ImageId")]
        public virtual Photo Image { get; set; }

        public int? MicrositeImageId { get; set; }

        [ForeignKey("MicrositeImageId")]
        public virtual Photo MicrositeImage { get; set; }

        public int? IconId { get; set; }

        [ForeignKey("IconId")]
        public virtual Photo Icon { get; set; }

        public int? ActiveImageId { get; set; }

        [ForeignKey("ActiveImageId")]
        public virtual Photo ActiveImage { get; set; }

        public int? DefaultImageId { get; set; }

        [ForeignKey("DefaultImageId")]
        public virtual Photo DefaultImage { get; set; }

        /// <summary>
        /// Parent software category (Revit, Autocad and other)
        /// </summary>
        public int? ParentId { get; set; }

        [ForeignKey("ParentId")]
        public virtual ProjectDataType ParentDataType { get; set; }

        public virtual ICollection<ProjectDataType> Children { get; set; }

        public virtual ICollection<RevitProcessProjectDataType> RevitProcessProjectDataTypes { get; set; }

        public virtual ICollection<RevitJob> RevitJobs { get; set; }

        public ProjectDataType()
        {
            Children = new List<ProjectDataType>();
            RevitProcessProjectDataTypes = new List<RevitProcessProjectDataType>();
            RevitJobs = new List<RevitJob>();
        }
    }
}