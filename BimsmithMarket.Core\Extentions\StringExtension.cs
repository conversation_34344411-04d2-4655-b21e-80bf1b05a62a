﻿using BIMsmithMarket.Domain.Constants;
using System;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;

namespace BIMsmithMarket.Core.Extentions
{
    public static class StringExtension
    {
        private static readonly MD5 Md5Instance = MD5.Create();

        private static readonly Func<string, string> GetHash = strToHashing =>
            Convert.ToBase64String(Md5Instance.ComputeHash(Encoding.UTF8.GetBytes(strToHashing)));

        public static string TrimAndReduce(this string str)
        {
            return ConvertWhitespacesToSingleSpaces(str).Trim();
        }

        public static string ConvertWhitespacesToSingleSpaces(this string value)
        {
            return Regex.Replace(value, @"\s+", " ");
        }

        public static string AsVanityUrl(this string value)
        {
            return Regex.Replace(Regex.Replace(value.Trim().Replace(' ', '-'), Constants.ReplaceUrlVanityRegax, ""), "[-]{2,}", "-");
        }

        public static string GetMd5Hash(this string strToHash)
        {
            return GetHash(strToHash);
        }
    }
}