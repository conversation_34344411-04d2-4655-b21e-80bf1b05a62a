﻿using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface ICategoryKeyStatService
    {
        Task<object> UnitsAsync(IUnitOfWork unitOfWork);
        Task<object> ListAsync(IUnitOfWork unitOfWork, string q, int offset, int count);
        Task<object> GetAsync(IUnitOfWork unitOfWork, int id);
        Task<object> AddAsync(IUnitOfWork unitOfWork, string userId, AddCategoryKeyStatModel model);
        Task EditAsync(IUnitOfWork unitOfWork, string userId, EditCategoryKeyStatModel model);
        Task DeleteAsync(IUnitOfWork unitOfWork, int id);
    }
}
