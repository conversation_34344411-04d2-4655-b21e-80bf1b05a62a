﻿using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BIMsmithBlog.Controllers
{
    [Route("api/[controller]/[action]")]
    public class CheckController : ControllerBase
    {
        private ICheckService _checkService;

        public CheckController(ICheckService checkService)
        {
            _checkService = checkService;
        }

        [HttpGet]
        public async Task<IActionResult> CheckSMTPCredentials()
        {
            return Ok(await _checkService.CheckSMTPCredentialsAsync("TEST BLOG SMTP CREDENTIALS"));
        }
    }
}