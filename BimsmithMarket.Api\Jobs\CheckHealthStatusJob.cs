﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Quartz;
using Serilog;
using System.Threading.Tasks;
namespace BIMsmithMarket.Api.Jobs
{
    [DisallowConcurrentExecution]
    public class CheckHealthStatusJob : IJob
    {
        private readonly string _jobName = "Check Health Status Job";
        private readonly IHealthDashboardService _healthDashboardService;

        public CheckHealthStatusJob(IHealthDashboardService healthDashboardService)
        {
            _healthDashboardService = healthDashboardService;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            //Check health status only for prod
            if (ConfigurationHelper.GetValue("Environment") == "prod")
            {
                Log.Information($"[{_jobName}] started");

                using (IUnitOfWork unitOfWork = UnitOfWork.Create())
                {
                    var manufacturers = await unitOfWork.ManufacturerRepository.GetAll()
                                                        .ToArrayAsync();

                    foreach (var manufacturer in manufacturers)
                    {
                        manufacturer.HealthCheckStatus = await _healthDashboardService.CheckHealthForManufacturerAsync(manufacturer.Id, unitOfWork);
                        unitOfWork.ManufacturerRepository.Edit(manufacturer);
                    }
                    await unitOfWork.SaveAsync();
                    CacheHelper.ClearSpecificCacheAndInvalidateSearch("*/api/Manufacturer/List*");

                    var productLines = await unitOfWork.ProductLineRepository.GetAll()
                                                       .ToArrayAsync();

                    foreach (var productLine in productLines)
                    {
                        productLine.HealthCheckStatus = await _healthDashboardService.CheckHealthForProductLineAsync(productLine.Id, unitOfWork);
                        unitOfWork.ProductLineRepository.Edit(productLine);
                    }
                    await unitOfWork.SaveAsync();
                    CacheHelper.ClearSpecificCacheAndInvalidateSearch("*/api/ProductLine/List*");
                }
            }
        }
    }
}