﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Configurations>Debug;Release;Dev;UAT</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AsyncEnumerator" Version="4.0.2" />
    <PackageReference Include="BIMsmith.ExcelProvider" Version="8.0.2" />
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="DotNetZip" Version="1.16.0" />
    <PackageReference Include="Dropbox.Api" Version="7.0.0" />
    <PackageReference Include="HtmlAgilityPack" Version="1.11.62" />
    <PackageReference Include="MediaTypeMap.Core" Version="2.3.3" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.2.5" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
    <PackageReference Include="PdfSharpCore" Version="1.3.65" />
    <PackageReference Include="Stripe.net" Version="45.7.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BIMsmithMarket.Core\BIMsmithMarket.Core.csproj" />
    <ProjectReference Include="..\BIMsmithMarket.DataLayer\BIMsmithMarket.DataLayer.csproj" />
  </ItemGroup>

</Project>
