﻿using Azure.Storage.Queues.Models;
using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Quartz;
using Serilog;
using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Jobs
{
    [DisallowConcurrentExecution]
    public class DownloadFilesJob : IJob
    {
        private readonly string FolderFiles = Path.Combine(Path.GetTempPath(), ConfigurationHelper.GetValue("Environment"), "Files");
        private readonly string _jobName = "Download Files Job";
        private readonly IFileService _fileService;
        private readonly IAzureStorageService _azureStorageService;

        public DownloadFilesJob(
            IFileService fileService,
            IAzureStorageService azureStorageService)
        {
            _fileService = fileService;
            _azureStorageService = azureStorageService;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            Log.Information($"[{_jobName}] started");

            await AddFilesToQueueAsync();
            await StartSyncFilesProcessAsync();
        }

        private async Task AddFilesToQueueAsync()
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var fileUrlsQueue = _azureStorageService.GetQueueByName(AzureStorageConstants.ProductFileUrlsQueue);
                var filesLimit = 1000;
                var files = await unitOfWork.ProductFileRepository.GetAll()
                    .Where(pf => pf.File.SyncUrl != null
                              && pf.File.SyncUrl != ""
                              && pf.File.UpdatesCount < 3
                              && pf.File.SyncStatus != SyncFileStatus.InProcess
                              && pf.File.NextSyncDateTime <= DateTime.UtcNow
                    )
                    .Select(pf => pf.File)
                    .Distinct()
                    .Take(filesLimit)
                    .ToListAsync();

                foreach (var file in files)
                {
                    file.SyncStatus = SyncFileStatus.InProcess;
                    await fileUrlsQueue.SendMessageAsync(file.Id.ToString());
                    unitOfWork.FileRepository.Edit(file);
                }

                await unitOfWork.SaveAsync();

                if (files.Any())
                {
                    Log.Information($"[{_jobName}] " + files.Count + " files have been added to the upload queue");
                }
            }
        }

        private async Task StartSyncFilesProcessAsync()
        {
            var fileUrlsQueue = _azureStorageService.GetQueueByName(AzureStorageConstants.ProductFileUrlsQueue);
            var filesContainer = _azureStorageService.GetContainerByName(AzureStorageConstants.FilesContainer);

            if (!Directory.Exists(FolderFiles))
            {
                Directory.CreateDirectory(FolderFiles);
            }

            while (await _azureStorageService.GetMessageCountAsync(fileUrlsQueue.Name) > 0)
            {
                QueueMessage message = await fileUrlsQueue.ReceiveMessageAsync();
                if (message == null)
                    continue;

                int fileId = -1;
                if (int.TryParse(message.MessageText, out fileId))
                {
                    using (IUnitOfWork unitOfWork = UnitOfWork.Create())
                    {
                        try
                        {
                            unitOfWork.BeginTransaction();
                            var file = unitOfWork.FileRepository.GetAll().FirstOrDefault(f => f.Id == fileId);
                            if (file != null)
                            {
                                file.SyncStatus = SyncFileStatus.Free;

                                var attachUrl = file.SyncUrl;

                                if (string.IsNullOrWhiteSpace(attachUrl))
                                {
                                    fileUrlsQueue.DeleteMessage(message.MessageId, message.PopReceipt);
                                    continue;
                                }

                                if (!attachUrl.StartsWith("http"))
                                {
                                    attachUrl = "http://" + attachUrl;
                                }

                                try
                                {
                                    await _fileService.DownloadFileFromUrlAsync(file, attachUrl, filesContainer, unitOfWork);
                                }
                                catch (Exception e)
                                {
                                    Log.Error($"[{_jobName}] " + e.Message);
                                    if (e is WebException && ((WebException)e).Response is HttpWebResponse)
                                    {
                                        file.SyncStatusCode = (int)((HttpWebResponse)((WebException)e).Response).StatusCode;
                                    }
                                    else
                                    {
                                        file.SyncStatusCode = (int)HttpStatusCode.NotFound;
                                    }
                                    file.SyncStatus = SyncFileStatus.Free;
                                    file.UpdatesCount++;
                                    file.NextSyncDateTime = DateTime.UtcNow.Date.AddMonths(1);
                                }
                                finally
                                {
                                    unitOfWork.FileRepository.Edit(file);
                                    Debug.WriteLine($"Passed for fileId: {fileId} at " + DateTime.UtcNow.ToString());
                                    Log.Information($"[{_jobName}] " + $"Passed for fileId: {fileId} at " + DateTime.UtcNow.ToString());
                                    await unitOfWork.SaveAsync();

                                    unitOfWork.CommitTransaction();
                                    fileUrlsQueue.DeleteMessage(message.MessageId, message.PopReceipt);
                                }
                            }
                            else
                            {
                                Debug.WriteLine("Delete fileId: " + fileId + " at " + DateTime.UtcNow.ToString());
                                Log.Information($"[{_jobName}] " + "Delete fileId: " + fileId + " at " + DateTime.UtcNow.ToString());
                                fileUrlsQueue.DeleteMessage(message.MessageId, message.PopReceipt);
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine(ex.Message);
                            Log.Error($"[{_jobName}] " + $"Error for file Id: {fileId} " + ex.Message);
                            fileUrlsQueue.DeleteMessage(message.MessageId, message.PopReceipt);
                            continue;
                        }
                    }
                }
            }
        }
    }
}