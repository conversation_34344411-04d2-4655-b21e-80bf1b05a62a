﻿using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class ProductPhoto : BaseEntity
    {
        public int ProductId { get; set; }

        public int PhotoId { get; set; }

        public string Title { get; set; }

        /// ------------------------------------------

        [Foreign<PERSON><PERSON>("ProductId")]
        public virtual Product Product { get; set; }

        [ForeignKey("PhotoId")]
        public virtual Photo Photo { get; set; }
    }
}