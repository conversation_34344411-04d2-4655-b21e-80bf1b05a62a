﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using System.Threading.Tasks;

namespace BIMsmithMarket.Core.Middelewares
{
    public class BrowserCheckMiddleware
    {
        private readonly RequestDelegate _next;

        public BrowserCheckMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            if (!context.Request.Headers.TryGetValue("Browser", out StringValues browser)
                && browser.ToString().ToLower() == "internetexplorer")
                context.Response.Headers.Add("Origin", context.Request.Host.ToString());

            await _next(context);
        }
    }
}