﻿using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class DetailFile : BaseEntity
    {
        public int FileId { get; set; }

        public int DetailId { get; set; }

        public bool WasChanged { get; set; }

        public int? ProjectDataTypeId { get; set; }

        public string SoftwareRelease { get; set; }

        public string ContentCreatedby { get; set; }

        public string FileVersion { get; set; }

        public string ContentCheckedBy { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        [ForeignKey("DetailId")]
        public virtual Detail Detail { get; set; }

        [ForeignKey("FileId")]
        public virtual File File { get; set; }

        [ForeignKey("ProjectDataTypeId")]
        public virtual ProjectDataType ProjectDataType { get; set; }
    }
}