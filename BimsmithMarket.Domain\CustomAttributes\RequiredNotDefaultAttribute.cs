﻿using System;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.CustomAttributes
{
    public class RequiredNotDefaultAttribute : ValidationAttribute
    {
        public const string DefaultErrorMessage = "The {0} field is required and must not have the default value";

        public RequiredNotDefaultAttribute() : base(DefaultErrorMessage)
        {

        }

        public override bool IsValid(object value)
        {
            if (value is null)
                return false;

            Type type = value.GetType();

            if (type.IsValueType)
            {
                object defaultValue = Activator.CreateInstance(type);
                return !value.Equals(defaultValue);
            }

            return true;
        }
    }
}