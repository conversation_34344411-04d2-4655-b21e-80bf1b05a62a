﻿using BIMsmithMarket.Domain.Enums;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class StaticExcelFile : BaseEntity
    {
        public string Url { get; set; }

        public ExcelProcessingStatus Status { get; set; }

        //List<string> serialized to JSON
        public string ValidationErrors { get; set; }

        public int TotalProductsCount { get; set; }

        public int SucceedProdutcsCount { get; set; }

        public int ErrorProductsCount { get; set; }

        public int CancelledProductsCount { get; set; }

        public int? ManufacturerId { get; set; }

        [ForeignKey("ManufacturerId")]
        public virtual Manufacturer Manufacturer { get; set; }

        public virtual ICollection<StaticExcelProductError> StaticExcelProductErrors { get; set; }

        public StaticExcelFile()
        {
            StaticExcelProductErrors = new List<StaticExcelProductError>();
        }
    }
}