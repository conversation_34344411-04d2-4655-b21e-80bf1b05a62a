﻿using BIMsmithMarket.Domain.DBModels;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Models
{
    public class AddNewsModel
    {
        [Required]
        [StringLength(200)]
        public string VanityId { get; set; }

        public int? NewsCategoryId { get; set; }

        [Required]
        public string Title { get; set; }

        [Required]
        public string HtmlBody { get; set; }

        [Required]
        public string ImageUrlBig { get; set; }

        [Required]
        public string ImageUrlSmall { get; set; }

        public string AuthorTitle { get; set; }

        public string Tags { get; set; }

        [Required]
        public PublishOption PublishOption { get; set; }

        public DateTime? PublishedDate { get; set; }

        public string MetaDescription { get; set; }

        public string MetaKeywords { get; set; }

        [Required]
        public List<NewsTargetSite> Targets { get; set; }

        public DateTime? SchedulePublishDate { get; set; }
    }

    public class EditNewsModel : AddNewsModel
    {
        [Required]
        public int Id { get; set; }
    }

    public class ChangeNewsPublishStateModel
    {
        [Required]
        public int Id { get; set; }

        [Required]
        public PublishOption PublishOption { get; set; }
    }
}