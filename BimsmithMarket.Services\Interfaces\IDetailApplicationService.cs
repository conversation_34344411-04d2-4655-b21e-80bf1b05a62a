﻿using BIMsmithMarket.Domain.Models.ApplicationDetailModels;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IDetailApplicationService
    {
        Task<dynamic> AddAsync(AddDetailApplicationViewModel model, string userId);

        Task<dynamic> EditAsync(EditDetailApplicationViewModel model, string userId);

        Task<bool> DeleteAsync(int id);

        Task<dynamic> GetAsync(int id);

        Task<dynamic> GetByVanityUrlAsync(string vanityUrl);

        Task<dynamic> ListAsync(string query, int count, int offset);
    }
}