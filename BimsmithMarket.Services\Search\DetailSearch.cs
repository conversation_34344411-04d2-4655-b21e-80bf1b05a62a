﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Search.FullTextSearch;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;

namespace BIMsmithMarket.Services.Search
{
    public class DetailSearchOptions
    {
        public int DetailId { get; set; }

        public string Query { get; set; }

        public string ListQuery { get; set; }

        public List<int> ManufacturerIds { get; set; }

        public bool Published { get; set; }

        public string RegionId { get; set; }

        public string StateId { get; set; }
    }

    public class DetailSearchResults
    {
        public DetailSearchOptions Options { get; set; }

        public string OriginalQuery { get; set; }

        public string Query { get; }

        public List<QueryToken> Tokens { get; set; }

        public SearchCache SearchCache { get; set; }

        public IQueryable<Detail> Results { get; set; }

        public List<string> Explain { get; set; } = new List<string>();

        public DetailSearchResults(DetailSearchOptions options, SearchCache searchCache, IQueryable<Detail> input)
        {
            OriginalQuery = options.Query;
            Query = NormalizeQuery(options);
            Tokens = SearchResults.SplitIntoTokens(Query);
            Options = options;
            SearchCache = searchCache;
            Results = input;
        }

        public void ApplyCommonFilters()
        {
            if (Options.Published)
            {
                Results = Results.Where(a => a.Published && a.Manufacturer.Published);
            }

            if (Options.ManufacturerIds != null && Options.ManufacturerIds.Any())
            {
                Results = Results.Where(d => Options.ManufacturerIds.Contains(d.ManufacturerId.Value));
            }

            if (!string.IsNullOrWhiteSpace(Options.RegionId))
            {
                Results = Results.Where(m => m.RegionIds == null || m.RegionIds == "" || m.RegionIds.Contains(Options.RegionId));
            }

            if (!string.IsNullOrWhiteSpace(Options.StateId))
            {
                Results = Results.Where(m => m.StateIds == null || m.StateIds == "" || m.StateIds.Contains(Options.StateId));
            }

            // if we have high confidence matches return just those, otherwise all            
            if (Options.DetailId > 0)
            {
                Results = Results.Where(e =>
                        e.Id == Options.DetailId && e.Published); // carried over: use original input, i.e. ignore keyword filter?
            }
        }

        public void ApplyTriggerTokens()
        {
            if (!Tokens.Any())
                return;

            if (Tokens.Any(t => t.Original == "interior"))
            {
                Results = Results.Where(r => r.Interior);
                Tokens.RemoveAll(t => t.Original == "interior");
            }
            if (Tokens.Any(t => t.Original == "exterior"))
            {
                Results = Results.Where(r => r.Exterior);
                Tokens.RemoveAll(t => t.Original == "interior");
            }
        }

        public void TryIdentifyManufacturers()
        {
            var unknownTokens = Tokens.Where(t => t.Type == QueryTokenType.Unknown).ToList();
            if (SearchCache.ManufacturerTokens != null && (Options.ManufacturerIds == null || !Options.ManufacturerIds.Any()) && unknownTokens.Any())
            {
                var matchedManufacturers = SearchCache.TryMatchExact(unknownTokens, SearchCache.ManufacturerSynonymTokens, SearchCache.DefaultLanguage, QueryTokenType.Manufacturer, Tokens.Count(t => t.Type != QueryTokenType.Noise), null);
                if (matchedManufacturers.Any())
                {
                    var manufacturerList = matchedManufacturers.Keys.ToList();
                    Results = Results.Union(Results.Where(t => manufacturerList.Contains(t.ManufacturerId.Value)));
                    Explain.Add($"Narrowing down to manufacturers {string.Join(",", manufacturerList)} off tokens {string.Join(", ", Tokens.Where(t => t.Type == QueryTokenType.Manufacturer).Select(t => t.Original))}");
                }
            }
        }

        public void FreeTextSearch()
        {
            var unknownTokens = Tokens.Where(t => t.Type == QueryTokenType.Unknown).ToList();
            var ftsDetails = new List<int>();
            if (unknownTokens.Any())
            {
                // we have a phrase, so results have to match something                
                var queryMatchResults = SearchCache.TryMatchWithConfidence(unknownTokens, SearchCache.DetailTokens, QueryTokenType.Detail, unknownTokens.Count);
                if (queryMatchResults.Any(m => m.Value >= WeightsTable.DetailBeelineThreshold))
                {
                    ftsDetails.AddRange(queryMatchResults.Where(m => m.Value >= WeightsTable.DetailBeelineThreshold).Select(m => m.Key));
                }
                else
                {
                    ftsDetails.AddRange(queryMatchResults.Keys);
                }
            }

            if (!string.IsNullOrWhiteSpace(Query))
            {
                var query = Query.ToLower();

                Explain.Add($"FTS returned {ftsDetails.Count} results");

                // check for applications
                var applicationDetails = SearchCache.DetailApplications.Where(a => query.Contains(a.Key)).SelectMany(a => a.Value).ToList();

                Explain.Add($"Application returned {applicationDetails.Count} results");

                // add FTS and simple name search results
                Results = Results.Where(a =>
                    a.Name.ToLower() == query ||
                    a.Name.ToLower().Contains(query) ||
                    ftsDetails.Contains(a.Id) ||
                    applicationDetails.Contains(a.Id));

                Explain.Add($"Adding results by query '{query}'");
            }
        }

        private static string NormalizeQuery(DetailSearchOptions options)
        {
            var searchQuery = (options.Query ?? string.Empty).TrimStart('!').Trim().ToLower();
            var listQuery = (options.ListQuery ?? string.Empty).TrimStart('!').Trim().ToLower();

            // ignore single character query
            if (searchQuery.Length < 2)
            {
                searchQuery = string.Empty;
            }
            if (listQuery.Length < 2)
            {
                listQuery = string.Empty;
            }

            if (string.IsNullOrWhiteSpace(searchQuery) && !string.IsNullOrWhiteSpace(listQuery))
            {
                searchQuery = listQuery;
            }

            while (searchQuery.Contains("  "))
            {
                searchQuery = searchQuery.Replace("  ", " ");
            }

            if (searchQuery.Any(q => char.IsSymbol(q)))
            {
                // strip (R), (TM) etc. special characters
                searchQuery = new string(searchQuery.Where(c => !char.IsSymbol(c)).ToArray());
            }

            if (searchQuery.EndsWith("s") && !searchQuery.EndsWith("joints")) // strip plural as we have singular in the database
            {
                searchQuery = searchQuery.Substring(0, searchQuery.Length - 1);
            }

            return searchQuery;
        }
    }

    /// <summary>
    /// Utility class for performing product detail search
    /// </summary>
    public class DetailSearch
    {
        public static IQueryable<Detail> Search(DetailSearchOptions options, IQueryable<Detail> input, IUnitOfWork unitOfWork)
        {
            return SearchAlgoV1(options, input.AsNoTracking(), unitOfWork);
        }

        protected static IQueryable<Detail> SearchAlgoV1(DetailSearchOptions options, IQueryable<Detail> input, IUnitOfWork unitOfWork)
        {
            var searchResults = new DetailSearchResults(options, SearchCache.Get(unitOfWork), input);

            searchResults.ApplyCommonFilters();
            searchResults.ApplyTriggerTokens();
            searchResults.TryIdentifyManufacturers();
            searchResults.FreeTextSearch();

            return searchResults.Results;
        }
    }
}