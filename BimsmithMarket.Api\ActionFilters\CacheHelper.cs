﻿using BIMsmithMarket.Services.Search.FullTextSearch;

namespace BIMsmithMarket.Api.ActionFilters
{
    public static class CacheHelper
    {
        public static void ClearAllCache()
        {
            WebApiOutputCacheAttribute.ClearAllCache();
        }

        public static void ClearAllCacheAndInvalidateSearch()
        {
            WebApiOutputCacheAttribute.ClearAllCache();
            SearchCache.Invalidate();
        }

        public static void ClearSpecificCache(string pattern)
        {
            WebApiOutputCacheAttribute.ClearCacheKeysByPattern(pattern);
        }

        public static void ClearSpecificCacheAndInvalidateSearch(string pattern)
        {
            WebApiOutputCacheAttribute.ClearCacheKeysByPattern(pattern);
            SearchCache.Invalidate();
        }
    }
}