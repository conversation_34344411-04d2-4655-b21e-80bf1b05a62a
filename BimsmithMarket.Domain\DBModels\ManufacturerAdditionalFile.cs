﻿using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class ManufacturerAdditionalFile : BaseEntity
    {
        public int ManufacturerId { get; set; }

        public int FileId { get; set; }

        public int? ProjectDataTypeId { get; set; }

        [ForeignKey("ProjectDataTypeId")]
        public virtual ProjectDataType ProjectDataType { get; set; }

        public float Weight { get; set; }

        public string CustomFileId { get; set; }

        public string RegionIds { get; set; }

        public bool IsAttachment { get; set; }

        /// ------------------------------------------

        [<PERSON><PERSON><PERSON>("ManufacturerId")]
        public virtual Manufacturer Manufacturer { get; set; }


        [ForeignKey("FileId")]
        public virtual File File { get; set; }
    }
}