﻿using BIMsmithMarket.Domain.Dto.HealthDashboardDto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IHealthDashboardService
    {
        Task<HealthCheckStatus> CheckHealthForProductLineAsync(int productLineId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default);

        Task<HealthCheckStatus> CheckHealthForManufacturerAsync(int manufacturerId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default);

        Task<HealthCheckStatus> CheckHealthForProductAsync(int productId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default);

        Task<int> CalculateBrokenLinksAsync(ICollection<int> fileIds, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default);

        Task<int[]> GetBrokenLinksFileIdsAsync(ICollection<int> fileIds, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default);

        Task ManageDashboardAccessAsync(HealthDashboardManageAccessDto model, string creatorUserId);

        Task<ICollection<HealthDashboardAssignedUserListDto>> AssignedUserListAsync(HealthDashboardFilterDto model, CancellationToken cancellationToken = default);

        Task<HealthDashboardInfoDto> GetDashboardAsync(HealthDashboardFilterDto model, CancellationToken cancellationToken = default);

        Task<List<HealthDashboardInfoDto>> GetAllTypeDashboardAsync(HealthDashboardFilterDto model, CancellationToken cancellationToken = default);

        Task<List<HealthDashboardInfoDto>> GetAllTypeManufacturerDashboardAsync(HealthDashboardFilterDto model, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default);

        Task<List<HealthDashboardInfoDto>> GetAllTypeProductLineDashboardAsync(HealthDashboardFilterDto model, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default);

        Task<ICollection<HealthDashboardInfoDto>> GetUserDashboardsAsync(HealthDashboardFilterPeriod filterPeriod, string userId, CancellationToken cancellationToken = default);

        Task<List<HealthDashboardInfoDto>> GetAllMarketDashboardAsync(CancellationToken cancellationToken = default);

        Task<int[]> GetFileIdsAsync(HealthDashboardFilterDto model, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default);

        Task<int[]> GetModifiedDateFileIdsAsync(HealthDashboardFilterDto model, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default);

        Task<int[]> GetBlankDescriptionsProductIdsAsync(HealthDashboardFilterDto model, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default);

        Task<int[]> GetNoRevitFileProductIdsAsync(HealthDashboardFilterDto model, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default);

        Task<int[]> GetRevitFileNoExtensionFileIdsAsync(HealthDashboardFilterDto model, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default);

        Task<int[]> GetBlankNumberForAllManufacturersAsync(IUnitOfWork unitOfWork, CancellationToken cancellationToken = default);

        Task<ICollection<HealthDashboardExportProductWithZipFilesDto>> GetProductsWithZipFilesAsync(HealthDashboardFilterDto model, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default);

        Task<HealthDashboardAccessDto[]> GetUserDashboardListAsync(string userId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default);

        Task<HealthDashboardAccessWithNameDto[]> GetUserDashboardWithNameListAsync(HealthDashboardViewType viewType, string userId, IUnitOfWork unitOfWork, CancellationToken cancellationToken = default);

        Task<List<HealthDashboardInfoDto>> GetUserDashboardAsync(HealthDashboardFilterDto model, CancellationToken cancellationToken = default);
    }
}