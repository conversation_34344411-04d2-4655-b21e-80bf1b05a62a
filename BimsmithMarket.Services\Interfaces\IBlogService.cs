﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using System.Net;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IBlogService
    {
        Task<object> PublicListAsync(IUnitOfWork unitOfWork, int categoryId, string q, int offset, int count, bool isFeaturedFirst, TargetBlogType targetType, string author, string vanityCategoryid, string ip);
        Task UpdateAuthorPhotoAsync(IUnitOfWork unitOfWork, UpdateAuthorPhotoModel model);
        Task<object> ListAsync(IUnitOfWork unitOfWork, string q, int offset, int count);
        Task<object> GetAsync(IUnitOfWork unitOfWork, int id, string vanityId);
        Task<object> AddAsync(IUnitOfWork unitOfWork, string userId, AddBlogPostModel model);
        Task<object> EditAsync(IUnitOfWork unitOfWork, string userId, EditBlogPostModel model);
        Task DeleteAsync(IUnitOfWork unitOfWork, string userId, int id);
        Task<object> CategoriesAsync(IUnitOfWork unitOfWork);
        Task<object> AddCategoryAsync(IUnitOfWork unitOfWork, string userId, AddBlogCategoryModel model);
        Task<object> EditCategoryAsync(IUnitOfWork unitOfWork, string userId, EditBlogCategoryModel model);
        Task<object> CommentsListAsync(IUnitOfWork unitOfWork, int? blogPostId, string q, int offset, int count);
        Task AddSwatchboxComment(IUnitOfWork unitOfWork, string emailsPath, AddSwatchboxBlogCommentModel model);
        Task DeleteCommentAsync(IUnitOfWork unitOfWork, int id);
        Task<bool> IsReCaptchValidAsync(WebResponse response);
        Task<MetaInfoDto> GetNewsMetaInfoAsync(string vanityId, IUnitOfWork unitOfWork);

        Task<string> GetUserPhoto(string email);
    }
}