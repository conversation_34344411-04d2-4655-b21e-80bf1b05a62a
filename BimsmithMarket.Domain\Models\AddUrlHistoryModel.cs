﻿using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Models
{
    public class AddVanityHistoryModel
    {
        public int? ParentId { get; set; }

        [MaxLength(200)]
        public string VanityUrl { get; set; }

        [MaxLength(64)]
        public string EntityType { get; set; }

        [Required]
        public int EntityId { get; set; }
    }

    public class EditVanityHistoryModel : AddVanityHistoryModel
    {
        [Required]
        public int Id { get; set; }
    }
}