﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Configurations>Debug;Release;Dev;UAT</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Storage.Blobs" Version="12.21.2" />
    <PackageReference Include="DocumentFormat.OpenXml" Version="3.1.0" />
    <PackageReference Include="Flurl" Version="4.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Features" Version="5.0.17" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.1.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.7" />
    <PackageReference Include="Microsoft.Extensions.Identity.Stores" Version="8.0.7" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.0.1" />
    <PackageReference Include="MongoDB.Bson" Version="2.28.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

</Project>
