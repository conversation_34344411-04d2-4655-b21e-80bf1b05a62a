using BIMsmithMarket.Core.Helpers;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using Microsoft.EntityFrameworkCore;
using BIMsmithMarket.DataLayer.Context;

namespace BIMsmithNewsroom
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            // Add services to the container.
            builder.Services.AddControllersWithViews();

            builder.Services
                .AddDbContextPool<ApplicationDbContext>((serviceProvider, options)
                    => options.UseLazyLoadingProxies()
                              .UseSqlServer(ConfigurationHelper.GetValue("ConnectionStrings:MarketDBConnection"))
                              .UseInternalServiceProvider(serviceProvider));

            Log.Logger = LogHelper.Initialize("Newsroom");

            var app = builder.Build();

            // Configure the HTTP request pipeline.
            if (!app.Environment.IsDevelopment())
            {
                app.UseExceptionHandler("/Home/Error");
                // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
                app.UseHsts();
            }

            app.UseHttpsRedirection();
            app.UseStaticFiles();

            app.UseRouting();

            app.UseAuthorization();

            app.MapControllerRoute
            (
               name: "Sitemap",
               pattern: "Sitemap.xml",
               defaults: new { controller = "SEOData", action = "Sitemap" }
            );

            app.MapControllerRoute(
               name: "robots",
               pattern: "robots.txt",
               defaults: new { controller = "SEOData", action = "Robots" }
            );

            app.MapControllerRoute
            (
                name: "NewsDetails",
                pattern: "{vanityId}",
                defaults: new { controller = "News", action = "Details" }
            );

            app.MapControllerRoute
            (
                name: "NewsTag",
                pattern: "{controller}/{action}/{tag?}",
                defaults: new { controller = "News", action = "Tag" }
            );

            app.MapControllerRoute(
                name: "default",
                pattern: "{controller=Home}/{action=Index}/{id?}");

            app.Run();
        }
    }
}