﻿using BIMsmithMarket.Domain.Enums.RevitProcessing;
using System;

namespace BIMsmithMarket.Domain.Dto.RevitProcessing
{
    public class RevitProcessListDto
    {
        public int Id { get; set; }

        public DateTime CreatedDate { get; set; }

        public RevitProcessType Type { get; set; }

        public RevitProcessStatus Status { get; set; }

        public DateTime? FinishDate { get; set; }

        public string ManufacturerName { get; set; }

        public int ProductsCount { get; set; }

        public int FilesCount { get; set; }

        public int FilesInProgress { get; set; }

        public int FilesCompleted { get; set; }

        public int FilesCompletedWithErrors { get; set; }

        public int FilesCancelled { get; set; }

        public int FilesFailed { get; set; }
    }
}