﻿using BIMsmithMarket.Domain.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IManufacturerBackupService
    {
        Task<bool> CreateManufacturerBackupAsync(int manufacturerId);

        Task<List<ManufacturerBackupsListModel>> GetManufacturerBackupsListAsync(int manufacturerId);

        Task<bool> ManufacturerHasBackupForCurrentDataAsync(int manufacturerId);
    }
}