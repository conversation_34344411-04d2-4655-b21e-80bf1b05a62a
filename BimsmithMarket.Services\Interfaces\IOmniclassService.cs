﻿using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IOmniclassService
    {
        Task<object> SearchAsync(IUnitOfWork unitOfWork, string q, int offset, int count);
        Task<object> GetAsync(IUnitOfWork unitOfWork, int id);
        Task<object> LevelListAsync(IUnitOfWork unitOfWork, int? parentId, string q);
    }
}
