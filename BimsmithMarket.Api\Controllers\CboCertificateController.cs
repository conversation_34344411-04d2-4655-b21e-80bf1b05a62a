﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// Class for work with new CBO certificates on Market
    /// </summary>
    [Route("api/[controller]/[action]")]
    public class CboCertificateController : BaseApiController
    {
        private readonly ICboCertificateService _cboCertificateService;

        public CboCertificateController(ICboCertificateService cboCertificateService)
        {
            _cboCertificateService = cboCertificateService;
        }

        /// <summary>
        /// Remove old certificate and add new group
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> ChangeProductCertificates([FromBody] ChangeProductCertificatesModel model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _cboCertificateService.ChangeProductCertificates(unitOfWork, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), model));
            }
        }
    }
}