﻿@using BIMsmithMarket.Core.Helpers
@model BIMsmithNewsroom.Models.NewsModel
@{
    Layout = null;
}

<!DOCTYPE html>

<html lang="en-US" prefix="og: http://ogp.me/ns#">

<head>
    @if (ViewBag.IsPrivate)
    {
        <meta name="robots" content="noindex">
    }
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width">
    <meta name="description" content="@ViewBag.MetaDescription">
    <meta name="keywords" content="@ViewBag.MetaKeywords">
    <meta name="keywords" content="@Model.Title">
    <meta property="og:description" content="@ViewBag.MetaDescription">
    <meta property="og:title" content="@Model.Title">
    <meta property="og:image" content="@Model.ImageUrl">
    <meta property="og:type" content="article">

    <link rel="icon" type="image/x-icon" href="~/favicon.png">
    <link rel="stylesheet" href="Content/froala_editor.pkgd.min.css">
    <link rel="stylesheet" href="Content/froala_style.min.css">
    <script type='text/javascript' src='https://cdnjs.cloudflare.com/ajax/libs/js-cookie/2.1.4/js.cookie.min.js'></script>


    <meta name="google-site-verification" content="S931UZF3IQ9ehCHUtrDeFGsTowM-hC7cX-ar3sRWYVI" />

    <meta name="msvalidate.01" content="5533F7259691FCA7EF63EFB3CDED200F" />

    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="@ViewBag.Title">



    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=***********-2"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', '***********-2');
    </script>

    <title>@ViewBag.Title</title>
    <link rel="stylesheet" href="~/Bundles/contentcssbundle.min.css" />

</head>
<body class="post-template-default single single-post postid-451 single-format-standard">
    <div id="page" class="hfeed site details">
        <header id="masthead" role="banner">
            <nav class="navbar navbar-default navbar-fixed-top navbar-left" role="navigation">
                <!-- Brand and toggle get grouped for better mobile display -->
                <div class="container" id="navigation_menu">
                    <div class="navbar-header">
                        <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-ex1-collapse">
                            <span class="sr-only">Toggle navigation</span>
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                        </button>
                        <a class="navbar-brand" href="@ConfigurationHelper.GetValue("BIMsmithUrl")">Newsroom</a>
                    </div>
                    <div class="sign-in">
                        <div style="display: none;" id="user-name"></div>
                        <div style="display: none;" id="head-drop" class="head-drop">
                            <div class="av">
                                <img id="profile-image" alt="" src="/images/img01.jpg" height="383" width="383">
                                <div id="settings-name" class="name"></div>
                            </div>
                            <div class="links">
                                <a href="@ConfigurationHelper.GetValue("MarketUrl")/">Market</a>
                                <a href="@ConfigurationHelper.GetValue("ForgeUrl")/">forge</a>
                                <a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/NewMyBIMSmith/settings">MyBIMsmith</a>
                            </div>
                        </div>
                        <a id="logout-button" style="display: none;" class="def-btn logout-button" href="javascript:void(0)">Log out</a>
                        <a id="login-button" style="display: inline-block;" class="def-btn" href="@ConfigurationHelper.GetValue("BIMsmithUrl")/Account/Login/">Log in</a>
                        <a id="signup-button" style="display: inline-block;" class="g-btn" href="@ConfigurationHelper.GetValue("BIMsmithUrl")/Account/Register">Sign up</a>
                    </div>
                    <div class="collapse navbar-collapse navbar-ex1-collapse">
                        <ul id="menu-menu-1" class="nav navbar-nav">
                            <li id="menu-item-101" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-101">
                                <a title="NewsRoom" href="/">NewsRoom</a>
                            </li>
                            <li id="menu-item-151" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-151">
                                <a title="Contact" href="@ConfigurationHelper.GetValue("BIMsmithUrl")/Contact">Contact</a>
                            </li>
                            <li id="mob-login"><a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/Account/Login/">Sign In</a></li>
                            <li id="mob-regis"><a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/Account/Register">Sign Up</a></li>
                        </ul>
                    </div>
                </div>
                <!--#container-->
            </nav>
            <div id="cc_spacer" style="height: 45px;"></div>
            <!-- used to clear fixed navigation by the themes js -->
            <div class="site-header">
                <div class="site-branding">
                    <a class="home-link" href="/" title="Newsroom" rel="home">
                        <h1 class="site-title">Newsroom</h1>
                        <!-- <h2 class="site-description">BIMsmithMarket</h2>
                        -->
                    </a>
                </div>
                <!--.site-branding-->
            </div>
            <!--.site-header-->
        </header>
        <div id="content" class="site-content">
            <div class="breads">
                <section id="bcn_widget-2" class="widget widget_breadcrumb_navxt">
                    <!-- Breadcrumb NavXT 5.6.0 -->
                    <span property="itemListElement" typeof="ListItem">
                        <a property="item" typeof="WebPage" title="Go to Newsroom." href="/" class="home">
                            <span property="name">Newsroom</span>
                        </a>
                        <meta property="position" content="1">
                    </span>
                    &gt;
                    <span property="itemListElement" typeof="ListItem">
                        <a property="item" typeof="WebPage" title="Go to the News category archives." href="/" class="taxonomy category">
                            <span property="name">News</span>
                        </a>
                        <meta property="position" content="2">
                    </span>
                    &gt;
                    <span property="itemListElement" typeof="ListItem">
                        <span property="name">@Model.Title</span>
                        <meta property="position" content="3">
                    </span>
                </section>
            </div>
            <div class="back">
                &lt;
                <a href="/">go back</a>
            </div>
            <div class="container">
                <div class="row">
                    <div id="primary" class="col-md-9 content-area">
                        <main id="main" role="main">
                            <article id="post-451" class="post-content post-451 post type-post status-publish format-standard has-post-thumbnail hentry category-news tag-architect tag-architecture tag-bim tag-building-information-modeling tag-design tag-designer tag-roofing">
                                <div class="featured-image" style="background-image: url(&quot;@Model.ImageUrl&quot;); display: none;">
                                    <img width="1038" height="576" src="@Model.ImageUrl" class="attachment-bimsmith-full-width size-bimsmith-full-width" alt="" style="display: none;">
                                </div>
                                <header class="entry-header">
                                    <span class="screen-reader-text">@Model.Title</span>
                                    <h1 class="entry-title">@Model.Title</h1>
                                    <div class="entry-meta">
                                        <h5 class="entry-date">
                                            <i class="fa fa-calendar-o"></i>
                                            <a href="/@Model.VanityId" title="8:07 pm" rel="bookmark">
                                                <time class="entry-date" datetime="@Model.PublishedDate.Value.ToString()" pubdate="">@Model.PublishedDate.Value.ToString("d")</time>
                                            </a>
                                            <span class="byline">
                                                <span class="sep"></span> <i class="fa fa-user"></i>
                                                <span class="author vcard">
                                                    <a class="url fn n" href="/author/bimsmithadmin/" title="View all posts by bimsmithadmin" rel="author">bimsmithadmin</a>
                                                </span>
                                            </span>
                                            <i class="fa fa-comments-o"></i>
                                            <span class="screen-reader-text">Comments</span>
                                            <a href="/@Model.VanityId" class="comments-link">0 Comment</a>
                                        </h5>
                                    </div>
                                    <div class="main-details-img">
                                        <img src="@Model.ImageUrl" alt="">
                                    </div>
                                    <!-- .entry-meta -->
                                </header>
                                <!-- .entry-header -->
                                <div class="entry-excerpt"></div>
                                <div class="entry-content fr-element fr-view">
                                    @Html.Raw(Model.HtmlBody)
                                    <div class="tag-block">
                                        @foreach (var item in Model.Tags)
                                        {
                                            <div class="tag">
                                                <a href="/news/tag/@item">@item</a>
                                            </div>
                                        }
                                    </div>
                                    </div>
                                </div>
                                <!-- .entry-content -->
                                <footer class="entry-footer">
                                    <hr>
                                    <div class="row">
                                        <div class="col-md-6 cattegories">
                                            <span class="cat-links">
                                                <i class="fa fa-folder-open"></i>
                                            </span>
                                        </div>
                                        <div class="col-md-6 tags">
                                            @*<span class="tags-links">
                                                <i class="fa fa-tags"></i>
                                                <a href="/tag/architect/" rel="tag">architect</a>
                                                ,
                                                <a href="/tag/architecture/" rel="tag">Architecture</a>
                                                ,
                                                <a href="/tag/bim/" rel="tag">BIM</a>
                                                ,
                                                <a href="/tag/building-information-modeling/" rel="tag">Building Information Modeling</a>
                                                ,
                                                <a href="/tag/design/" rel="tag">Design</a>
                                                ,
                                                <a href="/tag/designer/" rel="tag">designer</a>
                                                ,
                                                <a href="/tag/roofing/" rel="tag">Roofing</a>
                                            </span>*@
                                        </div>
                                    </div>
                                </footer>
                                <!-- .entry-footer -->
                            </article>
                            <!-- #post-## -->
                        </main>
                        <!-- #main -->
                        <div class="post-navigation">
                            <nav class="navigation" role="navigation">
                                <h2 class="screen-reader-text">Post navigation</h2>
                                <div class="nav-links">
                                    <div class="row">
                                        <!-- Get Next Post -->
                                        <div class="col-md-6">
                                            <p></p>
                                        </div>
                                        <!-- Get Previous Post -->
                                        <div class="col-md-6 prev-post">
                                            <a class="" href="/">
                                                <span class="next-prev-text">
                                                    PREVIOUS
                                                    <i class="fa fa-angle-right"></i>
                                                </span>
                                                <br>Anguleris Founders Named to BCS50
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <!-- .nav-links -->
                            </nav>
                            <!-- .navigation-->
                        </div>
                    <!-- #primary -->

                    @if (Model.OtherNews.Any())
                    {
                        <div id="secondary" class="col-md-3 sidebar widget-area" role="complementary">
                            <aside id="recent-posts-2" class="widget widget_recent_entries">
                                <h4 class="widget-title">OTHER NEWS</h4>
                                <ul>
                                    @foreach (var item in Model.OtherNews)
                                    {
                                        <li>
                                            <a href="/@item.VanityId">@item.Title</a>
                                        </li>
                                    }
                                </ul>
                            </aside>
                            <a class="btn pt-cv-readmore" href="/">view all</a>
                        </div>
                    }

                    <!-- #secondary .widget-area -->
                    </div>
                </div>
                <!--.row-->
            </div>
            <!--.container-->
        </div>
        <!-- #content -->
        <footer id="colophon" class="site-footer" role="contentinfo">
            <div class="row site-info">
                <div class="brands-block">
                    <p>Chosen by: </p>
                    <ul>
                        <li>
                            <a href="http://market.bimsmith.com/Canon">
                                <img src="images/brand-img01.png" alt="">
                            </a>
                        </li>
                        <li>
                            <a href="http://market.bimsmith.com/SherwinWilliams">
                                <img src="images/brand-img02.png" alt="">
                            </a>
                        </li>
                        <li>
                            <a href="http://market.bimsmith.com/Moen">
                                <img src="images/brand-img03.png" alt="">
                            </a>
                        </li>
                        <li>
                            <a href="http://market.bimsmith.com/Dupont-na">
                                <img src="images/brand-img05.png" alt="">
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="footer-holder">
                    <footer class="footer">
                        <div class="info">
                            <a class="logo" href="/@Model.VanityId">
                                <img src="/images/logo-footer.png" alt="">
                            </a>
                            <div class="address">
                                <span class="white">BIMsmith Headquarters</span>
                                <span>68 S. Grove Avenue, Elgin, IL 60120</span>
                            </div>
                            <div class="contacts">
                                <div>
                                    <a class="tel" href="tel:+12244848896">+1 (224) 484 - 8896</a>
                                </div>
                                <div>
                                    <div>
                                        <a class="mail" href="mailto:<EMAIL>"><EMAIL></a>
                                    </div>
                                    <div>
                                        <a href="mailto:<EMAIL>"><EMAIL></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="links">
                            <div class="col">
                                <p>Products</p>
                                <ul>
                                    <li>
                                        <a href="@ConfigurationHelper.GetValue("MarketUrl")/">Market</a>
                                    </li>
                                    <li>
                                        <a href="@ConfigurationHelper.GetValue("ForgeUrl")/">Forge</a>
                                    </li>
                                    <li>
                                        <a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/">MyBIMsmith</a>
                                    </li>
                                    <li>
                                        <a href="https://anguleris.com/bim-strategy/bim-content-creation">BIM Content Creation</a>
                                    </li>
                                </ul>
                            </div>
                            <div class="col">
                                <p>Community</p>
                                <ul>
                                    <li>
                                        <a href="@ConfigurationHelper.GetValue("NewsroomUrl")/">Newsroom</a>
                                    </li>
                                    <li>
                                        <a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/Contact">Support</a>
                                    </li>
                                </ul>
                            </div>
                            <div class="col">
                                <p>Legal</p>
                                <ul>
                                    <li><a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/legal/terms-and-conditions">Terms and conditions</a></li>
                                    <li><a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/legal/privacy-policy">Privacy Policy</a></li>
                                    <li><a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/legal/patents-and-intellectual-property">Patents and Intellectual Property</a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="f-bot">
                            <ul class="social">
                                <li>
                                    <a href="https://www.facebook.com/thebimsmith/">
                                        <img src="images/soc-fb.png" alt="">
                                    </a>
                                </li>
                                <li>
                                    <a href="https://twitter.com/thebimsmith">
                                        <img src="images/soc-tw.png" alt="">
                                    </a>
                                </li>
                                <li>
                                    <a href="https://www.youtube.com/channel/UCAPJyryHrdzN5nTxYSvFqNQ">
                                        <img src="images/soc-youtube.png" alt="">
                                    </a>
                                </li>
                                <li>
                                    <a href="https://www.linkedin.com/company/10901027?trk=tyah&amp;trkInfo=clickedVertical%3Ashowcase%2CclickedEntityId%3A10901027%2Cidx%3A1-1-1%2CtarId%3A1478971756516%2Ctas%3ABIMsmi">
                                        <img src="images/soc-in.png" alt="">
                                    </a>
                                </li>
                                <li>
                                    <a href="https://www.pinterest.com/thebimsmith/">
                                        <img src="images/soc-p.png" alt="">
                                    </a>
                                </li>
                            </ul>
                            <div class="copy">© @(DateTime.UtcNow.Year) Anguleris Technologies</div>
                        </div>
                    </footer>
                </div>
            </div>

            <!-- .site-info -->
        </footer>
        <!-- #colophon -->
    </div>

    <!-- #page -->
    <script src="~/Bundles/jquerybundle.min.js"></script>
    <script src="~/Bundles/bimsmithbundle.min.js"></script>
    <script src="~/Bundles/bootstrapjsbundle.min.js"></script>

</body>
</html>
