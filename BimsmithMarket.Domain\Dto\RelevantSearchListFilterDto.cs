﻿using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Dto
{
    public class RelevantSearchListFilterDto
    {
        [Required]
        public string Query { get; set; }

        public int? ManufacturerId { get; set; } = null;

        public int Count { get; set; } = 5;

        public bool Published { get; set; } = false;

        public bool PublishedOnCustomMicrosite { get; set; } = false;
    }
}
