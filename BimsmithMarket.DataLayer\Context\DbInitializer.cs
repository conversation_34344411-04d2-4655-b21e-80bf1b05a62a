﻿using BIMsmithMarket.Domain.DBModels;
using System;
using System.Collections.Generic;

namespace BIMsmithMarket.DataLayer.Context
{
    /// <summary>
    /// Initializer just for testing
    /// </summary>
    public static class DbInitializer
    {
        public static void Initialize(ApplicationDbContext context)
        {
            context.Database.EnsureCreated();

            #region UserRoles

            ApplicationRole adminUserRole = new ApplicationRole();
            adminUserRole.Id = Guid.NewGuid().ToString();
            adminUserRole.Name = DbConstants.AdminRole;

            ApplicationRole customerUserRole = new ApplicationRole();
            customerUserRole.Id = Guid.NewGuid().ToString();
            customerUserRole.Name = DbConstants.CustomerRole;

            context.Roles.Add(adminUserRole);
            context.Roles.Add(customerUserRole);

            context.SaveChanges();

            #endregion

            #region Application Users
            var userSystem = new ApplicationUser
            {
                Id = Guid.NewGuid().ToString(),
                UserName = "System_User",
                Email = "<EMAIL>",
                EmailConfirmed = true,
                FirstName = "System",
                LastName = "User",
                PasswordHash = "--",
                SecurityStamp = Guid.NewGuid().ToString(),
                LockoutEnabled = true,
                CreatedDate = DateTime.UtcNow
            };

            var userAdmin = new ApplicationUser
            {
                Id = DbConstants.AdminUserId,
                UserName = DbConstants.AdminRole,
                Email = "<EMAIL>",
                EmailConfirmed = true,
                FirstName = DbConstants.AdminRole,
                LastName = "Advisr",
                PasswordHash = "Qa123456+",
                SecurityStamp = Guid.NewGuid().ToString(),
                LockoutEnabled = true,
                CreatedDate = DateTime.UtcNow
            };

            var userTestCustomer = new ApplicationUser
            {
                Id = Guid.NewGuid().ToString(),
                UserName = DbConstants.CustomerRole,
                Email = "<EMAIL>",
                FirstName = "Test",
                LastName = DbConstants.CustomerRole,
                PasswordHash = "Qa123456+",
                SecurityStamp = Guid.NewGuid().ToString(),
                LockoutEnabled = true,
                CreatedDate = DateTime.UtcNow
            };

            context.Users.Add(userSystem);
            context.Users.Add(userAdmin);
            context.Users.Add(userTestCustomer);

            context.SaveChanges();

            ApplicationUserRole userRoleAdmin = new ApplicationUserRole();
            userRoleAdmin.RoleId = adminUserRole.Id;
            userRoleAdmin.UserId = userAdmin.Id;

            ApplicationUserRole userRoleCustomer = new ApplicationUserRole();
            userRoleCustomer.RoleId = customerUserRole.Id;
            userRoleCustomer.UserId = userTestCustomer.Id;

            var userRoleSet = context.Set<ApplicationUserRole>();
            userRoleSet.Add(userRoleAdmin);
            userRoleSet.Add(userRoleCustomer);

            context.SaveChanges();

            #endregion

            #region Manufacturers
            List<Manufacturer> manufacturers = new List<Manufacturer>();
            for (int i = 1; i < 4; i++)
            {
                Manufacturer m1 = new Manufacturer();
                m1.Name = string.Concat("Manufacturer ", i.ToString());
                m1.Description = "";
                m1.Site = "www.manufacturer_" + i.ToString() + ".com";
                m1.CreatedById = userSystem.Id;
                m1.CreatedDate = DateTime.UtcNow;

                manufacturers.Add(m1);
            }

            context.Manufacturers.AddRange(manufacturers);
            context.SaveChanges();

            #endregion

            #region Category
            List<Category> categories = new List<Category>();

            List<string> catNameList = new List<string> {
                            "Mailbox",
                            "Plumbing",
            };

            foreach (var name in catNameList)
            {
                Category cat = new Category();
                cat.Name = name;
                cat.ParentCategoryId = null;
                cat.CreatedById = userSystem.Id;
                cat.CreatedDate = DateTime.UtcNow;

                categories.Add(cat);
            }

            context.Categories.AddRange(categories);
            context.SaveChanges();

            #endregion

            #region Products
            List<Product> products = new List<Product>();

            List<string> productNames = new List<string> {
                            "4CFL Max Height",
                            "4CFL 15 High",
                            "4CFL 11 High",
                            "4CFL 8 High",
                            "1570 - 12",
                            "1570 - 16",
                            "1570 - 4T5",
                            "1570 - 8T6",
                            "Mini Storage Lockers-Recessed Mount",
                            "Mini Storage Lockers-Surface Mount",
                            "12503HA",
                            "12504SMSHA"
            };


            foreach (var name in productNames)
            {
                Product product = new Product();
                product.Name = name;
                product.Description = "Some long description";
                product.CategoryId = categories[0].Id;
                product.ManufacturerId = manufacturers[0].Id;
                product.PhotoId = null;
                product.VideoUrl = "https://www.youtube.com/watch?v=biJs8z2FTzM";
                product.CreatedById = userSystem.Id;
                product.CreatedDate = DateTime.UtcNow;

                products.Add(product);
            }

            context.Products.AddRange(products);
            context.SaveChanges();


            #endregion

        }
    }
}