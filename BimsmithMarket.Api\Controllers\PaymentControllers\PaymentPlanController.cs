﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Dto.PaymentsDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;


namespace BIMsmithMarket.Api.Controllers.PaymentControllers
{
#if !DEBUG
    [Authorize(Roles = DbConstants.AdminRole)]
#endif
    [Route("api/[controller]/[action]/{id?}")]
    public class PaymentPlanController : BaseApiController
    {
        private readonly IPaymentPlanService _paymentPlanService;

        public PaymentPlanController(IPaymentPlanService paymentPlanService)
        {
            _paymentPlanService = paymentPlanService;
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] PaymentPlanDto model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var result = await _paymentPlanService.CreateAsync(model, unitOfWork);

                CacheHelper.ClearSpecificCache("*/api/PaymentPlan/List*");

                return Ok(result);
            }
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody] PaymentPlanDto model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var result = await _paymentPlanService.UpdateAsync(model, unitOfWork);

                CacheHelper.ClearSpecificCache("*/api/PaymentPlan/List*");

                return Ok(result);
            }
        }

        [HttpDelete]
        public async Task<IActionResult> Delete(int id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                await _paymentPlanService.DeleteAsync(id, unitOfWork);

                CacheHelper.ClearSpecificCache("*/api/PaymentPlan/List*");

                return Ok(new { success = true });
            }
        }

        [HttpGet]
        public async Task<IActionResult> Get(int id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var paymentPlan = await _paymentPlanService.GetAsync(id, unitOfWork);
                return Ok(paymentPlan);
            }
        }

        [HttpGet]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> List()
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var paymentPlan = await _paymentPlanService.ListAsync(unitOfWork);
                return Ok(paymentPlan);
            }
        }
    }
}