﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Dto.HealthDashboardDto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// Controller for checking health
    /// </summary>
    [Route("api/[controller]/[action]")]
    public class HealthDashboardController : BaseApiController
    {
        private readonly IHealthDashboardService _healthDashboardService;
        private readonly IHealthDashboardExcelService _healthDashboardExcelService;

        public HealthDashboardController(
            IHealthDashboardService healthDashboardService,
            IHealthDashboardExcelService healthDashboardExcelService)
        {
            _healthDashboardService = healthDashboardService;
            _healthDashboardExcelService = healthDashboardExcelService;
        }

        /// <summary>
        /// Manages user access to health dashboard
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        public async Task<IActionResult> ManageAccess(HealthDashboardManageAccessDto model)
        {
            string creatorUserId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            await _healthDashboardService.ManageDashboardAccessAsync(model, creatorUserId);
            CacheHelper.ClearSpecificCache("*/api/HealthDashboard*");
            return Ok();
        }

#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        /// <summary>
        /// Returns assigned user list for specified level
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> AssignedUserList(HealthDashboardFilterDto model, CancellationToken cancellationToken = default)
        {
            return Ok(await _healthDashboardService.AssignedUserListAsync(model, cancellationToken));
        }

        /// <summary>
        /// Returns all market dashboard
        /// </summary>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> GetAllMarketDashboard(CancellationToken cancellationToken = default)
        {
            return Ok(await _healthDashboardService.GetAllMarketDashboardAsync(cancellationToken));
        }

        /// <summary>
        /// Checks user access and returns specified dashboard
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        [WebApiOutputCache(CacheConstants.ServerExpiration, true)]
        public async Task<IActionResult> GetDashboard(HealthDashboardFilterDto model, CancellationToken cancellationToken = default)
        {
            return Ok(await _healthDashboardService.GetDashboardAsync(model, cancellationToken));
        }

        /// <summary>
        /// Checks user access and returns all dashboards for selected entity
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        [WebApiOutputCache(CacheConstants.ServerExpiration, true)]
        public async Task<IActionResult> GetAllTypeDashboard(HealthDashboardFilterDto model, CancellationToken cancellationToken = default)
        {
            return Ok(await _healthDashboardService.GetAllTypeDashboardAsync(model, cancellationToken));
        }

        /// <summary>
        /// Checks user access and returns all assigned dashboards
        /// </summary>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        [WebApiOutputCache(CacheConstants.ServerExpiration, true)]
        public async Task<IActionResult> GetUserDashboards(CancellationToken cancellationToken = default, HealthDashboardFilterPeriod filterPeriod = HealthDashboardFilterPeriod.AllTime)
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            var result = await _healthDashboardService.GetUserDashboardsAsync(filterPeriod, userId, cancellationToken);
            return Ok(result);
        }

        /// <summary>
        /// Checks user access and returns specified dashboard
        /// </summary>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        [WebApiOutputCache(CacheConstants.ServerExpiration, true)]
        public async Task<IActionResult> GetUserDashboard(HealthDashboardFilterDto model, CancellationToken cancellationToken = default)
        {
            var result = await _healthDashboardService.GetUserDashboardAsync(model, cancellationToken);
            return Ok(result);
        }

        /// <summary>
        /// Checks user access and returns all assigned dashboards list
        /// </summary>
        /// <param name="viewType">The view type</param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        [WebApiOutputCache(CacheConstants.ServerExpiration, true)]
        public async Task<IActionResult> GetUserDashboardList(HealthDashboardViewType viewType, CancellationToken cancellationToken = default)
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _healthDashboardService.GetUserDashboardWithNameListAsync(viewType, userId, unitOfWork, cancellationToken));
        }

        /// <summary>
        /// Generates report with broken list for specified level
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        public async Task<IActionResult> GetBrokenLinksExcel(HealthDashboardFilterDto model, CancellationToken cancellationToken = default)
        {
            var excelPath = await _healthDashboardExcelService.GetBrokenLinksExcelAsync(model, cancellationToken);
            return PhysicalFile(excelPath, "text/csv", "Broken Links Report.xlsx");
        }

        /// <summary>
        /// Generates report for specified level
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        public async Task<IActionResult> GetDashboardExcel(HealthDashboardFilterDto model, CancellationToken cancellationToken = default)
        {
            var excelPath = await _healthDashboardExcelService.GetDashboardExcelAsync(model, cancellationToken);
            return PhysicalFile(excelPath, "text/csv", "Dashboard Report.xlsx");
        }

        /// <summary>
        /// Replaces broken links for files specified
        /// </summary>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        public async Task<IActionResult> RepairBrokenLinks(CancellationToken cancellationToken = default)
        {
            if (!(Request.HasFormContentType && Request.Form.Files.Any()))
                return BadRequest("Mime Multipart Content missing");

            var result = await _healthDashboardExcelService.RepairBrokenLinksAsync(Request.Form.Files[0], cancellationToken);

            CacheHelper.ClearSpecificCache("*/api/HealthDashboard*");

            return Ok(result);
        }

        /// <summary>
        /// Replaces broken links for files specified
        /// </summary>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        public async Task<IActionResult> ImportExcel(int dashboardType, CancellationToken cancellationToken = default)
        {
            if (!(Request.HasFormContentType && Request.Form.Files.Any()))
                return BadRequest("Mime Multipart Content missing");

            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            var result = await _healthDashboardExcelService.RepairProductsAsync(Request.Form.Files[0], (Domain.Enums.HealthDashboardType)dashboardType, userId, cancellationToken);

            CacheHelper.ClearSpecificCache("*/api/HealthDashboard*");

            return Ok(result);
        }
    }
}