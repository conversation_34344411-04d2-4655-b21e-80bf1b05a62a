﻿using System.Collections.Generic;

namespace BIMsmithMarket.Services.Search.FullTextSearch
{
    public enum QueryTokenType
    {
        Unknown = 0,
        Category = 1,
        Unit = 2,
        Noise = 3,
        Manufacturer = 4,
        ProjectType = 5,
        ModelNumber = 6,
        Masterformat = 7,
        Omniclass = 8,
        Stopword = 9,
        Detail = 10
    }

    public class QueryToken
    {
        public static readonly char[] _invalidCharacters = new char[] { '[', ']', '(', ')', '!', ':', '?', ',', ';', '*', '\'', '\"', '~' };

        public QueryTokenType Type { get; set; }
        public string Original { get; set; }
        public string Corrected { get; set; }
        public string FTS { get; set; }
        public bool HasResults { get; set; }
        public int No { get; set; }
        public HashSet<int> Matches { get; set; }

        public QueryToken(string token, int no, QueryTokenType type = QueryTokenType.Unknown)
        {
            Original = token.Trim().ToLower();
            Corrected = Original;
            Type = type;
            No = no;
            HasResults = false;
            Matches = new HashSet<int>();
        }

        public static QueryToken DummyFTS(string token)
        {
            return new QueryToken(token, -1, QueryTokenType.Unknown) { FTS = token };
        }

        public bool HasCorrection => Original.ToLower().Trim() != Corrected.ToLower().Trim();

        /// <summary>
        /// Trim string if it exceeds certain length
        /// </summary>
        /// <returns></returns>
        public static string Trim(string s, int length)
        {
            return s != null && s.Length > length ? s.Substring(0, length) : s;
        }

        public static string NormalizeToken(string token)
        {
            return Trim(string.IsNullOrWhiteSpace(token) ? token : token.Trim(_invalidCharacters).Replace("\"", ""), 200);
        }
    }
}
