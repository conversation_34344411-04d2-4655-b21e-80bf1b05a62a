﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto.DetailDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Helpers;
using BIMsmithMarket.Services.Interfaces;
using BIMsmithMarket.Services.Search;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;

using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Services
{
    public class DetailService : IDetailService
    {
        private readonly IMasterformatService _masterformatService;

        public DetailService(IMasterformatService masterformatService)
        {
            _masterformatService = masterformatService;
        }

        public async Task<dynamic> AddAsync(AddDetailDto model, string userId)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                unitOfWork.BeginTransaction();
                var detailId = await AddDetailAsync(model, userId, unitOfWork);
                unitOfWork.CommitTransaction();
                CacheHelper.ClearSpecificCache("*/api/Detail/List*");

                var dbDetail = await GetAsync(detailId);
                return dbDetail;
            }
        }

        public async Task<int> AddDetailAsync(
            AddDetailDto model,
            string userId,
            IUnitOfWork unitOfWork,
            bool isRestoredFromBackup = false,
            int? detailId = null)
        {
            var detail = model.Adapt<Detail>();
            detail.CreatedById = userId;
            detail.CreatedDate = DateTime.UtcNow;

            if (isRestoredFromBackup)
            {
                detail.Id = detailId.Value;
                var setIdentityOnQuery = SqlHelper.GenerateSetIdentityOnQuery(DbConstants.DetailsTableName);
                var insertQuery = SqlHelper.GenerateInsertQuery(detail, DbConstants.DetailsTableName);
                var setIndentityOffQuery = SqlHelper.GenerateSetIdentityOffQuery(DbConstants.DetailsTableName);
                var query = string.Join(";", setIdentityOnQuery, insertQuery.Query, setIndentityOffQuery);
                await unitOfWork.CurrentDbContext.Database.ExecuteSqlRawAsync(query, insertQuery.Parameters);
            }
            else
            {
                unitOfWork.DetailRepository.Insert(detail);
            }
            await unitOfWork.SaveAsync();

            await AddOrUpdateDetailApplicationsAsync(model, userId, unitOfWork, detail);
            await AddOrUpdateDetailMasterformatsAsync(model, userId, unitOfWork, detail);
            await AddOrUpdateDetailPhotosAsync(model, userId, unitOfWork, detail);
            await AddOrUpdateDetailFilesAsync(model, userId, unitOfWork, detail);
            await AddOrUpdateDetailProductsAsync(model, userId, unitOfWork, detail);
            await AddOrUpdateRelatedDetailsAsync(model, userId, unitOfWork, detail);

            await unitOfWork.SaveAsync();

            return detail.Id;
        }

        public async Task<int> EditDetailAsync(EditDetailDto model, string userId, IUnitOfWork unitOfWork)
        {
            var dbDetail = await unitOfWork.DetailRepository.GetByIdAsync(model.Id);
            if (dbDetail == null)
                throw new InvalidInputException("Detail not found");

            model.Adapt(dbDetail);
            dbDetail.ModifiedById = userId;
            dbDetail.ModifiedDate = DateTime.UtcNow;
            unitOfWork.DetailRepository.Edit(dbDetail);

            await unitOfWork.SaveAsync();

            await AddOrUpdateDetailApplicationsAsync(model, userId, unitOfWork, dbDetail);
            await AddOrUpdateDetailMasterformatsAsync(model, userId, unitOfWork, dbDetail);
            await AddOrUpdateDetailPhotosAsync(model, userId, unitOfWork, dbDetail);
            await AddOrUpdateDetailFilesAsync(model, userId, unitOfWork, dbDetail);
            await AddOrUpdateDetailProductsAsync(model, userId, unitOfWork, dbDetail);
            await AddOrUpdateRelatedDetailsAsync(model, userId, unitOfWork, dbDetail);

            await unitOfWork.SaveAsync();

            return dbDetail.Id;
        }

        public async Task<dynamic> EditAsync(EditDetailDto model, string userId)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                unitOfWork.BeginTransaction();
                var detailId = await EditDetailAsync(model, userId, unitOfWork);
                unitOfWork.CommitTransaction();
                CacheHelper.ClearSpecificCache("*/api/Detail/List*");

                return await GetAsync(detailId);
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                unitOfWork.BeginTransaction();

                var result = await DeleteDetailAsync(id, unitOfWork);

                unitOfWork.CommitTransaction();
                CacheHelper.ClearSpecificCache("*/api/Detail/List*");
                return result;
            }
        }

        public async Task<bool> DeleteDetailAsync(int id, IUnitOfWork unitOfWork)
        {
            var dbDetail = unitOfWork.DetailRepository.GetById(id);
            if (dbDetail == null)
                throw new InvalidInputException("Detail not found");

            var detailApplications = dbDetail.DetailDetailApplications.ToList();
            detailApplications.ForEach(x => unitOfWork.DetailDetailApplicationRepository.Delete(x));
            var detailMasterformats = dbDetail.DetailMasterformats.ToList();
            detailMasterformats.ForEach(x => unitOfWork.DetailMasterformatRepository.Delete(x));
            var detailRatings = dbDetail.DetailRatings.ToList();
            detailRatings.ForEach(x => unitOfWork.DetailRatingRepository.Delete(x));
            var detailPhotos = dbDetail.DetailPhotos.ToList();
            detailPhotos.ForEach(x => unitOfWork.DetailPhotoRepository.Delete(x));
            var detailFiles = dbDetail.DetailFiles.ToList();
            detailFiles.ForEach(x => unitOfWork.DetailFileRepository.Delete(x));
            var detailProducts = dbDetail.ProductDetails.ToList();
            detailProducts.ForEach(x => unitOfWork.ProductDetailRepository.Delete(x));
            var relatedDetails = unitOfWork.RelatedDetailRepository.GetAll().Where(x => x.RelatedDetailId == dbDetail.Id || x.DetailId == dbDetail.Id).ToList();
            relatedDetails.ForEach(x => unitOfWork.RelatedDetailRepository.Delete(x));

            unitOfWork.DetailRepository.Delete(dbDetail);
            await unitOfWork.SaveAsync();

            return true;
        }

        public async Task<dynamic> GetAsync(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var relatedDetails = unitOfWork.RelatedDetailRepository.GetAll().Where(x => x.DetailId == id || x.RelatedDetailId == id);

                var detail = await unitOfWork.DetailRepository.GetAll().Where(x => x.Id == id).Select(x => new
                {
                    id = x.Id,
                    name = x.Name,
                    createdDate = x.CreatedDate,
                    modifiedDate = x.ModifiedDate,
                    description = x.Description,
                    exterior = x.Exterior,
                    interior = x.Interior,
                    orientation = x.Orientation,
                    regionIds = x.RegionIds,
                    stateIds = x.StateIds,
                    detailScaleId = x.DetailScaleId,
                    detailScale = x.DetailScaleId != null ? new
                    {
                        id = x.DetailScale.Id,
                        name = x.DetailScale.Name,
                    } : null,
                    published = x.Published,
                    manufacturerId = x.ManufacturerId,
                    manufacturer = new
                    {
                        id = x.ManufacturerId,
                        name = x.Manufacturer.Name,
                        site = x.Manufacturer.Site,
                        swatchboxManufacturerId = x.Manufacturer.SwatchboxManufacturerId,
                        videoUrl = x.Manufacturer.VideoUrl,
                        nodeSetting = x.Manufacturer.NodeSetting,
                        letsTalkSettings = x.Manufacturer.LetsTalkSettings,
                        logo = new
                        {
                            id = x.Manufacturer.PhotoId,
                            small = x.Manufacturer.PhotoId != null ? x.Manufacturer.Photo.SmallImgUrl : null,
                            original = x.Manufacturer.PhotoId != null ? x.Manufacturer.Photo.OriginalImgUrl : null,
                        }
                    },
                    photoId = x.PhotoId,
                    photo = new
                    {
                        photoId = x.PhotoId,
                        uploadUrl = x.PhotoId != null ? x.Photo.UploadUrl : null,
                        small = x.PhotoId != null ? x.Photo.SmallImgUrl : null,
                        middle = x.PhotoId != null ? x.Photo.OriginalImgUrl.Replace("_b", "_m") : null,
                        big = x.PhotoId != null ? x.Photo.OriginalImgUrl : null,
                    },
                    productRating = x.ProductRating,
                    productRatingCount = x.DetailRatings.Where(b => b.Type == DetailRatingType.ProductRating).Count(),
                    contentRating = x.ContentRating,
                    contentRatingCount = x.DetailRatings.Where(b => b.Type == DetailRatingType.ContentRating).Count(),
                    applications = x.DetailDetailApplications.Select(a => new
                    {
                        id = a.DetailApplicationId,
                        name = a.DetailApplication.Name
                    }).ToList(),
                    externalMasterformatIds = x.DetailMasterformats.Select(s => s.ExternalMasterformatId).ToList(),
                    photos = x.DetailPhotos.Select(p => new
                    {
                        photoId = p.PhotoId,
                        uploadUrl = p.Photo.UploadUrl,
                        small = p.Photo.SmallImgUrl,
                        middle = p.Photo.OriginalImgUrl.Replace("_b", "_m"),
                        big = p.Photo.OriginalImgUrl,
                        isMainPhoto = p.PhotoId == x.PhotoId
                    })
                    .OrderByDescending(p => p.isMainPhoto)
                    .ToList(),
                    projectFiles = x.DetailFiles.Select(f => new
                    {
                        fileId = f.FileId,
                        regionIds = f.RegionIds,
                        stateIds = f.StateIds,
                        softwareRelease = f.SoftwareRelease,
                        contentCreatedby = f.ContentCreatedby,
                        contentCheckedBy = f.ContentCheckedBy,
                        fileVersion = f.FileVersion,
                        projectDataType = new
                        {
                            id = f.ProjectDataTypeId,
                            title = f.ProjectDataType.Title,
                            header = f.ProjectDataType.Header
                        },
                        projectDataTypeId = f.ProjectDataTypeId,
                        title = f.File.Title,
                        fileName = f.File.FileName,
                        fileSize = f.File.FileSize,
                        mimeType = f.File.MediaType,
                        updatesCount = f.File.UpdatesCount,
                        fileSyncStatusCode = f.File.SyncStatusCode,
                        fileSyncUrl = f.File.SyncUrl,
                        url = f.File.Url,
                        preview = f.File.PreviewUrl,
                    }).ToList(),
                    products = x.ProductDetails.Select(p => new
                    {
                        id = p.ProductId,
                        name = p.Product.Name,
                        photo = new
                        {
                            photoId = p.Product.PhotoId,
                            uploadUrl = p.Product.PhotoId != null ? p.Product.Photo.UploadUrl : null,
                            small = p.Product.PhotoId != null ? p.Product.Photo.SmallImgUrl : null,
                            middle = p.Product.PhotoId != null ? p.Product.Photo.OriginalImgUrl.Replace("_b", "_m") : null,
                            big = p.Product.PhotoId != null ? p.Product.Photo.OriginalImgUrl : null,
                        },
                    }).ToList(),
                    relatedDetails = relatedDetails
                    .Select(d => new
                    {
                        id = d.DetailId == x.Id ? d.RelatedDetailItem.Id : d.Detail.Id,
                        name = d.DetailId == x.Id ? d.RelatedDetailItem.Name : d.Detail.Name,
                        manufacturerId = d.DetailId == x.Id ? d.RelatedDetailItem.ManufacturerId : d.Detail.ManufacturerId,
                        manufacturer = new
                        {
                            id = d.DetailId == x.Id ? d.RelatedDetailItem.ManufacturerId : d.Detail.ManufacturerId,
                            name = d.DetailId == x.Id ? d.RelatedDetailItem.Manufacturer.Name : d.Detail.Manufacturer.Name,
                            site = d.DetailId == x.Id ? d.RelatedDetailItem.Manufacturer.Site : d.Detail.Manufacturer.Site
                        },
                        photoId = d.DetailId == x.Id ? d.RelatedDetailItem.PhotoId : d.Detail.PhotoId,
                        photo = new
                        {
                            photoId = d.DetailId == x.Id ? d.RelatedDetailItem.PhotoId : d.Detail.PhotoId,
                            uploadUrl = d.DetailId == x.Id ? (d.RelatedDetailItem.PhotoId != null ? d.RelatedDetailItem.Photo.UploadUrl : null) : (d.Detail.PhotoId != null ? d.Detail.Photo.UploadUrl : null),
                            small = d.DetailId == x.Id ? (d.RelatedDetailItem.PhotoId != null ? d.RelatedDetailItem.Photo.SmallImgUrl : null) : (d.Detail.PhotoId != null ? d.Detail.Photo.SmallImgUrl : null),
                            middle = d.DetailId == x.Id ? (d.RelatedDetailItem.PhotoId != null ? d.RelatedDetailItem.Photo.OriginalImgUrl.Replace("_b", "_m") : null) : (d.Detail.PhotoId != null ? d.Detail.Photo.OriginalImgUrl.Replace("_b", "_m") : null),
                            big = d.DetailId == x.Id ? (d.RelatedDetailItem.PhotoId != null ? d.RelatedDetailItem.Photo.OriginalImgUrl : null) : (d.Detail.PhotoId != null ? d.Detail.Photo.OriginalImgUrl : null),
                        },
                        published = d.DetailId == x.Id ? d.RelatedDetailItem.Published : d.Detail.Published,
                    }).ToList()
                })
                .FirstOrDefaultAsync();

                if (detail == null)
                    throw new DbItemNotFoundException("Detail Not Found");

                return detail;
            }
        }

        public async Task<dynamic> ListAsync(DetailFilterDto model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                IQueryable<Detail> detailQuery = unitOfWork.DetailRepository.GetAll();
                detailQuery = await FilterDetailsAsync(model, detailQuery, unitOfWork);

                int totalCount = await detailQuery.CountAsync();
                List<DetailListDto> data = new();

                if (model.UseRotation)
                {
                    data = await RotateDetailsAsync(model, detailQuery, totalCount);
                }
                else
                {
                    data = await detailQuery.Select(DetailListProjection())
                        .Skip(model.Offset)
                        .Take(model.Count)
                        .ToListAsync();
                }

                return new
                {
                    totalCount,
                    data
                };
            }
        }

        public async Task<dynamic> GetFiltersAsync(DetailFilterDto model)
        {
            using (var unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var detailQuery = unitOfWork.DetailRepository.GetAll();
                detailQuery = (await FilterDetailsAsync(model, detailQuery, unitOfWork))
                    .OrderBy(x => x.Id);

                var details = await detailQuery.AsNoTracking()
                    .Select(x => new
                    {
                        x.Id,
                        x.RegionIds,
                        x.StateIds
                    })
                    .ToListAsync();

                if (model.RegionIds != null && model.RegionIds.Any())
                    details = details.Where(x => x.RegionIds.Split('_').Any(r => model.RegionIds.Contains(r)) || string.IsNullOrWhiteSpace(x.RegionIds)).ToList();
                if (model.StateIds != null && model.StateIds.Any())
                    details = details.Where(x => x.StateIds.Split('_').Any(r => model.StateIds.Contains(r)) || string.IsNullOrWhiteSpace(x.StateIds)).ToList();

                var detailIds = details.Select(x => x.Id).ToArray();

                detailQuery = unitOfWork.DetailRepository.GetAll().Where(x => detailIds.Contains(x.Id));
                var filters = new
                {
                    projectDataTypes = detailQuery.SelectMany(x => x.DetailFiles.Select(d => new { id = d.ProjectDataTypeId, name = d.ProjectDataType != null ? d.ProjectDataType.Title : "" })).Distinct().ToList(),
                    applications = detailQuery.SelectMany(x => x.DetailDetailApplications.Select(d => new
                    {
                        id = d.DetailApplicationId,
                        name = d.DetailApplication != null ? d.DetailApplication.Name : "",
                        vanityUrl = d.DetailApplication != null ? d.DetailApplication.VanityUrl : ""
                    })).Distinct().ToList(),
                    scales = detailQuery.Where(x => x.DetailScaleId != null).Select(x => new { id = x.DetailScaleId, name = x.DetailScale.Name }).Distinct().ToList(),
                    orientations = detailQuery.Select(x => x.Orientation).Distinct().ToList().Select(x => new { id = x, name = Constants.DetailOrientationCaptions[x] }).ToList(),
                    associatedProducts = detailQuery.SelectMany(x => x.ProductDetails.Select(p => new { id = p.ProductId, name = p.Product.Name })).Distinct().ToList(),
                    masterformatIds = detailQuery.SelectMany(x => x.DetailMasterformats.Select(m => m.ExternalMasterformatId)).Distinct().ToList()
                };

                return filters;
            }
        }

        public async Task<dynamic> PostRating(PostDetailRatingDto model, string userId)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                bool needToUpdate = true;

                unitOfWork.BeginTransaction();

                var detailRating = await unitOfWork.DetailRatingRepository.GetAll().FirstOrDefaultAsync(a => a.CreatedById == userId && a.DetailId == model.DetailId && a.Type == model.Type);

                if (detailRating == null)
                {
                    detailRating = new DetailRating();
                    detailRating.DetailId = model.DetailId;
                    needToUpdate = false;
                }

                detailRating.Rating = model.Rating;
                detailRating.Type = model.Type;
                detailRating.Comment = model.Comment;
                detailRating.CreatedById = userId;
                detailRating.CreatedDate = DateTime.UtcNow;

                if (needToUpdate)
                {
                    unitOfWork.DetailRatingRepository.Edit(detailRating);
                }
                else
                {
                    unitOfWork.DetailRatingRepository.Insert(detailRating);
                }

                await unitOfWork.SaveAsync();

                var query = unitOfWork.DetailRatingRepository.GetAll()
                    .Where(a => a.DetailId == model.DetailId && a.Type == model.Type);

                int countOfRatings = await query.CountAsync();
                int sumOfRatings = await query.SumAsync(a => a.Rating);
                float newRatingValue = ((float)sumOfRatings / countOfRatings);

                var detail = await unitOfWork.DetailRepository.GetByIdAsync(model.DetailId);

                if (detail == null) throw new Exception("Detail not found");

                switch (model.Type)
                {
                    case DetailRatingType.ProductRating:
                        detail.ProductRating = newRatingValue;
                        break;
                    case DetailRatingType.ContentRating:
                        detail.ContentRating = newRatingValue;
                        break;
                    default:
                        throw new NotImplementedException();
                }

                await unitOfWork.SaveAsync();
                unitOfWork.CommitTransaction();

                var result = new
                {
                    detailId = model.DetailId,
                    productRating = detail.ProductRating,
                    contentRating = detail.ContentRating,
                };

                return result;
            }
        }

        public async Task<bool> PostContentComment(DetailContentCommentDto model, string userId, string hostUrl, string emailsPath)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var user = await unitOfWork.UserRepository.GetAll().Where(x => x.Id == userId).FirstOrDefaultAsync();
                var detail = await unitOfWork.DetailRepository.GetByIdAsync(model.DetailId);
                var detailLink = hostUrl + "/product/" + model.DetailId;
                var manufacturerLink = hostUrl + "/manufacturer/" + detail?.ManufacturerId;

                await EmailNotificationHelper.Create(emailsPath).SendDetailContentCommentEmail(
                    model.Comment,
                    model.Rating,
                    detail?.Name,
                    detailLink,
                    detail?.Manufacturer?.Name,
                    manufacturerLink,
                    user?.FirstName,
                    user?.LastName,
                    user?.Email);
                ;
            }
            return true;
        }

        #region private methods
        private async Task AddOrUpdateDetailApplicationsAsync(AddDetailDto model, string userId, IUnitOfWork unitOfWork, Detail detail)
        {
            model.ApplicationIds = model.ApplicationIds ?? new List<int>();
            var existingApplicationIds = await unitOfWork.DetailDetailApplicationRepository.GetAll()
                .Where(x => x.DetailId == detail.Id).Select(x => x.DetailApplicationId)
                .ToListAsync();
            var applicationToAddIds = model.ApplicationIds.Except(existingApplicationIds);
            var applicationToDeleteIds = existingApplicationIds.Except(model.ApplicationIds);
            if (applicationToAddIds.Any())
            {
                foreach (var applicationId in applicationToAddIds.ToList())
                {
                    var dbApplication = await unitOfWork.DetailApplicationRepository.GetByIdAsync(applicationId);
                    if (dbApplication != null)
                    {
                        var detailApplicationRelation = new DetailDetailApplication();
                        detailApplicationRelation.CreatedById = userId;
                        detailApplicationRelation.CreatedDate = DateTime.UtcNow;
                        detailApplicationRelation.DetailApplicationId = dbApplication.Id;
                        detailApplicationRelation.DetailId = detail.Id;
                        unitOfWork.DetailDetailApplicationRepository.Insert(detailApplicationRelation);
                    }
                }
            }
            if (applicationToDeleteIds.Any())
            {
                var detailApplicationToDelete = await unitOfWork.DetailDetailApplicationRepository.GetAll()
                    .Where(x => applicationToDeleteIds.ToList().Contains(x.DetailApplicationId) && x.DetailId == detail.Id)
                    .ToListAsync();
                detailApplicationToDelete.ForEach(x => unitOfWork.DetailDetailApplicationRepository.Delete(x));
            }
        }

        private async Task AddOrUpdateDetailMasterformatsAsync(AddDetailDto model, string userId, IUnitOfWork unitOfWork, Detail detail)
        {
            model.ExternalMasteformatIds = model.ExternalMasteformatIds ?? new List<int>();
            var existingMasterformatIds = await unitOfWork.DetailMasterformatRepository.GetAll()
                .Where(x => x.DetailId == detail.Id).Select(x => x.ExternalMasterformatId)
                .ToListAsync();
            var masteformatToAddIds = model.ExternalMasteformatIds.Except(existingMasterformatIds);
            var masterformatToDeleteIds = existingMasterformatIds.Except(model.ExternalMasteformatIds);
            if (masteformatToAddIds.Any())
            {
                foreach (var masterformatId in masteformatToAddIds.ToList())
                {
                    var detailMasterformat = new DetailMasterformat();
                    detailMasterformat.CreatedById = userId;
                    detailMasterformat.CreatedDate = DateTime.UtcNow;
                    detailMasterformat.ExternalMasterformatId = masterformatId;
                    detailMasterformat.DetailId = detail.Id;
                    unitOfWork.DetailMasterformatRepository.Insert(detailMasterformat);
                }
            }
            if (masterformatToDeleteIds.Any())
            {
                var detailMasterformatToDelete = await unitOfWork.DetailMasterformatRepository.GetAll()
                    .Where(x => masterformatToDeleteIds.ToList().Contains(x.ExternalMasterformatId) && x.DetailId == detail.Id)
                    .ToListAsync();
                detailMasterformatToDelete.ForEach(x => unitOfWork.DetailMasterformatRepository.Delete(x));
            }
        }

        private async Task AddOrUpdateDetailPhotosAsync(AddDetailDto model, string userId, IUnitOfWork unitOfWork, Detail detail)
        {
            var modelPhotoIds = model.Photos?.Select(x => x.PhotoId) ?? new List<int>();
            var existingPhotoIds = await unitOfWork.DetailPhotoRepository.GetAll()
                .Where(x => x.DetailId == detail.Id).Select(x => x.PhotoId)
                .ToListAsync();
            var photoToAddIds = modelPhotoIds.Except(existingPhotoIds);
            var photoToDeleteIds = existingPhotoIds.Except(modelPhotoIds);
            if (photoToAddIds.Any())
            {
                foreach (var photoId in photoToAddIds.ToList())
                {
                    var dbPhoto = await unitOfWork.PhotoRepository.GetByIdAsync(photoId);
                    if (dbPhoto != null)
                    {
                        var detailPhoto = new DetailPhoto();
                        detailPhoto.CreatedById = userId;
                        detailPhoto.CreatedDate = DateTime.UtcNow;
                        detailPhoto.PhotoId = dbPhoto.Id;
                        detailPhoto.DetailId = detail.Id;
                        unitOfWork.DetailPhotoRepository.Insert(detailPhoto);
                    }
                }
            }
            if (photoToDeleteIds.Any())
            {
                var detailPhotoToDelete = await unitOfWork.DetailPhotoRepository.GetAll()
                    .Where(x => photoToDeleteIds.ToList().Contains(x.PhotoId) && x.DetailId == detail.Id)
                    .ToListAsync();
                detailPhotoToDelete.ForEach(x => unitOfWork.DetailPhotoRepository.Delete(x));
            }
            await unitOfWork.SaveAsync();
            detail.PhotoId = detail.DetailPhotos.Any() ? detail.DetailPhotos.First()?.PhotoId : null;
        }

        private async Task AddOrUpdateDetailFilesAsync(AddDetailDto model, string userId, IUnitOfWork unitOfWork, Detail detail)
        {
            model.ProjectFiles = model.ProjectFiles ?? new List<DetailProjectFileDto>();
            var modelFileIds = model.ProjectFiles.Select(x => x.FileId);
            var existingFileIds = await unitOfWork.DetailFileRepository.GetAll()
                .Where(x => x.DetailId == detail.Id).Select(x => x.FileId)
                .ToListAsync();
            var fileToAddIds = modelFileIds.Except(existingFileIds);
            var fileToUpdateIds = existingFileIds.Intersect(modelFileIds);
            var fileToDeleteIds = existingFileIds.Except(modelFileIds);
            if (fileToAddIds.Any())
            {
                var filesToAdd = model.ProjectFiles.Where(x => fileToAddIds.Contains(x.FileId)).ToList();
                foreach (var file in filesToAdd)
                {
                    DetailFile detailFile = file.Adapt<DetailFile>();
                    detailFile.DetailId = detail.Id;
                    detailFile.CreatedById = userId;
                    detailFile.CreatedDate = DateTime.UtcNow;
                    unitOfWork.DetailFileRepository.Insert(detailFile);

                    var dbFile = await unitOfWork.FileRepository.GetByIdAsync(file.FileId);
                    if (dbFile.Title != file.Title)
                    {
                        dbFile.Title = file.Title;
                        unitOfWork.FileRepository.Edit(dbFile);
                    }
                }
            }
            if (fileToUpdateIds.Any())
            {
                var filesToUpdate = model.ProjectFiles.Where(x => fileToUpdateIds.Contains(x.FileId)).ToList();
                foreach (var file in filesToUpdate)
                {
                    var dbDetailFile = await unitOfWork.DetailFileRepository.GetAll().FirstOrDefaultAsync(x => x.FileId == file.FileId && x.DetailId == detail.Id);
                    if (dbDetailFile != null)
                    {
                        file.Adapt(dbDetailFile);
                        dbDetailFile.ModifiedById = userId;
                        dbDetailFile.ModifiedDate = DateTime.UtcNow;
                        unitOfWork.DetailFileRepository.Edit(dbDetailFile);

                        var dbFile = await unitOfWork.FileRepository.GetByIdAsync(file.FileId);
                        if (dbFile.Title != file.Title)
                        {
                            dbFile.Title = file.Title;
                            unitOfWork.FileRepository.Edit(dbFile);
                        }
                    }
                }
            }
            if (fileToDeleteIds.Any())
            {
                var detailFilesToDelete = await unitOfWork.DetailFileRepository.GetAll()
                    .Where(x => fileToDeleteIds.ToList().Contains(x.FileId) && x.DetailId == detail.Id)
                    .ToListAsync();
                detailFilesToDelete.ForEach(x => unitOfWork.DetailFileRepository.Delete(x));
            }
        }

        private async Task AddOrUpdateDetailProductsAsync(AddDetailDto model, string userId, IUnitOfWork unitOfWork, Detail detail)
        {
            model.ProductIds = model.ProductIds ?? new List<int>();
            var existingProductIds = await unitOfWork.ProductDetailRepository.GetAll()
                .Where(x => x.DetailId == detail.Id).Select(x => x.ProductId)
                .ToListAsync();
            var productToAddIds = model.ProductIds.Except(existingProductIds);
            var productToDeleteIds = existingProductIds.Except(model.ProductIds);
            if (productToAddIds.Any())
            {
                foreach (var productId in productToAddIds.ToList())
                {
                    var dbProduct = await unitOfWork.ProductRepository.GetByIdAsync(productId);
                    if (dbProduct != null)
                    {
                        var productDetail = new ProductDetail();
                        productDetail.CreatedById = userId;
                        productDetail.CreatedDate = DateTime.UtcNow;
                        productDetail.ProductId = dbProduct.Id;
                        productDetail.DetailId = detail.Id;
                        unitOfWork.ProductDetailRepository.Insert(productDetail);
                    }
                }
            }
            if (productToDeleteIds.Any())
            {
                var productDetailsToDelete = await unitOfWork.ProductDetailRepository.GetAll()
                    .Where(x => productToDeleteIds.ToList().Contains(x.ProductId) && x.DetailId == detail.Id)
                    .ToListAsync();
                productDetailsToDelete.ForEach(x => unitOfWork.ProductDetailRepository.Delete(x));
            }
        }

        private async Task AddOrUpdateRelatedDetailsAsync(AddDetailDto model, string userId, IUnitOfWork unitOfWork, Detail detail)
        {
            model.RelatedDetailIds = model.RelatedDetailIds ?? new List<int>();
            var existingDetailIds = await unitOfWork.RelatedDetailRepository.GetAll()
                .Where(x => x.RelatedDetailId == detail.Id).Select(x => x.DetailId)
                .Union(unitOfWork.RelatedDetailRepository.GetAll()
                .Where(x => x.DetailId == detail.Id).Select(x => x.RelatedDetailId.Value))
                .ToListAsync();
            var detailToAddIds = model.RelatedDetailIds.Except(existingDetailIds);
            var detailToDeleteIds = existingDetailIds.Except(model.RelatedDetailIds);
            if (detailToAddIds.Any())
            {
                foreach (var detailId in detailToAddIds.ToList())
                {
                    var dbRelatedDetail = await unitOfWork.DetailRepository.GetByIdAsync(detailId);
                    if (dbRelatedDetail != null)
                    {
                        if (dbRelatedDetail.ManufacturerId != detail.ManufacturerId)
                            throw new Exception("Attemp to attach detail from another manufacturer!");
                        var relatedDetail = new RelatedDetail();
                        relatedDetail.CreatedById = userId;
                        relatedDetail.CreatedDate = DateTime.UtcNow;
                        relatedDetail.DetailId = detail.Id;
                        relatedDetail.RelatedDetailId = detailId;
                        unitOfWork.RelatedDetailRepository.Insert(relatedDetail);
                    }
                }
            }
            if (detailToDeleteIds.Any())
            {
                var productDetailsToDelete = await unitOfWork.RelatedDetailRepository.GetAll()
                    .Where(x => (detailToDeleteIds.ToList().Contains(x.RelatedDetailId.Value) && x.DetailId == detail.Id)
                    || (detailToDeleteIds.ToList().Contains(x.DetailId) && x.RelatedDetailId == detail.Id))
                    .ToListAsync();
                productDetailsToDelete.ForEach(x => unitOfWork.RelatedDetailRepository.Delete(x));
            }
        }

        private async Task<IQueryable<Detail>> FilterDetailsAsync(DetailFilterDto model, IQueryable<Detail> detailQuery, IUnitOfWork unitOfWork)
        {
            if (model.Published.HasValue)
                detailQuery = detailQuery.Where(x => x.Published == model.Published);

            if (model.RegionIds != null && model.RegionIds.Any())
                foreach (string regionId in model.RegionIds)
                    detailQuery = detailQuery.Where(x => x.RegionIds == regionId
                        || x.RegionIds.StartsWith(regionId + "_")
                        || x.RegionIds.EndsWith("_" + regionId)
                        || x.RegionIds.Contains("_" + regionId + "_")
                        || x.RegionIds == null
                        || x.RegionIds == string.Empty);

            if (model.StateIds != null && model.StateIds.Any())
                foreach (string stateId in model.StateIds)
                    detailQuery = detailQuery.Where(x => x.StateIds == stateId
                        || x.StateIds.StartsWith(stateId + "_")
                        || x.StateIds.EndsWith("_" + stateId)
                        || x.StateIds.Contains("_" + stateId + "_")
                        || x.StateIds == null
                        || x.StateIds == string.Empty);

            if (model.ManufacturerIds != null && model.ManufacturerIds.Any())
                detailQuery = detailQuery.Where(x => model.ManufacturerIds.Contains(x.ManufacturerId.Value));

            if (model.ProjectDataTypeIds != null && model.ProjectDataTypeIds.Any())
                detailQuery = detailQuery.Where(x => x.DetailFiles.Any(f => model.ProjectDataTypeIds.Contains(f.ProjectDataTypeId.Value)));

            if (model.Interior.HasValue)
                detailQuery = detailQuery.Where(x => x.Interior == model.Interior);

            if (model.Exterior.HasValue)
                detailQuery = detailQuery.Where(x => x.Exterior == model.Exterior);

            if (model.Orientations != null && model.Orientations.Any())
                detailQuery = detailQuery.Where(x => model.Orientations.Contains(x.Orientation));

            if (model.ApplicationIds != null && model.ApplicationIds.Any())
                detailQuery = detailQuery.Where(x => x.DetailDetailApplications.Any(f => model.ApplicationIds.Contains(f.DetailApplicationId)));

            if (model.AssociatedProductIds != null && model.AssociatedProductIds.Any())
                detailQuery = detailQuery.Where(x => x.ProductDetails.Any(f => model.AssociatedProductIds.Contains(f.ProductId)));

            if (model.ExternalMasterformatIds != null && model.ExternalMasterformatIds.Any())
                detailQuery = detailQuery.Where(x => x.DetailMasterformats.Any(f => model.ExternalMasterformatIds.Contains(f.ExternalMasterformatId)));

            if (model.ExternalMasterformatCodes != null && model.ExternalMasterformatCodes.Any())
            {
                var masterformatIds = (await _masterformatService.GetBackofficeMasterformatsAsync()).Where(x => model.ExternalMasterformatCodes.Select(m => m.ToUpper()).Contains(x.Code.ToUpper())).Select(x => x.Id).ToList();
                detailQuery = detailQuery.Where(x => x.DetailMasterformats.Any(f => masterformatIds.Contains(f.ExternalMasterformatId)));
            }

            if (model.DetailScaleIds != null && model.DetailScaleIds.Any())
                detailQuery = detailQuery.Where(x => x.DetailScaleId != null && model.DetailScaleIds.Contains(x.DetailScaleId.Value));

            if (!string.IsNullOrWhiteSpace(model.Query))
            {
                detailQuery = DetailSearch.Search(new DetailSearchOptions
                {
                    Query = model.Query
                }, detailQuery, unitOfWork);
            }

            return detailQuery;
        }

        private Expression<Func<Detail, DetailListDto>> DetailListProjection()
        {
            return x => new DetailListDto
            {
                Id = x.Id,
                Name = x.Name,
                ManufacturerId = x.ManufacturerId,
                Published = x.Published,
                RegionIds = x.RegionIds,
                StateIds = x.StateIds,
                PhotoId = x.PhotoId,
                DetailScaleId = x.DetailScaleId,
                Manufacturer = new DetailListManufacturerDto
                {
                    Id = x.Manufacturer.Id,
                    Name = x.Manufacturer.Name,
                    Site = x.Manufacturer.Site
                },
                Photo = new DetailListPhotoDto
                {
                    PhotoId = x.PhotoId,
                    UploadUrl = x.PhotoId != null ? x.Photo.UploadUrl : null,
                    Small = x.PhotoId != null ? x.Photo.SmallImgUrl : null,
                    Middle = x.PhotoId != null ? x.Photo.MiddleImgUrl : null,
                    Big = x.PhotoId != null ? x.Photo.OriginalImgUrl : null,
                },
                DetailScale = x.DetailScaleId != null ? new DetailListDetailScaleDto
                {
                    Id = x.DetailScale.Id,
                    Name = x.DetailScale.Name,
                } : null,
                ProjectFiles = x.DetailFiles.Select(f => new DetailListProjectFileDto
                {
                    FileId = f.FileId,
                    RegionIds = f.RegionIds,
                    StateIds = f.StateIds,
                    SoftwareRelease = f.SoftwareRelease,
                    ContentCreatedby = f.ContentCreatedby,
                    ContentCheckedBy = f.ContentCheckedBy,
                    FileVersion = f.FileVersion,
                    ProjectDataType = new DetailListProjectDataTypeDto
                    {
                        Id = f.ProjectDataType.Id,
                        Title = f.ProjectDataType.Title,
                        Header = f.ProjectDataType.Header
                    },
                    ProjectDataTypeId = f.ProjectDataTypeId,
                    Title = f.File.Title,
                    FileName = f.File.FileName,
                    FileSize = f.File.FileSize,
                    MimeType = f.File.MediaType,
                    UpdatesCount = f.File.UpdatesCount,
                    FileSyncStatusCode = f.File.SyncStatusCode,
                    FileSyncUrl = f.File.SyncUrl,
                    Url = f.File.Url,
                    Preview = f.File.PreviewUrl,
                }).ToList(),
                Applications = x.DetailDetailApplications.Select(d => new DetailListApplicationDto
                {
                    Id = d.DetailApplicationId,
                    Name = d.DetailApplication.Name
                }).ToList()
            };
        }

        private async Task<List<DetailListDto>> RotateDetailsAsync(DetailFilterDto model, IQueryable<Detail> detailQuery, int totalCount)
        {
            int[] rotatedDetailIds = Array.Empty<int>();
            List<DetailManufacturerPairDto> rotatedDetails = new();

            DetailManufacturerPairDto[] detailManufacturerPairs = await detailQuery.Select(x => new DetailManufacturerPairDto
            {
                DetailId = x.Id,
                ManufacturerId = x.ManufacturerId.Value
            })
            .ToArrayAsync();

            var detailManufacturerGroups = detailManufacturerPairs.GroupBy(x => x.ManufacturerId).ToList();
            int rotationLimit = model.Offset + model.Count;
            if (totalCount < rotationLimit)
                rotationLimit = totalCount;

            int rotatedDetailsCount = 0;
            int rotationTakePortion = 2;
            int elementsInGroupToSkip = 0;

            while (rotatedDetailsCount < rotationLimit)
            {
                foreach (var group in detailManufacturerGroups)
                {
                    DetailManufacturerPairDto[] manufacturerDetails = group.Skip(elementsInGroupToSkip).Take(rotationTakePortion).ToArray();
                    if (manufacturerDetails.Any())
                    {
                        rotatedDetails.AddRange(manufacturerDetails);
                        rotatedDetailsCount += manufacturerDetails.Length;
                    }
                    if (rotatedDetailsCount >= rotationLimit)
                        break;
                }
                elementsInGroupToSkip += rotationTakePortion;
            }

            rotatedDetailIds = rotatedDetails.Select(x => x.DetailId).Skip(model.Offset).Take(model.Count).ToArray();
            detailQuery = detailQuery.Where(x => rotatedDetailIds.Contains(x.Id));

            List<DetailListDto> data = await detailQuery.Select(DetailListProjection()).ToListAsync();

            List<DetailListDto> rotatedData = new();
            foreach (int id in rotatedDetailIds)
                rotatedData.Add(data.First(x => x.Id == id));

            return rotatedData;
        }
        #endregion
    }
}