﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Services.Helpers;
using Serilog;
using System;
using System.Globalization;

namespace BIMsmithMarket.Api.Providers
{
    public class AuthenticationService
    {
        public static UserToken GetUserToken(string authToken)
        {
            var key = ConfigurationHelper.GetValue("EncryptionKey");
            var max = ConfigurationHelper.GetValue("AllowedDurationMinutes");
            var allowedMinutes = string.IsNullOrEmpty(max) ? 60 : int.Parse(max);

            var result = new UserToken();
            try
            {
                var decrypted = EncryptionUtility.Decrypt(key, authToken);

                var arr = decrypted.Split('!');
                if (arr.Length != 3) return null;

                result.UserEmail = arr[0];
                result.UserId = arr[1].Replace("\"", "");
            }
            catch (Exception e)
            {
                Log.Error($"Error on parsing AuthToken: {authToken}. Exception: {e.GetAllMessages()}");
            }

            return result;
        }

        public static string GenerateAuthToken(string userId, string userEmail)
        {
            return EncryptionUtility.Encrypt(ConfigurationHelper.GetValue("EncryptionKey"), string.Format("{0}!{1}!{2}", userEmail, userId, DateTime.UtcNow)).Replace("\"", "");
        }

        public class UserToken
        {
            public string UserId { get; set; }

            public string UserEmail { get; set; }
        }
    }
}