﻿using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using BIMsmithMarket.Services.PaymentServices;
using Flurl;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using System.IO;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers.PaymentControllers
{
#if !DEBUG
    [Authorize]
#endif
    [Route("api/[controller]/[action]/{id?}")]
    public class PaymentController : BaseApiController
    {
        private readonly IProductPriceService _productPriceService;
        private readonly IPaymentService _paymentService;
        private readonly string _marketUrl = ConfigurationHelper.GetValue("MarketFrontBaseUrl");
        private readonly IWebHostEnvironment _webHostEnvironment;
        private UserManager<ApplicationUser> _userManager;

        public PaymentController(
            IProductPriceService productPriceService,
            IWebHostEnvironment webHostEnvironment,
            UserManager<ApplicationUser> userManager,
            IPaymentService paymentService)
        {
            _productPriceService = productPriceService;
            _paymentService = paymentService;
            _webHostEnvironment = webHostEnvironment;
            _userManager = userManager;
        }

        [HttpGet]
        public async Task<IActionResult> Checkout(int id)
        {
            var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
#if DEBUG
            userId = DbConstants.AdminUserId;
#endif
            var user = await _userManager.FindByIdAsync(userId);
            var userEmail = user.Email;
            var serverFolder = Path.Combine(_webHostEnvironment.WebRootPath, "assets/img/StripeImgs/");
            var apiUrl = ConfigurationHelper.GetValue("MarketApiBaseUrl");
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var userpaidProduct = await _paymentService.CreateCheckoutSessionAsync(id, userEmail, userId, serverFolder, apiUrl, unitOfWork);
                return Ok(userpaidProduct);
            }
        }

        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> GetCurrentProductPrice(int id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var currentPayModel = await _productPriceService.GetCurrentProductPriceAsync(id, unitOfWork);
                return Ok(currentPayModel);
            }
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="sessionId"></param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        [ActionName("StripeSuccess")]
        public async Task<IActionResult> StripeSuccess([FromQuery] string sessionId)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                await _paymentService.StripeConfirmation(sessionId, unitOfWork);
                return Redirect(new Url(_marketUrl).AppendPathSegment("Payment/StripeSuccess"));
            }
        }

        /// <summary>
        /// Sets the user subscription
        /// </summary>
        /// <param name="model">The model</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> SetUserSubscription(SetUserSubscriptionDto model)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            return Ok(await _paymentService.EditUserSubscriptionAsync(model, userId, unitOfWork));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="sessionId"></param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> StripeCancelation([FromQuery] string sessionId)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                await _paymentService.StripeCancelation(sessionId, unitOfWork);
                return Redirect($"{_marketUrl}Payment/StripeSuccess");
            }
        }
    }
}