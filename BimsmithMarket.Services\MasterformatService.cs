﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Domain.SearchModels;
using BIMsmithMarket.Services.Interfaces;
using Flurl;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class MasterformatService : IMasterformatService
    {
        private readonly ICacheService _cacheService;
        private readonly string _cacheKey = "CBOMasterformats";

        public MasterformatService(ICacheService cacheService)
        {
            _cacheService = cacheService;
        }

        public async Task<List<MasterformatModel>> GetBackofficeMasterformatsAsync(List<int> ids = null)
        {
            try
            {
                List<MasterformatModel> masterformats = new List<MasterformatModel>();

                if (_cacheService.Contains(_cacheKey))
                {
                    masterformats = _cacheService.Get<List<MasterformatModel>>(_cacheKey);
                }
                else
                {
                    Url url = new Url(ConfigurationHelper.GetValue("CBOBaseAddress"))
                                 .AppendPathSegment("api/Masterformat/Search");

                    MasterformatSearchModel model = new MasterformatSearchModel
                    {
                        Ids = null
                    };

                    var httpClient = HttpClientFactory.GetClient();
                    using HttpResponseMessage result = await httpClient.PostAsJsonAsync(url, model);
                    if (result.IsSuccessStatusCode)
                    {
                        masterformats = JsonConvert.DeserializeObject<List<MasterformatModel>>(await result.Content.ReadAsStringAsync());
                        _cacheService.Set(_cacheKey, masterformats, CacheConstants.OneDay);
                    }
                }

                if (ids != null)
                    return masterformats.Where(x => ids.Contains(x.Id)).ToList();

                return masterformats;
            }
            catch (Exception ex)
            {
                Log.Error(ex.Message);
            }
            return new List<MasterformatModel>();
        }

        public async Task<MasterformatModel> GetBackofficeMasterformatAsync(int id)
        {
            List<MasterformatModel> result = await GetBackofficeMasterformatsAsync(new List<int> { id });
            return result.FirstOrDefault();
        }
    }
}