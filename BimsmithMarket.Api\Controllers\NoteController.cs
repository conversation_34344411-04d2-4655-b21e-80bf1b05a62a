﻿using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    public class NoteController : BaseApiController
    {
        private readonly INoteService _noteService;

        public NoteController(INoteService noteService)
        {
            _noteService = noteService;
        }

        /// <summary>
        /// Adds new model
        /// </summary>
        /// <param name="model">The model</param>
        /// <returns></returns>
        [HttpPost]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Add(AddNoteDto model)
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _noteService.AddAsync(model, userId, unitOfWork));
        }

        /// <summary>
        /// Updates the model
        /// </summary>
        /// <param name="model">The model</param>
        /// <returns></returns>
        [HttpPost]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Edit(EditNoteDto model)
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _noteService.EditAsync(model, userId, unitOfWork));
        }

        /// <summary>
        /// Gets model for admin panel
        /// </summary>
        /// <param name="id">The model identifier</param>
        /// <returns></returns>
        [HttpGet]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> AdminGet(int id)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _noteService.AdminGetAsync(id, unitOfWork));
        }

        /// <summary>
        /// Gets list for admin panel
        /// </summary>
        /// <param name="entityType">The entity type</param>
        /// <param name="entityId">The entity identifier</param>
        /// <param name="isActive">The flag to show only active notes</param>
        /// <param name="query">The search query</param>
        /// <param name="startDate">The start date</param>
        /// <param name="endDate">The end date</param>
        /// <param name="offset">The offset to skip</param>
        /// <param name="count">The count to take</param>
        /// <returns></returns>
        [HttpGet]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> AdminList(
            EntityType entityType,
            int entityId,
            bool? isActive = null,
            string query = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int offset = 0,
            int count = 10
            )
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _noteService.AdminListAsync(unitOfWork, entityType, entityId, isActive, query, startDate, endDate, offset, count));
        }

        /// <summary>
        /// Deletes the model
        /// </summary>
        /// <param name="id">The identifier</param>
        /// <returns></returns>
        [HttpDelete]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Delete(int id)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _noteService.DeleteAsync(id, unitOfWork));
        }
    }
}