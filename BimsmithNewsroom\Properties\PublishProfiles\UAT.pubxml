<?xml version="1.0" encoding="utf-8"?>
<!--
This file is used by the publish/package process of your Web project. You can customize the behavior of this process
by editing this MSBuild file. In order to learn more about this please visit https://go.microsoft.com/fwlink/?LinkID=208121. 
-->
<Project>
  <PropertyGroup>
    <WebPublishMethod>MSDeploy</WebPublishMethod>
    <LastUsedBuildConfiguration>UAT</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish>https://newsroom-uat.bimsmith.com</SiteUrlToLaunchAfterPublish>
    <LaunchSiteAfterPublish>true</LaunchSiteAfterPublish>
    <ExcludeApp_Data>false</ExcludeApp_Data>
    <ProjectGuid>03e6b278-7f30-4f65-a2c2-ab63f4604fcd</ProjectGuid>
    <MSDeployServiceURL>https://*************:8172/msdeploy.axd</MSDeployServiceURL>
    <DeployIisAppPath>UAT-Newsroom</DeployIisAppPath>
    <RemoteSitePhysicalPath />
    <SkipExtraFilesOnServer>true</SkipExtraFilesOnServer>
    <MSDeployPublishMethod>WMSVC</MSDeployPublishMethod>
    <EnableMSDeployBackup>true</EnableMSDeployBackup>
    <EnableMsDeployAppOffline>true</EnableMsDeployAppOffline>
    <UserName>BIMSMITHDEV\MarketVm</UserName>
    <_SavePWD>false</_SavePWD>
    <TargetFramework>net8.0</TargetFramework>
    <SelfContained>false</SelfContained>
    <EnvironmentName>UAT</EnvironmentName>
  </PropertyGroup>
</Project>