﻿using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Models;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IUploadFileService
    {
        Task<Domain.Models.FileUploadResultModel> AddFileAsync(IFormFile formFile, string userId, string folderFiles, string previewUrl = null, string attachURL = null, bool allowAllMediaTypes = false, string customFileName = null);
        Task<object> UpdateFileWithUrlAsync(int fileId);
        Task<FileUploadResultModel> FileLocalAsync(IFormFile formFile, string startLinkToFile, string userId, string folderFiles, string previewUrl);
        Task<PhotoDto> PhotoAsync(IFormFile formFile, string userId, bool suppressAlphaChannel = true, string attachURL = null, bool isRevitIcon = false);
        Task<object> PhotoLocalAsync(IFormFile formFile, string startLinkToLogo, string userId, string folderImages);
        Task<object> ManufacturerFileAsync(IFormFile formFile, string userId, int manufacturerId);
        Task<FileUploadResultModel> TempFileAsync(IFormFile formFile, string userId, string startLinkToFile, string folderFiles);
        Task<FileUploadResultModel> AttachmentFileAsync(IFormFile formFile, string previewUrl);
        Task<FileUploadResultModel> UploadStyleFileAsync(IFormFile formFile, string userId, string folderFiles);
    }
}
