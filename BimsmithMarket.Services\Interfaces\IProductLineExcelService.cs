﻿using BIMsmithMarket.Domain.Dto.ProductLineExcelDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IProductLineExcelService
    {
        Task<string> GetProductLineExcelAsync(int manufacturerId, IUnitOfWork unitOfWork);

        Task<ProductLineExcelImportResultDto> ImportProductLineExcelAsync(int manufacturerId, IFormFile formFile, string userId, IUnitOfWork unitOfWork);
    }
}