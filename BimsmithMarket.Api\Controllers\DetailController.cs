﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Dto.DetailDto;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using System.IO;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// Detail Controller
    /// </summary>
    [Route("api/[controller]/[action]/{id?}")]
    public class DetailController : BaseApiController
    {
        /// <summary>
        /// Detail service
        /// </summary>
        private readonly IDetailService _detailService;

        private readonly IWebHostEnvironment _webHostEnvironment;

        public DetailController(
            IDetailService detailService,
            IWebHostEnvironment webHostEnvironment)
        {
            _detailService = detailService;
            _webHostEnvironment = webHostEnvironment;
        }

        /// <summary>
        /// Adds detail
        /// </summary>
        /// <param name="model">The detail creation model</param>
        /// <returns></returns>     
        [Authorize(Roles = DbConstants.AdminRole)]
        [HttpPost]
        public async Task<IActionResult> Add(AddDetailDto model)
        {
            var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            var result = await _detailService.AddAsync(model, userId);
            return Ok(result);
        }

        /// <summary>
        /// Edits detail
        /// </summary>
        /// <param name="model">The detail edit model</param>
        /// <returns></returns>     
        [Authorize(Roles = DbConstants.AdminRole)]
        [HttpPost]
        public async Task<IActionResult> Edit(EditDetailDto model)
        {
            var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            var result = await _detailService.EditAsync(model, userId);
            return Ok(result);
        }

        /// <summary>
        /// Gets detail with indentifier specified
        /// </summary>
        /// <param name="id">The detail identifier</param>
        /// <returns></returns>     
        [HttpGet]
        public async Task<IActionResult> Get(int id)
        {
            var result = await _detailService.GetAsync(id);
            return Ok(result);
        }

        /// <summary>
        /// Deletes detail with indentifier specified
        /// </summary>
        /// <param name="id">The detail identifier</param>
        /// <returns></returns> 
        [Authorize(Roles = DbConstants.AdminRole)]
        [HttpDelete]
        public async Task<IActionResult> Delete(int id)
        {
            var result = await _detailService.DeleteAsync(id);
            return Ok(result);
        }

        /// <summary>
        /// Gets details list
        /// </summary>
        /// <param name="model">Filter model</param>
        /// <returns></returns> 
        [HttpPost]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> List(DetailFilterDto model)
        {
            var result = await _detailService.ListAsync(model);
            return Ok(result);
        }

        /// <summary>
        /// Gets details filters
        /// </summary>
        /// <param name="model">Filter model</param>
        /// <returns></returns> 
        [HttpPost]
        public async Task<IActionResult> GetFilters(DetailFilterDto model)
        {
            var result = await _detailService.GetFiltersAsync(model);
            return Ok(result);
        }

        /// <summary>
        /// Posts rating to detail
        /// </summary>
        /// <param name="model">Post detail rating model</param>
        /// <returns></returns> 
        [HttpPost]
        public async Task<IActionResult> PostRating(PostDetailRatingDto model)
        {
            var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            var result = await _detailService.PostRating(model, userId);
            return Ok(result);
        }

        /// <summary>
        /// Posts rating to detail
        /// </summary>
        /// <param name="model">Post detail rating model</param>
        /// <returns></returns> 
        [HttpPost]
        public async Task<IActionResult> SendContentComment(DetailContentCommentDto model)
        {
            var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            var hostUrl = Request.Host.ToString();
            var emailsPath = Path.Combine(_webHostEnvironment.WebRootPath, "Emails");
            var result = await _detailService.PostContentComment(model, userId, hostUrl, emailsPath);
            return Ok(result);
        }
    }
}