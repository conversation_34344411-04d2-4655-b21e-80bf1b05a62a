﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class BlogMentionedEntry
    {
        public int Id { get; set; }

        public int BlogPostId { get; set; }

        public string Name { get; set; }

        public string EntryId { get; set; }

        public BlogMentionedEntryType EntryType { get; set; }

        public string EntryUrl { get; set; }

        public DateTime CreatedDate { get; set; }

        /// ------------------------------------------
        [ForeignK<PERSON>("BlogPostId")]
        public virtual BlogPost BlogPost { get; set; }
    }

    public enum BlogMentionedEntryType
    {
        Manufacturer = 0,
        Product = 1,
        //....
    }
}