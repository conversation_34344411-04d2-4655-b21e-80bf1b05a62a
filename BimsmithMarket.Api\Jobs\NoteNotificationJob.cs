﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using Flurl;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Quartz;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Jobs
{
    [DisallowConcurrentExecution]
    public class NoteNotificationJob : IJob
    {
        public async Task Execute(IJobExecutionContext context)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            DateTime today = DateTime.UtcNow.Date;
            NoteNotificationDto[] notes = await unitOfWork.NoteRepository.GetAll()
                .Where(x => x.DateToNotifyUsers != null && x.DateToNotifyUsers.Value.Date == today)
                .ProjectToType<NoteNotificationDto>()
                .ToArrayAsync();

            if (!notes.Any())
                return;

            string emailsPath = Path.Combine(Environment.CurrentDirectory, "wwwroot", "Emails");
            EmailNotificationHelper emailNotificationHelper = EmailNotificationHelper.Create(emailsPath);

            foreach (NoteNotificationDto note in notes)
            {
                NoteNotificationEmailDto noteEmailModel = await GetNoteEmailModelAsync(note, unitOfWork);
                await emailNotificationHelper.SendNoteNotificationEmailAsync(noteEmailModel);
            }
        }

        #region private methods
        private async Task<NoteNotificationEmailDto> GetNoteEmailModelAsync(NoteNotificationDto model, IUnitOfWork unitOfWork)
        {
            BaseNoteNotificationEntityInfoDto entityInfo = await GetEntityInfoAsync(model, unitOfWork);
            return new NoteNotificationEmailDto
            {
                EntityLinkCaption = GenerateEntityLinkCaption(model.EntityType, entityInfo),
                EntityLinkUrl = GenerateEntityLinkUrl(model.EntityType, entityInfo),
                NoteName = model.Name,
                NotificationList = model.NotificationList.ToList()
            };
        }

        private async Task<BaseNoteNotificationEntityInfoDto> GetEntityInfoAsync(NoteNotificationDto model, IUnitOfWork unitOfWork)
        {
            switch (model.EntityType)
            {
                case EntityType.ProductLine:
                    return await unitOfWork.ProductLineRepository.GetAll()
                        .Where(x => x.Id == model.EntityId)
                        .ProjectToType<NoteNotificationProductLineInfoDto>()
                        .FirstOrDefaultAsync();
                default: throw new InvalidInputException("Invalid Entity Type");
            }
        }

        private string GenerateEntityLinkUrl(EntityType entityType, BaseNoteNotificationEntityInfoDto entityInfo)
        {
            switch (entityType)
            {
                case EntityType.ProductLine:
                    {
                        NoteNotificationProductLineInfoDto productLineInfo = (NoteNotificationProductLineInfoDto)entityInfo;
                        return new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl"))
                            .AppendPathSegment("adminpanel")
                            .AppendPathSegment("product-line")
                            .AppendPathSegment(productLineInfo.ProductLineId)
                            .AppendPathSegment("edit")
                            .AppendPathSegment(productLineInfo.ManufacturerId)
                            .ToString();
                    }
                default: throw new InvalidInputException("Invalid Entity Type");
            }
        }

        private string GenerateEntityLinkCaption(EntityType entityType, BaseNoteNotificationEntityInfoDto entityInfo)
        {
            switch (entityType)
            {
                case EntityType.ProductLine:
                    {
                        NoteNotificationProductLineInfoDto productLineInfo = (NoteNotificationProductLineInfoDto)entityInfo;
                        return $"Product Line {productLineInfo.ProductLineName}. Manufacturer {productLineInfo.ManufacturerName}";
                    }
                default: throw new InvalidInputException("Invalid Entity Type");
            }
        }
        #endregion
    }
}