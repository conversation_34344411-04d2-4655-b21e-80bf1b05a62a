﻿using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels.RevitProcessing
{
    public class RevitProcessProduct : BaseEntity
    {
        public int RevitProcessId { get; set; }

        [ForeignKey("RevitProcessId")]
        public virtual RevitProcess RevitProcess { get; set; }

        public int ProductId { get; set; }

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }
    }
}