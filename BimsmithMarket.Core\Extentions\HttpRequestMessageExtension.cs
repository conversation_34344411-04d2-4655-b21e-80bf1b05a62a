﻿using System.Net.Http;

namespace BIMsmithMarket.Core.Extentions
{
    public static class HttpRequestMessageExtensions
    {
        private const string HttpContext = "MS_HttpContext";
        private const string RemoteEndpointMessage = "System.ServiceModel.Channels.RemoteEndpointMessageProperty";

        public static string GetClientIpAddress(this HttpRequestMessage request)
        {
            if (request.Properties.ContainsKey(HttpContext))
            {
                dynamic ctx = request.Properties[HttpContext];
                if (ctx != null)
                {
                    return ctx.Request.UserHostAddress;
                }
            }

            if (!request.Properties.ContainsKey(RemoteEndpointMessage)) return string.Empty;
            dynamic remoteEndpoint = request.Properties[RemoteEndpointMessage];
            return remoteEndpoint != null ? (string)remoteEndpoint.Address : string.Empty;
        }
    }
}