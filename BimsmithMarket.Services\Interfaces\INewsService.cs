﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface INewsService
    {
        Task<object> ListAsync(IUnitOfWork unitOfWork, string q, int offset, int count);
        Task<object> GetAsync(IUnitOfWork unitOfWork, int id, string vanityId);
        Task<object> AddAsync(IUnitOfWork unitOfWork, string userId, AddNewsModel model);
        Task<object> EditAsync(IUnitOfWork unitOfWork, string userId, EditNewsModel model);
        Task ChangePublishStateAsync(IUnitOfWork unitOfWork, ChangeNewsPublishStateModel model);
        Task DeleteAsync(IUnitOfWork unitOfWork, int id);
        Task<object> UploadImageAsync(IFormFile formFile, bool suppressAlphaChannel);
        Task<object> UploadContentAsync(IFormFile formFile, bool suppressAlphaChannel);
        Task<object> PublicListAsync(IUnitOfWork unitOfWork, NewsTargetSite target, string q, int offset, int count, string tag);
        Task<MetaInfoDto> GetNewsMetaInfoAsync(string vanityId, IUnitOfWork unitOfWork);
    }
}