﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Models
{
    public class AddCategoryModel
    {
        public int? ParentCategoryId { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; }

        public string IconUrl { get; set; }

        public string Keywords { get; set; }

        [Required]
        public List<int> KeyStatIds { get; set; }

        public string MetaDescription { get; set; }

        public string MetaKeywords { get; set; }

        public string MetaTitle { get; set; }

        public string Description { get; set; }

        public string Synonyms { get; set; }

        public string VanityUrl { get; set; }

        public bool IncludeRevitPlugin { get; set; }

        public int? RevitPluginPhotoId { get; set; }

        public string BimsmithVanityUrl { get; set; }

        public float Weight { get; set; }
    }

    public class EditCategoryModel : AddCategoryModel
    {
        [Required]
        public int Id { get; set; }
    }
}