﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using Mapster;
using System.Linq;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public static class ProjectDataTypeMap
    {
        public static void Register(TypeAdapterConfig config)
        {
            config.ForType<ProjectDataType, ProjectTypePublicListDto>()
                .Map(d => d.ChildrenIds, s => s.Children.Select(x => x.Id).ToArray());
        }
    }
}