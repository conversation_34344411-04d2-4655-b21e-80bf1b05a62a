﻿using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Http;
using System.IO;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Services
{
    public class UploadService : IUploadService
    {
        public async Task<string> SaveIcoToBlobAsync(IFormFile formFile)
        {
            if (formFile == null)
                return null;

            string fileName = formFile.FileName.Replace("\"", string.Empty);
            string extension = Path.GetExtension(fileName);

            AzureStorageService azureBlobProvider = new AzureStorageService(
                ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                ConfigurationHelper.GetValue("Environment"),
                bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                false);
            var filesContainer = azureBlobProvider.GetContainerByName(AzureStorageConstants.IconsContainer);

            BlobClient fileBlobUpload = filesContainer.GetBlobClient(fileName);

            while (fileBlobUpload.Exists())
            {
                fileName = Path.GetFileNameWithoutExtension(fileName) + "_c" + Path.GetExtension(fileName);
                fileBlobUpload = filesContainer.GetBlobClient(fileName);
            }

            using (var fileStream = formFile.OpenReadStream())
            {
                await fileBlobUpload.UploadAsync(fileStream);
            }

            if (FileHelper.IsVectorExtension(extension))
                await fileBlobUpload.SetHttpHeadersAsync(new BlobHttpHeaders { ContentType = MimeTypeProvider.GetMimeType(extension) });

            return fileBlobUpload.Uri.ToString();
        }
    }
}