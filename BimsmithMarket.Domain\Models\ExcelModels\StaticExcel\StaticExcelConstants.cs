﻿namespace BIMsmithMarket.Domain.Models.ExcelModels.StaticExcel
{
    public static class StaticExcelConstants
    {
        public const string IdCaption = "Id";

        public const string NameCaption = "Name";

        public const string ProductUrlCaption = "Product URL";

        public const string ULUrlCaption = "UL SPOT URL";

        public const string ManufacturerIdCaption = "ManufacturerId";

        public const string CategoryIdCaption = "CategoryId";

        public const string AdditionalCategoryIdsCaption = "Additional Category IDs";

        public const string ProductLineIdCaption = "ProductLineId";

        public const string PhotoIdCaption = "PhotoId";

        public const string PhotoURLsCaption = "Photo URLs";

        public const string DescriptionCaption = "Description";

        public const string VideoUrlCaption = "VideoUrl";

        public const string VanityURLCaption = "VanityURL";

        public const string MetaTitleCaption = "MetaTitle";

        public const string MetaDescriptionCaption = "MetaDescription";

        public const string MetaKeywordsCaption = "MetaKeywords";

        public const string KeywordsCaption = "Keywords";

        public const string PublishToPartnerCaption = "Publish To Partner";

        public const string ExternalIdCaption = "External Id";

        public const string ForgeWallURLCaption = "Forge Wall Url";

        public const string ForgeFloorURLCaption = "Forge Floor Url";

        public const string ForgeCeilingURLCaption = "Forge Ceiling Url";

        public const string ForgeRoofURLCaption = "Forge Roof Url";

        public const string CertificatesCaption = "Certificates";

        public const string MaterformatsCaption = "Masterformats";

        public const string OmniclassesCaption = "Omniclasses";

        public const string UniclassesCaption = "Uniclasses";

        public const string UniformatsCaption = "Uniformats";

        public const string CisfbsCaption = "Cisfbs";

        public const string ProductCutSheetUrlsCaption = "Product Cutsheet URLs";

        public const string PartSpecUrlsCaption = "3 Part Spec URLs";

        public const string ProductBrochureUrlsCaption = "Product Brochure URLs";

        public const string ImageUrlsCaption = "Image URLs";

        public const string SubmittalUrlsCaption = "Submittal URLs";

        public const string CatalogUrlsCaption = "Catalog URLs";

        public const string WarrantyUrlsCaption = "Warranty URLs";

        public const string TestingDataUrlsCaption = "Testing Data URLs";

        public const string SafetyDataSheetUrlsCaption = "Safety Data Sheet URLs";

        public const string InstallationGuideUrlsCaption = "Installation Guide URLs";

        public const string HpdUrlsCaption = "HPD URLs";

        public const string EpdUrlsCaption = "EPD URLs";

        public const string MasterPartSpecUrlsCaption = "3-Part Specification - Masterspec URLs";

        public const string PublishedCaption = "Published";

        public const string PublishedOnCustomMicrositeCaption = "Published On Custom Microsite";

        public const string StagingCaption = "Staging";

        public const string RevitFilesCaption = "Revit files (not for Upload)";

        public const string WeightCaption = "Product Weight";

        public const string LocalizationCaption = "Localization";

        public const string AttachmentsCaption = "Attachments (not for upload)";

        public const string ProjectFilesCaption = "Content files (not for upload)";
    }
}