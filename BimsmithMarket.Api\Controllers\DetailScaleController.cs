﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// Detail Scale Controller
    /// </summary>
    [Route("api/[controller]/[action]")]
    public class DetailScaleController : BaseApiController
    {
        /// <summary>
        /// Detail Scale service
        /// </summary>
        private readonly IDetailScaleService _detailScaleService;

        public DetailScaleController(IDetailScaleService detailScaleService)
        {
            _detailScaleService = detailScaleService;
        }

        /// <summary>
        /// Adds Detail Scale
        /// </summary>
        /// <param name="model">Detail scale creation model</param>
        /// <returns></returns> 
        [Authorize(Roles = DbConstants.AdminRole)]
        [HttpPost]
        public async Task<IActionResult> Add(AddDetailScaleViewModel model)
        {
            var userId = AuthHelper.GetUserInfo(HttpContext.Request, ClaimTypes.NameIdentifier);
            var result = await _detailScaleService.AddAsync(model, userId);
            return Ok(result);
        }

        /// <summary>
        /// Edits Detail Scale
        /// </summary>
        /// <param name="model">Detail scale edit model</param>
        /// <returns></returns>
        [Authorize(Roles = DbConstants.AdminRole)]
        [HttpPost]
        public async Task<IActionResult> Edit(EditDetailScaleViewModel model)
        {
            try
            {
                var userId = AuthHelper.GetUserInfo(HttpContext.Request, ClaimTypes.NameIdentifier);
                var result = await _detailScaleService.EditAsync(model, userId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message + " " + ex.InnerException);
            }
        }

        /// <summary>
        /// Gets Detail Scale with identifier specified
        /// </summary>
        /// <param name="id">Detail scale identifier</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> Get(int id)
        {
            try
            {
                var result = await _detailScaleService.GetAsync(id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message + " " + ex.InnerException);
            }
        }

        /// <summary>
        /// Deletes Detail Scale with identifier
        /// </summary>
        /// <param name="id">Detail scale identifier</param>
        /// <returns></returns>
        [Authorize(Roles = DbConstants.AdminRole)]
        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            var result = await _detailScaleService.DeleteAsync(id);
            return Ok(result);
        }

        /// <summary>
        /// Gets Detail Scale list
        /// </summary>
        /// <param name="query"></param>
        /// <param name="count"></param>
        /// <param name="offset"></param>
        /// <returns></returns>
        [HttpGet]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> List(string query = null, int count = 10, int offset = 0)
        {
            var result = await _detailScaleService.ListAsync(query, count, offset);
            return Ok(result);
        }
    }
}