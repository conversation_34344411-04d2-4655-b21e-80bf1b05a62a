﻿using Azure.Storage.Blobs;
using Azure.Storage.Queues;
using Azure.Storage.Queues.Models;
using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models.ExcelModels.StaticExcel;
using BIMsmithMarket.Services.Interfaces;
using BIMsmithMarket.Services.Providers;
using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class StaticExcelImportService : IStaticExcelImportService
    {
        private readonly IAzureStorageService _azureBlobProvider;
        private readonly IMasterformatService _masterformatService;
        private readonly IManufacturerBackupService _manufacturerBackupService;
        private readonly ICacheService _cacheService;
        private readonly string _cacheKey = "StaticExcelCache";

        public StaticExcelImportService(
            IAzureStorageService azureBlobProvider,
            IMasterformatService masterformatService,
            IManufacturerBackupService manufacturerBackupService,
            ICacheService cacheService)
        {
            _azureBlobProvider = azureBlobProvider;
            _masterformatService = masterformatService;
            _manufacturerBackupService = manufacturerBackupService;
            _cacheService = cacheService;
        }

        public async Task<int> UploadFileAsync(
            IFormFile formFile,
            int manufacturerId,
            string userId,
            IUnitOfWork unitOfWork)
        {
            BlobClient fileBlobUpload = await UploadStaticFileToBlobAsync(formFile);
            StaticExcelFile staticExcelFile = await CreateStaticExcelFileRecordAsync(manufacturerId, fileBlobUpload.Uri.ToString(), userId, unitOfWork);
            await UploadStaticFileToQueueAsync(staticExcelFile.Id);

            return staticExcelFile.Id;
        }

        public async Task ProcessFileQueueAsync(IUnitOfWork unitOfWork)
        {
            QueueClient staticExcelFileQueue = _azureBlobProvider.GetQueueByName(AzureStorageConstants.StaticExcelFilesQueue);
            QueueMessage message = await staticExcelFileQueue.ReceiveMessageAsync();
            if (message == null)
                return;

            int fileId = -1;
            if (!int.TryParse(message.MessageText, out fileId))
            {
                await DeleteMessageFromQueueAsync(staticExcelFileQueue, message);
                return;
            }

            StaticExcelFile staticExcelFile = await unitOfWork.StaticExcelFileRepository.GetByIdAsync(fileId);
            if (staticExcelFile == null)
            {
                await DeleteMessageFromQueueAsync(staticExcelFileQueue, message);
                return;
            }

            if (message.DequeueCount > 1)
            {
                Log.Error($"[StaticExcelFileQueue] Message is invalid {message.MessageText}");
                SetExcelFileStatus(staticExcelFile, ExcelProcessingStatus.ValidationFailed, staticExcelFile.CreatedById);
                await unitOfWork.SaveAsync();
                await DeleteMessageFromQueueAsync(staticExcelFileQueue, message);
                return;
            }

            if (staticExcelFile.Status == ExcelProcessingStatus.Cancelled)
            {
                await DeleteMessageFromQueueAsync(staticExcelFileQueue, message);
                return;
            }

            string userId = staticExcelFile.CreatedById;
            SetExcelFileStatus(staticExcelFile, ExcelProcessingStatus.Validading, userId);
            await unitOfWork.SaveAsync();

            string filePath = await SaveExcelFileLocallyAsync(staticExcelFile.Url);
            StaticExcelCache staticExcelCache = await GetStaticExcelCacheAsync(unitOfWork);
            ProductsExcelParseModel excelParseData = await ExcelProductsProvider.ParseExcel(filePath, staticExcelCache.QualityItems.Select(a => a.Name).Distinct().ToList());

            if (!excelParseData.Products.Any())
            {
                SetExcelFileStatus(staticExcelFile, ExcelProcessingStatus.Processed, userId);
                await unitOfWork.SaveAsync();
                await DeleteMessageFromQueueAsync(staticExcelFileQueue, message);
                return;
            }

            if (excelParseData.Errors.Any())
            {
                SetExcelFileStatus(staticExcelFile, ExcelProcessingStatus.ValidationFailed, userId);
                staticExcelFile.ValidationErrors = JsonConvert.SerializeObject(excelParseData.Errors);
                await unitOfWork.SaveAsync();
                await DeleteMessageFromQueueAsync(staticExcelFileQueue, message);
                return;
            }

            if (!excelParseData.Errors.Any())
                ValidateCodes(excelParseData, staticExcelCache, unitOfWork);

            if (excelParseData.Errors.Any())
            {
                SetExcelFileStatus(staticExcelFile, ExcelProcessingStatus.ValidationFailed, userId);
                staticExcelFile.ValidationErrors = JsonConvert.SerializeObject(excelParseData.Errors);
                await unitOfWork.SaveAsync();
                await DeleteMessageFromQueueAsync(staticExcelFileQueue, message);
                return;
            }

            SetExcelFileStatus(staticExcelFile, ExcelProcessingStatus.Validated, userId);
            staticExcelFile.TotalProductsCount = excelParseData.Products.Count;
            await unitOfWork.SaveAsync();

            //Create manufacturer info backup if backup for current date is not created already
            await CreateManufacturerBackupAsync(excelParseData, unitOfWork);

            QueueClient staticExcelProductsQueue = _azureBlobProvider.GetQueueByName(AzureStorageConstants.StaticExcelProductsQueue);
            foreach (ProductModel product in excelParseData.Products)
            {
                product.StaticExcelFileId = staticExcelFile.Id;
                if (product.ExcelParseErrors.Any())
                {
                    AddErrorProduct(product, staticExcelFile, unitOfWork);
                    staticExcelFile.ErrorProductsCount++;
                }
                else
                    await staticExcelProductsQueue.SendMessageAsync(JsonConvert.SerializeObject(product));
            }

            if (FileProcessingCompleted(staticExcelFile))
                SetExcelFileStatus(staticExcelFile, ExcelProcessingStatus.Processed, userId);
            await unitOfWork.SaveAsync();

            await DeleteMessageFromQueueAsync(staticExcelFileQueue, message);
            System.IO.File.Delete(filePath);
        }

        public async Task ProcessProductQueueAsync(IUnitOfWork unitOfWork)
        {
            QueueClient staticExcelProductQueue = _azureBlobProvider.GetQueueByName(AzureStorageConstants.StaticExcelProductsQueue);
            QueueMessage[] messages = await staticExcelProductQueue.ReceiveMessagesAsync(AzureStorageConstants.QueueMessagesLimit);

            if (!messages.Any())
                return;

            foreach (QueueMessage message in messages)
            {
                if (message == null)
                    continue;

                if (message.DequeueCount > 1)
                {
                    Log.Error($"[StaticExcelProductQueue] Message is invalid {message.MessageText}");
                    await DeleteMessageFromQueueAsync(staticExcelProductQueue, message);
                    continue;
                }

                unitOfWork.RejectChanges();

                ProductModel product = message.Body.ToObjectFromJson<ProductModel>();
                if (product == null)
                {
                    await DeleteMessageFromQueueAsync(staticExcelProductQueue, message);
                    continue;
                }

                StaticExcelFile staticExcelFile = await unitOfWork.StaticExcelFileRepository.GetByIdAsync(product.StaticExcelFileId.Value);
                if (staticExcelFile == null)
                {
                    await DeleteMessageFromQueueAsync(staticExcelProductQueue, message);
                    continue;
                }

                if (staticExcelFile.Status == ExcelProcessingStatus.Cancelled)
                {
                    staticExcelFile.CancelledProductsCount++;
                    await unitOfWork.SaveAsync();
                    await DeleteMessageFromQueueAsync(staticExcelProductQueue, message);
                    continue;
                }

                SetExcelFileStatus(staticExcelFile, ExcelProcessingStatus.Processing, staticExcelFile.CreatedById);
                await unitOfWork.SaveAsync();

                StaticExcelCache staticExcelCache = await GetStaticExcelCacheAsync(unitOfWork);

                ProductModel processedProduct = await ExcelProductsProvider.ImportSingleProductAsync(
                    product,
                    new List<int> { staticExcelFile.ManufacturerId.Value },
                    staticExcelCache.Manufacturers,
                    staticExcelCache.Categories,
                    staticExcelFile.CreatedById,
                    staticExcelCache.QualityItems,
                    staticExcelCache.Cisfbs,
                    staticExcelCache.ProjectTypes,
                    staticExcelCache.Masterformats,
                    staticExcelCache.Omniclasses,
                    staticExcelCache.Uniclasses,
                    staticExcelCache.Uniformats,
                    staticExcelCache.Keystats,
                    staticExcelCache.KeystatUnits,
                    unitOfWork
                    );

                if (!processedProduct.ExcelParseErrors.Any())
                    staticExcelFile.SucceedProdutcsCount++;
                else
                {
                    AddErrorProduct(processedProduct, staticExcelFile, unitOfWork);
                    staticExcelFile.ErrorProductsCount++;
                }

                if (FileProcessingCompleted(staticExcelFile))
                    SetExcelFileStatus(staticExcelFile, ExcelProcessingStatus.Processed, staticExcelFile.CreatedById);

                await unitOfWork.SaveAsync();

                await DeleteMessageFromQueueAsync(staticExcelProductQueue, message);
            }
        }

        public async Task CancelImportAsync(int id, string userId, IUnitOfWork unitOfWork)
        {
            StaticExcelFile staticExcelFile = await unitOfWork.StaticExcelFileRepository.GetByIdAsync(id);
            if (staticExcelFile == null)
                throw new DbItemNotFoundException("Import file not found");

            staticExcelFile.Status = ExcelProcessingStatus.Cancelled;
            staticExcelFile.ModifiedById = userId;
            staticExcelFile.ModifiedDate = DateTime.UtcNow;
            await unitOfWork.SaveAsync();
        }

        public async Task<PaginationListDto<StaticExcelFileListDto>> ListAsync(
            string userId,
            IUnitOfWork unitOfWork,
            int? manufacturerId = null,
            ExcelProcessingStatus? status = null,
            int count = 50,
            int offset = 0)
        {
            if (string.IsNullOrWhiteSpace(userId))
                throw new InvalidInputException("User is not authorized");

            ApplicationUser user = await unitOfWork.UserRepository.GetAll().FirstOrDefaultAsync(x => x.Id == userId);
            if (user == null)
                throw new InvalidInputException("User not found");

            string[] superAdminEmails = ConfigurationHelper.GetValue("AdministratorEmails").Split(',').Select(a => a.ToLower()).ToArray();
            bool isUserSuperAdmin = superAdminEmails.Contains(user.Email.ToLower());

            IQueryable<StaticExcelFile> staticExcelFilesQuery = unitOfWork.StaticExcelFileRepository.GetAll()
                                                                          .AsNoTracking()
                                                                          .Include(x => x.StaticExcelProductErrors);

            if (!isUserSuperAdmin)
                staticExcelFilesQuery = staticExcelFilesQuery.Where(x => x.CreatedById == userId);

            if (status.HasValue)
                staticExcelFilesQuery = staticExcelFilesQuery.Where(x => x.Status == status);

            if (manufacturerId.HasValue)
                staticExcelFilesQuery = staticExcelFilesQuery.Where(x => x.ManufacturerId == manufacturerId);

            int totalCount = await staticExcelFilesQuery.CountAsync();
            StaticExcelFile[] staticExcelFiles = await staticExcelFilesQuery.OrderByDescending(x => x.CreatedDate)
                                                                            .Skip(offset)
                                                                            .Take(count)
                                                                            .ToArrayAsync();

            return new PaginationListDto<StaticExcelFileListDto>
            {
                Data = staticExcelFiles.Adapt<StaticExcelFileListDto[]>(),
                Count = totalCount
            };
        }

        #region private methods
        private static async Task<StaticExcelFile> CreateStaticExcelFileRecordAsync(int manufacturerId, string url, string userId, IUnitOfWork unitOfWork)
        {
            StaticExcelFile staticExcelFile = new StaticExcelFile
            {
                CreatedById = userId,
                ManufacturerId = manufacturerId,
                Url = url,
                Status = ExcelProcessingStatus.Free
            };

            unitOfWork.StaticExcelFileRepository.Insert(staticExcelFile);
            await unitOfWork.SaveAsync();

            return staticExcelFile;
        }

        private async Task<BlobClient> UploadStaticFileToBlobAsync(IFormFile formFile)
        {
            BlobContainerClient staticFileExcelContainer = _azureBlobProvider.GetContainerByName(AzureStorageConstants.StaticExcelFilesContainer);
            string fileName = formFile.FileName.Replace("\"", string.Empty);
            string extension = Path.GetExtension(fileName);
            string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
            string blobName = $"{fileNameWithoutExtension} - {Guid.NewGuid()}{extension}";
            BlobClient fileBlobUpload = staticFileExcelContainer.GetBlobClient(blobName);
            using (var fileStream = formFile.OpenReadStream())
            {
                await fileBlobUpload.UploadAsync(fileStream);
            }

            return fileBlobUpload;
        }

        private async Task UploadStaticFileToQueueAsync(int id)
        {
            QueueClient staticExcelFileQueue = _azureBlobProvider.GetQueueByName(AzureStorageConstants.StaticExcelFilesQueue);
            await staticExcelFileQueue.SendMessageAsync(id.ToString());
        }

        private async Task<StaticExcelCache> GetStaticExcelCacheAsync(IUnitOfWork unitOfWork)
        {
            StaticExcelCache staticExcelCache = _cacheService.Get<StaticExcelCache>(_cacheKey);
            if (staticExcelCache != null)
                return staticExcelCache;

            staticExcelCache = new StaticExcelCache
            {
                QualityItems = await unitOfWork.QualityItemRepository.GetAll().AsNoTracking().Select(a => new StaticExcelCacheQualityItemModel { Id = a.Id, Name = a.Name.Trim() }).ToListAsync(),
                Cisfbs = await unitOfWork.CisfbRepository.GetAll().AsNoTracking().Select(a => new StaticExcelCacheCisfbModel { Id = a.Id, Code = a.Code.Trim() }).ToListAsync(),
                Masterformats = await _masterformatService.GetBackofficeMasterformatsAsync(),
                Omniclasses = await unitOfWork.OmniclassRepository.GetAll().AsNoTracking().Select(a => new StaticExcelCacheOmniclassModel { Id = a.Id, Code = a.Code.Trim() }).ToListAsync(),
                Uniclasses = await unitOfWork.UniclassRepository.GetAll().AsNoTracking().Select(a => new StaticExcelCacheUniclassModel { Id = a.Id, Code = a.Code.Trim() }).ToListAsync(),
                Uniformats = await unitOfWork.UniformatRepository.GetAll().AsNoTracking().Select(a => new StaticExcelCacheUniformatModel { Id = a.Id, Code = a.Code.Trim() }).ToListAsync(),
                Keystats = await unitOfWork.KeyStatRepository.GetAll().AsNoTracking().Select(a => new StaticExcelCacheKeystatModel { Id = a.Id, Name = a.Name.Trim() }).ToListAsync(),
                KeystatUnits = await unitOfWork.KeyStatUnitRepository.GetAll().AsNoTracking().Select(a => new StaticExcelCacheKeystatUnitModel { Id = a.Id, GroupName = a.GroupName.Trim(), BUnitName = a.BUnitName.Trim() }).ToListAsync(),
                ProjectTypes = await unitOfWork.ProjectDataTypeRepository.GetAll().AsNoTracking().Select(a => new StaticExcelCacheProjectTypeModel { Id = a.Id, Title = a.Title, ParentId = a.ParentId }).ToListAsync(),
                Manufacturers = await unitOfWork.ManufacturerRepository.GetAll().AsNoTracking().Select(a => new StaticExcelManufacturerModel { Id = a.Id, Name = a.Name }).ToListAsync(),
                Categories = await unitOfWork.CategoryRepository.GetAll().AsNoTracking().Select(a => new StaticExcelCategoryModel { Id = a.Id, Name = a.Name }).ToListAsync()
            };

            _cacheService.Set(_cacheKey, staticExcelCache, CacheConstants.OneDay);

            return staticExcelCache;
        }

        private async Task<string> SaveExcelFileLocallyAsync(string url)
        {
            string fileName = FileHelper.RemoveInvalidChars(Path.GetFileName(url));
            string filePath = Path.Combine(Path.GetTempPath(), fileName);
            HttpClient client = HttpClientFactory.GetClient();

            using HttpResponseMessage response = await client.GetAsync(url);
            using (FileStream fileStream = new FileStream(filePath, FileMode.Create))
            {
                await response.Content.CopyToAsync(fileStream);
            }

            return filePath;
        }

        private void ValidateCodes(ProductsExcelParseModel excelParseData, StaticExcelCache staticExcelCache, IUnitOfWork unitOfWork)
        {
            List<string> quilityItemNames = staticExcelCache.QualityItems.Select(a => a.Name.Trim()).ToList();
            List<string> cisfbsCodes = staticExcelCache.Cisfbs.Select(a => a.Code.Trim()).ToList();
            List<string> externalMasterformatsCodes = staticExcelCache.Masterformats.Select(a => a.Code).ToList();
            List<string> omniclassesCodes = staticExcelCache.Omniclasses.Select(a => a.Code.Trim()).ToList();
            List<string> uniclassesCodes = staticExcelCache.Uniclasses.Select(a => a.Code.Trim()).ToList();
            List<string> uniformatsCodes = staticExcelCache.Uniformats.Select(a => a.Code.Trim()).ToList();
            List<string> projectDataTypeTitles = staticExcelCache.ProjectTypes.Select(a => a.Title).ToList();
            List<string> keyStatNames = staticExcelCache.Keystats.Select(a => a.Name).Distinct().ToList();

            //New logic
            foreach (ProductModel excelProduct in excelParseData.Products)
            {
                string id = excelProduct.Id.HasValue ? excelProduct.Id.Value.ToString() : string.Empty;

                if (excelProduct.Id.HasValue)
                {
                    Product productdb = unitOfWork.ProductRepository.GetById(excelProduct.Id.Value);
                    if (productdb is null)
                    {
                        excelProduct.ExcelParseErrors.Add($"Product Id: {id}. Can`t find product with current Id in Database");
                    }
                }

                if (string.IsNullOrWhiteSpace(excelProduct.Name))
                {
                    excelProduct.ExcelParseErrors.Add($"Product Id: {id}. Product Name can`t be empty");
                }

                //check quility names
                foreach (string name in excelProduct.ProductQualityItems)
                {
                    if (!quilityItemNames.Contains(name))
                    {
                        excelProduct.ExcelParseErrors.Add($"Wrong name of quility item: '{name}'");
                    }
                }
                //check cisfb codes
                foreach (string code in excelProduct.ProductCisfbs)
                {
                    if (!cisfbsCodes.Contains(code))
                    {
                        excelProduct.ExcelParseErrors.Add($"Wrong code of cisfb: '{code}'");
                    }
                }
                //check masterformat codes
                foreach (string masterformatCode in excelProduct.ProductExternalMasterformatCodes)
                {
                    if (!externalMasterformatsCodes.Contains(masterformatCode))
                    {
                        excelProduct.ExcelParseErrors.Add($"Wrong code of masterformat: '{masterformatCode}'");
                    }
                }
                //check omniclass codes
                foreach (string code in excelProduct.ProductOmniclasses)
                {
                    if (!omniclassesCodes.Contains(code))
                    {
                        excelProduct.ExcelParseErrors.Add($"Wrong code of omniclass: '{code}'");
                    }
                }
                //check uniclass codes
                foreach (string code in excelProduct.ProductUniclasses)
                {
                    if (!uniclassesCodes.Contains(code))
                    {
                        excelProduct.ExcelParseErrors.Add($"Wrong code of uniclass: '{code}'");
                    }
                }
                //check uniclass codes
                foreach (string code in excelProduct.ProductUniformats)
                {
                    if (!uniformatsCodes.Contains(code))
                    {
                        excelProduct.ExcelParseErrors.Add($"Wrong code of uniformat: '{code}'");
                    }
                }
                //check project data type titles
                foreach (string title in excelProduct.ProjectFiles.Select(x => x.FileType.Title))
                {
                    if (!projectDataTypeTitles.Contains(title))
                    {
                        excelProduct.ExcelParseErrors.Add($"Wrong title of project data type: '{title}'");
                    }
                }
                //check keyStat names
                foreach (string productStatName in excelProduct.ProductStats.Select(x => x.KeyStatName).Distinct())
                {
                    if (!keyStatNames.Contains(productStatName))
                    {
                        excelProduct.ExcelParseErrors.Add($"Wrong name of KeyStat: '{productStatName}'");
                    }
                }

                //check keyStatUnits
                foreach (ProductStatKeyStatUnitModel productStatKeyStatUnit in excelProduct.ProductStats.Where(ps => ps.KeyStatUnit != null).Select(ps => ps.KeyStatUnit).Distinct())
                {
                    if (staticExcelCache.KeystatUnits.FirstOrDefault(ksu => ksu.GroupName.Trim() == productStatKeyStatUnit.GroupName && ksu.BUnitName.Trim() == productStatKeyStatUnit.BUnitName) == null)
                    {
                        excelProduct.ExcelParseErrors.Add($"Wrong KeyStatUnit: GroupName='{productStatKeyStatUnit.GroupName}'; BUnitName='{productStatKeyStatUnit.BUnitName}'");
                    }
                }
            }
        }

        private async Task DeleteMessageFromQueueAsync(QueueClient queue, QueueMessage message)
        {
            await queue.DeleteMessageAsync(message.MessageId, message.PopReceipt);
        }

        private async Task CreateManufacturerBackupAsync(ProductsExcelParseModel excelParseData, IUnitOfWork unitOfWork)
        {
            List<int> manufacturerIds = excelParseData.Products.Select(a => a.ManufacturerId).Distinct().ToList();
            List<Manufacturer> manufacturers = await unitOfWork.ManufacturerRepository.GetAll().Where(a => manufacturerIds.Contains(a.Id)).ToListAsync();
            foreach (var manufacturer in manufacturers)
            {
                try
                {
                    if (!await _manufacturerBackupService.ManufacturerHasBackupForCurrentDataAsync(manufacturer.Id))
                    {
                        await _manufacturerBackupService.CreateManufacturerBackupAsync(manufacturer.Id);
                    }
                }
                catch (Exception)
                {
                }
            }
        }

        private void AddErrorProduct(ProductModel product, StaticExcelFile staticExcelFile, IUnitOfWork unitOfWork)
        {
            StaticExcelProductError staticExcelProductError = new StaticExcelProductError
            {
                CreatedById = staticExcelFile.CreatedById,
                Errors = JsonConvert.SerializeObject(product.ExcelParseErrors),
                RowNumber = product.RowIndex.Value,
                StaticExcelFileId = staticExcelFile.Id
            };
            unitOfWork.StaticExcelProductErrorRepository.Insert(staticExcelProductError);
        }

        private bool FileProcessingCompleted(StaticExcelFile staticExcelFile)
        {
            return staticExcelFile.Status != ExcelProcessingStatus.Cancelled
                && staticExcelFile.Status != ExcelProcessingStatus.ValidationFailed
                && staticExcelFile.TotalProductsCount == staticExcelFile.SucceedProdutcsCount + staticExcelFile.ErrorProductsCount + staticExcelFile.CancelledProductsCount;
        }

        private void SetExcelFileStatus(StaticExcelFile staticExcelFile, ExcelProcessingStatus status, string userId)
        {
            staticExcelFile.ModifiedById = userId;
            staticExcelFile.ModifiedDate = DateTime.UtcNow;
            staticExcelFile.Status = status;
        }
        #endregion
    }
}