﻿using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Quartz;
using Serilog;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Jobs
{
    [DisallowConcurrentExecution]
    public class StaticExcelProductProcessingJob : IJob
    {
        private readonly IStaticExcelImportService _staticExcelImportService;
        private readonly string _jobName = "Static Excel Product Processing Job";

        public StaticExcelProductProcessingJob(IStaticExcelImportService staticExcelImportService)
        {
            _staticExcelImportService = staticExcelImportService;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            Log.Information($"[{_jobName}] started");
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            await _staticExcelImportService.ProcessProductQueueAsync(unitOfWork);
        }
    }
}