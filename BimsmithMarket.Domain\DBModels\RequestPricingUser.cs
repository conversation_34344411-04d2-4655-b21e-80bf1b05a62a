﻿using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class RequestPricingUser : BaseEntity
    {
        public int ManufacturerId { get; set; }

        public int? ProductId { get; set; }

        [ForeignKey("ManufacturerId")]
        public virtual Manufacturer Manufacturer { get; set; }


        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }
    }
}