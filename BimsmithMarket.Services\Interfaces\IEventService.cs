﻿using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IEventService
    {
        Task<BaseAddEditResultDto> AddAsync(IUnitOfWork unitOfWork, AddEditEventDto model, string userId);

        Task<BaseAddEditResultDto> EditAsync(IUnitOfWork unitOfWork, AddEditEventDto model, string userId);

        Task DeleteAsync(IUnitOfWork unitOfWork, int id);

        Task<AdminGetEventDto> GetAsync(IUnitOfWork unitOfWork, int id);

        Task<PaginationListDto<AdminListEventDto>> AdminListAsync(IUnitOfWork unitOfWork, string q = null, int offset = 0, int count = 10);

        Task<PublicListEventDto[]> PublicListAsync(IUnitOfWork unitOfWork);
    }
}