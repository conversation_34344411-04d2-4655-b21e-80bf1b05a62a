﻿using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver.Linq;
using Quartz;
using Serilog;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Jobs
{
    [DisallowConcurrentExecution]
    public class PublishNewsAndBlogsByScheduleJob : IJob
    {
        private readonly IHealthDashboardService _healthDashboardService;
        private readonly string _jobName = "Publish News And Blogs By Schedule Job";

        public PublishNewsAndBlogsByScheduleJob(IHealthDashboardService healthDashboardService)
        {
            _healthDashboardService = healthDashboardService;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            Log.Information($"[{_jobName}] started");

            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            DateTime currentDate = DateTime.UtcNow;

            News[] newsPosts = await unitOfWork.NewsRepository.GetAll()
                .Where(x => x.PublishOption == PublishOption.Scheduled
                         && x.SchedulePublishDate != null
                         && x.SchedulePublishDate <= currentDate)
                .ToArrayAsync();

            foreach (News post in newsPosts)
            {
                post.PublishOption = PublishOption.Published;
                unitOfWork.NewsRepository.Edit(post);
            }

            BlogPost[] blogPosts = await unitOfWork.BlogPostRepository.GetAll()
                .Where(x => x.PublishOption == PublishOption.Scheduled
                         && x.SchedulePublishDate != null
                         && x.SchedulePublishDate <= currentDate)
                .ToArrayAsync();

            foreach (BlogPost post in blogPosts)
            {
                post.PublishOption = PublishOption.Published;
                unitOfWork.BlogPostRepository.Edit(post);
            }

            await unitOfWork.SaveAsync();
        }
    }
}