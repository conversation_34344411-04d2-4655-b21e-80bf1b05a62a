﻿using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.PaymentsDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IPaymentService
    {
        Task<UserPaidProductDto> CreateCheckoutSessionAsync(
            int productId,
            string userEmail,
            string userId,
            string serverFolder,
            string apiUrl,
            IUnitOfWork unitOfWork);

        Task StripeConfirmation(
             string sessionId,
             IUnitOfWork unitOfWork);

        Task StripeCancelation(
            string sessionId,
            IUnitOfWork unitOfWork);

        Task<OperationResultDto> EditUserSubscriptionAsync(
            SetUserSubscriptionDto model,
            string userId,
            IUnitOfWork unitOfWork);

        Task<List<CheckPaidFilesResultDto>> CheckSelectedFilesAsync(
            int[] productFileIds,
            IUnitOfWork unitOfWork,
            string userId = null,
            int? productId = null);
    }
}