﻿using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IChangeLogService
    {
        Task<PaginationListDto<AdminListChangeLogDto>> AdminListAsync(
            IUnitOfWork unitOfWork,
            EntityType entityType,
            int entityId,
            string query = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int offset = 0,
            int count = 10);

        Task<ChangeLogExcelDto[]> ExcelListAsync(
            IUnitOfWork unitOfWork,
            EntityType entityType,
            int entityId,
            DateTime? startDate = null,
            DateTime? endDate = null);

        Task TrackAndLogChangesAsync<T>(T currentModel, T newModel, EntityType entityType, int entityId, string userId, IUnitOfWork unitOfWork);

        Task LogEntityActionAsync(EntityType entityType, int entityId, EntityAction entityAction, string userId, IUnitOfWork unitOfWork);
    }
}