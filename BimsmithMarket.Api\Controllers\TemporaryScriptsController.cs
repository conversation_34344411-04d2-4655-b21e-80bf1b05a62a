using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Specialized;
using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.DBModels.PaymentDbModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.RevitProcessing;
using BIMsmithMarket.Domain.Dto.TempScriptsDto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Enums.RevitProcessing;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services;
using BIMsmithMarket.Services.HealthDashboardServices;
using BIMsmithMarket.Services.Interfaces;
using BIMsmithMarket.Services.Interfaces.RevitProcessing;
using BIMsmithMarket.Services.PaymentServices;
using BIMsmithMarket.Services.ProductServices;
using BIMsmithMarket.Services.Providers;
using Dasync.Collections;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using Flurl;
using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Claims;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// 
    /// </summary>
#if !DEBUG
    [Authorize(Roles = DbConstants.AdminRole)]
#endif
    [Route("api/[controller]/[action]")]
    public class TemporaryScriptsController : BaseApiController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ICategoryService _categoryService;
        private readonly IAzureStorageService _azureStorageService;
        private readonly IProductService _productService;
        private readonly IManufacturerBackupFileService _manufacturerBackupFileService;
        private readonly IRevitProcessingService _revitProcessingService;

        public TemporaryScriptsController(
            IHttpClientFactory httpClientFactory,
            ICategoryService categoryService,
            IAzureStorageService azureStorageService,
            IProductService productService,
            IManufacturerBackupFileService manufacturerBackupFileService,
            IRevitProcessingService revitProcessingService)
        {
            _httpClientFactory = httpClientFactory;
            _categoryService = categoryService;
            _azureStorageService = azureStorageService;
            _productService = productService;
            _manufacturerBackupFileService = manufacturerBackupFileService;
            _revitProcessingService = revitProcessingService;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        [Authorize(Roles = DbConstants.AdminRole)]
        [HttpGet]
        public async Task<IActionResult> CleanCategoryKeyWords()
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var categories = unitOfWork.CategoryRepository.GetAll().ToList();
                int totalCount = categories.Count();
                int done = 0;
                foreach (var category in categories)
                {
                    if (category.Keywords != null)
                    {
                        category.Keywords = null;
                        unitOfWork.CategoryRepository.Edit(category);
                        try
                        {
                            await unitOfWork.SaveAsync();
                            done++;
                            Debug.WriteLine($"Total = {totalCount}, Done= {done}");
                        }
                        catch (Exception)
                        {
                            return BadRequest($"Cannot find manufacturer with id: {category.Id}");
                        }
                    }
                }
                return Ok($"Well done. Total {totalCount} fixed = {done}");
            }
        }

        [HttpGet]
        public async Task<IActionResult> TestManufacturer()
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var manufacturer = unitOfWork.ManufacturerRepository.GetById(161);
                var products = manufacturer.Products.ToArray();
                var productfiles = products.SelectMany(x => x.ProductFiles.Where(c => !c.IsAttachment)).ToArray();
                var files = productfiles.Select(x => x.File).ToArray();

            }
            return Ok();
        }

        [HttpGet]
        public async Task<IActionResult> SetDefaultPaymentPlanAndPricesAsync()
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var defpaymentPlanId = unitOfWork.PaymentPlanRepository.GetAll().FirstOrDefault(x => x.Title == "DefaultFree").Id;
                var products = unitOfWork.ProductRepository.GetAll().Select(x => x.Id).ToList();

                unitOfWork.BeginTransaction();
                var cout = 0;
                try
                {
                    foreach (var p in products)
                    {
                        Debug.WriteLine($"current {cout} of {products.Count}");
                        var newProductPrice = new ProductPrice
                        {
                            PaymentPlanId = defpaymentPlanId,
                            ProductId = p
                        };
                        unitOfWork.ProductPriceRepository.Insert(newProductPrice);
                        cout++;
                    }
                    await unitOfWork.SaveAsync();
                    unitOfWork.CommitTransaction();
                }
                catch (Exception)
                {
                    unitOfWork.RollbackTransaction();
                }


                unitOfWork.BeginTransaction();
                cout = 0;
                try
                {
                    foreach (var p in products)
                    {
                        Debug.WriteLine($"current {cout} of {products.Count}");
                        var newPrice = new Price
                        {
                            Amount = 0,
                            PriceType = Domain.Enums.PriceType.Free,
                            ProductId = p
                        };
                        unitOfWork.PriceRepository.Insert(newPrice);
                        cout++;
                    }
                    await unitOfWork.SaveAsync();
                    unitOfWork.CommitTransaction();
                }
                catch (Exception)
                {
                    unitOfWork.RollbackTransaction();
                }
            }
            return Ok();
        }


        [HttpGet]
        public IActionResult CheckCategoryIcons()
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var categories = unitOfWork.CategoryRepository.GetAll().ToList();
                int changed = 0;

                foreach (var cat in categories)
                {
                    string defaultPath = @"/assets/img/categoryIcons/category_cube_ico.png";
                    if (cat.IconUrl.Contains("assets"))
                    {
                        string path = string.Empty;//System.Web.HttpContext.Current.Server.MapPath(cat.IconUrl);
                        var exists = System.IO.File.Exists(path);
                        if (!exists)
                        {
                            cat.IconUrl = defaultPath;
                            unitOfWork.CategoryRepository.Edit(cat);
                            unitOfWork.Save();
                            changed++;
                        }
                    }
                    else
                    {

                    }
                }

                return Ok(new { changed = changed });
            }
        }


#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        public async Task<IActionResult> UpdateRevitVersionAsync()
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var revitFiles = unitOfWork.ProductFileRepository.GetAll().Where(x => !x.IsAttachment
                                                                                  && x.ProjectDataType.Title == "Revit"
                                                                                  && string.IsNullOrEmpty(x.SoftwareRelease)
                                                                                  && x.SoftwareVersion == null)
                                                                          .ToList();
                var setFiles = unitOfWork.ProductFileRepository.GetAll().Where(x => !x.IsAttachment
                                                                                  && x.ProjectDataType.Title == "Revit"
                                                                                  && !string.IsNullOrEmpty(x.SoftwareRelease)
                                                                                  && x.SoftwareVersion == null)
                                                                          .ToList();

                AzureStorageService provider = new AzureStorageService(
                    ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                    ConfigurationHelper.GetValue("Environment"),
                    bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                    false);
                var fileContainer = provider.GetContainerByName(AzureStorageConstants.FilesContainer);

                var projectDataTypes = unitOfWork.ProjectDataTypeRepository.GetAll().Where(x => x.ParentId == 1).ToList();
                if (!projectDataTypes.Any())
                    return Forbid("No revit file types");

                if (setFiles.Any())
                {
                    unitOfWork.BeginTransaction();

                    foreach (ProductFile rFile in setFiles)
                    {
                        var softwareVersionId = projectDataTypes.FirstOrDefault(x => x.Title.Contains(rFile.SoftwareRelease))?.Id;
                        rFile.SoftwareVersionId = softwareVersionId;
                        unitOfWork.ProductFileRepository.Edit(rFile);
                    }
                    await unitOfWork.SaveAsync();
                    unitOfWork.CommitTransaction();
                }


                unitOfWork.BeginTransaction();
                var test = revitFiles.Where(x => x.File != null && string.IsNullOrEmpty(x.File.Url)).ToList();
                revitFiles = revitFiles.Where(x => x.File != null && !string.IsNullOrEmpty(x.File.Url) && (x.File.Url.Contains("rvt") || x.File.Url.Contains("rfa"))).ToList();
                int totalcount = revitFiles.Count();
                int current = 0;
                foreach (ProductFile rFile in revitFiles)
                {
                    current++;
                    Debug.WriteLine($"current= {current} of {totalcount}");
                    if (rFile.File?.Url == null)
                        continue;

                    BlockBlobClient fileBlob = provider.GetBlob(rFile.File.Url, fileContainer);
                    if (!fileBlob.Exists())
                        continue;
                    string text = "";
                    using (MemoryStream stream = new MemoryStream())
                    {
                        await fileBlob.DownloadToAsync(stream);
                        byte[] streamAsBytes = stream.GetBuffer();
                        text = Encoding.UTF8.GetString(streamAsBytes);
                    }

                    if (text.Contains("<A:product-version>") && text.Contains("</A:product-version>"))
                    {
                        int pFrom = text.IndexOf("<A:product-version>") + "<A:product-version>".Length;
                        int pTo = text.LastIndexOf("</A:product-version>");
                        string version = text.Substring(pFrom, pTo - pFrom);

                        var needProjectDataType = projectDataTypes.FirstOrDefault(x => x.Title.Contains(version));
                        if (needProjectDataType == null)
                            continue;

                        rFile.SoftwareVersionId = needProjectDataType.Id;
                        rFile.SoftwareRelease = version;
                        unitOfWork.ProductFileRepository.Edit(rFile);
                    }
                }
                await unitOfWork.SaveAsync();
                unitOfWork.CommitTransaction();
            }
            return Ok();
        }



        [Authorize(Roles = DbConstants.AdminRole)]
        [HttpGet]
        public IActionResult ImportDataFormExcel(int fileId = -1, string filePath = null)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                unitOfWork.BeginTransaction();

                if (fileId != -1)
                {
                    var file = unitOfWork.FileRepository.GetById(fileId);
                    string folder = Path.GetTempPath();
                    filePath = Path.Combine(folder, file.Id.ToString() + Path.GetExtension(file.FileName));
                }

                int spreadsheet = 1;
                if (filePath.Contains("omni"))
                {
                    spreadsheet = 3;
                }

                var excelParseData = ExcelProductsProvider.ParseAllExcel(filePath, spreadsheet);
                int totalCount = excelParseData.Count;
                int done = 0;

                foreach (var row in excelParseData)
                {
                    try
                    {
                        // ADD OMNICLASSES

                        if (!row.First().Contains("-"))
                        {
                            continue;
                        }

                        if (row.Count > 1)
                        {
                            var code = row.First(a => !string.IsNullOrWhiteSpace(a)).Trim();
                            var title = row.Last(a => !string.IsNullOrWhiteSpace(a)).Trim();
                            var searchCode = code.Replace(" 00 00", "").Replace(" 00", "");
                            var parent = unitOfWork.OmniclassRepository.GetAll().ToList().Where(a => searchCode.StartsWith(a.Code.Replace(" 00", "")) || searchCode.StartsWith(a.Code.Replace(" 00 00", ""))).LastOrDefault();
                            var omniclass = new Omniclass
                            {
                                Code = code,
                                Title = title,
                                ParentId = parent?.Id
                            };

                            unitOfWork.OmniclassRepository.Insert(omniclass);
                            unitOfWork.Save();
                        }

                        done++;
                        Debug.WriteLine($"Total = {totalCount}, Done = {done}");
                    }
                    catch (Exception)
                    {
                        unitOfWork.RollbackTransaction();
                        return StatusCode(StatusCodes.Status500InternalServerError);
                    }
                }

                unitOfWork.CommitTransaction();
                return Ok($"Well done. Total {totalCount} fixed = {done}");
            }
        }

#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        public async Task<IActionResult> RestoreProjectDataTypes()
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var projectDataTypeToFileExtensionMapping = new Dictionary<ProjectDataType, List<string>>
                {
                    { unitOfWork.ProjectDataTypeRepository.GetAll().FirstOrDefault(x => x.Title == "AutoCAD"), new List<string> { ".dwg" } }
                };

                var projectFiles = unitOfWork.ProductFileRepository.GetAll()
                                                                   .Where(x => !x.IsAttachment
                                                                            && x.ProjectDataTypeId == null)
                                                                   .ToList();

                var totalFiles = projectFiles.Count;
                var processedFiles = 0;

                unitOfWork.BeginTransaction();
                foreach (var projectFile in projectFiles)
                {
                    var projectDataType = projectDataTypeToFileExtensionMapping.FirstOrDefault(x => x.Value.Contains(Path.GetExtension(projectFile.File.FileName))).Key;
                    if (projectDataType != null)
                    {
                        projectFile.ProjectDataTypeId = projectDataType.Id;
                        unitOfWork.ProductFileRepository.Edit(projectFile);
                        processedFiles++;
                        Debug.WriteLine($"{processedFiles} of {totalFiles} is being processed");
                    }
                }
                await unitOfWork.SaveAsync();
                unitOfWork.CommitTransaction();
                Debug.WriteLine($"Processing finished");
                return Ok(new
                {
                    totalFiles,
                    processedFiles
                });
            }
        }

#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        [ActionName("PushAttachmentsForReload")]
        public async Task<IActionResult> PushAttachmentsForReloadAsync(int manufacturerId)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                AzureStorageService azureBlobProvider = new AzureStorageService(
                    ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                    ConfigurationHelper.GetValue("Environment"),
                    bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                    false);
                var fileUrlsQueue = azureBlobProvider.GetQueueByName(AzureStorageConstants.ProductFileUrlsQueue);

                ////Find all product attachments
                var productIds = await unitOfWork.ProductRepository.GetAll().Where(x => x.ManufacturerId == manufacturerId).Select(x => x.Id).ToArrayAsync();
                var productFiles = await unitOfWork.ProductFileRepository.GetAll().Where(x => x.IsAttachment && productIds.Contains(x.ProductId)).ToArrayAsync();
                var productFileIds = productFiles.Select(x => x.FileId).Distinct().ToArray();

                //Find all product line attachments
                var productLineIds = await unitOfWork.ProductLineRepository.GetAll().Where(x => x.ManufacturerId == manufacturerId).Select(x => x.Id).ToArrayAsync();
                var productLineFiles = await unitOfWork.ProductLineFileRepository.GetAll().Where(x => x.IsAttachment && productLineIds.Contains(x.ProductLineId)).ToArrayAsync();
                var productLineFileIds = productLineFiles.Select(x => x.FileId).Distinct().ToArray();

                //Find all manufacturer attachments
                var manufacturerFiles = await unitOfWork.ManufacturerFileRepository.GetAll().Where(x => x.IsAttachment && x.ManufacturerId == manufacturerId).ToArrayAsync();
                var manufacturerFileIds = manufacturerFiles.Select(x => x.FileId).Distinct().ToArray();

                var fileIds = productFileIds.Concat(productLineFileIds).Concat(manufacturerFileIds).ToArray();
                foreach (var fileId in fileIds)
                {
                    fileUrlsQueue.SendMessage(fileId.ToString());
                }
            }

            return Ok();
        }

        /// <summary>
        /// Generates middle photo and regenerate small photos
        /// </summary>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        public async Task<IActionResult> RegeneratePhotos()
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                int qualityCompressionPercentage = 70;
                unitOfWork.CurrentDbContext.ChangeTracker.AutoDetectChangesEnabled = false;

                var detailPhotoIds = unitOfWork.DetailRepository.GetAll()
                                               .Where(x => x.PhotoId != null)
                                               .Select(x => x.PhotoId.Value);

                var detailPhotoPhotoIds = unitOfWork.DetailPhotoRepository.GetAll()
                                                    .Select(x => x.PhotoId);

                var manufacturerPhotoIds = unitOfWork.ManufacturerRepository.GetAll()
                                                     .Where(x => x.PhotoId != null)
                                                     .Select(x => x.PhotoId.Value);

                var manufacturerImageIds = unitOfWork.ManufacturerRepository.GetAll()
                                                     .Where(x => x.ImageId != null)
                                                     .Select(x => x.ImageId.Value);

                var manufacturerFooterImageIds = unitOfWork.ManufacturerRepository.GetAll()
                                                           .Where(x => x.FooterAdImageId != null)
                                                           .Select(x => x.FooterAdImageId.Value);

                var manufacturerCustomSignInImageIds = unitOfWork.ManufacturerRepository.GetAll()
                                                                 .Where(x => x.CustomSignInLogoImageId != null)
                                                                 .Select(x => x.CustomSignInLogoImageId.Value);

                var manufacturerPhotoPhotoIds = unitOfWork.ManufacturerPhotoRepository.GetAll()
                                                          .Select(x => x.PhotoId);

                var productPhotoIds = unitOfWork.ProductRepository.GetAll()
                                                .Where(x => x.PhotoId != null)
                                                .Select(x => x.PhotoId.Value);

                var productFooterAdImageIds = unitOfWork.ProductRepository.GetAll()
                                                        .Where(x => x.FooterAdImageId != null)
                                                        .Select(x => x.FooterAdImageId.Value);

                var productPhotoPhotoIds = unitOfWork.ProductPhotoRepository.GetAll()
                                                     .Select(x => x.PhotoId);

                var projectDataTypeImageIds = unitOfWork.ProjectDataTypeRepository.GetAll()
                                                        .Where(x => x.ImageId != null)
                                                        .Select(x => x.ImageId.Value);

                var projectDataTypeMicrositeImageIds = unitOfWork.ProjectDataTypeRepository.GetAll()
                                                                 .Where(x => x.MicrositeImageId != null)
                                                                 .Select(x => x.MicrositeImageId.Value);

                var projectDataTypeIconIds = unitOfWork.ProjectDataTypeRepository.GetAll()
                                                       .Where(x => x.IconId != null)
                                                       .Select(x => x.IconId.Value);

                var projectDataTypeActiveImageIds = unitOfWork.ProjectDataTypeRepository.GetAll()
                                                              .Where(x => x.ActiveImageId != null)
                                                              .Select(x => x.ActiveImageId.Value);

                var projectDataTypeDefaultImageIds = unitOfWork.ProjectDataTypeRepository.GetAll()
                                                               .Where(x => x.DefaultImageId != null)
                                                               .Select(x => x.DefaultImageId.Value);

                var photoIds = await detailPhotoIds
                    .Concat(detailPhotoPhotoIds)
                    .Concat(manufacturerPhotoIds)
                    .Concat(manufacturerImageIds)
                    .Concat(manufacturerFooterImageIds)
                    .Concat(manufacturerCustomSignInImageIds)
                    .Concat(manufacturerPhotoPhotoIds)
                    .Concat(productPhotoIds)
                    .Concat(productFooterAdImageIds)
                    .Concat(productPhotoPhotoIds)
                    .Concat(projectDataTypeImageIds)
                    .Concat(projectDataTypeMicrositeImageIds)
                    .Concat(projectDataTypeIconIds)
                    .Concat(projectDataTypeActiveImageIds)
                    .Concat(projectDataTypeDefaultImageIds)
                    .Distinct()
                    .ToArrayAsync();

                var photos = await unitOfWork.PhotoRepository.GetAll()
                                             .Where(x => photoIds.Contains(x.Id)
                                                      && (x.MiddleImgUrl == null
                                                      || x.MiddleImgUrl == string.Empty))
                                             .ToArrayAsync();

                var totalCount = photos.Count();
                var processed = 0;
                var failed = 0;
                var counter = 1;

                var azureBlobProvider = new AzureStorageService(
                    ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                    ConfigurationHelper.GetValue("Environment"),
                    bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                    false);
                var photosContainer = azureBlobProvider.GetContainerByName(AzureStorageConstants.PhotosContainer);

                foreach (var photo in photos)
                {
                    Debug.WriteLine($"{counter} of {totalCount} is being processed");
                    try
                    {
                        var httpClient = _httpClientFactory.CreateClient();
                        {
                            using var result = await httpClient.GetAsync(photo.OriginalImgUrl);
                            if (result.IsSuccessStatusCode)
                            {
                                var fileStream = await result.Content.ReadAsStreamAsync();
                                var originalImage = new Bitmap(fileStream);
                                double scaleMiddle = Math.Max(originalImage.Height, originalImage.Width) / 330d; //max side is 330px for middle image
                                double scaleSmall = Math.Max(originalImage.Height, originalImage.Width) / 30d; //max side is 250px for small image

                                Image middleImage = new Bitmap(originalImage, new Size((int)(originalImage.Width / scaleMiddle), (int)(originalImage.Height / scaleMiddle)));

                                Image smallImage = new Bitmap(originalImage, new Size((int)(originalImage.Width / scaleSmall), (int)(originalImage.Height / scaleSmall)));

                                string extension = ".png";
                                string imageName = Path.GetFileName(photo.OriginalImgUrl);

                                if (imageName != null)
                                {
                                    Regex rgx = new Regex("[^a-zA-Z0-9 -]");
                                    var normalImageName = rgx.Replace(imageName, "");
                                    imageName = normalImageName + photo.Id.ToString();
                                }
                                else
                                {
                                    imageName = photo.Id.ToString();
                                }

                                var middleImageBlobName = string.Format("{0}_m{1}", imageName, extension);
                                var smallImageBlobName = string.Format("{0}_s{1}", imageName, extension);

                                //Options for compression of middle and small images
                                ImageCodecInfo jpegCodec = PhotoProvider.GetEncoder(ImageFormat.Jpeg);
                                EncoderParameters encoderParameters = new EncoderParameters(1);
                                EncoderParameter qualityParameter = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, qualityCompressionPercentage);
                                encoderParameters.Param[0] = qualityParameter;

                                var middleImageWidth = middleImage.Width;
                                var middleImageHeight = middleImage.Height;

                                using (var b = new Bitmap(middleImageWidth, middleImageHeight))
                                {
                                    b.SetResolution(middleImage.HorizontalResolution, middleImage.VerticalResolution);

                                    using (var g = Graphics.FromImage(b))
                                    {
                                        g.Clear(System.Drawing.Color.White);
                                        int x = 0;
                                        int y = 0;
                                        g.DrawImageUnscaled(middleImage, x, y);
                                    }
                                    BlockBlobClient middleImageBlobUpload = photosContainer.GetBlockBlobClient(middleImageBlobName);
                                    using (var stream = await middleImageBlobUpload.OpenWriteAsync(true))
                                    {
                                        b.Save(stream, jpegCodec, encoderParameters);
                                    }
                                    photo.MiddleImgUrl = middleImageBlobUpload.Uri.ToString();
                                }

                                var smallImageWidth = smallImage.Width;
                                var smallImageHeight = smallImage.Height;

                                using (var b = new Bitmap(smallImageWidth, smallImageHeight))
                                {
                                    b.SetResolution(smallImage.HorizontalResolution, smallImage.VerticalResolution);

                                    using (var g = Graphics.FromImage(b))
                                    {
                                        g.Clear(System.Drawing.Color.White);
                                        int x = 0;
                                        int y = 0;
                                        g.DrawImageUnscaled(smallImage, x, y);
                                    }
                                    BlockBlobClient smallImageBlobUpload = photosContainer.GetBlockBlobClient(smallImageBlobName);
                                    using (var stream = await smallImageBlobUpload.OpenWriteAsync(true))
                                    {
                                        b.Save(stream, jpegCodec, encoderParameters);
                                    }
                                    photo.SmallImgUrl = smallImageBlobUpload.Uri.ToString();
                                }

                                middleImage.Dispose();
                                smallImage.Dispose();
                                unitOfWork.PhotoRepository.Edit(photo);
                                await unitOfWork.SaveAsync();
                                processed++;
                                Debug.WriteLine($"Passed for photo {photo.Id}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Failed for photo {photo.Id}. Exception {ex.Message}");
                        failed++;
                    }
                    counter++;
                }

                return Ok(new
                {
                    totalCount,
                    processed,
                    failed
                });
            }
        }

        /// <summary>
        /// Moves icons from assets to blob
        /// </summary>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        public async Task<IActionResult> MoveIconsFromAssetsToBlob()
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                List<Category> categories = await unitOfWork.CategoryRepository.GetAll()
                                                            .Where(x => x.IconUrl.StartsWith("/assets")
                                                                     || x.IconUrl.StartsWith("./assets"))
                                                            .ToListAsync();

                List<QualityItem> qualityItems = await unitOfWork.QualityItemRepository.GetAll()
                                                                 .Where(x => x.IconUrl.StartsWith("/assets")
                                                                          || x.IconUrl.StartsWith("./assets"))
                                                                 .ToListAsync();

                List<string> categoryIconUrls = categories.Select(x => x.IconUrl).Distinct().ToList();
                List<string> qualityItemIconUrls = qualityItems.Select(x => x.IconUrl).Distinct().ToList();
                string marketProdBaseAddress = "https://market.bimsmith.com/";
                AzureStorageService azureBlobProvider = new AzureStorageService(
                    ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                    ConfigurationHelper.GetValue("Environment"),
                    bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                    false);
                BlobContainerClient filesContainer = azureBlobProvider.GetContainerByName(AzureStorageConstants.IconsContainer);

                int categoryTotalCount = categories.Count();
                int categoriesProcessed = 0;
                int categoriesFailed = 0;
                int counter = 1;

                foreach (string iconUrl in categoryIconUrls)
                {
                    int itemsWithCurrentIconUrl = categories.Count(x => x.IconUrl == iconUrl);
                    try
                    {
                        counter += itemsWithCurrentIconUrl;
                        string fullUrl = new Url(marketProdBaseAddress).AppendPathSegment(iconUrl.Replace("./assets", "/assets"));
                        string fileName = Path.GetFileName(iconUrl);
                        string blobIconUrl = await UploadIconAsync(fullUrl, fileName, filesContainer);

                        if (string.IsNullOrWhiteSpace(blobIconUrl))
                        {
                            Debug.WriteLine($"Failed for category url {iconUrl}");
                            categoriesFailed += itemsWithCurrentIconUrl;
                            continue;
                        }

                        List<Category> categoriesWithCurrentIcon = categories.Where(x => x.IconUrl == iconUrl).ToList();
                        categoriesWithCurrentIcon.ForEach(x => { x.IconUrl = blobIconUrl; unitOfWork.CategoryRepository.Edit(x); });
                        await unitOfWork.SaveAsync();
                        categoriesProcessed += itemsWithCurrentIconUrl;
                        Debug.WriteLine($"Passed for category url {iconUrl}");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Failed for category url {iconUrl}. Exception {ex.Message}");
                        categoriesFailed += itemsWithCurrentIconUrl;
                    }
                    finally
                    {
                        Debug.WriteLine($"{counter} of {categoryTotalCount} categories are processed");
                    }
                }

                int qualityItemTotalCount = qualityItems.Count();
                int qualityItemsProcessed = 0;
                int qualityItemsFailed = 0;
                counter = 1;

                foreach (string iconUrl in qualityItemIconUrls)
                {
                    int itemsWithCurrentIconUrl = qualityItems.Count(x => x.IconUrl == iconUrl);
                    try
                    {
                        counter += itemsWithCurrentIconUrl;
                        string fullUrl = new Url(marketProdBaseAddress).AppendPathSegment(iconUrl.Replace("./assets", "/assets"));
                        string fileName = Path.GetFileName(iconUrl);
                        string blobIconUrl = await UploadIconAsync(fullUrl, fileName, filesContainer);

                        if (string.IsNullOrWhiteSpace(blobIconUrl))
                        {
                            Debug.WriteLine($"Failed for quality item url {iconUrl}");
                            qualityItemsFailed += itemsWithCurrentIconUrl;
                            continue;
                        }

                        List<QualityItem> qualityItemsWithCurrentIcon = qualityItems.Where(x => x.IconUrl == iconUrl).ToList();
                        qualityItemsWithCurrentIcon.ForEach(x => { x.IconUrl = blobIconUrl; unitOfWork.QualityItemRepository.Edit(x); });
                        await unitOfWork.SaveAsync();
                        qualityItemsProcessed += itemsWithCurrentIconUrl;
                        Debug.WriteLine($"Passed for quality item url {iconUrl}");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Failed for quality item url {iconUrl}. Exception {ex.Message}");
                        qualityItemsFailed += itemsWithCurrentIconUrl;
                    }
                    finally
                    {
                        Debug.WriteLine($"{counter} of {qualityItemTotalCount} quality items are processed");
                    }
                }

                return Ok(new
                {
                    categoryTotalCount,
                    categoriesProcessed,
                    categoriesFailed,
                    qualityItemTotalCount,
                    qualityItemsProcessed,
                    qualityItemsFailed
                });
            }
        }

        [HttpGet]
        public async Task<IActionResult> ReloadBrokenPdf()
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                AzureStorageService azureBlobProvider = new AzureStorageService(
                    ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                    ConfigurationHelper.GetValue("Environment"),
                    bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                    false);
                var fileUrlsQueue = azureBlobProvider.GetQueueByName(AzureStorageConstants.ProductFileUrlsQueue);

                ////Find all product pdfs
                var productIds = await unitOfWork.ProductRepository.GetAll().Select(x => x.Id).ToArrayAsync();
                var productFileIds = await unitOfWork.ProductFileRepository.GetAll()
                                                     .Where(x => productIds.Contains(x.ProductId)
                                                              && x.File.SyncUrl != null
                                                              && x.File.MediaType.Contains("pdf"))
                                                     .Select(x => x.FileId)
                                                     .Distinct()
                                                     .OrderBy(x => x)
                                                     .ToArrayAsync();
                int i = 0;
                foreach (var fileId in productFileIds)
                {
                    fileUrlsQueue.SendMessage(fileId.ToString());
                    Debug.WriteLine($"{++i} is processed");
                }
            }
            return Ok();
        }

#if !Release
        [AllowAnonymous]
#endif
        [HttpGet]
        public async Task<IActionResult> UpdateMongo()
        {
            ProductService productService = new ProductService(new PriceService(new FileService()), new MongoRepository<ProductMongoDto>(), new CacheService(), new SlackWebHook(), new HealthDashboardService(), new FileService());
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                int[] productIds = await unitOfWork.ProductRepository.GetAll().Select(x => x.Id).ToArrayAsync();
                foreach (int productId in productIds)
                    await productService.SaveProductToMongoAsync(productId, false, unitOfWork);
            }
            return Ok();
        }

#if DEBUG
        [AllowAnonymous]
#endif
        [HttpGet]
        public async Task<IActionResult> ExportUsers()
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            UserExcelExportDto[] users = await unitOfWork.UserRepository.GetAll()
                .Select(x => new UserExcelExportDto
                {
                    Email = x.Email
                })
                .ToArrayAsync();

            string tempFolder = Path.GetTempPath();
            string filePath = Path.Combine(tempFolder, $"All_users_{Guid.NewGuid()}.xlsx");
            SpreadsheetDocument spreadsheetDocument;
            WorkbookPart workbookpart;
            WorksheetPart worksheetPart;
            CommonExcelProvider.CreateDocument(filePath, out spreadsheetDocument, out workbookpart, out worksheetPart);
            Sheets sheets;

            // Add a WorksheetPart to the WorkbookPart.
            WorksheetPart usersWorksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            usersWorksheetPart.Worksheet = new Worksheet();

            //columns
            var userColumnsCount = typeof(UserExcelExportDto).GetProperties().Length;
            Columns userColumns = new Columns();

            for (uint userColumnIndex = 1; userColumnIndex < userColumnsCount + 1; userColumnIndex++)
            {
                userColumns.AppendChild(new Column { Min = userColumnIndex, Max = userColumnIndex++, Width = 25, CustomWidth = true });
            }
            usersWorksheetPart.Worksheet.AppendChild(userColumns);
            sheets = workbookpart.Workbook.AppendChild(new Sheets());
            workbookpart.Workbook.Save();

            Sheet usersSheet = new Sheet
            {
                Id = spreadsheetDocument.WorkbookPart.
                GetIdOfPart(usersWorksheetPart),
                SheetId = 1,
                Name = "Users"
            };
            sheets.Append(usersSheet);
            workbookpart.Workbook.Save();

            // Get the sheetData cell table.
            SheetData usersSheetData = usersWorksheetPart.Worksheet.AppendChild(new SheetData());

            int usersRowIndex = 1;
            Row usersRowHeader = new Row();
            usersSheetData.AppendChild(usersRowHeader);

            var usersColumnIndex = 1;

            usersRowHeader.AppendChild(CommonExcelProvider.ConstructCell("Email", CellValues.String, usersColumnIndex++, usersRowIndex, 2));

            usersRowIndex++;

            foreach (var user in users)
            {
                Row row = new Row();
                usersSheetData.AppendChild(row);

                usersColumnIndex = 1;

                row.AppendChild(CommonExcelProvider.ConstructCell(user.Email, CellValues.String, usersColumnIndex++, usersRowIndex));

                usersRowIndex++;
            }

            workbookpart.Workbook.Save();
            spreadsheetDocument.Dispose();

            return PhysicalFile(filePath, "text/csv", "Market users.xlsx");
        }

#if DEBUG
        [AllowAnonymous]
#endif
        [HttpGet]
        public async Task<IActionResult> ExportProductsWithMasterspec()
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            TempScriptProductWithMasterspecDto[] products = await unitOfWork.ProductRepository.GetAll()
                .Where(x => x.ProductFiles.Any(f => f.IsAttachment && f.File.SyncUrl.ToLower().Contains(AttachmentConstants.MasterspecUrl)))
                .Select(x => new TempScriptProductWithMasterspecDto
                {
                    ProductId = x.Id,
                    ProductName = x.Name,
                    ManufacturerId = x.ManufacturerId,
                    ManufacturerName = x.Manufacturer.Name
                })
                .OrderBy(x => x.ManufacturerId)
                .ThenBy(x => x.ProductId)
                .ToArrayAsync();

            string baseMarketUrl = ConfigurationHelper.GetValue("MarketFrontBaseUrl");
            foreach (TempScriptProductWithMasterspecDto product in products)
            {
                product.ProductPublicPage = new Url(baseMarketUrl).AppendPathSegment($"/product/{product.ProductId}").ToString();
                product.ProductAdminPanelPage = new Url(baseMarketUrl).AppendPathSegment($"adminpanel/{product.ProductId}/edit/{product.ManufacturerId}").ToString();
            }

            string tempFolder = Path.GetTempPath();
            string filePath = Path.Combine(tempFolder, $"Products With Masterspec_{Guid.NewGuid()}.xlsx");
            SpreadsheetDocument spreadsheetDocument;
            WorkbookPart workbookpart;
            WorksheetPart worksheetPart;
            CommonExcelProvider.CreateDocument(filePath, out spreadsheetDocument, out workbookpart, out worksheetPart);
            Sheets sheets;

            // Add a WorksheetPart to the WorkbookPart.
            WorksheetPart usersWorksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            usersWorksheetPart.Worksheet = new Worksheet();

            //columns
            var userColumnsCount = typeof(UserExcelExportDto).GetProperties().Length;
            Columns userColumns = new Columns();

            for (uint userColumnIndex = 1; userColumnIndex < userColumnsCount + 1; userColumnIndex++)
            {
                userColumns.AppendChild(new Column { Min = userColumnIndex, Max = userColumnIndex++, Width = 25, CustomWidth = true });
            }
            usersWorksheetPart.Worksheet.AppendChild(userColumns);
            sheets = workbookpart.Workbook.AppendChild(new Sheets());
            workbookpart.Workbook.Save();

            Sheet usersSheet = new Sheet
            {
                Id = spreadsheetDocument.WorkbookPart.
                GetIdOfPart(usersWorksheetPart),
                SheetId = 1,
                Name = "Products"
            };
            sheets.Append(usersSheet);
            workbookpart.Workbook.Save();

            // Get the sheetData cell table.
            SheetData usersSheetData = usersWorksheetPart.Worksheet.AppendChild(new SheetData());

            int rowIndex = 1;
            Row headerRow = new Row();
            usersSheetData.AppendChild(headerRow);

            int columnIndex = 1;

            headerRow.AppendChild(CommonExcelProvider.ConstructCell("Manufacturer Id", CellValues.String, columnIndex++, rowIndex, 2));
            headerRow.AppendChild(CommonExcelProvider.ConstructCell("Manufacturer Name", CellValues.String, columnIndex++, rowIndex, 2));
            headerRow.AppendChild(CommonExcelProvider.ConstructCell("Product Id", CellValues.String, columnIndex++, rowIndex, 2));
            headerRow.AppendChild(CommonExcelProvider.ConstructCell("Product Name", CellValues.String, columnIndex++, rowIndex, 2));
            headerRow.AppendChild(CommonExcelProvider.ConstructCell("Product Admin Panel Page", CellValues.String, columnIndex++, rowIndex, 2));
            headerRow.AppendChild(CommonExcelProvider.ConstructCell("Product Front Page", CellValues.String, columnIndex++, rowIndex, 2));

            rowIndex++;

            foreach (TempScriptProductWithMasterspecDto product in products)
            {
                Row row = new Row();
                usersSheetData.AppendChild(row);

                columnIndex = 1;

                row.AppendChild(CommonExcelProvider.ConstructCell(product.ManufacturerId.ToString(), CellValues.Number, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.ManufacturerName, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.ProductId.ToString(), CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.ProductName, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.ProductAdminPanelPage, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.ProductPublicPage, CellValues.String, columnIndex++, rowIndex));

                rowIndex++;
            }

            workbookpart.Workbook.Save();
            spreadsheetDocument.Dispose();

            return PhysicalFile(filePath, "text/csv", "Products with masterspec.xlsx");
        }

        [HttpGet]
#if DEBUG
        [AllowAnonymous]
#endif
        public async Task<IActionResult> CheckFileDownloadsLinks()
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            string filePath = @"C:\\Work Folder\\Cadrevit-data.json";
            string fileContent = System.IO.File.ReadAllText(filePath);
            string pattern = "\"https://api-market.bimsmith.com/api/File/Download.{49}\"";
            Regex regex = new Regex(pattern, RegexOptions.IgnoreCase);
            IEnumerable<string> matches = regex.Matches(fileContent).Select(x => x.Value.Trim('"')).Distinct();
            Debug.WriteLine($"Total count {matches.Count()}");

            foreach (string match in matches)
            {
                string queryString = match.Substring(match.IndexOf("?") + 1);
                NameValueCollection parameters = HttpUtility.ParseQueryString(queryString);
                int fileId = int.Parse(parameters["fileId"]);
                bool fileExists = await unitOfWork.FileRepository.GetAll().AnyAsync(x => x.Id == fileId);
                if (!fileExists)
                    Debug.WriteLine($"File {fileId} does not exist. Link {match}");

                int productId = int.Parse(parameters["productId"]);
                bool productExists = await unitOfWork.ProductRepository.GetAll().AnyAsync(x => x.Id == productId);
                if (!productExists)
                    Debug.WriteLine($"Product {productId} does not exist. Link {match}");

                int manufacturerId = int.Parse(parameters["manufacturerId"]);
                if (manufacturerId != 224)
                    Debug.WriteLine($"Invalid manufacturerId {manufacturerId} for USG. Link {match}");
            }

            return Ok();
        }

        [HttpGet]
#if DEBUG
        [AllowAnonymous]
#endif
        public async Task<IActionResult> AddUsersToAssignedList()
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            int[] manufacturerIds = await unitOfWork.ManufacturerRepository.GetAll()
                .Select(x => x.Id)
                .ToArrayAsync();
            string[] userEmails = new string[] { "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>" };
            string[] userIds = await unitOfWork.UserRepository.GetAll()
                .Where(x => userEmails.Any(e => e == x.Email))
                .Select(x => x.Id)
                .ToArrayAsync();
            string creatorUserId = DbConstants.AdminUserId;
            HealthDashboardAccess[] existingAccesses = await unitOfWork.HealthDashboardAccessRepository.GetAll()
                .Where(x => userIds.Contains(x.UserId))
                .ToArrayAsync();

            List<HealthDashboardAccess> newHealthDashboardAccesses = new List<HealthDashboardAccess>();

            foreach (int manufacturerId in manufacturerIds)
            {
                foreach (string userId in userIds)
                {
                    if (existingAccesses.Any(x => x.UserId == userId && x.Type == HealthDashboardAccessType.Manufacturer && x.EntityId == manufacturerId))
                        continue;

                    HealthDashboardAccess newHealthDashboardAccess = new HealthDashboardAccess
                    {
                        CreatedById = creatorUserId,
                        EntityId = manufacturerId,
                        Type = HealthDashboardAccessType.Manufacturer,
                        UserId = userId
                    };

                    newHealthDashboardAccesses.Add(newHealthDashboardAccess);
                }
            }

            unitOfWork.HealthDashboardAccessRepository.InsertRange(newHealthDashboardAccesses);
            await unitOfWork.SaveAsync();

            return Ok();
        }

        [HttpGet]
#if DEBUG
        [AllowAnonymous]
#endif
        public async Task<IActionResult> UploadRevitFilesToManufacturer()
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            int manufacturerId = 140; //Peerless-AV
            string inputFolderPath = "C:\\Work Folder\\Peerless AV - Revit 2021 files\\Revit 2021";
            string[] filePaths = Directory.GetFiles(inputFolderPath, "*", new EnumerationOptions { RecurseSubdirectories = true });
            int revit2021ProjectDataTypeId = 24;
            int revitProjectDataTypeId = 1;
            HashSet<int> productIds = new();

            int totalFilesCount = filePaths.Length;
            int count = 0;

            //upload files
            foreach (string filePath in filePaths)
            {
                Debug.WriteLine($"{++count} file of {totalFilesCount} is being processed");

                string title = Path.GetFileName(filePath);
                ProductFile[] existingProjectFiles = await unitOfWork.ProductFileRepository.GetAll()
                    .Where(x => !x.IsAttachment
                             && x.ProjectDataTypeId == revitProjectDataTypeId
                             && x.File.Title == title
                             && x.Product.ManufacturerId == manufacturerId)
                    .ToArrayAsync();

                unitOfWork.BeginTransaction();
                foreach (ProductFile existingProjectFile in existingProjectFiles)
                {
                    int fileId = await UploadFileToBlobAsync(filePath, unitOfWork);
                    ProjectFileModel projectFile = new ProjectFileModel
                    {
                        FileId = fileId,
                        ProjectDataTypeTypeId = revitProjectDataTypeId,
                        SoftwareVersionId = revit2021ProjectDataTypeId,
                        StateIds = existingProjectFile.StateIds,
                        RegionIds = existingProjectFile.RegionIds
                    };
                    await AddProductProjectFile(projectFile, existingProjectFile.ProductId, unitOfWork);
                    productIds.Add(existingProjectFile.ProductId);
                }
                unitOfWork.CommitTransaction();
            }

            int totalProductsCount = productIds.Count;
            count = 0;

            //update Mongo json for products
            ProductService productService = new ProductService(new PriceService(new FileService()), new MongoRepository<ProductMongoDto>(), new CacheService(), new SlackWebHook(), new HealthDashboardService(), new FileService());
            foreach (int productId in productIds)
            {
                Debug.WriteLine($"{++count} file of {totalProductsCount} is being processed");
                await productService.SaveProductToMongoAsync(productId, true, unitOfWork);
            }

            string productIdsString = string.Join(",", productIds);

            //generate adminpanel links
            string[] productLinks = productIds.Select(x =>
            {
                return new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl"))
                    .AppendPathSegment("adminpanel")
                    .AppendPathSegment(x.ToString())
                    .AppendPathSegment("edit")
                    .AppendPathSegment(manufacturerId.ToString())
                    .ToString();
            }).ToArray();

            return Ok(new
            {
                productLinks
            });
        }

        [HttpGet]
#if DEBUG
        [AllowAnonymous]
#endif
        public async Task<IActionResult> UpdateRevitFilesInManufacturer()
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            int manufacturerId = 140; //Peerless-AV
            string inputFolderPath = "C:\\Work Folder\\Peerless AV - Revit 2021 files";
            string[] filePaths = Directory.GetFiles(inputFolderPath, "*", new EnumerationOptions { RecurseSubdirectories = true });
            int revitProjectDataTypeId = 1;
            int revit2021ProjectDataTypeId = 24;
            HashSet<int> productIds = new();

            int totalFilesCount = filePaths.Length;
            int count = 0;

            //upload files
            foreach (string filePath in filePaths)
            {
                ++count;
                Debug.WriteLine($"{count} file of {totalFilesCount} is being processed");
                LogHelper.LogInfo("C:\\Work Folder\\TempScriptLogs.txt", $"[UpdateRevitFilesInManufacturer] {count} file of {totalFilesCount} is being processed");

                string title = Path.GetFileName(filePath);
                ProductFile[] existingProjectFiles = await unitOfWork.ProductFileRepository.GetAll()
                    .Where(x => !x.IsAttachment
                             && x.ProjectDataTypeId == revitProjectDataTypeId
                             && x.File.Title == title
                             && x.Product.ManufacturerId == manufacturerId
                             && x.SoftwareVersionId == revit2021ProjectDataTypeId)
                    .ToArrayAsync();

                if (!existingProjectFiles.Any())
                {
                    Debug.WriteLine($"Files with Title {title} and Revit Version 2021 not found");
                    LogHelper.LogInfo("C:\\Work Folder\\TempScriptLogs.txt", $"[UpdateRevitFilesInManufacturer] Files with Title {title} and Revit Version 2021 not found");

                    existingProjectFiles = await unitOfWork.ProductFileRepository.GetAll()
                        .Where(x => !x.IsAttachment
                                 && x.ProjectDataTypeId == revitProjectDataTypeId
                                 && x.File.Title == title
                                 && x.Product.ManufacturerId == manufacturerId)
                        .ToArrayAsync();

                    if (!existingProjectFiles.Any())
                    {
                        Debug.WriteLine($"Files with Title {title} and any version of Revit not found");
                        LogHelper.LogInfo("C:\\Work Folder\\TempScriptLogs.txt", $"[UpdateRevitFilesInManufacturer] Files with Title {title} and any version of Revit not found");
                        continue;
                    }
                }

                unitOfWork.BeginTransaction();
                foreach (ProductFile existingProjectFile in existingProjectFiles)
                {
                    int fileId = await UploadFileToBlobAsync(filePath, unitOfWork);
                    ProjectFileModel projectFile = new ProjectFileModel
                    {
                        FileId = fileId,
                        ProjectDataTypeTypeId = revitProjectDataTypeId,
                        SoftwareVersionId = existingProjectFile.SoftwareVersionId,
                        StateIds = existingProjectFile.StateIds,
                        RegionIds = existingProjectFile.RegionIds,
                        CustomFileId = existingProjectFile.CustomFileId,
                        Title = title
                    };

                    await AddProductProjectFile(projectFile, existingProjectFile.ProductId, unitOfWork);
                    productIds.Add(existingProjectFile.ProductId);
                }

                unitOfWork.ProductFileRepository.Delete(existingProjectFiles);
                await unitOfWork.SaveAsync();

                unitOfWork.CommitTransaction();
            }

            //update Mongo json for products
            count = 0;
            ProductService productService = new ProductService(new PriceService(new FileService()), new MongoRepository<ProductMongoDto>(), new CacheService(), new SlackWebHook(), new HealthDashboardService(), new FileService());
            foreach (int productId in productIds)
            {
                ++count;
                Debug.WriteLine($"[UpdateRevitFilesInManufacturer] {count} file of {productIds.Count} is being processed");
                LogHelper.LogInfo("C:\\Work Folder\\TempScriptLogs.txt", $"{count} file of {productIds.Count} is being processed");
                await productService.SaveProductToMongoAsync(productId, true, unitOfWork);
            }

            string productIdsString = string.Join(",", productIds);
            LogHelper.LogInfo("C:\\Work Folder\\TempScriptLogs.txt", $"[UpdateRevitFilesInManufacturer] Affected products: {productIds}");

            //generate adminpanel links
            string[] productLinks = productIds.Select(x =>
            {
                return new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl"))
                    .AppendPathSegment("adminpanel")
                    .AppendPathSegment(x.ToString())
                    .AppendPathSegment("edit")
                    .AppendPathSegment(manufacturerId.ToString())
                    .ToString();
            }).ToArray();

            return Ok(new
            {
                productLinks
            });
        }

        [HttpGet]
#if DEBUG
        [AllowAnonymous]
#endif
        public async Task<IActionResult> ResetRevitFilesGlobalizationInManufacturer()
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            int manufacturerId = 333; //Hanwha Vision
            int revitProjectDataTypeId = 1;

            ProductFile[] existingProjectFiles = await unitOfWork.ProductFileRepository.GetAll()
                .Where(x => !x.IsAttachment
                         && x.ProjectDataTypeId == revitProjectDataTypeId
                         && x.Product.ManufacturerId == manufacturerId)
                .ToArrayAsync();

            unitOfWork.BeginTransaction();
            foreach (ProductFile existingProjectFile in existingProjectFiles)
            {
                existingProjectFile.RegionIds = null;
                existingProjectFile.StateIds = null;
                existingProjectFile.ModifiedById = DbConstants.AdminUserId;
                existingProjectFile.ModifiedDate = DateTime.UtcNow;
                existingProjectFile.WasChanged = true;

                unitOfWork.ProductFileRepository.Edit(existingProjectFile);
            }

            await unitOfWork.SaveAsync();
            unitOfWork.CommitTransaction();

            return Ok();
        }


#if DEBUG
        [AllowAnonymous]
#endif
        [HttpGet]
        public async Task<IActionResult> ExportManufacturerLetsTalkList()
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            int manufacturerId = 221;
            var letsTalkList = await unitOfWork.UserBIMsmithLTManufacturerRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId)
                .Select(x => new
                {
                    UserEmail = x.AddedBy.Email,
                    UserFirstName = x.AddedBy.FirstName,
                    UserLastName = x.AddedBy.LastName,
                    Date = x.AddedDate
                })
                .ToArrayAsync();

            string tempFolder = Path.GetTempPath();
            string filePath = Path.Combine(tempFolder, $"LetsTalkList_{Guid.NewGuid()}.xlsx");
            SpreadsheetDocument spreadsheetDocument;
            WorkbookPart workbookpart;
            WorksheetPart worksheetPart;
            CommonExcelProvider.CreateDocument(filePath, out spreadsheetDocument, out workbookpart, out worksheetPart);
            Sheets sheets;

            // Add a WorksheetPart to the WorkbookPart.
            WorksheetPart usersWorksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            usersWorksheetPart.Worksheet = new Worksheet();

            //columns
            var userColumnsCount = typeof(UserExcelExportDto).GetProperties().Length;
            Columns userColumns = new Columns();

            for (uint userColumnIndex = 1; userColumnIndex < userColumnsCount + 1; userColumnIndex++)
            {
                userColumns.AppendChild(new Column { Min = userColumnIndex, Max = userColumnIndex++, Width = 25, CustomWidth = true });
            }
            usersWorksheetPart.Worksheet.AppendChild(userColumns);
            sheets = workbookpart.Workbook.AppendChild(new Sheets());
            workbookpart.Workbook.Save();

            Sheet usersSheet = new Sheet
            {
                Id = spreadsheetDocument.WorkbookPart.
                GetIdOfPart(usersWorksheetPart),
                SheetId = 1,
                Name = "Let's Talk List"
            };
            sheets.Append(usersSheet);
            workbookpart.Workbook.Save();

            // Get the sheetData cell table.
            SheetData usersSheetData = usersWorksheetPart.Worksheet.AppendChild(new SheetData());

            int rowIndex = 1;
            Row headerRow = new Row();
            usersSheetData.AppendChild(headerRow);

            int columnIndex = 1;

            headerRow.AppendChild(CommonExcelProvider.ConstructCell("User Email", CellValues.String, columnIndex++, rowIndex, 2));
            headerRow.AppendChild(CommonExcelProvider.ConstructCell("User First Name", CellValues.String, columnIndex++, rowIndex, 2));
            headerRow.AppendChild(CommonExcelProvider.ConstructCell("User Last Name", CellValues.String, columnIndex++, rowIndex, 2));
            headerRow.AppendChild(CommonExcelProvider.ConstructCell("Date", CellValues.String, columnIndex++, rowIndex, 2));

            rowIndex++;

            foreach (var item in letsTalkList)
            {
                Row row = new Row();
                usersSheetData.AppendChild(row);

                columnIndex = 1;

                row.AppendChild(CommonExcelProvider.ConstructCell(item.UserEmail, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(item.UserFirstName, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(item.UserLastName, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(item.Date.ToString(), CellValues.String, columnIndex++, rowIndex));

                rowIndex++;
            }

            workbookpart.Workbook.Save();
            spreadsheetDocument.Dispose();

            return PhysicalFile(filePath, "text/csv", "Lets Talk List.xlsx");
        }

        [HttpGet]
#if DEBUG
        [AllowAnonymous]
#endif
        public async Task<IActionResult> RegenerateWrongPreviews()
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            string defaultPreviewImageName = "default_preview.png";
            var productFilesWithWrongPreviews = await unitOfWork.ProductFileRepository.GetAll()
                .Where(x => x.IsAttachment
                         && x.File.PreviewUrl.ToLower().Contains(defaultPreviewImageName))
                .Select(x => new
                {
                    x.FileId,
                    x.ProductId
                })
                .ToArrayAsync();

            var productLineFilesWithWrongPreviews = await unitOfWork.ProductLineFileRepository.GetAll()
                .Where(x => x.IsAttachment
                         && x.File.PreviewUrl.ToLower().Contains(defaultPreviewImageName))
                .Select(x => new
                {
                    x.FileId,
                    ProductIds = x.ProductLine.Products.Select(p => p.Id)
                })
                .ToArrayAsync();

            var manufacturerFilesWithWrongPreviews = await unitOfWork.ManufacturerFileRepository.GetAll()
                .Where(x => x.IsAttachment
                         && x.File.PreviewUrl.ToLower().Contains(defaultPreviewImageName))
                .Select(x => new
                {
                    x.FileId,
                    ProductIds = x.Manufacturer.ProductLines.SelectMany(pl => pl.Products.Select(p => p.Id))
                })
                .ToArrayAsync();

            AzureStorageService azureBlobProvider = new AzureStorageService(
                ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                ConfigurationHelper.GetValue("Environment"),
                bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                false);
            var filesContainer = azureBlobProvider.GetContainerByName(AzureStorageConstants.FilesContainer);

            string folderFiles = Path.Combine(Path.GetTempPath(), ConfigurationHelper.GetValue("Environment"), "Files");
            if (!Directory.Exists(folderFiles))
            {
                Directory.CreateDirectory(folderFiles);
            }

            IFileService fileService = new FileService();
            int[] fileIds = productFilesWithWrongPreviews.Select(x => x.FileId)
                .Concat(productLineFilesWithWrongPreviews.Select(x => x.FileId))
                .Concat(manufacturerFilesWithWrongPreviews.Select(x => x.FileId))
                .ToArray();

            int counter = 0;
            foreach (int fileId in fileIds)
            {
                try
                {
                    unitOfWork.BeginTransaction();
                    var file = unitOfWork.FileRepository.GetAll().FirstOrDefault(f => f.Id == fileId);
                    if (file != null)
                    {
                        file.SyncStatus = SyncFileStatus.Free;

                        string attachUrl = string.IsNullOrWhiteSpace(file.SyncUrl) ? file.Url : file.SyncUrl;

                        if (string.IsNullOrWhiteSpace(attachUrl))
                            continue;

                        if (!attachUrl.StartsWith("http"))
                        {
                            attachUrl = "http://" + attachUrl;
                        }

                        try
                        {
                            await fileService.DownloadFileFromUrlAsync(file, attachUrl, filesContainer, unitOfWork, true);
                        }
                        catch (Exception e)
                        {
                            if (e is WebException && ((WebException)e).Response is HttpWebResponse)
                            {
                                file.SyncStatusCode = (int)((HttpWebResponse)((WebException)e).Response).StatusCode;
                            }
                            else
                            {
                                file.SyncStatusCode = (int)HttpStatusCode.NotFound;
                            }
                            file.SyncStatus = SyncFileStatus.Free;
                            file.UpdatesCount++;
                            file.NextSyncDateTime = DateTime.UtcNow.Date.AddMonths(1);
                        }
                        finally
                        {
                            unitOfWork.FileRepository.Edit(file);
                            Debug.WriteLine($"Passed for fileId: {fileId} at " + DateTime.UtcNow.ToString());
                            LogHelper.LogInfo("C:\\Work Folder\\TempScriptLogs.txt", $"[RegenerateWrongPreviews] Passed for fileId: {fileId} at " + DateTime.UtcNow.ToString());
                            await unitOfWork.SaveAsync();

                            unitOfWork.CommitTransaction();
                        }
                    }
                }
                catch (Exception)
                {
                    continue;
                }
                finally
                {
                    Debug.WriteLine($"{++counter} of {fileIds.Length} files have been processed");
                    LogHelper.LogInfo("C:\\Work Folder\\TempScriptLogs.txt", $"[RegenerateWrongPreviews] {++counter} of {fileIds.Length} files have been processed");
                }
            }

            ProductService productService = new ProductService(new PriceService(new FileService()), new MongoRepository<ProductMongoDto>(), new CacheService(), new SlackWebHook(), new HealthDashboardService(), new FileService());
            int[] productIds = productFilesWithWrongPreviews.Select(x => x.ProductId)
                .Concat(productLineFilesWithWrongPreviews.SelectMany(x => x.ProductIds))
                .Concat(manufacturerFilesWithWrongPreviews.SelectMany(x => x.ProductIds))
                .Distinct()
                .ToArray();
            counter = 0;
            foreach (int productId in productIds)
            {
                await productService.SaveProductToMongoAsync(productId, true, unitOfWork);
                Debug.WriteLine($"{++counter} of {productIds.Length} products have been processed");
                LogHelper.LogInfo("C:\\Work Folder\\TempScriptLogs.txt", $"[RegenerateWrongPreviews] {++counter} of {productIds.Length} products have been processed");
            }

            return Ok();
        }

#if DEBUG
        [AllowAnonymous]
#endif
        [HttpGet]
        public async Task<IActionResult> ReplaceSpacesInOmniclasses()
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            char nonBreakingSpaceCharacter = ' ';
            char regularSpaceCharacter = ' ';
            Omniclass[] omniclasses = await unitOfWork.OmniclassRepository.GetAll().ToArrayAsync();

            omniclasses = omniclasses
                .Where(x => x.Code.Contains(nonBreakingSpaceCharacter) || x.Title.Contains(nonBreakingSpaceCharacter))
                .ToArray();

            Parallel.ForEach(omniclasses, omniclass =>
            {
                omniclass.Code = omniclass.Code.Replace(nonBreakingSpaceCharacter, regularSpaceCharacter);
                omniclass.Title = omniclass.Title.Replace(nonBreakingSpaceCharacter, regularSpaceCharacter);
                unitOfWork.OmniclassRepository.Edit(omniclass);
            });

            await unitOfWork.SaveAsync();

            return Ok();
        }


#if DEBUG
        [AllowAnonymous]
#endif
        [HttpGet]
        public async Task<IActionResult> ExportProductsWithoutRevit2021AndHigher()
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            string[] categoryNames = ["Equipment", "Fireplaces", "Generators", "HVAC", "Piping", "Plumbing Fixtures", "Pumps", "Water Heaters"];
            string[] newRevitVersions = ["Revit 2021"];

            int[] softwareVersionIds = await unitOfWork.ProjectDataTypeRepository.GetAll()
                .Where(x => newRevitVersions.Contains(x.Title))
                .Select(x => x.Id)
                .ToArrayAsync();

            CategoryTreeDto[] allCategories = await unitOfWork.CategoryRepository.GetAll()
                    .ProjectToType<CategoryTreeDto>()
                    .ToArrayAsync();

            int[] topLevelCategoryIds = await unitOfWork.CategoryRepository.GetAll()
                .Where(x => categoryNames.Contains(x.Name))
                .Select(x => x.Id)
                .ToArrayAsync();

            List<int> categoryIds = new();
            foreach (int categoryId in topLevelCategoryIds)
            {
                categoryIds.AddRange(_categoryService.GetAllCategoryHierarchyIds(categoryId, allCategories));
            }

            categoryIds = categoryIds.Distinct().ToList();

            TempScriptProductBaseDto[] productsWithOldRevitVersions = await unitOfWork.ProductRepository.GetAll()
                .Where(x => (categoryIds.Contains(x.CategoryId) || x.ProductCategories.Any(c => categoryIds.Contains(c.CategoryId)))
                         && x.ProductFiles.Any(f => !f.IsAttachment && f.ProjectDataTypeId == (int)ProductFileType.Revit && !softwareVersionIds.Contains(f.SoftwareVersionId.Value) && f.File.FileName.Contains(".txt")))
                .Select(x => new TempScriptProductBaseDto
                {
                    ProductId = x.Id,
                    ProductName = x.Name,
                    ManufacturerId = x.ManufacturerId,
                    ManufacturerName = x.Manufacturer.Name
                })
                .OrderBy(x => x.ManufacturerId)
                .ThenBy(x => x.ProductId)
                .ToArrayAsync();

            TempScriptProductBaseDto[] productWithNewRevitVersions = await unitOfWork.ProductRepository.GetAll()
                .Where(x => (categoryIds.Contains(x.CategoryId) || x.ProductCategories.Any(c => categoryIds.Contains(c.CategoryId)))
                         && x.ProductFiles.Any(f => !f.IsAttachment && f.ProjectDataTypeId == (int)ProductFileType.Revit && softwareVersionIds.Contains(f.SoftwareVersionId.Value)))
                .Select(x => new TempScriptProductBaseDto
                {
                    ProductId = x.Id,
                    ProductName = x.Name,
                    ManufacturerId = x.ManufacturerId,
                    ManufacturerName = x.Manufacturer.Name
                })
                .OrderBy(x => x.ManufacturerId)
                .ThenBy(x => x.ProductId)
                .ToArrayAsync();

            TempScriptProductBaseDto[] products = productsWithOldRevitVersions.Except(productWithNewRevitVersions, new TempScriptProductBaseDtoEqualityComparer()).ToArray();

            string baseMarketUrl = ConfigurationHelper.GetValue("MarketFrontBaseUrl");
            foreach (TempScriptProductBaseDto product in products)
            {
                product.ProductPublicPage = new Url(baseMarketUrl).AppendPathSegment($"/product/{product.ProductId}").ToString();
                product.ProductAdminPanelPage = new Url(baseMarketUrl).AppendPathSegment($"adminpanel/{product.ProductId}/edit/{product.ManufacturerId}").ToString();
            }

            string tempFolder = Path.GetTempPath();
            string filePath = Path.Combine(tempFolder, $"Products_Without_Revit_2021_And_Higher_{Guid.NewGuid()}.xlsx");
            SpreadsheetDocument spreadsheetDocument;
            WorkbookPart workbookpart;
            WorksheetPart worksheetPart;
            CommonExcelProvider.CreateDocument(filePath, out spreadsheetDocument, out workbookpart, out worksheetPart);
            Sheets sheets;

            // Add a WorksheetPart to the WorkbookPart.
            WorksheetPart usersWorksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            usersWorksheetPart.Worksheet = new Worksheet();

            //columns
            var userColumnsCount = typeof(UserExcelExportDto).GetProperties().Length;
            Columns userColumns = new Columns();

            for (uint userColumnIndex = 1; userColumnIndex < userColumnsCount + 1; userColumnIndex++)
            {
                userColumns.AppendChild(new Column { Min = userColumnIndex, Max = userColumnIndex++, Width = 25, CustomWidth = true });
            }
            usersWorksheetPart.Worksheet.AppendChild(userColumns);
            sheets = workbookpart.Workbook.AppendChild(new Sheets());
            workbookpart.Workbook.Save();

            Sheet usersSheet = new Sheet
            {
                Id = spreadsheetDocument.WorkbookPart.
                GetIdOfPart(usersWorksheetPart),
                SheetId = 1,
                Name = "Products"
            };
            sheets.Append(usersSheet);
            workbookpart.Workbook.Save();

            // Get the sheetData cell table.
            SheetData usersSheetData = usersWorksheetPart.Worksheet.AppendChild(new SheetData());

            int rowIndex = 1;
            Row headerRow = new Row();
            usersSheetData.AppendChild(headerRow);

            int columnIndex = 1;

            headerRow.AppendChild(CommonExcelProvider.ConstructCell("Manufacturer Id", CellValues.String, columnIndex++, rowIndex, 2));
            headerRow.AppendChild(CommonExcelProvider.ConstructCell("Manufacturer Name", CellValues.String, columnIndex++, rowIndex, 2));
            headerRow.AppendChild(CommonExcelProvider.ConstructCell("Product Id", CellValues.String, columnIndex++, rowIndex, 2));
            headerRow.AppendChild(CommonExcelProvider.ConstructCell("Product Name", CellValues.String, columnIndex++, rowIndex, 2));
            headerRow.AppendChild(CommonExcelProvider.ConstructCell("Product Admin Panel Page", CellValues.String, columnIndex++, rowIndex, 2));
            headerRow.AppendChild(CommonExcelProvider.ConstructCell("Product Front Page", CellValues.String, columnIndex++, rowIndex, 2));

            rowIndex++;

            foreach (TempScriptProductBaseDto product in products)
            {
                Row row = new Row();
                usersSheetData.AppendChild(row);

                columnIndex = 1;

                row.AppendChild(CommonExcelProvider.ConstructCell(product.ManufacturerId.ToString(), CellValues.Number, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.ManufacturerName, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.ProductId.ToString(), CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.ProductName, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.ProductAdminPanelPage, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.ProductPublicPage, CellValues.String, columnIndex++, rowIndex));

                rowIndex++;
            }

            workbookpart.Workbook.Save();
            spreadsheetDocument.Dispose();

            return PhysicalFile(filePath, "text/csv", "Products without new Revit files.xlsx");
        }

#if DEBUG
        [AllowAnonymous]
#endif
        [HttpGet]
        public async Task<IActionResult> MoveProductFilesFromPhotosContainerToFilesContainer()
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            var files = await unitOfWork.ProductFileRepository.GetAll()
                .Where(x => x.File.Url.Contains("photos"))
                .Select(x => new
                {
                    FileId = x.FileId,
                    FileUrl = x.File.Url,
                    ProductId = x.ProductId
                })
                .ToArrayAsync();

            BlobContainerClient photosContainer = _azureStorageService.GetContainerByName(AzureStorageConstants.PhotosContainer);
            BlobContainerClient filesContainer = _azureStorageService.GetContainerByName(AzureStorageConstants.FilesContainer);
            int counter = 1;

            foreach (var file in files)
            {
                string photosBlobName = Path.GetFileName(file.FileUrl);
                BlockBlobClient photosBlob = photosContainer.GetBlockBlobClient(photosBlobName);

                if (!await photosBlob.ExistsAsync())
                {
                    Debug.WriteLine($"File {file.FileId} with Url {file.FileUrl} skipped");
                    LogHelper.LogInfo("C:\\Work Folder\\TempScript.txt", $"File {file.FileId} with Url {file.FileUrl} skipped");
                    continue;
                }

                string filesBlobName = photosBlob.Name;
                BlockBlobClient filesBlob = filesContainer.GetBlockBlobClient(filesBlobName);
                await filesBlob.StartCopyFromUriAsync(new Uri(file.FileUrl));

                await unitOfWork.FileRepository.GetAll()
                    .Where(x => x.Id == file.FileId)
                    .ExecuteUpdateAsync(x => x.SetProperty(p => p.Url, filesBlob.Uri.ToString()));

                Debug.WriteLine($"{counter++} of {files.Length} have been processed");
                LogHelper.LogInfo("C:\\Work Folder\\TempScript.txt", $"{counter++} of {files.Length} have been processed");
            }

            int[] fileIds = files.Select(x => x.FileId).Distinct().ToArray();
            LogHelper.LogInfo("C:\\Work Folder\\TempScript.txt", $"Files to update {string.Join(",", fileIds)}");
            await unitOfWork.ProductFileRepository.GetAll()
                .Where(x => fileIds.Contains(x.FileId))
                .ExecuteUpdateAsync(x => x.SetProperty(p => p.WasChanged, true));

            int[] productIds = files.Select(x => x.ProductId).Distinct().ToArray();
            LogHelper.LogInfo("C:\\Work Folder\\TempScript.txt", $"Products to update {string.Join(",", productIds)}");
            foreach (int productId in productIds)
                await _productService.SaveProductToMongoAsync(productId, true, unitOfWork);

            return Ok(new
            {
                fileIds,
                productIds
            });
        }

        [HttpGet]
        public async Task<IActionResult> CheckUserIdMismatchBetweenBIMsmithAndMarket()
        {
            string bimsmithUsersFilePath = "C:\\Work Folder\\BIMsmith Users.txt";
            string[] bimsmithUsersFileLines = await System.IO.File.ReadAllLinesAsync(bimsmithUsersFilePath);
            TempScriptUserDto[] bimsmithUsers = bimsmithUsersFileLines.Select(x => new TempScriptUserDto
            {
                Id = x.Split("\t")[0],
                Email = x.Split("\t")[1]
            }).ToArray();

            string marketUsersFilePath = "C:\\Work Folder\\Market Users.txt";
            string[] marketUsersFileLines = await System.IO.File.ReadAllLinesAsync(marketUsersFilePath);
            TempScriptUserDto[] marketUsers = marketUsersFileLines.Select(x => new TempScriptUserDto
            {
                Id = x.Split("\t")[0],
                Email = x.Split("\t")[1]
            }).ToArray();

            List<TempScriptMismatchUserDto> mismatchUsers = [];
            int counter = 0;
            foreach (TempScriptUserDto bimsmithUser in bimsmithUsers)
            {
                TempScriptUserDto marketUser = marketUsers.FirstOrDefault(x => x.Email == bimsmithUser.Email);

                if (marketUser != null && marketUser.Id != bimsmithUser.Id)
                    mismatchUsers.Add(new TempScriptMismatchUserDto
                    {
                        Email = bimsmithUser.Email,
                        BIMsmithId = bimsmithUser.Id,
                        MarketId = marketUser.Id
                    });

                counter++;
                Debug.WriteLine($"{counter} records processed");
            }

            string mismatchUsersFilePath = "C:\\Work Folder\\Mismatch Users.txt";
            await System.IO.File.WriteAllLinesAsync(mismatchUsersFilePath, mismatchUsers.Select(x => $"{x.Email}\t{x.BIMsmithId}\t{x.MarketId}"));

            return Ok();
        }

        [HttpGet]
        public async Task<IActionResult> MoveManufacturerBackupsToAzureStorage()
        {
            BlobContainerClient manufacturerBackupContainer = await _manufacturerBackupFileService.GetManufacturerBackupContainerAsync();
            string manufacturerBackupDirectory = ConfigurationHelper.GetValue("ManufacturerBackupsPath");
            string[] filePaths = Directory.GetFiles(manufacturerBackupDirectory, "*", SearchOption.AllDirectories);

            foreach (string filePath in filePaths)
            {
                string blobName = filePath.Replace(manufacturerBackupDirectory, string.Empty);
                BlockBlobClient blobFile = manufacturerBackupContainer.GetBlockBlobClient(blobName);
                using Stream stream = System.IO.File.OpenRead(filePath);
                await blobFile.UploadAsync(stream);
            }

            return Ok(new
            {
                totalFiles = filePaths.Length
            });
        }

        [HttpGet]
        public async Task<IActionResult> CollectRevitFileStatistics()
        {
            //statistinc for all files
            //IUnitOfWork unitOfWork = UnitOfWork.Create();
            //int revitProjectDataTypeId = 1;
            //IQueryable<ProductFile> productFilesQuery = unitOfWork.ProductFileRepository.GetAll()
            //    .Where(x => !x.IsAttachment && x.ProjectDataTypeId == revitProjectDataTypeId);

            //int totalRevitFilesCount = await productFilesQuery.CountAsync();
            //var versions = await productFilesQuery
            //    .GroupBy(x => x.SoftwareVersion.Title)
            //    .Select(x => new
            //    {
            //        Version = x.Key ?? "Without Version",
            //        Count = x.Count()
            //    })
            //    .OrderByDescending(x => x.Count)
            //    .ToArrayAsync();

            //var fileUrls = await productFilesQuery
            //    .Select(x => x.File.Url)
            //    .ToArrayAsync();

            //var extenstions = fileUrls
            //    .Select(x => !string.IsNullOrWhiteSpace(x) ? Path.GetExtension(x).ToLower() : null)
            //    .GroupBy(x => x)
            //    .Select(x => new
            //    {
            //        Extenstion = x.Key ?? "Without Extension",
            //        Count = x.Count()
            //    })
            //    .OrderByDescending(x => x.Count)
            //    .ToArray();

            //statistics for file with specific extensions
            IUnitOfWork unitOfWork = UnitOfWork.Create();
            string[] fileExtensions = RevitProcessingConstants.UpdateParametersRevitFileExtensions
                .Concat(RevitProcessingConstants.UpdateVersionRevitFileExtenstions)
                .Distinct()
                .ToArray();
            IQueryable<ProductFile> productFilesQuery = unitOfWork.ProductFileRepository.GetAll()
                .Where(x => !x.IsAttachment && x.ProjectDataTypeId == RevitProcessingConstants.RevitProjectDataTypeId && fileExtensions.Any(f => x.File.FileName.EndsWith(f)));

            int totalRevitFilesCount = await productFilesQuery.CountAsync();
            var versions = await productFilesQuery
                .GroupBy(x => x.SoftwareVersion.Title)
                .Select(x => new
                {
                    Version = x.Key ?? "Without Version",
                    Count = x.Count()
                })
                .OrderByDescending(x => x.Count)
                .ToArrayAsync();

            var fileNames = await productFilesQuery
                .Select(x => x.File.FileName)
                .ToArrayAsync();

            var extensions = fileNames
                .Select(x => !string.IsNullOrWhiteSpace(x) ? Path.GetExtension(x).ToLower() : null)
                .GroupBy(x => x)
                .Select(x => new
                {
                    Extenstion = x.Key ?? "Without Extension",
                    Count = x.Count()
                })
                .OrderByDescending(x => x.Count)
                .ToArray();

            //statistics for file with specific extensions and without Revit version specified
            //IUnitOfWork unitOfWork = UnitOfWork.Create();
            //string[] fileExtensions = RevitProcessingConstants.UpdateParametersRevitFileExtensions
            //    .Concat(RevitProcessingConstants.UpdateVersionRevitFileExtenstions)
            //    .Distinct()
            //    .ToArray();
            //IQueryable<ProductFile> productFilesQuery = unitOfWork.ProductFileRepository.GetAll()
            //    .Where(x => !x.IsAttachment
            //             && x.ProjectDataTypeId == RevitProcessingConstants.RevitProjectDataTypeId
            //             && fileExtensions.Any(f => x.File.FileName.Contains(f))
            //             && x.SoftwareVersionId == null);

            //int totalRevitFilesCount = await productFilesQuery.CountAsync();
            //var fileNames = await productFilesQuery
            //    .Select(x => x.File.FileName)
            //    .ToArrayAsync();

            //var extenstions = fileNames
            //    .Select(x => !string.IsNullOrWhiteSpace(x) ? Path.GetExtension(x).ToLower() : null)
            //    .GroupBy(x => x)
            //    .Select(x => new
            //    {
            //        Extenstion = x.Key ?? "Without Extension",
            //        Count = x.Count()
            //    })
            //    .OrderByDescending(x => x.Count)
            //    .ToArray();

            return Ok(new
            {
                extensions,
                versions
            });
        }

        [HttpGet]
        public async Task<IActionResult> SetRevitFileVersion()
        {
            IUnitOfWork unitOfWork = UnitOfWork.Create();
            string[] revitExtenstions = [".rfa", ".rvt", ".rte"];
            IQueryable<ProductFile> productFilesQuery = unitOfWork.ProductFileRepository.GetAll()
                .Where(x => !x.IsAttachment
                         && x.ProjectDataTypeId == RevitProcessingConstants.RevitProjectDataTypeId
                         && x.SoftwareVersionId == null
                         && !(x.File.Url == null || x.File.Url == string.Empty)
                         && revitExtenstions.Any(r => x.File.FileName.EndsWith(r)));

            int revitFilesCount = await productFilesQuery.CountAsync();
            Debug.WriteLine($"{revitFilesCount} files to process");
            var revitProductFiles = await productFilesQuery
                .AsSplitQuery()
                .Select(x => new
                {
                    ProductFileId = x.Id,
                    ProductId = x.ProductId,
                    FileUrl = x.File.Url,
                    ProductFileSoftwareRelease = x.SoftwareRelease,
                    ProductFileSoftwareVersionName = x.SoftwareVersion.Title,
                    ProductFileSoftwareVersionId = x.SoftwareVersionId
                })
                .ToArrayAsync();

            ProjectDataType[] revitProjectDataTypes = await unitOfWork.ProjectDataTypeRepository.GetAllAsNoTracking()
                .Where(x => x.ParentId == RevitProcessingConstants.RevitProjectDataTypeId)
                .ToArrayAsync();

            int counter = 0;
            int skippedCount = 0;
            HashSet<int> affectedProductIds = [];
            Encoding[] mainEncodings = [Encoding.UTF8, Encoding.ASCII, Encoding.Unicode, Encoding.Latin1, Encoding.UTF32, Encoding.BigEndianUnicode, Encoding.UTF7];
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
            Encoding[] additionalEncodings = Encoding.GetEncodings().Select(x => Encoding.GetEncoding(x.Name)).Except(mainEncodings).ToArray();

            foreach (var revitProductFile in revitProductFiles)
            {
                try
                {
                    HttpClient httpClient = _httpClientFactory.CreateClient();
                    byte[] bytes = await httpClient.GetByteArrayAsync(revitProductFile.FileUrl);
                    string revitVersion = string.Empty;

                    foreach (Encoding encoding in mainEncodings)
                    {
                        string fileContent = encoding.GetString(bytes);
                        revitVersion = DetectRevitVersion(fileContent);

                        if (!string.IsNullOrWhiteSpace(revitVersion))
                        {
                            break;
                        }
                    }

                    if (string.IsNullOrWhiteSpace(revitVersion))
                    {
                        foreach (Encoding encoding in additionalEncodings)
                        {
                            string fileContent = encoding.GetString(bytes);
                            revitVersion = DetectRevitVersion(fileContent);

                            if (!string.IsNullOrWhiteSpace(revitVersion))
                            {
                                break;
                            }
                        }
                    }

                    bool skipped = false;
                    if (!string.IsNullOrWhiteSpace(revitVersion))
                    {
                        int? revitVersionProjectDataTypeId = revitProjectDataTypes.FirstOrDefault(x => x.Title.Contains(revitVersion))?.Id;

                        if (revitVersionProjectDataTypeId.HasValue)
                        {
                            await unitOfWork.ProductFileRepository.GetAll()
                                .Where(x => x.Id == revitProductFile.ProductFileId)
                                .ExecuteUpdateAsync(x => x.SetProperty(p => p.SoftwareVersionId, revitVersionProjectDataTypeId)
                                                          .SetProperty(p => p.WasChanged, true)
                                                          .SetProperty(p => p.ModifiedById, DbConstants.AdminUserId)
                                                          .SetProperty(p => p.ModifiedDate, DateTime.UtcNow));
                        }
                        else
                        {
                            skipped = true;
                        }
                    }
                    else
                    {
                        skipped = true;
                        Debug.WriteLine($"File has been skipped");
                    }

                    if (skipped)
                    {
                        skippedCount++;
                    }
                    else
                    {
                        affectedProductIds.Add(revitProductFile.ProductId);
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"{ex.GetAllMessages()} thrown");
                }
                finally
                {
                    Debug.WriteLine($"{++counter} of {revitFilesCount} have been processed");
                }
            }

            return Ok(new
            {
                totalCount = revitFilesCount,
                skippedCount = skippedCount,
                affectedProductIds = affectedProductIds
            });
        }

        [HttpGet]
        public async Task<IActionResult> CollectRevitFileVersionWithoutSet()
        {
            string rawReportFilePath = "C:\\Work Folder\\Revit File Versions Raw Report.txt";
            string orderedReportFilePath = "C:\\Work Folder\\Revit File Versions Ordered Report.txt";
            IUnitOfWork unitOfWork = UnitOfWork.Create();
            string[] revitExtenstions = [".rfa", ".rvt", ".rte"];
            IQueryable<ProductFile> productFilesQuery = unitOfWork.ProductFileRepository.GetAll()
                .Where(x => !x.IsAttachment
                         && x.ProjectDataTypeId == RevitProcessingConstants.RevitProjectDataTypeId
                         && x.SoftwareVersionId == null
                         && !(x.File.Url == null || x.File.Url == string.Empty)
                         && revitExtenstions.Any(r => x.File.FileName.EndsWith(r)));

            int revitFilesCount = await productFilesQuery.CountAsync();
            Debug.WriteLine($"{revitFilesCount} files to process");
            var revitProductFiles = await productFilesQuery
                .AsSplitQuery()
                .Select(x => new
                {
                    ProductId = x.ProductId,
                    ProductName = x.Product.Name,
                    ManufacturerId = x.Product.ManufacturerId,
                    ManufacturerName = x.Product.Manufacturer.Name,
                    FileName = x.File.Title ?? x.File.FileName,
                    FileUrl = x.File.Url
                })
                .ToArrayAsync();

            int counter = 0;
            Encoding[] mainEncodings = [Encoding.UTF8, Encoding.ASCII, Encoding.Unicode, Encoding.Latin1, Encoding.UTF32, Encoding.BigEndianUnicode, Encoding.UTF7];
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
            Encoding[] additionalEncodings = Encoding.GetEncodings().Select(x => Encoding.GetEncoding(x.Name)).Except(mainEncodings).ToArray();

            List<dynamic> files = [];
            foreach (var revitProductFile in revitProductFiles)
            {
                string revitVersion = string.Empty;
                try
                {
                    HttpClient httpClient = _httpClientFactory.CreateClient();
                    byte[] bytes = await httpClient.GetByteArrayAsync(revitProductFile.FileUrl);

                    foreach (Encoding encoding in mainEncodings)
                    {
                        string fileContent = encoding.GetString(bytes);
                        revitVersion = DetectRevitVersion(fileContent);

                        if (!string.IsNullOrWhiteSpace(revitVersion))
                        {
                            break;
                        }
                    }

                    if (string.IsNullOrWhiteSpace(revitVersion))
                    {
                        foreach (Encoding encoding in additionalEncodings)
                        {
                            string fileContent = encoding.GetString(bytes);
                            revitVersion = DetectRevitVersion(fileContent);

                            if (!string.IsNullOrWhiteSpace(revitVersion))
                            {
                                break;
                            }
                        }
                    }

                    using (StreamWriter streamWriter = System.IO.File.AppendText(rawReportFilePath))
                    {
                        streamWriter.WriteLine($"{revitProductFile.ManufacturerName}\t{revitProductFile.ManufacturerId}\t{revitProductFile.ProductName}\t{revitProductFile.ProductId}\t{revitProductFile.FileName}\t{revitVersion}");
                    }

                    files.Add(new
                    {
                        ManufacturerName = revitProductFile.ManufacturerName,
                        ManufacturerId = revitProductFile.ManufacturerId,
                        ProductName = revitProductFile.ProductName,
                        ProductId = revitProductFile.ProductId,
                        FileName = revitProductFile.FileName,
                        RevitVersion = revitVersion
                    });
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"{ex.GetAllMessages()} thrown");
                }
                finally
                {
                    Debug.WriteLine($"{++counter} of {revitFilesCount} have been processed");
                }
            }

            files = files.OrderBy(x => x.ManufacturerId).ThenBy(x => x.ProductId).ThenBy(x => x.FileName).ToList();
            using (StreamWriter streamWriter = System.IO.File.AppendText(orderedReportFilePath))
            {
                foreach (var file in files)
                {
                    streamWriter.WriteLine($"{file.ManufacturerName}\t{file.ManufacturerId}\t{file.ProductName}\t{file.ProductId}\t{file.FileName}\t{file.RevitVersion}");
                }
            }

            return Ok();
        }

        [HttpPost]
        public async Task<IActionResult> RunUpdateRevitProcessForAllManufacturers()
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();

            // Отримуємо userId з токена
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized("User not authenticated");
            }

            // Розширення файлів Revit для UpdateRevitVersion
            string[] revitExtensions = RevitProcessingConstants.UpdateVersionRevitFileExtenstions; // [".rfa", ".rvt", ".rte"]

            // Отримуємо всіх мануфактурерів з продуктами, що мають Revit файли
            var manufacturers = await unitOfWork.ManufacturerRepository
                .GetAllAsNoTracking()
                .Where(m => m.Products.Any(p => p.ProductFiles.Any(pf =>
                    !pf.IsAttachment
                    && pf.ProjectDataTypeId == RevitProcessingConstants.RevitProjectDataTypeId
                    && revitExtensions.Any(ext => pf.File.FileName.EndsWith(ext)))))
                .Select(x => new
                {
                    Id = x.Id,
                    Name = x.Name,
                    ProductIds = x.Products
                        .Where(p => p.ProductFiles.Any(pf =>
                            !pf.IsAttachment
                            && pf.ProjectDataTypeId == RevitProcessingConstants.RevitProjectDataTypeId
                            && revitExtensions.Any(ext => pf.File.FileName.EndsWith(ext))))
                        .Select(p => p.Id)
                        .ToArray()
                })
                .Where(x => x.ProductIds.Any()) // Тільки мануфактурери з продуктами, що мають Revit файли
                .ToArrayAsync();

            int totalManufacturers = manufacturers.Length;
            int processedManufacturers = 0;
            int failedManufacturers = 0;

            Debug.WriteLine($"Found {totalManufacturers} manufacturers with Revit files");
            LogHelper.LogInfo("C:\\Work Folder\\TempScript.txt", $"Found {totalManufacturers} manufacturers with Revit files");

            foreach (var manufacturer in manufacturers)
            {
                try
                {
                    Debug.WriteLine($"Processing manufacturer: {manufacturer.Name} (ID: {manufacturer.Id}) with {manufacturer.ProductIds.Length} products");
                    LogHelper.LogInfo("C:\\Work Folder\\TempScript.txt", $"Processing manufacturer: {manufacturer.Name} (ID: {manufacturer.Id}) with {manufacturer.ProductIds.Length} products");

                    StartRevitProcessDto model = new StartRevitProcessDto()
                    {
                        Type = RevitProcessType.UpdateRevitVersion,
                        ManufacturerId = manufacturer.Id,
                        ProductIds = manufacturer.ProductIds
                    };

                    var result = await _revitProcessingService.StartRevitProcessAsync(model, userId, unitOfWork);

                    if (result.Status == OperationResultStatus.Succeed)
                    {
                        processedManufacturers++;
                        Debug.WriteLine($"Successfully started Revit process for {manufacturer.Name}");
                        LogHelper.LogInfo("C:\\Work Folder\\TempScript.txt", $"Successfully started Revit process for {manufacturer.Name}");
                    }
                    else
                    {
                        failedManufacturers++;
                        Debug.WriteLine($"Failed to start Revit process for {manufacturer.Name}");
                        LogHelper.LogInfo("C:\\Work Folder\\TempScript.txt", $"Failed to start Revit process for {manufacturer.Name}");
                    }
                }
                catch (Exception ex)
                {
                    failedManufacturers++;
                    Debug.WriteLine($"Exception while processing manufacturer {manufacturer.Name}: {ex.Message}");
                    LogHelper.LogInfo("C:\\Work Folder\\TempScript.txt", $"Exception while processing manufacturer {manufacturer.Name}: {ex.Message}");
                }
            }

            string summary = $"Completed processing. Total: {totalManufacturers}, Processed: {processedManufacturers}, Failed: {failedManufacturers}";
            Debug.WriteLine(summary);
            LogHelper.LogInfo("C:\\Work Folder\\TempScript.txt", summary);

            return Ok(new
            {
                TotalManufacturers = totalManufacturers,
                ProcessedManufacturers = processedManufacturers,
                FailedManufacturers = failedManufacturers,
                Message = summary
            });
        }

        #region private methods
        private async Task<string> UploadIconAsync(Url url, string fileName, BlobContainerClient filesContainer)
        {
            HttpClient httpClient = _httpClientFactory.CreateClient();
            {
                using HttpResponseMessage result = await httpClient.GetAsync(url);
                if (!result.IsSuccessStatusCode)
                    return string.Empty;

                Stream fileStream = await result.Content.ReadAsStreamAsync();
                BlobClient fileBlobUpload = filesContainer.GetBlobClient(fileName);
                while (fileBlobUpload.Exists())
                {
                    fileName = Path.GetFileNameWithoutExtension(fileName) + "_c" + Path.GetExtension(fileName);
                    fileBlobUpload = filesContainer.GetBlobClient(fileName);
                }

                using (fileStream)
                {
                    await fileBlobUpload.UploadAsync(fileStream);
                }

                return fileBlobUpload.Uri.ToString();
            }
        }

        private async Task<int> UploadFileToBlobAsync(string localFilePath, IUnitOfWork unitOfWork)
        {
            AzureStorageService azureBlobProvider = new AzureStorageService(
                ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                ConfigurationHelper.GetValue("Environment"),
                bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                false);
            BlobContainerClient filesContainer = azureBlobProvider.GetContainerByName(AzureStorageConstants.FilesContainer);

            string fileName = Path.GetFileName(localFilePath).Replace("\"", string.Empty);

            string mediaType = MimeTypeProvider.GetMimeType(fileName);

            Domain.DBModels.File dbFile = new Domain.DBModels.File
            {
                FileName = fileName,
                Title = fileName,
                MediaType = mediaType,
                CreatedById = DbConstants.AdminUserId,
                CreatedDate = DateTime.UtcNow,
                NextSyncDateTime = DateTime.UtcNow.Date.AddDays(-1),
                SyncStatusCode = (int)HttpStatusCode.OK,
                UpdatesCount = 0
            };
            unitOfWork.FileRepository.Insert(dbFile);
            await unitOfWork.SaveAsync();

            var blobName = dbFile.Id + Path.GetExtension(fileName);
            BlockBlobClient fileBlobUpload = filesContainer.GetBlockBlobClient(blobName);

            using (Stream fileStream = System.IO.File.Open(localFilePath, FileMode.Open))
            {
                await fileBlobUpload.UploadAsync(fileStream);
                dbFile.Url = fileBlobUpload.Uri.ToString();
                dbFile.FileSize = fileStream.Length;
                dbFile.CheckSum = HashProvider.CalculateMD5Hash(fileStream);
            }

            unitOfWork.FileRepository.Edit(dbFile);
            await unitOfWork.SaveAsync();

            return dbFile.Id;
        }

        private async Task AddProductProjectFile(ProjectFileModel projectFile, int productId, IUnitOfWork unitOfWork)
        {
            ProductFile productFile = new ProductFile();
            productFile.CustomFileId = projectFile.CustomFileId;
            productFile.ProductId = productId;
            productFile.FileId = projectFile.FileId;
            productFile.RegionIds = projectFile.RegionIds;
            productFile.StateIds = projectFile.StateIds;
            productFile.SoftwareRelease = projectFile.SoftwareRelease;
            productFile.ContentCheckedBy = projectFile.ContentCheckedBy;
            productFile.ContentCreatedby = projectFile.ContentCreatedby;
            productFile.SoftwareVersionId = projectFile.SoftwareVersionId;
            productFile.ProjectDataTypeId = projectFile.ProjectDataTypeTypeId;
            productFile.IsAttachment = projectFile.ProjectDataTypeTypeId == null;
            productFile.CreatedById = DbConstants.AdminUserId;
            productFile.CreatedDate = DateTime.UtcNow;
            productFile.WasChanged = true;
            unitOfWork.ProductFileRepository.Insert(productFile);

            Domain.DBModels.File file = unitOfWork.FileRepository.GetById(projectFile.FileId);
            if (file.Title != projectFile.Title)
            {
                file.Title = projectFile.Title;
                productFile.WasChanged = true;
                unitOfWork.FileRepository.Edit(file);
            }

            await unitOfWork.SaveAsync();
        }

        private string DetectRevitVersion(string fileContent)
        {
            //.rfa
            string rfaProductVersionOpenTag = "<A:product-version>";
            string rfaProductVersionCloseTag = "</A:product-version>";
            if (fileContent.Contains(rfaProductVersionOpenTag) && fileContent.Contains(rfaProductVersionCloseTag))
            {
                int startIndex = fileContent.IndexOf(rfaProductVersionOpenTag) + rfaProductVersionOpenTag.Length;
                int endIndex = fileContent.IndexOf(rfaProductVersionCloseTag, startIndex);
                string revitVersion = fileContent.Substring(startIndex, endIndex - startIndex);
                if (revitVersion.Length > 4) revitVersion = revitVersion.Substring(0, 4);
                return revitVersion;
            }

            //.rvt, .rfa
            string[] rvtVersionPrefixes = ["Revit Build: Autodesk Revit Architecture ", "Revit Build: Autodesk Revit Structure ", "Revit Build: Autodesk Revit MEP ", "Revit Build: Autodesk Revit Architecture ", "Revit Build: Autodesk Revit ", "Format: ", "Revit Build: "];
            foreach (string rvtVersionPrefix in rvtVersionPrefixes)
            {
                if (fileContent.Contains(rvtVersionPrefix))
                {
                    int startIndex = fileContent.IndexOf(rvtVersionPrefix) + rvtVersionPrefix.Length;
                    return fileContent.Substring(startIndex, 4); //4 symbols for year
                }
            }

            return string.Empty;
        }
        #endregion
    }
}