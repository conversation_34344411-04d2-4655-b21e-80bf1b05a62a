﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.Emails;
using BIMsmithMarket.Domain.Models;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Net.Mime;
using System.Text;
using System.Threading.Tasks;

namespace BIMsmithMarket.Core.Providers
{
    public class EmailNotificationHelper
    {
        private static string _templatePath;

        private EmailNotificationHelper()
        {
        }

        public static EmailNotificationHelper Create(string templatePath)
        {
            _templatePath = templatePath;
            return new EmailNotificationHelper();
        }

        public async Task SendLetsTalkEmail(
            string manufacturerEmail,
            string manufacturerEmailCC,
            string userEmail,
            string firstName,
            string lastName,
            string city,
            string state,
            string country,
            string userType,
            string message,
            string requestURL,
            string productName,
            string productLink
        )
        {
            string templateName = (productName == "null" || productLink == "null" || string.IsNullOrWhiteSpace(productName) || string.IsNullOrWhiteSpace(productLink))
                                    ? "LetsTalkTemplate"
                                    : "LetsTalkWithProductTemplate";

            var htmlBody = GetHtmlBody(templateName);

            htmlBody = htmlBody.Replace("{FirstName}", firstName);
            htmlBody = htmlBody.Replace("{LastName}", lastName);
            htmlBody = htmlBody.Replace("{UserEmail}", userEmail);
            htmlBody = htmlBody.Replace("{City}", city);
            htmlBody = htmlBody.Replace("{State}", state);
            htmlBody = htmlBody.Replace("{Country}", country);
            htmlBody = htmlBody.Replace("{UserType}", userType);
            htmlBody = htmlBody.Replace("{Message}", message);
            htmlBody = htmlBody.Replace("{RequestURL}", requestURL);
            htmlBody = htmlBody.Replace("{CurrentYear}", DateTime.UtcNow.Year.ToString());

            TimeZoneInfo targetZone = TimeZoneInfo.FindSystemTimeZoneById("Central Standard Time");
            DateTime cstDateTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, targetZone);
            htmlBody = htmlBody.Replace("{RequestTimeCST}", cstDateTime.ToString(CultureInfo.InvariantCulture) + " CST");

            if (!string.IsNullOrEmpty(productName) && !string.IsNullOrEmpty(productLink))
            {
                htmlBody = htmlBody.Replace("{ProductName}", productName);
                htmlBody = htmlBody.Replace("{ProductLink}", productLink);
            }

            await SendEmail(manufacturerEmail, manufacturerEmailCC, "Lets Talk Request", htmlBody);
        }

        public async Task SendLunchAndLearnEmail(string manufacturerEmail, string manufacturerEmailCC, string manufacturerName, string userEmail, string userName, string message)
        {
            var htmlBody = GetHtmlBody("LunchAndLearnTemplate");

            htmlBody = htmlBody.Replace("{ManufacturerName}", manufacturerName);
            htmlBody = htmlBody.Replace("{UserName}", userName);
            htmlBody = htmlBody.Replace("{UserEmail}", userEmail);
            htmlBody = htmlBody.Replace("{Message}", message);
            htmlBody = htmlBody.Replace("{CurrentYear}", DateTime.UtcNow.Year.ToString());

            TimeZoneInfo targetZone = TimeZoneInfo.FindSystemTimeZoneById("Central Standard Time");
            DateTime cstDateTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, targetZone);
            htmlBody = htmlBody.Replace("{RequestTimeCST}", cstDateTime.ToString(CultureInfo.InvariantCulture) + " CST");
            await SendEmail(manufacturerEmail, manufacturerEmailCC, "Lunch And Learn Request", htmlBody);
        }

        public async Task SendBIMRequestEmail(string email, string firstName, string lastName, string message, string sourceUrl, string manufacturerName)
        {
            var supportEmail = ConfigurationHelper.GetValue("SupportEmail");
            var htmlBody = GetHtmlBody("BIMRequestTemplate");

            htmlBody = htmlBody.Replace("{ManufacturerName}", manufacturerName);
            htmlBody = htmlBody.Replace("{Email}", email);
            htmlBody = htmlBody.Replace("{FirstName}", firstName);
            htmlBody = htmlBody.Replace("{LastName}", lastName);
            htmlBody = htmlBody.Replace("{Message}", message);
            htmlBody = htmlBody.Replace("{SourceUrl}", sourceUrl);
            htmlBody = htmlBody.Replace("{CurrentYear}", DateTime.UtcNow.Year.ToString());

            TimeZoneInfo targetZone = TimeZoneInfo.FindSystemTimeZoneById("Central Standard Time");
            DateTime cstDateTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, targetZone);
            htmlBody = htmlBody.Replace("{RequestTimeCST}", cstDateTime.ToString(CultureInfo.InvariantCulture) + " CST");
            await SendEmail(supportEmail, "BIM Request", htmlBody);
        }

        public async Task SendScheduleSessionRequestEmail(string firstName, string lastName, string company, string companyEmail, string comments, string additionalInfo)
        {
            var htmlBody = GetHtmlBody("ScheduleSessionRequestTemplate");
            htmlBody = htmlBody.Replace("{FirstName}", firstName);
            htmlBody = htmlBody.Replace("{LastName}", lastName);
            htmlBody = htmlBody.Replace("{Company}", company);
            htmlBody = htmlBody.Replace("{CompanyEmail}", companyEmail);
            htmlBody = htmlBody.Replace("{Comments}", comments);
            htmlBody = htmlBody.Replace("{AdditionalInfo}", additionalInfo);
            htmlBody = htmlBody.Replace("{CurrentYear}", DateTime.UtcNow.Year.ToString());

            var destionationEmail = ConfigurationHelper.GetValue("ShoutEmail");
            var subject = "Schedule a Session request";

            await SendEmail(destionationEmail, subject, htmlBody);
        }

        public async Task SendProductRequestEmail(string firstName, string lastName, string company, string companyEmail, string comments, string additionalInfo)
        {
            var htmlBody = GetHtmlBody("ProductRequestTemplate");
            htmlBody = htmlBody.Replace("{FirstName}", firstName);
            htmlBody = htmlBody.Replace("{LastName}", lastName);
            htmlBody = htmlBody.Replace("{Company}", company);
            htmlBody = htmlBody.Replace("{CompanyEmail}", companyEmail);
            htmlBody = htmlBody.Replace("{Comments}", comments);
            htmlBody = htmlBody.Replace("{AdditionalInfo}", additionalInfo);
            htmlBody = htmlBody.Replace("{CurrentYear}", DateTime.UtcNow.Year.ToString());

            var destionationEmail = ConfigurationHelper.GetValue("ShoutEmail");
            var subject = "Add Products to BIMsmith request";

            await SendEmail(destionationEmail, subject, htmlBody);
        }

        public async Task SendProductContentCommentEmail(string comment, float rating, string productName, string productLink, string manufacturerName, string manufacturerLink, string firstName, string lastName, string userEmail)
        {
            var htmlBody = GetHtmlBody("ProductContentCommentTemplate");
            htmlBody = htmlBody.Replace("{Comment}", comment);
            htmlBody = htmlBody.Replace("{Rating}", rating.ToString());
            htmlBody = htmlBody.Replace("{ProductName}", productName);
            htmlBody = htmlBody.Replace("{ProductLink}", productLink);
            htmlBody = htmlBody.Replace("{ManufacturerName}", manufacturerName);
            htmlBody = htmlBody.Replace("{ManufacturerLink}", manufacturerLink);
            htmlBody = htmlBody.Replace("{FirstName}", firstName);
            htmlBody = htmlBody.Replace("{LastName}", lastName);
            htmlBody = htmlBody.Replace("{UserEmail}", userEmail);
            htmlBody = htmlBody.Replace("{CurrentYear}", DateTime.UtcNow.Year.ToString());

            var destionationEmail = ConfigurationHelper.GetValue("ShoutEmail");
            var subject = "Content comment";

            await SendEmail(destionationEmail, subject, htmlBody);
        }

        public async Task SendDetailContentCommentEmail(string comment, float rating, string detailName, string detailLink, string manufacturerName, string manufacturerLink, string firstName, string lastName, string userEmail)
        {
            var htmlBody = GetHtmlBody("DetailContentCommentTemplate");
            htmlBody = htmlBody.Replace("{Comment}", comment);
            htmlBody = htmlBody.Replace("{Rating}", rating.ToString());
            htmlBody = htmlBody.Replace("{DetailName}", detailName);
            htmlBody = htmlBody.Replace("{DetailLink}", detailLink);
            htmlBody = htmlBody.Replace("{ManufacturerName}", manufacturerName);
            htmlBody = htmlBody.Replace("{ManufacturerLink}", manufacturerLink);
            htmlBody = htmlBody.Replace("{FirstName}", firstName);
            htmlBody = htmlBody.Replace("{LastName}", lastName);
            htmlBody = htmlBody.Replace("{UserEmail}", userEmail);
            htmlBody = htmlBody.Replace("{CurrentYear}", DateTime.UtcNow.Year.ToString());

            var destionationEmail = ConfigurationHelper.GetValue("ShoutEmail");
            var subject = "Content comment";

            await SendEmail(destionationEmail, subject, htmlBody);
        }

        public async Task SendNoteNotificationEmailAsync(NoteNotificationEmailDto model)
        {
            string htmlBody = GetHtmlBody("NoteNotificationTemplate");
            htmlBody = htmlBody.Replace("{NoteName}", model.NoteName);
            htmlBody = htmlBody.Replace("{EntityLinkUrl}", model.EntityLinkUrl);
            htmlBody = htmlBody.Replace("{EntityLinkCaption}", model.EntityLinkCaption);
            htmlBody = htmlBody.Replace("{CurrentYear}", DateTime.UtcNow.Year.ToString());
            string subject = "Note Notification";

            await SendEmail(model.NotificationList, subject, htmlBody);
        }

        public async Task SendRequestPricingEmailAsync(RequestPricingEmailDto model)
        {
            string templateName = "RequestPricingTemplate";
            string htmlBody = GetHtmlBody(templateName);

            htmlBody = htmlBody.Replace("{FirstName}", model.FirstName);
            htmlBody = htmlBody.Replace("{LastName}", model.LastName);
            htmlBody = htmlBody.Replace("{UserEmail}", model.UserEmail);
            htmlBody = htmlBody.Replace("{City}", model.City);
            htmlBody = htmlBody.Replace("{State}", model.State);
            htmlBody = htmlBody.Replace("{Country}", model.Country);
            htmlBody = htmlBody.Replace("{UserType}", model.UserType);
            htmlBody = htmlBody.Replace("{Message}", model.Message);
            htmlBody = htmlBody.Replace("{ProductLink}", model.ProductLink);
            htmlBody = htmlBody.Replace("{ProductName}", model.ProductName);
            htmlBody = htmlBody.Replace("{RequestURL}", model.RequestURL);
            htmlBody = htmlBody.Replace("{CurrentYear}", DateTime.UtcNow.Year.ToString());

            TimeZoneInfo targetZone = TimeZoneInfo.FindSystemTimeZoneById("Central Standard Time");
            DateTime cstDateTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, targetZone);
            htmlBody = htmlBody.Replace("{RequestTimeCST}", cstDateTime.ToString(CultureInfo.InvariantCulture) + " CST");

            await SendEmail(model.ManufacturerEmail, model.ManufacturerEmailCC ?? string.Empty, "Pricing Request", htmlBody);
        }

        public async Task SendProductShareEmailAsync(string destionationEmail, string productName, string productDescription, ICollection<EmailLinkDto> productlinks)
        {
            string templateName = "ProductShareTemplate";
            string htmlBodyTemplate = GetHtmlBody(templateName);
            string productLinksBody = GetLinksHtmlBlock(productlinks);

            StringBuilder stringBuilder = new StringBuilder(htmlBodyTemplate);
            stringBuilder.Replace("{ProductName}", productName)
                         .Replace("{ProductDescription}", productDescription)
                         .Replace("{ProductLinks}", productLinksBody);
            string htmlBody = stringBuilder.ToString();

            await SendEmail(destionationEmail, productName, htmlBody);
        }

        public async Task SendTestEmailAsync(string subject)
        {
            await SendEmail(ConfigurationHelper.GetValue("Smtp:ChecksEmail"), string.Empty, subject, string.Empty);
        }

        public Task SendEmail(string destination, string subject, string text)
        {
            return SendEmail(new List<string> { destination }, null, subject, text, text, null);
        }

        public Task SendEmail(List<string> destinations, string subject, string text)
        {
            return SendEmail(destinations, null, subject, text, text, null);
        }

        public Task SendEmail(string destination, string cc, string subject, string text)
        {
            return SendEmail(new List<string> { destination }, new List<string> { cc }, subject, text, text, null);
        }

        public Task SendEmail(string destination, string cc, string subject, string body, string text)
        {
            return SendEmail(new List<string> { destination }, new List<string> { cc }, subject, body, text, null);
        }

        public Task SendEmail(IEnumerable<string> destinations, IEnumerable<string> ccDestinations, string subject, string body, string text, List<Attachment> attachments)
        {
            if (bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")))
            {
                destinations = FilterRecipientsForDevelopmentEnvironment(destinations);
                ccDestinations = FilterRecipientsForDevelopmentEnvironment(ccDestinations);

                if (!destinations.Any())
                    return Task.CompletedTask;

                subject = $"[{ConfigurationHelper.GetValue("Environment").ToUpper()}] {subject}";
            }

            if (text == null)
            {
                text = body;
            }

            MailMessage msg = new MailMessage();
            msg.From = new MailAddress(ConfigurationHelper.GetValue("emailAccountInfo"), ConfigurationHelper.GetValue("emailAccountDisplayName"));

            foreach (var email in destinations)
            {
                msg.To.Add(new MailAddress(email));
            }
            if (ccDestinations != null)
            {
                foreach (var email in ccDestinations)
                {
                    if (!string.IsNullOrEmpty(email))
                    {
                        msg.CC.Add(new MailAddress(email));
                    }
                }
            }
            msg.Subject = subject;
            msg.AlternateViews.Add(AlternateView.CreateAlternateViewFromString(text, null, MediaTypeNames.Text.Plain));
            msg.AlternateViews.Add(AlternateView.CreateAlternateViewFromString(body, null, MediaTypeNames.Text.Html));
            msg.Body = body;
            msg.IsBodyHtml = true;

            if (attachments != null && attachments.Count > 0)
            {
                foreach (var attachment in attachments)
                {
                    msg.Attachments.Add(attachment);
                }
            }

            SmtpClient smtpClient = new SmtpClient();
            smtpClient.Host = ConfigurationHelper.GetValue("Smtp:Host");
            smtpClient.Port = int.Parse(ConfigurationHelper.GetValue("Smtp:Port"));
            smtpClient.EnableSsl = bool.Parse(ConfigurationHelper.GetValue("Smtp:EnableSsl"));
            smtpClient.Credentials = new NetworkCredential(ConfigurationHelper.GetValue("Smtp:UserName"), ConfigurationHelper.GetValue("Smtp:Password"));
            smtpClient.Send(msg);

            return Task.CompletedTask;
        }

        private string GetHtmlBody(string nameOfTemplate)
        {
            string bodyHtml = string.Empty;

            var path = $"{_templatePath}/{nameOfTemplate}.html";
            using (FileStream html = new FileStream(path, FileMode.Open, FileAccess.Read))
            {
                using (System.IO.StreamReader sr = new StreamReader(html))
                {
                    bodyHtml = sr.ReadToEnd();
                }
            }

            return bodyHtml;
        }

        private IEnumerable<string> FilterRecipientsForDevelopmentEnvironment(IEnumerable<string> recipients)
        {
            if (recipients == null)
                return Enumerable.Empty<string>();

            string[] developmentAllowedEmails = ConfigurationHelper.GetValue("DevelopmentAllowedEmails").Split(',').ToArray();
            return recipients.Where(x => developmentAllowedEmails.Contains(x));
        }

        private string GetLinksHtmlBlock(ICollection<EmailLinkDto> productLinks)
        {
            if (productLinks == null || !productLinks.Any())
            {
                return string.Empty;
            }

            StringBuilder linksHtml = new StringBuilder();

            foreach (var link in productLinks)
            {
                linksHtml.Append($"<p><a href=\"{link.Url}\">{link.Name}</a></p>");
            }

            return linksHtml.ToString();
        }
    }
}