﻿using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    [Index(nameof(Code), Name = "IX_Uniformat_Code")]
    [Index(nameof(Title), Name = "IX_Uniformat_Title")]
    public class Uniformat
    {
        public int Id { get; set; }

        public int? ParentId { get; set; }

        [StringLength(200)]
        public string Code { get; set; }

        [StringLength(200)]
        public string Title { get; set; }

        [ForeignKey("ParentId")]
        public virtual Uniformat Parent { get; set; }

        public virtual ICollection<Uniformat> Children { get; set; }

        public Uniformat()
        {
            Children = new List<Uniformat>();
        }
    }
}