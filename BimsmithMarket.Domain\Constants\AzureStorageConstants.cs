﻿namespace BIMsmithMarket.Domain.Constants
{
    public static class AzureStorageConstants
    {
        #region containers
        public const string AttachmentsContainer = "attachments";

        public const string TempFilesContainer = "temp-files";

        public const string ArchivesContainer = "archives";

        public const string NewsContentContainer = "news-content";

        public const string PhotosContainer = "photos";

        public const string IconsContainer = "icons";

        public const string LockedArchiveContainer = "locked-archive";

        public const string LockedProductFilesContainer = "locked-product-files";

        public const string FilesContainer = "files";

        public const string StaticExcelFilesContainer = "static-excel-files";

        public const string ManufacturerBackupContainer = "manufacturer-backup";
        #endregion

        #region queues
        public const string ProductFileUrlsQueue = "productfileurls";

        public const string ProductRevitsQueue = "productrevits";

        public const string UlProductsQueue = "ulproducts";

        public const string UpdateThumbnailsQueue = "updatethumbnails";

        public const string StaticExcelFilesQueue = "static-excel-files";

        public const string StaticExcelProductsQueue = "static-excel-products";

        public const string ExternalApiUpdateProductsQueue = "external-api-update-products";

        public const string RevitProcessingQueuePrefix = "revit-processing";
        #endregion

        #region file shares
        public const string InternsRevitFileShare = "internsrevitfiles";

        public const string MarketRevitFileShare = "marketrevitfiles";
        #endregion

        public const int QueueMessagesLimit = 32;
    }
}