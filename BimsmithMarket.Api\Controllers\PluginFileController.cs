﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Dto.PluginFileDto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("api/[controller]/[action]")]
    public class PluginFileController : BaseApiController
    {
        private readonly IPluginFileService _pluginFileService;

        public PluginFileController(IPluginFileService pluginFileService)
        {
            _pluginFileService = pluginFileService;
        }

        /// <summary>
        /// Gets the specified identifier.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("Get")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Get(int id)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _pluginFileService.GetAsync(unitOfWork, id));
        }

        /// <summary>
        /// Lists this instance.
        /// </summary>
        /// <param name="pluginType">The plugin type</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("List")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> List(PluginType pluginType = PluginType.BIMsmith)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _pluginFileService.ListAsync(pluginType, unitOfWork));
        }

        /// <summary>
        /// Adds the specified model.
        /// </summary>
        /// <param name="model">The model.</param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Add")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Add([FromBody] AddPluginFileDto model)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            EditPluginFileDto response = await _pluginFileService.AddAsync(unitOfWork, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), model);
            ClearCache();
            return Ok(response);
        }

        /// <summary>
        /// Updates the specified model.
        /// </summary>
        /// <param name="model">The model.</param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Edit")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Edit([FromBody] EditPluginFileDto model)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            EditPluginFileDto response = await _pluginFileService.EditAsync(unitOfWork, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), model);
            ClearCache();
            return Ok(response);
        }

        /// <summary>
        /// Deletes the specified identifier.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("Delete")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Delete(int id)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            await _pluginFileService.DeleteAsync(unitOfWork, id);
            ClearCache();
            return Ok();
        }

        /// <summary>
        /// Lists versions for BIMsmith plugin.
        /// </summary>
        /// <param name="orderByDate">If true order list by date</param>
        /// <param name="pluginType">If any - filter</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("VersionList")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> VersionList(bool orderByDate = false, PluginType pluginType = PluginType.BIMsmith)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _pluginFileService.VersionListAsync(orderByDate, pluginType, unitOfWork));
        }

        /// <summary>
        /// Lists updates for BIMsmith plugin.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("VersionUpdates")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> VersionUpdates(string version)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _pluginFileService.VersionUpdatesAsync(version, PluginType.BIMsmith, unitOfWork));
        }

        /// <summary>
        /// Lists versions for Hanwha plugin.
        /// </summary>
        /// <param name="orderByDate">If true order list by date</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("HanwhaVersionList")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> HanwhaVersionList(bool orderByDate = false)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _pluginFileService.VersionListAsync(orderByDate, PluginType.Hanwha, unitOfWork));
        }

        /// <summary>
        /// Lists updates for BIMsmith plugin.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("HanwhaVersionUpdates")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> HanwhaVersionUpdates(string version)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _pluginFileService.VersionUpdatesAsync(version, PluginType.Hanwha, unitOfWork));
        }

        #region private methods
        private void ClearCache()
        {
            CacheHelper.ClearSpecificCache("*/api/PluginFile/VersionList*");
            CacheHelper.ClearSpecificCache("*/api/PluginFile/VersionUpdates*");
            CacheHelper.ClearSpecificCache("*/api/PluginFile/List*");
            CacheHelper.ClearSpecificCache("*/api/PluginFile/HanwhaVersionList*");
            CacheHelper.ClearSpecificCache("*/api/PluginFile/HanwhaVersionUpdates*");
        }
        #endregion
    }
}