﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{

    [Index(nameof(VanityUrl), Name = "IX_VanityHistory_VanityUrl")]
    public class VanityHistory
    {
        [Required]
        public int Id { get; set; }

        public int? ParentId { get; set; }

        [StringLength(200)]
        public string VanityUrl { get; set; }

        [StringLength(64)]
        public string EntityType { get; set; }

        [Required]
        public int EntityId { get; set; }

        public string CreatedById { get; set; }

        public DateTime CreatedDate { get; set; }

        /// ------------------------------------------
        [ForeignKey("CreatedById")]
        public virtual ApplicationUser CreatedBy { get; set; }

        [ForeignKey("ParentId")]
        public virtual VanityHistory Parent { get; set; }

        public virtual ICollection<VanityHistory> Children { get; set; }

        public VanityHistory()
        {
            Children = new List<VanityHistory>();
        }
    }
}