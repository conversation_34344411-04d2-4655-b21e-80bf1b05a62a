﻿using BIMsmithMarket.Domain.DBModels;
using System;
using System.Collections.Generic;

namespace BIMsmithNewsroom.Models
{
    public class NewsList
    {
        public int CurrentPage { get; set; }

        public int PagesCount { get; set; }

        public bool IsTagList { get; set; }

        public string TagId { get; set; }

        public List<NewsModel> News { get; set; }

        public List<string> Tags { get; set; }
    }

    public class NewsModel
    {
        public string VanityId { get; set; }

        public string Title { get; set; }

        public string Descriptions { get; set; }

        public string HtmlBody { get; set; }

        public string ImageUrl { get; set; }

        public DateTime? PublishedDate { get; set; }

        public PublishOption PublishOption { get; set; }

        public List<OtherNewsModel> OtherNews { get; set; }

        public List<string> Tags { get; set; }
    }

    public class OtherNewsModel
    {
        public string VanityId { get; set; }

        public string Title { get; set; }
    }

}