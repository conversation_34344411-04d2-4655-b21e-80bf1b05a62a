﻿using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Dto.DynamicTranslationDto;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    public class DynamicTranslationController : BaseApiController
    {
        private readonly IDynamicTranslationService _dynamicTranslationService;

        public DynamicTranslationController(IDynamicTranslationService dynamicTranslationService)
        {
            _dynamicTranslationService = dynamicTranslationService;
        }

        /// <summary>
        /// Returns translatable entities list
        /// </summary>
        /// <param name="offset">The offset to skip</param>
        /// <param name="count">The count to take</param>
        /// <returns></returns>
        [HttpGet]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> TranslatableEntityList(int offset = 0, int count = 10)
        {
            return Ok(await _dynamicTranslationService.TranslatableEntityListAsync(offset, count));
        }

        /// <summary>
        /// Returns translatable entity field list
        /// </summary>
        /// <param name="translatableEntityId">The translatable entity fields list</param>
        /// <param name="offset">The offset to skip</param>
        /// <param name="count">The count to take</param>
        /// <returns></returns>
        [HttpGet]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> TranslatableEntityFieldList(int translatableEntityId, int offset = 0, int count = 10)
        {
            return Ok(await _dynamicTranslationService.TranslatableEntityFieldListAsync(translatableEntityId, offset, count));
        }

        /// <summary>
        /// Adds dynamic translation
        /// </summary>
        /// <param name="model">The model specified</param>
        /// <returns></returns>
        [HttpPost]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> AddTranslation(AddDynamicTranslationDto model)
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            return Ok(await _dynamicTranslationService.AddDynamicTranslationAsync(model, userId));
        }
        
        /// <summary>
        /// Adds dynamic translations list
        /// </summary>
        /// <param name="model">The model specified</param>
        /// <returns></returns>
        [HttpPost]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> AddTranslations(AddDynamicTranslationListDto model)
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            await _dynamicTranslationService.AddDynamicTranslationsAsync(model, userId);
            
            return Ok();
        }

        /// <summary>
        /// Edits dynamic translation
        /// </summary>
        /// <param name="model">The model specified</param>
        /// <returns></returns>
        [HttpPost]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> EditTranslation(EditDynamicTranslationDto model)
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            return Ok(await _dynamicTranslationService.EditDynamicTranslationAsync(model, userId));
        }

        /// <summary>
        /// Gets dynamic translation
        /// </summary>
        /// <param name="id">The identifier</param>
        /// <returns></returns>
        [HttpGet]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> GetTranslation(int id)
        {
            return Ok(await _dynamicTranslationService.GetDynamicTranslationAsync(id));
        }

        /// <summary>
        /// Lists translations for entity
        /// </summary>
        /// <param name="translatableEntityId">The translatable entity identifier</param>
        /// <param name="entityId">The entity identifier</param>
        /// <param name="offset">The count to skip</param>
        /// <param name="count">The count to take</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> ListTranslation(int translatableEntityId, int entityId, int offset = 0, int count = 10)
        {
            return Ok(await _dynamicTranslationService.DynamicTranslationListAsync(translatableEntityId, entityId, offset, count));
        }

        /// <summary>
        /// Deletes specified dynamic translation
        /// </summary>
        /// <param name="id">The identifier</param>
        /// <returns></returns>
        [HttpDelete]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> DeleteTranslation(int id)
        {
            await _dynamicTranslationService.DeleteDynamicTranslationAsync(id);
            return Ok();
        }
    }
}