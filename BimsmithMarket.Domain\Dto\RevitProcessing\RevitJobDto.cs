﻿using BIMsmithMarket.Domain.Enums.RevitProcessing;
using System;

namespace BIMsmithMarket.Domain.Dto.RevitProcessing
{
    public class RevitJobDto
    {
        public DateTime CreatedDate { get; set; }

        public DateTime? FinishDate { get; set; }

        public RevitProcessType RevitProcessType { get; set; }

        public RevitJobStatus Status { get; set; }

        public string Report { get; set; }

        public int ProductId { get; set; }

        public string ProductName { get; set; }

        public int ManufacturerId { get; set; }

        public string ManufacturerName { get; set; }

        public int FileId { get; set; }

        public string FileName { get; set; }

        public int ProductFileId { get; set; }

        public string RevitVersion { get; set; }

        public RevitJobRevitParameterMappingDto[] RevitParameterMappings { get; set; }
    }
}