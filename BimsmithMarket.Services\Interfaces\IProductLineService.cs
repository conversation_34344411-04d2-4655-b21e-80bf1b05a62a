﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.ChangeLog;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IProductLineService
    {
        Task<bool> DeleteProductLineAsync(IUnitOfWork unitOfWork, ProductLine productLine);

        Task<AddEditProductLineResponse> AddOrUpdateProductLineAsync(AddProductLineModel model, IUnitOfWork unitOfWork, string userId, int? productLineId = null, bool isRestoredFromBackup = false);

        Task<bool> UpdateRegionIdsAsync(int id, string regionIds, string userId, IUnitOfWork unitOfWork);

        Task<bool> UpdateStateIds(int id, string stateIds, string userId, IUnitOfWork unitOfWork);

        Task<BaseNameListDto[]> GetNamesListAsync(EntityIdsDto model, IUnitOfWork unitOfWork);

        Task<ProductLineAddEditResultDto> AddAsync(AddProductLineModel model, string userId, IUnitOfWork unitOfWork);

        Task<ProductLineAddEditResultDto> EditAsync(EditProductLineModel model, string userId, IUnitOfWork unitOfWork);

        Task<OperationResultDto> DeleteAsync(int id, string userId, IUnitOfWork unitOfWork);

        Task<ProductLineListDto[]> ListAsync(
            IUnitOfWork unitOfWork,
            string q = null,
            int manufacturerId = -1,
            string manufacturerName = null,
            int categoryId = -1,
            bool onlyWithPublishedProducts = false,
            bool isStaging = false,
            string regionId = null
            );

        Task<ProductLineGetDto> GetAsync(int id, IUnitOfWork unitOfWork);

        Task<ProductLineGetProductFileDto[]> GetProductFilesAsync(int productLineId, IUnitOfWork unitOfWork);

        Task<ProductLineChangeLogDto> GetModelForChangeLogAsync(int id, IUnitOfWork unitOfWork);
    }
}