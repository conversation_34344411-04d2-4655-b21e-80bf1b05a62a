﻿using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Dto.SalesRepresentative
{
    public class AddSalesRepresentativeDto
    {
        [Required]
        [StringLength(100)]
        public string FirstName { get; set; }

        [Required]
        [StringLength(100)]
        public string LastName { get; set; }

        [Required]
        [StringLength(100)]
        public string PhoneNumber { get; set; }

        [Required]
        [StringLength(100)]
        public string WebsiteName { get; set; }

        [Required]
        [StringLength(100)]
        public string WebsiteLink { get; set; }

        [Required]
        [StringLength(100)]
        public string Email { get; set; }

        [Required]
        [StringLength(500)]
        public string LinkendInLink { get; set; }

        public int? ProfilePhotoId { get; set; }

        [Required]
        public int ManufacturerId { get; set; }
    }
}