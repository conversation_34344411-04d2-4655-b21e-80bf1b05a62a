﻿using BIMsmithMarket.DataLayer.Context;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.DBModels.DynamicTranslationDbModels;
using BIMsmithMarket.Domain.DBModels.PaymentDbModels;
using BIMsmithMarket.Domain.DBModels.RevitProcessing;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using System;
using System.Data;
using System.Threading;
using System.Threading.Tasks;

namespace BIMsmithMarket.DataLayer
{
    public class UnitOfWork : IUnitOfWork, IDisposable
    {
        private readonly ApplicationDbContext dbContext;
        private IDbContextTransaction dbContextTransaction = null;

        private IRepository<ApplicationUser> userRepository;
        public IRepository<ApplicationUser> UserRepository
        {
            get
            {
                if (userRepository == null)
                {
                    userRepository = new Repository<ApplicationUser>(dbContext);
                }
                return userRepository;
            }
        }


        private IRepository<ApplicationUserRole> userRoleRepository;

        public IRepository<ApplicationUserRole> UserRoleRepository
        {
            get
            {
                if (userRoleRepository == null)
                {
                    userRoleRepository = new Repository<ApplicationUserRole>(dbContext);
                }
                return userRoleRepository;
            }
        }

        private IRepository<ApplicationRole> roleRepository;
        public IRepository<ApplicationRole> RoleRepository
        {
            get
            {
                if (roleRepository == null)
                {
                    roleRepository = new Repository<ApplicationRole>(dbContext);
                }
                return roleRepository;
            }
        }

        private IRepository<VanityHistory> vanityHistoryRepository;
        public IRepository<VanityHistory> VanityHistoryRepository
        {
            get
            {
                if (vanityHistoryRepository == null)
                {
                    vanityHistoryRepository = new Repository<VanityHistory>(dbContext);
                }
                return vanityHistoryRepository;
            }
        }

        private IRepository<Manufacturer> manufacturerRepository;
        public IRepository<Manufacturer> ManufacturerRepository
        {
            get
            {
                if (manufacturerRepository == null)
                {
                    manufacturerRepository = new Repository<Manufacturer>(dbContext);
                }
                return manufacturerRepository;
            }
        }

        private IRepository<Photo> photoRepository;
        public IRepository<Photo> PhotoRepository
        {
            get
            {
                if (photoRepository == null)
                {
                    photoRepository = new Repository<Photo>(dbContext);
                }
                return photoRepository;
            }
        }

        private IRepository<Product> productRepository;
        public IRepository<Product> ProductRepository
        {
            get
            {
                if (productRepository == null)
                {
                    productRepository = new Repository<Product>(dbContext);
                }
                return productRepository;
            }
        }

        private IRepository<Cisfb> cisfbRepository;
        public IRepository<Cisfb> CisfbRepository
        {
            get
            {
                if (cisfbRepository == null)
                {
                    cisfbRepository = new Repository<Cisfb>(dbContext);
                }
                return cisfbRepository;
            }
        }

        private IRepository<ProductCisfb> productCisfbRepository;
        public IRepository<ProductCisfb> ProductCisfbRepository
        {
            get
            {
                if (productCisfbRepository == null)
                {
                    productCisfbRepository = new Repository<ProductCisfb>(dbContext);
                }
                return productCisfbRepository;
            }
        }

        private IRepository<RelatedProduct> relatedProductRepository;
        public IRepository<RelatedProduct> RelatedProductRepository
        {
            get
            {
                if (relatedProductRepository == null)
                {
                    relatedProductRepository = new Repository<RelatedProduct>(dbContext);
                }
                return relatedProductRepository;
            }
        }

        private IRepository<Category> categoryRepository;
        public IRepository<Category> CategoryRepository
        {
            get
            {
                if (categoryRepository == null)
                {
                    categoryRepository = new Repository<Category>(dbContext);
                }
                return categoryRepository;
            }
        }

        private IRepository<AttachmentOrder> attachmentOrderRepository;
        public IRepository<AttachmentOrder> AttachmentOrderRepository
        {
            get
            {
                if (attachmentOrderRepository == null)
                {
                    attachmentOrderRepository = new Repository<AttachmentOrder>(dbContext);
                }
                return attachmentOrderRepository;
            }
        }

        private IRepository<PluginFile> pluginFileRepository;
        public IRepository<PluginFile> PluginFileRepository
        {
            get
            {
                if (pluginFileRepository == null)
                {
                    pluginFileRepository = new Repository<PluginFile>(dbContext);
                }
                return pluginFileRepository;
            }
        }

        private IRepository<KeyStat> keyStatRepository;
        public IRepository<KeyStat> KeyStatRepository
        {
            get
            {
                if (keyStatRepository == null)
                {
                    keyStatRepository = new Repository<KeyStat>(dbContext);
                }
                return keyStatRepository;
            }
        }

        private IRepository<CategoryKeyStat> categoryKeyStatRepository;
        public IRepository<CategoryKeyStat> CategoryKeyStatRepository
        {
            get
            {
                if (categoryKeyStatRepository == null)
                {
                    categoryKeyStatRepository = new Repository<CategoryKeyStat>(dbContext);
                }
                return categoryKeyStatRepository;
            }
        }

        private IRepository<File> fileRepository;
        public IRepository<File> FileRepository
        {
            get
            {
                if (fileRepository == null)
                {
                    fileRepository = new Repository<File>(dbContext);
                }
                return fileRepository;
            }
        }

        private IRepository<ProductPhoto> productPhotoRepository;
        public IRepository<ProductPhoto> ProductPhotoRepository
        {
            get
            {
                if (productPhotoRepository == null)
                {
                    productPhotoRepository = new Repository<ProductPhoto>(dbContext);
                }
                return productPhotoRepository;
            }
        }

        private IRepository<ProductSample> productSampleRepository;
        public IRepository<ProductSample> ProductSampleRepository
        {
            get
            {
                if (productSampleRepository == null)
                {
                    productSampleRepository = new Repository<ProductSample>(dbContext);
                }
                return productSampleRepository;
            }
        }

        private IRepository<ProductFile> productFileRepository;
        public IRepository<ProductFile> ProductFileRepository
        {
            get
            {
                if (productFileRepository == null)
                {
                    productFileRepository = new Repository<ProductFile>(dbContext);
                }
                return productFileRepository;
            }
        }

        private IRepository<ProductLine> productLineRepository;
        public IRepository<ProductLine> ProductLineRepository
        {
            get
            {
                if (productLineRepository == null)
                {
                    productLineRepository = new Repository<ProductLine>(dbContext);
                }
                return productLineRepository;
            }
        }

        private IRepository<ForgeProductLineIds> forgeProductLineIdsRepository;
        public IRepository<ForgeProductLineIds> ForgeProductLineIdsRepository
        {
            get
            {
                if (forgeProductLineIdsRepository == null)
                {
                    forgeProductLineIdsRepository = new Repository<ForgeProductLineIds>(dbContext);
                }
                return forgeProductLineIdsRepository;
            }
        }

        private IRepository<ProductStats> productStatsRepository;
        public IRepository<ProductStats> ProductStatsRepository
        {
            get
            {
                if (productStatsRepository == null)
                {
                    productStatsRepository = new Repository<ProductStats>(dbContext);
                }
                return productStatsRepository;
            }
        }

        private IRepository<ProductRating> productRatingRepository;
        public IRepository<ProductRating> ProductRatingRepository
        {
            get
            {
                if (productRatingRepository == null)
                {
                    productRatingRepository = new Repository<ProductRating>(dbContext);
                }
                return productRatingRepository;
            }
        }

        private IRepository<ProductCertificate> productCertificateRepository;
        public IRepository<ProductCertificate> ProductCertificateRepository
        {
            get
            {
                if (productCertificateRepository == null)
                {
                    productCertificateRepository = new Repository<ProductCertificate>(dbContext);
                }
                return productCertificateRepository;
            }
        }

        private IRepository<ProductCategory> productCategoryRepository;
        public IRepository<ProductCategory> ProductCategoryRepository
        {
            get
            {
                if (productCategoryRepository == null)
                {
                    productCategoryRepository = new Repository<ProductCategory>(dbContext);
                }
                return productCategoryRepository;
            }
        }

        private IRepository<ULRatingSystemSustainableCredit> ulRatingSystemSustainableCreditRepository;
        public IRepository<ULRatingSystemSustainableCredit> ULRatingSystemSustainableCreditRepository
        {
            get
            {
                if (ulRatingSystemSustainableCreditRepository == null)
                {
                    ulRatingSystemSustainableCreditRepository = new Repository<ULRatingSystemSustainableCredit>(dbContext);
                }
                return ulRatingSystemSustainableCreditRepository;
            }
        }

        private IRepository<ULCertificate> ulCertificateRepository;
        public IRepository<ULCertificate> ULCertificateRepository
        {
            get
            {
                if (ulCertificateRepository == null)
                {
                    ulCertificateRepository = new Repository<ULCertificate>(dbContext);
                }
                return ulCertificateRepository;
            }
        }

        private IRepository<ULStandardNumber> ulStandardNumberRepository;
        public IRepository<ULStandardNumber> ULStandardNumberRepository
        {
            get
            {
                if (ulStandardNumberRepository == null)
                {
                    ulStandardNumberRepository = new Repository<ULStandardNumber>(dbContext);
                }
                return ulStandardNumberRepository;
            }
        }

        private IRepository<QualityItem> qualityItemRepository;
        public IRepository<QualityItem> QualityItemRepository
        {
            get
            {
                if (qualityItemRepository == null)
                {
                    qualityItemRepository = new Repository<QualityItem>(dbContext);
                }
                return qualityItemRepository;
            }
        }

        private IRepository<ProductQualityItem> productQualityItemRepository;
        public IRepository<ProductQualityItem> ProductQualityItemRepository
        {
            get
            {
                if (productQualityItemRepository == null)
                {
                    productQualityItemRepository = new Repository<ProductQualityItem>(dbContext);
                }
                return productQualityItemRepository;
            }
        }

        private IRepository<UserBIMsmithManufacturer> userBIMsmithManufacturerRepository;
        public IRepository<UserBIMsmithManufacturer> UserBIMsmithManufacturerRepository
        {
            get
            {
                if (userBIMsmithManufacturerRepository == null)
                {
                    userBIMsmithManufacturerRepository = new Repository<UserBIMsmithManufacturer>(dbContext);
                }
                return userBIMsmithManufacturerRepository;
            }
        }

        private IRepository<ManufacturerAdminUser> manufacturerAdminUserRepository;
        public IRepository<ManufacturerAdminUser> ManufacturerAdminUserRepository
        {
            get
            {
                if (manufacturerAdminUserRepository == null)
                {
                    manufacturerAdminUserRepository = new Repository<ManufacturerAdminUser>(dbContext);
                }
                return manufacturerAdminUserRepository;
            }
        }

        private IRepository<UserBIMsmithLLManufacturer> userBIMsmithLLManufacturerRepository;
        public IRepository<UserBIMsmithLLManufacturer> UserBIMsmithLLManufacturerRepository
        {
            get
            {
                if (userBIMsmithLLManufacturerRepository == null)
                {
                    userBIMsmithLLManufacturerRepository = new Repository<UserBIMsmithLLManufacturer>(dbContext);
                }
                return userBIMsmithLLManufacturerRepository;
            }
        }

        private IRepository<UserBIMsmithLTManufacturer> userBIMsmithLTManufacturerRepository;
        public IRepository<UserBIMsmithLTManufacturer> UserBIMsmithLTManufacturerRepository
        {
            get
            {
                if (userBIMsmithLTManufacturerRepository == null)
                {
                    userBIMsmithLTManufacturerRepository = new Repository<UserBIMsmithLTManufacturer>(dbContext);
                }
                return userBIMsmithLTManufacturerRepository;
            }
        }

        private IRepository<UserBIMsmithBIMManufacturer> userBIMsmithBIMManufacturerRepository;
        public IRepository<UserBIMsmithBIMManufacturer> UserBIMsmithBIMManufacturerRepository
        {
            get
            {
                if (userBIMsmithBIMManufacturerRepository == null)
                {
                    userBIMsmithBIMManufacturerRepository = new Repository<UserBIMsmithBIMManufacturer>(dbContext);
                }
                return userBIMsmithBIMManufacturerRepository;
            }
        }

        private IRepository<UserBIMsmithProduct> userBIMsmithProductRepository;
        public IRepository<UserBIMsmithProduct> UserBIMsmithProductRepository
        {
            get
            {
                if (userBIMsmithProductRepository == null)
                {
                    userBIMsmithProductRepository = new Repository<UserBIMsmithProduct>(dbContext);
                }
                return userBIMsmithProductRepository;
            }
        }

        private IRepository<Address> addressRepository;
        public IRepository<Address> AddressRepository
        {
            get
            {
                if (addressRepository == null)
                {
                    addressRepository = new Repository<Address>(dbContext);
                }
                return addressRepository;
            }
        }

        private IRepository<ReportItem> reportItemRepository;
        public IRepository<ReportItem> ReportItemRepository
        {
            get
            {
                if (reportItemRepository == null)
                {
                    reportItemRepository = new Repository<ReportItem>(dbContext);
                }
                return reportItemRepository;
            }
        }

        private IRepository<Masterformat> masterformatRepository;
        public IRepository<Masterformat> MasterformatRepository
        {
            get
            {
                if (masterformatRepository == null)
                {
                    masterformatRepository = new Repository<Masterformat>(dbContext);
                }
                return masterformatRepository;
            }
        }

        private IRepository<Omniclass> omniclassRepository;
        public IRepository<Omniclass> OmniclassRepository
        {
            get
            {
                if (omniclassRepository == null)
                {
                    omniclassRepository = new Repository<Omniclass>(dbContext);
                }
                return omniclassRepository;
            }
        }

        private IRepository<Uniclass> uniclassRepository;
        public IRepository<Uniclass> UniclassRepository
        {
            get
            {
                if (uniclassRepository == null)
                {
                    uniclassRepository = new Repository<Uniclass>(dbContext);
                }
                return uniclassRepository;
            }
        }

        private IRepository<ProductUniclass> productUniclassRepository;
        public IRepository<ProductUniclass> ProductUniclassRepository
        {
            get
            {
                if (productUniclassRepository == null)
                {
                    productUniclassRepository = new Repository<ProductUniclass>(dbContext);
                }
                return productUniclassRepository;
            }
        }

        private IRepository<Uniformat> uniformatRepository;
        public IRepository<Uniformat> UniformatRepository
        {
            get
            {
                if (uniformatRepository == null)
                {
                    uniformatRepository = new Repository<Uniformat>(dbContext);
                }
                return uniformatRepository;
            }
        }

        private IRepository<ProductUniformat> productUniformatRepository;
        public IRepository<ProductUniformat> ProductUniformatRepository
        {
            get
            {
                if (productUniformatRepository == null)
                {
                    productUniformatRepository = new Repository<ProductUniformat>(dbContext);
                }
                return productUniformatRepository;
            }
        }

        private IRepository<ProductMasterformat> productMasterformatRepository;
        public IRepository<ProductMasterformat> ProductMasterformatRepository
        {
            get
            {
                if (productMasterformatRepository == null)
                {
                    productMasterformatRepository = new Repository<ProductMasterformat>(dbContext);
                }
                return productMasterformatRepository;
            }
        }

        private IRepository<ProductOmniclass> productOmniclassRepository;
        public IRepository<ProductOmniclass> ProductOmniclassRepository
        {
            get
            {
                if (productOmniclassRepository == null)
                {
                    productOmniclassRepository = new Repository<ProductOmniclass>(dbContext);
                }
                return productOmniclassRepository;
            }
        }

        private IRepository<KeyStatUnit> keyStatUnitRepository;
        public IRepository<KeyStatUnit> KeyStatUnitRepository
        {
            get
            {
                if (keyStatUnitRepository == null)
                {
                    keyStatUnitRepository = new Repository<KeyStatUnit>(dbContext);
                }
                return keyStatUnitRepository;
            }
        }

        private IRepository<KeyStatValueList> keyStatValueListRepository;
        public IRepository<KeyStatValueList> KeyStatValueListRepository
        {
            get
            {
                if (keyStatValueListRepository == null)
                {
                    keyStatValueListRepository = new Repository<KeyStatValueList>(dbContext);
                }
                return keyStatValueListRepository;
            }
        }

        private IRepository<KeyStatUnitRelation> keyStatUnitRelationRepository;
        public IRepository<KeyStatUnitRelation> KeyStatUnitRelationRepository
        {
            get
            {
                if (keyStatUnitRelationRepository == null)
                {
                    keyStatUnitRelationRepository = new Repository<KeyStatUnitRelation>(dbContext);
                }
                return keyStatUnitRelationRepository;
            }
        }

        private IRepository<Localization> localizationRepository;
        public IRepository<Localization> LocalizationRepository
        {
            get
            {
                if (localizationRepository == null)
                {
                    localizationRepository = new Repository<Localization>(dbContext);
                }
                return localizationRepository;
            }
        }

        private IRepository<News> newsRepository;
        public IRepository<News> NewsRepository
        {
            get
            {
                if (newsRepository == null)
                {
                    newsRepository = new Repository<News>(dbContext);
                }
                return newsRepository;
            }
        }

        private IRepository<NewsCategory> newsCategoryRepository;
        public IRepository<NewsCategory> NewsCategoryRepository
        {
            get
            {
                if (newsCategoryRepository == null)
                {
                    newsCategoryRepository = new Repository<NewsCategory>(dbContext);
                }
                return newsCategoryRepository;
            }
        }

        private IRepository<SearchHistory> searchHistoryRepository;
        public IRepository<SearchHistory> SearchHistoryRepository
        {
            get
            {
                if (searchHistoryRepository == null)
                {
                    searchHistoryRepository = new Repository<SearchHistory>(dbContext);
                }
                return searchHistoryRepository;
            }
        }

        private IRepository<Setting> settingRepository;
        public IRepository<Setting> SettingRepository
        {
            get
            {
                if (settingRepository == null)
                {
                    settingRepository = new Repository<Setting>(dbContext);
                }
                return settingRepository;
            }
        }

        private IRepository<Synonym> synonymRepository;
        public IRepository<Synonym> SynonymRepository
        {
            get
            {
                if (synonymRepository == null)
                {
                    synonymRepository = new Repository<Synonym>(dbContext);
                }
                return synonymRepository;
            }
        }

        private IRepository<BlogPost> blogPostRepository;
        public IRepository<BlogPost> BlogPostRepository
        {
            get
            {
                if (blogPostRepository == null)
                {
                    blogPostRepository = new Repository<BlogPost>(dbContext);
                }
                return blogPostRepository;
            }
        }

        private IRepository<BlogTargetType> blogTargetTypeRepository;
        public IRepository<BlogTargetType> BlogTargetTypeRepository
        {
            get
            {
                if (blogTargetTypeRepository == null)
                {
                    blogTargetTypeRepository = new Repository<BlogTargetType>(dbContext);
                }
                return blogTargetTypeRepository;
            }
        }

        private IRepository<BlogCategoryTargetType> blogCategoryTargetTypeRepository;
        public IRepository<BlogCategoryTargetType> BlogCategoryTargetTypeRepository
        {
            get
            {
                if (blogCategoryTargetTypeRepository == null)
                {
                    blogCategoryTargetTypeRepository = new Repository<BlogCategoryTargetType>(dbContext);
                }
                return blogCategoryTargetTypeRepository;
            }
        }

        private IRepository<BlogCategory> blogCategoryRepository;
        public IRepository<BlogCategory> BlogCategoryRepository
        {
            get
            {
                if (blogCategoryRepository == null)
                {
                    blogCategoryRepository = new Repository<BlogCategory>(dbContext);
                }
                return blogCategoryRepository;
            }
        }

        private IRepository<BlogComment> blogCommentRepository;
        public IRepository<BlogComment> BlogCommentRepository
        {
            get
            {
                if (blogCommentRepository == null)
                {
                    blogCommentRepository = new Repository<BlogComment>(dbContext);
                }
                return blogCommentRepository;
            }
        }

        private IRepository<BlogMentionedEntry> blogMentionedEntryRepository;
        public IRepository<BlogMentionedEntry> BlogMentionedEntryRepository
        {
            get
            {
                if (blogMentionedEntryRepository == null)
                {
                    blogMentionedEntryRepository = new Repository<BlogMentionedEntry>(dbContext);
                }
                return blogMentionedEntryRepository;
            }
        }

        private IRepository<EmailSubscriber> emailSubscriberRepository;
        public IRepository<EmailSubscriber> EmailSubscriberRepository
        {
            get
            {
                if (emailSubscriberRepository == null)
                {
                    emailSubscriberRepository = new Repository<EmailSubscriber>(dbContext);
                }
                return emailSubscriberRepository;
            }
        }

        private IRepository<Company> companyRepository;
        public IRepository<Company> CompanyRepository
        {
            get
            {
                if (companyRepository == null)
                {
                    companyRepository = new Repository<Company>(dbContext);
                }
                return companyRepository;
            }
        }

        private IRepository<ManufacturerPhoto> manufacturerPhotoRepository;
        public IRepository<ManufacturerPhoto> ManufacturerPhotoRepository
        {
            get
            {
                if (manufacturerPhotoRepository == null)
                {
                    manufacturerPhotoRepository = new Repository<ManufacturerPhoto>(dbContext);
                }
                return manufacturerPhotoRepository;
            }
        }

        private IRepository<ManufacturerFile> manufacturerFileRepository;
        public IRepository<ManufacturerFile> ManufacturerFileRepository
        {
            get
            {
                if (manufacturerFileRepository == null)
                {
                    manufacturerFileRepository = new Repository<ManufacturerFile>(dbContext);
                }
                return manufacturerFileRepository;
            }
        }

        private IRepository<ManufacturerAdditionalFile> manufacturerAdditionalFileRepository;
        public IRepository<ManufacturerAdditionalFile> ManufacturerAdditionalFileRepository
        {
            get
            {
                if (manufacturerAdditionalFileRepository == null)
                {
                    manufacturerAdditionalFileRepository = new Repository<ManufacturerAdditionalFile>(dbContext);
                }
                return manufacturerAdditionalFileRepository;
            }
        }

        private IRepository<ProductLineQualityItem> productLineQualityItemRepository;
        public IRepository<ProductLineQualityItem> ProductLineQualityItemRepository
        {
            get
            {
                if (productLineQualityItemRepository == null)
                {
                    productLineQualityItemRepository = new Repository<ProductLineQualityItem>(dbContext);
                }
                return productLineQualityItemRepository;
            }
        }

        private IRepository<ProductLineCertificate> productLineCertificateRepository;
        public IRepository<ProductLineCertificate> ProductLineCertificateRepository
        {
            get
            {
                if (productLineCertificateRepository == null)
                {
                    productLineCertificateRepository = new Repository<ProductLineCertificate>(dbContext);
                }
                return productLineCertificateRepository;
            }
        }

        private IRepository<ProductLineStats> productLineStatsRepository;
        public IRepository<ProductLineStats> ProductLineStatsRepository
        {
            get
            {
                if (productLineStatsRepository == null)
                {
                    productLineStatsRepository = new Repository<ProductLineStats>(dbContext);
                }
                return productLineStatsRepository;
            }
        }

        private IRepository<ProductLineFile> productLineFileRepository;
        public IRepository<ProductLineFile> ProductLineFileRepository
        {
            get
            {
                if (productLineFileRepository == null)
                {
                    productLineFileRepository = new Repository<ProductLineFile>(dbContext);
                }
                return productLineFileRepository;
            }
        }

        private IRepository<Starter> starterRepository;
        public IRepository<Starter> StarterRepository
        {
            get
            {
                if (starterRepository == null)
                {
                    starterRepository = new Repository<Starter>(dbContext);
                }
                return starterRepository;
            }
        }

        private IRepository<StarterProductLines> starterProductLinesRepository;
        public IRepository<StarterProductLines> StarterProductLinesRepository
        {
            get
            {
                if (starterProductLinesRepository == null)
                {
                    starterProductLinesRepository = new Repository<StarterProductLines>(dbContext);
                }
                return starterProductLinesRepository;
            }
        }

        private IRepository<ProjectDataType> projectDataTypeRepository;
        public IRepository<ProjectDataType> ProjectDataTypeRepository
        {
            get
            {
                if (projectDataTypeRepository == null)
                {
                    projectDataTypeRepository = new Repository<ProjectDataType>(dbContext);
                }
                return projectDataTypeRepository;
            }
        }

        private IRepository<HelpCategory> helpCategoryRepository;
        public IRepository<HelpCategory> HelpCategoryRepository
        {
            get
            {
                if (helpCategoryRepository == null)
                {
                    helpCategoryRepository = new Repository<HelpCategory>(dbContext);
                }
                return helpCategoryRepository;
            }
        }

        private IRepository<HelpArticle> helpArticleRepository;
        public IRepository<HelpArticle> HelpArticleRepository
        {
            get
            {
                if (helpArticleRepository == null)
                {
                    helpArticleRepository = new Repository<HelpArticle>(dbContext);
                }
                return helpArticleRepository;
            }
        }

        private IRepository<Event> eventRepository;
        public IRepository<Event> EventRepository
        {
            get
            {
                if (eventRepository == null)
                {
                    eventRepository = new Repository<Event>(dbContext);
                }
                return eventRepository;
            }
        }

        private IRepository<Announcement> announcementRepository;
        public IRepository<Announcement> AnnouncementRepository
        {
            get
            {
                if (announcementRepository == null)
                {
                    announcementRepository = new Repository<Announcement>(dbContext);
                }
                return announcementRepository;
            }
        }

        private IRepository<Detail> detailRepository;
        public IRepository<Detail> DetailRepository
        {
            get
            {
                if (detailRepository == null)
                {
                    detailRepository = new Repository<Detail>(dbContext);
                }
                return detailRepository;
            }
        }

        private IRepository<DetailApplication> detailApplicationRepository;
        public IRepository<DetailApplication> DetailApplicationRepository
        {
            get
            {
                if (detailApplicationRepository == null)
                {
                    detailApplicationRepository = new Repository<DetailApplication>(dbContext);
                }
                return detailApplicationRepository;
            }
        }

        private IRepository<DetailDetailApplication> detailDetailApplicationRepository;
        public IRepository<DetailDetailApplication> DetailDetailApplicationRepository
        {
            get
            {
                if (detailDetailApplicationRepository == null)
                {
                    detailDetailApplicationRepository = new Repository<DetailDetailApplication>(dbContext);
                }
                return detailDetailApplicationRepository;
            }
        }

        private IRepository<DetailRating> detailRatingRepository;
        public IRepository<DetailRating> DetailRatingRepository
        {
            get
            {
                if (detailRatingRepository == null)
                {
                    detailRatingRepository = new Repository<DetailRating>(dbContext);
                }
                return detailRatingRepository;
            }
        }

        private IRepository<DetailFile> detailFileRepository;
        public IRepository<DetailFile> DetailFileRepository
        {
            get
            {
                if (detailFileRepository == null)
                {
                    detailFileRepository = new Repository<DetailFile>(dbContext);
                }
                return detailFileRepository;
            }
        }

        private IRepository<DetailPhoto> detailPhotoRepository;
        public IRepository<DetailPhoto> DetailPhotoRepository
        {
            get
            {
                if (detailPhotoRepository == null)
                {
                    detailPhotoRepository = new Repository<DetailPhoto>(dbContext);
                }
                return detailPhotoRepository;
            }
        }

        private IRepository<ProductDetail> productDetailRepository;
        public IRepository<ProductDetail> ProductDetailRepository
        {
            get
            {
                if (productDetailRepository == null)
                {
                    productDetailRepository = new Repository<ProductDetail>(dbContext);
                }
                return productDetailRepository;
            }
        }

        private IRepository<DetailMasterformat> detailMasterformatRepository;
        public IRepository<DetailMasterformat> DetailMasterformatRepository
        {
            get
            {
                if (detailMasterformatRepository == null)
                {
                    detailMasterformatRepository = new Repository<DetailMasterformat>(dbContext);
                }
                return detailMasterformatRepository;
            }
        }

        private IRepository<RelatedDetail> relatedDetailRepository;
        public IRepository<RelatedDetail> RelatedDetailRepository
        {
            get
            {
                if (relatedDetailRepository == null)
                {
                    relatedDetailRepository = new Repository<RelatedDetail>(dbContext);
                }
                return relatedDetailRepository;
            }
        }

        private IRepository<DetailScale> detailScaleRepository;
        public IRepository<DetailScale> DetailScaleRepository
        {
            get
            {
                if (detailScaleRepository == null)
                {
                    detailScaleRepository = new Repository<DetailScale>(dbContext);
                }
                return detailScaleRepository;
            }
        }

        private IRepository<FeatureSetting> featureSettingRepository;
        public IRepository<FeatureSetting> FeatureSettingRepository
        {
            get
            {
                if (featureSettingRepository == null)
                {
                    featureSettingRepository = new Repository<FeatureSetting>(dbContext);
                }
                return featureSettingRepository;
            }
        }

        private IRepository<NewsTarget> newsTargetRepository;
        public IRepository<NewsTarget> NewsTargetRepository
        {
            get
            {
                if (newsTargetRepository == null)
                {
                    newsTargetRepository = new Repository<NewsTarget>(dbContext);
                }
                return newsTargetRepository;
            }
        }

        private IRepository<ManufacturerStyleFile> manufacturerStyleFileRepository;
        public IRepository<ManufacturerStyleFile> ManufacturerStyleFileRepository
        {
            get
            {
                if (manufacturerStyleFileRepository == null)
                {
                    manufacturerStyleFileRepository = new Repository<ManufacturerStyleFile>(dbContext);
                }
                return manufacturerStyleFileRepository;
            }
        }

        private IRepository<Price> priceRepository;
        public IRepository<Price> PriceRepository
        {
            get
            {
                if (priceRepository == null)
                {
                    priceRepository = new Repository<Price>(dbContext);
                }
                return priceRepository;
            }
        }

        private IRepository<PaymentPlan> paymentPlanRepository;
        public IRepository<PaymentPlan> PaymentPlanRepository
        {
            get
            {
                if (paymentPlanRepository == null)
                {
                    paymentPlanRepository = new Repository<PaymentPlan>(dbContext);
                }
                return paymentPlanRepository;
            }
        }

        private IRepository<ProductPrice> productPriceRepository;
        public IRepository<ProductPrice> ProductPriceRepository
        {
            get
            {
                if (productPriceRepository == null)
                {
                    productPriceRepository = new Repository<ProductPrice>(dbContext);
                }
                return productPriceRepository;
            }
        }

        private IRepository<UserPaidProduct> userPaidProductRepository;
        public IRepository<UserPaidProduct> UserPaidProductRepository
        {
            get
            {
                if (userPaidProductRepository == null)
                {
                    userPaidProductRepository = new Repository<UserPaidProduct>(dbContext);
                }
                return userPaidProductRepository;
            }
        }

        private IRepository<ManufacturerAnnouncement> manufacturerAnnouncementRepository;
        public IRepository<ManufacturerAnnouncement> ManufacturerAnnouncementRepository
        {
            get
            {
                if (manufacturerAnnouncementRepository == null)
                {
                    manufacturerAnnouncementRepository = new Repository<ManufacturerAnnouncement>(dbContext);
                }
                return manufacturerAnnouncementRepository;
            }
        }

        private IRepository<HealthDashboardAccess> healthDashboardAccessRepository;
        public IRepository<HealthDashboardAccess> HealthDashboardAccessRepository
        {
            get
            {
                if (healthDashboardAccessRepository == null)
                {
                    healthDashboardAccessRepository = new Repository<HealthDashboardAccess>(dbContext);
                }
                return healthDashboardAccessRepository;
            }
        }

        private IRepository<DynamicTranslation> dynamicTranslationRepository;
        public IRepository<DynamicTranslation> DynamicTranslationRepository
        {
            get
            {
                if (dynamicTranslationRepository == null)
                {
                    dynamicTranslationRepository = new Repository<DynamicTranslation>(dbContext);
                }
                return dynamicTranslationRepository;
            }
        }

        private IRepository<TranslatableEntity> translatableEntityRepository;
        public IRepository<TranslatableEntity> TranslatableEntityRepository
        {
            get
            {
                if (translatableEntityRepository == null)
                {
                    translatableEntityRepository = new Repository<TranslatableEntity>(dbContext);
                }
                return translatableEntityRepository;
            }
        }

        private IRepository<TranslatableEntityField> translatableEntityFieldRepository;
        public IRepository<TranslatableEntityField> TranslatableEntityFieldRepository
        {
            get
            {
                if (translatableEntityFieldRepository == null)
                {
                    translatableEntityFieldRepository = new Repository<TranslatableEntityField>(dbContext);
                }
                return translatableEntityFieldRepository;
            }
        }

        private IRepository<PublishedDisplayOrderView> publishedDisplayOrderViewRepository;
        public IRepository<PublishedDisplayOrderView> PublishedDisplayOrderViewRepository
        {
            get
            {
                if (publishedDisplayOrderViewRepository == null)
                {
                    publishedDisplayOrderViewRepository = new Repository<PublishedDisplayOrderView>(dbContext);
                }
                return publishedDisplayOrderViewRepository;
            }
        }

        private IRepository<ProductDisplayOrderView> productDisplayOrderViewRepository;
        public IRepository<ProductDisplayOrderView> ProductDisplayOrderViewRepository
        {
            get
            {
                if (productDisplayOrderViewRepository == null)
                {
                    productDisplayOrderViewRepository = new Repository<ProductDisplayOrderView>(dbContext);
                }
                return productDisplayOrderViewRepository;
            }
        }

        private IRepository<StaticExcelFile> staticExcelFileRepository;
        public IRepository<StaticExcelFile> StaticExcelFileRepository
        {
            get
            {
                if (staticExcelFileRepository == null)
                {
                    staticExcelFileRepository = new Repository<StaticExcelFile>(dbContext);
                }
                return staticExcelFileRepository;
            }
        }

        private IRepository<StaticExcelProductError> staticExcelProductErrorRepository;
        public IRepository<StaticExcelProductError> StaticExcelProductErrorRepository
        {
            get
            {
                if (staticExcelProductErrorRepository == null)
                {
                    staticExcelProductErrorRepository = new Repository<StaticExcelProductError>(dbContext);
                }
                return staticExcelProductErrorRepository;
            }
        }

        private IRepository<ApplicationUserClaim> userClaimReporisory;
        public IRepository<ApplicationUserClaim> UserClaimReporisory
        {
            get
            {
                if (userClaimReporisory == null)
                {
                    userClaimReporisory = new Repository<ApplicationUserClaim>(dbContext);
                }
                return userClaimReporisory;
            }
        }

        private IRepository<ApplicationUserLogin> userLoginRepository;
        public IRepository<ApplicationUserLogin> UserLoginRepository
        {
            get
            {
                if (userLoginRepository == null)
                {
                    userLoginRepository = new Repository<ApplicationUserLogin>(dbContext);
                }
                return userLoginRepository;
            }
        }

        private IRepository<DropboxSetting> dropboxSettingRepository;
        public IRepository<DropboxSetting> DropboxSettingRepository
        {
            get
            {
                if (dropboxSettingRepository == null)
                {
                    dropboxSettingRepository = new Repository<DropboxSetting>(dbContext);
                }
                return dropboxSettingRepository;
            }
        }        

        private IRepository<CustomCategoryIcon> customCategoryIconRepository;
        public IRepository<CustomCategoryIcon> CustomCategoryIconRepository
        {
            get
            {
                if (customCategoryIconRepository == null)
                {
                    customCategoryIconRepository = new Repository<CustomCategoryIcon>(dbContext);
                }
                return customCategoryIconRepository;
            }
        }

        private IRepository<SalesRepresentative> salesRepresentativeRepository;
        public IRepository<SalesRepresentative> SalesRepresentativeRepository
        {
            get
            {
                if (salesRepresentativeRepository == null)
                {
                    salesRepresentativeRepository = new Repository<SalesRepresentative>(dbContext);
                }
                return salesRepresentativeRepository;
            }
        }

        private IRepository<ChangeLog> changeLogRepository;
        public IRepository<ChangeLog> ChangeLogRepository
        {
            get
            {
                if (changeLogRepository == null)
                {
                    changeLogRepository = new Repository<ChangeLog>(dbContext);
                }
                return changeLogRepository;
            }
        }

        private IRepository<Note> noteRepository;
        public IRepository<Note> NoteRepository
        {
            get
            {
                if (noteRepository == null)
                {
                    noteRepository = new Repository<Note>(dbContext);
                }
                return noteRepository;
            }
        }

        private IRepository<NoteNotificationUser> noteNotificationUserRepository;
        public IRepository<NoteNotificationUser> NoteNotificationUserRepository
        {
            get
            {
                if (noteNotificationUserRepository == null)
                {
                    noteNotificationUserRepository = new Repository<NoteNotificationUser>(dbContext);
                }
                return noteNotificationUserRepository;
            }
        }

        private IRepository<RequestPricingUser> requestPricingUserRepository;
        public IRepository<RequestPricingUser> RequestPricingUserRepository
        {
            get
            {
                if (requestPricingUserRepository == null)
                {
                    requestPricingUserRepository = new Repository<RequestPricingUser>(dbContext);
                }
                return requestPricingUserRepository;
            }
        }

        private IRepository<RevitProcess> revitProcessRepository;
        public IRepository<RevitProcess> RevitProcessRepository
        {
            get
            {
                if (revitProcessRepository == null)
                {
                    revitProcessRepository = new Repository<RevitProcess>(dbContext);
                }
                return revitProcessRepository;
            }
        }

        private IRepository<RevitProcessProduct> revitProcessProductRepository;
        public IRepository<RevitProcessProduct> RevitProcessProductRepository
        {
            get
            {
                if (revitProcessProductRepository == null)
                {
                    revitProcessProductRepository = new Repository<RevitProcessProduct>(dbContext);
                }
                return revitProcessProductRepository;
            }
        }

        private IRepository<RevitJobRevitParameterMapping> revitJobRevitParameterMappingRepository;
        public IRepository<RevitJobRevitParameterMapping> RevitJobRevitParameterMappingRepository
        {
            get
            {
                if (revitJobRevitParameterMappingRepository == null)
                {
                    revitJobRevitParameterMappingRepository = new Repository<RevitJobRevitParameterMapping>(dbContext);
                }
                return revitJobRevitParameterMappingRepository;
            }
        }

        private IRepository<RevitJob> revitJobRepository;
        public IRepository<RevitJob> RevitJobRepository
        {
            get
            {
                if (revitJobRepository == null)
                {
                    revitJobRepository = new Repository<RevitJob>(dbContext);
                }
                return revitJobRepository;
            }
        }

        private IRepository<RevitProcessProjectDataType> revitProcessProjectDataTypeRepository;
        public IRepository<RevitProcessProjectDataType> RevitProcessProjectDataTypeRepository
        {
            get
            {
                if (revitProcessProjectDataTypeRepository == null)
                {
                    revitProcessProjectDataTypeRepository = new Repository<RevitProcessProjectDataType>(dbContext);
                }
                return revitProcessProjectDataTypeRepository;
            }
        }

        private IRepository<RevitProcessRevitParameterMapping> revitProcessRevitParameterMappingRepository;
        public IRepository<RevitProcessRevitParameterMapping> RevitProcessRevitParameterMappingRepository
        {
            get
            {
                if (revitProcessRevitParameterMappingRepository == null)
                {
                    revitProcessRevitParameterMappingRepository = new Repository<RevitProcessRevitParameterMapping>(dbContext);
                }
                return revitProcessRevitParameterMappingRepository;
            }
        }

        public DbContext CurrentDbContext
        {
            get
            {
                return dbContext;
            }
        }

        private UnitOfWork(int timeout = DbConstants.ConnectionTimeout, bool disableAllTracking = false)
        {
            dbContext = ApplicationDbContextFactory.CreateContext();
            dbContext.Database.SetCommandTimeout(timeout);
            if (disableAllTracking)
            {
                dbContext.ChangeTracker.AutoDetectChangesEnabled = false;
                dbContext.ChangeTracker.LazyLoadingEnabled = false;
            }
        }

        public int Save()
        {
            return dbContext.SaveChanges();
        }

        public async Task<int> SaveAsync(CancellationToken cancellationToken = default)
        {
            return await dbContext.SaveChangesAsync(cancellationToken);
        }

        public void BeginTransaction()
        {
            if (dbContextTransaction != null)
            {
                throw new FieldAccessException("Need to commit or rollback previously transation");
            }

            dbContextTransaction = dbContext.Database.BeginTransaction();
        }

        public void BeginTransaction(IsolationLevel isolationLevel)
        {
            if (dbContextTransaction != null)
            {
                throw new FieldAccessException("Need to commit or rollback previously transation");
            }

            dbContextTransaction = dbContext.Database.BeginTransaction(isolationLevel);
        }

        public void RollbackTransaction()
        {
            if (dbContextTransaction == null)
            {
                throw new FieldAccessException("Transaction was not begining");
            }
            dbContextTransaction.Rollback();
            dbContextTransaction.Dispose();
            dbContextTransaction = null;
            RejectChanges();
        }

        public void CommitTransaction()
        {
            if (dbContextTransaction == null)
            {
                throw new FieldAccessException("Transaction was not begining");
            }
            dbContextTransaction.Commit();
            dbContextTransaction.Dispose();
            dbContextTransaction = null;
        }

        public void Detached(object entity)
        {
            dbContext.Entry(entity).State = EntityState.Detached;
        }

        public void RejectChanges()
        {
            foreach (var entry in dbContext.ChangeTracker.Entries())
            {
                switch (entry.State)
                {
                    case EntityState.Modified:
                    case EntityState.Deleted:
                        entry.State = EntityState.Modified; //Revert changes made to deleted entity.
                        entry.State = EntityState.Unchanged;
                        break;
                    case EntityState.Added:
                        entry.State = EntityState.Detached;
                        break;
                }
            }
        }

        public static IUnitOfWork Create(int timeout = DbConstants.ConnectionTimeout, bool disableAllTracking = false)
        {
            return new UnitOfWork(timeout, disableAllTracking);
        }

        public void Dispose()
        {
            if (dbContextTransaction != null)
            {
                RollbackTransaction();
            }

            dbContext.Dispose();
        }
    }
}