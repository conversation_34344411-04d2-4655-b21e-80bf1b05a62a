﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.DBModels
{
    public class DetailApplication : BaseEntity
    {
        public string Name { get; set; }

        public string Header { get; set; }

        public string PageTitle { get; set; }

        public string MetaDescription { get; set; }

        public string Description { get; set; }

        [Required]
        public string VanityUrl { get; set; }

        public string Keywords { get; set; }

        public string Synonyms { get; set; }

        public virtual ICollection<DetailDetailApplication> DetailDetailApplications { get; set; }

        public DetailApplication()
        {
            DetailDetailApplications = new List<DetailDetailApplication>();
        }
    }
}