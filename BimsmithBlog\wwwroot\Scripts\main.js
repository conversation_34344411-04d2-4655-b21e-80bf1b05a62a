
$(document).ready(function () {
    let bimsmithUrl = $('#BimsmithUrl').val();
    let Category_element = document.getElementById("category-name");
    let Category_block = document.getElementById("category-block");
    if (Category_element && Category_block) {
        if (Category_element.innerText.toLocaleLowerCase() == "bimsmith") {
            Category_element.innerText = "BIMsmith";
        } else {
            let text = Category_element.innerText;
            text = text.toLocaleLowerCase();
            Category_element.innerText = text.charAt(0).toUpperCase() + text.slice(1);
        }
        Category_block.style.display = "flex";
    }

	$(".search .fa-search").click(function() {
        $(".search input").toggleClass('active');
        if (!$(".search input").hasClass("active") && $(".search input").val()) {
            window.location.href = "/?q=" + $(".search input").val() || '';
        }
        return false;
    });

    $(".search input").on('keyup', function (e) {
        if (e.keyCode == 13) {
            if ($(".search input").hasClass("active") && $(".search input").val()) {
                window.location.href = "/?q=" + $(".search input").val() || '';
            }
        }
    });

    $("#mob-search").on('keyup', function (e) {
        if (e.keyCode == 13) {
            if ($("#mob-search") && $("#mob-search").val()) {
                window.location.href = "/?q=" + $("#mob-search").val() || '';
            }
        }
    });

    let mobSearch = document.getElementById("mob-search");
    if (mobSearch) mobSearch.style.display = "none";

    $("#mob-search-btn").click(function () {
        let elem = document.getElementById("mob-search");
        let state = elem.style.display == "block";
        elem.style.display = state ? "none" : "block";
        elem.style.border = "1px solid silver";
        elem.style.width = "100%";
        elem.style.padding = "5px";
    })

  $("#top").click(function() {
    $("html, body").animate({ scrollTop: 0 }, "slow");
    return false;
  });

  $(".header .menu").click(function () {
  	$(".header .links").slideToggle(300);
  	$(this).toggleClass("on");
  	return false;
  });


  let subscribe = document.getElementById('subscribe');
  if (subscribe) {
      let session = localStorage.getItem('session');
      subscribe.style.display = session ? 'flex' : 'none';
  }
  $("#subscribe .sub-close").click(function () {
      if (subscribe) {
          subscribe.style.display = 'none';
          localStorage.setItem('session', '');
      }
  });

	$(".sign button").click(function() {
        window.location.replace(`${bimsmithUrl}/NewMyBIMSmith/login?returnURL=${window.location.href}`);
  });


    //auth popup logic

    $('#user-name').click(function (e) {
        e.stopPropagation();
        $('.head-drop').toggleClass('show-menu');
    });
    $('.head-drop').click(function (e) {
        e.stopPropagation();
    });
    $('body,html').click(function (e) {
        $('.head-drop').removeClass('show-menu');
    });

    //auth logic

    document.querySelector('#login-button').href = bimsmithUrl + "/NewMyBIMSmith/login?returnURL=" + window.location.href;
    document.querySelector('#signup-button').href = bimsmithUrl + "/NewMyBIMSmith/register?returnURL=" + window.location.href;
    document.querySelector('#mob-login a').href = bimsmithUrl + "/NewMyBIMSmith/login?returnURL=" + window.location.href;
    document.querySelector('#mob-regis a').href = bimsmithUrl + "/NewMyBIMSmith/register?returnURL=" + window.location.href;

    // authentication logic
    document.querySelector('#logout-button').onclick = function () {
        document.querySelector('#login-button').style.display = 'inline-block';
        document.querySelector('#signup-button').style.display = 'inline-block';

        document.querySelector('#logout-button').style.display = 'none';
        document.querySelector('#user-name').style.display = 'none';

        document.cookie = 'authToken=;expires=Thu, 01 Jan 1970 00:00:01 GMT;domain=.bimsmith.com;';
    };

    const authToken = Cookies.get('authToken');

    if (authToken) {
        jQuery.get(bimsmithUrl + "/api/Auth/UserInfo/?authToken=" + authToken)
            .then(
            function (data) {

                document.querySelector('#login-button').style.display = 'none';
                document.querySelector('#mob-login').style.display = 'none';
                document.querySelector('#mob-regis').style.display = 'none';
                document.querySelector('#signup-button').style.display = 'none';
                document.querySelector('#profile-image').src = data['Image'] ? data['Image'] : '/images/img01.jpg';

                document.querySelector('#logout-button').style.display = 'inline-block';
                document.querySelector('#user-name').style.display = 'inline-block';

                if (data['lastName'].length == 0 && data['firstName'] == 0) {
                    document.querySelector('#user-name, #settings-name').innerHTML = 'Anonymous User';
                    document.querySelector('#settings-name').innerHTML = 'Anonymous User';
                }
                else {
                    document.querySelector('#user-name, #settings-name').innerHTML = data['lastName'] + ' ' + data['firstName'];
                    document.querySelector('#settings-name').innerHTML = data['lastName'] + ' ' + data['firstName'];
                }

            },
            function (err) {
                console.log('error');
                //TODO logout show Login log out buttons
                document.cookie = 'authToken=;expires=Thu, 01 Jan 1970 00:00:01 GMT;domain=.bimsmith.com;';

                document.querySelector('#login-button').style.display = 'inline-block';
                document.querySelector('#signup-button').style.display = 'inline-block';
            }
            )
    } else {
        document.cookie = 'authToken=;expires=Thu, 01 Jan 1970 00:00:01 GMT;domain=.bimsmith.com;';
        document.querySelector('#login-button').style.display = 'inline-block';
        document.querySelector('#signup-button').style.display = 'inline-block';
    }

        //<authentication logic

    (function collapse() {
        const collection = document.querySelectorAll('.footer-custom h4');
        const titles = Array.from(collection);

        titles.forEach(item => {
            item.addEventListener('click', callback);
        });

        function callback() {
            const className = 'footer-custom__list_active';
            let selfClick = true;

            if (this.nextElementSibling.classList.contains(className)) {
                selfClick = false;
            }

            titles.forEach(item => {
                item.nextElementSibling.classList.remove(className);
            });

            if (selfClick) {
                this.nextElementSibling.classList.add(className);
            } else {
                this.nextElementSibling.classList.remove(className);
            }
        }
    })();
});
