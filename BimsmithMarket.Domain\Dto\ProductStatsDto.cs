﻿using BIMsmithMarket.Domain.DBModels;
using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Dto
{
    public class ProductStatsDto
    {
        public int KeyStatId { get; set; }
        public KeyStatDto KeyStatUnit { get; set; }
        public int? KeyStatUnitId { get; set; }
        public int Order { get; set; }
        public IEnumerable<KeyStatListDto> MultipleValues { get; set; }
        public string SingleValue { get; set; }
        public string MinRangeValue { get; set; }
        public string MaxRangeValue { get; set; }
        public string ConvertSingleValue { get; set; }
        public string ConvertMinRangeValue { get; set; }
        public string ConvertMaxRangeValue { get; set; }
        public KeyStatDto ConvertKeyStatUnit { get; set; }
        public int? ConvertKeyStatUnitId { get; set; }
        public string Name { get; set; }
        public string Note { get; set; }
        public KeyStatType KeyStatType { get; set; }
    }
}
