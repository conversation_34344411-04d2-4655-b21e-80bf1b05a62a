﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.DBModels.PaymentDbModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.PaymentsDto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.PaymentServices
{
    public class PriceService : IPriceService
    {
        private readonly IFileService _fileService;
        public PriceService(IFileService fileService)
        {
            _fileService = fileService;
        }

        public List<PriceTypeDto> GetPricetypes()
        {
            var values = Enum.GetValues(typeof(PriceType)).Cast<PriceType>();
            var result = new List<PriceTypeDto>();
            foreach (var value in values)
            {
                result.Add(new PriceTypeDto
                {
                    PriceType = value,
                    PriceTypeStr = value.ToString() != "Subscription" ? value.ToString() : "Social Media Subscription",
                    ShowAmount = value == values.First()
                });
            }

            return result;
        }

        public async Task<PriceDto> CreateAsync(PriceDto model, IUnitOfWork unitOfWork)
        {
            var product = unitOfWork.ProductRepository.GetById(model.ProductId);
            if (product == null)
                throw new InvalidInputException($"product id {model.ProductId} not found");

            //Move product files to locked blob if price not Free
            if (model.PriceType != PriceType.Free)
                await _fileService.MoveProductFilesToLockedBlob(model.ProductId, unitOfWork);

            Price price = model.Adapt<Price>();
            unitOfWork.PriceRepository.Insert(price);
            await unitOfWork.SaveAsync();

            return price.Adapt<PriceDto>();
        }

        public async Task<PriceDto> UpdateAsync(PriceDto model, IUnitOfWork unitOfWork)
        {
            Price price = unitOfWork.PriceRepository.GetById(model.ProductId);

            if (price == null)
                throw new InvalidInputException($"price id {model.ProductId} not found");

            await MoveProductFilesBetweenPaidAndFreeContainerAsync(model.ProductId, price.PriceType, model.PriceType, unitOfWork);

            model.Adapt(price);
            unitOfWork.PriceRepository.Edit(price);
            await unitOfWork.SaveAsync();

            return price.Adapt<PriceDto>();
        }

        public async Task DeleteAsync(int priceId, IUnitOfWork unitOfWork)
        {
            Price price = unitOfWork.PriceRepository.GetById(priceId);
            if (price == null)
                throw new InvalidInputException($"price id {priceId} not found");

            unitOfWork.PriceRepository.Delete(price);
            await unitOfWork.SaveAsync();
        }

        public async Task<PriceDto> GetByProductAsync(int productId, IUnitOfWork unitOfWork)
        {
            PriceDto price = await unitOfWork.ProductRepository.GetAll()
                .Where(x => x.Id == productId)
                .Select(x => x.Price)
                .ProjectToType<PriceDto>()
                .FirstOrDefaultAsync();

            if (price == null)
                throw new DbItemNotFoundException($"price id {productId} not found");

            return price;
        }

        public async Task SetDefaultPriceToProductAsync(int productId, IUnitOfWork unitOfWork)
        {
            var newPrice = new Price
            {
                Amount = 0,
                PriceType = PriceType.Free,
                ProductId = productId
            };
            unitOfWork.PriceRepository.Insert(newPrice);
            await unitOfWork.SaveAsync();
        }

        public async Task<IEnumerable<PriceDto>> GetPriceByProductsAsync(EntityIdsDto model, IUnitOfWork unitOfWork)
        {
            return await unitOfWork.PriceRepository.GetAllAsNoTracking()
                .Where(x => model.Ids.Contains(x.ProductId))
                .Select(x => new PriceDto
                {
                    Amount = x.Amount,
                    PriceType = x.PriceType,
                    ProductId = x.ProductId
                })
                .ToArrayAsync();
        }

        public async Task MoveProductFilesBetweenPaidAndFreeContainerAsync(int productId, PriceType oldPriceType, PriceType newPriceType, IUnitOfWork unitOfWork, bool forceMove = false)
        {
            bool changedFromFreeToPaid = (forceMove && newPriceType != PriceType.Free) || (oldPriceType == PriceType.Free && newPriceType != PriceType.Free);
            bool changeFromPaidToFree = (forceMove && newPriceType == PriceType.Free) || (oldPriceType != PriceType.Free && newPriceType == PriceType.Free);

            //Move product files to locked blob if price not Free
            if (changedFromFreeToPaid)
                await _fileService.MoveProductFilesToLockedBlob(productId, unitOfWork);

            //Move files back if type was changed back
            if (changeFromPaidToFree)
                await _fileService.MoveProductFilesToUnLockedBlob(productId, unitOfWork);
        }
    }
}
