﻿using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Dto.HealthDashboardDto
{
    public class HealthDashboardExportProductDtoBase
    {
        public int ProductId { get; set; }

        public string ProductName { get; set; }

        public string AdminPanelProductPageUrl { get; set; }

        public string MarketProductPageUrl { get; set; }
    }

    public class HealthDashboardExportProductBrokenLinksDto : HealthDashboardExportProductDtoBase
    {
        public string AttachmentType { get; set; }

        public string AttachmentSourceUrl { get; set; }

        public int AttachmentSourceStatus { get; set; }

        public int FileId { get; set; }
    }

    public class HealthDashboardExportProductBlankDescriptionsDto : HealthDashboardExportProductDtoBase
    {
        public string Description { get; set; }
    }

    public class HealthDashboardExportNoRevitFileDto : HealthDashboardExportProductDtoBase
    {
        public string RevitFileUrl { get; set; }
    }

    public class HealthDashboardExportRevitWithoutDescriptionDto : HealthDashboardExportProductDtoBase
    {
        public string FileId { get; set; }
        public string FileName { get; set; }
    }

    public class HealthDashboardExportProductWithZipFilesDto : HealthDashboardExportProductDtoBase
    {
        public string ZipFiles { get; set; }
    }

    public class HealthDashboardExportProductWithZipFilesDbDto
    {
        public int ProductId { get; set; }

        public string ProductName { get; set; }

        public int ManufacturerId { get; set; }

        public List<string> ZipFileNames { get; set; }

        public HealthDashboardExportProductWithZipFilesDbDto()
        {
            ZipFileNames = new List<string>();
        }
    }
}