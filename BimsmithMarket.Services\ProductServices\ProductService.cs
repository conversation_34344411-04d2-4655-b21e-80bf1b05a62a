using Azure.Storage.Blobs;
using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Core.Extentions;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.DBModels.RevitProcessing;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.DetailDto;
using BIMsmithMarket.Domain.Dto.Product;
using BIMsmithMarket.Domain.Dto.SwatchboxDto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Enums.RevitProcessing;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Helpers;
using BIMsmithMarket.Services.Interfaces;
using BIMsmithMarket.Services.Search;
using BIMsmithMarket.Services.Search.FullTextSearch;
using Dasync.Collections;
using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Claims;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.ProductServices
{
    public class ProductService : IProductService
    {
        private readonly IPriceService _priceService;
        private readonly IMongoRepository<ProductMongoDto> _productRepository;
        private readonly ICacheService _cacheService;
        private readonly SlackWebHook _slackWebHook;
        private readonly IHealthDashboardService _healthDashboardService;
        private readonly IFileService _fileService;

        public ProductService(
            IPriceService priceService,
            IMongoRepository<ProductMongoDto> productRepository,
            ICacheService cacheService,
            SlackWebHook slackWebHook,
            IHealthDashboardService healthDashboardService,
            IFileService fileService)
        {
            _priceService = priceService;
            _productRepository = productRepository;
            _cacheService = cacheService;
            _slackWebHook = slackWebHook;
            _healthDashboardService = healthDashboardService;
            _fileService = fileService;
        }

        public IEnumerable<int> GetFileIdsForProjectType(int productId, IEnumerable<int> projectDataTypeIds)
        {
            var fileIds = new List<int>();
            if (projectDataTypeIds.Any())
            {
                using (var unitOfWork = UnitOfWork.Create())
                {
                    var product = unitOfWork.ProductRepository.GetById(productId);
                    if (product != null)
                    {
                        var productFileIds = unitOfWork.ProductFileRepository.GetAll().Where(x => x.ProjectDataTypeId != null && x.ProductId == product.Id && projectDataTypeIds.Contains(x.ProjectDataTypeId.Value)).Select(x => x.FileId).ToList();
                        fileIds.AddRange(productFileIds);
                        var productLineFileIds = unitOfWork.ProductLineFileRepository.GetAll().Where(x => x.ProjectDataTypeId != null && x.ProductLineId == product.ProductLineId && projectDataTypeIds.Contains(x.ProjectDataTypeId.Value)).Select(x => x.FileId).ToList();
                        fileIds.AddRange(productLineFileIds);
                        var manufacturerFileIds = unitOfWork.ManufacturerFileRepository.GetAll().Where(x => x.ProjectDataTypeId != null && x.ManufacturerId == product.ManufacturerId && projectDataTypeIds.Contains(x.ProjectDataTypeId.Value)).Select(x => x.FileId).ToList();
                        fileIds.AddRange(manufacturerFileIds);
                    }
                }
            }
            fileIds = fileIds.OrderBy(x => x).Distinct().ToList();
            return fileIds;
        }

        public bool ProductExists(int productId)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                return unitOfWork.ProductRepository.GetAll().Any(x => x.Id == productId);
            }
        }

        public async Task<bool> IsManufacturerTypeGenericAsync(int productId, IUnitOfWork unitOfWork)
        {
            return await unitOfWork.ProductRepository.GetAll()
                .Where(x => x.Id == productId)
                .Select(x => x.Manufacturer.Type == ManufacturerType.Generic)
                .FirstOrDefaultAsync();
        }

        public async Task<IsManufacturerTypeGenericResponseDto[]> BatchIsManufacturerTypeGenericAsync(IsManufacturerTypeGenericRequestDto model, IUnitOfWork unitOfWork)
        {
            return await unitOfWork.ProductRepository.GetAll()
                .Where(x => model.ProductIds.Contains(x.Id))
                .Select(x => new IsManufacturerTypeGenericResponseDto
                {
                    ProductId = x.Id,
                    IsManufacturerTypeGeneric = x.Manufacturer.Type == ManufacturerType.Generic
                })
                .ToArrayAsync();
        }

        public async Task<dynamic> LookupAsync(int manufacturerId, string name = null, string externalId = null)
        {
            if (string.IsNullOrWhiteSpace(name) && string.IsNullOrWhiteSpace(externalId)) throw new Exception("Could not find product. Please provide name or externalId");
            using (var unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var delimeter = ';';
                var productQuery = unitOfWork.ProductRepository.GetAll().Where(x => x.ManufacturerId == manufacturerId);
                if (!string.IsNullOrWhiteSpace(name))
                    productQuery = productQuery.Where(x => x.Name.ToUpper().Contains(name.ToUpper()));
                if (!string.IsNullOrWhiteSpace(externalId))
                    productQuery = productQuery.Where(x => x.ExternalId.ToUpper().Contains(externalId.ToUpper()));

                //Need to filter in memory because of split externalId by delimeter
                var products = await productQuery.AsNoTracking().ToListAsync();
                if (!string.IsNullOrWhiteSpace(externalId))
                    products = products.Where(x => x.ExternalId.ToUpper().Split(delimeter).Contains(externalId.ToUpper())).ToList();

                return products.Select(x => new
                {
                    x.Id,
                    x.Name,
                    x.Published
                }).FirstOrDefault();
            }
        }

        public async Task<AddEditProductResponse> AddAsync(AddProductModel model, string userId)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                try
                {
                    unitOfWork.BeginTransaction();
                    unitOfWork.CurrentDbContext.ChangeTracker.LazyLoadingEnabled = false;
                    unitOfWork.CurrentDbContext.ChangeTracker.AutoDetectChangesEnabled = false;
                    var result = await AddOrUpdateAsync(model, userId, unitOfWork);
                    unitOfWork.CommitTransaction();
                    return result;
                }
                catch (Exception)
                {
                    unitOfWork.RollbackTransaction();
                    throw;
                }
            }
        }

        public async Task<AddEditProductResponse> EditAsync(EditProductModel model, string userId)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                try
                {
                    unitOfWork.BeginTransaction();
                    unitOfWork.CurrentDbContext.ChangeTracker.LazyLoadingEnabled = false;
                    unitOfWork.CurrentDbContext.ChangeTracker.AutoDetectChangesEnabled = false;
                    var result = await AddOrUpdateAsync(model, userId, unitOfWork, model.Id);
                    unitOfWork.CommitTransaction();
                    return result;
                }
                catch (Exception)
                {
                    unitOfWork.RollbackTransaction();
                    throw;
                }
            }
        }

        public async Task<AddEditProductResponse> CloneAsync(int productId, string userId)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                try
                {
                    unitOfWork.CurrentDbContext.ChangeTracker.LazyLoadingEnabled = false;
                    unitOfWork.CurrentDbContext.ChangeTracker.AutoDetectChangesEnabled = false;
                    unitOfWork.BeginTransaction();

                    var product = unitOfWork.ProductRepository.GetAll()
                        .Include(a => a.ProductCategories)
                        .Include(a => a.ProductOmniclasses)
                        .Include(a => a.ProductUniclasses)
                        .Include(a => a.ProductUniformats)
                        .Include(a => a.ProductCisfbs)
                        .Include(a => a.ProductMasterformats)
                        .Include(a => a.ProductFiles).ThenInclude(b => b.File)
                        .Include(a => a.ProductPhotos)
                        .Include(a => a.ProductStats).ThenInclude(b => b.KeyStatValueList)
                        .Include(a => a.ProductQualityItems)
                        .Include(a => a.ProductCertificates)
                        .Include(a => a.ProductSamples)
                        .Include(a => a.RelatedProducts)
                        .Include(a => a.ProductDetails)
                        .FirstOrDefault(a => a.Id == productId);

                    if (product == null)
                    {
                        throw new Exception($"Product with id: {productId} was not found");
                    }

                    var vanityUrls = unitOfWork.ProductRepository.GetAll().Select(a => a.VanityURL).Distinct().ToList();

                    var createProductModel = new AddProductModel
                    {
                        Name = product.Name + " (2)",
                        ExternalProductId = product.ExternalId,
                        Description = product.Description,
                        ManufacturerId = product.ManufacturerId,
                        ProductUrl = product.ProductUrl,
                        VanityURL = CloneVanityUrl(product.VanityURL, vanityUrls),
                        ULUrl = product.ULUrl,
                        SwatchboxProductId = product.SwatchboxProductId,
                        SwatchboxOptionId = product.SwatchboxOptionId,
                        ForgeWallURL = product.ForgeWallURL,
                        ForgeFloorURL = product.ForgeFloorURL,
                        ForgeCeilingURL = product.ForgeCeilingURL,
                        ProductLineId = product.ProductLineId,
                        ForgeRoofURL = product.ForgeRoofURL,
                        Weight = product.Weight,
                        Keywords = product.Keywords,
                        IsFeatured = product.IsFeatured,
                        Note = product.Note,
                        VideoUrl = product.VideoUrl,
                        CategoryId = product.CategoryId,
                        CategoryIds = product.ProductCategories.Select(a => a.CategoryId).Distinct().ToList(),
                        OmniclassIds = product.ProductOmniclasses.Select(a => a.OmniclassId).Distinct().ToList(),
                        UniclassIds = product.ProductUniclasses.Select(a => a.UniclassId).Distinct().ToList(),
                        UniformatIds = product.ProductUniformats.Select(a => a.UniformatId).Distinct().ToList(),
                        ExternalMasterformatIds = product.ProductMasterformats.Select(a => a.ExternalMasterformatId).Distinct().ToList(),
                        CisfbIds = product.ProductCisfbs.Select(a => a.CisfbId).Distinct().ToList(),
                        Photos = product.ProductPhotos.Select(a => new PhotoModel { PhotoId = a.PhotoId }).ToList(),
                        Files = product.ProductFiles.Where(pf => pf.IsAttachment).Select(a => new FileDto
                        {
                            FileId = a.FileId,
                            IsAttachment = a.IsAttachment,
                            NeedSync = false,
                            Title = a.File.Title
                        }).ToList(),
                        ProjectFiles = product.ProductFiles.Where(pf => !pf.IsAttachment).Select(a => new ProjectFileModel
                        {
                            FileId = a.FileId,
                            Title = a.File.Title,
                            ProjectDataTypeTypeId = a.ProjectDataTypeId,
                            ContentCheckedBy = a.ContentCheckedBy,
                            ContentCreatedby = a.ContentCreatedby,
                            FileVersion = a.FileVersion,
                            SoftwareRelease = a.SoftwareRelease
                        }).ToList(),
                        ProductStats = product.ProductStats.Select(a => new ProductStatsModel
                        {
                            KeyStatId = a.KeyStatId,
                            KeyStatType = a.KeyStatType,
                            KeyStatUnitId = a.KeyStatUnitId,
                            SingleValue = a.Value,
                            MinRangeValue = a.MinRangeValue,
                            MaxRangeValue = a.MaxRangeValue,
                            ConvertKeyStatUnitId = a.ConvertKeyStatUnitId,
                            ConvertMaxRangeValue = a.ConvertMaxRangeValue,
                            ConvertMinRangeValue = a.ConvertMinRangeValue,
                            ConvertSingleValue = a.ConvertValue,
                            Note = a.Note,
                            MultipleValues = a.KeyStatValueList != null && a.KeyStatValueList.Any() ? a.KeyStatValueList.Select(b => new ProductStatsMultipleValueModel
                            {
                                KeyStatUnitId = b.KeyStatUnitId,
                                Value = b.Value,
                                MaxValue = b.MaxRangeValue,
                                ConvertValue = b.ConvertValue,
                                ConvertMaxValue = b.ConvertValue,
                                ConvertKeyStatUnitId = b.ConvertKeyStatUnitId,
                                Note = b.Note
                            }).ToList() : new List<ProductStatsMultipleValueModel>()
                        }).ToList(),
                        QualityItemIds = product.ProductQualityItems.Select(a => a.QualityItemId).Distinct().ToList(),
                        ExternalCertificateIds = product.ProductCertificates.Where(a => a.ExternalCertificateId != null).Select(a => a.ExternalCertificateId.Value).Distinct().ToList(),
                        SampleIds = product.ProductSamples.Select(a => a.SampleId).Distinct().ToList(),
                        RelatedProductIds = product.RelatedProducts.Select(a => a.RelatedProductId).Distinct().ToList(),
                        DetailIds = product.ProductDetails.Select(x => x.DetailId).ToList()
                    };

                    var result = await AddOrUpdateAsync(createProductModel, userId, unitOfWork, default(int?));

                    unitOfWork.CommitTransaction();
                    return result;
                }
                catch (Exception)
                {
                    unitOfWork.RollbackTransaction();
                    throw;
                }
            }
        }

        public async Task<AddExternalResultModel> AddExternalAsync(
            AddExternalProduct[] models,
            string host,
            AllowedManufacturersModel allowedManufacturers,
            HttpRequest httpRequest)
        {
            List<KeyStatUnit> keyStatUnits = null;
            AddExternalValidatedUserModel validatedUserModel = null;
            using (var unitOfWork = UnitOfWork.Create())
            {
                unitOfWork.CurrentDbContext.ChangeTracker.LazyLoadingEnabled = false;
                unitOfWork.CurrentDbContext.ChangeTracker.AutoDetectChangesEnabled = false;
                validatedUserModel = ValidateUser(unitOfWork, host, allowedManufacturers, httpRequest);
                keyStatUnits = unitOfWork.KeyStatUnitRepository.GetAll().ToList();
            }

            var responseList = new List<AddEditProductResponse>();
            var errors = new List<AddExternalErrorModel>();
            var userIdentifier = AuthHelper.GetUserInfo(httpRequest, ClaimTypes.NameIdentifier);
#if DEBUG
            userIdentifier = DbConstants.AdminUserId;
#endif
            var maxDegreeOfParallelism = 8;
            await models.ParallelForEachAsync(async model =>
            {
                using (var unitOfWork = UnitOfWork.Create())
                {
                    try
                    {
                        unitOfWork.CurrentDbContext.ChangeTracker.LazyLoadingEnabled = false;
                        unitOfWork.CurrentDbContext.ChangeTracker.AutoDetectChangesEnabled = false;
                        unitOfWork.BeginTransaction();
                        if (model.ProductId.HasValue)
                        {
                            var isProductRelatedToManufacturer = await unitOfWork.ProductRepository.GetAll().AnyAsync(x => x.ManufacturerId == validatedUserModel.Manufacturer.Id && x.Id == model.ProductId);
                            if (!isProductRelatedToManufacturer) throw new Exception($"Product {model.ProductId} not found for manufacturer {validatedUserModel.Manufacturer.Name}");
                        }
                        var createProductModel = CreateExternalProductModel(model, validatedUserModel);

                        var catNames = model.CategoryNames.Select(a => a.Name.ToLower()).ToList();
                        var categories = unitOfWork.CategoryRepository.GetAll().Where(a => catNames.Any(b => b == a.Name.ToLower())).ToList();
                        if (categories != null && categories.Any())
                        {
                            AddCategoriesToExternalModel(model, createProductModel, categories);
                        }
                        else
                        {
                            errors.Add(new AddExternalErrorModel
                            {
                                ErrorMessage = $"ProductId: {model.ProductId ?? -1}; ProductName: {model.Name};" + Environment.NewLine + "Categories are not valid!"
                            });
                            return;
                        }

                        AddProductLineToExternalModel(model, unitOfWork, validatedUserModel, ref createProductModel);
                        AddOmniclassesToExternalModel(model, unitOfWork, ref createProductModel);
                        AddUniclassesToExternalModel(model, unitOfWork, ref createProductModel);
                        AddUniformatsToExternalModel(model, unitOfWork, ref createProductModel);
                        AddMasterformatsToExternalModel(model, unitOfWork, ref createProductModel);
                        AddCisfbToExternalModel(model, unitOfWork, ref createProductModel);
                        await AddPhotosToExternalModelAsync(model, unitOfWork, createProductModel, validatedUserModel);
                        AddAttachmentsToExternalModel(model, unitOfWork, validatedUserModel, ref createProductModel);
                        AddProductStatToExternalModel(model, unitOfWork, keyStatUnits, ref createProductModel);
                        var response = await AddOrUpdateAsync(createProductModel, userIdentifier, unitOfWork, model.ProductId != null && model.ProductId.Value > 0 ? model.ProductId.Value : default(int?), true);
                        if (response is AddEditProductResponse)
                        {
                            responseList.Add(response);
                            unitOfWork.CommitTransaction();
                        }
                    }
                    catch (Exception ex)
                    {
                        unitOfWork.RollbackTransaction();
                        var error = new AddExternalErrorModel
                        {
                            ErrorMessage = $"ProductId: {model.ProductId ?? -1}; ProductName: {model.Name};" + Environment.NewLine,
                            Exception = ex
                        };
                        errors.Add(error);
                    }
                }
            },
            maxDegreeOfParallelism: maxDegreeOfParallelism);

            if (responseList.Any())
            {
                // Download file job uploads 50 files per minute.
                // So we sum total files count, calculate how minutes download job will upload it
                // Then we push message to update thumbnails queue and make it invisible until download job finishes upload
                var count = models.Select(x => x.Documents?.Count ?? 0).DefaultIfEmpty(0).Sum();
                if (count > 0)
                {
                    var minutesToDelay = (count / 50) + 2;
                    var azureBlobProvider = new AzureStorageService(
                        ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                        ConfigurationHelper.GetValue("Environment"),
                        bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                        false);
                    var updateThumbnailsQueue = azureBlobProvider.GetQueueByName(AzureStorageConstants.UpdateThumbnailsQueue);
                    await updateThumbnailsQueue.SendMessageAsync(validatedUserModel.Manufacturer.Id.ToString(), TimeSpan.FromMinutes(minutesToDelay).Subtract(new TimeSpan(0, 0, 1)), TimeSpan.FromMinutes(minutesToDelay));
                }
            }

            return new AddExternalResultModel
            {
                ProcessedProducts = responseList,
                Errors = errors
            };
        }

        public AddExternalValidatedUserModel ValidateUser(IUnitOfWork unitOfWork, string host, AllowedManufacturersModel allowedManufacturers, HttpRequest httpRequest)
        {
            Manufacturer manufacturer = null;
            ApplicationUser user = null;
            try
            {
                string userEmail = null;
                //Custom manufacturer api call
                userEmail = allowedManufacturers?.AllowedManufacturers?.FirstOrDefault(x => x.ManufacturerUrl == host)?.ManufacturerEmail;
                var userId = AuthHelper.GetUserInfo(httpRequest, ClaimTypes.NameIdentifier);
                if (userEmail == null)
                {
                    userEmail = AuthHelper.GetUserInfo(httpRequest, ClaimTypes.Email);
                }

                user = unitOfWork.UserRepository.GetAll().FirstOrDefault(a => a.Email == userEmail || a.UserName == userEmail || a.Id == userId);
                if (userEmail == null || user == null)
                {
                    var exception = new Exception("Invalid user");
                    exception.Data.Add("StatusCode", HttpStatusCode.BadRequest);
                    throw exception;
                }

                manufacturer = unitOfWork.ManufacturerAdminUserRepository.GetAll()
                    .Where(a => a.Email == user.Email || a.AdminUser.Email == user.Email)
                    .Select(a => a.Manufacturer).FirstOrDefault();

                if (manufacturer == null)
                {
                    var exception = new Exception("Current user do not have manufacturer permissions");
                    exception.Data.Add("StatusCode", HttpStatusCode.Unauthorized);
                    throw exception;
                }

                return new AddExternalValidatedUserModel
                {
                    Manufacturer = manufacturer,
                    User = user
                };
            }
            catch (Exception e)
            {
                var exception = new Exception(e.GetAllMessages());
                exception.Data.Add("StatusCode", HttpStatusCode.InternalServerError);
                throw exception;
            }
        }





        #region add or update methods
        public void AddOrUpdateProductCategories(Product product, IUnitOfWork unitOfWork, List<int> addedItemIds)
        {
            if (addedItemIds == null) addedItemIds = new List<int>();
            if (product.ProductCategories == null) product.ProductCategories = new List<ProductCategory>();
            var existingItemIds = product.ProductCategories.Select(x => x.CategoryId).ToList();
            var itemToAddIds = addedItemIds.Except(existingItemIds).ToList();
            var itemToDeleteIds = existingItemIds.Except(addedItemIds).ToList();
            foreach (var categoryId in itemToAddIds)
            {
                ProductCategory productCategory = new ProductCategory();
                productCategory.ProductId = product.Id;
                productCategory.CategoryId = categoryId;
                unitOfWork.ProductCategoryRepository.Insert(productCategory);
            }
            var itemsToDelete = unitOfWork.ProductCategoryRepository.GetAll()
            .Where(x => x.ProductId == product.Id && itemToDeleteIds.Contains(x.CategoryId))
            .ToList();
            itemsToDelete.ForEach(x => unitOfWork.ProductCategoryRepository.Delete(x));
            unitOfWork.Save();
        }

        public async Task AddOrUpdateProductAttachmentsAsync(Product product, IUnitOfWork unitOfWork, List<FileDto> addedProductFiles, string userId, AzureStorageService azureBlobProvider, BlobContainerClient filesContainer, string productName, string manufacturerName)
        {
            if (addedProductFiles == null) addedProductFiles = new List<FileDto>();
            if (product.ProductFiles == null) product.ProductFiles = new List<ProductFile>();
            var addedItemIds = addedProductFiles.Select(x => x.FileId).ToList();
            var existingItemIds = product.ProductFiles.Where(x => x.IsAttachment).Select(x => x.FileId).ToList();
            var existingItems = product.ProductFiles.Where(x => x.IsAttachment).ToList();
            var maxFileWeight = existingItems.Any() ? existingItems.Max(x => x.Weight) : 0;
            var itemIdsToAdd = addedItemIds.Except(existingItemIds).ToList();
            var itemsToAdd = addedProductFiles.Where(x => itemIdsToAdd.Contains(x.FileId)).ToList();
            var itemIdsToUpdate = addedItemIds.Intersect(existingItemIds).ToList();
            var itemsToUpdate = existingItems.Where(x => itemIdsToUpdate.Contains(x.FileId)).ToList();
            var itemIdsToDelete = existingItemIds.Except(addedItemIds).ToList();
            foreach (var fileModel in itemsToAdd)
            {
                ProductFile productFile = new ProductFile();
                productFile.ProductId = product.Id;
                productFile.CustomFileId = fileModel.CustomFileId;
                productFile.FileId = fileModel.FileId;
                productFile.RegionIds = fileModel.RegionIds;
                productFile.StateIds = fileModel.StateIds;
                productFile.IsAttachment = fileModel.IsAttachment;
                productFile.IsULPartnership = !fileModel.IsAttachment;
                productFile.Weight = ++maxFileWeight;
                productFile.CreatedById = userId;
                productFile.CreatedDate = DateTime.UtcNow;
                unitOfWork.ProductFileRepository.Insert(productFile);

                await HandleAddOrUpdateProductFileAsync(unitOfWork, azureBlobProvider, filesContainer, productName, manufacturerName, fileModel);
            }
            foreach (var productFile in itemsToUpdate)
            {
                var modelProductFile = addedProductFiles.FirstOrDefault(x => x.FileId == productFile.FileId);
                if (modelProductFile != null)
                {
                    bool productFileChanged = !IsAttachmentEqualsToModelFile(productFile, modelProductFile);
                    bool fileChanged = await HandleAddOrUpdateProductFileAsync(unitOfWork, azureBlobProvider, filesContainer, productName, manufacturerName, modelProductFile);

                    if (productFileChanged || fileChanged)
                    {
                        modelProductFile.Adapt(productFile);
                        productFile.IsULPartnership = !modelProductFile.IsAttachment;
                        productFile.ModifiedById = userId;
                        productFile.ModifiedDate = DateTime.UtcNow;
                        unitOfWork.ProductFileRepository.Edit(productFile);

                        await unitOfWork.SaveAsync();
                    }
                }
            }
            var productFilesToDelete = existingItems.Where(x => itemIdsToDelete.Contains(x.FileId)).ToList();
            productFilesToDelete.ForEach(x => unitOfWork.ProductFileRepository.Delete(x));
            await unitOfWork.SaveAsync();
        }

        public void AddOrUpdateProductCertificates(Product product, IUnitOfWork unitOfWork, List<int> addedItemIds, string userId)
        {
            if (addedItemIds == null) addedItemIds = new List<int>();
            if (product.ProductCertificates == null) product.ProductCertificates = new List<ProductCertificate>();
            var existingItemIds = product.ProductCertificates.Where(x => x.ExternalCertificateId != null).Select(x => x.ExternalCertificateId.Value).ToList();
            var itemToAddIds = addedItemIds.Except(existingItemIds).ToList();
            var itemToDeleteIds = existingItemIds.Except(addedItemIds).ToList();
            foreach (var certificateId in itemToAddIds)
            {
                AddProductCertificate(product.Id, unitOfWork, userId, certificateId);
            }
            var itemsToDelete = unitOfWork.ProductCertificateRepository.GetAll()
            .Where(x => x.ProductId == product.Id && x.ExternalCertificateId != null && itemToDeleteIds.Contains(x.ExternalCertificateId.Value))
            .ToList();
            itemsToDelete.ForEach(x => unitOfWork.ProductCertificateRepository.Delete(x));
            unitOfWork.Save();
        }

        public async Task AddOrUpdateRelatedProductsAsync(Product product, IUnitOfWork unitOfWork, List<int> addedItemIds, string userId)
        {
            if (addedItemIds == null) addedItemIds = new List<int>();
            if (product.RelatedProducts == null) product.RelatedProducts = new List<RelatedProduct>();
            var existingItemIds = product.RelatedProducts.Select(x => x.RelatedProductId).ToList();
            var itemIdsToAdd = addedItemIds.Except(existingItemIds).ToList();
            var itemToDeleteIds = existingItemIds.Except(addedItemIds).ToList();
            foreach (var relatedProductId in itemIdsToAdd)
            {
                AddRelatedProduct(product.Id, unitOfWork, userId, relatedProductId);
            }
            var itemsToDelete = unitOfWork.RelatedProductRepository.GetAll()
            .Where(x => x.ProductId == product.Id && itemToDeleteIds.Contains(x.RelatedProductId))
            .ToList();
            itemsToDelete.ForEach(x => unitOfWork.RelatedProductRepository.Delete(x));
            await unitOfWork.SaveAsync();
        }

        public async Task AddOrUpdateProductSamplesAsync(Product product, IUnitOfWork unitOfWork, List<int> addedItemIds, string userId)
        {
            if (addedItemIds == null) addedItemIds = new List<int>();
            if (product.ProductSamples == null) product.ProductSamples = new List<ProductSample>();
            var existingItemIds = product.ProductSamples.Select(x => x.SampleId).ToList();
            var itemIdsToAdd = addedItemIds.Except(existingItemIds).ToList();
            var itemToDeleteIds = existingItemIds.Except(addedItemIds).ToList();
            foreach (var sampleId in itemIdsToAdd)
            {
                AddProductSample(product.Id, unitOfWork, userId, sampleId);
            }
            var itemsToDelete = unitOfWork.ProductSampleRepository.GetAll()
            .Where(x => x.ProductId == product.Id && itemToDeleteIds.Contains(x.SampleId))
            .ToList();
            itemsToDelete.ForEach(x => unitOfWork.ProductSampleRepository.Delete(x));
            await unitOfWork.SaveAsync();
        }

        public async Task AddOrUpdateProductDetailsAsync(Product product, IUnitOfWork unitOfWork, List<int> addedItemIds, string userId)
        {
            addedItemIds = addedItemIds ?? new List<int>();
            var existingItemIds = unitOfWork.ProductDetailRepository.GetAll()
                .Where(x => x.ProductId == product.Id).Select(x => x.DetailId)
                .ToList();
            var itemToAddIds = addedItemIds.Except(existingItemIds);
            var itemToDeleteIds = existingItemIds.Except(addedItemIds);
            if (itemToAddIds.Any())
            {
                foreach (var detailId in itemToAddIds.ToList())
                {
                    var dbDetail = unitOfWork.DetailRepository.GetById(detailId);
                    if (dbDetail != null)
                    {
                        AddProductDetail(product.Id, unitOfWork, userId, detailId);
                    }
                }
            }
            if (itemToDeleteIds.Any())
            {
                var itemsToDelete = unitOfWork.ProductDetailRepository.GetAll()
                    .Where(x => itemToDeleteIds.ToList().Contains(x.DetailId) && x.ProductId == product.Id)
                    .ToList();
                itemsToDelete.ForEach(x => unitOfWork.ProductDetailRepository.Delete(x));
            }
            await unitOfWork.SaveAsync();
        }

        public async Task AddOrUpdateProductQualityItemsAsync(Product product, IUnitOfWork unitOfWork, List<int> addedItemIds, string userId)
        {
            if (addedItemIds == null) addedItemIds = new List<int>();
            if (product.ProductQualityItems == null) product.ProductQualityItems = new List<ProductQualityItem>();
            var existingItemIds = product.ProductQualityItems.Select(x => x.QualityItemId).ToList();
            var itemIdsToAdd = addedItemIds.Except(existingItemIds).ToList();
            var itemsToDeleteIds = existingItemIds.Except(addedItemIds).ToList();
            foreach (var qualityItemId in itemIdsToAdd)
            {
                AddProductQualityItem(product.Id, unitOfWork, userId, qualityItemId);
            }
            var itemsToDelete = unitOfWork.ProductQualityItemRepository.GetAll()
            .Where(x => x.ProductId == product.Id && itemsToDeleteIds.Contains(x.QualityItemId))
            .ToList();
            itemsToDelete.ForEach(x => unitOfWork.ProductQualityItemRepository.Delete(x));
            await unitOfWork.SaveAsync();
        }

        public async Task AddOrUpdateProductCisfbsAsync(Product product, IUnitOfWork unitOfWork, List<int> addedItemIds, string userId)
        {
            if (addedItemIds == null) addedItemIds = new List<int>();
            if (product.ProductCisfbs == null) product.ProductCisfbs = new List<ProductCisfb>();
            var existingItemIds = product.ProductCisfbs.Select(x => x.CisfbId).ToList();
            var itemIdsToAdd = addedItemIds.Except(existingItemIds).ToList();
            var itemsToDeleteIds = existingItemIds.Except(addedItemIds).ToList();
            foreach (var cisfbId in itemIdsToAdd)
            {
                AddProductCisfb(product.Id, unitOfWork, userId, cisfbId);
            }
            var itemsToDelete = unitOfWork.ProductCisfbRepository.GetAll()
            .Where(x => x.ProductId == product.Id && itemsToDeleteIds.Contains(x.CisfbId))
            .ToList();
            itemsToDelete.ForEach(x => unitOfWork.ProductCisfbRepository.Delete(x));
            await unitOfWork.SaveAsync();
        }

        public async Task AddOrUpdateProductOmniclassesAsync(Product product, IUnitOfWork unitOfWork, List<int> addedItemIds, string userId)
        {
            if (addedItemIds == null) addedItemIds = new List<int>();
            if (product.ProductOmniclasses == null) product.ProductOmniclasses = new List<ProductOmniclass>();
            var existingItemIds = product.ProductOmniclasses.Select(x => x.OmniclassId).ToList();
            var itemIdsToAdd = addedItemIds.Except(existingItemIds).ToList();
            var itemsToDeleteIds = existingItemIds.Except(addedItemIds).ToList();
            foreach (var omniclassId in itemIdsToAdd)
            {
                AddProductOmniclass(product.Id, unitOfWork, userId, omniclassId);
            }
            var itemsToDelete = unitOfWork.ProductOmniclassRepository.GetAll()
            .Where(x => x.ProductId == product.Id && itemsToDeleteIds.Contains(x.OmniclassId))
            .ToList();
            itemsToDelete.ForEach(x => unitOfWork.ProductOmniclassRepository.Delete(x));
            await unitOfWork.SaveAsync();
        }

        public async Task AddOrUpdateProductUniclassesAsync(Product product, IUnitOfWork unitOfWork, List<int> addedItemIds, string userId)
        {
            if (addedItemIds == null) addedItemIds = new List<int>();
            if (product.ProductUniclasses == null) product.ProductUniclasses = new List<ProductUniclass>();
            var existingItemIds = product.ProductUniclasses.Select(x => x.UniclassId).ToList();
            var itemIdsToAdd = addedItemIds.Except(existingItemIds).ToList();
            var itemsToDeleteIds = existingItemIds.Except(addedItemIds).ToList();
            foreach (var uniclassId in itemIdsToAdd)
            {
                AddProductUniclass(product.Id, unitOfWork, userId, uniclassId);
            }
            var itemsToDelete = unitOfWork.ProductUniclassRepository.GetAll()
            .Where(x => x.ProductId == product.Id && itemsToDeleteIds.Contains(x.UniclassId))
            .ToList();
            itemsToDelete.ForEach(x => unitOfWork.ProductUniclassRepository.Delete(x));
            await unitOfWork.SaveAsync();
        }

        public async Task AddOrUpdateProductUniformatsAsync(Product product, IUnitOfWork unitOfWork, List<int> addedItemIds, string userId)
        {
            if (addedItemIds == null) addedItemIds = new List<int>();
            if (product.ProductUniformats == null) product.ProductUniformats = new List<ProductUniformat>();
            var existingItemIds = product.ProductUniformats.Select(x => x.UniformatId).ToList();
            var itemIdsToAdd = addedItemIds.Except(existingItemIds).ToList();
            var itemsToDeleteIds = existingItemIds.Except(addedItemIds).ToList();
            foreach (var uniformatId in itemIdsToAdd)
            {
                AddProductUniformat(product.Id, unitOfWork, userId, uniformatId);
            }
            var itemsToDelete = unitOfWork.ProductUniformatRepository.GetAll()
            .Where(x => x.ProductId == product.Id && itemsToDeleteIds.Contains(x.UniformatId))
            .ToList();
            itemsToDelete.ForEach(x => unitOfWork.ProductUniformatRepository.Delete(x));
            await unitOfWork.SaveAsync();
        }

        public async Task AddOrUpdateProductMasterformatsAsync(Product product, IUnitOfWork unitOfWork, List<int> addedItemIds, string userId)
        {
            if (addedItemIds == null) addedItemIds = new List<int>();
            if (product.ProductMasterformats == null) product.ProductMasterformats = new List<ProductMasterformat>();
            var existingItemIds = product.ProductMasterformats.Select(x => x.ExternalMasterformatId).ToList();
            var itemIdsToAdd = addedItemIds.Except(existingItemIds).ToList();
            var itemsToDeleteIds = existingItemIds.Except(addedItemIds).ToList();
            foreach (var masterformatId in itemIdsToAdd)
            {
                AddProductMasterformat(product.Id, unitOfWork, userId, masterformatId);
            }
            var itemsToDelete = unitOfWork.ProductMasterformatRepository.GetAll()
            .Where(x => x.ProductId == product.Id && itemsToDeleteIds.Contains(x.ExternalMasterformatId))
            .ToList();
            itemsToDelete.ForEach(x => unitOfWork.ProductMasterformatRepository.Delete(x));
            await unitOfWork.SaveAsync();
        }

        public async Task AddOrUpdateProductPhotosAsync(Product product, IUnitOfWork unitOfWork, List<PhotoModel> addedProductPhotos, string userId, AzureStorageService azureBlobProvider, BlobContainerClient photosContainer, string productName, string manufacturerName)
        {
            if (addedProductPhotos == null) addedProductPhotos = new List<PhotoModel>();
            if (product.ProductPhotos == null) product.ProductPhotos = new List<ProductPhoto>();
            var addedItemIds = addedProductPhotos.Select(x => x.PhotoId).ToList();
            var existingItemIds = product.ProductPhotos.Select(x => x.PhotoId).ToList();
            var existingItems = product.ProductPhotos.ToList();
            var itemIdsToAdd = addedItemIds.Except(existingItemIds).ToList();
            var itemsToAdd = addedProductPhotos.Where(x => itemIdsToAdd.Contains(x.PhotoId)).ToList();
            var itemIdsToUpdate = addedItemIds.Intersect(existingItemIds).ToList();
            var itemsToUpdate = existingItems.Where(x => itemIdsToUpdate.Contains(x.PhotoId)).ToList();
            var itemIdsToDelete = existingItemIds.Except(addedItemIds).ToList();
            foreach (var photoModel in itemsToAdd)
            {
                await AddProductPhotoAsync(product, unitOfWork, userId, azureBlobProvider, photosContainer, productName, manufacturerName, photoModel);
            }
            foreach (var productPhoto in itemsToUpdate)
            {
                var modelProductPhoto = addedProductPhotos.FirstOrDefault(x => x.PhotoId == productPhoto.PhotoId);
                if (modelProductPhoto != null)
                {
                    await UpdateProductPhotoAsync(product, unitOfWork, userId, azureBlobProvider, photosContainer, productName, manufacturerName, productPhoto, modelProductPhoto);
                }
            }
            var productPhotosToDelete = existingItems.Where(x => itemIdsToDelete.Contains(x.PhotoId)).ToList();
            productPhotosToDelete.ForEach(x => unitOfWork.ProductPhotoRepository.Delete(x));
            //TODO: Rework when file deletion is implementing
            //itemIdsToDelete.ForEach(x =>
            //{
            //    var otherProductsHaveUseThisPhoto = unitOfWork.ProductPhotoRepository.GetAll().Any(p => p.PhotoId == x && p.ProductId != product.Id);
            //    if (!otherProductsHaveUseThisPhoto)
            //    {
            //        var photo = unitOfWork.PhotoRepository.GetById(x);
            //        if (photo != null) unitOfWork.PhotoRepository.Delete(photo);
            //    }
            //});
            await unitOfWork.SaveAsync();
        }

        public async Task AddOrUpdateProductProjectFilesAsync(Product product, IUnitOfWork unitOfWork, List<ProjectFileModel> addedProjectFiles, string userId)
        {
            if (addedProjectFiles == null) addedProjectFiles = new List<ProjectFileModel>();
            if (product.ProductFiles == null) product.ProductFiles = new List<ProductFile>();
            var addedItemIds = addedProjectFiles.Select(x => x.FileId).ToList();
            var existingItems = product.ProductFiles.Where(x => !x.IsAttachment).ToList();
            var existingItemIds = existingItems.Select(x => x.FileId).ToList();

            var itemIdsToAdd = addedItemIds.Except(existingItemIds).ToList();
            var itemsToAdd = addedProjectFiles.Where(x => itemIdsToAdd.Contains(x.FileId)).ToList();
            var itemIdsToUpdate = addedItemIds.Intersect(existingItemIds).ToList();
            var itemsToUpdate = existingItems.Where(x => itemIdsToUpdate.Contains(x.FileId)).ToList();
            var itemIdsToDelete = existingItemIds.Except(addedItemIds).ToList();
            foreach (var projectFile in itemsToAdd)
            {
                AddProductProjectFile(projectFile, product.Id, userId, unitOfWork);
            }
            foreach (var projectFile in itemsToUpdate)
            {
                var modelProjectFile = addedProjectFiles.FirstOrDefault(x => x.FileId == projectFile.FileId);
                if (modelProjectFile != null)
                {
                    UpdateProductProjectFile(projectFile, modelProjectFile, product.Id, userId, unitOfWork);
                }
            }
            var projectFilesToDelete = existingItems.Where(x => itemIdsToDelete.Contains(x.FileId)).ToList();
            projectFilesToDelete.ForEach(x => unitOfWork.ProductFileRepository.Delete(x));
            await unitOfWork.SaveAsync();
        }

        public async Task AddOrUpdateProductKeyStatsAsync(Product product, IUnitOfWork unitOfWork, List<ProductStatsModel> addedProductStats, string userId, int? productId, bool isRestoredFromBackup)
        {
            if (productId != null && !isRestoredFromBackup)
            {
                var productStatsToDelete = product.ProductStats.ToList();
                foreach (var productStatToDelete in productStatsToDelete)
                {
                    unitOfWork.KeyStatValueListRepository.Delete(productStatToDelete.KeyStatValueList.ToList());
                }
                unitOfWork.ProductStatsRepository.Delete(productStatsToDelete);

                await unitOfWork.SaveAsync();
            }

            if (addedProductStats != null)
            {
                var keyStatOrder = 0;
                //Add product stats
                foreach (var productStats in addedProductStats)
                {
                    List<KeyStatValueList> keyStatValueList = new List<KeyStatValueList>();

                    ProductStats pStats = new ProductStats();
                    pStats.ProductId = product.Id;
                    pStats.KeyStatId = productStats.KeyStatId;
                    pStats.KeyStatType = productStats.KeyStatType;
                    pStats.Order = keyStatOrder++;

                    pStats.KeyStatUnitId = productStats.KeyStatUnitId;
                    pStats.ConvertKeyStatUnitId = productStats.ConvertKeyStatUnitId;

                    pStats.CreatedById = userId;
                    pStats.CreatedDate = DateTime.UtcNow;
                    pStats.Note = productStats.Note;

                    if (productStats.KeyStatType == KeyStatType.SingleNumeric || productStats.KeyStatType == KeyStatType.TextValue)
                    {
                        pStats.Value = productStats.SingleValue;
                        pStats.ConvertValue = productStats.ConvertSingleValue;
                    }
                    else if (productStats.KeyStatType == KeyStatType.NumericRange)
                    {
                        pStats.MinRangeValue = productStats.MinRangeValue;
                        pStats.MaxRangeValue = productStats.MaxRangeValue;
                        pStats.ConvertMinRangeValue = productStats.ConvertMinRangeValue;
                        pStats.ConvertMaxRangeValue = productStats.ConvertMaxRangeValue;
                    }
                    else if (productStats.KeyStatType == KeyStatType.MultivalueNumeric || productStats.KeyStatType == KeyStatType.TextMultivalue)
                    {
                        if (productStats.MultipleValues != null)
                        {
                            foreach (var item in productStats.MultipleValues)
                            {
                                KeyStatValueList keyStatValueListItem = new KeyStatValueList();
                                if (item.MaxValue == null)
                                {
                                    keyStatValueListItem.Value = item.Value;
                                    keyStatValueListItem.ConvertValue = item.ConvertValue;
                                }
                                else
                                {
                                    keyStatValueListItem.MinRangeValue = item.MinValue;
                                    keyStatValueListItem.Value = item.Value;
                                    keyStatValueListItem.MaxRangeValue = item.MaxValue;
                                    keyStatValueListItem.ConvertMinRangeValue = item.ConvertValue;
                                    keyStatValueListItem.ConvertMaxRangeValue = item.ConvertMaxValue;
                                }
                                if (keyStatValueListItem.Value == null) keyStatValueListItem.Value = string.Empty;

                                keyStatValueListItem.Note = item.Note;
                                keyStatValueListItem.KeyStatUnitId = item.KeyStatUnitId;
                                keyStatValueListItem.ConvertKeyStatUnitId = item.ConvertKeyStatUnitId;

                                keyStatValueListItem.CreatedById = userId;
                                keyStatValueListItem.CreatedDate = DateTime.UtcNow;
                                keyStatValueList.Add(keyStatValueListItem);
                            }
                        }
                    }
                    else if (productStats.KeyStatType == KeyStatType.None)
                    {
                        // empty key stat
                    }
                    else
                    {
                        throw new NotImplementedException("Passed not implemented KeyStatType");
                    }

                    unitOfWork.ProductStatsRepository.Insert(pStats);
                    await unitOfWork.SaveAsync();

                    foreach (var item in keyStatValueList)
                    {
                        item.ProductStatsId = pStats.Id;
                        unitOfWork.KeyStatValueListRepository.Insert(item);
                    }
                }
            }
        }
        #endregion

        public void HandleProductMedataData(ref Product product, string mainCategoryName, string manufacturerName)
        {
            var metaData = MetaDataHelper.GetProductMetaData(product.Name, mainCategoryName, manufacturerName);
            if (string.IsNullOrWhiteSpace(product.MetaTitle))
            {
                product.MetaTitle = metaData.Title;
            }
            if (string.IsNullOrWhiteSpace(product.MetaDescription))
            {
                product.MetaDescription = metaData.Description;
            }
            if (string.IsNullOrWhiteSpace(product.MetaKeywords))
            {
                product.MetaKeywords = metaData.Keywords;
            }
        }

        public async Task<ICollection<SwatchboxProductsResponseDto>> SwatchboxListAsync(SwatchboxProductsRequestDto model)
        {
            if (model.ProductId == null)
                throw new InvalidInputException("Invalid product id");

            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var productQuery = unitOfWork.ProductRepository.GetAll().Where(x => x.Published);

                if (model.ProductId.HasValue)
                    productQuery = productQuery.Where(x => x.SwatchboxProductId == model.ProductId.Value.ToString())
                                               .Include(x => x.Manufacturer)
                                               .Include(x => x.Photo)
                                               .Take(model.Take);

                Product[] products = await productQuery.AsNoTracking().ToArrayAsync();
                return products.Adapt<SwatchboxProductsResponseDto[]>();
            }
        }

        public async Task<List<ProjectFileDto>> GetProjectFilesAsync(EntityIdsDto model, IUnitOfWork unitOfWork)
        {
            if (model == null)
                throw new InvalidInputException("Invalid model");

            return await unitOfWork.ProductFileRepository.GetAllAsNoTracking()
                .Where(w => model.Ids.Contains(w.FileId))
                .Distinct()
                .Select(x => new ProjectFileDto
                {
                    Id = x.FileId,
                    FileName = x.File.FileName,
                    ProjectDataTypeId = x.ProjectDataTypeId,
                    RegionIds = x.RegionIds,
                    StateIds = x.StateIds,
                    Title = x.File.Title,
                    Url = x.File.Url
                })
                .ToListAsync();
        }

        public async Task<List<ProductFileDto>> GetProductFilesAsync(EntityIdsDto model, IUnitOfWork unitOfWork)
        {
            return await unitOfWork.ProductFileRepository.GetAllAsNoTracking()
                .Where(w => model.Ids.Contains(w.FileId))
                .Select(x => new ProductFileDto
                {
                    Id = x.FileId,
                    Title = x.File.Title,
                    FileName = x.File.FileName,
                    Url = x.File.Url,
                    Preview = x.File.PreviewUrl,
                    RegionIds = x.RegionIds,
                    StateIds = x.StateIds,
                    MimeType = x.File.MediaType
                })
                .ToListAsync();
        }

        public async Task<int[]> GetProjectFilesForProductAsync(int productId, IUnitOfWork unitOfWork)
        {
            return await unitOfWork.ProductFileRepository.GetAll()
                .Where(w => w.ProductId == productId && !w.IsAttachment)
                .Select(s => s.FileId)
                .Distinct()
                .ToArrayAsync();
        }

        public async Task<int[]> GetProjectFilesDataTypesAsync(int productId, IUnitOfWork unitOfWork)
        {
            return await unitOfWork.ProductFileRepository.GetAll()
                .Where(w => w.ProductId == productId && !w.IsAttachment && w.ProjectDataTypeId.HasValue)
                .Select(s => s.ProjectDataTypeId.Value)
                .Distinct()
                .ToArrayAsync();
        }

        public async Task<int[]> GetProductFilesForProductAsync(int productId, IUnitOfWork unitOfWork)
        {
            var productInfo = await unitOfWork.ProductRepository.GetAll()
                .Where(x => x.Id == productId)
                .Select(x => new
                {
                    x.Id,
                    x.ProductLineId,
                    x.ManufacturerId
                })
                .FirstOrDefaultAsync();

            List<int> fileIds = new();

            fileIds.AddRange(await unitOfWork.ProductFileRepository.GetAll()
                .Where(x => x.ProductId == productId && x.IsAttachment)
                .Select(x => x.FileId)
                .ToArrayAsync());

            fileIds.AddRange(await unitOfWork.ManufacturerFileRepository.GetAll()
                .Where(x => x.ManufacturerId == productInfo.ManufacturerId && x.IsAttachment)
                .Select(x => x.FileId)
                .ToArrayAsync());

            if (productInfo.ProductLineId.HasValue)
            {
                fileIds.AddRange(await unitOfWork.ProductLineFileRepository.GetAll()
                    .Where(x => x.ProductLineId == productInfo.ProductLineId && x.IsAttachment)
                    .Select(x => x.FileId)
                    .ToArrayAsync());
            }

            return fileIds.Distinct().ToArray();
        }

        /// <summary>
        /// Basic method that used for add/update product
        /// </summary>
        /// <param name="model"></param>
        /// <param name="userId"></param>
        /// <param name="unitOfWork"></param>
        /// <param name="productId"></param>
        /// <param name="isExternalCall"></param>
        /// <param name="isRestoredFromBackup"></param>
        /// <returns></returns>
        public async Task<AddEditProductResponse> AddOrUpdateAsync(
           AddProductModel model,
           string userId,
           IUnitOfWork unitOfWork,
           int? productId = null,
           bool isExternalCall = false,
           bool isRestoredFromBackup = false)
        {
            var isNewProduct = productId == null || productId == default(int?) || isRestoredFromBackup;
            ValidateProduct(model, productId, unitOfWork);
            StringValidatorHelper.ValidateStrings(model);

            string keywords = null;
            if (!string.IsNullOrWhiteSpace(model.Keywords))
            {
                keywords = " " + model.Keywords.Trim(' ', ',').Replace(",", ", ").Replace("  ", " ") + ", ";
            }

            var manufacturer = unitOfWork.ManufacturerRepository.GetById(model.ManufacturerId);

            Product product = new Product();
            if (productId != null && !isRestoredFromBackup)
            {
                product = await GetForEditAsync(product, productId, unitOfWork);
            }

            AssignProduct(model, ref product, userId, productId, keywords, isExternalCall, isRestoredFromBackup);

            if (product.VanityURL == null)
            {
                await UpdateVanityHistoryAsync(model, product, unitOfWork, userId);
                product.VanityURL = !string.IsNullOrWhiteSpace(model.VanityURL) ? model.VanityURL.AsVanityUrl() : model.Name.AsVanityUrl();
            }

            var mainCategoryName = unitOfWork.CategoryRepository.GetAll().Where(a => a.Id == model.CategoryId).Select(a => a.Name).First();
            HandleProductMedataData(ref product, mainCategoryName, manufacturer.Name);

            if (productId != null && !isRestoredFromBackup)
            {
                unitOfWork.ProductRepository.Edit(product);
            }
            else
            {
                if (isRestoredFromBackup)
                {
                    product.Id = productId.Value;
                    var setIdentityOnQuery = SqlHelper.GenerateSetIdentityOnQuery(DbConstants.ProductsTableName);
                    var insertQuery = SqlHelper.GenerateInsertQuery(product, DbConstants.ProductsTableName);
                    var setIndentityOffQuery = SqlHelper.GenerateSetIdentityOffQuery(DbConstants.ProductsTableName);
                    var query = string.Join(";", setIdentityOnQuery, insertQuery.Query, setIndentityOffQuery);
                    await unitOfWork.CurrentDbContext.Database.ExecuteSqlRawAsync(query, insertQuery.Parameters);
                }
                else
                {
                    unitOfWork.ProductRepository.Insert(product);
                }
            }

            await unitOfWork.SaveAsync();

            var azureBlobProvider = new AzureStorageService(
                ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                ConfigurationHelper.GetValue("Environment"),
                bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                false);
            var photosContainer = azureBlobProvider.GetContainerByName(AzureStorageConstants.PhotosContainer);
            var rgx = new Regex("[^a-zA-Z0-9 -]");
            var productName = rgx.Replace(product.Name, "");
            var manufacturerName = rgx.Replace(manufacturer.Name, "");

            #region add or update product related entities
            AddOrUpdateProductCategories(product, unitOfWork, model.CategoryIds);
            if (!isExternalCall) AddOrUpdateProductCertificates(product, unitOfWork, model.ExternalCertificateIds, userId);
            if (!isExternalCall) await AddOrUpdateRelatedProductsAsync(product, unitOfWork, model.RelatedProductIds, userId);
            if (!isExternalCall) await AddOrUpdateProductSamplesAsync(product, unitOfWork, model.SampleIds, userId);
            if (!isExternalCall) await AddOrUpdateProductDetailsAsync(product, unitOfWork, model.DetailIds, userId);
            if (!isExternalCall) await AddOrUpdateProductQualityItemsAsync(product, unitOfWork, model.QualityItemIds, userId);
            await AddOrUpdateProductCisfbsAsync(product, unitOfWork, model.CisfbIds, userId);
            await AddOrUpdateProductOmniclassesAsync(product, unitOfWork, model.OmniclassIds, userId);
            await AddOrUpdateProductUniclassesAsync(product, unitOfWork, model.UniclassIds, userId);
            await AddOrUpdateProductUniformatsAsync(product, unitOfWork, model.UniformatIds, userId);
            await AddOrUpdateProductMasterformatsAsync(product, unitOfWork, model.ExternalMasterformatIds, userId);
            await AddOrUpdateProductAttachmentsAsync(product, unitOfWork, model.Files, userId, azureBlobProvider, photosContainer, productName, manufacturerName);
            await AddOrUpdateProductPhotosAsync(product, unitOfWork, model.Photos, userId, azureBlobProvider, photosContainer, productName, manufacturerName);
            if (!isExternalCall) await AddOrUpdateProductProjectFilesAsync(product, unitOfWork, model.ProjectFiles, userId);
            await AddOrUpdateProductKeyStatsAsync(product, unitOfWork, model.ProductStats, userId, productId, isRestoredFromBackup);
            await SetAttachmentsWeightAsync(product, isNewProduct, manufacturer.Id, unitOfWork, model.Files);
            #endregion

            await unitOfWork.SaveAsync();
            if (product.Price == null)
                await _priceService.SetDefaultPriceToProductAsync(product.Id, unitOfWork);

            if (model.AddToULQueue)
            {
                var ulProducts = azureBlobProvider.GetQueueByName(AzureStorageConstants.UlProductsQueue);
                await ulProducts.SendMessageAsync(product.Id.ToString());
                product.ULSyncStatus = ULSyncStatus.WaitForSync;
                await unitOfWork.SaveAsync();
                unitOfWork.ProductRepository.Edit(product);
            }

            if (!isNewProduct && product.Price != null)
                await _priceService.MoveProductFilesBetweenPaidAndFreeContainerAsync(product.Id, product.Price.PriceType, product.Price.PriceType, unitOfWork, true);

            await SaveProductToMongoAsync(product.Id, productId.HasValue, unitOfWork);

            return product.Adapt<AddEditProductResponse>();
        }

        /// <summary>
        /// Delete product/s and relations
        /// </summary>
        /// <param name="unitOfWork"></param>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task DeleteProductsByIds(IUnitOfWork unitOfWork, IList<int> ids)
        {
            var products = unitOfWork.ProductRepository.GetAll()
                                     .Include(x => x.ProductCategories)
                                     .Include(x => x.RelatedProducts)
                                     .Include(x => x.ProductFiles)
                                     .Include(x => x.ProductStats).ThenInclude(k => k.KeyStatValueList)
                                     .Include(x => x.ProductCertificates)
                                     .Include(x => x.ProductPhotos)
                                     .Include(x => x.ProductCisfbs)
                                     .Include(x => x.ProductMasterformats)
                                     .Include(x => x.ProductOmniclasses)
                                     .Include(x => x.ProductUniclasses)
                                     .Include(x => x.ProductUniformats)
                                     .Include(x => x.ULCertificates)
                                     .Include(x => x.ULRatingSystemsSustainableCredits)
                                     .Include(x => x.ULStandardNumbers)
                                     .Include(x => x.ProductRatings)
                                     .Include(x => x.ProductDetails)
                                     .Include(x => x.ProductQualityItems)
                                     .Include(x => x.ProductPrices)
                                     .Include(x => x.Price)
                                     .Where(a => ids.Contains(a.Id));

            foreach (Product product in products)
            {
                #region Delete Related Data
                unitOfWork.ProductCategoryRepository.Delete(product.ProductCategories.ToList());
                unitOfWork.RelatedProductRepository.Delete(product.RelatedProducts.ToList());
                unitOfWork.ProductFileRepository.Delete(product.ProductFiles.ToList());

                foreach (var productStatToDelete in product.ProductStats.ToList())
                {
                    var keyStatsToDelete = productStatToDelete.KeyStatValueList.ToList();
                    unitOfWork.KeyStatValueListRepository.Delete(keyStatsToDelete);
                }
                unitOfWork.ProductStatsRepository.Delete(product.ProductStats.ToList());
                unitOfWork.ProductCertificateRepository.Delete(product.ProductCertificates.ToList());
                unitOfWork.ProductPhotoRepository.Delete(product.ProductPhotos.ToList());
                unitOfWork.ProductCisfbRepository.Delete(product.ProductCisfbs.ToList());
                unitOfWork.ProductMasterformatRepository.Delete(product.ProductMasterformats.ToList());
                unitOfWork.ProductOmniclassRepository.Delete(product.ProductOmniclasses.ToList());
                unitOfWork.ProductUniclassRepository.Delete(product.ProductUniclasses.ToList());
                unitOfWork.ProductUniformatRepository.Delete(product.ProductUniformats.ToList());
                unitOfWork.ULCertificateRepository.Delete(product.ULCertificates.ToList());
                unitOfWork.ULRatingSystemSustainableCreditRepository.Delete(product.ULRatingSystemsSustainableCredits.ToList());
                unitOfWork.ULStandardNumberRepository.Delete(product.ULStandardNumbers.ToList());
                unitOfWork.ProductRatingRepository.Delete(product.ProductRatings.ToList());
                unitOfWork.ProductDetailRepository.Delete(product.ProductDetails.ToList());
                unitOfWork.ProductQualityItemRepository.Delete(product.ProductQualityItems.ToList());
                unitOfWork.ProductPriceRepository.Delete(product.ProductPrices.ToList());
                if (product.Price != null)
                    unitOfWork.PriceRepository.Delete(product.Price);
                #endregion

                unitOfWork.ProductRepository.Delete(product);
                await _productRepository.DeleteAsync(product.Id);
            }
            await unitOfWork.SaveAsync();
        }

        public async Task<ProductGetDto> GetProductForAdminPanelAsync(int id, IUnitOfWork unitOfWork)
        {
            ProductGetDto product = await unitOfWork.ProductRepository.GetAll()
                .AsSplitQuery()
                .Select(x => new ProductGetDto
                {
                    Id = x.Id,
                    AssemblyCode = x.AssemblyCode,
                    Categories = x.ProductCategories.Select(c => new
                    {
                        Id = c.CategoryId,
                        Name = c.Category.Name,
                        VanityUrl = c.Category.VanityUrl,
                        Parent = c.Category.ParentCategory == null ? null : new
                        {
                            Id = c.Category.ParentCategoryId.Value,
                            Name = c.Category.ParentCategory.Name,
                            VanityUrl = c.Category.VanityUrl,
                            Parent = c.Category.ParentCategory.ParentCategory == null ? null : new
                            {
                                Id = c.Category.ParentCategory.ParentCategoryId.Value,
                                Name = c.Category.ParentCategory.ParentCategory.Name,
                                VanityUrl = c.Category.VanityUrl
                            }
                        }
                    }),
                    CategoryId = x.CategoryId,
                    Category = x.Category == null ? null : new
                    {
                        Id = x.CategoryId,
                        Name = x.Category.Name,
                        VanityUrl = x.Category.VanityUrl,
                        Parent = x.Category.ParentCategory == null ? null : new
                        {
                            Id = x.Category.ParentCategoryId.Value,
                            Name = x.Category.ParentCategory.Name,
                            VanityUrl = x.Category.ParentCategory.VanityUrl,
                            Parent = x.Category.ParentCategory.ParentCategory == null ? null : new
                            {
                                Id = x.Category.ParentCategory.ParentCategoryId.Value,
                                Name = x.Category.ParentCategory.ParentCategory.Name,
                                VanityUrl = x.Category.ParentCategory.ParentCategory.VanityUrl
                            }
                        }
                    },
                    CategoryIds = x.ProductCategories.Select(c => c.CategoryId),
                    Cisfbs = x.ProductCisfbs.Select(c => new FormatDto
                    {
                        Id = c.CisfbId,
                        Code = c.Cisfb.Code,
                        Title = c.Cisfb.Title
                    }),
                    ContentRating = x.ContentRating,
                    ContentRatingCount = x.ProductRatings.Where(b => b.Type == ProductRatingType.ContentRating).Count(),
                    Description = x.Description,
                    Details = x.ProductDetails.Select(d => new ProductDetailDto
                    {
                        Id = d.DetailId,
                        Name = d.Detail.Name,
                        Photos = d.Detail.DetailPhotos.Select(p => new PhotoProductDto
                        {
                            PhotoId = p.PhotoId,
                            UploadUrl = p.Photo.UploadUrl,
                            Small = p.Photo.SmallImgUrl,
                            Middle = p.Photo.OriginalImgUrl.Replace("_b", "_m"),
                            Big = p.Photo.OriginalImgUrl
                        })
                    }),
                    DisplaySwatchboxProductOnMicrosite = x.DisplaySwatchboxProductOnMicrosite,
                    DisplaySwatchboxProductOnProductPage = x.DisplaySwatchboxProductOnProductPage,
                    ExternalCertificates = x.ProductCertificates.Select(b => b.ExternalCertificateId.Value),
                    ExternalMasterformatIds = x.ProductMasterformats.Select(m => m.ExternalMasterformatId),
                    ExternalProductId = x.ExternalId,
                    FooterAdImage = x.FooterAdImageId == null ? null : new FooterAdImageDto
                    {
                        Id = x.FooterAdImageId,
                        Small = x.FooterAdImageId != null ? x.FooterAdImage.SmallImgUrl : null,
                        Big = x.FooterAdImageId != null ? x.FooterAdImage.OriginalImgUrl : null
                    },
                    FooterAdImageId = x.FooterAdImageId,
                    FooterAdUrl = x.FooterAdUrl,
                    ForgeCeilingURL = x.ForgeCeilingURL,
                    ForgeFloorURL = x.ForgeFloorURL,
                    ForgeRoofURL = x.ForgeRoofURL,
                    ForgeWallURL = x.ForgeWallURL,
                    HideOnMicrosite = x.HideOnMicrosite,
                    IsFeatured = x.IsFeatured,
                    IsImperialDefault = x.IsImperialDefault,
                    Keywords = x.Keywords == null ? null : x.Keywords.Trim(),
                    Manufacture = new ManufacturerDto
                    {
                        Id = x.ManufacturerId,
                        Name = x.Manufacturer.Name,
                        Site = x.Manufacturer.Site,
                        SwatchboxManufacturerId = x.Manufacturer.SwatchboxManufacturerId,
                        VideoUrl = x.Manufacturer.VideoUrl,
                        NodeSetting = x.Manufacturer.NodeSetting,
                        LetsTalkSettings = x.Manufacturer.LetsTalkSettings,
                        Logo = new PhotoLogoDto
                        {
                            Id = x.Manufacturer.PhotoId,
                            Small = x.Manufacturer.Photo != null ? x.Manufacturer.Photo.SmallImgUrl : null,
                            Original = x.Manufacturer.Photo != null ? x.Manufacturer.Photo.OriginalImgUrl : null,
                        }
                    },
                    ManufacturerId = x.ManufacturerId,
                    MetaDescription = x.MetaDescription,
                    MetaKeywords = x.MetaKeywords,
                    MetaTitle = x.MetaTitle,
                    Name = x.Name,
                    Note = x.Note,
                    Omniclasses = x.ProductOmniclasses.Select(r => new FormatDto
                    {
                        Id = r.OmniclassId,
                        Code = r.Omniclass.Code,
                        Title = r.Omniclass.Title
                    }),
                    Photo = x.Photo == null ? null : new PhotoProductWithStatusDto
                    {
                        PhotoId = x.PhotoId,
                        UploadUrl = x.Photo.UploadUrl,
                        Small = x.Photo.SmallImgUrl,
                        Middle = x.Photo.OriginalImgUrl ?? x.Photo.OriginalImgUrl.Replace("_b", "_m"),
                        Big = x.Photo.OriginalImgUrl,
                        UpdatesCount = x.Photo.UpdatesCount,
                        FileSyncStatusCode = x.Photo.SyncStatusCode,
                    },
                    ProductFiles = x.ProductFiles.Where(f => f.IsAttachment).Select(r => new ProductGetFileWithRegionDto
                    {
                        Id = r.FileId,
                        CustomFileId = r.CustomFileId,
                        Title = r.File.Title,
                        FileName = r.File.FileName,
                        FileSize = r.File.FileSize,
                        RegionIds = r.RegionIds,
                        StateIds = r.StateIds,
                        UpdatesCount = r.File.UpdatesCount,
                        FileSyncStatusCode = r.File.SyncStatusCode,
                        FileSyncUrl = r.File.SyncUrl,
                        MimeType = r.File.MediaType,
                        Url = r.File.Url,
                        Preview = r.File.PreviewUrl,
                        Weight = r.Weight,
                        ModifiedDate = r.ModifiedDate ?? r.CreatedDate
                    })
                    .OrderByDescending(p => p.Weight)
                    .AsEnumerable(),
                    ProductLine = x.ProductLineId == null ? null : new ProductLineDto
                    {
                        Id = x.ProductLineId,
                        Name = x.ProductLine.Name,
                        RegionIds = x.ProductLine.RegionIds,
                        StateIds = x.ProductLine.StateIds,
                        ExternalCertificates = x.ProductLine.ProductLineCertificates.Select(b => b.ExternalCertificateId),
                        QualityItems = x.ProductLine.ProductLineQualityItems.Select(r => new QualityItemDto
                        {
                            Id = r.QualityItemId,
                            Name = r.QualityItem.Name,
                            IconUrl = r.QualityItem.IconUrl
                        }),
                        ProductFiles = x.ProductLine.ProductLineFiles.Where(f => f.IsAttachment).Select(r => new ProductGetFileWithWeightDto
                        {
                            Id = r.FileId,
                            CustomFileId = r.CustomFileId,
                            Title = r.File.Title,
                            FileName = r.File.FileName,
                            FileSize = r.File.FileSize,
                            MimeType = r.File.MediaType,
                            UpdatesCount = r.File.UpdatesCount,
                            FileSyncStatusCode = r.File.SyncStatusCode,
                            FileSyncUrl = r.File.SyncUrl,
                            Url = r.File.Url,
                            Preview = r.File.PreviewUrl,
                            Weight = r.Weight,
                            ModifiedDate = r.ModifiedDate ?? r.CreatedDate
                        })
                        .OrderByDescending(p => p.Weight)
                        .AsEnumerable(),
                        ProjectFiles = x.ProductLine.ProductLineFiles.Where(f => !f.IsAttachment).Select(r => new ProjectGetFileWithTypeDto
                        {
                            Id = r.FileId,
                            CustomFileId = r.CustomFileId,
                            SoftwareRelease = r.SoftwareRelease,
                            ContentCreatedby = r.ContentCreatedby,
                            ContentCheckedBy = r.ContentCheckedBy,
                            FileVersion = r.FileVersion,
                            ProjectType = new ProjectGetDataTypeDto
                            {
                                Id = r.ProjectDataTypeId,
                                Title = r.ProjectDataType.Title,
                                Header = r.ProjectDataType.Header,
                                ParentId = r.ProjectDataType.ParentId
                            },
                            Title = r.File.Title,
                            FileName = r.File.FileName,
                            FileSize = r.File.FileSize,
                            MimeType = r.File.MediaType,
                            UpdatesCount = r.File.UpdatesCount,
                            FileSyncStatusCode = r.File.SyncStatusCode,
                            FileSyncUrl = r.File.SyncUrl,
                            Url = r.File.Url,
                            Preview = r.File.PreviewUrl,
                            ModifiedDate = r.ModifiedDate ?? r.CreatedDate
                        })
                    },
                    ProductPhotos = x.ProductPhotos.Select(r => new PhotoProductWithMainParamDto
                    {
                        PhotoId = r.PhotoId,
                        UploadUrl = r.Photo.UploadUrl,
                        Small = r.Photo.SmallImgUrl,
                        Middle = r.Photo.OriginalImgUrl.Replace("_b", "_m"), // try to get middle size
                        Big = r.Photo.OriginalImgUrl,
                        IsMainPhoto = r.PhotoId == x.PhotoId,
                        UpdatesCount = r.Photo.UpdatesCount,
                        FileSyncStatusCode = r.Photo.SyncStatusCode
                    })
                    .OrderByDescending(p => p.IsMainPhoto)
                    .AsEnumerable(),
                    ProductRating = x.ProductRating,
                    ProductRatingCount = x.ProductRatings.Where(b => b.Type == ProductRatingType.ProductRating).Count(),
                    ProductStats = x.ProductStats.Select(r => new ProductStatsDto
                    {
                        KeyStatId = r.KeyStatId,
                        Name = r.KeyStat == null ? null : r.KeyStat.Name,
                        Note = r.Note,
                        KeyStatType = r.KeyStatType,
                        Order = r.Order,
                        KeyStatUnitId = r.KeyStatUnitId,
                        KeyStatUnit = r.KeyStatUnit == null ? null : new KeyStatDto
                        {
                            AUnitName = r.KeyStatUnit.AUnitName,
                            BUnitName = r.KeyStatUnit.BUnitName,
                            UnitMetricType = r.KeyStatUnit.UnitMetricType
                        },
                        ConvertKeyStatUnitId = r.ConvertKeyStatUnitId,
                        ConvertKeyStatUnit = r.ConvertKeyStatUnit == null ? null : new KeyStatDto
                        {
                            AUnitName = r.ConvertKeyStatUnit.AUnitName,
                            BUnitName = r.ConvertKeyStatUnit.BUnitName,
                            UnitMetricType = r.ConvertKeyStatUnit.UnitMetricType
                        },
                        SingleValue = r.Value,
                        MinRangeValue = r.MinRangeValue,
                        MaxRangeValue = r.MaxRangeValue,
                        ConvertSingleValue = r.ConvertValue,
                        ConvertMinRangeValue = r.ConvertMinRangeValue,
                        ConvertMaxRangeValue = r.ConvertMaxRangeValue,
                        MultipleValues = r.KeyStatValueList.Select(k => new KeyStatListDto
                        {
                            KeyStatUnitId = k.KeyStatUnitId,
                            KeyStatUnit = k.KeyStatUnit == null ? null : new KeyStatDto
                            {
                                AUnitName = k.KeyStatUnit.AUnitName,
                                BUnitName = k.KeyStatUnit.BUnitName,
                                UnitMetricType = k.KeyStatUnit.UnitMetricType
                            },
                            Note = k.Note,
                            ConvertKeyStatUnitId = k.ConvertKeyStatUnitId,
                            ConvertKeyStatUnit = k.ConvertKeyStatUnit == null ? null : new KeyStatDto
                            {
                                AUnitName = k.ConvertKeyStatUnit.AUnitName,
                                BUnitName = k.ConvertKeyStatUnit.BUnitName,
                                UnitMetricType = k.ConvertKeyStatUnit.UnitMetricType
                            },
                            Value = !(k.Value == null || k.Value.Trim() == string.Empty) ? k.Value : k.MinRangeValue,
                            MaxValue = k.MaxRangeValue,
                            ConvertValue = k.ConvertValue ?? k.ConvertMinRangeValue,
                            ConvertMaxValue = k.ConvertMaxRangeValue
                        })
                    })
                    .OrderBy(k => k.Order)
                    .AsEnumerable(),
                    ProductUrl = x.ProductUrl,
                    ProjectFiles = x.ProductFiles.Where(f => !f.IsAttachment && !f.IsULPartnership).Select(r => new ProjectGetFileWithVersionDto
                    {
                        Id = r.FileId,
                        CustomFileId = r.CustomFileId,
                        RegionIds = r.RegionIds,
                        StateIds = r.StateIds,
                        SoftwareRelease = r.SoftwareRelease,
                        ContentCreatedby = r.ContentCreatedby,
                        ContentCheckedBy = r.ContentCheckedBy,
                        FileVersion = r.FileVersion,
                        SoftwareVersionId = r.SoftwareVersionId,
                        SoftwareVersion = r.SoftwareVersion != null ? new SoftwareVersionDto
                        {
                            Id = r.SoftwareVersion.Id,
                            Title = r.SoftwareVersion.Title,
                            Header = r.SoftwareVersion.Header,
                            ParentId = r.SoftwareVersion.ParentId
                        } : null,
                        ProjectDataType = r.ProjectDataType != null ? new ProjectGetDataTypeDto
                        {
                            Id = r.ProjectDataTypeId,
                            Title = r.ProjectDataType.Title,
                            Header = r.ProjectDataType.Header,
                            ParentId = r.ProjectDataType.ParentId
                        } : null,
                        Title = r.File.Title,
                        FileName = r.File.FileName,
                        FileSize = r.File.FileSize,
                        MimeType = r.File.MediaType,
                        UpdatesCount = r.File.UpdatesCount,
                        FileSyncStatusCode = r.File.SyncStatusCode,
                        FileSyncUrl = r.File.SyncUrl,
                        Url = r.File.Url,
                        Preview = r.File.PreviewUrl,
                        CreatedDate = r.CreatedDate,
                        UserName = r.CreatedById != null ? r.CreatedBy.FirstName + " " + r.CreatedBy.LastName : string.Empty,
                        ModifiedDate = r.ModifiedDate ?? r.CreatedDate
                    }),
                    Published = x.Published,
                    PublishToPartner = x.PublishToPartner,
                    QualityItems = x.ProductQualityItems.Select(r => new QualityItemDto
                    {
                        Id = r.QualityItemId,
                        Name = r.QualityItem.Name == null ? null : r.QualityItem.Name,
                        IconUrl = r.QualityItem.IconUrl == null ? null : r.QualityItem.IconUrl
                    }),
                    RegionIds = x.RegionIds,
                    RelatedProducts = x.RelatedProducts.Select(r => new RelatedProductDto
                    {
                        ProductId = r.RelatedProductId,
                        Name = r.RelatedProductItem.Name,
                        CategoryName = r.RelatedProductItem.Category.Name,
                        ManufacturerName = r.RelatedProductItem.Manufacturer.Name,
                        PhotoUrl = r.RelatedProductItem.PhotoId != null ? r.RelatedProductItem.Photo.MiddleImgUrl : null
                    }),
                    Samples = x.ProductSamples.Select(b => b.SampleId),
                    Staging = x.Staging,
                    StateIds = x.StateIds,
                    SwatchboxOptionId = x.SwatchboxOptionId,
                    SwatchboxProductId = x.SwatchboxProductId,
                    UlProductFiles = x.ProductFiles.Where(f => f.IsULPartnership).Select(r => new ProductGetFileDto
                    {
                        Id = r.FileId,
                        CustomFileId = r.CustomFileId,
                        Title = r.File.Title,
                        FileName = r.File.FileName,
                        FileSize = r.File.FileSize,
                        UpdatesCount = r.File.UpdatesCount,
                        FileSyncStatusCode = r.File.SyncStatusCode,
                        FileSyncUrl = r.File.SyncUrl,
                        MimeType = r.File.MediaType,
                        Url = r.File.Url,
                        Preview = r.File.PreviewUrl,
                        ModifiedDate = r.ModifiedDate ?? r.CreatedDate
                    }),
                    UlSyncStatus = x.ULSyncStatus,
                    UlURL = x.ULUrl,
                    Uniclasses = x.ProductUniclasses.Select(r => new FormatDto
                    {
                        Id = r.UniclassId,
                        Code = r.Uniclass.Code,
                        Title = r.Uniclass.Title
                    }),
                    Uniformats = x.ProductUniformats.Select(r => new FormatDto
                    {
                        Id = r.UniformatId,
                        Code = r.Uniformat.Code,
                        Title = r.Uniformat.Title
                    }),
                    UpdateDate = x.ModifiedDate ?? x.CreatedDate,
                    VanityURL = x.VanityURL,
                    VideoUrl = x.VideoUrl,
                    Weight = x.Weight,
                    PublishedOnCustomMicrosite = x.PublishedOnCustomMicrosite
                })
                .FirstOrDefaultAsync(a => a.Id == id);

            product = await CheckProductForLockedLinksAndCorrectItAsync(product, unitOfWork);

            return product;
        }

        public async Task<MicrositeListWithStarterListResultDto> MicrositeListWithStarterAsync(
            MicrositeListWithStarterFiltersDto model,
            string userName,
            IUnitOfWork unitOfWork)
        {
            string searchId = Guid.NewGuid().ToString();
            List<int> productIds = new List<int>();

            var productLink = new Flurl.Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment("/product/").ToString();

            var manufacturer = ManufacturerSearch.Search(new ManufacturerSearchOptions
            {
                ManufacturerName = model.ManufacturerName,
                HubVanityURL = model.VanityUrlPart,
                Published = false,
                Query = null,
                RegionId = model.RegionId,
                StateId = model.StateId,
            }, unitOfWork.ManufacturerRepository.GetAll(), unitOfWork).Select(a => new { a.Id, a.ForgeManufacturerId }).FirstOrDefault();

            if (manufacturer == null)
            {
                _cacheService.Set($"micrositeProductIds{searchId}", productIds, CacheConstants.ServerExpiration);

                return new MicrositeListWithStarterListResultDto
                {
                    Count = 0,
                    Data = Array.Empty<dynamic>(),
                    SearchId = searchId
                };
            }

            List<dynamic> resultData = new List<dynamic>();

            int countOfProducts = 0;
            int countOfStarters = 0;
            int skip = Math.Max(model.Offset, 0);
            var productSearchOptions = new ProductSearchOptions
            {
                Query = model.Q,
                Published = model.Published,
                PublishedOnCustomMicrosite = model.PublishedOnCustomMicrosite,
                CategoryId = model.CategoryId,
                ProductLineId = model.ProductLineId,
                ManufacturerIds = new List<int> { manufacturer.Id },
                SortType = model.StartersPosition == StartersPosition.Weighted ? ProductSortType.Relevant : model.SortType,
                ProductFileTypeIds = !string.IsNullOrEmpty(model.ProjectTypeIds) ? model.ProjectTypeIds.Split('_').Select(a => int.Parse(a)).ToList() : null,
                Skip = skip,
                Take = model.Count,
                StartersPosition = model.StartersPosition,
                IncludeHideOnMicrosite = model.IncludeHideOnMicrosite,
                RegionId = model.RegionId,
                StateId = model.StateId,
                KeyStats = model.KeyStats,
                SearchSource = SearchSource.Microsites
            };

            var searchCache = SearchCache.Get(unitOfWork);
            ProductSearchResults results = ProductSearch.Search(productSearchOptions, unitOfWork.ProductRepository.GetAll(), model.WithStarter ? unitOfWork.StarterRepository.GetAll() : null, searchCache, unitOfWork);
            productIds.AddRange(results.AllResultsIds);

            int categoryId = productSearchOptions.CategoryId; // could have been recognized from text query

            var primaryResults = results.PrimaryResults(skip, model.Count, unitOfWork, a => new ProductSearchResult<object>
            {
                ProductId = a.Id,
                ItemType = "product",
                Projection = new
                {
                    id = a.Id,
                    name = a.Name,
                    productSiteUrl = a.ProductUrl ?? string.Concat(productLink, a.Id),
                    photoUrl = a.PhotoId != null ? a.Photo.OriginalImgUrl : a.ProductPhotos.FirstOrDefault().Photo.OriginalImgUrl,
                    productLineId = a.ProductLineId,
                    categoryId = a.CategoryId,
                    swatchboxProductId = a.SwatchboxProductId,
                    displaySwatchboxProductOnProductPage = a.DisplaySwatchboxProductOnProductPage,
                    displaySwatchboxProductOnMicrosite = a.DisplaySwatchboxProductOnMicrosite,
                    projectFileIds = a.ProductFiles.Where(f => !f.IsAttachment && !f.IsULPartnership).Select(r => r.FileId),
                    productFileIds = a.ProductFiles.Where(f => f.IsAttachment && !f.IsULPartnership).Select(r => r.FileId),
                    externalCertificationsIds = a.ProductCertificates.Where(b => b.ExternalCertificateId != null).Select(b => b.ExternalCertificateId.Value),
                    externalMasterformatIds = a.ProductMasterformats.Select(x => x.ExternalMasterformatId),
                    description = a.Description
                }
            }, a => model.WithStarter ? new ProductSearchResult<object>
            {
                ProductId = 0,
                ItemType = "starter",
                Projection = new
                {
                    id = a.Id,
                    name = a.Name,
                    manufacturerName = a.ManufacturerName,
                    brand = a.Brand,
                    orientation = a.Orientation,
                    preview = a.Preview,
                    stc = a.STC,
                    fstc = a.FSTC,
                    rvalue = a.RValue,
                    fire = a.Fire,
                    ul = a.UL,
                    iic = a.IIC,
                    fiic = a.FIIC,
                    nfpa285 = a.NFPA285
                }
            } : null
            ).Select(r => new
            {
                itemType = r.ItemType,
                item = r.Projection
            });

            resultData.AddRange(primaryResults.ToList());

            countOfProducts = results.CountOfProducts;
            countOfStarters = results.CountOfStarters;

            if (model.Offset == 0 && (!string.IsNullOrEmpty(model.Q) || categoryId != -1))
            {
                try
                {
                    var categoryName = (await unitOfWork.CategoryRepository.GetAll().FirstOrDefaultAsync(a => a.Id == categoryId))?.Name;

                    await _slackWebHook.SendSearchMessage(userName,
                        model.Q,
                        model.ManufacturerName ?? model.VanityUrlPart,
                        categoryName,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null);
                }
                catch (Exception e)
                {
                    Log.Error(e.Message, e);
                    Trace.TraceError(e.Message);
                }
            }

            var result = new MicrositeListWithStarterListResultDto
            {
                Count = countOfProducts + countOfStarters,
                Data = resultData,
                SearchId = searchId
            };

            _cacheService.Set($"micrositeProductIds{searchId}", productIds, CacheConstants.ServerExpiration);

            return result;
        }

        public async Task<MicrositeListWithStarterFiltersResultDto> MicrositeListWithStarterGetFiltersAsync(
            string searchId,
            IUnitOfWork unitOfWork)
        {
            List<int> productIds = _cacheService.Get<List<int>>($"micrositeProductIds{searchId}");
            if (productIds == null)
                throw new Exception("Invalid search identifier");

            int[] projectDataTypeIds = await unitOfWork.ProductFileRepository.GetAllAsNoTracking()
                                                       .Where(x => !x.IsAttachment
                                                                && productIds.Contains(x.ProductId))
                                                       .Select(x => x.ProjectDataTypeId.Value)
                                                       .Distinct()
                                                       .ToArrayAsync();

            var availableProjectDataTypes = await unitOfWork.ProjectDataTypeRepository.GetAllAsNoTracking()
                                                            .Where(x => projectDataTypeIds.Contains(x.Id))
                                                            .Select(a => new
                                                            {
                                                                id = a.Id,
                                                                title = a.Title
                                                            })
                                                            .ToArrayAsync();

            MicrositeListWithStarterFiltersResultKeyStatDto[] singleValueKeyStats = await unitOfWork.ProductStatsRepository.GetAll()
                                                                                                    .Where(x => productIds.Contains(x.ProductId)
                                                                                                             && (x.KeyStatType == KeyStatType.SingleNumeric || x.KeyStatType == KeyStatType.NumericRange || x.KeyStatType == KeyStatType.TextValue))
                                                                                                    .Select(x => new MicrositeListWithStarterFiltersResultKeyStatDto
                                                                                                    {
                                                                                                        KeyStatId = x.KeyStat.Id,
                                                                                                        KeyStatName = x.KeyStat.Name,
                                                                                                        Values = new MicrositeListWithStarterFiltersResultKeyStatValueDto[]
                                                                                                        {
                                                                                                            new MicrositeListWithStarterFiltersResultKeyStatValueDto
                                                                                                            {
                                                                                                                KeyStatType = x.KeyStatType,
                                                                                                                Value = !(x.Value == null || x.Value.Trim() == string.Empty) ? x.Value : x.MinRangeValue,
                                                                                                                MaxRangeValue = x.MaxRangeValue,
                                                                                                                KeyStatUnitId = x.KeyStatUnitId,
                                                                                                                KeyStatUnitDescription = x.KeyStatUnit.Description,
                                                                                                                KeyStatUnitAName = x.KeyStatUnit.AUnitName,
                                                                                                                KeyStatUnitBName = x.KeyStatUnit.BUnitName
                                                                                                            }
                                                                                                        }
                                                                                                    })
                                                                                                    .ToArrayAsync();

            MicrositeListWithStarterFiltersResultKeyStatDto[] multiValueKeyStats = await unitOfWork.ProductStatsRepository.GetAll()
                                                                                                   .Where(x => productIds.Contains(x.ProductId)
                                                                                                            && (x.KeyStatType == KeyStatType.MultivalueNumeric || x.KeyStatType == KeyStatType.TextMultivalue))
                                                                                                   .Select(x => new MicrositeListWithStarterFiltersResultKeyStatDto
                                                                                                   {
                                                                                                       KeyStatId = x.KeyStat.Id,
                                                                                                       KeyStatName = x.KeyStat.Name,
                                                                                                       Values = x.KeyStatValueList.Select(v => new MicrositeListWithStarterFiltersResultKeyStatValueDto
                                                                                                       {
                                                                                                           KeyStatType = x.KeyStatType,
                                                                                                           Value = !(v.Value == null || v.Value.Trim() == string.Empty) ? v.Value : v.MinRangeValue,
                                                                                                           MaxRangeValue = v.MaxRangeValue,
                                                                                                           KeyStatUnitId = v.KeyStatUnitId,
                                                                                                           KeyStatUnitDescription = v.KeyStatUnit.Description,
                                                                                                           KeyStatUnitAName = v.KeyStatUnit.AUnitName,
                                                                                                           KeyStatUnitBName = v.KeyStatUnit.BUnitName
                                                                                                       })
                                                                                                   })
                                                                                                   .ToArrayAsync();

            MicrositeListWithStarterFiltersResultKeyStatDto[] keyStats = singleValueKeyStats.Concat(multiValueKeyStats)
                                                                                            .GroupBy(x => x.KeyStatId)
                                                                                            .Select(x => new MicrositeListWithStarterFiltersResultKeyStatDto
                                                                                            {
                                                                                                KeyStatId = x.Key,
                                                                                                KeyStatName = x.FirstOrDefault().KeyStatName,
                                                                                                Values = x.SelectMany(v => v.Values).Distinct(new MicrositeListWithStarterFiltersResultKeyStatValueDtoComparer()).ToArray()
                                                                                            })
                                                                                            .ToArray();

            return new MicrositeListWithStarterFiltersResultDto
            {
                ProjectDataTypes = availableProjectDataTypes,
                KeyStats = keyStats
            };
        }

        public async Task ChangePublishedAllAsync(ChangeProductFlagAllDto model, string userId, IUnitOfWork unitOfWork)
        {
            await unitOfWork.ProductRepository.GetAll()
                .Where(x => x.ManufacturerId == model.ManufacturerId)
                .ExecuteUpdateAsync(x => x
                    .SetProperty(p => p.Published, model.FlagValue)
                    .SetProperty(p => p.ModifiedById, userId)
                    .SetProperty(p => p.ModifiedDate, DateTime.UtcNow));
        }

        public async Task ChangePublishedAllOnCustomMicrositeAsync(ChangeProductFlagAllDto model, string userId, IUnitOfWork unitOfWork)
        {
            await unitOfWork.ProductRepository.GetAll()
                .Where(x => x.ManufacturerId == model.ManufacturerId)
                .ExecuteUpdateAsync(x => x
                    .SetProperty(p => p.PublishedOnCustomMicrosite, model.FlagValue)
                    .SetProperty(p => p.ModifiedById, userId)
                    .SetProperty(p => p.ModifiedDate, DateTime.UtcNow));
        }

        public async Task ChangeStagingAllAsync(ChangeProductFlagAllDto model, string userId, IUnitOfWork unitOfWork)
        {
            await unitOfWork.ProductRepository.GetAll()
                .Where(x => x.ManufacturerId == model.ManufacturerId)
                .ExecuteUpdateAsync(x => x
                    .SetProperty(p => p.Staging, model.FlagValue)
                    .SetProperty(p => p.ModifiedById, userId)
                    .SetProperty(p => p.ModifiedDate, DateTime.UtcNow));
        }

        public async Task<Dictionary<int, string>> GetLastMofifierAsync(int[] productIds, IUnitOfWork unitOfWork)
        {
            return await unitOfWork.ProductRepository.GetAllAsNoTracking()
                .Where(x => productIds.Contains(x.Id))
                .Select(x => new
                {
                    x.Id,
                    CreatedName = x.CreatedBy.FirstName + " " + x.CreatedBy.LastName,
                    ModifiedName = x.ModifiedById != null ? x.ModifiedBy.FirstName + " " + x.ModifiedBy.LastName : null
                })
                .DefaultIfEmpty()
                .ToDictionaryAsync(x => x.Id, x => x.ModifiedName ?? x.CreatedName);
        }

        public async Task<PaginationListDto<AdminListProductDto>> AdminListAsync(
            IUnitOfWork unitOfWork,
            int? manufacturerId = null,
            int? productLineId = null,
            string query = null,
            ProductSortType sortType = ProductSortType.Relevant,
            int offset = 0,
            int count = 50
            )
        {
            IQueryable<Product> dbQuery = unitOfWork.ProductRepository.GetAll();

            if (manufacturerId.HasValue)
                dbQuery = dbQuery.Where(x => x.ManufacturerId == manufacturerId);

            if (productLineId.HasValue)
                dbQuery = dbQuery.Where(x => x.ProductLineId == productLineId);

            if (!string.IsNullOrWhiteSpace(query))
            {
                query = query.Trim().ToUpper();
                dbQuery = dbQuery.Where(x => x.Name.ToUpper().Contains(query) || x.Id.ToString().Contains(query));
            }

            switch (sortType)
            {
                case ProductSortType.Relevant: dbQuery = dbQuery.OrderByDescending(a => a.Weight); break;
                case ProductSortType.LatestProducts: dbQuery = dbQuery.OrderByDescending(a => a.CreatedDate); break;
                case ProductSortType.LatestModified: dbQuery = dbQuery.OrderByDescending(a => a.ModifiedDate ?? a.CreatedDate); break;
            }

            int totalCount = await dbQuery.CountAsync();
            AdminListProductDto[] items = await dbQuery.Skip(offset).Take(count).ProjectToType<AdminListProductDto>().ToArrayAsync();

            int[] productIds = items.Select(x => x.Id).ToArray();
            if (productIds.Any())
            {
                Dictionary<int, string> lastModifiers = await GetLastMofifierAsync(productIds, unitOfWork);

                foreach (var item in items)
                {
                    item.UserName = lastModifiers[item.Id];
                    item.HealthCheckStatus = await _healthDashboardService.CheckHealthForProductAsync(item.Id, unitOfWork);
                }
            }

            return new PaginationListDto<AdminListProductDto>
            {
                Count = totalCount,
                Data = items
            };
        }

        public async Task<ProductManufacturerDto[]> CheckProductsWithTheSameProjectFilesAndDifferentPriceTypeAsync(int productId, IUnitOfWork unitOfWork)
        {
            PriceType? priceType = await unitOfWork.ProductRepository.GetAll()
                .Where(x => x.Id == productId)
                .Select(x => x.Price.PriceType)
                .FirstOrDefaultAsync();

            if (priceType == null)
                throw new DbItemNotFoundException($"Product {productId} not found");

            int[] projectFileIds = await unitOfWork.ProductFileRepository.GetAll()
                .Where(x => !x.IsAttachment && x.ProductId == productId)
                .Select(x => x.FileId)
                .ToArrayAsync();

            if (!projectFileIds.Any())
                return Array.Empty<ProductManufacturerDto>();

            int[] productIds = await unitOfWork.ProductFileRepository.GetAll()
                .Where(x => projectFileIds.Contains(x.FileId)
                         && !x.IsAttachment
                         && x.ProductId != productId
                         && x.Product.Price.PriceType != priceType)
                .Select(x => x.ProductId)
                .Distinct()
                .ToArrayAsync();

            return await unitOfWork.ProductRepository.GetAll()
                .Where(x => productIds.Contains(x.Id))
                .ProjectToType<ProductManufacturerDto>()
                .ToArrayAsync();
        }

        public async Task<ICollection<string>> RelevantSearchListAsync(RelevantSearchListFilterDto filter, IUnitOfWork unitOfWork)
        {
            filter.Query = $"%{filter.Query}%";

            IQueryable<Product> productsQuery = unitOfWork.ProductRepository.GetAll()
                .Where(p => EF.Functions.Like(p.Name, filter.Query));

            if (filter.ManufacturerId.HasValue)
            {
                productsQuery = productsQuery.Where(p => p.ManufacturerId == filter.ManufacturerId);
            }

            if (filter.Published)
            {
                productsQuery = productsQuery.Where(p => p.Published);
            }

            if (filter.PublishedOnCustomMicrosite)
            {
                productsQuery = productsQuery.Where(p => p.PublishedOnCustomMicrosite);
            }

            return await productsQuery.OrderBy(p => p.Id)
                .Take(filter.Count)
                .Select(p => p.Name)
                .ToArrayAsync();
        }

        public async Task<PaginationListDto<BaseNameListDto>> RevitProcessingListAsync(RevitProcessingProductFilterDto model, IUnitOfWork unitOfWork)
        {
            string[] fileExtensions = [];

            if (model.RevitProcessType == RevitProcessType.UpdateParameterValues)
            {
                fileExtensions = RevitProcessingConstants.UpdateParametersRevitFileExtensions;
            }
            else if (model.RevitProcessType == RevitProcessType.UpdateRevitVersion)
            {
                fileExtensions = RevitProcessingConstants.UpdateVersionRevitFileExtenstions;
            }

            IQueryable<Product> productsQuery = unitOfWork.ProductRepository.GetAll()
                .Where(x => x.ManufacturerId == model.ManufacturerId
                         && x.ProductFiles.Any(p => !p.IsAttachment && p.ProjectDataTypeId == RevitProcessingConstants.RevitProjectDataTypeId && fileExtensions.Any(f => p.File.FileName.EndsWith(f))));

            if (model.RevitProcessType == RevitProcessType.UpdateParameterValues)
            {
                productsQuery = productsQuery.Where(x => x.ProductFiles.Any(p => RevitProcessingConstants.RevitVersions.Any(r => p.SoftwareVersion.Title.Contains(r))));
            }

            int totalCount = await productsQuery.CountAsync();
            BaseNameListDto[] products = await productsQuery.OrderBy(x => x.Name).ProjectToType<BaseNameListDto>().ToArrayAsync();

            return new PaginationListDto<BaseNameListDto>
            {
                Count = totalCount,
                Data = products
            };
        }

        public void AddProductProjectFile(ProjectFileModel projectFile, int productId, string userId, IUnitOfWork unitOfWork)
        {
            ProductFile productFile = projectFile.Adapt<ProductFile>();
            productFile.ProductId = productId;
            productFile.CreatedById = userId;
            productFile.WasChanged = true;
            unitOfWork.ProductFileRepository.Insert(productFile);

            var file = unitOfWork.FileRepository.GetById(projectFile.FileId);
            if (file.Title != projectFile.Title)
            {
                file.Title = projectFile.Title;
                unitOfWork.FileRepository.Edit(file);
            }
        }

        public void UpdateProductProjectFile(ProductFile projectFile, ProjectFileModel modelProjectFile, int productId, string userId, IUnitOfWork unitOfWork)
        {
            bool projectFileChanged = !IsProjectFileEqualsToModelFile(projectFile, modelProjectFile);

            var file = unitOfWork.FileRepository.GetById(modelProjectFile.FileId);
            if (file.Title != modelProjectFile.Title)
            {
                file.Title = modelProjectFile.Title;
                projectFile.WasChanged = true;
                unitOfWork.FileRepository.Edit(file);
                projectFileChanged = true;
            }

            if (projectFileChanged)
            {
                modelProjectFile.Adapt(projectFile);
                projectFile.ModifiedById = userId;
                projectFile.ModifiedDate = DateTime.UtcNow;
                projectFile.WasChanged = true;
                unitOfWork.ProductFileRepository.Edit(projectFile);
            }
        }

        #region private methods
        private async Task<bool> HandleAddOrUpdateProductFileAsync(IUnitOfWork unitOfWork, AzureStorageService azureBlobProvider, BlobContainerClient filesContainer, string productName, string manufacturerName, FileDto modelProductFile)
        {
            bool fileChanged = false;

            var file = unitOfWork.FileRepository.GetById(modelProductFile.FileId);
            if (file.Title != modelProductFile.Title)
            {
                file.Title = modelProductFile.Title;
                fileChanged = true;
            }

            //TODO: Check it on tests
            //The purpose of this block code is unclear
            //if (file.MediaType.StartsWith("image"))
            //{
            //    var fileName = Path.GetFileName(file.Url);
            //    if (!fileName.Contains(productName) && !fileName.Contains(manufacturerName))
            //    {
            //        var newFileName = $"{manufacturerName}-{productName}-revit-" + fileName;
            //        var newBlob = await azureBlobProvider.RenameBlobAsync(filesContainer, newFileName, fileName, file.Url);
            //        if (newBlob != null)
            //        {
            //            file.Url = newBlob.Uri.ToString();
            //            fileChanged = true;
            //        }
            //    }
            //}

            //var previewFileName = Path.GetFileName(file.PreviewUrl);
            //if (!previewFileName.Contains(productName) && !previewFileName.Contains(manufacturerName))
            //{
            //    var newPreviewFileName = $"{manufacturerName}-{productName}-revit-" + previewFileName;
            //    var newPreviewBlob = await azureBlobProvider.RenameBlobAsync(filesContainer, newPreviewFileName, previewFileName, file.PreviewUrl);
            //    if (newPreviewBlob != null)
            //    {
            //        file.PreviewUrl = newPreviewBlob.Uri.ToString();
            //        fileChanged = true;
            //    }
            //}

            if (fileChanged)
                unitOfWork.FileRepository.Edit(file);

            return fileChanged;
        }

        private async Task HandleAddOrUpdateProductPhoto(IUnitOfWork unitOfWork, AzureStorageService azureBlobProvider, BlobContainerClient photosContainer, string productName, string manufacturerName, int photoId)
        {
            var photo = unitOfWork.PhotoRepository.GetById(photoId);
            if (!string.IsNullOrWhiteSpace(photo.OriginalImgUrl))
            {
                var oldPhotoNameB = Path.GetFileName(photo.OriginalImgUrl);
                if (!oldPhotoNameB.Contains(productName) && !oldPhotoNameB.Contains(manufacturerName))
                {
                    var newPhotoNameB = $"{manufacturerName}-{productName}-revit-" + oldPhotoNameB;
                    var newBlobPhotoB = await azureBlobProvider.RenameBlobAsync(photosContainer, newPhotoNameB, oldPhotoNameB, photo.OriginalImgUrl);
                    if (newBlobPhotoB != null)
                    {
                        photo.OriginalImgUrl = newBlobPhotoB.Uri.ToString();
                    }

                    var oldPhotoNameM = oldPhotoNameB.Replace("_b", "_m");
                    var newPhotoNameM = $"{manufacturerName}-{productName}-revit-" + oldPhotoNameM;
                    await azureBlobProvider.RenameBlobAsync(photosContainer, newPhotoNameM, oldPhotoNameM, photo.SmallImgUrl.Replace("_s", "_m"));
                }
            }

            if (!string.IsNullOrWhiteSpace(photo.MiddleImgUrl))
            {
                var oldPhotoNameM = Path.GetFileName(photo.MiddleImgUrl);
                if (!oldPhotoNameM.Contains(productName) && !oldPhotoNameM.Contains(manufacturerName))
                {
                    var newPhotoNameM = $"{manufacturerName}-{productName}-revit-" + oldPhotoNameM;
                    var newBlobPhotoM = await azureBlobProvider.RenameBlobAsync(photosContainer, newPhotoNameM, oldPhotoNameM, photo.MiddleImgUrl);
                    if (newBlobPhotoM != null)
                    {
                        photo.MiddleImgUrl = newBlobPhotoM.Uri.ToString();
                    }
                }
            }

            if (!string.IsNullOrWhiteSpace(photo.SmallImgUrl))
            {
                var oldPhotoNameS = Path.GetFileName(photo.SmallImgUrl);
                if (!oldPhotoNameS.Contains(productName) && !oldPhotoNameS.Contains(manufacturerName))
                {
                    var newPhotoNameS = $"{manufacturerName}-{productName}-revit-" + oldPhotoNameS;
                    var newBlobPhotoS = await azureBlobProvider.RenameBlobAsync(photosContainer, newPhotoNameS, oldPhotoNameS, photo.SmallImgUrl);
                    if (newBlobPhotoS != null)
                    {
                        photo.SmallImgUrl = newBlobPhotoS.Uri.ToString();
                    }
                }
            }

            unitOfWork.PhotoRepository.Edit(photo);
        }

        private async Task UpdateVanityHistoryAsync(AddProductModel model, Product product, IUnitOfWork unitOfWork, string userId)
        {
            if (!string.IsNullOrWhiteSpace(product.VanityURL) && product.VanityURL != model.VanityURL.AsVanityUrl())
            {
                var oldVanityHistory = unitOfWork.VanityHistoryRepository.GetAll().Where(a => a.VanityUrl == product.VanityURL).SingleOrDefault();
                if (oldVanityHistory == null)
                {
                    oldVanityHistory = new VanityHistory
                    {
                        EntityId = product.Id,
                        EntityType = "product",
                        VanityUrl = product.VanityURL,
                        CreatedById = userId,
                        CreatedDate = DateTime.UtcNow
                    };
                    unitOfWork.VanityHistoryRepository.Insert(oldVanityHistory);
                    await unitOfWork.SaveAsync();
                }

                var vanityHistory = new VanityHistory
                {
                    EntityId = product.Id,
                    ParentId = oldVanityHistory.Id,
                    EntityType = "product",
                    VanityUrl = model.VanityURL.AsVanityUrl(),
                    CreatedById = userId,
                    CreatedDate = DateTime.UtcNow
                };
                unitOfWork.VanityHistoryRepository.Insert(vanityHistory);
                await unitOfWork.SaveAsync();
            }
        }

        private void AssignProduct(AddProductModel model, ref Product product, string userId, int? productId, string keywords, bool isExternalCall, bool isRestoredFromBackup)
        {
            if (!isExternalCall)
            {
                product.Staging = model.Staging != null ? model.Staging.Value : false;
                product.Published = model.Published != null ? model.Published.Value : false;
                product.PublishedOnCustomMicrosite = model.PublishedOnCustomMicrosite != null ? model.PublishedOnCustomMicrosite.Value : false;
                product.PublishToPartner = model.PublishToPartner != null ? model.PublishToPartner.Value : false;
                product.IsImperialDefault = model.IsImperialDefault;
                product.VideoUrl = model.VideoUrl;
                product.SwatchboxOptionId = model.SwatchboxOptionId;
                product.SwatchboxProductId = model.SwatchboxProductId;
                product.ULUrl = model.ULUrl;
                product.MetaTitle = model.MetaTitle;
                product.MetaDescription = model.MetaDescription;
                product.MetaKeywords = model.MetaKeywords;
                product.ForgeWallURL = model.ForgeWallURL;
                product.ForgeRoofURL = model.ForgeRoofURL;
                product.ForgeCeilingURL = model.ForgeCeilingURL;
                product.ForgeFloorURL = model.ForgeFloorURL;
                product.RegionIds = model.RegionIds;
                product.StateIds = model.StateIds;
                product.HideOnMicrosite = model.HideOnMicrosite;
                product.AssemblyCode = model.AssemblyCode;
                product.Weight = model.Weight;
                product.Note = model.Note;
                product.VanityURL = model.VanityURL;
                product.DisplaySwatchboxProductOnProductPage = model.DisplaySwatchboxProductOnProductPage;
                product.DisplaySwatchboxProductOnMicrosite = model.DisplaySwatchboxProductOnMicrosite;
                product.FooterAdImageId = model.FooterAdImageId;
                product.FooterAdUrl = model.FooterAdUrl;
            }

            if (isExternalCall)
            {
                product.ExternalModifiedDate = DateTime.UtcNow;
            }

            if (isExternalCall && productId == null)
            {
                product.Staging = model.Staging != null ? model.Staging.Value : false;
            }

            product.Name = model.Name;
            product.ExternalId = model.ExternalProductId;
            product.Description = model.Description;
            product.CategoryId = model.CategoryId;
            product.ManufacturerId = model.ManufacturerId;
            product.ProductLineId = model.ProductLineId;
            product.PhotoId = model.Photos != null && model.Photos.Any() ? model.Photos[0].PhotoId : null;
            product.IsFeatured = model.IsFeatured != null ? model.IsFeatured.Value : false;
            product.ProductUrl = model.ProductUrl;
            product.Keywords = keywords;

            if ((productId != null && isExternalCall)
                || (productId != null && !isRestoredFromBackup && !isExternalCall))
            {
                product.ModifiedById = userId;
                product.ModifiedDate = DateTime.UtcNow;
            }
            if (((productId == null || isRestoredFromBackup) && !isExternalCall)
                || (productId == null && isExternalCall))
            {
                product.CreatedById = userId;
                product.CreatedDate = DateTime.UtcNow;
            }
        }

        private void ValidateProduct(AddProductModel model, int? productId, IUnitOfWork unitOfWork)
        {
            //check passed related product Ids
            if (model.RelatedProductIds != null && model.RelatedProductIds.Any())
            {
                var relatedIds = unitOfWork.ProductRepository.GetAll().Where(a => model.RelatedProductIds.Contains(a.Id)).Select(a => a.Id).ToList();
                var diff = model.RelatedProductIds.Except(relatedIds).ToList();
                if (diff.Any())
                {
                    throw new Exception(string.Format("Can't find next related product by id(s): {0}", string.Join(", ", diff)));
                }
            }

            //validate VanityUrl
            if (!string.IsNullOrEmpty(model.VanityURL))
            {
                if (unitOfWork.ProductRepository.GetAll().Any(a => a.VanityURL.ToLower() == model.VanityURL.ToLower() && (productId == null || (productId != null && productId.Value != a.Id))))
                {
                    throw new Exception("This Vanity Url occupied by another product, please enter another url");
                }

                if (!Regex.IsMatch(model.VanityURL, BIMsmithMarket.Domain.Constants.Constants.UrlVanityRegax))
                {
                    throw new Exception("This Vanity Url contains not accessible characters. Available next characters '[A-Za-z]|[0-9]|_|-|()'");
                }
            }
        }

        private async Task AddOrUpdateKeyValueStatListAsync(ProductStats productStats, List<ProductStatsMultipleValueModel> addedMultipleValues, IUnitOfWork unitOfWork, string userId)
        {
            if (addedMultipleValues == null) addedMultipleValues = new List<ProductStatsMultipleValueModel>();
            var addedItemIds = addedMultipleValues.Select(x => x.KeyStatUnitId).ToList();
            var existingItems = productStats.KeyStatValueList.ToList();
            var existingItemIds = existingItems.Select(x => x.KeyStatUnitId).ToList();
            var itemIdsToAdd = addedItemIds.Except(existingItemIds).ToList();
            var itemsToAdd = addedMultipleValues.Where(x => itemIdsToAdd.Contains(x.KeyStatUnitId)).ToList();
            var itemIdsToUpdate = addedItemIds.Intersect(existingItemIds).ToList();
            var itemsToUpdate = existingItems.Where(x => itemIdsToUpdate.Contains(x.KeyStatUnitId)).ToList();
            var itemIdsToDelete = existingItemIds.Except(addedItemIds).ToList();

            foreach (var item in itemsToAdd)
            {
                KeyStatValueList keyStatValueListItem = new KeyStatValueList();
                if (item.MaxValue == null)
                {
                    keyStatValueListItem.Value = item.Value;
                    keyStatValueListItem.ConvertValue = item.ConvertValue;
                }
                else
                {
                    keyStatValueListItem.MinRangeValue = item.Value;
                    keyStatValueListItem.MaxRangeValue = item.MaxValue;
                    keyStatValueListItem.ConvertMinRangeValue = item.ConvertValue;
                    keyStatValueListItem.ConvertMaxRangeValue = item.ConvertMaxValue;
                }
                if (keyStatValueListItem.Value == null) keyStatValueListItem.Value = string.Empty;

                keyStatValueListItem.Note = item.Note;

                keyStatValueListItem.KeyStatUnitId = item.KeyStatUnitId;
                keyStatValueListItem.ConvertKeyStatUnitId = item.ConvertKeyStatUnitId;
                keyStatValueListItem.ProductStatsId = productStats.Id;

                keyStatValueListItem.CreatedById = userId;
                keyStatValueListItem.CreatedDate = DateTime.UtcNow;
                unitOfWork.KeyStatValueListRepository.Insert(keyStatValueListItem);
            }
            foreach (var keyStatValueListItem in itemsToUpdate)
            {
                var modelKeyStatValueListItem = addedMultipleValues.FirstOrDefault(x => x.KeyStatUnitId == keyStatValueListItem.KeyStatUnitId);
                if (modelKeyStatValueListItem != null)
                {
                    if (modelKeyStatValueListItem.MaxValue == null)
                    {
                        keyStatValueListItem.Value = modelKeyStatValueListItem.Value;
                        keyStatValueListItem.ConvertValue = modelKeyStatValueListItem.ConvertValue;
                    }
                    else
                    {
                        keyStatValueListItem.MinRangeValue = modelKeyStatValueListItem.Value;
                        keyStatValueListItem.MaxRangeValue = modelKeyStatValueListItem.MaxValue;
                        keyStatValueListItem.ConvertMinRangeValue = modelKeyStatValueListItem.ConvertValue;
                        keyStatValueListItem.ConvertMaxRangeValue = modelKeyStatValueListItem.ConvertMaxValue;
                    }
                    if (keyStatValueListItem.Value == null) keyStatValueListItem.Value = string.Empty;

                    keyStatValueListItem.KeyStatUnitId = modelKeyStatValueListItem.KeyStatUnitId;
                    keyStatValueListItem.ConvertKeyStatUnitId = modelKeyStatValueListItem.ConvertKeyStatUnitId;

                    keyStatValueListItem.Note = modelKeyStatValueListItem.Note;

                    keyStatValueListItem.CreatedById = userId;
                    keyStatValueListItem.CreatedDate = DateTime.UtcNow;
                    unitOfWork.KeyStatValueListRepository.Edit(keyStatValueListItem);
                }
            }
            var itemsToDelete = productStats.KeyStatValueList.Where(x => itemIdsToDelete.Contains(x.KeyStatUnitId)).ToList();
            itemsToDelete.ForEach(x => unitOfWork.KeyStatValueListRepository.Delete(x));
            await unitOfWork.SaveAsync();
        }

        private string CloneVanityUrl(string currentVanityUrl, List<string> existingVanityUrls)
        {
            currentVanityUrl = currentVanityUrl + "-2";
            if (existingVanityUrls.Contains(currentVanityUrl))
            {
                return CloneVanityUrl(currentVanityUrl, existingVanityUrls);
            }
            else
            {
                return currentVanityUrl;
            }
        }

        private void AddProductCertificate(int productId, IUnitOfWork unitOfWork, string userId, int certificateId)
        {
            ProductCertificate productCertificate = new ProductCertificate();
            productCertificate.ProductId = productId;
            productCertificate.ExternalCertificateId = certificateId;
            productCertificate.CreatedById = userId;
            productCertificate.CreatedDate = DateTime.UtcNow;
            unitOfWork.ProductCertificateRepository.Insert(productCertificate);
        }

        private void AddRelatedProduct(int productId, IUnitOfWork unitOfWork, string userId, int relatedProductId)
        {
            RelatedProduct newRelatedProduct = new RelatedProduct();
            newRelatedProduct.ProductId = productId;
            newRelatedProduct.RelatedProductId = relatedProductId;
            newRelatedProduct.CreatedById = userId;
            newRelatedProduct.CreatedDate = DateTime.UtcNow;
            unitOfWork.RelatedProductRepository.Insert(newRelatedProduct);
        }

        private void AddProductSample(int productId, IUnitOfWork unitOfWork, string userId, int sampleId)
        {
            ProductSample productSample = new ProductSample();
            productSample.ProductId = productId;
            productSample.SampleId = sampleId;
            productSample.CreatedById = userId;
            productSample.CreatedDate = DateTime.UtcNow;
            unitOfWork.ProductSampleRepository.Insert(productSample);
        }

        private void AddProductDetail(int productId, IUnitOfWork unitOfWork, string userId, int detailId)
        {
            var productDetail = new ProductDetail();
            productDetail.CreatedById = userId;
            productDetail.CreatedDate = DateTime.UtcNow;
            productDetail.ProductId = productId;
            productDetail.DetailId = detailId;
            unitOfWork.ProductDetailRepository.Insert(productDetail);
        }

        private void AddProductQualityItem(int productId, IUnitOfWork unitOfWork, string userId, int qualityItemId)
        {
            ProductQualityItem productQualityItem = new ProductQualityItem();
            productQualityItem.ProductId = productId;
            productQualityItem.QualityItemId = qualityItemId;
            productQualityItem.CreatedById = userId;
            productQualityItem.CreatedDate = DateTime.UtcNow;
            unitOfWork.ProductQualityItemRepository.Insert(productQualityItem);
        }

        private void AddProductCisfb(int productId, IUnitOfWork unitOfWork, string userId, int cisfbId)
        {
            ProductCisfb productCisfb = new ProductCisfb();
            productCisfb.CisfbId = cisfbId;
            productCisfb.ProductId = productId;
            productCisfb.CreatedById = userId;
            productCisfb.CreatedDate = DateTime.UtcNow;
            unitOfWork.ProductCisfbRepository.Insert(productCisfb);
        }

        private void AddProductOmniclass(int productId, IUnitOfWork unitOfWork, string userId, int omniclassId)
        {
            ProductOmniclass productOmniclass = new ProductOmniclass();
            productOmniclass.OmniclassId = omniclassId;
            productOmniclass.ProductId = productId;
            productOmniclass.CreatedById = userId;
            productOmniclass.CreatedDate = DateTime.UtcNow;
            unitOfWork.ProductOmniclassRepository.Insert(productOmniclass);
        }

        private void AddProductUniclass(int productId, IUnitOfWork unitOfWork, string userId, int uniclassId)
        {
            ProductUniclass productUniclass = new ProductUniclass();
            productUniclass.UniclassId = uniclassId;
            productUniclass.ProductId = productId;
            productUniclass.CreatedById = userId;
            productUniclass.CreatedDate = DateTime.UtcNow;
            unitOfWork.ProductUniclassRepository.Insert(productUniclass);
        }

        private void AddProductUniformat(int productId, IUnitOfWork unitOfWork, string userId, int uniformatId)
        {
            ProductUniformat productUniformat = new ProductUniformat();
            productUniformat.UniformatId = uniformatId;
            productUniformat.ProductId = productId;
            productUniformat.CreatedById = userId;
            productUniformat.CreatedDate = DateTime.UtcNow;
            unitOfWork.ProductUniformatRepository.Insert(productUniformat);
        }

        private void AddProductMasterformat(int productId, IUnitOfWork unitOfWork, string userId, int masterformatId)
        {
            ProductMasterformat productMasterformat = new ProductMasterformat();
            productMasterformat.ExternalMasterformatId = masterformatId;
            productMasterformat.ProductId = productId;
            productMasterformat.CreatedById = userId;
            productMasterformat.CreatedDate = DateTime.UtcNow;
            unitOfWork.ProductMasterformatRepository.Insert(productMasterformat);
        }

        private async Task UpdateProductPhotoAsync(Product product, IUnitOfWork unitOfWork, string userId, AzureStorageService azureBlobProvider, BlobContainerClient photosContainer, string productName, string manufacturerName, ProductPhoto productPhoto, PhotoModel modelProductPhoto)
        {
            productPhoto.ProductId = product.Id;
            productPhoto.PhotoId = modelProductPhoto.PhotoId;
            productPhoto.ModifiedById = userId;
            productPhoto.ModifiedDate = DateTime.UtcNow;
            unitOfWork.ProductPhotoRepository.Edit(productPhoto);
            await HandleAddOrUpdateProductPhoto(unitOfWork, azureBlobProvider, photosContainer, productName, manufacturerName, productPhoto.PhotoId);
        }

        private async Task AddProductPhotoAsync(Product product, IUnitOfWork unitOfWork, string userId, AzureStorageService azureBlobProvider, BlobContainerClient photosContainer, string productName, string manufacturerName, PhotoModel photoModel)
        {
            ProductPhoto productPhoto = new ProductPhoto();
            productPhoto.ProductId = product.Id;
            productPhoto.PhotoId = photoModel.PhotoId;
            productPhoto.CreatedById = userId;
            productPhoto.CreatedDate = DateTime.UtcNow;
            unitOfWork.ProductPhotoRepository.Insert(productPhoto);
            await HandleAddOrUpdateProductPhoto(unitOfWork, azureBlobProvider, photosContainer, productName, manufacturerName, photoModel.PhotoId);
        }

        private async Task UpdateProductStatAsync(Product product, IUnitOfWork unitOfWork, string userId, int keyStatOrder, ProductStats productStats, ProductStatsModel modelProductStats)
        {
            productStats.ProductId = product.Id;
            productStats.KeyStatId = modelProductStats.KeyStatId;
            productStats.KeyStatType = modelProductStats.KeyStatType;
            productStats.Order = keyStatOrder;

            productStats.KeyStatUnitId = modelProductStats.KeyStatUnitId;
            productStats.ConvertKeyStatUnitId = modelProductStats.ConvertKeyStatUnitId;

            productStats.CreatedById = userId;
            productStats.CreatedDate = DateTime.UtcNow;
            productStats.Note = modelProductStats.Note;

            if (!(productStats.KeyStatType == KeyStatType.MultivalueNumeric || productStats.KeyStatType == KeyStatType.TextMultivalue) && productStats.KeyStatValueList.Any())
            {
                productStats.KeyStatValueList.ToList().ForEach(x => unitOfWork.KeyStatValueListRepository.Delete(x));
            }

            if (modelProductStats.KeyStatType == KeyStatType.SingleNumeric || modelProductStats.KeyStatType == KeyStatType.TextValue)
            {
                productStats.Value = modelProductStats.SingleValue;
                productStats.ConvertValue = modelProductStats.ConvertSingleValue;
            }
            else if (modelProductStats.KeyStatType == KeyStatType.NumericRange)
            {
                productStats.MinRangeValue = modelProductStats.MinRangeValue;
                productStats.MaxRangeValue = modelProductStats.MaxRangeValue;
                productStats.ConvertMinRangeValue = modelProductStats.ConvertMinRangeValue;
                productStats.ConvertMaxRangeValue = modelProductStats.ConvertMaxRangeValue;
            }
            else if (modelProductStats.KeyStatType == KeyStatType.MultivalueNumeric || modelProductStats.KeyStatType == KeyStatType.TextMultivalue)
            {
                await AddOrUpdateKeyValueStatListAsync(productStats, modelProductStats.MultipleValues, unitOfWork, userId);
            }
            else if (modelProductStats.KeyStatType == KeyStatType.None)
            {
                // empty key stat
            }
            else
            {
                throw new NotImplementedException("Passed not implemented KeyStatType");
            }

            unitOfWork.ProductStatsRepository.Edit(productStats);
            await unitOfWork.SaveAsync();
        }

        private async Task AddProductStatAsync(Product product, IUnitOfWork unitOfWork, string userId, int keyStatOrder, ProductStatsModel productStats)
        {
            List<KeyStatValueList> keyStatValueList = new List<KeyStatValueList>();

            ProductStats pStats = new ProductStats();
            pStats.ProductId = product.Id;
            pStats.KeyStatId = productStats.KeyStatId;
            pStats.KeyStatType = productStats.KeyStatType;
            pStats.Order = keyStatOrder;

            pStats.KeyStatUnitId = productStats.KeyStatUnitId;
            pStats.ConvertKeyStatUnitId = productStats.ConvertKeyStatUnitId;

            pStats.CreatedById = userId;
            pStats.CreatedDate = DateTime.UtcNow;
            pStats.Note = productStats.Note;

            if (productStats.KeyStatType == KeyStatType.SingleNumeric || productStats.KeyStatType == KeyStatType.TextValue)
            {
                pStats.Value = productStats.SingleValue;
                pStats.ConvertValue = productStats.ConvertSingleValue;
            }
            else if (productStats.KeyStatType == KeyStatType.NumericRange)
            {
                pStats.MinRangeValue = productStats.MinRangeValue;
                pStats.MaxRangeValue = productStats.MaxRangeValue;
                pStats.ConvertMinRangeValue = productStats.ConvertMinRangeValue;
                pStats.ConvertMaxRangeValue = productStats.ConvertMaxRangeValue;
            }
            else if (productStats.KeyStatType == KeyStatType.MultivalueNumeric || productStats.KeyStatType == KeyStatType.TextMultivalue)
            {
                if (productStats.MultipleValues != null)
                {
                    foreach (var item in productStats.MultipleValues)
                    {
                        KeyStatValueList keyStatValueListItem = new KeyStatValueList();
                        if (item.MaxValue == null)
                        {
                            keyStatValueListItem.Value = item.Value;
                            keyStatValueListItem.ConvertValue = item.ConvertValue;
                        }
                        else
                        {
                            keyStatValueListItem.MinRangeValue = item.Value;
                            keyStatValueListItem.MaxRangeValue = item.MaxValue;
                            keyStatValueListItem.ConvertMinRangeValue = item.ConvertValue;
                            keyStatValueListItem.ConvertMaxRangeValue = item.ConvertMaxValue;
                        }
                        if (keyStatValueListItem.Value == null) keyStatValueListItem.Value = string.Empty;

                        keyStatValueListItem.KeyStatUnitId = item.KeyStatUnitId;
                        keyStatValueListItem.ConvertKeyStatUnitId = item.ConvertKeyStatUnitId;

                        keyStatValueListItem.Note = item.Note;

                        keyStatValueListItem.CreatedById = userId;
                        keyStatValueListItem.CreatedDate = DateTime.UtcNow;
                        keyStatValueList.Add(keyStatValueListItem);
                    }
                }
            }
            else if (productStats.KeyStatType == KeyStatType.None)
            {
                // empty key stat
            }
            else
            {
                throw new NotImplementedException("Passed not implemented KeyStatType");
            }

            unitOfWork.ProductStatsRepository.Insert(pStats);
            await unitOfWork.SaveAsync();

            foreach (var item in keyStatValueList)
            {
                item.ProductStatsId = pStats.Id;
                unitOfWork.KeyStatValueListRepository.Insert(item);
            }
        }

        private async Task SetAttachmentsWeightAsync(Product product, bool isNewProduct, int manufacturerId, IUnitOfWork unitOfWork, List<FileDto> addedProductFiles)
        {
            if (addedProductFiles == null) addedProductFiles = new List<FileDto>();
            if (isNewProduct)
            {
                var attachmentOrders = unitOfWork.AttachmentOrderRepository.GetAll().Where(a => a.ManufacturerId == manufacturerId).OrderBy(a => a.Order).Select(a => a.Type).ToList();
                if (attachmentOrders != null && attachmentOrders.Any())
                {
                    addedProductFiles = addedProductFiles.OrderBy(a => attachmentOrders.IndexOf(a.Title)).ToList();
                }
                else
                {
                    addedProductFiles = addedProductFiles.OrderBy(a => AttachmentOrderProvider.DefaultAttachmentsOrder.IndexOf(a.Title)).ToList();
                }
            }

            var productFileWeight = 1;
            foreach (var fileModel in addedProductFiles)
            {
                var productFile = product.ProductFiles.FirstOrDefault(x => x.IsAttachment && x.FileId == fileModel.FileId);
                if (productFile != null)
                {
                    productFile.Weight = productFileWeight;
                    unitOfWork.ProductFileRepository.Edit(productFile);
                    productFileWeight++;
                }
            }
            await unitOfWork.SaveAsync();
        }

        public async Task SaveProductToMongoAsync(int productId, bool isUpdate, IUnitOfWork unitOfWork)
        {
            var dbProduct = await GetProductForMongoAsync(productId, unitOfWork);
            var item = dbProduct.Adapt<ProductMongoDto>();
            if (isUpdate)
            {
                var mongoProduct = await _productRepository.GetByIdAsync(productId);
                if (mongoProduct != null)
                    await _productRepository.EditAsync(item);
                else
                    await _productRepository.InsertAsync(item);
            }
            else
                await _productRepository.InsertAsync(item);
        }

        public async Task UpdateMongoProductsAsync(int? manufacturerId, IUnitOfWork unitOfWork)
        {
            IQueryable<Product> productQuery = unitOfWork.ProductRepository.GetAll();

            if (manufacturerId.HasValue)
                productQuery = productQuery.Where(x => x.ManufacturerId == manufacturerId);

            int[] productIds = await productQuery.Select(x => x.Id).ToArrayAsync();
            foreach (int productId in productIds)
                await SaveProductToMongoAsync(productId, true, unitOfWork);
        }

        #region create product model from external model
        private void AddCategoriesToExternalModel(AddExternalProduct model, AddProductModel createProductModel, List<Category> categories)
        {
            var categoryNames = model.CategoryNames.Where(a => categories.Any(b => b.Name == a.Name)).Select(a => a.Name.ToLower()).ToList();
            createProductModel.CategoryId = categories.First(a => a.Name.ToLower() == categoryNames.First()).Id;
            if (categoryNames.Count > 1)
            {
                createProductModel.CategoryIds = new List<int>();
                foreach (var category in categories.Where(a => a.Id != createProductModel.CategoryId))
                {
                    createProductModel.CategoryIds.Add(category.Id);
                }
            }
        }

        private async Task AddPhotosToExternalModelAsync(
            AddExternalProduct model,
            IUnitOfWork unitOfWork,
            AddProductModel createProductModel,
            AddExternalValidatedUserModel validatedUserModel)
        {
            if (model.Photos != null && model.Photos.Any())
            {
                createProductModel.Photos = new List<PhotoModel>();
                foreach (var photo in model.Photos)
                {
                    var fileName = Path.GetFileName(photo.PhotoURL);
                    Photo newPhoto = new Photo
                    {
                        Name = $"{validatedUserModel.Manufacturer.Name}-{model.Name}-revit-{fileName}",
                        CreatedById = validatedUserModel.User.Id,
                        CreatedDate = DateTime.UtcNow,
                        UploadUrl = photo.PhotoURL
                    };
                    unitOfWork.PhotoRepository.Insert(newPhoto);
                    unitOfWork.Save();

                    var attachURL = photo.PhotoURL;
                    if (attachURL.StartsWith("https://"))
                    {
                        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
                    }

                    MemoryStream originalImage = new MemoryStream();
                StartRedirect:

                    using (HttpClient httpClient = new())
                    {
                        using (var photoResponse = await httpClient.GetAsync(attachURL))
                        {
                            if (photoResponse.StatusCode == HttpStatusCode.OK || photoResponse.StatusCode == HttpStatusCode.Accepted)
                            {
                                using (Stream photoResponseStream = await photoResponse.Content.ReadAsStreamAsync())
                                {
                                    photoResponseStream.CopyTo(originalImage);
                                }
                                newPhoto.SyncStatusCode = (int)HttpStatusCode.OK;
                                newPhoto.UpdatesCount = 0;
                                unitOfWork.PhotoRepository.Edit(newPhoto);
                                unitOfWork.Save();
                            }
                            else if
                            (
                                (photoResponse.StatusCode == HttpStatusCode.Moved || photoResponse.StatusCode == HttpStatusCode.MovedPermanently || photoResponse.StatusCode == HttpStatusCode.Found) &&
                                !string.IsNullOrWhiteSpace(photoResponse.Headers.Location?.ToString())
                            )
                            {
                                attachURL = photoResponse.Headers.Location?.ToString();
                                photoResponse.Dispose();
                                goto StartRedirect;
                            }
                        }

                        if (originalImage.Length != 0)
                        {
                            await PhotoProvider.CreateProductPhotosAsync(newPhoto, originalImage, Path.GetExtension(attachURL));
                            unitOfWork.PhotoRepository.Edit(newPhoto);
                            unitOfWork.Save();
                            originalImage.Dispose();
                        }

                        createProductModel.Photos.Add(new PhotoModel
                        {
                            PhotoId = newPhoto.Id
                        });
                    }
                }
            }
        }

        private void AddAttachmentsToExternalModel(AddExternalProduct model, IUnitOfWork unitOfWork, AddExternalValidatedUserModel validatedUserModel, ref AddProductModel createProductModel)
        {
            if (model.Documents != null && model.Documents.Any())
            {
                createProductModel.Files = new List<FileDto>();
                foreach (var document in model.Documents)
                {
                    var attachUrl = document.Url.Trim();
                    if (attachUrl[attachUrl.Length - 1] == '/')
                    {
                        attachUrl = attachUrl.Remove(attachUrl.Length - 1);
                    }

                    HttpResponseHeadersInfo headers = WepApiProvider.GetHeaders(attachUrl);
                    string fileName = headers.FileName ?? FileHelper.RemoveQueryPath(Path.GetFileName(attachUrl));
                    string mediaType = MimeTypeProvider.GetMimeType(fileName);

                    var dbFile = new Domain.DBModels.File();
                    dbFile.FileName = fileName;
                    dbFile.MediaType = mediaType;
                    dbFile.CreatedById = validatedUserModel.User.Id;
                    dbFile.Title = document.Type;
                    dbFile.CreatedDate = DateTime.UtcNow;
                    dbFile.PreviewUrl = new Flurl.Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment("/assets/img/default_preview.png").ToString();
                    dbFile.NextSyncDateTime = DateTime.UtcNow.AddDays(-1);
                    dbFile.SyncStatusCode = (int)HttpStatusCode.OK;
                    dbFile.UpdatesCount = 0;
                    dbFile.SyncUrl = attachUrl;

                    dbFile.Url = attachUrl;
                    dbFile.FileSize = 0;
                    dbFile.CheckSum = string.Empty;

                    unitOfWork.FileRepository.Insert(dbFile);
                    unitOfWork.Save();
                    createProductModel.Files.Add(new FileDto
                    {
                        FileId = dbFile.Id,
                        IsAttachment = true,
                        Title = dbFile.Title
                    });
                }
            }
        }

        private void AddProductStatToExternalModel(AddExternalProduct model, IUnitOfWork unitOfWork, List<KeyStatUnit> keyStatUnits, ref AddProductModel createProductModel)
        {
            if (model.ProductStats != null && model.ProductStats.Any())
            {
                createProductModel.ProductStats = new List<ProductStatsModel>();
                var keyStatNames = model.ProductStats.Select(a => a.Name.ToLower()).ToList();
                var keyStats = unitOfWork.KeyStatRepository.GetAll().Where(a => keyStatNames.Any(b => b == a.Name.ToLower())).ToList();

                foreach (var productStat in model.ProductStats)
                {
                    var keyStat = keyStats.FirstOrDefault(a => productStat.Name.ToLower() == a.Name.ToLower());
                    if (keyStat != null)
                    {
                        var keyStatType = KeyStatType.None;
                        if (!Enum.TryParse(productStat.KeyStatType, out keyStatType))
                        {
                            keyStatType = KeyStatType.TextValue;
                        }

                        var modelKeyStatUnit = productStat.KeyStatUnit;
                        if (modelKeyStatUnit == null && (keyStatType == KeyStatType.MultivalueNumeric || keyStatType == KeyStatType.TextMultivalue))
                        {
                            modelKeyStatUnit = productStat.MultipleValues?.FirstOrDefault()?.KeyStatUnit;
                        }

                        var keyStatUnit = keyStatUnits.FirstOrDefault(a => a.BUnitName == modelKeyStatUnit?.BUnitName && a.GroupName == modelKeyStatUnit?.GroupName);

                        var newProductStat = new ProductStatsModel
                        {
                            KeyStatId = keyStat.Id,
                            KeyStatType = keyStatType,
                            KeyStatUnitId = keyStatUnit?.Id,
                            Note = keyStat.Note
                        };

                        if (keyStatType == KeyStatType.SingleNumeric || keyStatType == KeyStatType.TextValue)
                        {
                            newProductStat.SingleValue = productStat.Value;
                        }
                        else if (keyStatType == KeyStatType.NumericRange)
                        {
                            newProductStat.MinRangeValue = productStat.MinRangeValue;
                            newProductStat.MaxRangeValue = productStat.MaxRangeValue;
                        }
                        else if (keyStatType == KeyStatType.MultivalueNumeric || keyStatType == KeyStatType.TextMultivalue)
                        {
                            newProductStat.MultipleValues = new List<ProductStatsMultipleValueModel>();
                            foreach (var item in productStat.MultipleValues)
                            {
                                var keyStatValueListItem = new ProductStatsMultipleValueModel();
                                if (item.MaxRangeValue == null)
                                {
                                    keyStatValueListItem.Value = item.Value;
                                }
                                else
                                {
                                    keyStatValueListItem.Value = item.Value;
                                    keyStatValueListItem.MaxValue = item.MaxRangeValue;
                                }

                                keyStatValueListItem.KeyStatUnitId = keyStatUnit?.Id;
                                newProductStat.MultipleValues.Add(keyStatValueListItem);
                            }
                        }

                        createProductModel.ProductStats.Add(newProductStat);
                    }
                }
            }
        }

        private void AddCisfbToExternalModel(AddExternalProduct model, IUnitOfWork unitOfWork, ref AddProductModel createProductModel)
        {
            if (model.CisfbIds != null && model.CisfbIds.Any())
            {
                var cisCodes = model.CisfbIds.Select(a => a.Code.ToLower()).ToList();
                var cisfbs = unitOfWork.CisfbRepository.GetAll().Where(a => cisCodes.Any(b => b == a.Code.ToLower())).ToList();
                if (cisfbs.Any())
                {
                    createProductModel.CisfbIds = new List<int>();
                    foreach (var cisfb in cisfbs)
                    {
                        createProductModel.CisfbIds.Add(cisfb.Id);
                    }
                }
            }
        }

        private void AddMasterformatsToExternalModel(AddExternalProduct model, IUnitOfWork unitOfWork, ref AddProductModel createProductModel)
        {
            if (model.MasterformatIds != null && model.MasterformatIds.Any())
            {
                var masterfCodes = model.MasterformatIds.Select(a => a.Code.ToLower()).ToList();
                var masterformats = unitOfWork.MasterformatRepository.GetAll().Where(a => masterfCodes.Any(b => b == a.Code.ToLower())).ToList();
                if (masterformats.Any())
                {
                    createProductModel.ExternalMasterformatIds = new List<int>();
                    foreach (var masterformat in masterformats)
                    {
                        createProductModel.ExternalMasterformatIds.Add(masterformat.Id);
                    }
                }
            }
        }

        private void AddUniformatsToExternalModel(AddExternalProduct model, IUnitOfWork unitOfWork, ref AddProductModel createProductModel)
        {
            if (model.UniformatIds != null && model.UniformatIds.Any())
            {
                var unifCodes = model.UniformatIds.Select(a => a.Code.ToLower()).ToList();
                var uniformats = unitOfWork.UniformatRepository.GetAll().Where(a => unifCodes.Any(b => b == a.Code.ToLower())).ToList();
                if (uniformats.Any())
                {
                    createProductModel.UniformatIds = new List<int>();
                    foreach (var uniformat in uniformats)
                    {
                        createProductModel.UniformatIds.Add(uniformat.Id);
                    }
                }
            }
        }

        private void AddUniclassesToExternalModel(AddExternalProduct model, IUnitOfWork unitOfWork, ref AddProductModel createProductModel)
        {
            if (model.UniclassIds != null && model.UniclassIds.Any())
            {
                var unicCodes = model.UniclassIds.Select(a => a.Code.ToLower()).ToList();
                var uniclasses = unitOfWork.UniclassRepository.GetAll().Where(a => unicCodes.Any(b => b == a.Code.ToLower())).ToList();
                if (uniclasses.Any())
                {
                    createProductModel.UniclassIds = new List<int>();
                    foreach (var uniclass in uniclasses)
                    {
                        createProductModel.UniclassIds.Add(uniclass.Id);
                    }
                }
            }
        }

        private void AddOmniclassesToExternalModel(AddExternalProduct model, IUnitOfWork unitOfWork, ref AddProductModel createProductModel)
        {
            if (model.OmniclassIds != null && model.OmniclassIds.Any())
            {
                var omnicCodes = model.OmniclassIds.Select(a => a.Code.ToLower()).ToList();
                var omniclasses = unitOfWork.OmniclassRepository.GetAll().Where(a => omnicCodes.Any(b => b == a.Code.ToLower())).ToList();
                if (omniclasses.Any())
                {
                    createProductModel.OmniclassIds = new List<int>();
                    foreach (var omniclass in omniclasses)
                    {
                        createProductModel.OmniclassIds.Add(omniclass.Id);
                    }
                }
            }
        }

        private void AddProductLineToExternalModel(AddExternalProduct model, IUnitOfWork unitOfWork, AddExternalValidatedUserModel validatedUserModel, ref AddProductModel createProductModel)
        {
            if (!string.IsNullOrWhiteSpace(model.ProductLine))
            {
                var productLine = unitOfWork.ProductLineRepository.GetAll().Where(a => a.ManufacturerId == validatedUserModel.Manufacturer.Id && a.Name.ToLower() == model.ProductLine.ToLower()).FirstOrDefault();
                if (productLine != null)
                {
                    createProductModel.ProductLineId = productLine.Id;
                }
            }
        }

        private AddProductModel CreateExternalProductModel(AddExternalProduct model, AddExternalValidatedUserModel validatedUserModel)
        {
            return new AddProductModel
            {
                Name = model.Name,
                ExternalProductId = model.ExternalProductId,
                Description = model.Description,
                ManufacturerId = validatedUserModel.Manufacturer.Id,
                Published = model.Published,
                PublishedOnCustomMicrosite = model.PublishedOnCustomMicrosite,
                Staging = model.Staging,
                ProductUrl = model.ProductUrl,
                ULUrl = model.ULUrl,
                VideoUrl = model.VideoUrl
            };
        }
        #endregion 

        private async Task<Product> GetForEditAsync(Product product, int? productId, IUnitOfWork unitOfWork)
        {
            product = await unitOfWork.ProductRepository.GetAll()
                .AsSplitQuery()
                .Include(a => a.ProductCategories)
                .Include(a => a.ProductOmniclasses)
                .Include(a => a.ProductUniclasses)
                .Include(a => a.ProductUniformats)
                .Include(a => a.ProductCisfbs)
                .Include(a => a.ProductMasterformats)
                .Include(a => a.ProductFiles).ThenInclude(b => b.File)
                .Include(a => a.ProductFiles).ThenInclude(b => b.ProjectDataType)
                .Include(a => a.ProductPhotos)
                .Include(a => a.ProductStats).ThenInclude(b => b.KeyStatValueList)
                .Include(a => a.ProductQualityItems)
                .Include(a => a.ProductQualityItems).ThenInclude(b => b.QualityItem)
                .Include(a => a.ProductCertificates)
                .Include(a => a.ProductSamples)
                .Include(a => a.RelatedProducts)
                .Include(a => a.ProductDetails)
                .Include(a => a.ProductFiles)
                .Include(a => a.ProductOmniclasses).ThenInclude(b => b.Omniclass)
                .Include(a => a.ProductUniclasses).ThenInclude(b => b.Uniclass)
                .Include(a => a.ProductUniformats).ThenInclude(b => b.Uniformat)
                .Include(a => a.ProductPrices)
                .Include(a => a.Price)
                .FirstOrDefaultAsync(a => a.Id == productId.Value);

            return product;
        }

        private async Task<ProductMongoDto> GetProductForMongoAsync(int id, IUnitOfWork unitOfWork)
        {
            ProductMongoHelpDto dbProduct = await unitOfWork.ProductRepository.GetAll()
                .AsSplitQuery()
                .Select(x => new ProductMongoHelpDto
                {
                    Id = x.Id,
                    Categories = x.ProductCategories.Select(c => new
                    {
                        Id = c.CategoryId,
                        Name = c.Category.Name,
                        VanityUrl = c.Category.VanityUrl,
                        Parent = c.Category.ParentCategory == null ? null : new
                        {
                            Id = c.Category.ParentCategoryId.Value,
                            Name = c.Category.ParentCategory.Name,
                            VanityUrl = c.Category.VanityUrl,
                            Parent = c.Category.ParentCategory.ParentCategory == null ? null : new
                            {
                                Id = c.Category.ParentCategory.ParentCategoryId.Value,
                                Name = c.Category.ParentCategory.ParentCategory.Name,
                                VanityUrl = c.Category.VanityUrl
                            }
                        }
                    }),
                    Category = x.Category == null ? null : new
                    {
                        Id = x.CategoryId,
                        Name = x.Category.Name,
                        VanityUrl = x.Category.VanityUrl,
                        Parent = x.Category.ParentCategory == null ? null : new
                        {
                            Id = x.Category.ParentCategoryId.Value,
                            Name = x.Category.ParentCategory.Name,
                            VanityUrl = x.Category.ParentCategory.VanityUrl,
                            Parent = x.Category.ParentCategory.ParentCategory == null ? null : new
                            {
                                Id = x.Category.ParentCategory.ParentCategoryId.Value,
                                Name = x.Category.ParentCategory.ParentCategory.Name,
                                VanityUrl = x.Category.ParentCategory.ParentCategory.VanityUrl
                            }
                        }
                    },
                    ContentRating = x.ContentRating,
                    ContentRatingCount = x.ProductRatings.Where(b => b.Type == ProductRatingType.ContentRating).Count(),
                    Description = x.Description,
                    Details = x.ProductDetails.Select(d => new ProductDetailDto
                    {
                        Id = d.DetailId,
                        Name = d.Detail.Name,
                        Photos = d.Detail.DetailPhotos.Select(p => new PhotoProductDto
                        {
                            PhotoId = p.PhotoId,
                            UploadUrl = p.Photo.UploadUrl,
                            Small = p.Photo.SmallImgUrl,
                            Middle = p.Photo.OriginalImgUrl.Replace("_b", "_m"),
                            Big = p.Photo.OriginalImgUrl
                        })
                    }),
                    DisplaySwatchboxProductOnMicrosite = x.DisplaySwatchboxProductOnMicrosite,
                    DisplaySwatchboxProductOnProductPage = x.DisplaySwatchboxProductOnProductPage,
                    ExternalCertificates = x.ProductCertificates.Select(b => b.ExternalCertificateId.Value),
                    ExternalMasterformatIds = x.ProductMasterformats.Select(m => m.ExternalMasterformatId),
                    FooterAdImage = x.FooterAdImageId == null ? null : new FooterAdImageDto
                    {
                        Id = x.FooterAdImageId,
                        Small = x.FooterAdImageId != null ? x.FooterAdImage.SmallImgUrl : null,
                        Big = x.FooterAdImageId != null ? x.FooterAdImage.OriginalImgUrl : null
                    },
                    FooterAdUrl = x.FooterAdUrl,
                    ForgeCeilingURL = x.ForgeCeilingURL,
                    ForgeFloorURL = x.ForgeFloorURL,
                    ForgeRoofURL = x.ForgeRoofURL,
                    ForgeWallURL = x.ForgeWallURL,
                    IsImperialDefault = x.IsImperialDefault,
                    Manufacture = new ManufacturerDto
                    {
                        Id = x.ManufacturerId,
                        Name = x.Manufacturer.Name,
                        Site = x.Manufacturer.Site,
                        SwatchboxManufacturerId = x.Manufacturer.SwatchboxManufacturerId,
                        VideoUrl = x.Manufacturer.VideoUrl,
                        NodeSetting = x.Manufacturer.NodeSetting,
                        LetsTalkSettings = x.Manufacturer.LetsTalkSettings,
                        Logo = new PhotoLogoDto
                        {
                            Id = x.Manufacturer.PhotoId,
                            Small = x.Manufacturer.Photo != null ? x.Manufacturer.Photo.SmallImgUrl : null,
                            Original = x.Manufacturer.Photo != null ? x.Manufacturer.Photo.OriginalImgUrl : null,
                        }
                    },
                    MetaDescription = x.MetaDescription,
                    MetaKeywords = x.MetaKeywords,
                    MetaTitle = x.MetaTitle,
                    Name = x.Name,
                    Photo = x.Photo == null ? null : new PhotoProductWithStatusDto
                    {
                        PhotoId = x.PhotoId,
                        UploadUrl = x.Photo.UploadUrl,
                        Small = x.Photo.SmallImgUrl,
                        Middle = x.Photo.OriginalImgUrl ?? x.Photo.OriginalImgUrl.Replace("_b", "_m"),
                        Big = x.Photo.OriginalImgUrl,
                        UpdatesCount = x.Photo.UpdatesCount,
                        FileSyncStatusCode = x.Photo.SyncStatusCode,
                    },
                    ProductFiles = x.ProductFiles.Where(f => f.IsAttachment).Select(r => new ProductGetFileWithRegionDto
                    {
                        Id = r.FileId,
                        CustomFileId = r.CustomFileId,
                        Title = r.File.Title,
                        FileName = r.File.FileName,
                        FileSize = r.File.FileSize,
                        RegionIds = r.RegionIds,
                        StateIds = r.StateIds,
                        UpdatesCount = r.File.UpdatesCount,
                        FileSyncStatusCode = r.File.SyncStatusCode,
                        FileSyncUrl = r.File.SyncUrl,
                        MimeType = r.File.MediaType,
                        Url = r.File.Url,
                        Preview = r.File.PreviewUrl,
                        Weight = r.Weight
                    })
                    .OrderByDescending(p => p.Weight)
                    .AsEnumerable(),
                    ProductLine = x.ProductLineId == null ? null : new ProductLineDto
                    {
                        Id = x.ProductLineId,
                        Name = x.ProductLine.Name,
                        RegionIds = x.ProductLine.RegionIds,
                        StateIds = x.ProductLine.StateIds,
                        ExternalCertificates = x.ProductLine.ProductLineCertificates.Select(b => b.ExternalCertificateId),
                        QualityItems = x.ProductLine.ProductLineQualityItems.Select(r => new QualityItemDto
                        {
                            Id = r.QualityItemId,
                            Name = r.QualityItem.Name,
                            IconUrl = r.QualityItem.IconUrl
                        }),
                        ProductFiles = x.ProductLine.ProductLineFiles.Where(f => f.IsAttachment).Select(r => new ProductGetFileWithWeightDto
                        {
                            Id = r.FileId,
                            CustomFileId = r.CustomFileId,
                            Title = r.File.Title,
                            FileName = r.File.FileName,
                            FileSize = r.File.FileSize,
                            MimeType = r.File.MediaType,
                            UpdatesCount = r.File.UpdatesCount,
                            FileSyncStatusCode = r.File.SyncStatusCode,
                            FileSyncUrl = r.File.SyncUrl,
                            Url = r.File.Url,
                            Preview = r.File.PreviewUrl,
                            Weight = r.Weight
                        })
                        .OrderByDescending(p => p.Weight)
                        .AsEnumerable(),
                        ProjectFiles = x.ProductLine.ProductLineFiles.Where(f => !f.IsAttachment).Select(r => new ProjectGetFileWithTypeDto
                        {
                            Id = r.FileId,
                            CustomFileId = r.CustomFileId,
                            SoftwareRelease = r.SoftwareRelease,
                            ContentCreatedby = r.ContentCreatedby,
                            ContentCheckedBy = r.ContentCheckedBy,
                            FileVersion = r.FileVersion,
                            ProjectType = new ProjectGetDataTypeDto
                            {
                                Id = r.ProjectDataTypeId,
                                Title = r.ProjectDataType.Title,
                                Header = r.ProjectDataType.Header,
                                ParentId = r.ProjectDataType.ParentId
                            },
                            Title = r.File.Title,
                            FileName = r.File.FileName,
                            FileSize = r.File.FileSize,
                            MimeType = r.File.MediaType,
                            UpdatesCount = r.File.UpdatesCount,
                            FileSyncStatusCode = r.File.SyncStatusCode,
                            FileSyncUrl = r.File.SyncUrl,
                            Url = r.File.Url,
                            Preview = r.File.PreviewUrl
                        })
                    },
                    ProductPhotos = x.ProductPhotos.Select(r => new PhotoProductWithMainParamDto
                    {
                        PhotoId = r.PhotoId,
                        UploadUrl = r.Photo.UploadUrl,
                        Small = r.Photo.SmallImgUrl,
                        Middle = r.Photo.OriginalImgUrl.Replace("_b", "_m"), // try to get middle size
                        Big = r.Photo.OriginalImgUrl,
                        IsMainPhoto = r.PhotoId == x.PhotoId,
                        UpdatesCount = r.Photo.UpdatesCount,
                        FileSyncStatusCode = r.Photo.SyncStatusCode
                    })
                    .OrderByDescending(p => p.IsMainPhoto)
                    .AsEnumerable(),
                    ProductRating = x.ProductRating,
                    ProductRatingCount = x.ProductRatings.Where(b => b.Type == ProductRatingType.ProductRating).Count(),
                    ProductStats = x.ProductStats.Select(r => new ProductStatsDto
                    {
                        KeyStatId = r.KeyStatId,
                        Name = r.KeyStat == null ? null : r.KeyStat.Name,
                        Note = r.Note,
                        KeyStatType = r.KeyStatType,
                        Order = r.Order,
                        KeyStatUnitId = r.KeyStatUnitId,
                        KeyStatUnit = r.KeyStatUnit == null ? null : new KeyStatDto
                        {
                            AUnitName = r.KeyStatUnit.AUnitName,
                            BUnitName = r.KeyStatUnit.BUnitName,
                            UnitMetricType = r.KeyStatUnit.UnitMetricType
                        },
                        ConvertKeyStatUnitId = r.ConvertKeyStatUnitId,
                        ConvertKeyStatUnit = r.ConvertKeyStatUnit == null ? null : new KeyStatDto
                        {
                            AUnitName = r.ConvertKeyStatUnit.AUnitName,
                            BUnitName = r.ConvertKeyStatUnit.BUnitName,
                            UnitMetricType = r.ConvertKeyStatUnit.UnitMetricType
                        },
                        SingleValue = r.Value,
                        MinRangeValue = r.MinRangeValue,
                        MaxRangeValue = r.MaxRangeValue,
                        ConvertSingleValue = r.ConvertValue,
                        ConvertMinRangeValue = r.ConvertMinRangeValue,
                        ConvertMaxRangeValue = r.ConvertMaxRangeValue,
                        MultipleValues = r.KeyStatValueList.Select(k => new KeyStatListDto
                        {
                            KeyStatUnitId = k.KeyStatUnitId,
                            KeyStatUnit = k.KeyStatUnit == null ? null : new KeyStatDto
                            {
                                AUnitName = k.KeyStatUnit.AUnitName,
                                BUnitName = k.KeyStatUnit.BUnitName,
                                UnitMetricType = k.KeyStatUnit.UnitMetricType
                            },
                            Note = k.Note,
                            ConvertKeyStatUnitId = k.ConvertKeyStatUnitId,
                            ConvertKeyStatUnit = k.ConvertKeyStatUnit == null ? null : new KeyStatDto
                            {
                                AUnitName = k.ConvertKeyStatUnit.AUnitName,
                                BUnitName = k.ConvertKeyStatUnit.BUnitName,
                                UnitMetricType = k.ConvertKeyStatUnit.UnitMetricType
                            },
                            Value = !(k.Value == null || k.Value.Trim() == string.Empty) ? k.Value : k.MinRangeValue,
                            MaxValue = k.MaxRangeValue,
                            ConvertValue = k.ConvertValue ?? k.ConvertMinRangeValue,
                            ConvertMaxValue = k.ConvertMaxRangeValue
                        })
                    })
                    .OrderBy(k => k.Order)
                    .AsEnumerable(),
                    ProductUrl = x.ProductUrl,
                    ProjectFiles = x.ProductFiles.Where(f => !f.IsAttachment && !f.IsULPartnership).Select(r => new ProjectGetFileWithVersionDto
                    {
                        Id = r.FileId,
                        CustomFileId = r.CustomFileId,
                        RegionIds = r.RegionIds,
                        StateIds = r.StateIds,
                        SoftwareRelease = r.SoftwareRelease,
                        ContentCreatedby = r.ContentCreatedby,
                        ContentCheckedBy = r.ContentCheckedBy,
                        FileVersion = r.FileVersion,
                        SoftwareVersionId = r.SoftwareVersionId,
                        SoftwareVersion = r.SoftwareVersion != null ? new SoftwareVersionDto
                        {
                            Id = r.SoftwareVersion.Id,
                            Title = r.SoftwareVersion.Title,
                            Header = r.SoftwareVersion.Header,
                            ParentId = r.SoftwareVersion.ParentId
                        } : null,
                        ProjectDataType = r.ProjectDataType != null ? new ProjectGetDataTypeDto
                        {
                            Id = r.ProjectDataTypeId,
                            Title = r.ProjectDataType.Title,
                            Header = r.ProjectDataType.Header,
                            ParentId = r.ProjectDataType.ParentId
                        } : null,
                        Title = r.File.Title,
                        FileName = r.File.FileName,
                        FileSize = r.File.FileSize,
                        MimeType = r.File.MediaType,
                        UpdatesCount = r.File.UpdatesCount,
                        FileSyncStatusCode = r.File.SyncStatusCode,
                        FileSyncUrl = r.File.SyncUrl,
                        Url = r.File.Url,
                        Preview = r.File.PreviewUrl,
                    }),
                    Published = x.Published,
                    QualityItems = x.ProductQualityItems.Select(r => new QualityItemDto
                    {
                        Id = r.QualityItemId,
                        Name = r.QualityItem.Name == null ? null : r.QualityItem.Name,
                        IconUrl = r.QualityItem.IconUrl == null ? null : r.QualityItem.IconUrl
                    }),
                    RelatedProducts = x.RelatedProducts.Select(r => new RelatedProductDto
                    {
                        ProductId = r.RelatedProductId,
                        Name = r.RelatedProductItem.Name,
                        CategoryName = r.RelatedProductItem.Category.Name,
                        ManufacturerName = r.RelatedProductItem.Manufacturer.Name,
                        PhotoUrl = r.RelatedProductItem.PhotoId != null ? r.RelatedProductItem.Photo.MiddleImgUrl : null
                    }),
                    Samples = x.ProductSamples.Select(b => b.SampleId),
                    Staging = x.Staging,
                    SwatchboxOptionId = x.SwatchboxOptionId,
                    SwatchboxProductId = x.SwatchboxProductId,
                    UlProductFiles = x.ProductFiles.Where(f => f.IsULPartnership).Select(r => new ProductGetFileDto
                    {
                        Id = r.FileId,
                        CustomFileId = r.CustomFileId,
                        Title = r.File.Title,
                        FileName = r.File.FileName,
                        FileSize = r.File.FileSize,
                        UpdatesCount = r.File.UpdatesCount,
                        FileSyncStatusCode = r.File.SyncStatusCode,
                        FileSyncUrl = r.File.SyncUrl,
                        MimeType = r.File.MediaType,
                        Url = r.File.Url,
                        Preview = r.File.PreviewUrl,
                    }),
                    VanityURL = x.VanityURL,
                    VideoUrl = x.VideoUrl
                })
                .FirstOrDefaultAsync(a => a.Id == id);

            return dbProduct.Adapt<ProductMongoDto>();
        }

        private bool IsAttachmentEqualsToModelFile(ProductFile dbProductFile, FileDto modelProductFile)
        {
            return dbProductFile.CustomFileId == modelProductFile.CustomFileId
                && dbProductFile.FileId == modelProductFile.FileId
                && dbProductFile.RegionIds == modelProductFile.RegionIds
                && dbProductFile.StateIds == modelProductFile.StateIds
                && dbProductFile.IsAttachment == modelProductFile.IsAttachment
                && dbProductFile.IsULPartnership == !modelProductFile.IsAttachment;
        }

        private bool IsProjectFileEqualsToModelFile(ProductFile dbProductFile, ProjectFileModel modelProjectFile)
        {
            return dbProductFile.CustomFileId == modelProjectFile.CustomFileId
                && dbProductFile.FileId == modelProjectFile.FileId
                && dbProductFile.RegionIds == modelProjectFile.RegionIds
                && dbProductFile.StateIds == modelProjectFile.StateIds
                && dbProductFile.SoftwareRelease == modelProjectFile.SoftwareRelease
                && dbProductFile.ContentCheckedBy == modelProjectFile.ContentCheckedBy
                && dbProductFile.ContentCreatedby == modelProjectFile.ContentCreatedby
                && dbProductFile.FileVersion == modelProjectFile.FileVersion
                && dbProductFile.ProjectDataTypeId == modelProjectFile.ProjectDataTypeTypeId
                && dbProductFile.SoftwareVersionId == modelProjectFile.SoftwareVersionId;
        }

        private async Task<ProductGetDto> CheckProductForLockedLinksAndCorrectItAsync(ProductGetDto product, IUnitOfWork unitOfWork)
        {
            foreach (ProductGetFileWithRegionDto productFile in product.ProductFiles)
            {
                if (productFile.Url.Contains(AzureStorageConstants.LockedProductFilesContainer))
                    productFile.Url = await _fileService.GenerateSharedAccessSignatureForFileAsync(productFile.Id, AzureStorageConstants.LockedProductFilesContainer, TimeSpan.FromHours(1), unitOfWork);
            }

            foreach (ProjectGetFileWithVersionDto projectFile in product.ProjectFiles)
            {
                if (projectFile.Url.Contains(AzureStorageConstants.LockedProductFilesContainer))
                    projectFile.Url = await _fileService.GenerateSharedAccessSignatureForFileAsync(projectFile.Id, AzureStorageConstants.LockedProductFilesContainer, TimeSpan.FromHours(1), unitOfWork);
            }

            return product;
        }
        #endregion
    }
}