﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// Open API Controller
    /// </summary>
    [Route("api/[controller]/[action]")]
    public class OpenApiController : BaseApiController
    {
        private readonly IFileService _fileService;
        private readonly IProductService _productService;
        private readonly Services.SendAnalyticsService _sendAnalyticsService;
        private readonly SlackWebHook _slackWebHook;

        public OpenApiController(
            IFileService fileService,
            IProductService productService,
            Services.SendAnalyticsService sendAnalyticsService,
            SlackWebHook slackWebHook)
        {
            _fileService = fileService;
            _productService = productService;
            _sendAnalyticsService = sendAnalyticsService;
            _slackWebHook = slackWebHook;
        }

        /// <summary>
        /// Get files archive for specific project data type 
        /// </summary>
        /// <param name="productId">Product Id</param>
        /// <param name="projectTypes">Project types list</param>
        /// <param name="fileName">Archive name</param>
        /// <returns></returns>
        [HttpGet]
        //[ManufacturersAuthorize]
        public async Task<IActionResult> GetProjectTypeZip(int productId, [FromQuery] int[] projectTypes = null, string fileName = "archive")
        {
            var productExists = _productService.ProductExists(productId);
            if (!productExists) return NotFound("Product not found");

            if (projectTypes == null || !projectTypes.Any()) projectTypes = new[] { 1, 2 };
            var fileIds = _productService.GetFileIdsForProjectType(productId, projectTypes).ToArray();

            var initDownloadZipResult = await _fileService.InitDownloadZipAsync(fileIds, localFileFolder: Path.GetTempPath());
            if (initDownloadZipResult == null)
            {
                return NotFound("File not found");
            }

#if RELEASE
                await SendSlackDownloadZipMessage(fileName);
#endif

            Stream blobStream = await initDownloadZipResult.File.OpenReadAsync();
            if (blobStream.Length <= 22)
            {
                initDownloadZipResult.File.Delete();
                return NotFound("Empty archive was created, cant find any content!");
            }

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                try
                {
                    foreach (var projectType in projectTypes)
                        await _sendAnalyticsService.SendEventToGoogleAnalytics(ConfigurationHelper.GetValue("DownloadStatisticsUserId"));

                    var newAnalyticsEvents = await _sendAnalyticsService.GenerateProjectTypeNewAnalyticsEvents(productId, projectTypes, string.Join("_", fileIds.ToArray()), Request, unitOfWork);
                    await _sendAnalyticsService.SendEventsToNewAnalytics(newAnalyticsEvents);
                }
                catch (Exception ex)
                {
                    LogHelper.LogInfo(ConfigurationHelper.GetValue("DownloadFileLogPath"), $"ERROR OPENAPICONTROLLER File download ProductId: {productId} Referer: {Request.Headers.Referer} Requested Url {Request.GetDisplayUrl()} Exception: {ex.GetAllMessages()}");
                }
            }

            return File(initDownloadZipResult.File.OpenRead(), "application/zip", fileName + ".zip");
        }

        private async Task SendSlackDownloadZipMessage(string fileName)
        {

            //Send to Slack
            //----------------------
            string email = null;
            if (this.User != null && this.User.Identity != null)
            {
                email = AuthHelper.GetUserInfo(Request, ClaimTypes.Email);
            }
            await _slackWebHook.SendDownloadZipMessage(email, fileName);
            //----------------------
        }
    }
}