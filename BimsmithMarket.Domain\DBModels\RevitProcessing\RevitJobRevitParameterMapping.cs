﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels.RevitProcessing
{
    public class RevitJobRevitParameterMapping : BaseEntity
    {
        [StringLength(100)]
        public string RevitParameter { get; set; }

        [StringLength(100)]
        public string MarketValue { get; set; }

        public int RevitJobId { get; set; }

        [ForeignKey("RevitJobId")]
        public virtual RevitJob RevitJob { get; set; }
    }
}