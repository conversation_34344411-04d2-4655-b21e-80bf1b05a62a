﻿using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    public class CheckController : BaseApiController
    {
        private ICheckService _checkService;

        public CheckController(ICheckService checkService)
        {
            _checkService = checkService;
        }

        [HttpGet]
        public async Task<IActionResult> CheckSMTPCredentials()
        {
            return Ok(await _checkService.CheckSMTPCredentialsAsync("TEST MARKET SMTP CREDENTIALS"));
        }
    }
}