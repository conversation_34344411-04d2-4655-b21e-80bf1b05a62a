using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// Newsroom administration controller
    /// </summary>
#if !DEBUG
    [Authorize(Roles = DbConstants.AdminRole)]
#endif
    [Route("api/[controller]/[action]/{id?}")]
    public class NewsController : BaseApiController
    {
        private readonly INewsService _newsService;

        public NewsController(INewsService newsService)
        {
            _newsService = newsService;
        }

        /// <summary>
        /// Get list of all news publications
        /// </summary>
        /// <param name="q"></param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("List")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> List(string q = null, int offset = 0, int count = 10)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _newsService.ListAsync(unitOfWork, q, offset, count));
            }
        }

        /// <summary>
        /// Get detailed news 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="vanityId"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        [ActionName("Get")]
        public async Task<IActionResult> Get(int id = -1, string vanityId = null)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _newsService.GetAsync(unitOfWork, id, vanityId));
            }
        }

        /// <summary>
        /// Create new news
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Add")]
        public async Task<IActionResult> Add([FromBody] AddNewsModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var result = await _newsService.AddAsync(unitOfWork, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), model);

                CacheHelper.ClearSpecificCache("*/api/News/List*");
                CacheHelper.ClearSpecificCache("*/api/News/PublicList*");

                return Ok(result);
            }
        }

        /// <summary>
        /// Create new news
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Edit")]
        public async Task<IActionResult> Edit([FromBody] EditNewsModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var result = await _newsService.EditAsync(unitOfWork, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), model);

                CacheHelper.ClearSpecificCache("*/api/News/List*");
                CacheHelper.ClearSpecificCache("*/api/News/PublicList*");

                return Ok(result);
            }
        }


        /// <summary>
        /// Change Published flag for Manufacturer: Role-ADMIN
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("ChangePublishState")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> ChangePublishState([FromBody] ChangeNewsPublishStateModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                await _newsService.ChangePublishStateAsync(unitOfWork, model);

                CacheHelper.ClearSpecificCache("*/api/News/List*");
                CacheHelper.ClearSpecificCache("*/api/News/PublicList*");

                return Ok();
            }
        }


        /// <summary>
        /// delete news
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [ActionName("Delete")]
        public async Task<IActionResult> Delete(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                await _newsService.DeleteAsync(unitOfWork, id);

                CacheHelper.ClearSpecificCache("*/api/News/List*");
                CacheHelper.ClearSpecificCache("*/api/News/PublicList*");

                return Ok();
            }
        }


        /// <summary>
        /// Upload image to Azure BLOB and get 2 links to the images
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ActionName("UploadImage")]
        [SwaggerOperation("FileUpload")]
        public async Task<IActionResult> UploadImage(bool suppressAlphaChannel = true)
        {
            if (Request.HasFormContentType && Request.Form.Files.Any())
                return Ok(await _newsService.UploadImageAsync(Request.Form.Files[0], suppressAlphaChannel));

            return BadRequest("Content missing");
        }

        /// <summary>
        /// Upload content to Azure BLOB and get link to the contetn
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ActionName("UploadContent")]
        [SwaggerOperation("FileUpload")]
        public async Task<IActionResult> UploadContent(bool suppressAlphaChannel = true)
        {
            if (!(Request.HasFormContentType && Request.Form.Files.Any()))
                return BadRequest("Mime Multipart Content missing");

            return Ok(await _newsService.UploadContentAsync(Request.Form.Files[0], suppressAlphaChannel));
        }

        /// <summary>
        /// Lists news for public access
        /// </summary>
        /// <param name="q">The query string</param>
        /// <param name="target">The target site</param>
        /// <param name="offset">The offset to skip</param>
        /// <param name="count">The count to list</param>
        /// <param name="tag">Tha tag</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        [ActionName("PublicList")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> PublicList(NewsTargetSite target,
            string q = null,
            int offset = 0,
            int count = 10,
            string tag = null
            )
        {
            using (var unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var result = await _newsService.PublicListAsync(unitOfWork, target, q, offset, count, tag);

                CacheHelper.ClearSpecificCache("*/api/News/List*");
                CacheHelper.ClearSpecificCache("*/api/News/PublicList*");

                return Ok(result);
            }
        }

        /// <summary>
        /// Gets meta info for specified new
        /// </summary>
        /// <param name="vanityId">The vanity identifier</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        [ActionName("GetNewsMetaInfo")]
        public async Task<IActionResult> GetNewsMetaInfo(string vanityId)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _newsService.GetNewsMetaInfoAsync(vanityId, unitOfWork));
            }
        }
    }
}