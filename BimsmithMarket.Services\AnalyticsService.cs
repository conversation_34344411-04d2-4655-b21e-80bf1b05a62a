﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.Analytics;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Flurl;
using Mapster;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class AnalyticsService : IAnalyticsService
    {
        private UserManager<ApplicationUser> _userManager;
        private readonly IHttpClientFactory _httpClientFactory;

        public AnalyticsService(
            UserManager<ApplicationUser> userManager,
            IHttpClientFactory httpClientFactory
            )
        {
            _userManager = userManager;
            _httpClientFactory = httpClientFactory;
        }

        public async Task<bool> HasAnalyticsManufacturerAccessAsync(string userId, string email, int manufactuterId, ProjectType projectType)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var adminEmails = ConfigurationHelper.GetValue("AdministratorEmails").Split(',').Select(a => a.ToLower()).ToList();
                var user = await unitOfWork.UserRepository.GetAll().FirstOrDefaultAsync(a => a.Id == userId);
                var userIsNull = user == null;

                return adminEmails.Contains(email.ToLower())
                    || (user != null && await _userManager.IsInRoleAsync(user, DbConstants.AdminRole))
                    || await unitOfWork.ManufacturerRepository.GetAll().AnyAsync(x => x.Id == manufactuterId && x.OwnerId == userId)
                    || await unitOfWork.ManufacturerAdminUserRepository.GetAll().AnyAsync(x => x.ManufacturerId == manufactuterId
                                                                                            && ((!userIsNull && x.AdminUserId == userId) || (userIsNull && x.Email.ToLower() == email.ToLower()))
                                                                                            && (projectType == ProjectType.Market && (x.Roles & ManufacturerAdminRole.Analytics) == ManufacturerAdminRole.Analytics
                                                                                            || (projectType == ProjectType.Forge && (x.Roles & ManufacturerAdminRole.AnalyticsForge) == ManufacturerAdminRole.AnalyticsForge)
                                                                                            || (projectType == ProjectType.Nanolumens && (x.Roles & ManufacturerAdminRole.NanolumensAnalytics) == ManufacturerAdminRole.NanolumensAnalytics)
                                                                                            || (x.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess));
            }
        }

        public async Task<List<ProductManufacturerDictionaryDto>> GetProductManufacturerDictionaryAsync(IUnitOfWork unitOfWork)
        {
            return await unitOfWork.ProductRepository.GetAll()
                                   .Select(x => new ProductManufacturerDictionaryDto
                                   {
                                       ProductId = x.Id,
                                       VanityUrl = x.VanityURL,
                                       ManufacturerId = x.ManufacturerId
                                   })
                                   .ToListAsync();
        }

        public async Task<List<DetailManufacturerDictionaryDto>> GetDetailManufacturerDictionaryAsync(IUnitOfWork unitOfWork)
        {
            return await unitOfWork.DetailRepository.GetAll()
                                   .Select(x => new DetailManufacturerDictionaryDto
                                   {
                                       DetailId = x.Id,
                                       ManufacturerId = x.ManufacturerId.Value
                                   })
                                   .ToListAsync();
        }

        public async Task<AnalyticsLocationModel> GetLocationByIpAsync(string ip)
        {
            using HttpClient httpClient = new();
            Url url = new Url(ConfigurationHelper.GetValue("AnalyticsWebApiBaseAddress"))
                .AppendPathSegment("api/Location/GetLocation")
                .SetQueryParam("ipAddress", ip);
            using HttpResponseMessage response = await httpClient.GetAsync(url);

            if (!response.IsSuccessStatusCode)
                return null;

            string content = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<AnalyticsLocationModel>(content);
        }

        public async Task<bool> SaveSalesForceEventToAnalyticsAsync(SalesForceUserEventViewModel model)
        {
            HttpClient client = _httpClientFactory.CreateClient();
            Url url = new Url(ConfigurationHelper.GetValue("AnalyticsWebApiBaseAddress")).AppendPathSegment("/api/SalesForce/SaveUserEvent");
            HttpResponseMessage response = await client.PostAsJsonAsync(url, model);
            return response.IsSuccessStatusCode;
        }

        public async Task<AnalyticsPaginationListDto<AnalyticsRequestPricingUserListDto>> RequestPricingUserListAsync(
            int manufacturerId,
            IUnitOfWork unitOfWork,
            int offset = 0,
            int count = 10)
        {
            IQueryable<RequestPricingUser> dbQuery = unitOfWork.RequestPricingUserRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId);

            int totalCount = await dbQuery.CountAsync();
            AnalyticsRequestPricingUserListDto[] items = await dbQuery
                .OrderBy(x => x.CreatedDate)
                .Skip(offset)
                .Take(count)
                .ProjectToType<AnalyticsRequestPricingUserListDto>()
                .ToArrayAsync();

            return new AnalyticsPaginationListDto<AnalyticsRequestPricingUserListDto>
            {
                TotalCount = totalCount,
                Items = items
            };
        }

        public async Task<AnalyticsUserDataManufacturerDto[]> UserDataManufacturersAsync(IUnitOfWork unitOfWork)
        {
            return (await unitOfWork.ManufacturerRepository.GetAll()
                .Where(x => x.AnalyticsSetting != null)
                .Select(x => new
                {
                    x.Id,
                    x.Name,
                    x.AnalyticsSetting
                })
                .ToArrayAsync())
                .Select(x => new
                {
                    x.Id,
                    x.Name,
                    AnalyticsSettings = JsonConvert.DeserializeObject<AnalyticsSettingDto[]>(x.AnalyticsSetting)
                })
                .Where(x => x.AnalyticsSettings.Any(s => s.Id == "Display User Data Tab" && s.Value))
                .Select(x => new AnalyticsUserDataManufacturerDto
                {
                    Id = x.Id,
                    Name = x.Name,
                    IncludeNodes = x.AnalyticsSettings.FirstOrDefault(s => s.Id == "Include Microsites data to User Data Tab")?.Value ?? false
                })
                .ToArray();
        }
    }
}