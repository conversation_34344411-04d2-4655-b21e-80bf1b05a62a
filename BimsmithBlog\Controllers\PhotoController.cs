﻿using BIMsmithMarket.Core.Helpers;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;

namespace BIMsmithBlog.Controllers
{
    public class PhotoController
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly IHttpClientFactory _httpClientFactory;

        public PhotoController(
            IWebHostEnvironment webHostEnvironment, 
            IHttpClientFactory httpClientFactory)
        {
            _webHostEnvironment = webHostEnvironment;
            _httpClientFactory = httpClientFactory;
        }

        public async Task<ActionResult> Index(string email)
        {
            var secretKey = ConfigurationHelper.GetValue("BimsmithApiToken");
            var endPoint = ConfigurationHelper.GetValue("BimsmithApiUrl");

            string imagesFolder = Path.Combine(_webHostEnvironment.WebRootPath, "images");
            string defaultImages = Path.Combine(imagesFolder, "avatar_default.jpg");

            HttpClient client = _httpClientFactory.CreateClient();
            {
                try
                {
                    //http://uat.bimsmith.com/api/users/picture?email=<EMAIL>

                    HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Get, $"{endPoint}/users/picture?email={email}");
                    request.Headers.Add("X-API-Token", secretKey);

                    using (var response = await client.SendAsync(request))
                    {
                        if (response.StatusCode == System.Net.HttpStatusCode.OK)
                        {
                            var content = await response.Content.ReadAsStringAsync();
                            return new RedirectResult(content.Trim('\"'));
                        }
                        else
                        {
                            return new PhysicalFileResult(defaultImages, "image/png");
                        }
                    }
                }
                catch (Exception e)
                {
                    Log.Error(e.Message, e);
                    return new PhysicalFileResult(defaultImages, "image/png");
                }
            }
        }
    }
}