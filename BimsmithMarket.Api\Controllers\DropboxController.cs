﻿using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Dto.DropboxDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services;
using Dropbox.Api;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using System.IO;
using System.Net.Http;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    public class DropboxController : BaseApiController
    {
        private IDropboxService _dropboxService;

        public DropboxController(IDropboxService dropboxService)
        {
            _dropboxService = dropboxService;
        }

        /// <summary>
        /// Updates product files loaded by url from Dropbox
        /// </summary>
        /// <param name="model">The filter model</param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        public async Task<IActionResult> UpdateDropboxFiles(UpdateDropboxFilesDto model)
        {
            IUnitOfWork unitOfWork = UnitOfWork.Create();
            await _dropboxService.UpdateDropboxFilesAsync(model, unitOfWork);
            return Ok();
        }

        /// <summary>
        /// Exports shared links for Dropbox selected folder
        /// </summary>
        /// <param name="dropboxPath">The Dropbox folder path.</param>
        /// <returns></returns>
        [HttpGet]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> ExportDropboxLinks(string dropboxPath)
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            IUnitOfWork unitOfWork = UnitOfWork.Create();
            Stream excelStream = await _dropboxService.ExportDropboxLinksAsync(dropboxPath, userId, unitOfWork);
            return File(excelStream, "text/csv", "Dropbox Links.xlsx");
        }

        /// <summary>
        /// Completes Dropbox OAuth flow
        /// </summary>
        /// <param name="access_token"></param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        public async Task<IActionResult> CompleteOAuth(DropboxOAuthResponseDto model)
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            IUnitOfWork unitOfWork = UnitOfWork.Create();
            await _dropboxService.CompleteOAuthAsync(model, userId, unitOfWork);
            return Ok();
        }

        /// <summary>
        /// Checks user Dropbox authorization
        /// </summary>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        public async Task<IActionResult> CheckAuthorization()
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            IUnitOfWork unitOfWork = UnitOfWork.Create();
            await _dropboxService.CheckAuthorizationAsync(userId, unitOfWork);
            return Ok();
        }
    }
}