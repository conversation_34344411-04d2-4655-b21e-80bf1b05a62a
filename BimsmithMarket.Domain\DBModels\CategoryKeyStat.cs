﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class CategoryKeyStat
    {
        [Key]
        [Column(Order = 1)]
        public int CategoryId { get; set; }

        [Key]
        [Column(Order = 2)]
        public int KeyStatId { get; set; }

        public string CreatedById { get; set; }

        public DateTime CreatedDate { get; set; }

        public int Order { get; set; }

        /// ------------------------------------------
        [<PERSON><PERSON><PERSON>("CreatedById")]
        public virtual ApplicationUser CreatedBy { get; set; }

        [ForeignKey("CategoryId")]
        public virtual Category Category { get; set; }

        [ForeignKey("KeyStatId")]
        public virtual KeyStat KeyStat { get; set; }
    }
}
