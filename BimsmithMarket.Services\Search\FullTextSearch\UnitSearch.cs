﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;

namespace BIMsmithMarket.Services.Search.FullTextSearch
{
    public static class UnitSearch
    {
        public class UnitCandidateToken
        {
            public string Value;
            public string Unit;
            public List<QueryToken> OriginalTokens { get; set; }
        }

        /// <summary>
        /// Checks input tokens for possible units in the form of "[numbers] [text]" or "[numbers][text]"
        /// </summary>
        public static Dictionary<int, UnitCandidateToken> TryFindUnits(List<QueryToken> tokens, IUnitOfWork unitOfWork)
        {
            // we either have a token in search, or a numeric ending with it
            var foundUnits = new Dictionary<int, UnitCandidateToken>();
            var unitCandidates = new Dictionary<string, UnitCandidateToken>();

            var numericTokens = new List<QueryToken>();
            foreach (var currentToken in tokens)
            {
                var tokenString = currentToken.Original;
                var startsWithNumeric = char.IsDigit(tokenString[0]) || tokenString[0] == '.';
                if (!startsWithNumeric)
                {
                    if (!numericTokens.Any())
                    {
                        // just a text token
                        continue;
                    }
                    else
                    {
                        // could be a unit for previously found numericTokens
                        var potentialToken = new UnitCandidateToken
                        {
                            Unit = tokenString,
                            Value = string.Join(" ", numericTokens.Select(n => n.Original)),
                            OriginalTokens = numericTokens.Union(new QueryToken[] { currentToken }).ToList()
                        };

                        if (!unitCandidates.ContainsKey(potentialToken.Unit))
                        {
                            unitCandidates.Add(potentialToken.Unit, potentialToken);
                        }
                        numericTokens.Clear();
                        continue;
                    }
                }
                else
                {
                    var isAllNumeric = tokenString.All(t => char.IsDigit(t) || t == '/' || t == '.');
                    if (isAllNumeric)
                    {
                        numericTokens.Add(currentToken);
                        continue;
                    }
                    else
                    {
                        // could be value with a unit at the end
                        for (int j = 0; j < tokenString.Length; j++)
                        {
                            if (!char.IsDigit(tokenString[j]))
                            {
                                var potentialToken = new UnitCandidateToken
                                {
                                    Unit = tokenString.Substring(j),
                                    Value = string.Join(" ", numericTokens.Select(n => n.Original).Union(new string[] { tokenString.Substring(0, j) })),
                                    OriginalTokens = numericTokens.Union(new QueryToken[] { currentToken }).ToList()
                                };
                                if (!unitCandidates.ContainsKey(potentialToken.Unit))
                                {
                                    unitCandidates.Add(potentialToken.Unit, potentialToken);
                                }
                                numericTokens.Clear();
                                continue;
                            }
                        }
                    }
                }
            }

            if (unitCandidates.Any())
            {
                // common shortcuts - TODO: move to database column
                if (unitCandidates.ContainsKey("'") && !unitCandidates.ContainsKey("ft"))
                {
                    unitCandidates["ft"] = unitCandidates["'"];
                }
                if (unitCandidates.ContainsKey("\"") && !unitCandidates.ContainsKey("in"))
                {
                    unitCandidates["in"] = unitCandidates["\""];
                }
                if (unitCandidates.ContainsKey("inches") && !unitCandidates.ContainsKey("in"))
                {
                    unitCandidates["in"] = unitCandidates["inches"];
                }
                if (unitCandidates.ContainsKey("feet") && !unitCandidates.ContainsKey("foot"))
                {
                    unitCandidates["foot"] = unitCandidates["feet"];
                }
                if (unitCandidates.ContainsKey("volt") && !unitCandidates.ContainsKey("v"))
                {
                    unitCandidates["v"] = unitCandidates["volt"];
                }
                if (unitCandidates.ContainsKey("volts") && !unitCandidates.ContainsKey("v"))
                {
                    unitCandidates["v"] = unitCandidates["volts"];
                }
                if (unitCandidates.ContainsKey("kg") && !unitCandidates.ContainsKey("kilogram"))
                {
                    unitCandidates["kilogram"] = unitCandidates["kg"];
                }
                if (unitCandidates.ContainsKey("lumen") && !unitCandidates.ContainsKey("lumens"))
                {
                    unitCandidates["lumens"] = unitCandidates["lumen"];
                }

                var candidateUnits = unitOfWork.KeyStatUnitRepository.GetAll().AsNoTracking().Where(c =>
                    c.BUnitName != null && unitCandidates.Keys.Contains(c.BUnitName.ToLower()) ||
                    c.Description != null && unitCandidates.Keys.Contains(c.Description.ToLower())).AsEnumerable();

                foreach (var candidateUnit in candidateUnits)
                {
                    UnitCandidateToken statToken = null;
                    if (candidateUnit.BUnitName != null && unitCandidates.ContainsKey(candidateUnit.BUnitName.ToLower()))
                    {
                        statToken = unitCandidates[candidateUnit.BUnitName.ToLower()];
                    }
                    else if (candidateUnit.Description != null && unitCandidates.ContainsKey(candidateUnit.Description.ToLower()))
                    {
                        statToken = unitCandidates[candidateUnit.Description.ToLower()];
                    }

                    if (statToken != null)
                    {
                        foundUnits.Add(candidateUnit.Id, statToken);
                        statToken.OriginalTokens.ForEach(t => t.Type = QueryTokenType.Unit); // mark tokens so they can be removed from full text search
                    }
                }
            }

            return foundUnits;
        }

        public static Dictionary<int, float> CheckForUnitMatch(List<QueryToken> tokens, IQueryable<Product> input, IUnitOfWork unitOfWork, out bool unitsFound)
        {
            var foundUnits = TryFindUnits(tokens, unitOfWork);
            if (foundUnits.Any())
            {
                unitsFound = true;

                HashSet<int> foundProducts = null;
                foreach (var foundUnit in foundUnits)
                {
                    var unitQuery = input.Where(q => q.ProductStats.Any(s =>
                        s.KeyStatUnitId.HasValue && s.KeyStatUnitId.Value == foundUnit.Key && s.Value == foundUnit.Value.Value ||
                        s.KeyStatValueList.Any(v => v.KeyStatUnitId.HasValue && v.KeyStatUnitId.Value == foundUnit.Key && v.Value == foundUnit.Value.Value))).ToList();
                    if (unitQuery.Any())
                    {
                        foundUnit.Value.OriginalTokens.ForEach(t => t.HasResults = true);
                    }
                    if (foundProducts == null)
                    {
                        foundProducts = new HashSet<int>(unitQuery.Select(u => u.Id));
                    }
                    else
                    {
                        foundProducts.IntersectWith(unitQuery.Select(u => u.Id));
                    }
                }

                return foundProducts.ToDictionary(u => u, u => 1.0f);
            }

            unitsFound = false;
            return new Dictionary<int, float>();
        }
    }
}
