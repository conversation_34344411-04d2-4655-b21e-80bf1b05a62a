﻿using BIMsmithMarket.Domain.DBModels;
using System.Collections.Generic;
using System.Linq;

namespace BIMsmithMarket.Domain.Models.ExcelModels.StaticExcel
{
    public class ProjectDataTypeModelComparer : IEqualityComparer<ProjectDataTypeModel>
    {
        public bool Equals(ProjectDataTypeModel x, ProjectDataTypeModel y)
        {
            return x.Title.ToLowerInvariant() == y.Title.ToLowerInvariant();
        }

        public int GetHashCode(ProjectDataTypeModel obj)
        {
            return obj.Title.GetHashCode();
        }
    }

    public class ProductStatKeyStatValueListModelComparer : IComparer<List<ProductStatKeyStatValueListModel>>
    {
        public int Compare(List<ProductStatKeyStatValueListModel> x, List<ProductStatKeyStatValueListModel> y)
        {
            if ((x == null || x.Count == 0) && (y == null || y.Count == 0))
                return 0;
            else if (x == null || x.Count == 0)
                return -1;
            else if (y == null || y.Count == 0)
                return 1;
            else
            {
                var xFirst = x.First();
                var yFirst = y.First();
                if (xFirst.KeyStatUnit == null && yFirst.KeyStatUnit == null)
                    return 0;
                else if (xFirst.KeyStatUnit == null)
                    return -1;
                else if (yFirst.KeyStatUnit == null)
                    return 1;

                return xFirst.KeyStatUnit.BUnitName.CompareTo(yFirst.KeyStatUnit.BUnitName).CompareTo(xFirst.KeyStatUnit.GroupName.CompareTo(yFirst.KeyStatUnit.GroupName));
            }
        }
    }

    public class ProductStatModelComparer : IEqualityComparer<ProductProductStatModel>
    {
        public bool Equals(ProductProductStatModel x, ProductProductStatModel y)
        {
            var result = x.KeyStatName == y.KeyStatName && x.KeyStatType == y.KeyStatType;
            if (result && x.KeyStatType == KeyStatType.MultivalueNumeric)
            {
                var isEqual = true;

                var xFirst = x.KeyStatValueList.FirstOrDefault();
                var yFirst = y.KeyStatValueList.FirstOrDefault();
                if ((x.KeyStatValueList != null && x.KeyStatValueList.Count > 0 && (y.KeyStatValueList == null || (y.KeyStatValueList != null && y.KeyStatValueList.Count == 0)))
                            || ((x.KeyStatValueList == null || (x.KeyStatValueList != null && x.KeyStatValueList.Count == 0)) && y.KeyStatValueList != null && y.KeyStatValueList.Count > 0))
                {
                    isEqual = false;
                }
                else if ((x.KeyStatValueList == null || x.KeyStatValueList.Count == 0) && (y.KeyStatValueList == null || y.KeyStatValueList.Count == 0))
                {
                    isEqual = true;
                }
                else if ((xFirst.KeyStatUnit != null && yFirst.KeyStatUnit == null)
                        || (xFirst.KeyStatUnit == null && yFirst.KeyStatUnit != null))
                {
                    isEqual = false;
                }
                else if ((xFirst.KeyStatUnit != null && yFirst.KeyStatUnit == null)
                    || (xFirst.KeyStatUnit == null && yFirst.KeyStatUnit != null))
                {
                    isEqual = false;
                }
                else if (xFirst.KeyStatUnit == null && yFirst.KeyStatUnit == null)
                {
                    isEqual = true;
                }
                else if (xFirst.KeyStatUnit.BUnitName != yFirst.KeyStatUnit.BUnitName
                    || xFirst.KeyStatUnit.GroupName != yFirst.KeyStatUnit.GroupName)
                {
                    isEqual = false;
                }
                result = isEqual;
            }

            if (!result || (x.KeyStatUnit == null && y.KeyStatUnit == null))
            {
                return result;
            }

            if ((x.KeyStatUnit != null && y.KeyStatUnit == null) || (x.KeyStatUnit == null && y.KeyStatUnit != null))
            {
                return false;
            }

            return x.KeyStatUnit.GroupName == y.KeyStatUnit.GroupName && x.KeyStatUnit.BUnitName == y.KeyStatUnit.BUnitName;
        }

        public int GetHashCode(ProductProductStatModel productStat)
        {
            var result = productStat.KeyStatName.GetHashCode() ^ productStat.KeyStatType.GetHashCode();

            if (productStat.KeyStatUnit != null)
            {
                result = result ^ productStat.KeyStatUnit.GroupName.GetHashCode() ^ productStat.KeyStatUnit.BUnitName.GetHashCode();
            }

            if (productStat.KeyStatValueList != null && productStat.KeyStatValueList.Count > 0 && productStat.KeyStatValueList.First().KeyStatUnit != null)
            {
                result = result ^ productStat.KeyStatValueList.First().KeyStatUnit.GroupName.GetHashCode() ^ productStat.KeyStatValueList.First().KeyStatUnit.BUnitName.GetHashCode();
            }
            return result;
        }
    }

    public class ProductsExcelParseModel
    {
        public List<string> Errors { get; set; }

        public List<ProductModel> Products { get; set; }

        public List<ProductQualityItemsModel> QualityItems { get; set; }

        public List<ProductProductStatModel> ProductStats { get; set; }

        public List<ProductFileModel> ProductFiles { get; set; }

        public List<ProjectDataTypeModel> ProjectTypes { get; set; }

        public ProductsExcelParseModel()
        {
            Errors = new List<string>();
            Products = new List<ProductModel>();
            QualityItems = new List<ProductQualityItemsModel>();
            ProductStats = new List<ProductProductStatModel>();
            ProductFiles = new List<ProductFileModel>();
            ProjectTypes = new List<ProjectDataTypeModel>();
        }
    }

    public class ProductsExcelModel
    {
        public string FileName { get; set; }

        public List<ProductModel> Products { get; set; }

        public List<ProductQualityItemsModel> QualityItems { get; set; }

        public List<ProjectDataTypeModel> RevitProjectTypes { get; set; }
    }

    public class ProductModel
    {
        public int? Id { get; set; }

        public string Name { get; set; }

        public string ProductUrl { get; set; }

        public string ULUrl { get; set; }

        public int ManufacturerId { get; set; }

        public int CategoryId { get; set; }

        public List<int> ProductCategoryIds { get; set; }

        public int? ProductLineId { get; set; }

        public int? PhotoId { get; set; }

        public List<string> PhotoURLs { get; set; }

        public string Description { get; set; }

        public string VideoUrl { get; set; }

        public string VanityURL { get; set; }

        public string MetaTitle { get; set; }

        public string MetaDescription { get; set; }

        public string MetaKeywords { get; set; }

        public string Keywords { get; set; }

        public bool Published { get; set; }

        public bool PublishToPartner { get; set; }

        public bool Staging { get; set; }

        public string ExternalId { get; set; }

        public string ForgeWallURL { get; set; }

        public string ForgeFloorURL { get; set; }

        public string ForgeCeilingURL { get; set; }

        public string ForgeRoofURL { get; set; }

        public bool PublishedOnCustomMicrosite { get; set; }

        public List<ProductFileModel> Attachments { get; set; }

        public List<string> RevitFiles { get; set; }

        public List<ProductFileModel> ProjectFiles { get; set; }

        public List<ProductProductStatModel> ProductStats { get; set; }

        public List<int> RelatedProductsIds { get; set; }

        public List<int> ProductCertificates { get; set; }

        public List<string> ProductQualityItems { get; set; }

        public List<string> ProductCisfbs { get; set; }

        public List<int> ProductExternalMasterformatIds { get; set; }

        public List<string> ProductExternalMasterformatCodes { get; set; }

        public List<string> ProductOmniclasses { get; set; }

        public List<string> ProductUniclasses { get; set; }

        public List<string> ProductUniformats { get; set; }

        public List<string> ExcelParseErrors { get; set; }

        public float Weight { get; set; }

        public string RegionIds { get; set; }

        public int? RowIndex { get; set; }

        public int? StaticExcelFileId { get; set; }

        public List<string> AttachmentFileNames { get; set; }

        public List<string> ProjectFileNames { get; set; }

        public ProductModel()
        {
            ExcelParseErrors = new List<string>();
            AttachmentFileNames = new List<string>();
            ProjectFileNames = new List<string>();
        }
    }

    public class ProductFileModel
    {
        private char customIdSeparator = '@';

        private string customFileId;
        private string fileSyncUrl;

        public string CustomFileId
        {
            get { return customFileId; }
            set
            {
                customFileId = value;

                if (!string.IsNullOrEmpty(fileSyncUrl) && fileSyncUrl.Contains(customIdSeparator))
                {
                    customFileId = fileSyncUrl.Split(customIdSeparator).Last();
                }
            }
        }

        public string FileSyncUrl
        {
            get { return fileSyncUrl; }
            set
            {
                fileSyncUrl = value;

                if (!string.IsNullOrEmpty(fileSyncUrl) && fileSyncUrl.Contains(customIdSeparator))
                {
                    fileSyncUrl = value.Split(customIdSeparator).First();
                    customFileId = value.Split(customIdSeparator).Last();
                }

                if (!string.IsNullOrEmpty(customFileId) && !value.Contains(customIdSeparator))
                {
                    fileSyncUrl = value + customIdSeparator + CustomFileId;
                }
            }
        }

        public ProjectDataTypeModel FileType { get; set; }
        public string FileTitle { get; set; }
    }

    public class ProjectDataTypeModel
    {
        public string Title { get; set; }

        public int CellRefNumber { get; set; }
    }

    public class ProductQualityItemsModel
    {
        public int Id { get; set; }

        public string Name { get; set; }

        public int CellRefNumber { get; set; }
    }

    public class ProductProductStatModel
    {
        public int CellRefNumber { get; set; }

        public KeyStatType KeyStatType { get; set; }

        public string KeyStatName { get; set; }

        public ProductStatKeyStatUnitModel KeyStatUnit { get; set; }

        public string Value { get; set; }

        public string MinRangeValue { get; set; }

        public string MaxRangeValue { get; set; }

        public List<ProductStatKeyStatValueListModel> KeyStatValueList { get; set; }

        public string Note { get; set; }
    }

    public class ProductStatKeyStatUnitModel
    {
        public string GroupName { get; set; }

        public string BUnitName { get; set; }
    }

    public class ProductStatKeyStatValueListModel
    {
        public ProductStatKeyStatUnitModel KeyStatUnit { get; set; }

        public string Value { get; set; }

        public string MinRangeValue { get; set; }

        public string MaxRangeValue { get; set; }

        public string Note { get; set; }

        public override string ToString()
        {
            if (MinRangeValue != null)
            {
                return $"{MinRangeValue}*{MaxRangeValue}";
            }
            return Value;
        }
    }

    public class ProductAuditExcelModel
    {
        public int ProductId { get; set; }
        public string Manufacturer { get; set; }
        public string ProductName { get; set; }
        public string Category { get; set; }
        public List<ProductFileAuditExcelModel> Files { get; set; }
    }

    public class ProductFileAuditExcelModel
    {
        public string FileName { get; set; }
        public string ProjectDataType { get; set; }
        public int ProjectDataTypeId { get; set; }
    }
}