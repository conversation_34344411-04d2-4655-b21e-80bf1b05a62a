﻿using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Helpers;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    public class ReportController : BaseApiController
    {
        private readonly IMasterformatService _masterformatService;
        private readonly SitemapGenerator _sitemapGenerator;

        public ReportController(
            IMasterformatService masterformatService,
            SitemapGenerator sitemapGenerator)
        {
            _masterformatService = masterformatService;
            _sitemapGenerator = sitemapGenerator;
        }

        [HttpGet]
        public async Task<IActionResult> GetIndexSitemap()
        {
            byte[] bytes = await _sitemapGenerator.GetIndexSitemapAsync();
            return File(bytes, "text/xml", "sitemap.xml");
        }

        [HttpGet]
        public async Task<IActionResult> GetMainSitemap()
        {
            byte[] bytes = await _sitemapGenerator.GetMainSitemapAsync();
            return File(bytes, "text/xml", "main-sitemap.xml");
        }

        [HttpGet]
        public async Task<IActionResult> GetProductSitemap()
        {
            IUnitOfWork unitOfWork = UnitOfWork.Create();
            byte[] bytes = await _sitemapGenerator.GetProductSitemapAsync(unitOfWork);
            return File(bytes, "text/xml", "product-sitemap.xml");
        }

        [HttpGet]
        public async Task<IActionResult> GetManufacturerSitemap()
        {
            IUnitOfWork unitOfWork = UnitOfWork.Create();
            byte[] bytes = await _sitemapGenerator.GetManufacturerSitemapAsync(unitOfWork);
            return File(bytes, "text/xml", "manufacturer-sitemap.xml");
        }

        [HttpGet]
        public async Task<IActionResult> GetCategorySitemap()
        {
            IUnitOfWork unitOfWork = UnitOfWork.Create();
            byte[] bytes = await _sitemapGenerator.GetCategorySitemapAsync(unitOfWork);
            return File(bytes, "text/xml", "category-sitemap.xml");
        }

        [HttpGet]
        public async Task<IActionResult> GetProjectDataTypeSitemap()
        {
            IUnitOfWork unitOfWork = UnitOfWork.Create();
            byte[] bytes = await _sitemapGenerator.GetProjectDataTypeSitemapAsync(unitOfWork);
            return File(bytes, "text/xml", "project-data-type-sitemap.xml");
        }

        [HttpGet]
        public async Task<IActionResult> GetMasterformatSitemap()
        {
            byte[] bytes = await _sitemapGenerator.GetMasteformatSitemapAsync(_masterformatService);
            return File(bytes, "text/xml", "masterformat.xml");
        }

        [HttpGet]
        public async Task<IActionResult> GetRobots()
        {
            var bytes = RobotsHelper.GenerateRobots();
            return File(bytes, "text/plain", "robots.txt");
        }
    }
}