﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class ProductLineQualityItem
    {
        [Key]
        [Column(Order = 1)]
        public int ProductLineId { get; set; }

        [Key]
        [Column(Order = 2)]
        public int QualityItemId { get; set; }

        public string CreatedById { get; set; }

        public DateTime CreatedDate { get; set; }

        /// ------------------------------------------
        [<PERSON><PERSON><PERSON>("CreatedById")]
        public virtual ApplicationUser CreatedBy { get; set; }

        [ForeignKey("ProductLineId")]
        public virtual ProductLine ProductLine { get; set; }

        [ForeignKey("QualityItemId")]
        public virtual QualityItem QualityItem { get; set; }
    }
}