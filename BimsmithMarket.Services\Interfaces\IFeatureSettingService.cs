﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IFeatureSettingService
    {
        Task<bool> GetSettingStatusAsync(string name);

        Task<FeatureSetting> SetStatus(FeatureSettingSetStatusModel model);

        Task<List<FeatureSettingListDto>> ListAsync(FeatureSettingTarget? target = null);
    }
}