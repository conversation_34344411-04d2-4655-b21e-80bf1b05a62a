﻿using System;

namespace BIMsmithMarket.Domain.Dto.PaymentsDto
{
    public class PaymentPlanDto
    {
        public int? Id { get; set; }
        public int PaymentPriority { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public decimal PaymentDiscount { get; set; }
        public DateTime? StartDateUtc { get; set; }
        public DateTime? EndDateUtc { get; set; }
        public bool Active { get => CheckActive(); }

        private bool CheckActive()
        {
            var now = DateTime.UtcNow;

            if (EndDateUtc == null)
                return true;

            if (StartDateUtc == null)
                return false;

            if (StartDateUtc < now && EndDateUtc > now)
                return true;

            return false;
        }

    }
}
