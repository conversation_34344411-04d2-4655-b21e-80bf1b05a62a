﻿using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Azure.Storage.Blobs.Specialized;
using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Services.ManufacturerBackup
{
    public class ManufacturerBackupFileService : IManufacturerBackupFileService
    {
        private readonly IAzureStorageService _azureStorageService;

        public ManufacturerBackupFileService(IAzureStorageService azureStorageService)
        {
            _azureStorageService = azureStorageService;
        }

        public string GetManufacturerBackupFileName(int manufacturerId, string backupFileName)
        {
            return Path.Combine(manufacturerId.ToString(), $"{backupFileName}.json");
        }

        public async Task SaveManufacturerBackupToFileAsync(ManufacturerBackupModel manufacturerBackupModel)
        {
            BlobContainerClient manufacturerBackupContainer = await GetManufacturerBackupContainerAsync();
            string fileName = GetManufacturerBackupFileName(manufacturerBackupModel.Id, DateTime.UtcNow.ToString("yyyy MM dd HH mm"));
            string manufacturerBackupModelString = JsonConvert.SerializeObject(manufacturerBackupModel);
            BlockBlobClient blobFile = manufacturerBackupContainer.GetBlockBlobClient(fileName);
            using (MemoryStream stream = new MemoryStream(Encoding.UTF8.GetBytes(manufacturerBackupModelString)))
            {
                await blobFile.UploadAsync(stream);
            }
        }

        public string ConvertBackupFileNameToDateString(string backupFileName)
        {
            var dateParts = backupFileName.Split(' ');
            var dateString = string.Concat(dateParts[0], "-", dateParts[1], "-", dateParts[2], " ", dateParts[3], ":", dateParts[4], " UTC");
            return dateString;
        }

        public async Task<List<string>> GetManufacturerBackupFilesAsync(int manufacturerId)
        {
            BlobContainerClient manufacturerBackupContainer = await GetManufacturerBackupContainerAsync();
            return await GetContainerFilesAsync(manufacturerBackupContainer, manufacturerId.ToString());
        }

        public async Task<BlobContainerClient> GetManufacturerBackupContainerAsync()
        {
            return await _azureStorageService.GetContainerByNameAsync(AzureStorageConstants.ManufacturerBackupContainer);
        }

        #region private methods

        private async Task<List<string>> GetContainerFilesAsync(BlobContainerClient container, string prefix)
        {
            List<string> blobNames = new List<string>();
            await foreach (BlobItem blobItem in container.GetBlobsAsync(prefix: prefix))
            {
                blobNames.Add(blobItem.Name);
            }

            return blobNames;
        }
        #endregion
    }
}