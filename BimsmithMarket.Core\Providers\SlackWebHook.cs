﻿using BIMsmithMarket.Core.Helpers;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace BIMsmithMarket.Core.Providers
{
    public class SlackWebHook
    {
        public async Task<bool> SendLLManufacturerMessage(string email, string manufacturerName)
        {
            return await SendMessage(ConfigurationHelper.GetValue("Slack:Channels:ReportingMarket"), "Market L and L Manufacturer", string.Format("L&L by: {0} name={1}", email ?? "Unknown", manufacturerName));
        }

        public async Task<bool> SendLetsTalkManufacturerMessage(string email, string manufacturerName)
        {
            return await SendMessage(ConfigurationHelper.GetValue("Slack:Channels:ReportingMarket"), "Market Lets Talk Manufacturer", string.Format("Lets Talk by: {0} name={1}", email ?? "Unknown", manufacturerName));
        }

        public async Task<bool> SendBIMManufacturerMessage(string email, string manufacturerName, string message)
        {
            return await SendMessage(ConfigurationHelper.GetValue("Slack:Channels:ReportingMarket"), "Request BIM from Manufacturer", string.Format("Request BIM by: {0} manufacturer={1} message={2}", email ?? "Unknown", manufacturerName, message));
        }

        public async Task<bool> SendBIMRequestMessage(string email, string firstName, string lastName, string message, string sourceUrl, string manufacturerName)
        {
            return await SendMessage(ConfigurationHelper.GetValue("Slack:Channels:RequestBIM"), "BIM Request", $"{firstName} {lastName} ({email ?? "Unknown"}) just submitted a feedback request: \"{message}\". Ref: {sourceUrl}. Manufacturer: {manufacturerName}", ConfigurationHelper.GetValue("Slack:Hooks:RequestBIM"));
        }

        public async Task<bool> SendSubscribeManufacturerMessage(string email, string manufacturerName)
        {
            return await SendMessage(ConfigurationHelper.GetValue("Slack:Channels:ReportingMarket"), "Market Subscribe Manufacturer", string.Format("Subscribed by: {0} name={1}", email ?? "Unknown", manufacturerName));
        }

        public async Task<bool> SendEmptySearchMessage(
            string email,
            string firstName,
            string lastName,
            string searchQuery,
            string manufacturerName,
            string categoryName,
            string productLineName,
            string masterformatTitle,
            string omniclassTitle,
            string uniclassTitle,
            string uniformatTitle,
            string certificatesNames,
            string productFileTypes,
            string cisfbTitle,
            string qualityItemNames,
            string sourceUrl)
        {
            return await SendMessage(
                ConfigurationHelper.GetValue("Slack:Channels:ReportingSearch"),
                "Unfulfilled Search",
                $"New unfulfilled search by {firstName ?? ""} " +
                $"{lastName ?? ""} ({email ?? "Unknown"}): \"{searchQuery}\".{Environment.NewLine}*Selected filters*: " +
                $"Category: {categoryName}; Manufacturer: {manufacturerName}; " +
                $"Product Line: {productLineName}; " +
                $"Cisfb: {cisfbTitle}; " +
                $"MasterFormat: {masterformatTitle}; " +
                $"Omniclass: {omniclassTitle}; " +
                $"Uniclass: {uniclassTitle}; " +
                $"Uniformat: {uniformatTitle}; " +
                $"Certificates: {certificatesNames}; " +
                $"File Types: {productFileTypes}; " +
                $"Quality Items: {qualityItemNames}; " +
                $"Ref: {sourceUrl}",
                ConfigurationHelper.GetValue("Slack:Hooks:ReportingSearch")
            );
        }

        public async Task<bool> SendSearchMessage(
            string email,
            string searchQuery,
            string manufacturerName,
            string categoryName,
            string cisfbTitle,
            string masterformatTitle,
            string omniclassTitle,
            string uniclassTitle,
            string uniformatTitle,
            string certificatesNames,
            string productFileTypes,
            string qualityItemNames)
        {

            StringBuilder sb = new StringBuilder();

            sb.AppendLine();
            if (manufacturerName != null)
            {
                sb.AppendLine($"manufacturerName={manufacturerName}");
            }
            if (categoryName != null)
            {
                sb.AppendLine($"categoryName={categoryName}");
            }
            if (cisfbTitle != null)
            {
                sb.AppendLine($"cisfbTitle={cisfbTitle}");
            }
            if (masterformatTitle != null)
            {
                sb.AppendLine($"masterformatTitle={masterformatTitle}");
            }
            if (omniclassTitle != null)
            {
                sb.AppendLine($"omniclassTitle={omniclassTitle}");
            }
            if (uniclassTitle != null)
            {
                sb.AppendLine($"uniclassTitle={uniclassTitle}");
            }
            if (uniformatTitle != null)
            {
                sb.AppendLine($"uniformatTitle={uniformatTitle}");
            }
            if (certificatesNames != null)
            {
                sb.AppendLine($"certificatesNames={certificatesNames}");
            }
            if (productFileTypes != null)
            {
                sb.AppendLine($"productFileTypes={productFileTypes}");
            }
            if (qualityItemNames != null)
            {
                sb.AppendLine($"qualityItemNames={qualityItemNames}");
            }

            var filters = sb.ToString();
            var message = string.Format("Search by: {0} q={1} {2}", email ?? "Unknown", searchQuery, filters);
            var result = await SendMessage(ConfigurationHelper.GetValue("Slack:Channels:ReportingMarket"), "Market Search", message);

            return result;
        }

        public async Task<bool> SendDownloadZipMessage(string email, string fileName)
        {
            var result = await SendMessage(ConfigurationHelper.GetValue("Slack:Channels:ReportingMarket"), "Market Download", string.Format("Download by: {0}, Product: {1}", email ?? "Unknown", fileName));
            return result;
        }

        public async Task<bool> SendRequestPricingMessageAsync(string email, string manufacturerName, string productName)
        {
            return await SendMessage(ConfigurationHelper.GetValue("Slack:Channels:ReportingMarket"), "Market Request Pricing", $"Pricing request by: {email ?? "Unknown"} Manufacturer: {manufacturerName} Product: {productName}");
        }

        public async Task<bool> SendRevitProcessingMessageAsync(string text)
        {
            return await SendMessage(ConfigurationHelper.GetValue("Slack:Channels:RevitProcessing"), "Market Revit Processing", text);
        }

        public async Task<bool> SendMessage(string channel, string username, string text, string url = null)
        {
            if (bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")))
                text = $"[{ConfigurationHelper.GetValue("Environment").ToUpper()}] {text}";

            bool result = false;

            HttpClient client = HttpClientFactory.GetClient();

            if (string.IsNullOrWhiteSpace(url))
            {
                url = ConfigurationHelper.GetValue("Slack:Hooks:ReportingMarket");
            }
            var json = new
            {
                channel = channel,
                username = username,
                text = text
            };

            var jsonBody = JsonConvert.SerializeObject(json);

            var parameters = new Dictionary<string, string>
                        {
                            { "payload", jsonBody },
                        };

            var encodedContent = new FormUrlEncodedContent(parameters);

            try
            {
                using (var response = await client.PostAsync(url, encodedContent))
                {
                    var responseResult = await response.Content.ReadAsStringAsync();

                    result = responseResult == "ok";
                }
            }
            catch (Exception e)
            {
                Log.Error(e.Message, e);
            }

            return result;
        }
    }
}