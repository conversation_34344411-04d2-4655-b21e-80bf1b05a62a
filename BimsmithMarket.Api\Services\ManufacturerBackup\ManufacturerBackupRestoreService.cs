﻿using Azure.Storage.Blobs.Specialized;
using Azure.Storage.Blobs;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto.SalesRepresentative;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Domain.Models.MassTransit.Events;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Text;

namespace BIMsmithMarket.Api.Services.ManufacturerBackup
{
    public class ManufacturerBackupRestoreService : IManufacturerBackupRestoreService
    {
        private readonly IManufacturerBackupFileService _manufacturerBackupFileService;
        private readonly IProductService _productService;
        private readonly IDetailService _detailService;
        private readonly IProductLineService _productLineService;
        private readonly IManufacturerService _manufacturerService;
        private readonly IPublishEndpoint _publishEndpoint;
        private readonly ISalesRepresentativeService _salesRepresentativeService;

        public ManufacturerBackupRestoreService(
            IManufacturerBackupFileService manufacturerBackupFileService,
            IProductService productService,
            IDetailService detailService,
            IProductLineService productLineService,
            IManufacturerService manufacturerService,
            IPublishEndpoint publishEndpoint,
            ISalesRepresentativeService salesRepresentativeService)
        {
            _manufacturerBackupFileService = manufacturerBackupFileService;
            _productService = productService;
            _detailService = detailService;
            _productLineService = productLineService;
            _manufacturerService = manufacturerService;
            _publishEndpoint = publishEndpoint;
            _salesRepresentativeService = salesRepresentativeService;
        }

        /// <summary>
        /// Restores manufacturer data from backup
        /// </summary>
        /// <param name="model">The model with manufacturer identifier</param>
        /// <param name="userId">The user indentifier</param>
        /// <returns></returns>
        public async Task<bool> RestoreManufacturerFromBackupAsync(ManufacturerRestoreBackupViewModel model, string userId)
        {
            var timeout = 600; //10 minutes
            using (var unitOfWork = UnitOfWork.Create(timeout))
            {
                var modelToRestore = await GetManufacturerBackupModelFromFileAsync(model.ManufacturerId, model.BackupName);
                if (model.ManufacturerId != modelToRestore.Id) throw new Exception("Backup does not contain info for current manufacturer");

                if (!unitOfWork.ManufacturerRepository.GetAll().Any(x => x.Id == model.ManufacturerId))
                    throw new Exception("Manufacturer not found");
                try
                {
                    unitOfWork.BeginTransaction();

                    try
                    {
                        await RestoreManufacturerInfoAsync(modelToRestore, unitOfWork, userId);
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("Manufacturer info restore failed", ex);
                    }
                    try
                    {
                        await RestoreManufacturerProductLinesAsync(model.ManufacturerId, userId, unitOfWork, modelToRestore);
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("Product Lines restore failed", ex);
                    }
                    try
                    {
                        await RestoreManufacturerProductsAsync(model.ManufacturerId, userId, unitOfWork, modelToRestore);
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("Products restore failed", ex);
                    }
                    try
                    {
                        await RestoreManufacturerPhotosAsync(model.ManufacturerId, userId, unitOfWork, modelToRestore);
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("Manufacturer Photos restore failed", ex);
                    }
                    try
                    {
                        await RestoreManufacturerFilesAsync(model.ManufacturerId, userId, unitOfWork, modelToRestore);
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("Manufacturer Files restore failed", ex);
                    }
                    try
                    {
                        await RestoreManufacturerDetailsAsync(model.ManufacturerId, userId, unitOfWork, modelToRestore);
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("Details restore failed", ex);
                    }
                    try
                    {
                        await RestoreManufacturerSubscribedUsersAsync(model.ManufacturerId, unitOfWork, modelToRestore);
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("Manufacturer subscribers restore failed", ex);
                    }
                    try
                    {
                        await RestoreManufacturerLetsTalkUsersAsync(model.ManufacturerId, unitOfWork, modelToRestore);
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("Manufacturer Let's Talk Users restore failed", ex);
                    }
                    try
                    {
                        await RestoreManufacturerLunchAndLearnUsersAsync(model.ManufacturerId, unitOfWork, modelToRestore);
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("Manufacturer Lunch And Learn Users restore failed", ex);
                    }
                    try
                    {
                        await RestoreManufacturerAdminUsersAsync(model.ManufacturerId, unitOfWork, modelToRestore);
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("Manufacturer admin users restore failed", ex);
                    }
                    try
                    {
                        await RestoreManufacturerBimRequestsAsync(model.ManufacturerId, unitOfWork, modelToRestore);
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("Manufacturer Bim Requests restore failed", ex);
                    }
                    try
                    {
                        await RestoreRequestPricingUsersAsync(model.ManufacturerId, unitOfWork, modelToRestore);
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("Manufacturer Request Pricing Users restore failed", ex);
                    }
                    try
                    {
                        await RestoreSalesRepresentativesAsync(model.ManufacturerId, userId, unitOfWork, modelToRestore);
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("Manufacturer Sales Representative restore failed", ex);
                    }
                    try
                    {
                        await RestoreManufacturerAttachmentOrdersAsync(model.ManufacturerId, userId, unitOfWork, modelToRestore);
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("Manufacturer Attachment Orders restore failed", ex);
                    }
                    await unitOfWork.SaveAsync();
                    unitOfWork.CommitTransaction();

                    await _publishEndpoint.Publish(new ManufacturerChangedEvent
                    {
                        ManufacturerId = model.ManufacturerId
                    });

                    return true;
                }
                catch (Exception)
                {
                    unitOfWork.RollbackTransaction();
                    throw;
                }
            }
        }

        #region private methods
        private async Task RestoreManufacturerInfoAsync(
            ManufacturerBackupModel modelToRestore,
            IUnitOfWork unitOfWork,
            string userId)
        {
            //update manufacturer fields which are not updated on CMS on Save button
            Manufacturer manufacturer = await unitOfWork.ManufacturerRepository.GetByIdAsync(modelToRestore.Id);
            manufacturer.Weight = modelToRestore.Weight;
            manufacturer.Status = modelToRestore.Status;
            manufacturer.PromotedId = modelToRestore.PromotedId;
            await unitOfWork.SaveAsync();

            //update manufacturer with method which used on CMS on Save button
            await _manufacturerService.EditAsync(modelToRestore, userId, unitOfWork, true);
        }

        private async Task RestoreManufacturerProductLinesAsync(
            int manufacturerId,
            string userId,
            IUnitOfWork unitOfWork,
            ManufacturerBackupModel modelToRestore)
        {
            var existingProductLineIds = await unitOfWork.ProductLineRepository.GetAll()
                                    .Where(x => x.ManufacturerId == manufacturerId)
                                    .Select(x => x.Id)
                                    .ToListAsync();
            var productLineIdsInBackup = modelToRestore.ProductLines.Select(x => x.Id).ToList();
            var productLineToAddIds = productLineIdsInBackup.Except(existingProductLineIds).ToList();
            var productLineToUpdateIds = productLineIdsInBackup.Intersect(existingProductLineIds).ToList();
            var productLineToDeleteIds = existingProductLineIds.Except(productLineIdsInBackup).ToList();
            foreach (var productLine in modelToRestore.ProductLines)
            {
                var productLineModel = productLine.Adapt<AddProductLineModel>();
                if (productLineToUpdateIds.Contains(productLine.Id))
                {
                    await _productLineService.AddOrUpdateProductLineAsync(productLineModel, unitOfWork, userId, productLine.Id);
                }
                else if (productLineToAddIds.Contains(productLine.Id))
                {
                    await _productLineService.AddOrUpdateProductLineAsync(productLineModel, unitOfWork, userId, productLine.Id, true);
                }
            }
            foreach (var productLineId in productLineToDeleteIds)
            {
                var productLine = unitOfWork.ProductLineRepository.GetById(productLineId);
                await _productLineService.DeleteProductLineAsync(unitOfWork, productLine);
            }
            await unitOfWork.SaveAsync();
        }

        private async Task RestoreManufacturerProductsAsync(int manufacturerId, string userId, IUnitOfWork unitOfWork, ManufacturerBackupModel modelToRestore)
        {
            var existingProductIds = await unitOfWork.ProductRepository.GetAll()
                                    .Where(x => x.ManufacturerId == manufacturerId)
                                    .Select(x => x.Id)
                                    .ToListAsync();
            var productIdsInBackup = modelToRestore.Products.Select(x => x.Id).ToList();
            var productToAddIds = productIdsInBackup.Except(existingProductIds).ToList();
            var productToUpdateIds = productIdsInBackup.Intersect(existingProductIds).ToList();
            var productToDeleteIds = existingProductIds.Except(productIdsInBackup).ToList();
            foreach (var product in modelToRestore.Products)
            {
                var productModel = product.Adapt<AddProductModel>();
                if (productToUpdateIds.Contains(product.Id))
                {
                    await _productService.AddOrUpdateAsync(productModel, userId, unitOfWork, product.Id);
                }
                else if (productToAddIds.Contains(product.Id))
                {
                    await _productService.AddOrUpdateAsync(productModel, userId, unitOfWork, product.Id, isRestoredFromBackup: true);
                }
            }
            foreach (var productId in productToDeleteIds)
            {
                await _productService.DeleteProductsByIds(unitOfWork, new List<int> { productId });
            }
        }

        private async Task RestoreManufacturerPhotosAsync(int manufacturerId, string userId, IUnitOfWork unitOfWork, ManufacturerBackupModel modelToRestore)
        {
            var existingPhotoIds = await unitOfWork.ManufacturerPhotoRepository.GetAll()
                                    .Where(x => x.ManufacturerId == manufacturerId)
                                    .Select(x => x.PhotoId)
                                    .ToListAsync();
            var photoIdsInBackup = modelToRestore.ManufacturerPhotoIds.ToList();
            var photoToAddIds = photoIdsInBackup.Except(existingPhotoIds).ToList();
            foreach (var photoId in photoToAddIds)
            {
                ManufacturerPhoto manufacturerPhoto = new ManufacturerPhoto();
                manufacturerPhoto.ManufacturerId = manufacturerId;
                manufacturerPhoto.PhotoId = photoId;
                manufacturerPhoto.CreatedDate = DateTime.UtcNow;
                manufacturerPhoto.CreatedById = userId;
                unitOfWork.ManufacturerPhotoRepository.Insert(manufacturerPhoto);
            }
            var photoToDeleteIds = existingPhotoIds.Except(photoIdsInBackup).ToList();
            var itemsToDelete = unitOfWork.ManufacturerPhotoRepository.GetAll()
            .Where(x => x.ManufacturerId == manufacturerId && photoToDeleteIds.Contains(x.PhotoId))
            .ToList();
            itemsToDelete.ForEach(x => unitOfWork.ManufacturerPhotoRepository.Delete(x));
        }

        private async Task RestoreManufacturerFilesAsync(int manufacturerId, string userId, IUnitOfWork unitOfWork, ManufacturerBackupModel modelToRestore)
        {
            var existingFileIds = await unitOfWork.ManufacturerFileRepository.GetAll()
                                    .Where(x => x.ManufacturerId == manufacturerId && x.IsAttachment)
                                    .Select(x => x.FileId)
                                    .ToListAsync();
            var fileIdsInBackup = modelToRestore.ManufacturerFiles.Select(x => x.FileId).ToList();
            var fileToAddIds = fileIdsInBackup.Except(existingFileIds).ToList();
            var fileToUpdateIds = fileIdsInBackup.Intersect(existingFileIds).ToList();
            var fileToDeleteIds = existingFileIds.Except(fileIdsInBackup).ToList();
            foreach (var fileId in fileToAddIds)
            {
                var fileModelToAdd = modelToRestore.ManufacturerFiles.FirstOrDefault(x => x.FileId == fileId);
                if (fileModelToAdd != null)
                {
                    var manufacturerFile = new ManufacturerFile();
                    manufacturerFile.ManufacturerId = manufacturerId;
                    manufacturerFile.CustomFileId = fileModelToAdd.CustomFileId;
                    manufacturerFile.FileId = fileModelToAdd.FileId;
                    manufacturerFile.IsAttachment = true;
                    manufacturerFile.CreatedById = userId;
                    manufacturerFile.CreatedDate = DateTime.UtcNow;
                    manufacturerFile.WasChanged = true;
                    unitOfWork.ManufacturerFileRepository.Insert(manufacturerFile);

                    var file = unitOfWork.FileRepository.GetById(fileModelToAdd.FileId);
                    if (file.Title != fileModelToAdd.Title)
                    {
                        file.Title = fileModelToAdd.Title;
                        unitOfWork.FileRepository.Edit(file);
                    }
                }
            }
            foreach (var fileId in fileToUpdateIds)
            {
                var dbFile = unitOfWork.ManufacturerFileRepository.GetAll().Where(x => x.FileId == fileId).FirstOrDefault();
                var modelFile = modelToRestore.ManufacturerFiles.FirstOrDefault(x => x.FileId == fileId);
                if (dbFile != null && modelFile != null)
                {
                    dbFile.CustomFileId = modelFile.CustomFileId;
                    dbFile.ModifiedById = userId;
                    dbFile.ModifiedDate = DateTime.UtcNow;
                }
                unitOfWork.ManufacturerFileRepository.Edit(dbFile);
            }
            var itemsToDelete = unitOfWork.ManufacturerFileRepository.GetAll()
            .Where(x => x.ManufacturerId == manufacturerId && fileToDeleteIds.Contains(x.FileId))
            .ToList();
            itemsToDelete.ForEach(x => unitOfWork.ManufacturerFileRepository.Delete(x));
            await unitOfWork.SaveAsync();
        }

        private async Task RestoreManufacturerDetailsAsync(int manufacturerId, string userId, IUnitOfWork unitOfWork, ManufacturerBackupModel modelToRestore)
        {
            var existingDetailIds = await unitOfWork.DetailRepository.GetAll()
                                   .Where(x => x.ManufacturerId == manufacturerId)
                                   .Select(x => x.Id)
                                   .ToListAsync();
            var detailIdsInBackup = modelToRestore.Details.Select(x => x.Id).ToList();
            var detailToAddIds = detailIdsInBackup.Except(existingDetailIds).ToList();
            var detailToUpdateIds = detailIdsInBackup.Intersect(existingDetailIds).ToList();
            var detailToDeleteIds = existingDetailIds.Except(detailIdsInBackup).ToList();
            foreach (var detail in modelToRestore.Details)
            {
                var detailModel = detail.Adapt<DetailBackupModel>();
                if (detailToUpdateIds.Contains(detail.Id))
                {
                    await _detailService.EditDetailAsync(detailModel, userId, unitOfWork);
                }
                else if (detailToAddIds.Contains(detail.Id))
                {
                    await _detailService.AddDetailAsync(detailModel, userId, unitOfWork, true, detail.Id);
                }
            }
            foreach (var detailId in detailToDeleteIds)
            {
                await _detailService.DeleteDetailAsync(detailId, unitOfWork);
            }
        }

        //Only restore deleted records, do not update or remove any item
        private async Task RestoreManufacturerSubscribedUsersAsync(int manufacturerId, IUnitOfWork unitOfWork, ManufacturerBackupModel modelToRestore)
        {
            var entityRepository = unitOfWork.UserBIMsmithManufacturerRepository;
            var modelCollection = modelToRestore.SubscribedUsers;
            var existingSubscriberIds = await entityRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId)
                                    .Select(x => x.AddedById)
                                    .ToListAsync();
            var subscriberIdsInBackup = modelCollection.Select(x => x.AddedById).ToList();
            var subscriberToAddIds = subscriberIdsInBackup.Except(existingSubscriberIds).ToList();
            foreach (var subscriberId in subscriberToAddIds)
            {
                var subscriberModelToAdd = modelCollection.FirstOrDefault(x => x.AddedById == subscriberId);
                if (subscriberModelToAdd != null)
                {
                    var itemToAdd = new UserBIMsmithManufacturer();
                    itemToAdd.ManufacturerId = manufacturerId;
                    itemToAdd.AddedById = subscriberModelToAdd.AddedById;
                    itemToAdd.AddedDate = subscriberModelToAdd.AddedDate;
                    entityRepository.Insert(itemToAdd);
                }
            }

            await unitOfWork.SaveAsync();
        }

        //Only restore deleted records, do not update or remove any item
        private async Task RestoreManufacturerLetsTalkUsersAsync(int manufacturerId, IUnitOfWork unitOfWork, ManufacturerBackupModel modelToRestore)
        {
            var entityRepository = unitOfWork.UserBIMsmithLTManufacturerRepository;
            var modelCollection = modelToRestore.LetsTalkUsers;
            var existingSubscriberIds = await entityRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId)
                                    .Select(x => x.AddedById)
                                    .ToListAsync();
            var subscriberIdsInBackup = modelCollection.Select(x => x.AddedById).ToList();
            var subscriberToAddIds = subscriberIdsInBackup.Except(existingSubscriberIds).ToList();
            foreach (var subscriberId in subscriberToAddIds)
            {
                var subscriberModelToAdd = modelCollection.FirstOrDefault(x => x.AddedById == subscriberId);
                if (subscriberModelToAdd != null)
                {
                    var itemToAdd = new UserBIMsmithLTManufacturer();
                    itemToAdd.ManufacturerId = manufacturerId;
                    itemToAdd.AddedById = subscriberModelToAdd.AddedById;
                    itemToAdd.AddedDate = subscriberModelToAdd.AddedDate;
                    itemToAdd.Timezone = subscriberModelToAdd.Timezone;
                    itemToAdd.Status = subscriberModelToAdd.Status;
                    entityRepository.Insert(itemToAdd);
                }
            }

            await unitOfWork.SaveAsync();
        }

        //Only restore deleted records, do not update or remove any item
        private async Task RestoreManufacturerLunchAndLearnUsersAsync(int manufacturerId, IUnitOfWork unitOfWork, ManufacturerBackupModel modelToRestore)
        {
            var entityRepository = unitOfWork.UserBIMsmithLLManufacturerRepository;
            var modelCollection = modelToRestore.LunchAndLearnUsers;
            var existingSubscriberIds = await entityRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId)
                                    .Select(x => x.AddedById)
                                    .ToListAsync();
            var subscriberIdsInBackup = modelCollection.Select(x => x.AddedById).ToList();
            var subscriberToAddIds = subscriberIdsInBackup.Except(existingSubscriberIds).ToList();
            foreach (var subscriberId in subscriberToAddIds)
            {
                var subscriberModelToAdd = modelCollection.FirstOrDefault(x => x.AddedById == subscriberId);
                if (subscriberModelToAdd != null)
                {
                    var itemToAdd = new UserBIMsmithLLManufacturer();
                    itemToAdd.ManufacturerId = manufacturerId;
                    itemToAdd.AddedById = subscriberModelToAdd.AddedById;
                    itemToAdd.AddedDate = subscriberModelToAdd.AddedDate;
                    itemToAdd.Status = subscriberModelToAdd.Status;
                    entityRepository.Insert(itemToAdd);
                }
            }

            await unitOfWork.SaveAsync();
        }

        private async Task RestoreManufacturerAdminUsersAsync(int manufacturerId, IUnitOfWork unitOfWork, ManufacturerBackupModel modelToRestore)
        {
            var entityRepository = unitOfWork.ManufacturerAdminUserRepository;
            var modelCollection = modelToRestore.ManufacturerAdminUsers;
            var existingEmails = await entityRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId)
                                    .Select(x => x.Email)
                                    .ToListAsync();
            var emailsInBackup = modelCollection.Select(x => x.Email).ToList();
            var emailsToAdd = emailsInBackup.Except(existingEmails).ToList();
            var emailsToUpdate = emailsInBackup.Intersect(existingEmails).ToList();
            var emailsToDelete = existingEmails.Except(emailsInBackup).ToList();
            foreach (var email in emailsToAdd)
            {
                var modelToAdd = modelCollection.FirstOrDefault(x => x.Email == email);
                if (modelToAdd != null)
                {
                    var itemToAdd = new ManufacturerAdminUser();
                    itemToAdd.AddedDate = modelToAdd.AddedDate;
                    itemToAdd.AddedById = modelToAdd.AddedById;
                    itemToAdd.ManufacturerId = manufacturerId;
                    itemToAdd.AdminUserId = modelToAdd.AdminUserId;
                    itemToAdd.Email = modelToAdd.Email;
                    itemToAdd.Roles = modelToAdd.Roles;
                    itemToAdd.Status = modelToAdd.Status;
                    entityRepository.Insert(itemToAdd);
                }
            }
            foreach (var email in emailsToUpdate)
            {
                var modelToUpdate = modelCollection.FirstOrDefault(x => x.Email.ToUpper() == email.ToUpper());
                if (modelToUpdate != null)
                {
                    var itemToUpdate = await entityRepository.GetAll()
                        .FirstOrDefaultAsync(x => x.ManufacturerId == manufacturerId
                        && x.Email.ToUpper() == email.ToUpper());
                    if (itemToUpdate != null)
                    {
                        itemToUpdate.AddedDate = modelToUpdate.AddedDate;
                        itemToUpdate.AddedById = modelToUpdate.AddedById;
                        itemToUpdate.ManufacturerId = manufacturerId;
                        itemToUpdate.AdminUserId = modelToUpdate.AdminUserId;
                        itemToUpdate.Email = modelToUpdate.Email;
                        itemToUpdate.Roles = modelToUpdate.Roles;
                        itemToUpdate.Status = modelToUpdate.Status;
                        entityRepository.Edit(itemToUpdate);
                    }
                }
            }
            var itemsToDelete = entityRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId && emailsToDelete.Contains(x.Email))
                .ToList();
            itemsToDelete.ForEach(x => entityRepository.Delete(x));

            await unitOfWork.SaveAsync();
        }

        //Only restore deleted records, do not update or remove any item
        private async Task RestoreManufacturerBimRequestsAsync(int manufacturerId, IUnitOfWork unitOfWork, ManufacturerBackupModel modelToRestore)
        {
            var entityRepository = unitOfWork.UserBIMsmithBIMManufacturerRepository;
            var modelCollection = modelToRestore.BimRequests;
            var existingSubscriberIds = await entityRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId)
                                    .Select(x => x.AddedById)
                                    .ToListAsync();
            var subscriberIdsInBackup = modelCollection.Select(x => x.AddedById).ToList();
            var subscriberToAddIds = subscriberIdsInBackup.Except(existingSubscriberIds).ToList();
            foreach (var subscriberId in subscriberToAddIds)
            {
                var subscriberModelToAdd = modelCollection.FirstOrDefault(x => x.AddedById == subscriberId);
                if (subscriberModelToAdd != null)
                {
                    var itemToAdd = new UserBIMsmithBIMManufacturer();
                    itemToAdd.ManufacturerId = manufacturerId;
                    itemToAdd.Message = subscriberModelToAdd.Message;
                    itemToAdd.ProductName = subscriberModelToAdd.ProductName;
                    itemToAdd.ProductLink = subscriberModelToAdd.ProductLink;
                    itemToAdd.TimeZone = subscriberModelToAdd.TimeZone;
                    itemToAdd.Status = subscriberModelToAdd.Status;
                    itemToAdd.AddedById = subscriberModelToAdd.AddedById;
                    itemToAdd.AddedDate = subscriberModelToAdd.AddedDate;
                    entityRepository.Insert(itemToAdd);
                }
            }

            await unitOfWork.SaveAsync();
        }

        private async Task RestoreManufacturerAttachmentOrdersAsync(int manufacturerId, string userId, IUnitOfWork unitOfWork, ManufacturerBackupModel modelToRestore)
        {
            var oldAttachmentOrders = unitOfWork.AttachmentOrderRepository.GetAll().Where(a => a.ManufacturerId == manufacturerId).ToList();
            unitOfWork.AttachmentOrderRepository.Delete(oldAttachmentOrders);
            unitOfWork.Save();

            foreach (var attachmentOrder in modelToRestore.AttachmentOrders)
            {
                var newAttachmentOrder = new AttachmentOrder
                {
                    ManufacturerId = manufacturerId,
                    Type = attachmentOrder.Type,
                    Order = attachmentOrder.Order,
                    CreatedDate = DateTime.UtcNow,
                    CreatedById = userId
                };
                unitOfWork.AttachmentOrderRepository.Insert(newAttachmentOrder);
                await unitOfWork.SaveAsync();
            }
        }

        //Only restore deleted records, do not update or remove any item
        private async Task RestoreRequestPricingUsersAsync(int manufacturerId, IUnitOfWork unitOfWork, ManufacturerBackupModel modelToRestore)
        {
            var modelCollection = modelToRestore.RequestPricingUsers;
            var existingRequestPricingUserIds = await unitOfWork.RequestPricingUserRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId)
                .Select(x => x.Id)
                .ToListAsync();
            var existingRequestPricingUserIdsInBackup = modelCollection.Select(x => x.Id).ToList();
            var requestPricingUserToAddIds = existingRequestPricingUserIdsInBackup.Except(existingRequestPricingUserIds).ToList();
            foreach (var requestPricingUserId in requestPricingUserToAddIds)
            {
                var requestPricingUserToAdd = modelCollection.FirstOrDefault(x => x.Id == requestPricingUserId);
                if (requestPricingUserToAdd != null)
                {
                    var itemToAdd = requestPricingUserToAdd.Adapt<RequestPricingUser>();
                    unitOfWork.RequestPricingUserRepository.Insert(itemToAdd);
                }
            }

            await unitOfWork.SaveAsync();
        }

        private async Task RestoreSalesRepresentativesAsync(int manufacturerId, string userId, IUnitOfWork unitOfWork, ManufacturerBackupModel modelToRestore)
        {
            var entityRepository = unitOfWork.SalesRepresentativeRepository;
            var modelCollection = modelToRestore.SalesRepresentatives;
            var existingSalesRepresentativeIds = await entityRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId)
                .Select(x => x.Id)
                .ToListAsync();
            var salesRepresentativeIdsInBackup = modelCollection.Select(x => x.Id).ToList();
            var salesRepresentativeToAddIds = salesRepresentativeIdsInBackup.Except(existingSalesRepresentativeIds).ToList();
            var salesRepresentativeToUpdateIds = salesRepresentativeIdsInBackup.Intersect(existingSalesRepresentativeIds).ToList();
            var salesRepresentativeToDeleteIds = existingSalesRepresentativeIds.Except(salesRepresentativeIdsInBackup).ToList();
            foreach (var salesRepresentativeId in salesRepresentativeToAddIds)
            {
                var salesRepresentativeModelToAdd = modelCollection.FirstOrDefault(x => x.Id == salesRepresentativeId);
                if (salesRepresentativeModelToAdd != null)
                {
                    var itemToAdd = salesRepresentativeModelToAdd.Adapt<AddSalesRepresentativeDto>();
                    await _salesRepresentativeService.AddAsync(itemToAdd, salesRepresentativeModelToAdd.CreatedById, unitOfWork);
                }
            }
            foreach (var salesRepresentativeId in salesRepresentativeToUpdateIds)
            {
                var salesRepresentativeModelToUpdate = modelCollection.FirstOrDefault(x => x.Id == salesRepresentativeId);
                if (salesRepresentativeModelToUpdate != null)
                {
                    await _salesRepresentativeService.EditAsync(salesRepresentativeModelToUpdate.Adapt<EditSalesRepresentativeDto>(), userId, unitOfWork);
                }
            }
            foreach (int salesRepresentativeId in salesRepresentativeToDeleteIds)
            {
                await _salesRepresentativeService.DeleteAsync(salesRepresentativeId, unitOfWork);
            }
        }

        private async Task<ManufacturerBackupModel> GetManufacturerBackupModelFromFileAsync(int manufacturerId, string backupName)
        {
            BlobContainerClient manufacturerBackupContainer = await _manufacturerBackupFileService.GetManufacturerBackupContainerAsync();
            string fullBackupName = _manufacturerBackupFileService.GetManufacturerBackupFileName(manufacturerId, backupName);
            BlockBlobClient blobFile = manufacturerBackupContainer.GetBlockBlobClient(fullBackupName);

            if (!await blobFile.ExistsAsync())
                throw new Exception("Backup file not found");

            var manufacturerBackupModelString = string.Empty;

            using (var stream = await blobFile.OpenReadAsync())
            {
                using (StreamReader reader = new StreamReader(stream, Encoding.UTF8))
                {
                    manufacturerBackupModelString = await reader.ReadToEndAsync();
                }
            }

            if (string.IsNullOrWhiteSpace(manufacturerBackupModelString))
                throw new Exception("Backup file is corrupted");

            var manufacturerBackupModel = JsonConvert.DeserializeObject<ManufacturerBackupModel>(manufacturerBackupModelString);
            return manufacturerBackupModel;
        }
        #endregion
    }
}