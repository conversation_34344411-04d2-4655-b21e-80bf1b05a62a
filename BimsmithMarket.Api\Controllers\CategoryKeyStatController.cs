﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("api/[controller]/[action]/{id?}")]
    public class CategoryKeyStatController : BaseApiController
    {
        private readonly ICategoryKeyStatService _categoryKeyStatService;

        public CategoryKeyStatController(ICategoryKeyStatService categoryKeyStatService)
        {
            _categoryKeyStatService = categoryKeyStatService;
        }

        /// <summary>
        /// Get list of key stat units
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("Units")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> Units()
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _categoryKeyStatService.UnitsAsync(unitOfWork));
            }
        }

        /// <summary>
        /// Get list of key stats
        /// </summary>
        /// <param name="q"></param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("List")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> List(string q = null, int offset = 0, int count = 10)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _categoryKeyStatService.ListAsync(unitOfWork, q, offset, count));
            }
        }

        /// <summary>
        /// Get detailed information about Category Key Stat
        /// </summary>
        /// <param name="id">Id of Category Key Stat</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("Get")]
        public async Task<IActionResult> Get(int id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _categoryKeyStatService.GetAsync(unitOfWork, id));
            }
        }

        /// <summary>
        /// Add new Key stat: Role-ADMIN
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Add")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> Add([FromBody] AddCategoryKeyStatModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var result = await _categoryKeyStatService.AddAsync(unitOfWork, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), model);

                CacheHelper.ClearSpecificCache("*/api/CategoryKeyStat/Units*");
                CacheHelper.ClearSpecificCache("*/api/CategoryKeyStat/List*");

                return Ok(result);
            }
        }

        /// <summary>
        /// Edit Key stat: Role-ADMIN
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Edit")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> Edit([FromBody] EditCategoryKeyStatModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                await _categoryKeyStatService.EditAsync(unitOfWork, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), model);

                CacheHelper.ClearSpecificCache("*/api/CategoryKeyStat/Units*");
                CacheHelper.ClearSpecificCache("*/api/CategoryKeyStat/List*");

                return Ok();
            }
        }

        /// <summary>
        /// Delete key stat (Note: will be delated all dependens for this item): Role-ADMIN
        /// </summary>
        /// <param name="id">Key Stat Id</param>
        /// <returns></returns>
        [HttpDelete]
        [ActionName("Delete")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> Delete(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                await _categoryKeyStatService.DeleteAsync(unitOfWork, id);

                CacheHelper.ClearSpecificCache("*/api/CategoryKeyStat/Units*");
                CacheHelper.ClearSpecificCache("*/api/CategoryKeyStat/List*");

                return Ok();
            }
        }
    }
}