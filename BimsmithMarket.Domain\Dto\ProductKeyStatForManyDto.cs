﻿using BIMsmithMarket.Domain.DBModels;
using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Dto
{
    public class ProductKeyStatForManyDto
    {
        public int ProductId { get; set; }

        public ICollection<KeyStatForManyDto> KeyStats { get; set; }
    }

    public class KeyStatForManyDto
    {
        public int KeyStatId { get; set; }

        public string Name { get; set; }

        public string SingleValue { get; set; }

        public KeyStatType KeyStatType { get; set; }

        public List<KeyStatForManyMultipleValueDto> MultipleValues { get; set; }
    }

    public class KeyStatForManyMultipleValueDto
    {
        public string Value { get; set; }
    }
}
