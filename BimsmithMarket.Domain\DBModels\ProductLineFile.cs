﻿using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class ProductLineFile : BaseEntity
    {
        public string CustomFileId { get; set; }

        public int ProductLineId { get; set; }

        public int FileId { get; set; }

        public int? ProjectDataTypeId { get; set; }

        public string SoftwareRelease { get; set; }

        public int Weight { get; set; }

        public string ContentCreatedby { get; set; }

        public string FileVersion { get; set; }

        public string ContentCheckedBy { get; set; }

        [ForeignKey("ProjectDataTypeId")]
        public virtual ProjectDataType ProjectDataType { get; set; }

        public bool IsAttachment { get; set; }

        public bool WasChanged { get; set; }

        /// ------------------------------------------

        [Foreign<PERSON><PERSON>("ProductLineId")]
        public virtual ProductLine ProductLine { get; set; }


        [Foreign<PERSON><PERSON>("FileId")]
        public virtual File File { get; set; }
    }
}