﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class BIMsmithService : IBIMsmithService
    {
        public async Task<object> UserProductsListAsync(IUnitOfWork unitOfWork, string userId, string streamId, int offset, int count)
        {
            var query = unitOfWork.UserBIMsmithProductRepository.GetAll()
                    .Where(a => a.AddedById == userId);

            if (streamId != null)
            {
                query = query.Where(a => a.StreamId == streamId);
            }

            var countOfData = query.Count();

            var products = await query
                        .OrderBy(a => a.Id)
                        .Skip(offset)
                        .Take(count)
                        .Select(a => new
                        {
                            userId = a.AddedById,
                            productId = a.ProductId,
                            streamId = a.StreamId,
                            product = new
                            {
                                name = a.Product.Name,
                                description = a.Product.Description,
                                productRating = a.Product.ProductRating,
                                contentRating = a.Product.ContentRating,
                                category = new
                                {
                                    id = a.Product.CategoryId,
                                    name = a.Product.Category.Name,
                                    parent = a.Product.Category.ParentCategoryId == null ? null : new
                                    {
                                        id = a.Product.Category.ParentCategoryId,
                                        name = a.Product.Category.ParentCategory.Name,
                                        parent = a.Product.Category.ParentCategoryId == null ? null : new
                                        {
                                            id = a.Product.Category.ParentCategory.ParentCategoryId,
                                            name = a.Product.Category.ParentCategory.ParentCategory.Name,
                                        }
                                    }
                                },
                                photoUrl = a.Product.PhotoId != null ? a.Product.Photo.SmallImgUrl : a.Product.ProductPhotos.FirstOrDefault().Photo.SmallImgUrl,
                                manufacture = a.Product.Manufacturer.Name,
                            }
                        })
                        .AsNoTracking()
                        .ToListAsync();

            return new
            {
                count = countOfData,
                data = products
            };
        }

        public async Task<object> UserManufacturersListAsync(IUnitOfWork unitOfWork, string userId, int offset, int count)
        {
            var query = unitOfWork.UserBIMsmithManufacturerRepository.GetAll()
                    .Where(a => a.AddedById == userId);

            var countOfData = await query.CountAsync();

            var items = await query
                        .OrderBy(a => a.Id)
                        .Skip(offset)
                        .Take(count)
                        .Select(a => new
                        {
                            a = a.Id,
                            userId = a.AddedById,
                            manufacturerId = a.ManufacturerId,
                            manufacturer = new
                            {
                                name = a.Manufacturer.Name,
                                logo = new
                                {
                                    id = a.Manufacturer.PhotoId,
                                    small = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.SmallImgUrl : null,
                                    big = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.OriginalImgUrl : null,
                                }
                            }
                        })
                        .AsNoTracking()
                        .ToListAsync();

            return new
            {
                count = countOfData,
                data = items
            };
        }

        public async Task RemoveProductFromMyBIMsmithAsync(IUnitOfWork unitOfWork, string ownerId, int productId, string streamId)
        {
            var userBIMsmithProduct = await unitOfWork.UserBIMsmithProductRepository.GetAll()
                    .FirstOrDefaultAsync(a => a.ProductId == productId && a.AddedById == ownerId && a.StreamId == streamId);

            if (userBIMsmithProduct != null)
            {
                unitOfWork.UserBIMsmithProductRepository.Delete(userBIMsmithProduct);
                await unitOfWork.SaveAsync();
            }
            else
            {
                throw new DbItemNotFoundException("Not found the Product in user MyBIMsmith list");
            }
        }

        public async Task RemoveManufacturerFromMyBIMsmithAsync(IUnitOfWork unitOfWork, string ownerId, int manufacturerId)
        {
            var userBIMsmithManufacturer = await unitOfWork.UserBIMsmithManufacturerRepository.GetAll()
                    .FirstOrDefaultAsync(a => a.ManufacturerId == manufacturerId && a.AddedById == ownerId);

            if (userBIMsmithManufacturer != null)
            {
                unitOfWork.UserBIMsmithManufacturerRepository.Delete(userBIMsmithManufacturer);
                await unitOfWork.SaveAsync();
            }
            else
            {
                throw new DbItemNotFoundException("Not found the Manufacturer in user MyBIMsmith list");
            }
        }

        public async Task CopyProductToMyBIMsmith(IUnitOfWork unitOfWork, string ownerId, int productId, string fromStreamId, string toStreamId)
        {
            var userBIMsmithProduct = await unitOfWork.UserBIMsmithProductRepository.GetAll()
                    .FirstOrDefaultAsync(a => a.ProductId == productId && a.AddedById == ownerId && a.StreamId == fromStreamId);

            if (userBIMsmithProduct != null)
            {
                UserBIMsmithProduct copyBIMsmithProduct = new UserBIMsmithProduct();
                copyBIMsmithProduct.ProductId = productId;
                copyBIMsmithProduct.StreamId = toStreamId;
                copyBIMsmithProduct.AddedById = userBIMsmithProduct.AddedById;
                copyBIMsmithProduct.AddedDate = DateTime.UtcNow;

                unitOfWork.UserBIMsmithProductRepository.Insert(copyBIMsmithProduct);
                await unitOfWork.SaveAsync();
            }
            else
            {
                throw new DbItemNotFoundException("Not found the Product in user MyBIMsmith list");
            }
        }

        public async Task<object> ClearUnusedFiles(IUnitOfWork unitOfWork, string photosFolder, string filesFolder)
        {
            unitOfWork.BeginTransaction();

            List<string> errors = new List<string>();

            //Delete Notused Photos 
            var allPhotoIds = await unitOfWork.PhotoRepository.GetAll().Select(a => a.Id).ToListAsync();

            var productPhotoPhotoIds = unitOfWork.ProductPhotoRepository.GetAll().Where(a => allPhotoIds.Contains(a.PhotoId)).Select(a => a.PhotoId);
            var productPhotoIds = unitOfWork.ProductRepository.GetAll().Where(a => a.PhotoId != null && allPhotoIds.Contains(a.PhotoId.Value)).Select(a => a.PhotoId.Value);
            var manufacturerPhotoIds = unitOfWork.ManufacturerRepository.GetAll().Where(a => a.PhotoId != null && allPhotoIds.Contains(a.PhotoId.Value)).Select(a => a.PhotoId.Value);
            var manufacturerImagePhotoIds = unitOfWork.ManufacturerRepository.GetAll().Where(a => a.ImageId != null && allPhotoIds.Contains(a.ImageId.Value)).Select(a => a.ImageId.Value);
            var manufacturerOriginalImagePhotoIds = unitOfWork.ManufacturerRepository.GetAll().Where(a => a.OriginalImageId != null && allPhotoIds.Contains(a.OriginalImageId.Value)).Select(a => a.OriginalImageId.Value);
            var usedPhotoIds = await productPhotoPhotoIds.Concat(productPhotoIds).Concat(manufacturerPhotoIds).Concat(manufacturerImagePhotoIds).Concat(manufacturerOriginalImagePhotoIds).Distinct().ToListAsync();

            var notusedPhotoIds = allPhotoIds.Except(usedPhotoIds).ToList();
            var photoToDelete = await unitOfWork.PhotoRepository.GetAll().Where(a => notusedPhotoIds.Contains(a.Id)).ToListAsync();

            foreach (var photo in photoToDelete)
            {
                try
                {
                    unitOfWork.PhotoRepository.Delete(photo);
                    await unitOfWork.SaveAsync();

                    string pathB = Path.Combine(photosFolder, string.Format("{0}_b.png", photo.Id));
                    if (System.IO.File.Exists(pathB))
                    {
                        System.IO.File.Delete(pathB);
                    }

                    string pathS = Path.Combine(photosFolder, string.Format("{0}_s.png", photo.Id));
                    if (System.IO.File.Exists(pathS))
                    {
                        System.IO.File.Delete(pathS);
                    }

                    string pathSJPG = Path.Combine(photosFolder, string.Format("{0}_s.jpg", photo.Id));
                    if (System.IO.File.Exists(pathSJPG))
                    {
                        System.IO.File.Delete(pathSJPG);
                    }

                    string pathBJPG = Path.Combine(photosFolder, string.Format("{0}_b.jpg", photo.Id));
                    if (System.IO.File.Exists(pathBJPG))
                    {
                        System.IO.File.Delete(pathBJPG);
                    }

                }
                catch (Exception e)
                {
                    errors.Add("Delete Photo: " + photo.Id.ToString() + "" + e.Message);
                }
            }

            //Delete not used files
            var allFileIds = await unitOfWork.FileRepository.GetAll().Select(a => a.Id).ToListAsync();
            var productFileIds = await unitOfWork.ProductFileRepository.GetAll().Where(a => allFileIds.Contains(a.FileId)).Select(a => a.FileId).ToListAsync();
            var usedFileIds = productFileIds;

            var notusedFileIds = allFileIds.Except(usedFileIds).ToList();
            var filesToDelete = await unitOfWork.FileRepository.GetAll().Where(a => notusedFileIds.Contains(a.Id)).ToListAsync();

            foreach (var file in filesToDelete)
            {
                try
                {
                    unitOfWork.FileRepository.Delete(file);
                    await unitOfWork.SaveAsync();

                    string pathPreview = Path.Combine(filesFolder, file.Id.ToString() + "_p.png");

                    string path = Path.Combine(filesFolder, file.Id.ToString() + System.IO.Path.GetExtension(file.FileName));

                    if (System.IO.File.Exists(pathPreview))
                    {
                        System.IO.File.Delete(pathPreview);
                    }

                    if (System.IO.File.Exists(path))
                    {
                        System.IO.File.Delete(path);
                    }

                }
                catch (Exception e)
                {
                    errors.Add("Delete File: " + file.Id.ToString() + "" + e.Message);
                }
            }

            unitOfWork.CommitTransaction();

            return new
            {
                countOfDeletedFiles = notusedFileIds.Count,
                countOfDeletedPhotos = notusedPhotoIds.Count,
                errors = errors
            };
        }

        public async Task<object> GeneratePreviewForPdfAndTxtFilesAsync(IUnitOfWork unitOfWork, string filesFolder, bool pdf, bool image, bool txt, int offset, int count)
        {
            List<string> errors = new List<string>();
            int countOfNewPreview = 0;

            var query = unitOfWork.FileRepository.GetAll();

            if (pdf)
            {
                query = query.Where(a => a.MediaType == "application/pdf");
            }
            if (txt)
            {
                query = query.Where(a => a.MediaType == "text/plain");
            }
            if (image)
            {
                query = query.Where(a => a.MediaType.Contains("image"));
            }

            var countOfData = await query.CountAsync();

            var files = await query
                .OrderBy(a => a.Id)
                .Skip(offset)
                .Take(count)
                .ToListAsync();

            foreach (var file in files)
            {
                try
                {
                    string filePath = Path.Combine(filesFolder, file.Id.ToString() + Path.GetExtension(file.FileName));

                    if (System.IO.File.Exists(filePath))
                    {
                        var thumbnail = ThumbnailProvider.GetAndSaveThumbnail(filePath, file.MediaType, file.Id);
                        if (thumbnail.HasError == false)
                        {
                            countOfNewPreview++;
                        }
                        else
                        {
                            errors.Add("Error Create Id:" + file.Id.ToString() + " " + thumbnail.Error);
                        }
                    }

                }
                catch (Exception e)
                {
                    errors.Add("Error: " + file.Id.ToString() + "" + e.Message);
                }
            }

            return new
            {
                countOfFiles = countOfData,
                countOfNewPreview = countOfNewPreview,
                errors = errors
            };
        }

        public async Task ChangeFilesUrlAsync(IUnitOfWork unitOfWork, List<Domain.DBModels.File> httpLinkFiles, string http, string https, int takeCount, bool needContinue)
        {
            httpLinkFiles = await unitOfWork.FileRepository.GetAll()
                                       .Where(f => f.Url.StartsWith(http) || f.PreviewUrl.StartsWith(http))
                                       .Take(takeCount).ToListAsync();

            if (httpLinkFiles.Count() > 0)
            {
                foreach (var file in httpLinkFiles)
                {
                    file.Url = file.Url.Replace(http, https);
                    file.PreviewUrl = file.PreviewUrl.Replace(http, https);

                    unitOfWork.FileRepository.Edit(file);
                }
                await unitOfWork.SaveAsync();
                httpLinkFiles.Clear();
            }
            else
            {
                needContinue = false;
            }
        }
    }
}
