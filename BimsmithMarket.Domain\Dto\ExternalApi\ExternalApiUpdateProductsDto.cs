﻿using BIMsmithMarket.Domain.CustomAttributes;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Dto.ExternalApi
{
    public class ExternalApiUpdateProductsDto
    {
        [Required]
        public string AccessToken { get; set; }

        [Required]
        public ExternalApiProductExternalDto[] Products { get; set; }
    }

    public class ExternalApiProductExternalDto
    {
        [RequiredNotDefault]
        public int Id { get; set; }

        [Required]
        public List<ExternalApiProductAttachmentDto> Attachments { get; set; }

        [Required]
        public List<ExternalApiProductContentFileDto> ContentFiles { get; set; }

        [Required]
        public List<ExternalApiProductPhotoDto> Photos { get; set; }
    }

    public class ExternalApiProductInternalDto : ExternalApiProductExternalDto
    {
        public int ManufacturerId { get; set; }

        public string UserId { get; set; }
    }

    public class ExternalApiProductAttachmentDto
    {
        [Required]
        public string Url { get; set; }

        [Required]
        public string Type { get; set; }
    }

    public class ExternalApiProductContentFileDto
    {
        [Required]
        public string Url { get; set; }

        [Required]
        public string Type { get; set; }
    }

    public class ExternalApiProductPhotoDto
    {
        [Required]
        public string Url { get; set; }
    }
}