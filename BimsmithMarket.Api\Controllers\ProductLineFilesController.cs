﻿using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Security.Claims;
using System.Threading.Tasks;
using static BIMsmithMarket.Services.ProductLineFilesService;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("api/[controller]/[action]")]
    public class ProductLineFilesController : BaseApiController
    {
        private readonly IProductLineFilesService _productLineFilesService;
        public ProductLineFilesController(IProductLineFilesService productLineFilesService)
        {
            _productLineFilesService = productLineFilesService;
        }

        /// <summary>
        /// Set weight for productLines and Products
        /// </summary>
        /// <param name="model">The model.</param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        [ActionName("SetWeight")]
        public async Task<IActionResult> SetWeight(WeightSetModel model)
        {
#if !DEBUG
            var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            if (userId == null)
            {
                return BadRequest("Invalid user authorization");
            }
#endif
            var editTime = DateTime.UtcNow;
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _productLineFilesService.SetWeightAsync(unitOfWork, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), model));
            }
        }
    }
}
