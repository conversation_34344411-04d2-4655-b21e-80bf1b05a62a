﻿using BIMsmithMarket.Domain.Dto.PaymentsDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IPaymentPlanService
    {
        Task<PaymentPlanDto> CreateAsync(PaymentPlanDto model, IUnitOfWork unitOfWork);

        Task<PaymentPlanDto> UpdateAsync(PaymentPlanDto model, IUnitOfWork unitOfWork);

        Task DeleteAsync(int id, IUnitOfWork unitOfWork);

        Task<PaymentPlanDto> GetAsync(int id, IUnitOfWork unitOfWork);

        Task<List<PaymentPlanDto>> ListAsync(IUnitOfWork unitOfWork);
    }
}