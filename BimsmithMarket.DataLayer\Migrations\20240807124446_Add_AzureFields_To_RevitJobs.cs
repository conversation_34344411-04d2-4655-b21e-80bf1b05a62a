﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BIMsmithMarket.DataLayer.Migrations
{
    /// <inheritdoc />
    public partial class Add_AzureFields_To_RevitJobs : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "AzureQueueBaseName",
                table: "RevitJobs",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AzureQueueMessageId",
                table: "RevitJobs",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AzureQueuePopReceipt",
                table: "RevitJobs",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AzureQueueBaseName",
                table: "RevitJobs");

            migrationBuilder.DropColumn(
                name: "AzureQueueMessageId",
                table: "RevitJobs");

            migrationBuilder.DropColumn(
                name: "AzureQueuePopReceipt",
                table: "RevitJobs");
        }
    }
}
