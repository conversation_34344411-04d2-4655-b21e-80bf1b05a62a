﻿using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IProductQualityItemService
    {
        Task<object> ListAsync(IUnitOfWork unitOfWork);
        Task<object> GetAsync(IUnitOfWork unitOfWork, int id);
        Task<object> AddAsync(IUnitOfWork unitOfWork, string userId, AddProductQualityModel model);
        Task EditAsync(IUnitOfWork unitOfWork, string userId, EditProductQualityModel model);
        Task DeleteAsync(IUnitOfWork unitOfWork, int id);
    }
}
