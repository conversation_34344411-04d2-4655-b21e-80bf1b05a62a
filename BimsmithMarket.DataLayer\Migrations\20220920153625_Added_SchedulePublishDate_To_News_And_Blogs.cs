﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BIMsmithMarket.DataLayer.Migrations
{
    public partial class Added_SchedulePublishDate_To_News_And_Blogs : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "SchedulePublishDate",
                table: "News",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "SchedulePublishDate",
                table: "BlogPosts",
                type: "datetime2",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "SchedulePublishDate",
                table: "News");

            migrationBuilder.DropColumn(
                name: "SchedulePublishDate",
                table: "BlogPosts");
        }
    }
}
