﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Dto.AnnouncementDto
{
    public class AddAnnouncementDto
    {
        [Required]
        public string Text { get; set; }

        public string IconUrl { get; set; }

        [Required]
        public bool IsActive { get; set; }

        public string Link { get; set; }

        public string LinkCaption { get; set; }

        [Required]
        public bool IsGlobal { get; set; }

        [Required]
        public bool ShowLink { get; set; }

        [Required]
        public bool OverrideColor { get; set; }

        public string BannerColor { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        public ICollection<int> ManufacturerIds { get; set; }
    }

    public class EditAnnouncementDto : AddAnnouncementDto
    {
        [Required]
        public int Id { get; set; }
    }

    public class GetAnnouncementDto : EditAnnouncementDto
    {

    }
}