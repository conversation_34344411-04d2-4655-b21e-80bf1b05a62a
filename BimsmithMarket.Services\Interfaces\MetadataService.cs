﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using Flurl;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;
using Newtonsoft.Json;
using System.Diagnostics;

namespace BIMsmithMarket.Services.Interfaces
{
    public class MetadataService : IMetadataService
    {
        private readonly IHttpClientFactory _httpClientFactory;

        public MetadataService(IHttpClientFactory httpClientFactory)
        {
            _httpClientFactory = httpClientFactory;
        }

        public async Task<MetadataModel> GetMetadataForCurrentRouteAsync(string requestUrl, IMasterformatService masterformatService, IUnitOfWork unitOfWork)
        {
            MetadataModel result = new MetadataModel();
            result.Title = null;
            
            if (string.IsNullOrEmpty(requestUrl) || requestUrl == "/")
            {
                return result;
            }

            string requestLowerUrl = requestUrl.ToLower();

            if (requestLowerUrl.Contains("/details"))
            {
                result.Keywords = string.Empty;
                result.Title = "Details – Download Free Construction Details & Architectural Details – BIMsmith Market";
                result.Description = "Download Free Construction Details on BIMsmith Market. Browse Architectural Details by Scale, Application, Orientation, and Product, then Download as PDF, Revit, AutoCAD, and more with BIMsmith.";
                result.OgImageUrl = new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment("/assets/img/DetailShelfOG.png");
                result.CanonicalUrl = GetDefaultCanonicalUrl(requestUrl);
                return result;
            }

            if (requestLowerUrl.Contains("/revit-materials"))
            {
                result.Title = "Revit Materials – Download Revit Material Libraries – BIMsmith Market";
                result.Description = "Download Free Revit Material Libraries on BIMsmith. Find Revit Materials for Paint, Flooring, Roofing, Ceilings, Decking, Coatings, and More. Download Now for Free.";
                result.Keywords = "Revit materials, Revit Material Library, Materials in Revit, Material Libraries, material download, free Revit materials, download Revit materials, .ADSKLIB, Autodesk library file, Revit, BIM";
                result.OgImageUrl = new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment("/assets/img/BIMsmith-og-Future-Building-Product-Data-600.png");
                result.CanonicalUrl = GetDefaultCanonicalUrl(requestUrl);
                return result;
            }

			if (requestLowerUrl.Contains("/masterformats"))
			{
				var masterformatSearch = HttpUtility.UrlDecode(requestLowerUrl.Split('?').First().Split("/masterformats/").Last());
			    var masterformats = await masterformatService.GetBackofficeMasterformatsAsync();
			    var masterformat = masterformats.FirstOrDefault(w => masterformatSearch.ToLower() == w.Title.ToLower() || masterformatSearch.Replace('-', ' ').ToLower() == w.Code.ToLower());
			    if (masterformat != null)
			    {
			    	result.Title = $"{masterformat.Code} - {masterformat.Title} - BIMsmith Market";
			    	result.Description = $"Download specifications and BIM content for {masterformat.Code} - {masterformat.Title} on BIMsmith. Research {masterformat.Title} products from Section {masterformat.Code} now for free.";
			    }
				result.Keywords = "Revit materials, Revit Material Library, Materials in Revit, Material Libraries, material download, free Revit materials, download Revit materials, .ADSKLIB, Autodesk library file, Revit, BIM";
				result.OgImageUrl = new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment("/assets/img/BIMsmith-og-Future-Building-Product-Data-600.png");
                result.CanonicalUrl = GetDefaultCanonicalUrl(requestUrl);
                return result;
			}

			// Try find Meta tags in manufacturer, product, category
			//------------------------------------------------------
			const int maxDescriptionLenght = 160;
            const string manufacturerStr = "manufacturer";
            const string productStr = "product";
            const string categoryStr = "category";

            int entityId = 0;
            string nameOrVanity = null;
            string parameter = string.Empty;

            requestLowerUrl = requestLowerUrl.Replace("https://", "");
            requestLowerUrl = requestLowerUrl.Replace("http://", "");

            var queryParts = requestLowerUrl.Split('/');

            if (queryParts.Length == 2) //Try get vanity top level
            {
                nameOrVanity = HttpUtility.UrlDecode(queryParts[1].Split('?').First());
            }
            else if (queryParts.Length == 3) //Try get vanity or id or name in second level
            {
                nameOrVanity = HttpUtility.UrlDecode(queryParts[2].Split('?').First());
                parameter = queryParts[1];
            }
            else if (queryParts.Contains("node") && queryParts.Length == 4) //Try get vanity or id or name in second level
            {
                nameOrVanity = HttpUtility.UrlDecode(queryParts[3].Split('?').First());
                parameter = queryParts[1];
            }
            else if (queryParts.Contains("product") && queryParts.Length == 4) //for new product route 'marketdev.qarea.org/product/Haworth-Inc/revit-bim-TecCrete--Pedestal--Low-Profile-2479'
            {
                nameOrVanity =
                    HttpUtility.UrlDecode(queryParts[3].Split('?').First().Split('-').Last()); //Get Product Id
                parameter = queryParts[1];
            }

            int.TryParse(nameOrVanity, out entityId);

            Manufacturer manufacturer = null;
            Product product = null;
            Category category = null;

            if (!string.IsNullOrWhiteSpace(nameOrVanity))
            {
                if (parameter == string.Empty) //try find entity with the same vanity url
                {
                    manufacturer = await unitOfWork.ManufacturerRepository.GetAll()
                        .FirstOrDefaultAsync(m => m.HubVanityURL == nameOrVanity || m.Name == nameOrVanity);

                    if (manufacturer == null)
                    {
                        var currentVanity = unitOfWork.VanityHistoryRepository.GetAll()
                            .AsNoTracking()
                            .Include(a => a.Children).ThenInclude(b => b.Children).ThenInclude(c => c.Children)
                            .Where(a => a.EntityType == manufacturerStr && a.VanityUrl == nameOrVanity)
                            .AsEnumerable()
                            .SelectMany(a => a.Children.Concat(a.Children.SelectMany(b => b.Children)).Concat(a.Children.SelectMany(b => b.Children.SelectMany(c => c.Children))))
                            .OrderByDescending(a => a.Id)
                            .Select(a => a.VanityUrl)
                            .FirstOrDefault();

                        if (currentVanity != null)
                        {
                            result.VanityRedirect = currentVanity;
                            result.OldVanity = nameOrVanity;
                        }
                    }

                    product = await unitOfWork.ProductRepository.GetAll().FirstOrDefaultAsync(m => m.VanityURL == nameOrVanity);
                    category = await unitOfWork.CategoryRepository.GetAll().FirstOrDefaultAsync(m => m.VanityUrl == nameOrVanity);

                    if (category == null)
                    {
                        var currentVanity = unitOfWork.VanityHistoryRepository.GetAll()
                            .AsNoTracking()
                            .Include(a => a.Children).ThenInclude(b => b.Children).ThenInclude(c => c.Children)
                            .Where(a => a.EntityType == categoryStr && a.VanityUrl == nameOrVanity)
                            .AsEnumerable()
                            .SelectMany(a => a.Children.Concat(a.Children.SelectMany(b => b.Children)).Concat(a.Children.SelectMany(b => b.Children.SelectMany(c => c.Children))))
                            .OrderByDescending(a => a.Id)
                            .Select(a => a.VanityUrl)
                            .FirstOrDefault();

                        if (currentVanity != null)
                        {
                            result.VanityRedirect = currentVanity;
                            result.OldVanity = nameOrVanity;
                        }
                        else
                        {
                            var httpClient = _httpClientFactory.CreateClient();
                            {
                                Url url = new Url(ConfigurationHelper.GetValue("CBOBaseAddress"))
                                    .AppendPathSegment("api/Certificate/List");
                                using var certificatesResult = await httpClient.GetAsync(url);
                                if (certificatesResult.IsSuccessStatusCode)
                                {
                                    var certificates = JsonConvert.DeserializeObject<List<CertificateModel>>(await certificatesResult.Content.ReadAsStringAsync());
                                    var certificate = certificates.FirstOrDefault(w => w.VanityUrl.ToLower() == nameOrVanity.ToLower());
                                    if (certificate != null)
                                    {
                                        result.Title = certificate.MetaTitle;
                                        result.Description = certificate.MetaDescription;
                                    }
                                }
                            }
                        }
                    }
                }
                else if (parameter.Contains(manufacturerStr))
                {
                    manufacturer = await unitOfWork.ManufacturerRepository.GetAll().Where(m =>
                            m.Id == entityId || m.HubVanityURL == nameOrVanity || m.Name == nameOrVanity)
                        .FirstOrDefaultAsync();

                    if (manufacturer == null)
                    {
                        var currentVanity = unitOfWork.VanityHistoryRepository.GetAll()
                            .AsNoTracking()
                            .Include(a => a.Children).ThenInclude(b => b.Children).ThenInclude(c => c.Children)
                            .Where(a => a.EntityType == manufacturerStr && a.VanityUrl == nameOrVanity)
                            .AsEnumerable()
                            .SelectMany(a => a.Children.Concat(a.Children.SelectMany(b => b.Children)).Concat(a.Children.SelectMany(b => b.Children.SelectMany(c => c.Children))))
                            .OrderByDescending(a => a.Id)
                            .Select(a => a.VanityUrl)
                            .FirstOrDefault();

                        if (currentVanity != null)
                        {
                            result.VanityRedirect = currentVanity;
                            result.OldVanity = nameOrVanity;
                        }
                    }
                }
                else if (parameter.Contains(productStr))
                {
                    product = await unitOfWork.ProductRepository.GetAll()
                        .FirstOrDefaultAsync(m => m.Id == entityId || m.VanityURL == nameOrVanity);
                }
                else if (parameter.Contains(categoryStr))
                {
                    category = await unitOfWork.CategoryRepository.GetAll()
                        .FirstOrDefaultAsync(m => m.Id == entityId || m.VanityUrl == nameOrVanity);

                    if (category == null)
                    {
                        var currentVanity = unitOfWork.VanityHistoryRepository.GetAll()
                            .AsNoTracking()
                            .Include(a => a.Children).ThenInclude(b => b.Children).ThenInclude(c => c.Children)
                            .Where(a => a.EntityType == categoryStr && a.VanityUrl == nameOrVanity)
                            .AsEnumerable()
                            .SelectMany(a => a.Children.Concat(a.Children.SelectMany(b => b.Children)).Concat(a.Children.SelectMany(b => b.Children.SelectMany(c => c.Children))))
                            .OrderByDescending(a => a.Id)
                            .Select(a => a.VanityUrl)
                            .FirstOrDefault();

                        if (currentVanity != null)
                        {
                            result.VanityRedirect = currentVanity;
                            result.OldVanity = nameOrVanity;
                        }
                    }
                }
            }
            else
            {
                var queryParameters = HttpUtility.ParseQueryString(queryParts.Last().Split('?').Last());
                if (queryParameters.AllKeys.Contains("q") || queryParameters.AllKeys.Contains("search"))
                {
                    var search = queryParameters["q"];
                    if (!string.IsNullOrWhiteSpace(search))
                    {
                        manufacturer = await unitOfWork.ManufacturerRepository.GetAll().FirstOrDefaultAsync(m => m.HubVanityURL.StartsWith(search) || m.Name.StartsWith(search));
                        if (manufacturer == null)
                        {
                            category = await unitOfWork.CategoryRepository.GetAll().FirstOrDefaultAsync(m => m.Name.StartsWith(search) || m.VanityUrl.StartsWith(search));
                        }

                        if (manufacturer == null && category == null)
                        {
                            product = await unitOfWork.ProductRepository.GetAll().FirstOrDefaultAsync(m => m.Name.StartsWith(search) || m.VanityURL.StartsWith(search));
                        }
                    }
                }
            }

            if (category != null)
            {
                result.Title = category.MetaTitle ?? string.Format("{0} - {1}", result.Title, category.Name);

                if (!string.IsNullOrEmpty(category.MetaDescription))
                {
                    result.Description = category.MetaDescription;
                }
                else
                {
                    result.Description = category.Name;
                }

                if (!string.IsNullOrEmpty(category.MetaKeywords))
                {
                    result.Keywords = category.MetaKeywords;
                }
                else if (!string.IsNullOrEmpty(category.Keywords))
                {
                    result.Keywords = category.Keywords;
                }
                else
                {
                    result.Keywords = result.Title;
                }

                //Canonical url for duplicated pages
                result.CanonicalUrl = GetDefaultCanonicalUrl(requestUrl);
            }

            if (manufacturer != null)
            {
                // Get title
                result.Title = manufacturer.MetaTitle ??
                               string.Format("{0} - {1}", result.Title, manufacturer.Name);
                // Get Description
                if (!string.IsNullOrEmpty(manufacturer.MetaDescription))
                {
                    result.Description = manufacturer.MetaDescription;
                }
                else if (!string.IsNullOrEmpty(manufacturer.Description))
                {
                    if (manufacturer.Description.Length > maxDescriptionLenght)
                    {
                        result.Description = manufacturer.Description.Substring(0, maxDescriptionLenght);
                    }
                    else
                    {
                        result.Description = manufacturer.Description;
                    }
                }

                // Get Keywords
                if (!string.IsNullOrEmpty(manufacturer.MetaKeywords))
                {
                    result.Keywords = manufacturer.MetaKeywords;
                }
                else if (!string.IsNullOrEmpty(manufacturer.Keywords))
                {
                    result.Keywords = manufacturer.Keywords;
                }
                else
                {
                    result.Keywords = result.Title;
                }

                //Open Image
                result.OgImageUrl = manufacturer.Image?.OriginalImgUrl;

                //Canonical url for duplicated pages
                result.CanonicalUrl = GetDefaultCanonicalUrl(requestUrl);
            }

            if (product != null)
            {
                // Get title
                result.Title = product.MetaTitle ??
                               $"Free Revit Download - {product.Manufacturer.Name} {product.Name}";
                // Get Description
                if (!string.IsNullOrEmpty(product.MetaDescription))
                {
                    result.Description = product.MetaDescription;
                }
                else if (!string.IsNullOrEmpty(product.Description))
                {
                    if (product.Description.Length > maxDescriptionLenght)
                    {
                        result.Description = product.Description.Substring(0, maxDescriptionLenght);
                    }
                    else
                    {
                        result.Description = product.Description;
                    }
                }

                // Get Keywords
                if (!string.IsNullOrEmpty(product.MetaKeywords))
                {
                    result.Keywords = product.MetaKeywords;
                }
                else if (!string.IsNullOrEmpty(product.Keywords))
                {
                    result.Keywords = product.Keywords;
                }
                else
                {
                    result.Keywords = result.Title;
                }

                //Open Image
                result.OgImageUrl = product.Photo?.OriginalImgUrl;

                //Canonical url for duplicated pages
                result.CanonicalUrl = GetProductCanonicalUrl(product);
            }

            if (!string.IsNullOrEmpty(result.Title))
            {
                return result;
            }

            // Check by projectType
            //---------------------------
            var projectType = await unitOfWork.ProjectDataTypeRepository.GetAll().FirstOrDefaultAsync(x => requestLowerUrl.Contains(x.VanityUrl));
            if (projectType != null)
            {
                result.Title = projectType.MetaTitle;
                result.Description = projectType.MetaDescription;
                result.Keywords = projectType.MetaKeyWords;
                result.OgImageUrl = projectType.Image?.OriginalImgUrl;
                result.CanonicalUrl = GetDefaultCanonicalUrl(requestUrl);
            }
            return result;
        }

        private string GetDefaultCanonicalUrl(string url)
        {
            if (string.IsNullOrWhiteSpace(url))
            {
                return null;
            }

            var path = url.Split('?').First();

            Url canonicalUrl = new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl"))
                    .AppendPathSegment(path);

            return canonicalUrl.ToString();
        }

        private string GetProductCanonicalUrl(Product product)
        {
            if (product is null)
            {
                return null;
            }

            string productPath = $"product/{StringHelper.FixUrlString(product.Manufacturer.Name)}/revit-bim-{StringHelper.FixUrlString(product.Name)}-{product.Id}";
            Url canonicalUrl = new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl"))
                .AppendPathSegment(productPath);

            return canonicalUrl.ToString();
        }
    }
}