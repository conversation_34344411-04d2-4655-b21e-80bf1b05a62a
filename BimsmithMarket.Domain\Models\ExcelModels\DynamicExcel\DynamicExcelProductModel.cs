﻿using BIMsmithMarket.Domain.Models.ExcelModels.StaticExcel;
using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Models.ExcelModels.DynamicExcel
{
    public class DynamicExcelProductModel
    {
        #region static fields
        public string Id { get; set; }

        public string Name { get; set; }

        public string ProductUrl { get; set; }

        public string ULUrl { get; set; }

        public string ManufacturerId { get; set; }

        public string CategoryId { get; set; }

        public string ProductCategoryIds { get; set; }

        public string ProductLineId { get; set; }

        public string PhotoId { get; set; }

        public string PhotoURLs { get; set; }

        public string Description { get; set; }

        public string VideoUrl { get; set; }

        public string VanityURL { get; set; }

        public string MetaTitle { get; set; }

        public string MetaDescription { get; set; }

        public string MetaKeywords { get; set; }

        public string Keywords { get; set; }

        public string PublishToPartner { get; set; }

        public string ExternalId { get; set; }

        public string ForgeWallURL { get; set; }

        public string ForgeFloorURL { get; set; }

        public string ForgeCeilingURL { get; set; }

        public string ForgeRoofURL { get; set; }

        public string ProductCertificates { get; set; }

        public string ProductCisfbs { get; set; }

        public string ProductExternalMasterformats { get; set; }

        public List<int> ProductExternalMasterformatIds { get; set; }

        public string ProductOmniclasses { get; set; }

        public string ProductUniclasses { get; set; }

        public string ProductUniformats { get; set; }

        public string ProductCutSheetUrls { get; set; }

        public string PartSpecUrls { get; set; }

        public string ProductBrochureUrls { get; set; }

        public string ImageUrls { get; set; }

        public string SubmittalUrls { get; set; }

        public string CatalogUrls { get; set; }

        public string WarrantyUrls { get; set; }

        public string TestingDataUrls { get; set; }

        public string SafetyDataSheetUrls { get; set; }

        public string InstallationGuideUrls { get; set; }

        public string HpdUrls { get; set; }

        public string MasterPartSpecUrls { get; set; }

        public string Published { get; set; }

        public string Staging { get; set; }

        public string RevitFiles { get; set; }

        public string Weight { get; set; }

        public string RegionIds { get; set; }

        public string EpdUrls { get; set; }

        public string AttachmentFileNames { get; set; }

        public string ProjectFileNames { get; set; }

        public string PublishedOnCustomMicrosite { get; set; }
        #endregion

        #region dynamic fields
        public List<ProductFileModel> ProjectFiles { get; set; }

        public List<string> ProductQualityItems { get; set; }

        public List<ProductProductStatModel> ProductStats { get; set; }
        #endregion
    }
}