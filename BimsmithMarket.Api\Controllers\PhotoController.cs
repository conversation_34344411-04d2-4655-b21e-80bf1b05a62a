﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using System.IO;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// Photo Controller
    /// </summary>
    [Route("api/[controller]/[action]")]
    public class PhotoController : BaseApiController
    {
        private readonly IWebHostEnvironment _webHostEnvironment;

        public PhotoController(IWebHostEnvironment webHostEnvironment)
        {
            _webHostEnvironment = webHostEnvironment;
        }

        /// <summary>
        /// Get photo by id and size
        /// </summary>
        /// <param name="id"></param>
        /// <param name="s"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult Get(int id, string s = "s")
        {
            string folder = Path.Combine(_webHostEnvironment.WebRootPath, "Photos");
            string path = Path.Combine(folder, string.Format("{0}_{1}.jpg", id, s));

            if (System.IO.File.Exists(path))
            {
                return PhysicalFile(path, "image/jpg", Path.GetFileName(path));
            }
            else
            {
                string pathPng = Path.Combine(folder, string.Format("{0}_{1}.png", id, s));
                if (System.IO.File.Exists(pathPng))
                {
                    return PhysicalFile(pathPng, "image/png", Path.GetFileName(pathPng));
                }
            }

            //Return default photo
            string contentFolder = Path.Combine(_webHostEnvironment.WebRootPath, "Content/img");
            string pathDef = Path.Combine(contentFolder, string.Format("Def_Photo_{0}.png", s));
            return PhysicalFile(pathDef, "image/png", Path.GetFileName(pathDef));
        }
    }
}