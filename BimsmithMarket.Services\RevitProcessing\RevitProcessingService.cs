﻿using Azure.Storage.Queues.Models;
using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.DBModels.RevitProcessing;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.RevitProcessing;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Enums.RevitProcessing;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using BIMsmithMarket.Services.Interfaces.RevitProcessing;
using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.RevitProcessing
{
    public class RevitProcessingService : IRevitProcessingService
    {
        private readonly IAzureStorageService _azureStorageService;
        private readonly IUploadFileService _uploadFileService;
        private readonly IProductService _productService;
        private readonly SlackWebHook _slackWebHook;

        public RevitProcessingService(
            IAzureStorageService azureStorageService,
            IUploadFileService uploadFileService,
            IProductService productService,
            SlackWebHook slackWebHook)
        {
            _azureStorageService = azureStorageService;
            _uploadFileService = uploadFileService;
            _productService = productService;
            _slackWebHook = slackWebHook;
        }

        public RevitProcessTypeDto[] RevitProcessTypeList()
        {
            return RevitProcessingConstants.RevitProcessTypes;
        }

        public MarketFieldDto[] MarketFieldList()
        {
            return RevitProcessingConstants.MarketFields;
        }

        public async Task<OperationResultDto> StartRevitProcessAsync(StartRevitProcessDto model, string userId, IUnitOfWork unitOfWork)
        {
            Log.Information($"[{RevitProcessingConstants.RevitProcessingCaption}] Revit Process creation started");

            ValidateRevitProcess(model);

            Log.Information($"[{RevitProcessingConstants.RevitProcessingCaption}] Revit Process passed validaton");

            unitOfWork.BeginTransaction();

            RevitProcess revitProcess = model.Adapt<RevitProcess>();
            revitProcess.Status = RevitProcessStatus.InProgress;
            revitProcess.CreatedById = userId;
            unitOfWork.RevitProcessRepository.Insert(revitProcess);

            await unitOfWork.SaveAsync();

            foreach (int productId in model.ProductIds)
            {
                RevitProcessProduct revitProcessProduct = new()
                {
                    ProductId = productId,
                    RevitProcessId = revitProcess.Id,
                    CreatedById = userId
                };

                unitOfWork.RevitProcessProductRepository.Insert(revitProcessProduct);
            }

            RevitProcessingProductFileDto[] productFiles = await GetRevitProductFilesAsync(model.ProductIds, model.Type, unitOfWork);

            if (model.Type == RevitProcessType.UpdateParameterValues)
            {
                await CreateUpdateParameterRevitProcessAsync(model, productFiles, revitProcess.Id, userId, unitOfWork);
            }
            else if (model.Type == RevitProcessType.UpdateRevitVersion)
            {
                await CreateUpdateVersionRevitProcessAsync(model, productFiles, revitProcess.Id, userId, unitOfWork);
            }

            await unitOfWork.SaveAsync();
            unitOfWork.CommitTransaction();

            Log.Information($"[{RevitProcessingConstants.RevitProcessingCaption}] Revit Process {revitProcess.Id} created");

            await PushRevitJobsToQueueAsync(revitProcess.Id, unitOfWork);

            Log.Information($"[{RevitProcessingConstants.RevitProcessingCaption}] Revit Process {revitProcess.Id} started");
            await _slackWebHook.SendRevitProcessingMessageAsync(await GetRevitProcessSlackNotificationMessageAsync(revitProcess.Id, unitOfWork));

            return new OperationResultDto
            {
                Status = OperationResultStatus.Succeed
            };
        }

        public async Task<PaginationListDto<RevitProcessListDto>> RevitProcessListAsync(IUnitOfWork unitOfWork, int? manufacturerId = null, int offset = 0, int count = 10)
        {
            IQueryable<RevitProcess> revitProcessQuery = unitOfWork.RevitProcessRepository.GetAll();

            if (manufacturerId.HasValue)
            {
                revitProcessQuery = revitProcessQuery.Where(x => x.ManufacturerId == manufacturerId);
            }

            int totalCount = await revitProcessQuery.CountAsync();
            RevitProcessListDto[] items = await revitProcessQuery
                .OrderByDescending(x => x.CreatedDate)
                .Skip(offset)
                .Take(count)
                .ProjectToType<RevitProcessListDto>()
                .ToArrayAsync();

            return new PaginationListDto<RevitProcessListDto>
            {
                Count = totalCount,
                Data = items
            };
        }

        public async Task<OperationResultDto> CancelRevitProcessAsync(int id, string userId, IUnitOfWork unitOfWork)
        {
            Log.Information($"[{RevitProcessingConstants.RevitProcessingCaption}] Revit Process {id} cancellation requested");

            bool revitProcessExists = await unitOfWork.RevitProcessRepository.GetAll().AnyAsync(x => x.Id == id);

            if (!revitProcessExists)
            {
                throw new InvalidInputException("Revit Process does not exist");
            }

            unitOfWork.BeginTransaction();

            await unitOfWork.RevitProcessRepository.GetAll()
                .Where(x => x.Id == id && x.Status == RevitProcessStatus.InProgress)
                .ExecuteUpdateAsync(x => x.SetProperty(p => p.Status, RevitProcessStatus.Cancelled)
                    .SetProperty(p => p.ModifiedById, userId)
                    .SetProperty(p => p.ModifiedDate, DateTime.UtcNow)
                    .SetProperty(p => p.FinishDate, DateTime.UtcNow));

            RevitJob[] revitJobs = await unitOfWork.RevitJobRepository.GetAll()
                .Where(x => x.RevitProcessId == id && x.Status == RevitJobStatus.InProgress)
                .ToArrayAsync();

            foreach (RevitJob revitJob in revitJobs)
            {
                revitJob.Status = RevitJobStatus.Cancelled;
                revitJob.ModifiedById = userId;
                revitJob.ModifiedDate = DateTime.UtcNow;
                revitJob.FinishDate = DateTime.UtcNow;

                try
                {
                    await _azureStorageService.DeleteMessageFromQueueAsync(revitJob.AzureQueueBaseName, revitJob.AzureQueueMessageId, revitJob.AzureQueuePopReceipt);
                }
                catch (Exception ex)
                {
                    Log.Information($"[{RevitProcessingConstants.RevitProcessingCaption}] Revit Job {revitJob.Id} queue message {revitJob.AzureQueueMessageId} could not be deleted. Exception: {ex.GetAllMessages()}");
                }
            }

            await unitOfWork.SaveAsync();
            unitOfWork.CommitTransaction();

            Log.Information($"[{RevitProcessingConstants.RevitProcessingCaption}] Revit Process {id} cancelled");
            await _slackWebHook.SendRevitProcessingMessageAsync(await GetRevitProcessSlackNotificationMessageAsync(id, unitOfWork));

            return new OperationResultDto
            {
                Status = OperationResultStatus.Succeed
            };
        }

        public async Task<byte[]> GenerateReportAsync(int id, IUnitOfWork unitOfWork)
        {
            bool revitProcessExists = await unitOfWork.RevitProcessRepository.GetAll().AnyAsync(x => x.Id == id);

            if (!revitProcessExists)
            {
                throw new InvalidInputException("Revit Process does not exist");
            }

            RevitJobDto[] revitJobs = await unitOfWork.RevitJobRepository.GetAll()
                .Where(x => x.RevitProcessId == id)
                .ProjectToType<RevitJobDto>()
                .ToArrayAsync();

            if (revitJobs.Length == 0)
            {
                return Array.Empty<byte>();
            }

            StringBuilder reportContent = new StringBuilder();

            int counter = 1;
            foreach (RevitJobDto revitJob in revitJobs)
            {
                if (counter > 1)
                {
                    reportContent.AppendLine();
                }

                reportContent.AppendLine($"File #{counter}");
                reportContent.AppendLine($"Revit Process Type: {revitJob.RevitProcessType}");
                reportContent.AppendLine($"FileName: {revitJob.FileName}");
                reportContent.AppendLine($"Revit Version: {revitJob.RevitVersion}");
                reportContent.AppendLine($"Job Created At: {revitJob.CreatedDate}");
                reportContent.AppendLine($"Status: {revitJob.Status}");
                reportContent.AppendLine("Report:");
                if (!string.IsNullOrWhiteSpace(revitJob.Report))
                {
                    reportContent.AppendLine(revitJob.Report);
                }
                reportContent.AppendLine($"Product File Id: {revitJob.ProductFileId}");
                reportContent.AppendLine($"File Id: {revitJob.FileId}");
                reportContent.AppendLine($"Product Id: {revitJob.ProductId}");
                reportContent.AppendLine($"Product Name: {revitJob.ProductName}");
                reportContent.AppendLine($"Manufacturer Id: {revitJob.ManufacturerId}");
                reportContent.AppendLine($"Manufacturer Name: {revitJob.ManufacturerName}");
                if (revitJob.RevitParameterMappings != null && revitJob.RevitParameterMappings.Any())
                {
                    reportContent.AppendLine("Revit parameters");
                    foreach (RevitJobRevitParameterMappingDto revitParameterMapping in revitJob.RevitParameterMappings)
                    {
                        reportContent.AppendLine($"Revit Parameter: {revitParameterMapping.RevitParameter} # Market Value: {revitParameterMapping.MarketValue}");
                    }
                }

                reportContent.AppendLine($"Job finished at: {revitJob.FinishDate}");

                counter++;
            }

            return Encoding.UTF8.GetBytes(reportContent.ToString());
        }

        public async Task<OperationResultDto> HandleRevitJobResultAsync(RevitJobResultDto model, IUnitOfWork unitOfWork)
        {
            Log.Information($"[{RevitProcessingConstants.RevitProcessingCaption}] Revit Job result processing requested. Model {JsonConvert.SerializeObject(model)}");

            RevitJob revitJob = await unitOfWork.RevitJobRepository.GetAll()
                .Where(x => x.Id == model.Id)
                .FirstOrDefaultAsync();

            if (revitJob == null)
            {
                throw new InvalidInputException("Revit Job does not exists");
            }

            if (revitJob.Status == RevitJobStatus.Cancelled)
            {
                throw new InvalidInputException("Revit Job was cancelled");
            }

            unitOfWork.BeginTransaction();

            model.Adapt(revitJob);
            revitJob.FinishDate = DateTime.UtcNow;

            if (IsRevitJobCompleted(model.Status))
            {
                string fileName = await unitOfWork.RevitJobRepository.GetAll()
                    .Where(x => x.Id == model.Id)
                    .Select(x => x.ProductFile.File.FileName)
                    .FirstOrDefaultAsync();
                await HandleJobSucceededResultAsync(model.File, fileName, revitJob, unitOfWork);
            }

            await unitOfWork.SaveAsync();
            unitOfWork.CommitTransaction();

            Log.Information($"[{RevitProcessingConstants.RevitProcessingCaption}] Revit Job result processing finished. Model {JsonConvert.SerializeObject(model)}");

            if (await IsRevitProcessFinishedAsync(revitJob.RevitProcessId, unitOfWork))
            {
                await unitOfWork.RevitProcessRepository.GetAll()
                    .Where(x => x.Id == revitJob.RevitProcessId)
                    .ExecuteUpdateAsync(x => x.SetProperty(p => p.Status, RevitProcessStatus.Done)
                        .SetProperty(p => p.FinishDate, DateTime.UtcNow));

                Log.Information($"[{RevitProcessingConstants.RevitProcessingCaption}] Revit Process {revitJob.RevitProcessId} finished");
                await _slackWebHook.SendRevitProcessingMessageAsync(await GetRevitProcessSlackNotificationMessageAsync(revitJob.RevitProcessId, unitOfWork));
            }

            if (IsRevitJobCompleted(model.Status))
            {
                int productId = await unitOfWork.ProductFileRepository.GetAll()
                    .Where(x => x.Id == revitJob.ProductFileId)
                    .Select(x => x.ProductId)
                    .FirstAsync();
                await _productService.SaveProductToMongoAsync(productId, true, unitOfWork);
            }

            return new OperationResultDto
            {
                Status = OperationResultStatus.Succeed
            };
        }

        public async Task<string[]> GetAssemblyCodesAsync(IUnitOfWork unitOfWork)
        {
            return await unitOfWork.ProductRepository.GetAll()
                .Select(x => x.AssemblyCode)
                .Distinct()
                .ToArrayAsync();
        }

        #region private methods
        private void ValidateRevitProcess(StartRevitProcessDto model)
        {
            if (model.Type == RevitProcessType.UpdateParameterValues && (model.RevitParametersMappings == null || !model.RevitParametersMappings.Any()))
            {
                throw new InvalidInputException("RevitParametersMappings are empty");
            }
            else if (model.Type == RevitProcessType.UpdateRevitVersion && (model.ProjectDataTypeIds == null || !model.ProjectDataTypeIds.Any()))
            {
                throw new InvalidInputException("ProjectDataTypeIds are empty");
            }
        }

        private async Task<RevitProcessingProductFileDto[]> GetRevitProductFilesAsync(int[] productIds, RevitProcessType revitProcessType, IUnitOfWork unitOfWork)
        {
            string[] fileExtenstions = GetRevitProcessFileExtensions(revitProcessType);
            IQueryable<ProductFile> productFileQuery = unitOfWork.ProductFileRepository.GetAll()
                .Where(x => !x.IsAttachment
                         && x.ProjectDataTypeId == RevitProcessingConstants.RevitProjectDataTypeId
                         && productIds.Contains(x.ProductId)
                         && fileExtenstions.Any(e => x.File.FileName.Contains(e)));

            RevitProcessingProductFileDto[] productFiles = await productFileQuery.ProjectToType<RevitProcessingProductFileDto>().ToArrayAsync();
            foreach (RevitProcessingProductFileDto productFile in productFiles)
            {
                productFile.RevitVersionNumber = !string.IsNullOrWhiteSpace(productFile.RevitVersionString) ? int.Parse(productFile.RevitVersionString) : 0;
            }

            return productFiles;
        }

        private async Task<string> GetRevitParameterMarketValueAsync(int productId, MarketField marketField, IUnitOfWork unitOfWork)
        {
            IQueryable<Product> productQuery = unitOfWork.ProductRepository.GetAll()
                .Where(x => x.Id == productId);

            Expression<Func<Product, string>> selection = (x) => string.Empty;

            switch (marketField)
            {
                case MarketField.ManufacturerName:
                    {
                        selection = (x => x.Manufacturer.Name ?? "BIMsmith"); break;
                    }
                case MarketField.ProductName:
                    {
                        selection = (x => x.Name ?? "Default Product"); break;
                    }
                case MarketField.ProductLineName:
                    {
                        selection = (x => x.ProductLine.Name ?? "Default Product Line"); break;
                    }
                case MarketField.ProductAssemblyCode:
                    {
                        selection = (x => x.AssemblyCode ?? "00:00:00"); break;
                    }
            }

            return await productQuery.Select(selection).FirstOrDefaultAsync();
        }

        private async Task<int> GetProjectDataTypeRevitNumberAsync(int projectDataTypeId, IUnitOfWork unitOfWork)
        {
            string projectDataTypeTitle = await unitOfWork.ProjectDataTypeRepository.GetAll()
                .Where(x => x.Id == projectDataTypeId)
                .Select(x => x.Title.Replace("Revit ", string.Empty))
                .FirstOrDefaultAsync();

            return !string.IsNullOrWhiteSpace(projectDataTypeTitle) ? int.Parse(projectDataTypeTitle) : 0;
        }

        private async Task CreateUpdateParameterRevitProcessAsync(StartRevitProcessDto model, RevitProcessingProductFileDto[] productFiles, int revitProcessId, string userId, IUnitOfWork unitOfWork)
        {
            bool anyFileToProcess = false;
            foreach (RevitProcessingProductFileDto productFile in productFiles)
            {
                if (productFile.RevitVersionNumber == 0 || productFile.RevitVersionNumber < RevitProcessingConstants.MinimumRevitVersion)
                {
                    Log.Information($"[{RevitProcessingConstants.RevitProcessingCaption}] Product File {productFile.ProductFileId} with Revit version {productFile.RevitVersionNumber} ({productFile.RevitVersionString}) for product {productFile.ProductId} skipped on Update Parameters Job Creation");
                    continue;
                }

                RevitJob revitJob = new()
                {
                    CreatedById = userId,
                    ProductFileId = productFile.ProductFileId,
                    ProjectDataTypeId = productFile.SoftwareVersionId.Value,
                    Status = RevitJobStatus.InProgress,
                    RevitProcessId = revitProcessId
                };

                unitOfWork.RevitJobRepository.Insert(revitJob);
                await unitOfWork.SaveAsync();

                foreach (RevitProcessRevitParameterMappingDto revitParametersMapping in model.RevitParametersMappings)
                {
                    RevitJobRevitParameterMapping revitJobRevitParameterMapping = new()
                    {
                        CreatedById = userId,
                        RevitParameter = revitParametersMapping.RevitParameter,
                        MarketValue = !string.IsNullOrWhiteSpace(revitParametersMapping.CustomValue) ? revitParametersMapping.CustomValue : await GetRevitParameterMarketValueAsync(productFile.ProductId, revitParametersMapping.MarketField.Value, unitOfWork),
                        RevitJobId = revitJob.Id
                    };

                    unitOfWork.RevitJobRevitParameterMappingRepository.Insert(revitJobRevitParameterMapping);
                }

                anyFileToProcess = true;
            }

            if (!anyFileToProcess)
            {
                throw new InvalidInputException("No files to process");
            }
        }

        private async Task CreateUpdateVersionRevitProcessAsync(StartRevitProcessDto model, RevitProcessingProductFileDto[] productFiles, int revitProcessId, string userId, IUnitOfWork unitOfWork)
        {
            bool anyFileToProcess = false;
            Dictionary<int, int> projectDataTypeRevitNumbers = [];
            var productFileGroupds = productFiles.GroupBy(x => new { x.ProductId, x.FileName }).ToArray();
            foreach (var productFileGroup in productFileGroupds)
            {
                RevitProcessingProductFileDto[] groupProductFiles = productFileGroup.OrderByDescending(x => x.RevitVersionNumber).ToArray();
                RevitProcessingProductFileDto baseProductFile = groupProductFiles.First();
                foreach (int projectDataTypeId in model.ProjectDataTypeIds)
                {
                    //If current file already has specific Revit version skip it
                    if (groupProductFiles.Any(x => x.SoftwareVersionId == projectDataTypeId && x.FileName == baseProductFile.FileName))
                    {
                        Log.Information($"[{RevitProcessingConstants.RevitProcessingCaption}] Product {baseProductFile.ProductId} already has {baseProductFile.FileName} with Project Data Type Id {projectDataTypeId}. Update version Job creation skipped");
                        continue;
                    }

                    if (!projectDataTypeRevitNumbers.ContainsKey(projectDataTypeId))
                    {
                        projectDataTypeRevitNumbers.Add(projectDataTypeId, await GetProjectDataTypeRevitNumberAsync(projectDataTypeId, unitOfWork));
                    }

                    RevitProcessingProductFileDto productFileWithNearestLowerRevitVersion = groupProductFiles.FirstOrDefault(x => x.RevitVersionNumber <= projectDataTypeRevitNumbers[projectDataTypeId]);

                    RevitJob revitJob = new()
                    {
                        CreatedById = userId,
                        ProductFileId = productFileWithNearestLowerRevitVersion.ProductFileId,
                        ProjectDataTypeId = projectDataTypeId,
                        Status = RevitJobStatus.InProgress,
                        RevitProcessId = revitProcessId
                    };

                    unitOfWork.RevitJobRepository.Insert(revitJob);

                    anyFileToProcess = true;
                }
            }

            if (!anyFileToProcess)
            {
                throw new InvalidInputException("No files to process");
            }
        }

        private async Task PushRevitJobsToQueueAsync(int revitProcessId, IUnitOfWork unitOfWork)
        {
            RevitJobQueueDto[] revitJobQueueItems = await unitOfWork.RevitJobRepository.GetAll()
                .Where(x => x.RevitProcessId == revitProcessId)
                .ProjectToType<RevitJobQueueDto>()
                .ToArrayAsync();

            Dictionary<string, string> revitVersionQueues = [];
            foreach (RevitJobQueueDto revitJobQueueItem in revitJobQueueItems)
            {
                if (!revitVersionQueues.ContainsKey(revitJobQueueItem.RevitVersion))
                {
                    revitVersionQueues.Add(revitJobQueueItem.RevitVersion, GenereateRevitProcessingQueueName(revitJobQueueItem.RevitVersion));
                }

                SendReceipt sendReceipt = await _azureStorageService.SendMessageToQueueAsync(revitJobQueueItem, revitVersionQueues[revitJobQueueItem.RevitVersion]);
                await unitOfWork.RevitJobRepository.GetAll()
                    .Where(x => x.Id == revitJobQueueItem.Id)
                    .ExecuteUpdateAsync(x => x.SetProperty(p => p.AzureQueueBaseName, revitVersionQueues[revitJobQueueItem.RevitVersion])
                        .SetProperty(p => p.AzureQueueMessageId, sendReceipt.MessageId)
                        .SetProperty(p => p.AzureQueuePopReceipt, sendReceipt.PopReceipt));
            }
        }

        private string GenereateRevitProcessingQueueName(string revitVersion)
        {
            return $"{AzureStorageConstants.RevitProcessingQueuePrefix}-{revitVersion}";
        }

        private async Task HandleJobSucceededResultAsync(IFormFile formFile, string fileName, RevitJob revitJob, IUnitOfWork unitOfWork)
        {
            FileUploadResultModel dbFile = await _uploadFileService.AddFileAsync(formFile, revitJob.CreatedById, Path.GetTempPath(), customFileName: fileName);
            ProductFile baseProductFile = await unitOfWork.ProductFileRepository.GetByIdAsync(revitJob.ProductFileId);
            ProjectFileModel updatedProjectFile = baseProductFile.Adapt<ProjectFileModel>();
            updatedProjectFile.SoftwareVersionId = revitJob.ProjectDataTypeId;
            updatedProjectFile.FileId = dbFile.Id;

            if (revitJob.RevitProcess.Type == RevitProcessType.UpdateParameterValues)
            {
                _productService.UpdateProductProjectFile(baseProductFile, updatedProjectFile, baseProductFile.ProductId, revitJob.CreatedById, unitOfWork);
            }
            else if (revitJob.RevitProcess.Type == RevitProcessType.UpdateRevitVersion)
            {
                _productService.AddProductProjectFile(updatedProjectFile, baseProductFile.ProductId, revitJob.CreatedById, unitOfWork);
            }
        }

        private async Task<bool> IsRevitProcessFinishedAsync(int id, IUnitOfWork unitOfWork)
        {
            IQueryable<RevitJob> revitJobQuery = unitOfWork.RevitJobRepository.GetAll()
                .Where(x => x.RevitProcessId == id);
            int revitJobTotalCount = await revitJobQuery.CountAsync();
            int revitJobFinishedCount = await revitJobQuery.CountAsync(x => x.Status != RevitJobStatus.InProgress);

            return revitJobTotalCount == revitJobFinishedCount;
        }

        private string[] GetRevitProcessFileExtensions(RevitProcessType revitProcessingType)
        {
            string[] fileExtensions = [];

            if (revitProcessingType == RevitProcessType.UpdateParameterValues)
            {
                fileExtensions = RevitProcessingConstants.UpdateParametersRevitFileExtensions;
            }
            else if (revitProcessingType == RevitProcessType.UpdateRevitVersion)
            {
                fileExtensions = RevitProcessingConstants.UpdateVersionRevitFileExtenstions;
            }

            return fileExtensions;
        }

        private async Task<string> GetRevitProcessSlackNotificationMessageAsync(int id, IUnitOfWork unitOfWork)
        {
            RevitProcessSlackNotificationDto revitProcessSlackNotificationModel = await unitOfWork.RevitProcessRepository.GetAll()
                .Where(x => x.Id == id)
                .ProjectToType<RevitProcessSlackNotificationDto>()
                .FirstOrDefaultAsync();

            if (revitProcessSlackNotificationModel == null)
            {
                return string.Empty;
            }

            string messagePrefix = string.Empty;

            switch (revitProcessSlackNotificationModel.Status)
            {
                case RevitProcessStatus.InProgress:
                    {
                        messagePrefix = $"{revitProcessSlackNotificationModel.CreatorUserEmail} started Revit Process {revitProcessSlackNotificationModel.Id}";
                        break;
                    }
                case RevitProcessStatus.Cancelled:
                    {
                        messagePrefix = $"{revitProcessSlackNotificationModel.ModifierUserEmail} cancelled Revit Process {revitProcessSlackNotificationModel.Id}";
                        break;
                    }
                case RevitProcessStatus.Done:
                    {
                        messagePrefix = $"Revit Process {revitProcessSlackNotificationModel.Id} finished";
                        break;
                    }
            }

            string message = $"{messagePrefix} at {DateTime.UtcNow}. Type: {revitProcessSlackNotificationModel.Type} Manufacturer: {revitProcessSlackNotificationModel.ManufacturerName} Products Count: {revitProcessSlackNotificationModel.ProductsCount} Files Count: {revitProcessSlackNotificationModel.FilesCount}";
            return message;
        }

        private bool IsRevitJobCompleted(RevitJobStatus status)
        {
            return status == RevitJobStatus.Completed || status == RevitJobStatus.CompletedWithErrors;
        }
        #endregion
    }
}