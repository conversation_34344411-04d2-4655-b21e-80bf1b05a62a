﻿using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithNewsroom.Models;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Linq;
using BIMsmithMarket.Core.Helpers;

namespace BIMsmithNewsroom.Controllers
{
    public class NewsController : Controller
    {
        private const int NewsInList = 10;

        public ActionResult Index(int page = 1)
        {
            NewsList model = new NewsList();
            
            var isDev = ConfigurationHelper.GetValue("IsDevEnvironment");
            this.ViewBag.isDev = isDev == "true";

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var query = unitOfWork.NewsRepository.GetAll().Where(a => a.PublishOption == PublishOption.Published && a.NewsTargets.Any(x => x.Site == NewsTargetSite.Market));

                var newsCount = query.Count();

                if (page < 1) page = 1;

                int skip = NewsInList * (page - 1);

                var list = query.OrderByDescending(a => a.PublishedDate)
                    .Skip(skip)
                    .Take(NewsInList)
                    .Select(a => new NewsModel
                    {
                        VanityId = a.VanityId,
                        ImageUrl = a.ImageUrlBig,
                        Title = a.Title,
                        Descriptions = a.Descriptions,
                        PublishedDate = a.PublishedDate
                    })
                    .ToList();

                var allTags = unitOfWork.NewsRepository.GetAll().Where(a => a.PublishOption == PublishOption.Published && a.NewsTargets.Any(x => x.Site == NewsTargetSite.Market)).Select(a => a.Tags).ToList()
                                                                .Select(a => new
                                                                {
                                                                    tags = a != null ? a.Split(',', ';').Select(t => t.Trim()).ToList() : new List<string>(),
                                                                })
                                                                .SelectMany(a => a.tags)
                                                                .Distinct()
                                                                .ToList();

                model.News = list;
                model.Tags = allTags;
                model.PagesCount = (int)System.Math.Ceiling(newsCount / (double)NewsInList);
                model.CurrentPage = page;
            }

            return View(model);
        }

        public ActionResult Tag(string tag, int page = 1)
        {
            NewsList model = new NewsList();
            
            var isDev = ConfigurationHelper.GetValue("IsDevEnvironment");
            this.ViewBag.isDev = isDev == "true";

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var newsWithTags = unitOfWork.NewsRepository.GetAll().Where(a => a.PublishOption == PublishOption.Published && a.NewsTargets.Any(x => x.Site == NewsTargetSite.Market) && a.Tags != null).Select(a => new
                {
                    id = a.Id,
                    tags = a.Tags
                })
                .ToList()
                .Select(a => new
                {
                    id = a.id,
                    tags = a.tags.Split(',', ';').Select(t => t.Trim()).ToList(),
                    tagsLower = a.tags.Split(',', ';').Select(t => t.Trim().ToLower()).ToList()
                });

                var allTags = newsWithTags.SelectMany(a => a.tags).Distinct().ToList();

                var query = unitOfWork.NewsRepository.GetAll().Where(a => a.PublishOption == PublishOption.Published && a.NewsTargets.Any(x => x.Site == NewsTargetSite.Market));

                if (!string.IsNullOrWhiteSpace(tag))
                {
                    var newsIdWithTheTag = newsWithTags.Where(a => a.tagsLower.Contains(tag.ToLower()))
                                                        .Select(a => a.id)
                                                        .ToList();

                    query = query.Where(a => newsIdWithTheTag.Contains(a.Id));
                }

                var newsCount = query.Count();

                if (page < 1) page = 1;

                int skip = NewsInList * (page - 1);

                var list = query.OrderByDescending(a => a.PublishedDate)
                    .Skip(skip)
                    .Take(NewsInList)
                    .Select(a => new NewsModel
                    {
                        VanityId = a.VanityId,
                        ImageUrl = a.ImageUrlSmall,
                        Title = a.Title,
                        Descriptions = a.Descriptions,
                        PublishedDate = a.PublishedDate
                    })
                    .ToList();

                model.News = list;
                model.IsTagList = true;
                model.Tags = allTags;
                model.TagId = tag;
                model.PagesCount = (int)System.Math.Ceiling(newsCount / (double)NewsInList);
                model.CurrentPage = page;
            }

            return View("Index", model);
        }

        public ActionResult Details(string vanityId)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var news = unitOfWork.NewsRepository.GetAll().FirstOrDefault(a => a.VanityId == vanityId);

                if (news == null)
                {
                    return NotFound("The news not found");
                }

                ViewBag.Title = news.Title;
                ViewBag.MetaDescription = news.MetaDescription;
                ViewBag.MetaKeywords = news.MetaKeywords;
                
                var isDev = ConfigurationHelper.GetValue("IsDevEnvironment");
                ViewBag.IsPrivate = (news.PublishOption == PublishOption.Private || isDev == "true");

                NewsModel model = new NewsModel();
                model.VanityId = news.VanityId;
                model.Title = news.Title;
                model.ImageUrl = news.ImageUrlBig;
                model.HtmlBody = news.HtmlBody;
                model.PublishedDate = news.PublishedDate;
                model.Tags = news.Tags != null ? news.Tags.Split(';', ',').Select(a => a.Trim()).ToList() : new List<string>();
                model.OtherNews = unitOfWork.NewsRepository.GetAll()
                    .Where(a => a.Id != news.Id && a.PublishOption == PublishOption.Published && a.NewsTargets.Any(x => x.Site == NewsTargetSite.Market))
                    .Select(a => new OtherNewsModel
                    {
                        Title = a.Title,
                        VanityId = a.VanityId
                    }).ToList();

                return View(model);
            }
        }
    }
}