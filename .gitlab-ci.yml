.before_msbuild: &vsdevshell
  before_script:
    - '$vsWherePath = [System.Environment]::ExpandEnvironmentVariables($env:VSWHERE_PATH)'
    - '& $vsWherePath -latest -format value -property installationPath -products Microsoft.VisualStudio.Product.BuildTools | Tee-Object -Variable visualStudioPath'
    - 'Join-Path "$visualStudioPath" "\Common7\Tools\Microsoft.VisualStudio.DevShell.dll" | Import-Module'
    - 'Enter-VsDevShell -VsInstallPath:"$visualStudioPath" -SkipAutomaticLocation'

variables:
  VSWHERE_PATH: '%ProgramFiles(x86)%\Microsoft Visual Studio\Installer\vswhere.exe'
  MSPROJECT_NAME: 'BIMsmithMarket.Api'
  MSPROJECT_BLOG: 'BimsmithBlog'
  MSPROJECT_NEWSROOM: 'BimsmithNewsroom'
  MSBUILD_CSPROJ: 'BIMsmithMarket.Api.csproj'
  USERNAME_ENV: 'MarketVM'
  DATALAYER_PATH: 'BimsmithMarket.DataLayer'
  DATALAYER_DLL: 'BIMsmithMarket.DataLayer.dll'

workflow:
  rules:
      # https://api-market-dev.bimsmith.com
    - if: '$CI_COMMIT_BRANCH == "dev"'
      variables:
        BUILD_DOTNET_VERSION: 'DEV'
        PUBLISH_PROFILE: 'DEV.pubxml'
        WEB_CONFIG: 'C:\inetpub\wwwroot\Market\DEV-Market-Api\Web.config'
        PASSWORD_ENV: '$PASSWORD_DEV'
        MSBUILD_CONCURRENCY: '2'
        MSPUBLISH_USER: 'MarketVM'
      # https://api-market-uat.bimsmith.com
    - if: '$CI_COMMIT_BRANCH =="uat"'
      variables:
        BUILD_DOTNET_VERSION: 'UAT'
        PUBLISH_PROFILE: 'UAT.pubxml'
        WEB_CONFIG: 'C:\inetpub\wwwroot\Market\UAT-Market-Api\Web.config'
        PASSWORD_ENV: '$PASSWORD_DEV'
        MSBUILD_CONCURRENCY: '2'
        MSPUBLISH_USER: 'MarketVM'        
      # https://api-market.bimsmith.com
    - if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH'
      variables:
        BUILD_DOTNET_VERSION: 'Release'
        PUBLISH_PROFILE: 'RELEASE.pubxml'
        WEB_CONFIG: 'C:\inetpub\wwwroot\Market-Prod-Api\Web.config'
        PASSWORD_ENV: '$PASSWORD_PROD'
        MSBUILD_CONCURRENCY: '8'
        MSPUBLISH_USER: 'MarketVM'        
    - when: always

stages:
  - build
  - deploy
  - migration

.build-dotnet-template:
  <<: *vsdevshell
  stage: build
  script:
    - echo $CI_COMMIT_SHORT_SHA
    - echo $CI_RUNNER_TAGS
    - echo $GITLAB_USER_LOGIN
    - echo $CI_PROJECT_DIR
    - dotnet build --configuration $BUILD_DOTNET_VERSION --verbosity quiet
    - echo "$BUILD_DOTNET_VERSION Build .NET succeeded."

.deploy-dotnet-template:
  <<: *vsdevshell
  stage: deploy
  script:
    - echo $CI_RUNNER_TAGS
    - $env:ASPNETCORE_ENVIRONMENT=$BUILD_DOTNET_VERSION    
    - $DECODED_PASSWORD = [System.Text.Encoding]::Unicode.GetString([System.Convert]::FromBase64String($PASSWORD_ENV))
    # - dotnet publish $MSPROJECT_NAME\$MSBUILD_CSPROJ --configuration $BUILD_DOTNET_VERSION /p:PublishProfile="$MSPROJECT_NAME\Properties\PublishProfiles\$PUBLISH_PROFILE" /p:AllowUntrustedCertificate=True /p:Username="$MSPUBLISH_USER" /p:Password="$DECODED_PASSWORD"
    - dotnet publish $MSPROJECT_NAME\$MSPROJECT_NAME.csproj --configuration $BUILD_DOTNET_VERSION /p:PublishProfile="$MSPROJECT_NAME\Properties\PublishProfiles\$PUBLISH_PROFILE" /p:AllowUntrustedCertificate=True /p:Username="$MSPUBLISH_USER" /p:Password="$DECODED_PASSWORD"
    - dotnet publish $MSPROJECT_BLOG\$MSPROJECT_BLOG.csproj --configuration $BUILD_DOTNET_VERSION /p:PublishProfile="$MSPROJECT_BLOG\Properties\PublishProfiles\$PUBLISH_PROFILE" /p:AllowUntrustedCertificate=True /p:Username="$MSPUBLISH_USER" /p:Password="$DECODED_PASSWORD"    
    - dotnet publish $MSPROJECT_NEWSROOM\$MSPROJECT_NEWSROOM.csproj --configuration $BUILD_DOTNET_VERSION /p:PublishProfile="$MSPROJECT_NEWSROOM\Properties\PublishProfiles\$PUBLISH_PROFILE" /p:AllowUntrustedCertificate=True /p:Username="$MSPUBLISH_USER" /p:Password="$DECODED_PASSWORD"    
    - echo "$BUILD_DOTNET_VERSION Publish succeeded"

.migration-template:
  <<: *vsdevshell
  stage: migration
  script:
    - echo $CI_RUNNER_TAGS
    - $env:ASPNETCORE_ENVIRONMENT=$BUILD_DOTNET_VERSION
    - echo "Migrations Update-Database -ProjectName $DATALAYER_PATH -StartUpProjectName $MSPROJECT_NAME"
    - dotnet ef database update --context ApplicationDbContext --project BimsmithMarket.DataLayer --startup-project BimsmithMarket.Api --verbose
    - echo "$BUILD_DOTNET_VERSION Migrations succeeded"

build-dotnet-dev-uat:
  extends: .build-dotnet-template
  tags:
    - BIMsmithDev
  only:
    variables:
     - ($CI_COMMIT_BRANCH == "dev" || $CI_COMMIT_BRANCH == "uat")

deploy-dotnet-dev-uat:
  extends: .deploy-dotnet-template
  tags:
    - BIMsmithDev
  only:
    variables:
     - ($CI_COMMIT_BRANCH == "dev" || $CI_COMMIT_BRANCH == "uat")

migration-dotnet-dev-uat:
  extends: .migration-template
  tags:
    - BIMsmithDev
  only:
    variables:
     - ($CI_COMMIT_BRANCH == "dev" || $CI_COMMIT_BRANCH == "uat")

build-dotnet-prod:
  extends: .build-dotnet-template
  tags:
    - MarketVM
  only:
    variables:
     - ($CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH)

deploy-dotnet-prod:
  extends: .deploy-dotnet-template
  tags:
    - MarketVM
  only:
    variables:
     - ($CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH)

migration-dotnet-prod:
  extends: .migration-template
  tags:
    - MarketVM
  only:
    variables:
     - ($CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH)