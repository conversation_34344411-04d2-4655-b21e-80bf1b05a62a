﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    public class AnalyticsController : BaseApiController
    {
        private readonly IAnalyticsService _analyticsService;

        public AnalyticsController(IAnalyticsService analyticsService)
        {
            _analyticsService = analyticsService;
        }

        /// <summary>
        /// Lists the request pricing users for Analytics
        /// </summary>
        /// <param name="token">The access token</param>
        /// <param name="manufacturerId">The manufacturer identifier</param>
        /// <param name="offset">The offset to skip</param>
        /// <param name="count">The count to take</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("RequestPricingUserList")]
        public async Task<IActionResult> RequestPricingUserList(
            string token,
            int manufacturerId,
            int offset = 0,
            int count = 10)
        {
            if (token != ConfigurationHelper.GetValue("ApiAccessToken"))
                return BadRequest("API Access token is invalid");

            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _analyticsService.RequestPricingUserListAsync(manufacturerId, unitOfWork, offset, count));
        }

        /// <summary>
        /// List of manufacturers which have User data tab on
        /// </summary>
        /// <param name="token">The access token</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("UserDataManufacturers")]
        public async Task<IActionResult> UserDataManufacturers(string token)
        {
            if (token != ConfigurationHelper.GetValue("ApiAccessToken"))
                return BadRequest("API Access token is invalid");

            IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _analyticsService.UserDataManufacturersAsync(unitOfWork));
        }
    }
}