﻿using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto.ExternalApi;
using Mapster;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public static class ExternalApiMap
    {
        public static void Register(TypeAdapterConfig config)
        {
            config.ForType<ProductFile, ExternalApiTechnicalDocsMappingDto>()
                .Map(dest => dest.FileName, src => src.File.FileName)
                .Map(dest => dest.Title, src => src.File.Title);

            config.ForType<ProductLineFile, ExternalApiTechnicalDocsMappingDto>()
                .Map(dest => dest.FileName, src => src.File.FileName)
                .Map(dest => dest.Title, src => src.File.Title)
                .Map(dest => dest.Weight, src => src.Weight + ExternalApiConstants.WeightLevelStep);

            config.ForType<ManufacturerFile, ExternalApiTechnicalDocsMappingDto>()
                .Map(dest => dest.FileName, src => src.File.FileName)
                .Map(dest => dest.Title, src => src.File.Title)
                .Map(dest => dest.Weight, src => src.Weight + 2 * ExternalApiConstants.WeightLevelStep);
        }
    }
}