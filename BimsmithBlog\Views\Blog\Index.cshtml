﻿@using System.Configuration;
@using BIMsmithMarket.Core.Helpers
@model BIMsmithBlog.Models.BlogsList

<header class="header-wrap">
    @if (ViewBag.isDev)
    {
        <meta name="robots" content="noindex">
    }
    <div class="header">
        <a href="#" class="menu"><span></span><span></span><span></span>menu</a>
        <div class="top clear">
            <a class="logo" href="@ConfigurationHelper.GetValue("BimsmithUrl")" target="_blank"><img src="~/images/logo.png" alt=""></a>
            <div class="sign-in">
                <div style="display: none;" id="user-name"></div>
                <div style="display: none;" id="head-drop" class="head-drop">
                    <div class="av">
                        <img id="profile-image" alt="" src="/images/img01.jpg" height="383" width="383">
                        <div id="settings-name" class="name"></div>
                    </div>
                    <div class="links">
                        <a href="@ConfigurationHelper.GetValue("MarketUrl")/">Market</a>
                        <a href="@ConfigurationHelper.GetValue("ForgeUrl")/">forge</a>
                        <a href="@ConfigurationHelper.GetValue("BimsmithUrl")/NewMyBIMSmith/settings">MyBIMsmith</a>
                    </div>
                </div>
                <a id="logout-button" style="display: none;" class="def-btn logout-button" href="javascript:void(0)">Log out</a>
                <a id="login-button" style="display: inline-block;" class="def-btn" href="@ConfigurationHelper.GetValue("BimsmithUrl")/Account/Login/">Log in</a>
                <a id="signup-button" style="display: inline-block;" class="g-btn" href="@ConfigurationHelper.GetValue("BimsmithUrl")/Account/Register">Sign up</a>
            </div>
        </div>

        <div class="bot clear">
            <ul class="links clear">
                @if (Model.IsCategoryPage)
                {
                    <li><a href="/">All</a></li>
                }
                else
                {
                    <li class="active"><a href="/">All</a></li>
                }

                @foreach (var category in Model.Categories)
                {
                    if (category.IsActive)
                    {
                        <li class="active"><a href="/blog/category/@category.VanityId">@category.Name</a></li>
                    }
                    else
                    {
                        <li><a href="/blog/category/@category.VanityId">@category.Name</a></li>
                    }
                }
                <li id="mob-login"><a href="@ConfigurationHelper.GetValue("BimsmithUrl")/Account/Login/">Sign In</a></li>
                <li id="mob-regis"><a href="@ConfigurationHelper.GetValue("BimsmithUrl")/Account/Register">Sign Up</a></li>
            </ul>
            <div class="search">
                <i class="fa fa-search"></i>
                <input type="search">
            </div>
        </div>
    </div>
</header>
<div class="main">
    <div class="block">
        @if (Model.HeadBlog != null)
        {
            <div class="img-box">
                <a href="/@Model.HeadBlog.VanityId">
                    <img src="@Model.HeadBlog.ImageUrlBig" alt="">
                </a>
            </div>
            <div class="r-box">
                <div class="btns center">
                    <a class="n-green-btn" href="/blog/category/@Model.HeadBlog.Category.VanityId">@Model.HeadBlog.Category.Name</a>
                    <span class="n-gray-btn" href="">@Model.HeadBlog.PublishedDate.ToString("d")</span>
                </div>
                <a class="blog-header" href="/@Model.HeadBlog.VanityId"><h2>@Model.HeadBlog.Title</h2></a>
                <p>
                    <img class="head-img" src="@(!string.IsNullOrEmpty(Model.HeadBlog.AuthorImage) ? Model.HeadBlog.AuthorImage : "/Photo?email=")" />
                    @Model.HeadBlog.AuthorTitle
                </p>
                <p>@Model.HeadBlog.Descriptions</p>
            </div>
        }
    </div>
    <div class="articles">
        @if (Model.HeadBlog == null && Model.RecentBlogs.Count == 0)
        {
            if (string.IsNullOrEmpty(Model.SearchQuery))
            {
                <h3>No posts</h3>
            }
            else
            {
                <h3>Your search - @Model.SearchQuery - did not match any post.</h3>
            }
        }
        else
        {
            <h3>Recent posts</h3>
        }

        <ul class="art-list">
            @foreach (var blogItem in Model.RecentBlogs)
            {
                <li>
                    <a href="/@blogItem.VanityId">
                        <img src="@blogItem.ImageUrlSmall" alt="">
                    </a>
                    <div class="btns center">
                        <a class="n-green-btn" href="/blog/category/@blogItem.Category.VanityId">@blogItem.Category.Name</a>
                        <span class="n-gray-btn" href="">@blogItem.PublishedDate.ToString("d")</span>
                    </div>
                    <a class="blog-header" href="/@blogItem.VanityId"><h3>@blogItem.Title</h3></a>
                    <p class="d-f a-i-c">
                        <img class="head-img" src="@(!string.IsNullOrEmpty(blogItem.AuthorImage) ? blogItem.AuthorImage : "/Photo?email=")" />
                        @blogItem.AuthorTitle
                    </p>
                    <p>@blogItem.Descriptions</p>
                    <a class="more" href="/@blogItem.VanityId">read more</a>
                </li>
            }
        </ul>

        @if (Model.PagesCount > 1)
        {
            var categoryVanity = string.Empty;
            if (Model.HeadBlog != null)
            {
                categoryVanity = Model.HeadBlog.Category.VanityId;
            }

            <nav aria-label="Page navigation" class="center-block">
                <ul class="pagination">
                    @if (Model.CurrentPage > 1)
                    {
                        <li>
                            <a href="/?page=@(Model.CurrentPage - 1)&categoryVanityId=@(Model.IsAll ? "" : categoryVanity)" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                    }
                    else
                    {
                        <li class="disabled">
                            <a href="#" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                    }
                    @for (int i = 0; i < Model.PagesCount; i++)
                    {
                        if ((i + 1) == Model.CurrentPage)
                        {
                            <li class="active"><a href="/?page=@(i+1)&categoryVanityId=@(Model.IsAll ? "" : categoryVanity)">@(i + 1)</a></li>
                        }
                        else
                        {
                            <li><a href="/?page=@(i+1)&categoryVanityId=@(Model.IsAll ? "" : categoryVanity)">@(i + 1)</a></li>
                        }
                    }
                    @if (Model.CurrentPage != Model.PagesCount)
                    {
                        <li>
                            <a href="/?page=@(Model.CurrentPage + 1)&categoryVanityId=@(Model.IsAll ? "" : categoryVanity)" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    }
                    else
                    {
                        <li class="disabled">
                            <a href="#" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    }
                </ul>
            </nav>
        }
    </div>
</div>