﻿namespace BIMsmithMarket.Services.Helpers
{
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;

    public class LockAsync : IDisposable
    {
        private static object lockPoint = new object();
        private static Dictionary<object, Queue<TaskCompletionSource<bool>>> lockPointsDictionary = new Dictionary<object, Queue<TaskCompletionSource<bool>>>();

        private Queue<TaskCompletionSource<bool>> lockQueue = null;
        private bool wasUnlock;
        private object lockPointObject;
        private bool keepQueueIfEmpty;

        private LockAsync(bool keepQueueIfEmpty)
        {
            this.keepQueueIfEmpty = keepQueueIfEmpty;
        }

        public Queue<TaskCompletionSource<bool>> Queue
        {
            get
            {
                return this.lockQueue;
            }
        }

        public async Task Lock(object lockPointObject)
        {
            this.lockPointObject = lockPointObject;
            TaskCompletionSource<bool> waiter = new TaskCompletionSource<bool>();
            lock (lockPoint)
            {
                if (!lockPointsDictionary.TryGetValue(lockPointObject, out lockQueue))
                {
                    lockQueue = new Queue<TaskCompletionSource<bool>>();
                    lockPointsDictionary[lockPointObject] = lockQueue;
                }

                if (lockQueue.Count == 0)
                {
                    waiter.TrySetResult(true);
                }
                lockQueue.Enqueue(waiter);
            }
            await waiter.Task;
        }

        public void Unlock()
        {
            this.wasUnlock = true;
            this.NextInQueue();
        }

        private void NextInQueue()
        {
            lock (lockPoint)
            {
                bool stop = false;
                do
                {
                    if (lockQueue.Count > 0)
                    {
                        var result = lockQueue.Dequeue();
                        stop = result.TrySetResult(true);
                    }
                    else
                    {
                        stop = true;
                    }

                    if (lockQueue.Count == 0 && !this.keepQueueIfEmpty) // clear queue
                    {
                        lockPointsDictionary.Remove(this.lockPointObject);
                    }
                } while (!stop);
            }
        }

        public static async Task<LockAsync> Create(object lockPointObject, bool keepQueueIfEmpty = true)
        {
            LockAsync lockAsync = new LockAsync(keepQueueIfEmpty);
            await lockAsync.Lock(lockPointObject);
            return lockAsync;
        }

        public void Dispose()
        {
            if (!this.wasUnlock)
            {
                this.Unlock();
            }
        }
    }
}
