﻿using System;
using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Dto
{
    public class GetManufacturerDto
    {
        public int id { get; set; }
        public string forgeManufacturerId { get; set; }
        public string name { get; set; }
        public string manufacturerHubTitle { get; set; }
        public string manufacturerHubSubtitle { get; set; }
        public string description { get; set; }
        public string site { get; set; }
        public string synonyms { get; set; }
        public string regionIds { get; set; }
        public string stateIds { get; set; }
        public string note { get; set; }
        public int type { get; set; }
        public string phoneMask { get; set; }
        public string phoneNumber { get; set; }
        public string pageSetting { get; set; }
        public string analyticsSetting { get; set; }
        public string nodeSetting { get; set; }
        public string metaDescription { get; set; }
        public string metaKeywords { get; set; }
        public string swatchboxManufacturerId { get; set; }
        public string metaTitle { get; set; }
        public string keywords { get; set; }
        public AddressDto address { get; set; }
        public bool isOnForge { get; set; }
        public bool isOnMarket { get; set; }
        public bool lAndL { get; set; }
        public bool letsTalk { get; set; }
        public bool sendULInfo { get; set; }
        public string letsTalkSettings { get; set; }
        public bool subscribeButton { get; set; }
        public string emailLandL { get; set; }
        public string emailLetsTalk { get; set; }
        public string emailLandLCC { get; set; }
        public string emailLetsTalkCC { get; set; }
        public string videoUrl { get; set; }
        public GetManufacturerImageDto image { get; set; }
        public GetManufacturerOriginalImageDto originalImage { get; set; }
        public string hubVanityURL { get; set; }
        public string forgeUrl { get; set; }
        public string marketUrl { get; set; }
        public string ownerId { get; set; }
        public GetManufacturerImageDto logo { get; set; }
        public IEnumerable<GetManufacturerProductLineDto> productLines { get; set; }
        public DateTime updateDate { get; set; }
        public bool published { get; set; }
        public bool publishToPartner { get; set; }
        public bool staging { get; set; }
        public string manualUrl { get; set; }
        public bool includeRevitPlugin { get; set; }
        public GetManufacturerRevitImageDto revitIcon { get; set; }
        public bool isLetsTalk { get; set; }
        public bool isSubscribed { get; set; }
        public bool isLunchLearn { get; set; }
        public IEnumerable<GetManufacturerManufacturerFileDto> productFiles { get; set; }
        public string detailsMicrositeSettings { get; set; }
        public IEnumerable<GetManufacturerManufacturerStyleFileDto> styleFiles { get; set; }
        public IEnumerable<GetManufacturerManufacturerFileDto> additionalFiles { get; set; }
        public bool showFooterAd { get; set; }
        public string footerAdUrl { get; set; }
        public int? footerAdImageId { get; set; }
        public GetManufacturerImageDto footerAdImage { get; set; }
        public bool useCustomLoginRegisterScreen { get; set; }
        public string customSignInBackgroundColor { get; set; }
        public string customSignInTextColor { get; set; }
        public string customSignInDescription { get; set; }
        public int? customSignInLogoImageId { get; set; }
        public GetManufacturerImageDto customSignInLogoImage { get; set; }
        public bool isParentManufacturer { get; set; }
        public List<GetManufacturerChildDto> childManufacturers { get; set; }
        public string CustomMicrositeName { get; set; }
        public float Weight { get; set; }
        public bool RequestPricingEnabled { get; set; }
        public string RequestPricingEmail { get; set; }
        public string RequestPricingEmailCC { get; set; }
    }

    public class GetManufacturerChildDto
    {
        public int? id { get; set; }

        public string name { get; set; }

        public bool Published { get; set; }
    }

    public class GetManufacturerOriginalImageDto
    {
        public int? id { get; set; }
        public string original { get; set; }
    }

    public class GetManufacturerImageDto
    {
        public int? id { get; set; }
        public string small { get; set; }
        public string big { get; set; }
    }

    public class GetManufacturerRevitImageDto
    {
        public int? id { get; set; }
        public string smallUrl { get; set; }
        public string originalUrl { get; set; }
    }

    public class GetManufacturerProductLineDto
    {
        public int id { get; set; }
        public string name { get; set; }
        public string regionIds { get; set; }
        public string stateIds { get; set; }
        public int? manufacturerId { get; set; }
        public string manufacturerName { get; set; }
        public string description { get; set; }
    }

    public class GetManufacturerManufacturerFileDto
    {
        public int id { get; set; }
        public string customFileId { get; set; }
        public string title { get; set; }
        public string fileName { get; set; }
        public long fileSize { get; set; }
        public int updatesCount { get; set; }
        public int fileSyncStatusCode { get; set; }
        public string fileSyncUrl { get; set; }
        public string mimeType { get; set; }
        public string url { get; set; }
        public string preview { get; set; }
        public float weight { get; set; }
    }

    public class GetManufacturerManufacturerStyleFileDto
    {
        public int? fileId { get; set; }
        public string title { get; set; }
        public string fileName { get; set; }
        public long fileSize { get; set; }
        public string mimeType { get; set; }
        public string url { get; set; }
        public string fileVersion { get; set; }
        public int type { get; set; }
    }
}
