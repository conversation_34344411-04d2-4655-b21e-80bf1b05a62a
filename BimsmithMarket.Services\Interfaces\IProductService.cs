﻿using Azure.Storage.Blobs;
using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.DBModels.RevitProcessing;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.Product;
using BIMsmithMarket.Domain.Dto.SwatchboxDto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IProductService
    {
        IEnumerable<int> GetFileIdsForProjectType(int productId, IEnumerable<int> projectDataTypeIds);

        bool ProductExists(int productId);

        Task<bool> IsManufacturerTypeGenericAsync(int productId, IUnitOfWork unitOfWork);

        Task<IsManufacturerTypeGenericResponseDto[]> BatchIsManufacturerTypeGenericAsync(IsManufacturerTypeGenericRequestDto model, IUnitOfWork unitOfWork);

        Task<dynamic> LookupAsync(int manufacturerId, string name = null, string externalId = null);

        Task<AddEditProductResponse> AddAsync(AddProductModel model, string userId);

        Task<AddEditProductResponse> EditAsync(EditProductModel model, string userId);

        Task<AddEditProductResponse> CloneAsync(int productId, string userId);

        Task<AddExternalResultModel> AddExternalAsync(
            AddExternalProduct[] models,
            string host,
            AllowedManufacturersModel allowedManufacturers,
            HttpRequest httpRequest);

        AddExternalValidatedUserModel ValidateUser(IUnitOfWork unitOfWork, string host, AllowedManufacturersModel allowedManufacturers, HttpRequest httpRequest);

        void AddOrUpdateProductCategories(Product product, IUnitOfWork unitOfWork, List<int> addedItemIds);

        Task AddOrUpdateProductAttachmentsAsync(Product product, IUnitOfWork unitOfWork, List<FileDto> addedProductFiles, string userId, AzureStorageService azureBlobProvider, BlobContainerClient filesContainer, string productName, string manufacturerName);

        void AddOrUpdateProductCertificates(Product product, IUnitOfWork unitOfWork, List<int> addedItemIds, string userId);

        Task AddOrUpdateRelatedProductsAsync(Product product, IUnitOfWork unitOfWork, List<int> addedItemIds, string userId);

        Task AddOrUpdateProductSamplesAsync(Product product, IUnitOfWork unitOfWork, List<int> addedItemIds, string userId);

        Task AddOrUpdateProductDetailsAsync(Product product, IUnitOfWork unitOfWork, List<int> addedItemIds, string userId);

        Task AddOrUpdateProductQualityItemsAsync(Product product, IUnitOfWork unitOfWork, List<int> addedItemIds, string userId);

        Task AddOrUpdateProductCisfbsAsync(Product product, IUnitOfWork unitOfWork, List<int> addedItemIds, string userId);

        Task AddOrUpdateProductOmniclassesAsync(Product product, IUnitOfWork unitOfWork, List<int> addedItemIds, string userId);

        Task AddOrUpdateProductUniclassesAsync(Product product, IUnitOfWork unitOfWork, List<int> addedItemIds, string userId);

        Task AddOrUpdateProductUniformatsAsync(Product product, IUnitOfWork unitOfWork, List<int> addedItemIds, string userId);

        Task AddOrUpdateProductMasterformatsAsync(Product product, IUnitOfWork unitOfWork, List<int> addedItemIds, string userId);

        Task AddOrUpdateProductPhotosAsync(Product product, IUnitOfWork unitOfWork, List<PhotoModel> addedProductPhotos, string userId, AzureStorageService azureBlobProvider, BlobContainerClient photosContainer, string productName, string manufacturerName);

        Task AddOrUpdateProductProjectFilesAsync(Product product, IUnitOfWork unitOfWork, List<ProjectFileModel> addedProjectFiles, string userId);

        Task AddOrUpdateProductKeyStatsAsync(Product product, IUnitOfWork unitOfWork, List<ProductStatsModel> addedProductStats, string userId, int? productId, bool isRestoredFromBackup);

        void HandleProductMedataData(ref Product product, string mainCategoryName, string manufacturerName);

        Task<ICollection<SwatchboxProductsResponseDto>> SwatchboxListAsync(SwatchboxProductsRequestDto model);

        Task<List<ProjectFileDto>> GetProjectFilesAsync(EntityIdsDto model, IUnitOfWork unitOfWork);

        Task<List<ProductFileDto>> GetProductFilesAsync(EntityIdsDto model, IUnitOfWork unitOfWork);

        Task<int[]> GetProjectFilesForProductAsync(int productId, IUnitOfWork unitOfWork);

        Task<int[]> GetProjectFilesDataTypesAsync(int productId, IUnitOfWork unitOfWork);

        Task<int[]> GetProductFilesForProductAsync(int productId, IUnitOfWork unitOfWork);

        Task DeleteProductsByIds(IUnitOfWork unitOfWork, IList<int> ids);

        Task<AddEditProductResponse> AddOrUpdateAsync(
           AddProductModel model,
           string userId,
           IUnitOfWork unitOfWork,
           int? productId = null,
           bool isExternalCall = false,
           bool isRestoredFromBackup = false);

        Task SaveProductToMongoAsync(int productId, bool isUpdate, IUnitOfWork unitOfWork);

        Task<ProductGetDto> GetProductForAdminPanelAsync(int id, IUnitOfWork unitOfWork);

        Task<MicrositeListWithStarterListResultDto> MicrositeListWithStarterAsync(
            MicrositeListWithStarterFiltersDto model,
            string userName,
            IUnitOfWork unitOfWork);

        Task<MicrositeListWithStarterFiltersResultDto> MicrositeListWithStarterGetFiltersAsync(
            string searchId,
            IUnitOfWork unitOfWork);

        Task ChangePublishedAllAsync(ChangeProductFlagAllDto model, string userId, IUnitOfWork unitOfWork);

        Task ChangePublishedAllOnCustomMicrositeAsync(ChangeProductFlagAllDto model, string userId, IUnitOfWork unitOfWork);

        Task ChangeStagingAllAsync(ChangeProductFlagAllDto model, string userId, IUnitOfWork unitOfWork);

        Task<Dictionary<int, string>> GetLastMofifierAsync(int[] productIds, IUnitOfWork unitOfWork);

        Task UpdateMongoProductsAsync(int? manufacturerId, IUnitOfWork unitOfWork);

        Task<PaginationListDto<AdminListProductDto>> AdminListAsync(
            IUnitOfWork unitOfWork,
            int? manufacturerId = null,
            int? productLineId = null,
            string query = null,
            ProductSortType sortType = ProductSortType.Relevant,
            int offset = 0,
            int count = 50
            );

        Task<ProductManufacturerDto[]> CheckProductsWithTheSameProjectFilesAndDifferentPriceTypeAsync(int productId, IUnitOfWork unitOfWork);

        Task<ICollection<string>> RelevantSearchListAsync(RelevantSearchListFilterDto filter, IUnitOfWork unitOfWork);

        Task<PaginationListDto<BaseNameListDto>> RevitProcessingListAsync(RevitProcessingProductFilterDto model, IUnitOfWork unitOfWork);

        void AddProductProjectFile(ProjectFileModel projectFile, int productId, string userId, IUnitOfWork unitOfWork);

        void UpdateProductProjectFile(ProductFile projectFile, ProjectFileModel modelProjectFile, int productId, string userId, IUnitOfWork unitOfWork);
    }
}