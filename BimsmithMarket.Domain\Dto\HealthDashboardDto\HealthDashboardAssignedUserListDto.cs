﻿using BIMsmithMarket.Domain.Enums;
using System;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Dto.HealthDashboardDto
{
    public class HealthDashboardFilterDto
    {
        [Required]
        public HealthDashboardAccessType Type { get; set; }

        public HealthDashboardType DashboardMissingType { get; set; }

        public HealthDashboardFilterPeriod FilterPeriod { get; set; }

        [Required]
        public int EntityId { get; set; }
    }

    public class HealthDashboardAssignedUserListDto
    {
        public string UserId { get; set; }

        public string UserEmail { get; set; }
    }

    public class HealthDashboardReturnDto
    {
        public HealthDashboardReturnDto()
        {
        }
        public HealthDashboardReturnDto(int entityId, DateTime? modifiedDate)
        {
            EntityId = entityId;
            LastModifiedDate = modifiedDate;
        }
        public int EntityId { get; set; }
        public DateTime? LastModifiedDate { get; set; }
    }
}