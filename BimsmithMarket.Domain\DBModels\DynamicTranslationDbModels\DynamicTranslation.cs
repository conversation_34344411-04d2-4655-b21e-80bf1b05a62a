﻿using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels.DynamicTranslationDbModels
{
    public class DynamicTranslation : BaseEntity
    {
        public string Value { get; set; }

        public string LanguageCode { get; set; }

        public int TranslatableEntityFieldId { get; set; }

        public int EntityId { get; set; }

        [ForeignKey("TranslatableEntityFieldId")]
        public virtual TranslatableEntityField TranslatableEntityField { get; set; }
    }
}