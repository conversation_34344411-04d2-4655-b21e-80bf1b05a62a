﻿using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    
    [Route("api/[controller]/[action]")]
    public class MetadataController : BaseApiController
    {
        private readonly IMetadataService _metadataService;
        private readonly IMasterformatService _masterformatService;


		public MetadataController(
            IMetadataService metadataService,
			IMasterformatService masterformatService)
        {
            _masterformatService = masterformatService;
			_metadataService = metadataService;
        }

        [HttpGet]
        public async Task<IActionResult> GetMetadataForCurrentRoute(string requestUrl)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _metadataService.GetMetadataForCurrentRouteAsync(requestUrl, _masterformatService, unitOfWork));
            }
        }
    }
}