﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Domain.DBModels.PaymentDbModels;
using BIMsmithMarket.Domain.Dto.PaymentsDto;
using Mapster;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public static class UserPaidProductMap
    {
        public static void Register(TypeAdapterConfig config)
        {
            config.ForType<UserPaidProductCreateByAdmin, UserPaidProductDto>()
                .Map(dest => dest.StripeSessionStatus, src => "paid")
                .Map(dest => dest.StripeSessionId, src => "AddedByAdmin")
                .Map(dest => dest.Key, src => ConfigurationHelper.GetValue("StripeOpenKey"));

            config.ForType<UserPaidProduct, UserPaidProductDto>()
                .Map(dest => dest.Key, src => ConfigurationHelper.GetValue("StripeOpenKey"));
        }
    }
}