﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Services.Helpers;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Xml;

namespace BIMsmithNewsroom.Controllers
{
    public class SEODataController : Controller
    {
        /// <summary>
        /// Get Sitemap.xml sheet
        /// </summary>
        /// <returns></returns>
        public FileContentResult Sitemap()
        {
            var nodes = GetSitemapNodes(ConfigurationHelper.GetValue("NewsroomUrl"));

            using (var memoryStream = new MemoryStream())
            {
                var settings = new XmlWriterSettings();
                settings.Indent = true;
                settings.IndentChars = "     ";
                settings.NewLineOnAttributes = false;

                using (var writer = XmlWriter.Create(memoryStream, settings))
                {
                    writer.WriteStartDocument();
                    string ns = "http://www.sitemaps.org/schemas/sitemap/0.9";
                    writer.WriteStartElement("urlset", ns);
                    writer.WriteAttributeString("xmlns", ns);

                    foreach (var node in nodes)
                    {
                        writer.WriteStartElement("url");

                        if (!string.IsNullOrEmpty(node.Loc))
                        {
                            writer.WriteElementString("loc", node.Loc);
                        }
                        if (node.Lastmod.HasValue)
                        {
                            writer.WriteElementString("lastmod", node.Lastmod.Value.Date.ToString("yyyy-MM-dd"));
                        }
                        if (node.ChangeFreq.HasValue)
                        {
                            writer.WriteElementString("changefreq", node.ChangeFreq.Value.ToString());
                        }
                        if (node.Priority.HasValue)
                        {
                            writer.WriteElementString("priority", node.Priority.Value.ToString());
                        }

                        writer.WriteEndElement();
                    }
                    writer.WriteEndElement();
                    writer.WriteEndDocument();
                    writer.Flush();
                }
                byte[] xmlData = memoryStream.ToArray();

                return File(xmlData, "text/xml");
            }
        }

        [HttpGet]
        public async Task<IActionResult> Robots()
        {
            var bytes = RobotsHelper.GenerateRobots();
            return File(bytes, "text/plain");
        }

        /// <summary>
        /// Retrive data from database
        /// </summary>
        /// <param name="baseUrl"></param>
        /// <returns></returns>
        private List<SitemapNode> GetSitemapNodes(string baseUrl)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var nodes = new List<SitemapNode>();

                //Add main domain
                nodes.Add(new SitemapNode
                {
                    Loc = baseUrl,
                    ChangeFreq = SitemapFrequency.Weekly,
                    Lastmod = DateTime.UtcNow,
                    Priority = 1.0
                });

                var listOfVanityIds = unitOfWork.NewsRepository.GetAll()
                                                .Where(a => (a.PublishOption == PublishOption.Published || a.PublishOption == PublishOption.Hidden) && a.NewsTargets.Any(x => x.Site == NewsTargetSite.Market))
                                                .Select(a => new { a.VanityId, a.CreatedDate, a.ModifiedDate })
                                                .ToList();

                foreach (var news in listOfVanityIds)
                {
                    nodes.Add(new SitemapNode
                    {
                        Loc = baseUrl + "/" + Uri.EscapeDataString(news.VanityId),
                        ChangeFreq = SitemapFrequency.Weekly,
                        Lastmod = news.ModifiedDate ?? news.CreatedDate,
                        Priority = 1.0
                    });
                }

                return nodes;
            }

        }

        /// <summary>
        /// Sitemap node model
        /// </summary>
        private class SitemapNode
        {
            public string Loc { get; set; }

            public DateTime? Lastmod { get; set; }

            public double? Priority { get; set; }

            public SitemapFrequency? ChangeFreq { get; set; }
        }

        /// <summary>
        /// Enumb for Sitemap Frequency
        /// </summary>
        private enum SitemapFrequency
        {
            Never,
            Yearly,
            Monthly,
            Weekly,
            Daily,
            Hourly,
            Always
        }
    }
}