﻿using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Dto.PluginFileDto
{
    public class AddPluginFileDto
    {
        public string Info { get; set; }

        [Required]
        public int FileId { get; set; }

        [Required]
        public PluginType PluginType { get; set; }
    }

    public class EditPluginFileDto : AddPluginFileDto
    {
        [Required]
        public int Id { get; set; }

        public string Version { get; set; }

        public string[] Updates { get; set; }
    }

    public class GetPluginFileDto
    {
        public int Id { get; set; }

        public string Info { get; set; }

        public GetPluginFileInfoDto File { get; set; }
    }

    public class GetPluginFileInfoDto
    {
        public int Id { get; set; }

        public string Name { get; set; }
    }
}