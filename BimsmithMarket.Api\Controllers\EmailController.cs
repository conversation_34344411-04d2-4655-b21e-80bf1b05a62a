﻿using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto.Emails;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    public class EmailController : BaseApiController
    {
        /// <summary>
        /// The web host environment
        /// </summary>
        private readonly IWebHostEnvironment _webHostEnvironment;

        public EmailController(IWebHostEnvironment webHostEnvironment)
        {
            _webHostEnvironment = webHostEnvironment;
        }

        /// <summary>
        /// Send product share email
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("SendProductShareEmail")]
        public async Task<IActionResult> SendProductShareEmail(ProductShareEmailDto model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var product = await unitOfWork.ProductRepository.GetByIdAsync(model.ProductId);
                var productLinks = GetProductFileEmailLinks(product, model.FileIds);

                var emailsPath = Path.Combine(_webHostEnvironment.WebRootPath, "Emails");
                EmailNotificationHelper emailNotificationHelper = EmailNotificationHelper.Create(emailsPath);
                await emailNotificationHelper.SendProductShareEmailAsync(model.RecipientEmail, product.Name, product.Description, productLinks);

                return Ok();
            }
        }

        private ICollection<EmailLinkDto> GetProductFileEmailLinks(Product product, ICollection<int> fileIds)
        {
            var hostUrl = ConfigurationHelper.GetValue("MarketApiBaseUrl");
            var downloadMethodUrl = "api/File/Download";
            var fileUrlPart = "?fileId=";
            var productUrlPart = "&productId=";
            var manufacturerUrlPart = "&manufacturerId=";

            return product.ProductFiles.Where(pf => fileIds.Contains(pf.FileId))
                .Select(b => new EmailLinkDto
                {
                    Name = b.File.FileName,
                    Url = hostUrl + downloadMethodUrl + fileUrlPart + b.File.Id + productUrlPart + product.Id + manufacturerUrlPart + product.ManufacturerId
                })
                .ToList();
        }
    }
}
