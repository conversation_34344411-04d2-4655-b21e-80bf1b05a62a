﻿using System;
using System.Collections.Generic;

namespace BIMsmithBlog.Models
{
    public class BlogsList : CommonModel
    {
        public BlogPreview HeadBlog { get; set; }

        public List<BlogPreview> RecentBlogs { get; set; }

        public bool IsCategoryPage { get; set; }

        public string SearchQuery { get; set; }

        public int PagesCount { get; set; }

        public int CurrentPage { get; set; }

        public bool IsAll { get; set; }
    }


    public class BlogPreview
    {
        public int Id { get; set; }

        public string VanityId { get; set; }

        public string Title { get; set; }

        public string AuthorTitle { get; set; }

        public string AuthorEmail { get; set; }

        public string AuthorImage { get; set; }

        public string Descriptions { get; set; }

        public string ImageUrlBig { get; set; }

        public string ImageUrlSmall { get; set; }

        public string RegionIds { get; set; }

        public CategoryModel Category { get; set; }

        public DateTime PublishedDate { get; set; }
    }


    public class CategoryModel
    {
        public string VanityId { get; set; }

        public string Name { get; set; }

        public bool IsActive { get; set; }
    }
}