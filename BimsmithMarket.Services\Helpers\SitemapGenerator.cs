﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Flurl;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Xml;

namespace BIMsmithMarket.Services.Helpers
{
    public class SitemapGenerator
    {
        public async Task<byte[]> GetIndexSitemapAsync()
        {
            string baseUrl = ConfigurationHelper.GetValue("MarketFrontBaseUrl");
            string[] sitemapList = GetSitemapList();
            return GenerateIndexSitemap(baseUrl, sitemapList);
        }

        public async Task<byte[]> GetMainSitemapAsync()
        {
            string baseUrl = ConfigurationHelper.GetValue("MarketFrontBaseUrl");
            List<SitemapNode> nodes = await GetSitemapMainNodesAsync(baseUrl);
            return GenerateSitemap(nodes);
        }

        public async Task<byte[]> GetProductSitemapAsync(IUnitOfWork unitOfWork)
        {
            string baseUrl = ConfigurationHelper.GetValue("MarketFrontBaseUrl");
            List<SitemapNode> nodes = await GetSitemapProductNodesAsync(baseUrl, unitOfWork);
            return GenerateSitemap(nodes);
        }

        public async Task<byte[]> GetManufacturerSitemapAsync(IUnitOfWork unitOfWork)
        {
            string baseUrl = ConfigurationHelper.GetValue("MarketFrontBaseUrl");
            List<SitemapNode> nodes = await GetSitemapManufacturerNodesAsync(baseUrl, unitOfWork);
            return GenerateSitemap(nodes);
        }

        public async Task<byte[]> GetCategorySitemapAsync(IUnitOfWork unitOfWork)
        {
            string baseUrl = ConfigurationHelper.GetValue("MarketFrontBaseUrl");
            List<SitemapNode> nodes = await GetSitemapCategoryNodesAsync(baseUrl, unitOfWork);
            return GenerateSitemap(nodes);
        }

        public async Task<byte[]> GetProjectDataSitemapAsync(IUnitOfWork unitOfWork)
        {
            string baseUrl = ConfigurationHelper.GetValue("MarketFrontBaseUrl");
            List<SitemapNode> nodes = await GetSitemapProjectDataTypeNodesAsync(baseUrl, unitOfWork);
            return GenerateSitemap(nodes);
        }

        public async Task<byte[]> GetProjectDataTypeSitemapAsync(IUnitOfWork unitOfWork)
        {
            string baseUrl = ConfigurationHelper.GetValue("MarketFrontBaseUrl");
            List<SitemapNode> nodes = await GetSitemapProjectDataTypeNodesAsync(baseUrl, unitOfWork);
            return GenerateSitemap(nodes);
        }

        public async Task<byte[]> GetMasteformatSitemapAsync(IMasterformatService masterformatService)
        {
            string baseUrl = ConfigurationHelper.GetValue("MarketFrontBaseUrl");
            List<SitemapNode> nodes = await GetSitemapMasterformatNodesAsync(baseUrl, masterformatService);
            return GenerateSitemap(nodes);
        }

        public static ICollection<SitemapNode> GetBimsmithHelpNodes(string baseUrl)
        {
            var categoryPriority = 0.8;
            var categoryFrequence = SitemapFrequency.Weekly;
            var articlePriority = 0.9;
            var articleFrequence = SitemapFrequency.Daily;
            using (var unitOfWork = UnitOfWork.Create())
            {
                var helpNodes = new List<SitemapNode>();

                var helpCategories = unitOfWork.HelpCategoryRepository.GetAll().Where(x => x.Status == HelpCategoryStatus.Published).ToList();
                foreach (var category in helpCategories)
                {
                    helpNodes.Add(new SitemapNode
                    {
                        Loc = string.Concat(baseUrl, "/help/", category.VanityUrl),
                        ChangeFreq = categoryFrequence,
                        Lastmod = category.ModifiedDate ?? category.CreatedDate,
                        Priority = categoryPriority
                    });
                }

                var helpArticles = unitOfWork.HelpArticleRepository.GetAll().Where(x => x.Status == HelpCategoryStatus.Published).ToList();
                foreach (var article in helpArticles)
                {
                    var articleCategories = article.HelpCategories.Where(x => x.Status == HelpCategoryStatus.Published).ToList();
                    foreach (var category in articleCategories)
                    {
                        helpNodes.Add(new SitemapNode
                        {
                            Loc = string.Concat(baseUrl, "/help/", category.VanityUrl, "/", article.VanityUrl),
                            ChangeFreq = articleFrequence,
                            Lastmod = article.ModifiedDate ?? article.CreatedDate,
                            Priority = articlePriority
                        });
                    }
                }
                return helpNodes;
            }
        }

        #region private methods
        private string[] GetSitemapList()
        {
            return
            [
                "main-sitemap.xml",
                "product-sitemap.xml",
                "manufacturer-sitemap.xml",
                "category-sitemap.xml",
                "project-data-type-sitemap.xml",
                "masterformat-sitemap.xml"
            ];
        }

        private async Task<List<SitemapNode>> GetSitemapMainNodesAsync(string baseUrl)
        {
            List<SitemapNode> nodes = [];

            nodes.Add(new SitemapNode
            {
                Loc = baseUrl,
                ChangeFreq = SitemapFrequency.Hourly,
                Lastmod = DateTime.UtcNow,
                Priority = 1.0
            });

            return nodes;
        }

        private async Task<List<SitemapNode>> GetSitemapProductNodesAsync(string baseUrl, IUnitOfWork unitOfWork)
        {
            List<SitemapNode> nodes = [];
            var products = await unitOfWork.ProductRepository.GetAll()
                .Where(a => a.Published && a.Manufacturer.Published)
                .Select(p => new
                {
                    Id = p.Id,
                    Name = p.Name,
                    ManufacturerName = p.Manufacturer.Name,
                    LastModify = p.ModifiedDate ?? p.CreatedDate
                })
                .ToArrayAsync();
            string productsBaseUrl = new Url(baseUrl).AppendPathSegment("/product/");

            foreach (var product in products)
            {
                nodes.Add(new SitemapNode
                {
                    Loc = new Url(productsBaseUrl).AppendPathSegment(StringHelper.FixUrlString(product.ManufacturerName)).AppendPathSegment("/revit-bim-" + StringHelper.FixUrlString(product.Name) + "-" + product.Id),
                    ChangeFreq = SitemapFrequency.Monthly,
                    Lastmod = product.LastModify,
                    Priority = 0.7
                });
            }

            return nodes;
        }

        private async Task<List<SitemapNode>> GetSitemapManufacturerNodesAsync(string baseUrl, IUnitOfWork unitOfWork)
        {
            List<SitemapNode> nodes = [];
            var manufacturers = await unitOfWork.ManufacturerRepository.GetAll()
                .Where(a => a.Published)
                .Select(m => new
                {
                    Name = m.Name,
                    HubVanityURL = m.HubVanityURL,
                    LastModify = m.ModifiedDate ?? m.CreatedDate
                })
                .ToArrayAsync();
            string manufacturersBaseUrl = new Url(baseUrl).AppendPathSegment("/manufacturer/");

            //Urls of manufacturers
            foreach (var manufacturer in manufacturers)
            {
                string name = manufacturer.Name;

                if (manufacturer.Name.EndsWith("."))
                {
                    name = name + "_";
                }

                SitemapNode node = new SitemapNode
                {
                    ChangeFreq = SitemapFrequency.Monthly,
                    Lastmod = manufacturer.LastModify,
                    Priority = 0.8
                };

                if (string.IsNullOrWhiteSpace(manufacturer.HubVanityURL))
                {
                    node.Loc = manufacturersBaseUrl + Uri.EscapeDataString(name);
                }
                else
                {
                    node.Loc = new Url(baseUrl).AppendPathSegment(Uri.EscapeDataString(manufacturer.HubVanityURL));
                }

                nodes.Add(node);
            }

            return nodes;
        }

        private async Task<List<SitemapNode>> GetSitemapCategoryNodesAsync(string baseUrl, IUnitOfWork unitOfWork)
        {
            List<SitemapNode> nodes = [];
            var categories = await unitOfWork.CategoryRepository.GetAll()
                .Select(a => new
                {
                    Id = a.Id,
                    VanityUrl = a.VanityUrl,
                    LastModify = a.ModifiedDate ?? a.CreatedDate
                })
                .ToArrayAsync();
            string categoryBaseUrl = new Url(baseUrl).AppendPathSegment("/category/");

            //Urls of categories
            foreach (var category in categories)
            {
                string location = new Url(baseUrl).AppendQueryParam("categoryId", category.Id);

                if (string.IsNullOrEmpty(category.VanityUrl) == false)
                {
                    location = new Url(baseUrl).AppendPathSegment("category").AppendPathSegment(Uri.EscapeDataString(category.VanityUrl));
                }

                nodes.Add(new SitemapNode
                {
                    Loc = location,
                    ChangeFreq = SitemapFrequency.Daily,
                    Lastmod = category.LastModify,
                    Priority = 0.9
                });
            }

            return nodes;
        }

        private async Task<List<SitemapNode>> GetSitemapProjectDataTypeNodesAsync(string baseUrl, IUnitOfWork unitOfWork)
        {
            List<SitemapNode> nodes = [];
            var projectDataTypes = await unitOfWork.ProjectDataTypeRepository.GetAll()
               .Where(x => x.ParentId == null)
               .Select(a => new
               {
                   Title = a.Title,
                   VanityUrl = a.VanityUrl,
                   LastModify = a.ModifiedDate ?? a.CreatedDate
               })
               .ToArrayAsync();

            foreach (var type in projectDataTypes)
            {
                string location = new Url(baseUrl).AppendPathSegment(type.Title);

                if (!string.IsNullOrEmpty(type.VanityUrl))
                {
                    location = new Url(baseUrl).AppendPathSegment(type.VanityUrl);
                }

                nodes.Add(new SitemapNode
                {
                    Loc = location,
                    ChangeFreq = SitemapFrequency.Weekly,
                    Lastmod = type.LastModify,
                    Priority = 0.9
                });
            }

            return nodes;
        }

        private async Task<List<SitemapNode>> GetSitemapMasterformatNodesAsync(string baseUrl, IMasterformatService masterformatService)
        {
            List<SitemapNode> nodes = [];
            var masterformats = (await masterformatService.GetBackofficeMasterformatsAsync())
                .Select(a => new
                {
                    Title = a.Title,
                    Code = a.Code,
                    LastModify = DateTime.UtcNow,
                });

            foreach (var masterformat in masterformats)
            {
                nodes.AddRange(new List<SitemapNode>
                {
                    new SitemapNode
                    {
                        Loc = new Url(baseUrl).AppendPathSegment("masterformats").AppendPathSegment(masterformat.Code.Replace(' ', '-')),
                        ChangeFreq = SitemapFrequency.Weekly,
                        Lastmod = masterformat.LastModify,
                        Priority = 0.5
                    },
                    new SitemapNode
                    {
                        Loc = new Url(baseUrl).AppendPathSegment("masterformats").AppendPathSegment(masterformat.Title),
                        ChangeFreq = SitemapFrequency.Weekly,
                        Lastmod = masterformat.LastModify,
                        Priority = 0.5
                    },
                });
            }

            return nodes;
        }

        private byte[] GenerateIndexSitemap(string baseUrl, IEnumerable<string> sitemapLocations)
        {
            using (var memoryStream = new MemoryStream())
            {
                var settings = new XmlWriterSettings();
                settings.Indent = true;
                settings.IndentChars = "     ";
                settings.NewLineOnAttributes = false;

                using (var writer = XmlWriter.Create(memoryStream, settings))
                {
                    writer.WriteStartDocument();
                    string ns = "http://www.sitemaps.org/schemas/sitemap/0.9";
                    writer.WriteStartElement("sitemapindex", ns);
                    writer.WriteAttributeString("xmlns", ns);

                    foreach (var sitemapLocation in sitemapLocations)
                    {
                        writer.WriteStartElement("sitemap");
                        string location = new Url(baseUrl).AppendPathSegment(sitemapLocation);
                        writer.WriteElementString("loc", location);
                        writer.WriteEndElement();
                    }
                    writer.WriteEndElement();
                    writer.WriteEndDocument();
                    writer.Flush();
                }
                return memoryStream.ToArray();
            }
        }

        private byte[] GenerateSitemap(IEnumerable<SitemapNode> nodes)
        {
            using (var memoryStream = new MemoryStream())
            {
                var settings = new XmlWriterSettings();
                settings.Indent = true;
                settings.IndentChars = "     ";
                settings.NewLineOnAttributes = false;

                using (var writer = XmlWriter.Create(memoryStream, settings))
                {
                    writer.WriteStartDocument();
                    string ns = "http://www.sitemaps.org/schemas/sitemap/0.9";
                    writer.WriteStartElement("urlset", ns);
                    writer.WriteAttributeString("xmlns", ns);

                    foreach (var node in nodes)
                    {
                        writer.WriteStartElement("url");

                        if (!string.IsNullOrEmpty(node.Loc))
                        {
                            writer.WriteElementString("loc", node.Loc);
                        }
                        if (node.Lastmod.HasValue)
                        {
                            writer.WriteElementString("lastmod", node.Lastmod.Value.Date.ToString("yyyy-MM-dd"));
                        }
                        if (node.ChangeFreq.HasValue)
                        {
                            writer.WriteElementString("changefreq", node.ChangeFreq.Value.ToString());
                        }
                        if (node.Priority.HasValue)
                        {
                            writer.WriteElementString("priority", node.Priority.Value.ToString());
                        }

                        writer.WriteEndElement();
                    }
                    writer.WriteEndElement();
                    writer.WriteEndDocument();
                    writer.Flush();
                }
                return memoryStream.ToArray();
            }
        }
        #endregion
    }

    public class SitemapNode
    {
        public string Loc { get; set; }

        public DateTime? Lastmod { get; set; }

        public double? Priority { get; set; }

        public SitemapFrequency? ChangeFreq { get; set; }
    }

    public enum SitemapFrequency
    {
        Never,
        Yearly,
        Monthly,
        Weekly,
        Daily,
        Hourly,
        Always
    }
}