﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Helpers;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Services
{
    public class CompanyService : ICompanyService
    {
        private readonly IHttpClientFactory _httpClientFactory;

        public CompanyService(IHttpClientFactory httpClientFactory)
        {
            _httpClientFactory = httpClientFactory;
        }

        public async Task<object> ListAsync(IUnitOfWork unitOfWork, int offset, int count)
        {
            var companies = unitOfWork.CompanyRepository.GetAll().OrderByDescending(x => x.CreatedDate);
            var dataCount = await companies.CountAsync();
            var list = await companies.Select(a => new
            {
                id = a.Id,
                companyName = a.CompanyName,
                companyEmailDomain = a.EmailDomain,
                matchCompanyUsers = a.MatchCompanyUsers,
            })
            .Skip(offset)
            .Take(count)
            .AsNoTracking()
            .ToListAsync();

            return new
            {
                count = dataCount,
                data = list
            };
        }

        public async Task<object> AddAsync(IUnitOfWork unitOfWork, AddCompanyModel model)
        {
            var company = new Company
            {
                CompanyName = model.CompanyName,
                EmailDomain = model.EmailDomain,
                MatchCompanyUsers = model.MatchCompanyUsers
            };

            unitOfWork.CompanyRepository.Insert(company);

            await unitOfWork.SaveAsync();

            return new
            {
                id = company.Id,
                companyName = company.CompanyName,
                emailDomain = company.EmailDomain,
                matchCompanyUsers = company.MatchCompanyUsers
            };
        }

        public async Task<object> EditAsync(IUnitOfWork unitOfWork, EditCompanyModel model)
        {
            var company = await unitOfWork.CompanyRepository.GetAll().FirstOrDefaultAsync(x => x.Id == model.Id);
            if (company == null)
            {
                throw new DbItemNotFoundException("Can't find company");
            }

            company.CompanyName = model.CompanyName;
            company.EmailDomain = model.EmailDomain;
            company.MatchCompanyUsers = model.MatchCompanyUsers;

            unitOfWork.CompanyRepository.Edit(company);

            await unitOfWork.SaveAsync();

            return new
            {
                id = company.Id,
                companyName = company.CompanyName,
                emailDomain = company.EmailDomain,
                matchCompanyUsers = company.MatchCompanyUsers
            };
        }

        public async Task<object> GetExcelDataAsync(IUnitOfWork unitOfWork, int fileId)
        {
            var excelFile = await unitOfWork.FileRepository.GetByIdAsync(fileId);
            if (excelFile == null)
            {
                throw new DbItemNotFoundException($"There i sno file with id {fileId}");
            }
            ExcelParser excelParser = new ExcelParser();
            var companyDataResult = excelParser.ParseCompanyResult(excelFile);
            if (companyDataResult.Errors.Any())
            {
                throw new InvalidInputException($"Pars Errors: {JsonConvert.SerializeObject(companyDataResult.Errors)}");
            }

            unitOfWork.BeginTransaction();
            try
            {
                foreach (var companyData in companyDataResult.Items)

                {
                    var company = await unitOfWork.CompanyRepository.GetAll().FirstOrDefaultAsync(x => x.EmailDomain == companyData.EmailDomain);
                    if (company == null)
                    {
                        Company newCompany = new Company()
                        {
                            CompanyName = companyData.CompanyName,
                            EmailDomain = companyData.EmailDomain,
                            MatchCompanyUsers = companyData.MatchCompanyUsers
                        };
                        unitOfWork.CompanyRepository.Insert(newCompany);
                    }
                    else
                    {
                        company.CompanyName = companyData.CompanyName;
                        company.MatchCompanyUsers = companyData.MatchCompanyUsers;

                        unitOfWork.CompanyRepository.Edit(company);
                    }
                };
                try
                {
                    await unitOfWork.SaveAsync();
                    unitOfWork.CommitTransaction();
                    return "Import has been done.";
                }
                catch (Exception e)
                {
                    unitOfWork.RollbackTransaction();
                    throw new InvalidInputException($"Can't save results of Company parsing: {e.Message}");
                }
            }
            catch (Exception ex)
            {
                unitOfWork.RollbackTransaction();
                throw new InvalidInputException($"Internal parsing Error: {ex.Message}");
            }
        }

        public async Task<object> SyncAsync(IUnitOfWork unitOfWork, string documentDBAuthKey, string bimsmith, string controller, int id)
        {
            var endPoint = Path.Combine(bimsmith, controller);

            var company = await unitOfWork.CompanyRepository.GetByIdAsync(id);
            if (company == null)
            {
                throw new DbItemNotFoundException($"Can't find company by id={id}.");
            }

            var companyDomain = company.EmailDomain;
            var companyName = company.CompanyName;

            var values = new Dictionary<string, string>
                {
                   { "CompanyDomain", companyDomain },
                   { "AuthKey", documentDBAuthKey },
                   { "CompanyName", companyName}
                };
            var content = new FormUrlEncodedContent(values);
            HttpClient client = _httpClientFactory.CreateClient();
            {
                using var response = await client.PostAsync(endPoint, content);
                if (response.StatusCode != HttpStatusCode.OK)
                {
                    throw new InvalidInputException($"Can't create BIMsmithMarket task.");
                }

                return new
                {
                    message = "Job for sync was created success.",
                    usersUpdated = response.Content
                };
            }
        }

        public async Task<object> CompanyInfoAsync(IUnitOfWork unitOfWork)
        {
            var companies = await unitOfWork.CompanyRepository.GetAll().Where(x => x.MatchCompanyUsers).Select(a => new
            {
                CompanyDomain = a.EmailDomain,
                CompanyName = a.CompanyName
            })
            .AsNoTracking()
            .ToListAsync();

            if (!companies.Any())
            {
                throw new DbItemNotFoundException();
            }

            return companies;
        }
    }
}
