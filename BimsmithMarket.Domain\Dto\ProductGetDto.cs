﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto.DetailDto;
using System;
using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Dto
{
    public class ProductGetDto
    {
        public int Id { get; set; }

        public int CategoryId { get; set; }

        public string Note { get; set; }

        public int ManufacturerId { get; set; }

        public bool Published { get; set; }

        public bool IsInMyBIMSmith { get; set; }

        public bool PublishToPartner { get; set; }

        public bool Staging { get; set; }

        public bool HideOnMicrosite { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }

        public string ProductUrl { get; set; }

        public string VideoUrl { get; set; }

        public float ProductRating { get; set; }

        public float ContentRating { get; set; }

        public int ContentRatingCount { get; set; }

        public bool IsFeatured { get; set; }

        public string VanityURL { get; set; }

        public string MetaTitle { get; set; }

        public string MetaDescription { get; set; }

        public string MetaKeywords { get; set; }

        public string Keywords { get; set; }

        public float Weight { get; set; }

        public bool IsImperialDefault { get; set; }

        public string ForgeWallURL { get; set; }

        public string ForgeFloorURL { get; set; }

        public string ForgeCeilingURL { get; set; }

        public string ForgeRoofURL { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        public string SwatchboxProductId { get; set; }

        public string SwatchboxOptionId { get; set; }

        public bool DisplaySwatchboxProductOnProductPage { get; set; }

        public bool DisplaySwatchboxProductOnMicrosite { get; set; }

        public ULSyncStatus UlSyncStatus { get; set; }

        public string UlURL { get; set; }

        public string AssemblyCode { get; set; }

        public string FooterAdUrl { get; set; }

        public int? FooterAdImageId { get; set; }

        public DateTime UpdateDate { get; set; }

        public bool PublishedOnCustomMicrosite { get; set; }

        public dynamic Category { get; set; }

        public ManufacturerDto Manufacture { get; set; }

        public ProductLineDto ProductLine { get; set; }

        public PhotoProductWithStatusDto Photo { get; set; }

        public string ExternalProductId { get; set; }

        public FooterAdImageDto FooterAdImage { get; set; }

        public IEnumerable<PhotoProductWithMainParamDto> ProductPhotos { get; set; }

        public IEnumerable<int> Samples { get; set; }

        public IEnumerable<ProductGetFileDto> UlProductFiles { get; set; }

        public IEnumerable<ProjectGetFileWithVersionDto> ProjectFiles { get; set; }

        public IEnumerable<ProductGetFileWithRegionDto> ProductFiles { get; set; }

        public IEnumerable<RelatedProductDto> RelatedProducts { get; set; }

        public IEnumerable<ProductStatsDto> ProductStats { get; set; }

        public IEnumerable<int> ExternalCertificates { get; set; }

        public IEnumerable<dynamic> Categories { get; set; }

        public IEnumerable<QualityItemDto> QualityItems { get; set; }

        public IEnumerable<int> ExternalMasterformatIds { get; set; }

        public IEnumerable<FormatDto> Omniclasses { get; set; }

        public IEnumerable<FormatDto> Uniformats { get; set; }

        public IEnumerable<FormatDto> Uniclasses { get; set; }

        public IEnumerable<int> CategoryIds { get; set; }

        public IEnumerable<FormatDto> Cisfbs { get; set; }

        public int ProductRatingCount { get; set; }

        public IEnumerable<ProductDetailDto> Details { get; set; }
    }
}