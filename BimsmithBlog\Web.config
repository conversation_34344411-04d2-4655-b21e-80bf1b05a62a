﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <clear />
        <rule name="Redirect to https" stopProcessing="true">
          <match url="(.*)" />
          <conditions>
            <add input="{HTTPS}" pattern="^OFF$" />
          </conditions>
          <action type="Redirect" url="https://{HTTP_HOST}{REQUEST_URI}" appendQueryString="false" redirectType="Permanent" />
        </rule>
        <rule name="Redirect to Swatchbox blog">
          <match url="Top-Interior-Design-Quotes-Design-Quotes-from-Interior-Designers-2021" />
          <action type="Redirect" url="https://www.swatchbox.com/blog/Top-Interior-Design-Quotes-Design-Quotes-from-Interior-Designers-2021" />
        </rule>
      </rules>
    </rewrite>    
    <httpProtocol>
      <customHeaders>
        <add name="Referrer-Policy" value="no-referrer-when-downgrade" />
      </customHeaders>
    </httpProtocol>
  </system.webServer>
</configuration>