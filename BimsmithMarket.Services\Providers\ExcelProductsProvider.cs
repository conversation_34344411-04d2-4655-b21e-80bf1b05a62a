﻿using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.Core.Extentions;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Domain.Models.ExcelModels.StaticExcel;
using BIMsmithMarket.Services.HealthDashboardServices;
using BIMsmithMarket.Services.Helpers;
using BIMsmithMarket.Services.Interfaces;
using BIMsmithMarket.Services.PaymentServices;
using BIMsmithMarket.Services.ProductServices;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Providers
{
    public static class ExcelProductsProvider
    {
        private static readonly IPriceService _priceService = new PriceService(new FileService());
        private static readonly IProductService _productService = new ProductService(_priceService, new MongoRepository<ProductMongoDto>(), new CacheService(), new SlackWebHook(), new HealthDashboardService(), new FileService());
        private static readonly ICacheService _cacheService = new CacheService();

        public static string GetExcel(ProductsExcelModel model, string tempFolder)
        {
            if (!Directory.Exists(tempFolder))
            {
                Directory.CreateDirectory(tempFolder);
            }

            var filePath = Path.Combine(tempFolder, $"manufacturer_{Guid.NewGuid()}.xlsx");


            // Create a spreadsheet document by supplying the filepath.
            // By default, AutoSave = true, Editable = true, and Type = xlsx.
            SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Create(filePath, SpreadsheetDocumentType.Workbook);

            // Add a WorkbookPart to the document.
            WorkbookPart workbookpart = spreadsheetDocument.AddWorkbookPart();
            workbookpart.Workbook = new Workbook();

            // Add a WorksheetPart to the WorkbookPart.
            WorksheetPart worksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            worksheetPart.Worksheet = new Worksheet();


            Fonts fonts = new Fonts(
                new DocumentFormat.OpenXml.Spreadsheet.Font( // Index 0 - default
                    new FontSize() { Val = 10 }
                ));

            Fills fills = new Fills(
                    new Fill(new PatternFill() { PatternType = PatternValues.None }), // Index 0 - default
                    new Fill(new PatternFill(new ForegroundColor { Rgb = new HexBinaryValue() { Value = "FFE7E6E6" } }) { PatternType = PatternValues.Solid }), // Index 1 - default
                    new Fill(new PatternFill(new ForegroundColor { Rgb = new HexBinaryValue() { Value = "FFBDD7EE" } }) { PatternType = PatternValues.Solid }) // Index 2 - header
                );

            Borders borders = new Borders(
                    new Border() // index 0 default
                );

            CellFormats cellFormats = new CellFormats(
                    new CellFormat(), // default
                    new CellFormat { FontId = 0, FillId = 1, BorderId = 0, ApplyFill = true }, // body
                    new CellFormat { FontId = 0, FillId = 2, BorderId = 0, ApplyFill = true }, // header
                    new CellFormat { FontId = 0, FillId = 2, BorderId = 0, ApplyFill = true, Alignment = new Alignment { TextRotation = 90, WrapText = true } } // header for certificate
                );


            Stylesheet styleSheet = new Stylesheet(fonts, fills, borders, cellFormats);

            // Adding style
            WorkbookStylesPart stylePart = workbookpart.AddNewPart<WorkbookStylesPart>();
            stylePart.Stylesheet = styleSheet;
            stylePart.Stylesheet.Save();

            //columns
            Columns columns = new Columns();

            uint headerColumnIndex = 1;
            int columnsCount = 50;
            for (int i = 0; i < columnsCount; i++)
                columns.AppendChild(new Column { Min = headerColumnIndex, Max = headerColumnIndex++, Width = 25, CustomWidth = true });

            int startIndex = (int)headerColumnIndex;

            var distinctProjectTypes = model.Products.SelectMany(p => p.ProjectFiles)
                                            .Where(a => a.FileType != null)
                                            .Select(a => a.FileType)
                                            .Concat(model.RevitProjectTypes)
                                            .Distinct(new ProjectDataTypeModelComparer())
                                            .OrderBy(x => x.Title)
                                            .ToList();

            foreach (var projType in distinctProjectTypes)
            {
                columns.AppendChild(new Column { Min = (uint)startIndex, Max = (uint)startIndex, Width = 25, CustomWidth = true });
                startIndex++;
            }

            foreach (var qualityItem in model.QualityItems)
            {
                columns.AppendChild(new Column { Min = (uint)startIndex, Max = (uint)startIndex, Width = 5, CustomWidth = true });
                startIndex++;
            }

            var distinctProductStats = model.Products.SelectMany(p => p.ProductStats).Distinct(new ProductStatModelComparer())
                .OrderBy(a => a.KeyStatType)
                .ThenBy(a => a.KeyStatUnit?.GroupName).ThenBy(a => a.KeyStatUnit?.BUnitName)
                .ThenBy(a => a.KeyStatValueList, new ProductStatKeyStatValueListModelComparer()).ThenBy(a => a.KeyStatValueList, new ProductStatKeyStatValueListModelComparer())
                .ThenBy(a => a.KeyStatName)
                .ToList();

            foreach (var productStat in distinctProductStats)
            {
                columns.AppendChild(new Column { Min = (uint)startIndex, Max = (uint)startIndex, Width = 25, CustomWidth = true });
                startIndex++;
            }

            worksheetPart.Worksheet.AppendChild(columns);
            workbookpart.Workbook.Save();

            //Sheets
            Sheets sheets = workbookpart.Workbook.AppendChild(new Sheets());

            //Append a new worksheet and associate it with the workbook.
            Sheet sheet = new Sheet()
            {
                Id = spreadsheetDocument.WorkbookPart.
                GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = model.FileName.Length > 30 ? model.FileName.Substring(0, 30) : model.FileName
            };
            sheets.Append(sheet);
            workbookpart.Workbook.Save();

            // Get the sheetData cell table.
            SheetData sheetData = worksheetPart.Worksheet.AppendChild(new SheetData());

            int rowIndex = 1;
            int columnIndex = 1;

            if (distinctProductStats.Any())
            {
                Row rowHeader1 = new Row();
                sheetData.AppendChild(rowHeader1);

                Row rowHeader2 = new Row();
                sheetData.AppendChild(rowHeader2);

                Row rowHeader3 = new Row();
                sheetData.AppendChild(rowHeader3);

                for (int ci = 1; ci < startIndex - distinctProductStats.Count; ci++)
                {
                    rowHeader1.AppendChild(CommonExcelProvider.ConstructCell(string.Empty, CellValues.String, ci, rowIndex, 2));
                    rowHeader2.AppendChild(CommonExcelProvider.ConstructCell(string.Empty, CellValues.String, ci, rowIndex + 1, 2));
                    rowHeader3.AppendChild(CommonExcelProvider.ConstructCell(string.Empty, CellValues.String, ci, rowIndex + 2, 2));
                }

                columnIndex = startIndex - distinctProductStats.Count;
                foreach (var productStat in distinctProductStats)
                {
                    rowHeader1.AppendChild(CommonExcelProvider.ConstructCell(productStat.KeyStatType.ToString(), CellValues.String, columnIndex, rowIndex, 2));
                    if (productStat.KeyStatType == KeyStatType.SingleNumeric || productStat.KeyStatType == KeyStatType.NumericRange)
                    {
                        ProductStatKeyStatUnitModel keyStatUnit = new ProductStatKeyStatUnitModel
                        {
                            GroupName = string.Empty,
                            BUnitName = string.Empty
                        };
                        if (productStat.KeyStatUnit != null)
                        {
                            keyStatUnit = productStat.KeyStatUnit;
                        }
                        else if (productStat.KeyStatValueList != null && productStat.KeyStatValueList.Any())
                        {
                            var valueListFirst = productStat.KeyStatValueList.FirstOrDefault();
                            if (valueListFirst != null && valueListFirst.KeyStatUnit != null)
                            {
                                keyStatUnit = valueListFirst.KeyStatUnit;
                            }
                        }
                        rowHeader2.AppendChild(CommonExcelProvider.ConstructCell(keyStatUnit.GroupName, CellValues.String, columnIndex, rowIndex + 1, 2));
                        rowHeader3.AppendChild(CommonExcelProvider.ConstructCell(keyStatUnit.BUnitName, CellValues.String, columnIndex, rowIndex + 2, 2));
                    }
                    else
                    {
                        rowHeader2.AppendChild(CommonExcelProvider.ConstructCell(string.Empty, CellValues.String, columnIndex, rowIndex + 1, 2));
                        rowHeader3.AppendChild(CommonExcelProvider.ConstructCell(string.Empty, CellValues.String, columnIndex, rowIndex + 2, 2));
                    }
                    columnIndex++;
                }

                rowIndex = 4;
            }

            Row rowHeader4 = new Row();
            rowHeader4.Height = 90;
            rowHeader4.CustomHeight = true;

            sheetData.AppendChild(rowHeader4);

            columnIndex = 1;

            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.IdCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.NameCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.ManufacturerIdCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.CategoryIdCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.AdditionalCategoryIdsCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.ProductLineIdCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.PhotoIdCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.PhotoURLsCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.DescriptionCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.VideoUrlCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.VanityURLCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.MetaTitleCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.MetaDescriptionCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.MetaKeywordsCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.KeywordsCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.CertificatesCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.MaterformatsCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.OmniclassesCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.UniclassesCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.UniformatsCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.CisfbsCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.ProductCutSheetUrlsCaption, CellValues.String, columnIndex++, rowIndex, 3));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.PartSpecUrlsCaption, CellValues.String, columnIndex++, rowIndex, 3));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.ProductBrochureUrlsCaption, CellValues.String, columnIndex++, rowIndex, 3));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.ImageUrlsCaption, CellValues.String, columnIndex++, rowIndex, 3));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.SubmittalUrlsCaption, CellValues.String, columnIndex++, rowIndex, 3));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.CatalogUrlsCaption, CellValues.String, columnIndex++, rowIndex, 3));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.WarrantyUrlsCaption, CellValues.String, columnIndex++, rowIndex, 3));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.TestingDataUrlsCaption, CellValues.String, columnIndex++, rowIndex, 3));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.SafetyDataSheetUrlsCaption, CellValues.String, columnIndex++, rowIndex, 3));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.InstallationGuideUrlsCaption, CellValues.String, columnIndex++, rowIndex, 3));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.HpdUrlsCaption, CellValues.String, columnIndex++, rowIndex, 3));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.EpdUrlsCaption, CellValues.String, columnIndex++, rowIndex, 3));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.MasterPartSpecUrlsCaption, CellValues.String, columnIndex++, rowIndex, 3));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.PublishedCaption, CellValues.String, columnIndex++, rowIndex, 3));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.PublishedOnCustomMicrositeCaption, CellValues.String, columnIndex++, rowIndex, 3));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.StagingCaption, CellValues.String, columnIndex++, rowIndex, 3));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.ProductUrlCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.ULUrlCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.RevitFilesCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.ExternalIdCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.PublishToPartnerCaption, CellValues.String, columnIndex++, rowIndex, 3));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.ForgeWallURLCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.ForgeFloorURLCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.ForgeCeilingURLCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.ForgeRoofURLCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.WeightCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.LocalizationCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.AttachmentsCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(StaticExcelConstants.ProjectFilesCaption, CellValues.String, columnIndex++, rowIndex, 2));

            foreach (var projectDataType in distinctProjectTypes)
            {
                rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(projectDataType.Title, CellValues.String, columnIndex++, rowIndex, 3));
            }

            foreach (var qualityItem in model.QualityItems)
            {
                rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(qualityItem.Name, CellValues.String, columnIndex++, rowIndex, 3));
            }

            foreach (var productStat in distinctProductStats)
            {
                rowHeader4.AppendChild(CommonExcelProvider.ConstructCell(productStat.KeyStatName, CellValues.String, columnIndex++, rowIndex, 2));
            }

            rowIndex++;
            foreach (var product in model.Products)
            {
                Row row = new Row();
                sheetData.AppendChild(row);

                columnIndex = 1;

                row.AppendChild(CommonExcelProvider.ConstructCell(product.Id?.ToString(), CellValues.Number, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.Name, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.ManufacturerId.ToString(), CellValues.Number, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.CategoryId.ToString(), CellValues.Number, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(";", product.ProductCategoryIds), CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.ProductLineId?.ToString(), CellValues.Number, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.PhotoId?.ToString(), CellValues.Number, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join("|", product.PhotoURLs), CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.Description, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.VideoUrl, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.VanityURL, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.MetaTitle, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.MetaDescription, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.MetaKeywords, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.Keywords, CellValues.String, columnIndex++, rowIndex));

                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(";", product.ProductCertificates), CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(";", product.ProductExternalMasterformatCodes), CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(";", product.ProductOmniclasses), CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(";", product.ProductUniclasses), CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(";", product.ProductUniformats), CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(";", product.ProductCisfbs), CellValues.String, columnIndex++, rowIndex));

                var productCuSheetUrls = product.Attachments.Where(pf => pf.FileTitle == "Product Cut Sheet").Select(pf => pf.FileSyncUrl).ToList();
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join("|", productCuSheetUrls), CellValues.String, columnIndex++, rowIndex));

                var partSpecUrls = product.Attachments.Where(pf => pf.FileTitle == "3-Part Specification").Select(pf => pf.FileSyncUrl).ToList();
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join("|", partSpecUrls), CellValues.String, columnIndex++, rowIndex));

                var productBrochureUrls = product.Attachments.Where(pf => pf.FileTitle == "Product Brochure").Select(pf => pf.FileSyncUrl).ToList();
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join("|", productBrochureUrls), CellValues.String, columnIndex++, rowIndex));

                var imageUrls = product.Attachments.Where(pf => pf.FileTitle == "Image").Select(pf => pf.FileSyncUrl).ToList();
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join("|", imageUrls), CellValues.String, columnIndex++, rowIndex));

                var submittalUrls = product.Attachments.Where(pf => pf.FileTitle == "Submittal").Select(pf => pf.FileSyncUrl).ToList();
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join("|", submittalUrls), CellValues.String, columnIndex++, rowIndex));

                var catalogUrls = product.Attachments.Where(pf => pf.FileTitle == "Catalog").Select(pf => pf.FileSyncUrl).ToList();
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join("|", catalogUrls), CellValues.String, columnIndex++, rowIndex));

                var warrantyUrls = product.Attachments.Where(pf => pf.FileTitle == "Warranty").Select(pf => pf.FileSyncUrl).ToList();
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join("|", warrantyUrls), CellValues.String, columnIndex++, rowIndex));

                var testingDataUrls = product.Attachments.Where(pf => pf.FileTitle == "Testing Data").Select(pf => pf.FileSyncUrl).ToList();
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join("|", testingDataUrls), CellValues.String, columnIndex++, rowIndex));

                var safetyDataSheetUrls = product.Attachments.Where(pf => pf.FileTitle == "Safety Data Sheet").Select(pf => pf.FileSyncUrl).ToList();
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join("|", safetyDataSheetUrls), CellValues.String, columnIndex++, rowIndex));

                var installationGuideUrls = product.Attachments.Where(pf => pf.FileTitle == "Installation Guide").Select(pf => pf.FileSyncUrl).ToList();
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join("|", installationGuideUrls), CellValues.String, columnIndex++, rowIndex));

                var hpdUrls = product.Attachments.Where(pf => pf.FileTitle == "HPD").Select(pf => pf.FileSyncUrl).ToList();
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join("|", hpdUrls), CellValues.String, columnIndex++, rowIndex));

                var epdUrls = product.Attachments.Where(pf => pf.FileTitle == "EPD").Select(pf => pf.FileSyncUrl).ToList();
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join("|", epdUrls), CellValues.String, columnIndex++, rowIndex));

                var masterPartSpecUrls = product.Attachments.Where(pf => pf.FileTitle == AttachmentConstants.MasterspecFileTitle).Select(pf => pf.FileSyncUrl).ToList();
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join("|", masterPartSpecUrls), CellValues.String, columnIndex++, rowIndex));

                var published = product.Published ? "1" : "0";
                row.AppendChild(CommonExcelProvider.ConstructCell(published, CellValues.Number, columnIndex++, rowIndex));

                var publishedOnCastomMicrosite = product.PublishedOnCustomMicrosite ? "1" : "0";
                row.AppendChild(CommonExcelProvider.ConstructCell(publishedOnCastomMicrosite, CellValues.Number, columnIndex++, rowIndex));

                var staging = product.Staging ? "1" : "0";
                row.AppendChild(CommonExcelProvider.ConstructCell(staging, CellValues.Number, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.ProductUrl, CellValues.String, columnIndex++, rowIndex));

                row.AppendChild(CommonExcelProvider.ConstructCell(product.ULUrl, CellValues.String, columnIndex++, rowIndex));

                var revitFiles = product.RevitFiles.ToList();
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join("|", revitFiles), CellValues.String, columnIndex++, rowIndex));

                row.AppendChild(CommonExcelProvider.ConstructCell(product.ExternalId ?? string.Empty, CellValues.String, columnIndex++, rowIndex));

                var publishToPartner = product.PublishToPartner ? "1" : "0";
                row.AppendChild(CommonExcelProvider.ConstructCell(publishToPartner, CellValues.Number, columnIndex++, rowIndex));

                row.AppendChild(CommonExcelProvider.ConstructCell(product.ForgeWallURL, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.ForgeFloorURL, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.ForgeCeilingURL, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.ForgeRoofURL, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.Weight.ToString(), CellValues.Number, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(product.RegionIds, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(";", product.AttachmentFileNames), CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(string.Join(";", product.ProjectFileNames), CellValues.String, columnIndex++, rowIndex));

                foreach (var projectType in distinctProjectTypes)
                {
                    var productProjectTypes = product.ProjectFiles.Where(p => p.FileType.Title == projectType.Title).ToList();
                    if (productProjectTypes != null && productProjectTypes.Any())
                    {
                        var fileUrls = productProjectTypes.Select(pf => pf.FileSyncUrl).ToList();
                        row.AppendChild(CommonExcelProvider.ConstructCell(string.Join("|", fileUrls), CellValues.String, columnIndex++, rowIndex));
                    }
                    else
                    {
                        row.AppendChild(CommonExcelProvider.ConstructCell(string.Empty, CellValues.String, columnIndex++, rowIndex));
                    }
                }

                foreach (var qualityItem in model.QualityItems)
                {
                    int hasQuality = product.ProductQualityItems.Contains(qualityItem.Name) ? 1 : 0;
                    row.AppendChild(CommonExcelProvider.ConstructCell(hasQuality.ToString(), CellValues.Number, columnIndex++, rowIndex));
                }

                foreach (var productStat in distinctProductStats)
                {
                    var productProductStat = product.ProductStats.FirstOrDefault(aps =>
                        aps.KeyStatType == productStat.KeyStatType &&
                        aps.KeyStatName == productStat.KeyStatName &&
                        (
                         (
                          !productStat.KeyStatValueList.Any() && !aps.KeyStatValueList.Any() &&
                          (
                           (productStat.KeyStatUnit == null && aps.KeyStatUnit == null) ||
                           (productStat.KeyStatUnit != null && aps.KeyStatUnit != null &&
                           aps.KeyStatUnit.GroupName == productStat.KeyStatUnit.GroupName &&
                           aps.KeyStatUnit.BUnitName == productStat.KeyStatUnit.BUnitName)
                          )
                         )
                         ||
                         (
                            productStat.KeyStatValueList.Any() && aps.KeyStatValueList.Any() &&
                             ((productStat.KeyStatValueList.First().KeyStatUnit == null && aps.KeyStatValueList.First().KeyStatUnit == null) ||
                             (productStat.KeyStatValueList.First().KeyStatUnit != null && aps.KeyStatValueList.First().KeyStatUnit != null &&
                               aps.KeyStatValueList.First().KeyStatUnit.GroupName == productStat.KeyStatValueList.First().KeyStatUnit.GroupName &&
                               aps.KeyStatValueList.First().KeyStatUnit.BUnitName == productStat.KeyStatValueList.First().KeyStatUnit.BUnitName))
                         )
                        )
                       );

                    if (productProductStat != null)
                    {
                        switch (productProductStat.KeyStatType)
                        {
                            case KeyStatType.SingleNumeric:
                                row.AppendChild(CommonExcelProvider.ConstructCell(!string.IsNullOrWhiteSpace(productProductStat.Note) ? $"{productProductStat.Value}*{productProductStat.Note}" : productProductStat.Value, CellValues.String, columnIndex++, rowIndex));
                                break;
                            case KeyStatType.NumericRange:
                                var numericRange = !string.IsNullOrWhiteSpace(productProductStat.Note) ? $"{productProductStat.MinRangeValue ?? string.Empty}*{productProductStat.MaxRangeValue ?? string.Empty}*{productProductStat.Note}" : $"{productProductStat.MinRangeValue ?? string.Empty}*{productProductStat.MaxRangeValue ?? string.Empty}";
                                row.AppendChild(CommonExcelProvider.ConstructCell(numericRange, CellValues.String, columnIndex++, rowIndex));
                                break;
                            case KeyStatType.MultivalueNumeric:
                                var multivalueNumeric = string.Join(";", productProductStat.KeyStatValueList.Select(x => $"{x.KeyStatUnit?.GroupName ?? string.Empty}*{x.KeyStatUnit?.BUnitName ?? string.Empty}*{x.Value ?? string.Empty}*{x.MaxRangeValue ?? string.Empty}*{x.Note ?? string.Empty}"));
                                row.AppendChild(CommonExcelProvider.ConstructCell(multivalueNumeric, CellValues.String, columnIndex++, rowIndex));
                                break;
                            case KeyStatType.TextValue:
                                row.AppendChild(CommonExcelProvider.ConstructCell(productProductStat.Value, CellValues.String, columnIndex++, rowIndex));
                                break;
                            case KeyStatType.TextMultivalue:
                                var textMultivalue = string.Join(";", productProductStat.KeyStatValueList.Select(x => x.Value));
                                row.AppendChild(CommonExcelProvider.ConstructCell(textMultivalue, CellValues.String, columnIndex++, rowIndex));
                                break;
                            case KeyStatType.None:
                                row.AppendChild(CommonExcelProvider.ConstructCell(string.Empty, CellValues.String, columnIndex++, rowIndex));
                                break;
                            default:
                                throw new NotImplementedException("KeyStatType not found!");
                        }
                    }
                    else
                    {
                        row.AppendChild(CommonExcelProvider.ConstructCell(string.Empty, CellValues.String, columnIndex++, rowIndex));
                    }
                }

                rowIndex++;
            }

            workbookpart.Workbook.Save();

            spreadsheetDocument.Dispose();

            return filePath;
        }

        public static async Task<ProductsExcelParseModel> ParseExcel(string filePath, IEnumerable<string> qulityItems)
        {
            var excelStructureErrors = new List<string>();

            var productErrorsDictionary = new Dictionary<string, List<string>>();

            var resultModel = new ProductsExcelParseModel();
            resultModel.Errors = excelStructureErrors;

            // Create a spreadsheet document by supplying the filepath.
            // By default, AutoSave = true, Editable = true, and Type = xlsx.
            SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Open(filePath, false);

            try
            {
                var workbookpart = spreadsheetDocument.WorkbookPart;
                var workbook = workbookpart.Workbook;
                var sheets = workbook.Descendants<Sheet>();
                var worksheetPart = (WorksheetPart)workbookpart.GetPartById(sheets.First().Id);
                var sharedStringPart = workbookpart.SharedStringTablePart;

                var rows = worksheetPart.Worksheet.Descendants<Row>().ToList();

                // Prepare headers
                var rowHeader1 = rows.FirstOrDefault();
                if (rowHeader1 == null)
                {
                    excelStructureErrors.Add("Can't to found Header1 row");
                    return resultModel;
                }

                bool keyStatsInImport = false;
                var headerCells1 = rowHeader1.Elements<Cell>().ToList();
                int headerRowIndex = 1;

                for (int i = 0; i < headerCells1.Count; i++)
                {
                    var cellReference = $"{CommonExcelProvider.ColumnIndexToColumnLetter(i + 1)}{headerRowIndex}";
                    var cellValue1 = CommonExcelProvider.GetCellStringValue(headerCells1.SingleOrDefault(c => c.CellReference.Value == cellReference), sharedStringPart);
                    if (!string.IsNullOrWhiteSpace(cellValue1))
                    {
                        if (Enum.TryParse(cellValue1, out KeyStatType keyStatType))
                        {
                            keyStatsInImport = true;
                            break;
                        }
                    }
                }

                List<Cell> headerCells4 = rows.FirstOrDefault()?.Elements<Cell>().ToList();
                int productStatsStartIndex = headerCells1.Count;
                if (keyStatsInImport)
                {
                    var rowHeader2 = rows.Skip(1).FirstOrDefault();
                    if (rowHeader2 == null)
                    {
                        excelStructureErrors.Add("Can't to found Header2 row");
                        return resultModel;
                    }
                    var rowHeader3 = rows.Skip(2).FirstOrDefault();
                    if (rowHeader3 == null)
                    {
                        excelStructureErrors.Add("Can't to found Header3 row");
                        return resultModel;
                    }

                    var rowHeader4 = rows.Skip(3).FirstOrDefault();
                    if (rowHeader4 == null)
                    {
                        excelStructureErrors.Add("Can't to found Header4 row");
                        return resultModel;
                    }

                    var headerCells2 = rowHeader2.Elements<Cell>().ToList();
                    var headerCells3 = rowHeader3.Elements<Cell>().ToList();
                    headerCells4 = rowHeader4.Elements<Cell>().ToList();

                    for (int i = 0; i < headerCells1.Count; i++) //change to work with cellRef if is needed
                    {
                        var cellValue1 = CommonExcelProvider.GetCellStringValue(headerCells1[i], sharedStringPart);
                        if (!string.IsNullOrWhiteSpace(cellValue1))
                        {
                            KeyStatType keyStatType;
                            if (Enum.TryParse(cellValue1, out keyStatType))
                            {
                                var cellValue5 = CommonExcelProvider.GetCellStringValue(headerCells4[i], sharedStringPart);
                                if (!string.IsNullOrWhiteSpace(cellValue5))
                                {
                                    var productStat = new ProductProductStatModel
                                    {
                                        KeyStatType = keyStatType,
                                        KeyStatName = cellValue5,
                                        CellRefNumber = i + 1
                                    };

                                    var cellValue2 = CommonExcelProvider.GetCellStringValue(headerCells2[i], sharedStringPart); // get by cellRef if is needed
                                    var cellValue3 = CommonExcelProvider.GetCellStringValue(headerCells3[i], sharedStringPart);

                                    if (productStat.KeyStatType == KeyStatType.SingleNumeric || productStat.KeyStatType == KeyStatType.NumericRange)
                                    {
                                        if (!string.IsNullOrWhiteSpace(cellValue2) || !string.IsNullOrWhiteSpace(cellValue3))
                                        {
                                            productStat.KeyStatUnit = new ProductStatKeyStatUnitModel
                                            {
                                                GroupName = cellValue2,
                                                BUnitName = cellValue3
                                            };
                                        }
                                    }

                                    resultModel.ProductStats.Add(productStat);
                                }
                                else
                                {
                                    excelStructureErrors.Add($"Empty KeyStatName cell: '{headerCells4[i].CellReference}'");
                                }
                            }
                            else
                            {
                                excelStructureErrors.Add($"Invalid KeyStatType in cell: '{headerCells1[i].CellReference}'");
                            }
                        }
                        if (resultModel.ProductStats.Count == 1)
                        {
                            productStatsStartIndex = i;
                        }
                    }

                    if (productStatsStartIndex == 0 || productStatsStartIndex < 14)
                    {
                        productStatsStartIndex = headerCells1.Count;
                    }
                }

                var cellCounter = 0;

                #region Validate position of cell
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.IdCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.IdCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.NameCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.NameCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.ManufacturerIdCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.ManufacturerIdCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.CategoryIdCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.CategoryIdCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.AdditionalCategoryIdsCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.AdditionalCategoryIdsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.ProductLineIdCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.ProductLineIdCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.PhotoIdCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.PhotoIdCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.PhotoURLsCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.PhotoURLsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.DescriptionCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.DescriptionCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.VideoUrlCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.VideoUrlCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.VanityURLCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.VanityURLCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.MetaTitleCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.MetaTitleCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.MetaDescriptionCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.MetaDescriptionCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.MetaKeywordsCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.MetaKeywordsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.KeywordsCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.KeywordsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.CertificatesCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.CertificatesCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.MaterformatsCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.MaterformatsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.OmniclassesCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.OmniclassesCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.UniclassesCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.UniclassesCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.UniformatsCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.UniformatsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.CisfbsCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.CisfbsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.ProductCutSheetUrlsCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.ProductCutSheetUrlsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.PartSpecUrlsCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.PartSpecUrlsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.ProductBrochureUrlsCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.ProductBrochureUrlsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.ImageUrlsCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.ImageUrlsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.SubmittalUrlsCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.SubmittalUrlsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.CatalogUrlsCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.CatalogUrlsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.WarrantyUrlsCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.WarrantyUrlsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.TestingDataUrlsCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.TestingDataUrlsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.SafetyDataSheetUrlsCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.SafetyDataSheetUrlsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.InstallationGuideUrlsCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.InstallationGuideUrlsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.HpdUrlsCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.HpdUrlsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.EpdUrlsCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.EpdUrlsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.MasterPartSpecUrlsCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.MasterPartSpecUrlsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.PublishedCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.PublishedCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.PublishedOnCustomMicrositeCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.PublishedOnCustomMicrositeCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.StagingCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.StagingCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.ProductUrlCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.ProductUrlCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.ULUrlCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.ULUrlCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.RevitFilesCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.RevitFilesCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.ExternalIdCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.ExternalIdCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.PublishToPartnerCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.PublishToPartnerCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.ForgeWallURLCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.ForgeWallURLCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.ForgeFloorURLCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.ForgeFloorURLCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.ForgeCeilingURLCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.ForgeCeilingURLCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.ForgeRoofURLCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.ForgeRoofURLCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.WeightCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.WeightCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.LocalizationCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.LocalizationCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.AttachmentsCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.AttachmentsCaption}'");
                CommonExcelProvider.ValidCellValue(headerCells4[cellCounter++], StaticExcelConstants.ProjectFilesCaption, sharedStringPart, excelStructureErrors, $"Wrong position of column with name '{StaticExcelConstants.ProjectFilesCaption}'");
                #endregion

                int startRefNumber = cellCounter;
                int startQulityItems = cellCounter;

                for (int i = startRefNumber; i < productStatsStartIndex; i++)
                {
                    var headerCellValue = CommonExcelProvider.GetCellStringValue(headerCells4[i], sharedStringPart);
                    if (qulityItems.Contains(headerCellValue))
                    {
                        startQulityItems = i;
                        break;
                    }
                    else
                    {
                        resultModel.ProjectTypes.Add(new ProjectDataTypeModel { Title = headerCellValue, CellRefNumber = i + 1 });
                    }
                }

                for (int i = startQulityItems; i < productStatsStartIndex; i++)
                {
                    var cellValue = CommonExcelProvider.GetCellStringValue(headerCells4[i], sharedStringPart);
                    resultModel.QualityItems.Add(new ProductQualityItemsModel { Name = cellValue, CellRefNumber = i + 1 }); //with Attachments titles
                }

                if (excelStructureErrors.Any())
                {
                    return resultModel;
                }

                using (IUnitOfWork unitOfWork = UnitOfWork.Create())
                {
                    // Prepare Items
                    int rowIndex = keyStatsInImport ? 5 : 2;
                    int rowsToSkip = keyStatsInImport ? 4 : 1;
                    foreach (var row in rows.Skip(rowsToSkip)) // skip header rows
                    {
                        var cells = row.Elements<Cell>().ToList();

                        if (!cells.Any() || cells.All(x => !CommonExcelProvider.HasCellValue(x, sharedStringPart)))
                        {
                            rowIndex++;
                            continue;
                        }

                        var product = new ProductModel();

                        product.RowIndex = rowIndex;

                        int cellIndex = 1;
                        product.Id = CommonExcelProvider.GetCellNullIntValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart, excelStructureErrors);
                        cellIndex++;

                        product.Name = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        cellIndex++;

                        if (string.IsNullOrWhiteSpace(product.Name))
                        {
                            resultModel.Products.Add(product);
                            rowIndex++;
                            continue; // skip the row
                        }

                        product.ManufacturerId = CommonExcelProvider.GetCellIntValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart, excelStructureErrors);
                        cellIndex++;
                        product.CategoryId = CommonExcelProvider.GetCellIntValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart, excelStructureErrors);
                        cellIndex++;
                        product.ProductCategoryIds = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart)?.Split(';').Where(a => string.IsNullOrWhiteSpace(a) == false).Select(a => a.Trim()).Select(a => int.Parse(a)).ToList() ?? new List<int>();
                        cellIndex++;
                        product.ProductLineId = CommonExcelProvider.GetCellNullIntValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart, excelStructureErrors);
                        cellIndex++;
                        product.PhotoId = CommonExcelProvider.GetCellNullIntValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart, excelStructureErrors);
                        cellIndex++;
                        product.PhotoURLs = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart)?.Split('|').ToList();
                        cellIndex++;
                        product.Description = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        cellIndex++;
                        product.VideoUrl = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        cellIndex++;
                        product.VanityURL = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        cellIndex++;

                        if (string.IsNullOrWhiteSpace(product.VanityURL))
                        {
                            product.VanityURL = product.Name.AsVanityUrl();
                        }

                        product.MetaTitle = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        cellIndex++;
                        product.MetaDescription = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        cellIndex++;
                        product.MetaKeywords = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        cellIndex++;
                        product.Keywords = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        cellIndex++;
                        product.ProductCertificates = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart)?.Split(';').Where(a => string.IsNullOrWhiteSpace(a) == false).Select(a => a.Trim()).Select(a => int.Parse(a)).ToList() ?? new List<int>();
                        cellIndex++;
                        product.ProductExternalMasterformatCodes = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart)?.Split(';').Where(a => string.IsNullOrWhiteSpace(a) == false).Select(a => a.Trim()).ToList() ?? new List<string>();
                        cellIndex++;
                        product.ProductOmniclasses = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart)?.Split(';').Where(a => string.IsNullOrWhiteSpace(a) == false).Select(a => a.Trim()).ToList() ?? new List<string>();
                        cellIndex++;
                        product.ProductUniclasses = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart)?.Split(';').Where(a => string.IsNullOrWhiteSpace(a) == false).Select(a => a.Trim()).ToList() ?? new List<string>();
                        cellIndex++;
                        product.ProductUniformats = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart)?.Split(';').Where(a => string.IsNullOrWhiteSpace(a) == false).Select(a => a.Trim()).ToList() ?? new List<string>();
                        cellIndex++;
                        product.ProductCisfbs = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart)?.Split(';').Where(a => string.IsNullOrWhiteSpace(a) == false).Select(a => a.Trim()).ToList() ?? new List<string>();
                        cellIndex++;

                        product.Attachments = new List<ProductFileModel>();
                        string[] productCuSheetUrls = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart)?.Split('|');
                        if (productCuSheetUrls != null)
                        {
                            foreach (string productFileUrl in productCuSheetUrls)
                            {
                                if (!string.IsNullOrEmpty(productFileUrl))
                                {
                                    var newProductFile = new ProductFileModel
                                    {
                                        FileTitle = "Product Cut Sheet",
                                        FileSyncUrl = productFileUrl
                                    };
                                    product.Attachments.Add(newProductFile);
                                }
                            }
                        }
                        cellIndex++;

                        var partSpecUrls = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart)?.Split('|');
                        if (partSpecUrls != null)
                        {
                            foreach (string productFileUrl in partSpecUrls)
                            {
                                if (!string.IsNullOrEmpty(productFileUrl))
                                {
                                    var newProductFile = new ProductFileModel
                                    {
                                        FileTitle = "3-Part Specification",
                                        FileSyncUrl = productFileUrl
                                    };
                                    product.Attachments.Add(newProductFile);
                                }
                            }
                        }
                        cellIndex++;

                        var productBrochureUrls = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart)?.Split('|');
                        if (productBrochureUrls != null)
                        {
                            foreach (string productFileUrl in productBrochureUrls)
                            {
                                if (!string.IsNullOrEmpty(productFileUrl))
                                {
                                    var newProductFile = new ProductFileModel
                                    {
                                        FileTitle = "Product Brochure",
                                        FileSyncUrl = productFileUrl
                                    };
                                    product.Attachments.Add(newProductFile);
                                }
                            }
                        }
                        cellIndex++;

                        var imageUrls = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart)?.Split('|');
                        if (imageUrls != null)
                        {
                            foreach (string productFileUrl in imageUrls)
                            {
                                if (!string.IsNullOrEmpty(productFileUrl))
                                {
                                    var newProductFile = new ProductFileModel
                                    {
                                        FileTitle = "Image",
                                        FileSyncUrl = productFileUrl
                                    };
                                    product.Attachments.Add(newProductFile);
                                }
                            }
                        }
                        cellIndex++;

                        var submittalUrls = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart)?.Split('|');
                        if (submittalUrls != null)
                        {
                            foreach (string productFileUrl in submittalUrls)
                            {
                                if (!string.IsNullOrEmpty(productFileUrl))
                                {
                                    var newProductFile = new ProductFileModel
                                    {
                                        FileTitle = "Submittal",
                                        FileSyncUrl = productFileUrl
                                    };
                                    product.Attachments.Add(newProductFile);
                                }
                            }
                        }
                        cellIndex++;

                        var catalogUrls = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart)?.Split('|');
                        if (catalogUrls != null)
                        {
                            foreach (string productFileUrl in catalogUrls)
                            {
                                if (!string.IsNullOrEmpty(productFileUrl))
                                {
                                    var newProductFile = new ProductFileModel
                                    {
                                        FileTitle = "Catalog",
                                        FileSyncUrl = productFileUrl
                                    };
                                    product.Attachments.Add(newProductFile);
                                }
                            }
                        }
                        cellIndex++;

                        var warrantyUrls = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart)?.Split('|');
                        if (warrantyUrls != null)
                        {
                            foreach (var productFileUrl in warrantyUrls)
                            {
                                if (!string.IsNullOrEmpty(productFileUrl))
                                {
                                    var newProductFile = new ProductFileModel
                                    {
                                        FileTitle = "Warranty",
                                        FileSyncUrl = productFileUrl
                                    };
                                    product.Attachments.Add(newProductFile);
                                }
                            }
                        }
                        cellIndex++;

                        var testingDataUrls = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart)?.Split('|');
                        if (testingDataUrls != null)
                        {
                            foreach (string productFileUrl in testingDataUrls)
                            {
                                if (!string.IsNullOrEmpty(productFileUrl))
                                {
                                    var newProductFile = new ProductFileModel
                                    {
                                        FileTitle = "Testing Data",
                                        FileSyncUrl = productFileUrl
                                    };
                                    product.Attachments.Add(newProductFile);
                                }
                            }
                        }
                        cellIndex++;

                        var safetyDataSheetUrls = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart)?.Split('|');
                        if (safetyDataSheetUrls != null)
                        {
                            foreach (string productFileUrl in safetyDataSheetUrls)
                            {
                                if (!string.IsNullOrEmpty(productFileUrl))
                                {
                                    var newProductFile = new ProductFileModel
                                    {
                                        FileTitle = "Safety Data Sheet",
                                        FileSyncUrl = productFileUrl
                                    };
                                    product.Attachments.Add(newProductFile);
                                }
                            }
                        }
                        cellIndex++;

                        var installationGuideUrls = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart)?.Split('|');
                        if (installationGuideUrls != null)
                        {
                            foreach (string productFileUrl in installationGuideUrls)
                            {

                                if (!string.IsNullOrEmpty(productFileUrl))
                                {
                                    var newProductFile = new ProductFileModel
                                    {
                                        FileTitle = "Installation Guide",
                                        FileSyncUrl = productFileUrl
                                    };
                                    product.Attachments.Add(newProductFile);
                                }
                            }
                        }
                        cellIndex++;

                        var hpdUrls = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart)?.Split('|');
                        if (hpdUrls != null)
                        {
                            foreach (string productFileUrl in hpdUrls)
                            {
                                if (!string.IsNullOrEmpty(productFileUrl))
                                {
                                    var newProductFile = new ProductFileModel
                                    {
                                        FileTitle = "HPD",
                                        FileSyncUrl = productFileUrl
                                    };
                                    product.Attachments.Add(newProductFile);
                                }
                            }
                        }
                        cellIndex++;

                        var epdUrls = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart)?.Split('|');
                        if (epdUrls != null)
                        {
                            foreach (string productFileUrl in epdUrls)
                            {
                                if (!string.IsNullOrEmpty(productFileUrl))
                                {
                                    var newProductFile = new ProductFileModel
                                    {
                                        FileTitle = "EPD",
                                        FileSyncUrl = productFileUrl
                                    };
                                    product.Attachments.Add(newProductFile);
                                }
                            }
                        }
                        cellIndex++;

                        var masterPartSpecUrls = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart)?.Split('|');
                        if (masterPartSpecUrls != null)
                        {
                            foreach (string productFileUrl in masterPartSpecUrls)
                            {
                                if (!string.IsNullOrEmpty(productFileUrl))
                                {
                                    var newProductFile = new ProductFileModel
                                    {
                                        FileTitle = AttachmentConstants.MasterspecFileTitle,
                                        FileSyncUrl = productFileUrl
                                    };
                                    product.Attachments.Add(newProductFile);
                                }
                            }
                        }
                        cellIndex++;

                        var published = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        product.Published = published == "0" ? false : true;
                        cellIndex++;

                        var publishedOnCustomMicrosite = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        product.PublishedOnCustomMicrosite = publishedOnCustomMicrosite == "0" ? false : true;
                        cellIndex++;

                        var staging = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        product.Staging = staging == "0" ? false : true;
                        cellIndex++;

                        product.ProductUrl = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        cellIndex++;

                        product.ULUrl = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        cellIndex++;

                        //revit files (not for upload)
                        cellIndex++;

                        var externalId = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        product.ExternalId = externalId == string.Empty ? null : externalId;
                        cellIndex++;

                        var publishToPartner = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        product.PublishToPartner = publishToPartner == "0" ? false : true;
                        cellIndex++;

                        product.ForgeWallURL = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        cellIndex++;

                        product.ForgeFloorURL = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        cellIndex++;

                        product.ForgeCeilingURL = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        cellIndex++;

                        product.ForgeRoofURL = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        cellIndex++;

                        float productWeight;
                        if (float.TryParse(CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart), out productWeight))
                        {
                            product.Weight = productWeight;
                        }
                        cellIndex++;

                        product.RegionIds = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                        cellIndex++;

                        //attachment names (not for upload)
                        cellIndex++;

                        //project file names (not for upload)
                        cellIndex++;

                        product.ProjectFiles = new List<ProductFileModel>();
                        for (int i = startRefNumber; i < startQulityItems; i++) //parse project types
                        {
                            var projectUrls = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart)?.Split('|');
                            var projectType = resultModel.ProjectTypes.First(a => a.CellRefNumber == i + 1);
                            if (projectUrls != null)
                            {
                                foreach (var productFileUrl in projectUrls)
                                {
                                    if (!string.IsNullOrEmpty(productFileUrl))
                                    {
                                        var newProductFile = new ProductFileModel
                                        {
                                            FileTitle = Path.GetFileName(productFileUrl),
                                            FileSyncUrl = productFileUrl,
                                            FileType = new ProjectDataTypeModel { Title = projectType.Title }
                                        };
                                        product.ProjectFiles.Add(newProductFile);
                                    }
                                }
                            }
                            cellIndex++;
                        }

                        product.ProductQualityItems = new List<string>();
                        for (int i = startQulityItems; i < productStatsStartIndex; i++) //parse certificates
                        {
                            var intValue = CommonExcelProvider.GetCellIntValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart, excelStructureErrors);
                            if (intValue == 1)
                            {
                                var certificate = resultModel.QualityItems.First(a => a.CellRefNumber == i + 1);
                                product.ProductQualityItems.Add(certificate.Name);
                            }
                            cellIndex++;
                        }

                        product.ProductStats = new List<ProductProductStatModel>();
                        if (keyStatsInImport)
                        {
                            for (int i = productStatsStartIndex; i < resultModel.ProductStats.Count + productStatsStartIndex; i++) //parse productStats
                            {
                                var stringValue = CommonExcelProvider.GetCellStringValue(cells.SingleOrDefault(c => c.CellReference.Value == $"{CommonExcelProvider.ColumnIndexToColumnLetter(cellIndex)}{rowIndex}"), sharedStringPart);
                                if (!string.IsNullOrWhiteSpace(stringValue))
                                {
                                    var productStat = resultModel.ProductStats.First(a => a.CellRefNumber == i + 1);
                                    var newProductStat = new ProductProductStatModel
                                    {
                                        KeyStatType = productStat.KeyStatType,
                                        KeyStatName = productStat.KeyStatName,
                                        KeyStatUnit = productStat.KeyStatUnit
                                    };
                                    switch (newProductStat.KeyStatType)
                                    {
                                        case KeyStatType.SingleNumeric:
                                            newProductStat.Value = stringValue;
                                            var stringValues = stringValue.Split('*');
                                            if (stringValues.Length > 1)
                                            {
                                                newProductStat.Value = stringValues[0];
                                                newProductStat.Note = stringValues[1];
                                            }
                                            break;
                                        case KeyStatType.TextValue:
                                            newProductStat.Value = stringValue;
                                            break;
                                        case KeyStatType.MultivalueNumeric:
                                            var multivalueNumericValues = new List<ProductStatKeyStatValueListModel>();
                                            newProductStat.KeyStatValueList = new List<ProductStatKeyStatValueListModel>();
                                            var multiValueStrings = stringValue.Split(';');
                                            foreach (var multiValueString in multiValueStrings)
                                            {
                                                var values = multiValueString.Split('*');
                                                multivalueNumericValues.Add(new ProductStatKeyStatValueListModel
                                                {
                                                    KeyStatUnit = new ProductStatKeyStatUnitModel
                                                    {
                                                        GroupName = values[0],
                                                        BUnitName = values[1]
                                                    },
                                                    Value = values[2],
                                                    MaxRangeValue = !string.IsNullOrWhiteSpace(values[3]) ? values[3] : null,
                                                    Note = values[4]
                                                });
                                            }
                                            newProductStat.KeyStatValueList.AddRange(multivalueNumericValues);
                                            break;
                                        case KeyStatType.TextMultivalue:
                                            newProductStat.KeyStatValueList = new List<ProductStatKeyStatValueListModel>();
                                            var multivalue = new List<ProductStatKeyStatValueListModel>();
                                            if (stringValue.Contains("*"))
                                            {
                                                multivalue = stringValue.Split(';')
                                                .Where(a => !string.IsNullOrWhiteSpace(a))
                                                .Select(a => new ProductStatKeyStatValueListModel
                                                {
                                                    Value = a.Split('*').Count() > 1 ? null : a,
                                                    MinRangeValue = a.Split('*').Count() > 1 ? a.Split('*')[0] : null,
                                                    MaxRangeValue = a.Split('*').Count() > 1 ? a.Split('*')[1] : null
                                                }).ToList();
                                            }
                                            else
                                            {
                                                multivalue = stringValue.Split(';')
                                                .Where(a => !string.IsNullOrWhiteSpace(a))
                                                .Select(a => new ProductStatKeyStatValueListModel
                                                {
                                                    Value = a
                                                }).ToList();
                                            }
                                            newProductStat.KeyStatValueList.AddRange(multivalue);
                                            break;
                                        case KeyStatType.NumericRange:
                                            var numericRange = stringValue.Split('*');
                                            newProductStat.MinRangeValue = numericRange.Count() > 1 ? numericRange[0] : null;
                                            newProductStat.MaxRangeValue = numericRange.Count() > 1 ? numericRange[1] : null;
                                            newProductStat.Note = numericRange.Count() > 2 ? numericRange[2] : string.Empty;
                                            break;
                                        default:
                                            throw new NotImplementedException("KeyStatType is not found!");
                                    }

                                    product.ProductStats.Add(newProductStat);
                                }
                                cellIndex++;
                            }
                        }

                        await ValidateProductUrlsAsync(product);

                        resultModel.Products.Add(product);
                        rowIndex++;
                    }
                }
            }
            catch (Exception e)
            {
                excelStructureErrors.Add("Parse error: " + e.Message);
                Log.Error(e.Message, e);
            }

            spreadsheetDocument.Dispose();
            return resultModel;
        }

        public static List<List<string>> ParseAllExcel(string filePath, int spreadsheet = 1)
        {
            var resultModel = new List<List<string>>();
            var errors = new List<string>();

            // Create a spreadsheet document by supplying the filepath.
            // By default, AutoSave = true, Editable = true, and Type = xlsx.
            SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Open(filePath, false);

            try
            {
                var workbookpart = spreadsheetDocument.WorkbookPart;
                var workbook = workbookpart.Workbook;
                var sheets = workbook.Descendants<Sheet>();
                var worksheetPart = (WorksheetPart)workbookpart.GetPartById(sheets.Skip(spreadsheet - 1).First().Id);
                var sharedStringPart = workbookpart.SharedStringTablePart;

                var rows = worksheetPart.Worksheet.Descendants<Row>().ToList();
                foreach (var row in rows)
                {
                    var rowValue = new List<string>();
                    var cells = row.Elements<Cell>().ToList();
                    foreach (var cell in cells)
                    {
                        var cellValue = CommonExcelProvider.GetCellStringValue(cell, sharedStringPart);
                        if (!string.IsNullOrWhiteSpace(cellValue))
                        {
                            rowValue.Add(cellValue);
                        }
                    }
                    resultModel.Add(rowValue);
                }
            }
            catch (Exception e)
            {
                errors.Add("Parse error: " + e.Message);
                Log.Error(e.Message, e);
            }

            return resultModel;
        }

        public static string GetProductFilesAudit(int manufacturerId, string tempFolder)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                bool manufacturerExists = unitOfWork.ManufacturerRepository.GetAll().Any(x => x.Id == manufacturerId);

                if (!manufacturerExists)
                    throw new Exception("Manufacturer Not Found");

                List<ProductAuditDto> products = unitOfWork.ProductRepository.GetAll()
                    .Where(x => x.ManufacturerId == manufacturerId)
                    .Select(x => new ProductAuditDto
                    {
                        ProductId = x.Id,
                        ProductName = x.Name,
                        ManufacturerName = x.Manufacturer.Name,
                        CategoryName = x.Category.Name,
                        ProductFiles = x.ProductFiles.Where(f => !f.IsAttachment).Select(f => new ProductFileAuditDto
                        {
                            FileTitle = f.File.Title,
                            ProjectDataType = f.ProjectDataType.Title,
                        })
                    })
                    .ToList();

                products.ForEach(x => x.ProductFileGroups = x.ProductFiles.GroupBy(x => x.ProjectDataType));

                var projectDataTypes = products.SelectMany(f => f.ProductFileGroups).GroupBy(x => x.Key).Select(x => new { ProjectDataType = x.Key, Max = x.Max(g => g.Count()) }).ToList();

                if (!Directory.Exists(tempFolder))
                {
                    Directory.CreateDirectory(tempFolder);
                }

                var filePath = Path.Combine(tempFolder, $"productAudit_{Guid.NewGuid()}.xlsx");
                var spreadsheetDocument = SpreadsheetDocument.Create(filePath, SpreadsheetDocumentType.Workbook);
                var workbookPart = spreadsheetDocument.AddWorkbookPart();
                workbookPart.Workbook = new Workbook();
                var worksheetPart = workbookPart.AddNewPart<WorksheetPart>();
                worksheetPart.Worksheet = new Worksheet();

                var fonts = new Fonts(
                    new DocumentFormat.OpenXml.Spreadsheet.Font(
                        new FontSize() { Val = 10 }
                    ));
                var fills = new Fills(
                        new Fill(new PatternFill() { PatternType = PatternValues.None }),
                        new Fill(new PatternFill(new ForegroundColor { Rgb = new HexBinaryValue() { Value = "FFE7E6E6" } }) { PatternType = PatternValues.Solid }),
                        new Fill(new PatternFill(new ForegroundColor { Rgb = new HexBinaryValue() { Value = "FFBDD7EE" } }) { PatternType = PatternValues.Solid })
                    );
                var borders = new Borders(
                        new Border()
                    );
                var cellFormats = new CellFormats(
                        new CellFormat(),
                        new CellFormat { FontId = 0, FillId = 1, BorderId = 0, ApplyFill = true },
                        new CellFormat { FontId = 0, FillId = 2, BorderId = 0, ApplyFill = true },
                        new CellFormat { FontId = 0, FillId = 2, BorderId = 0, ApplyFill = true, Alignment = new Alignment { TextRotation = 90, WrapText = true } }
                    );
                var styleSheet = new Stylesheet(fonts, fills, borders, cellFormats);
                var stylePart = workbookPart.AddNewPart<WorkbookStylesPart>();
                stylePart.Stylesheet = styleSheet;
                stylePart.Stylesheet.Save();

                var fieldsCount = 4;
                var columnsCount = fieldsCount + projectDataTypes.Sum(x => x.Max);
                var columns = new Columns();

                for (uint i = 0; i < columnsCount; i++)
                    columns.AppendChild(new Column { Min = i + 1, Max = i + 1, Width = 30, CustomWidth = true });

                worksheetPart.Worksheet.AppendChild(columns);
                workbookPart.Workbook.Save();

                var sheets = workbookPart.Workbook.AppendChild(new Sheets());
                var sheet = new Sheet()
                {
                    Id = spreadsheetDocument.WorkbookPart.
                    GetIdOfPart(worksheetPart),
                    SheetId = 1,
                    Name = "Project Data Files Audit"
                };
                sheets.Append(sheet);
                workbookPart.Workbook.Save();

                var sheetData = worksheetPart.Worksheet.AppendChild(new SheetData());
                var rowIndex = 1;
                var columnIndex = 1;
                var rowHeader = new Row();
                sheetData.AppendChild(rowHeader);
                var styleIndex = (uint)2;

                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Product ID", CellValues.String, columnIndex++, rowIndex, styleIndex));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Product Name", CellValues.String, columnIndex++, rowIndex, styleIndex));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Manufacturer", CellValues.String, columnIndex++, rowIndex, styleIndex));
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell("Category", CellValues.String, columnIndex++, rowIndex, styleIndex));

                foreach (var projectDataType in projectDataTypes)
                {
                    for (var i = 0; i < projectDataType.Max; i++)
                    {
                        rowHeader.AppendChild(CommonExcelProvider.ConstructCell(projectDataType.ProjectDataType + (i + 1), CellValues.String, columnIndex++, rowIndex, 2));
                    }
                }

                rowIndex++;
                foreach (var product in products)
                {
                    columnIndex = 1;
                    var valueRow = new Row();
                    sheetData.AppendChild(valueRow);
                    valueRow.AppendChild(CommonExcelProvider.ConstructCell(product.ProductId.ToString(), CellValues.String, columnIndex++, rowIndex));
                    valueRow.AppendChild(CommonExcelProvider.ConstructCell(product.ProductName, CellValues.String, columnIndex++, rowIndex));
                    valueRow.AppendChild(CommonExcelProvider.ConstructCell(product.ManufacturerName, CellValues.String, columnIndex++, rowIndex));
                    valueRow.AppendChild(CommonExcelProvider.ConstructCell(product.CategoryName, CellValues.String, columnIndex++, rowIndex));

                    foreach (var projectDataType in projectDataTypes)
                    {
                        var productFileGroup = product.ProductFileGroups.Where(x => x.Key == projectDataType.ProjectDataType).FirstOrDefault();
                        if (productFileGroup != null && productFileGroup.Any())
                        {
                            foreach (var file in productFileGroup)
                            {
                                valueRow.AppendChild(CommonExcelProvider.ConstructCell(file.FileTitle, CellValues.String, columnIndex++, rowIndex));
                            }
                        }
                        var numberOfEmptyCells = productFileGroup != null ? projectDataType.Max - productFileGroup.Count() : projectDataType.Max;
                        for (var i = 0; i < numberOfEmptyCells; i++)
                        {
                            valueRow.AppendChild(CommonExcelProvider.ConstructCell("", CellValues.String, columnIndex++, rowIndex));
                        }
                    }

                    rowIndex++;
                }

                workbookPart.Workbook.Save();
                spreadsheetDocument.Dispose();

                return filePath;
            }
        }

        public static void ExportDataSet(DataTable table, string blobExcelName)
        {
            SpreadsheetDocument workbook = SpreadsheetDocument.Create(blobExcelName, SpreadsheetDocumentType.Workbook);
            WorkbookPart workbookPart = workbook.AddWorkbookPart();
            workbookPart.Workbook = new Workbook();

            WorksheetPart worksheetPart = workbookPart.AddNewPart<WorksheetPart>();
            worksheetPart.Worksheet = new Worksheet();

            Fonts fonts = new Fonts(
                new Font( // Index 0 - default
                    new FontSize() { Val = 10 }
                ));

            Fills fills = new Fills(
                    new Fill(new PatternFill() { PatternType = PatternValues.None }), // Index 0 - default
                    new Fill(new PatternFill(new ForegroundColor { Rgb = new HexBinaryValue() { Value = "FFE7E6E6" } }) { PatternType = PatternValues.Solid }), // Index 1 - default
                    new Fill(new PatternFill(new ForegroundColor { Rgb = new HexBinaryValue() { Value = "FFBDD7EE" } }) { PatternType = PatternValues.Solid }) // Index 2 - header
                );

            Borders borders = new Borders(
                    new Border() // index 0 default
                );

            CellFormats cellFormats = new CellFormats(
                    new CellFormat(), // default
                    new CellFormat { FontId = 0, FillId = 1, BorderId = 0, ApplyFill = true }, // body
                    new CellFormat { FontId = 0, FillId = 2, BorderId = 0, ApplyFill = true }, // header
                    new CellFormat { FontId = 0, FillId = 2, BorderId = 0, ApplyFill = true, Alignment = new Alignment { TextRotation = 90, WrapText = true } } // header for certificate
                );

            Stylesheet styleSheet = new Stylesheet(fonts, fills, borders, cellFormats);

            // Adding style
            WorkbookStylesPart stylePart = workbookPart.AddNewPart<WorkbookStylesPart>();
            stylePart.Stylesheet = styleSheet;
            stylePart.Stylesheet.Save();

            var sharedStringTablePart = workbook.WorkbookPart.AddNewPart<SharedStringTablePart>();
            sharedStringTablePart.SharedStringTable = new SharedStringTable();
            sharedStringTablePart.SharedStringTable.Save();

            Columns columns = new Columns();
            for (uint i = 1; i <= table.Columns.Count; i++)
            {
                columns.AppendChild(new Column { Min = i, Max = i, Width = 25, CustomWidth = true });
            }

            worksheetPart.Worksheet.AppendChild(columns);
            workbookPart.Workbook.Save();

            //Sheets
            Sheets sheets = workbookPart.Workbook.AppendChild(new Sheets());

            Sheet sheet = new Sheet()
            {
                Id = workbook.WorkbookPart.
                GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = "Json data"
            };
            sheets.Append(sheet);
            workbookPart.Workbook.Save();

            // Get the sheetData cell table.
            SheetData sheetData = worksheetPart.Worksheet.AppendChild(new SheetData());

            Row rowHeader = new Row();
            sheetData.AppendChild(rowHeader);

            var columnIndex = 1;
            var rowIndex = 1;
            List<string> columnNames = new List<string>();
            foreach (DataColumn column in table.Columns)
            {
                columnNames.Add(column.ColumnName);
                rowHeader.AppendChild(CommonExcelProvider.ConstructCell(column.ColumnName, CellValues.String, columnIndex++, rowIndex, 2));
            }

            rowIndex++;
            foreach (DataRow dsrow in table.Rows)
            {
                Row newRow = new Row();
                sheetData.AppendChild(newRow);
                columnIndex = 1;
                foreach (string col in columnNames)
                {
                    newRow.AppendChild(CommonExcelProvider.ConstructCell(dsrow[col] != null ? dsrow[col].ToString() : string.Empty, CellValues.String, columnIndex++, rowIndex));
                }
                rowIndex++;
            }

            workbookPart.Workbook.Save();
            workbook.Dispose();
        }

        public static async Task<ProductsExcelParseModel> ImportProducts(IUnitOfWork unitOfWork,
            List<int> manufacturerIds,
            ProductsExcelParseModel excelParseData,
            List<Manufacturer> manufacturers,
            List<Category> categories,
            string userId,
            IEnumerable<dynamic> quilityItems,
            IEnumerable<dynamic> cisfbs,
            Dictionary<int, List<string>> attachmentOrdersByManufacturer,
            IEnumerable<ProjectDataType> projectDataTypes,
            List<MasterformatModel> masterformats,
            IEnumerable<dynamic> omniclasses,
            IEnumerable<dynamic> uniclasses,
            IEnumerable<dynamic> uniformats,
            IEnumerable<dynamic> keyStats,
            IEnumerable<dynamic> keyStatUnits,
            IEnumerable<dynamic> revitFileIdsQueue)
        {
            var httpClient = GetHttpClient();
            unitOfWork.BeginTransaction();

            var productLineIdsByManufacturer = unitOfWork.ProductLineRepository.GetAll()
                .Where(a => a.ManufacturerId != null && manufacturerIds.Contains(a.ManufacturerId.Value))
                .Select(a => new { a.Id, a.ManufacturerId })
                .AsEnumerable()
                .GroupBy(a => a.ManufacturerId.Value)
                .ToDictionary(a => a.Key, a => a.Select(b => b.Id));

            var processedProductsWithSamePhoto = new List<int>();
            Dictionary<int, bool> productIdUpdateDictionary = new Dictionary<int, bool>();

            int rowIndex = 5;
            foreach (ProductModel parsedProduct in excelParseData.Products)
            {
                try
                {
#if DEBUG
                    System.Diagnostics.Debug.WriteLine($"Product with row index {rowIndex} processing started");
#endif
                    if (parsedProduct.ExcelParseErrors.Any())
                    {
#if DEBUG
                        System.Diagnostics.Debug.WriteLine($"This product has any errors");
#endif
                        continue;
                    }

                    Manufacturer currentManufacturer = manufacturers.FirstOrDefault(a => a.Id == parsedProduct.ManufacturerId);
                    if (parsedProduct.ProductLineId.HasValue && !productLineIdsByManufacturer[parsedProduct.ManufacturerId].Contains(parsedProduct.ProductLineId.Value))
                    {
                        parsedProduct.ExcelParseErrors.Add($"Can't find product line id '{parsedProduct.ProductLineId}' for this manufacturer");
#if DEBUG
                        System.Diagnostics.Debug.WriteLine($"Can't find product line id '{parsedProduct.ProductLineId}' for this manufacturer");
#endif
                        continue;
                    }

                    StringValidatorHelper.ValidateStrings(parsedProduct);

                    bool newProduct = false;
                    Product product = null;
                    var attachmentsOrder = new Dictionary<int, string>();
                    var mainPhotoUrl = string.Empty;
                    if (parsedProduct.Id == null || parsedProduct.Id == 0)
                    {
                        product = new Product(); // create new product 
                        newProduct = true;
                    }
                    else
                    {
                        product = unitOfWork.ProductRepository.GetAll().Include(a => a.ProductFiles).ThenInclude(b => b.File).FirstOrDefault(a => a.Id == parsedProduct.Id.Value);
                        if (product == null)
                        {
                            parsedProduct.ExcelParseErrors.Add($"Can't find product with Id '{parsedProduct.Id}'");

#if DEBUG
                            System.Diagnostics.Debug.WriteLine($"Can't find product with Id '{parsedProduct.Id}'");
#endif
                            continue;
                        }
                    }

                    product.Name = parsedProduct.Name;
                    if (product.CategoryId != parsedProduct.CategoryId && product.Category != null)
                    {
                        var detachedCategory = product.Category;
                        unitOfWork.CategoryRepository.Detached(detachedCategory);
                        product.Category = null;
                    }

                    product.CategoryId = parsedProduct.CategoryId;
                    product.ManufacturerId = parsedProduct.ManufacturerId;
                    product.ProductUrl = parsedProduct.ProductUrl;
                    product.ULUrl = parsedProduct.ULUrl;
                    product.ExternalId = parsedProduct.ExternalId;
                    product.ProductLineId = parsedProduct.ProductLineId;
                    product.PhotoId = parsedProduct.PhotoId;
                    product.Description = parsedProduct.Description;
                    product.VideoUrl = parsedProduct.VideoUrl;
                    product.VanityURL = parsedProduct.VanityURL;
                    product.MetaTitle = parsedProduct.MetaTitle;
                    product.MetaDescription = parsedProduct.MetaDescription;
                    product.MetaKeywords = parsedProduct.MetaKeywords;
                    product.Keywords = parsedProduct.Keywords;
                    product.Published = parsedProduct.Published;
                    product.PublishedOnCustomMicrosite = parsedProduct.PublishedOnCustomMicrosite;
                    product.PublishToPartner = parsedProduct.PublishToPartner;
                    product.Staging = parsedProduct.Staging;

                    product.IsImperialDefault = parsedProduct.ProductStats != null &&
                        parsedProduct.ProductStats.Any(a => (a.KeyStatUnit != null && (a.KeyStatUnit.BUnitName.ToLowerInvariant() == "in" || a.KeyStatUnit.BUnitName.ToLowerInvariant() == "ft")) ||
                        (a.KeyStatValueList != null && (a.KeyStatValueList.Any(b => b.KeyStatUnit != null && b.KeyStatUnit.BUnitName.ToLowerInvariant() == "in") ||
                                                        a.KeyStatValueList.Any(b => b.KeyStatUnit != null && b.KeyStatUnit.BUnitName.ToLowerInvariant() == "ft"))));

                    product.ForgeWallURL = parsedProduct.ForgeWallURL;
                    product.ForgeFloorURL = parsedProduct.ForgeFloorURL;
                    product.ForgeCeilingURL = parsedProduct.ForgeCeilingURL;
                    product.ForgeRoofURL = parsedProduct.ForgeRoofURL;
                    product.Weight = parsedProduct.Weight;

                    var mainCategoryName = categories.Where(a => a.Id == parsedProduct.CategoryId).Select(a => a.Name).FirstOrDefault();
                    if (mainCategoryName == null)
                    {
                        parsedProduct.ExcelParseErrors.Add($"Category with Id {parsedProduct.CategoryId} not exist");

#if DEBUG
                        System.Diagnostics.Debug.WriteLine($"Category with Id {parsedProduct.CategoryId} not exist");
#endif
                        continue;
                    }

                    var metaData = MetaDataHelper.GetProductMetaData(product.Name, mainCategoryName, currentManufacturer.Name);
                    if (string.IsNullOrWhiteSpace(product.MetaTitle))
                    {
                        product.MetaTitle = metaData.Title;
                    }
                    if (string.IsNullOrWhiteSpace(product.MetaDescription))
                    {
                        product.MetaDescription = metaData.Description;
                    }
                    if (string.IsNullOrWhiteSpace(product.MetaKeywords))
                    {
                        product.MetaKeywords = metaData.Keywords;
                    }

                    var keyStatsOrders = new List<KeyValuePair<ProductProductStatModel, int>>();
                    if (newProduct)
                    {
                        product.CreatedById = userId;
                        product.CreatedDate = DateTime.UtcNow;

                        var photoFromDb = unitOfWork.PhotoRepository.GetAll().FirstOrDefault(x => x.Id == parsedProduct.PhotoId);
                        product.PhotoId = photoFromDb?.Id;
                        product.Photo = photoFromDb;

                        unitOfWork.ProductRepository.Insert(product);
                        unitOfWork.Save();
                        parsedProduct.Id = product.Id;

#if DEBUG
                        System.Diagnostics.Debug.WriteLine($"Passed product creation with row index {rowIndex}");
#endif
                    }
                    else
                    {
                        product.ModifiedById = userId;
                        product.ModifiedDate = DateTime.UtcNow;

                        var photoFromDb = unitOfWork.PhotoRepository.GetAll().FirstOrDefault(x => x.Id == parsedProduct.PhotoId);
                        product.PhotoId = photoFromDb?.Id;
                        product.Photo = photoFromDb;

                        unitOfWork.ProductRepository.Edit(product);
                        unitOfWork.Save();

#if DEBUG
                        System.Diagnostics.Debug.WriteLine($"Passed product save with row index {rowIndex}");
#endif

                        //Delete old data 
                        var productCertificates = product.ProductCertificates.ToList();
                        unitOfWork.ProductCertificateRepository.Delete(productCertificates);

                        var productCategories = product.ProductCategories.ToList();
                        unitOfWork.ProductCategoryRepository.Delete(productCategories);

                        var productQualityItems = product.ProductQualityItems.ToList();
                        unitOfWork.ProductQualityItemRepository.Delete(productQualityItems);

                        var productsWithSamePhotoId = unitOfWork.ProductRepository.GetAll().Where(x => x.Id != product.Id && x.PhotoId != null && x.PhotoId == product.PhotoId).ToList();

                        if (productsWithSamePhotoId.Any())
                        {
                            foreach (var productWithSamePhotoId in productsWithSamePhotoId)
                            {
                                if (!processedProductsWithSamePhoto.Contains(productWithSamePhotoId.Id))
                                {
                                    productWithSamePhotoId.PhotoId = null;
                                    productWithSamePhotoId.Photo = null;
                                    unitOfWork.ProductRepository.Edit(productWithSamePhotoId);
                                }
                            }
                            processedProductsWithSamePhoto.Add(product.Id);
                        }

                        var productPhotos = product.ProductPhotos.Where(pf => pf.Photo != null && pf.Photo.UploadUrl != null).ToList();

                        mainPhotoUrl = productPhotos.Where(pf => pf.PhotoId == product.PhotoId).Select(x => x.Photo?.UploadUrl).FirstOrDefault();
                        var photos = productPhotos.Select(pp => pp.Photo).ToList();
                        unitOfWork.ProductPhotoRepository.Delete(productPhotos);
                        unitOfWork.PhotoRepository.Delete(photos);

                        var productCisfbs = product.ProductCisfbs.ToList();
                        unitOfWork.ProductCisfbRepository.Delete(productCisfbs);

                        var productMasterformats = product.ProductMasterformats.ToList();
                        unitOfWork.ProductMasterformatRepository.Delete(productMasterformats);

                        var productOmniclasses = product.ProductOmniclasses.ToList();
                        unitOfWork.ProductOmniclassRepository.Delete(productOmniclasses);

                        var productUniclasses = product.ProductUniclasses.ToList();
                        unitOfWork.ProductUniclassRepository.Delete(productUniclasses);

                        var productUniformats = product.ProductUniformats.ToList();
                        unitOfWork.ProductUniformatRepository.Delete(productUniformats);

                        var productStatsToDelete = product.ProductStats.ToList();
                        foreach (var productStatToDelete in productStatsToDelete)
                        {
                            keyStatsOrders.Add(new KeyValuePair<ProductProductStatModel, int>(new ProductProductStatModel
                            {
                                KeyStatName = productStatToDelete.KeyStat.Name,
                                KeyStatType = productStatToDelete.KeyStatType,
                                KeyStatUnit = productStatToDelete.KeyStatUnit == null ? null : new ProductStatKeyStatUnitModel
                                {
                                    GroupName = productStatToDelete.KeyStatUnit.GroupName,
                                    BUnitName = productStatToDelete.KeyStatUnit.BUnitName
                                },
                                Note = productStatToDelete.Note,
                                Value = productStatToDelete.Value,
                                MinRangeValue = productStatToDelete.MinRangeValue,
                                MaxRangeValue = productStatToDelete.MaxRangeValue,
                                KeyStatValueList = productStatToDelete.KeyStatValueList.Select(ksvl => new ProductStatKeyStatValueListModel
                                {
                                    Value = ksvl.Value,
                                    MinRangeValue = ksvl.MinRangeValue,
                                    MaxRangeValue = ksvl.MaxRangeValue,
                                    KeyStatUnit = ksvl.KeyStatUnit == null ? null : new ProductStatKeyStatUnitModel
                                    {
                                        BUnitName = ksvl.KeyStatUnit.BUnitName,
                                        GroupName = ksvl.KeyStatUnit.GroupName
                                    }
                                }).ToList()
                            }, productStatToDelete.Order));
                            unitOfWork.KeyStatValueListRepository.Delete(productStatToDelete.KeyStatValueList.ToList());
                        }
                        unitOfWork.ProductStatsRepository.Delete(productStatsToDelete);
                    }

                    unitOfWork.Save();
#if DEBUG
                    System.Diagnostics.Debug.WriteLine($"Passed old data deleted for product with row index {rowIndex}");
#endif

                    //Add hero shots
                    var addedPhotoIds = new Dictionary<int, string>();
                    if (parsedProduct.PhotoURLs != null)
                    {
                        foreach (var photoUrl in parsedProduct.PhotoURLs.Where(a => !string.IsNullOrEmpty(a)).ToList())
                        {
                            var attachUrl = photoUrl.Trim();
                            if (!attachUrl.StartsWith("http"))
                            {
                                attachUrl = "http://" + attachUrl;
                            }

                            var fileName = Path.GetFileName(attachUrl);
                            Photo photo = new Photo();
                            photo.CreatedById = userId;
                            photo.CreatedDate = DateTime.UtcNow;
                            photo.Name = fileName;
                            photo.UploadUrl = attachUrl;
                            unitOfWork.PhotoRepository.Insert(photo);

                            unitOfWork.Save();

                            int redirectsCount = 0;
                        StartRedirect:

                            if (attachUrl.StartsWith("https://"))
                            {
                                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;
                                ServicePointManager.ServerCertificateValidationCallback += (sender, certificate, chain, sslPolicyErrors) => true;
                            }

                            using (var photoResponse = httpClient.GetAsync(attachUrl).GetAwaiter().GetResult())
                            {
                                if (photoResponse.StatusCode == HttpStatusCode.OK || photoResponse.StatusCode == HttpStatusCode.Accepted)
                                {
                                    var originalImage = new MemoryStream();
                                    using (Stream photoResponseStream = photoResponse.Content.ReadAsStream())
                                    {
                                        photoResponseStream.CopyTo(originalImage);
                                        var imageName = $"{currentManufacturer.Name}-{product.Name}-revit-";
                                        PhotoProvider.CreateProductPhotosAsync(photo, originalImage, Path.GetExtension(attachUrl), imageName, false).Wait();
                                        photo.SyncStatusCode = (int)HttpStatusCode.OK;
                                        photo.UpdatesCount = 0;
                                        unitOfWork.PhotoRepository.Edit(photo);
                                        unitOfWork.Save();
                                    }
                                }
                                else if ((photoResponse.StatusCode == HttpStatusCode.Moved || photoResponse.StatusCode == HttpStatusCode.MovedPermanently || photoResponse.StatusCode == HttpStatusCode.Found) &&
                                         !string.IsNullOrWhiteSpace(GetHeaderValue(photoResponse.Headers, "Location")) &&
                                         redirectsCount < 2)
                                {
                                    redirectsCount++;
                                    attachUrl = GetHeaderValue(photoResponse.Headers, "Location");
                                    photoResponse.Dispose();
                                    goto StartRedirect;
                                }
                            }

                            ProductPhoto productPhoto = new ProductPhoto();
                            productPhoto.ProductId = product.Id;
                            productPhoto.PhotoId = photo.Id;
                            productPhoto.CreatedById = userId;
                            productPhoto.CreatedDate = DateTime.UtcNow;
                            unitOfWork.ProductPhotoRepository.Insert(productPhoto);
                            addedPhotoIds.Add(photo.Id, photo.UploadUrl);
                        }
                    }
                    unitOfWork.Save();

#if DEBUG
                    System.Diagnostics.Debug.WriteLine($"Passed hero shots add for product with row index {rowIndex}");
#endif

                    if (parsedProduct.PhotoId != null)
                    {
                        var mainPhoto = unitOfWork.PhotoRepository.GetById(parsedProduct.PhotoId.Value);
                        if (mainPhoto != null)
                        {
                            product.PhotoId = parsedProduct.PhotoId.Value;
                            unitOfWork.ProductRepository.Edit(product);

                            var productPhotos = unitOfWork.ProductPhotoRepository.GetAll()
                                .Where(a => a.ProductId == product.Id && (a.PhotoId == mainPhoto.Id || (a.Photo.UploadUrl != null && a.Photo.UploadUrl == mainPhoto.UploadUrl))).ToList();

                            unitOfWork.ProductPhotoRepository.Delete(productPhotos);
                            unitOfWork.Save();

                            ProductPhoto productPhoto = new ProductPhoto();
                            productPhoto.ProductId = product.Id;
                            productPhoto.PhotoId = mainPhoto.Id;
                            productPhoto.CreatedById = userId;
                            productPhoto.CreatedDate = DateTime.UtcNow;

                            unitOfWork.ProductPhotoRepository.Insert(productPhoto);
                        }
                        else
                        {
                            if (!string.IsNullOrEmpty(mainPhotoUrl))
                            {
                                int? mainPhotoFromURLId = addedPhotoIds.Where(x => x.Value.ToLower() == mainPhotoUrl.ToLower()).Select(x => x.Key).FirstOrDefault();
                                if (mainPhotoFromURLId != null && mainPhotoFromURLId != 0) product.PhotoId = mainPhotoFromURLId;
                                else product.PhotoId = product.ProductPhotos.Any() ? product.ProductPhotos.First().PhotoId : default(int?);
                                unitOfWork.ProductRepository.Edit(product);
                            }
                            else
                            {
                                product.PhotoId = product.ProductPhotos.Any() ? product.ProductPhotos.First().PhotoId : default(int?);
                                unitOfWork.ProductRepository.Edit(product);
                            }
                        }
                    }
                    else
                    {
                        product.PhotoId = addedPhotoIds.Any() ? addedPhotoIds.First().Key : default(int?);
                        unitOfWork.ProductRepository.Edit(product);
                    }
                    unitOfWork.Save();
#if DEBUG
                    System.Diagnostics.Debug.WriteLine($"Passed photo add for product with row index {rowIndex}");
#endif

                    //Add product certificate
                    if (parsedProduct.ProductCertificates != null)
                    {
                        foreach (var certificateId in parsedProduct.ProductCertificates)
                        {
                            ProductCertificate productCertificate = new ProductCertificate();
                            productCertificate.ProductId = product.Id;
                            productCertificate.ExternalCertificateId = certificateId;
                            productCertificate.CreatedById = userId;
                            productCertificate.CreatedDate = DateTime.UtcNow;
                            unitOfWork.ProductCertificateRepository.Insert(productCertificate);
                        }
                    }

#if DEBUG
                    System.Diagnostics.Debug.WriteLine($"Passed certificates add for product with row index {rowIndex}");
#endif

                    if (parsedProduct.ProductCategoryIds != null)
                    {
                        foreach (var productCategoryId in parsedProduct.ProductCategoryIds)
                        {
                            ProductCategory productCategory = new ProductCategory();
                            productCategory.ProductId = product.Id;
                            productCategory.CategoryId = productCategoryId;
                            unitOfWork.ProductCategoryRepository.Insert(productCategory);
                        }
                    }

#if DEBUG
                    System.Diagnostics.Debug.WriteLine($"Passed categories add for product with row index {rowIndex}");
#endif

                    //Add product Quality Items
                    if (parsedProduct.ProductQualityItems != null)
                    {
                        foreach (var qualityItemName in parsedProduct.ProductQualityItems)
                        {
                            ProductQualityItem productQualityItem = new ProductQualityItem();
                            productQualityItem.ProductId = product.Id;
                            productQualityItem.QualityItemId = quilityItems.First(a => a.Name == qualityItemName).Id;
                            productQualityItem.CreatedById = userId;
                            productQualityItem.CreatedDate = DateTime.UtcNow;
                            unitOfWork.ProductQualityItemRepository.Insert(productQualityItem);
                        }
                    }

#if DEBUG
                    System.Diagnostics.Debug.WriteLine($"Passed quality items add for product with row index {rowIndex}");
#endif

                    //Add product files
                    if (parsedProduct.Attachments != null && parsedProduct.Attachments.Any())
                    {
                        var existingAttachmentsAddedByUrl = product.ProductFiles.Where(x => x.IsAttachment && x.File.SyncUrl != null).ToList();
                        foreach (var attachment in existingAttachmentsAddedByUrl)
                        {
                            attachmentsOrder.Add(attachment.Id, $"{attachment.File.Title.ToLowerInvariant()}|{attachment.File.SyncUrl.ToLowerInvariant()}");
                        }

                        var attachments = parsedProduct.Attachments.GroupBy(a => new { a.FileTitle, a.FileSyncUrl, a.CustomFileId })
                                                       .Select(a => new ProductFileModel
                                                       {
                                                           FileSyncUrl = a.Key.FileSyncUrl,
                                                           FileTitle = a.Key.FileTitle,
                                                           CustomFileId = a.Key.CustomFileId
                                                       }).ToList();

                        if (newProduct)
                        {
                            if (attachmentOrdersByManufacturer != null && attachmentOrdersByManufacturer.Any())
                            {
                                attachments = parsedProduct.Attachments.OrderBy(a => attachmentOrdersByManufacturer[parsedProduct.ManufacturerId].IndexOf(a.FileTitle)).ToList();
                            }
                            else
                            {
                                attachments = parsedProduct.Attachments.OrderBy(a => AttachmentOrderProvider.DefaultAttachmentsOrder.IndexOf(a.FileTitle)).ToList();
                            }
                        }

                        var existingAttachmentUrls = existingAttachmentsAddedByUrl.Select(x => x.File.SyncUrl).ToList();
                        var excelAttachmentUrls = attachments.Select(x => x.FileSyncUrl).ToList();
                        var newAttachmentUrls = excelAttachmentUrls.Except(existingAttachmentUrls).ToList();
                        var productFileWeight = 0;
                        foreach (var url in newAttachmentUrls)
                        {
                            var parsedProductFile = attachments.FirstOrDefault(x => x.FileSyncUrl == url);
                            var attachUrl = parsedProductFile.FileSyncUrl.Trim();
                            if (attachUrl[attachUrl.Length - 1] == '/')
                            {
                                attachUrl = attachUrl.Remove(attachUrl.Length - 1);
                            }
                            if (!attachUrl.StartsWith("http"))
                            {
                                attachUrl = "http://" + attachUrl;
                            }

                            HttpResponseHeadersInfo headers = await WepApiProvider.GetHeadersAsync(attachUrl);
                            string fileName = headers.FileName ?? FileHelper.RemoveQueryPath(Path.GetFileName(attachUrl));
                            string mediaType = MimeTypeProvider.GetMimeType(fileName);

                            var dbFile = new Domain.DBModels.File();
                            dbFile.FileName = fileName;
                            dbFile.MediaType = mediaType;
                            dbFile.CreatedById = userId;
                            dbFile.Title = parsedProductFile.FileTitle;
                            dbFile.CreatedDate = DateTime.UtcNow;
                            dbFile.PreviewUrl = new Flurl.Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment("/assets/img/default_preview.png").ToString();
                            dbFile.NextSyncDateTime = DateTime.UtcNow.AddDays(-1);
                            dbFile.SyncStatusCode = (int)HttpStatusCode.OK;
                            dbFile.UpdatesCount = 0;
                            dbFile.SyncUrl = attachUrl;

                            dbFile.Url = attachUrl;
                            dbFile.FileSize = 0;
                            dbFile.CheckSum = string.Empty;

                            unitOfWork.FileRepository.Insert(dbFile);
                            unitOfWork.Save();

                            ProductFile productFile = new ProductFile();
                            productFile.ProductId = product.Id;
                            productFile.CustomFileId = parsedProductFile.CustomFileId;
                            productFile.FileId = dbFile.Id;
                            productFile.IsAttachment = true;
                            productFile.Weight = productFileWeight;
                            productFile.CreatedById = userId;
                            productFile.CreatedDate = DateTime.UtcNow;
                            unitOfWork.ProductFileRepository.Insert(productFile);

                            if (newProduct)
                            {
                                productFileWeight++;
                            }
                            else
                            {
                                if (attachmentsOrder.ContainsValue($"{parsedProductFile.FileTitle.ToLowerInvariant()}|{attachUrl.ToLowerInvariant()}"))
                                {
                                    productFileWeight = attachmentsOrder.First(a => a.Value == $"{parsedProductFile.FileTitle.ToLowerInvariant()}|{attachUrl.ToLowerInvariant()}").Key;
                                }
                                else
                                {
                                    productFileWeight = attachments.Count + 2;
                                }
                            }
                        }
                    }
#if DEBUG
                    System.Diagnostics.Debug.WriteLine($"Passed attachments add for product with row index {rowIndex}");
#endif

                    //Add project files
                    if (parsedProduct.ProjectFiles != null && parsedProduct.ProjectFiles.Any())
                    {
                        var existingProjectFileUrls = product.ProductFiles.Where(x => !x.IsAttachment && x.File.SyncUrl != null).Select(x => x.File.SyncUrl).ToList();
                        var excelProjectFileUrls = parsedProduct.ProjectFiles.Select(x => x.FileSyncUrl).ToList();
                        var newProjectFileUrls = excelProjectFileUrls.Except(existingProjectFileUrls).ToList();

                        foreach (var url in newProjectFileUrls)
                        {
                            var parsedProductFile = parsedProduct.ProjectFiles.FirstOrDefault(x => x.FileSyncUrl == url);
                            var attachUrl = parsedProductFile.FileSyncUrl.Trim();
                            if (attachUrl[attachUrl.Length - 1] == '/')
                            {
                                attachUrl = attachUrl.Remove(attachUrl.Length - 1);
                            }
                            if (!attachUrl.StartsWith("http"))
                            {
                                attachUrl = "http://" + attachUrl;
                            }

                            HttpResponseHeadersInfo headers = await WepApiProvider.GetHeadersAsync(attachUrl);
                            string fileName = headers.FileName ?? FileHelper.RemoveQueryPath(Path.GetFileName(attachUrl));
                            string mediaType = MimeTypeProvider.GetMimeType(fileName);

                            var dbFile = new Domain.DBModels.File();
                            dbFile.FileName = fileName;
                            dbFile.MediaType = mediaType;
                            dbFile.CreatedById = userId;
                            dbFile.Title = parsedProductFile.FileTitle;
                            dbFile.CreatedDate = DateTime.UtcNow;
                            dbFile.PreviewUrl = new Flurl.Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment("/assets/img/default_preview.png").ToString();
                            dbFile.NextSyncDateTime = DateTime.UtcNow.AddDays(-1);
                            dbFile.SyncStatusCode = (int)HttpStatusCode.OK;
                            dbFile.UpdatesCount = 0;
                            dbFile.SyncUrl = attachUrl;

                            dbFile.Url = attachUrl;
                            dbFile.FileSize = 0;
                            dbFile.CheckSum = string.Empty;

                            unitOfWork.FileRepository.Insert(dbFile);
                            unitOfWork.Save();

                            ProductFile productFile = new ProductFile();
                            productFile.ProductId = product.Id;
                            productFile.CustomFileId = parsedProductFile.CustomFileId;
                            productFile.FileId = dbFile.Id;
                            productFile.CreatedById = userId;
                            productFile.CreatedDate = DateTime.UtcNow;
                            var projectDataType = projectDataTypes.FirstOrDefault(a => a.Title == parsedProductFile.FileType.Title);
                            if (projectDataType.ParentId.HasValue)
                            {
                                productFile.ProjectDataTypeId = projectDataType.ParentId;
                                productFile.SoftwareVersionId = projectDataType.Id;
                            }
                            else
                            {
                                productFile.ProjectDataTypeId = projectDataType.Id;
                            }
                            unitOfWork.ProductFileRepository.Insert(productFile);
                        }
                    }

#if DEBUG
                    System.Diagnostics.Debug.WriteLine($"Passed project files add for product with row index {rowIndex}");
#endif

                    //Add product cisfb
                    if (parsedProduct.ProductCisfbs != null)
                    {
                        foreach (var cisfbCode in parsedProduct.ProductCisfbs)
                        {
                            ProductCisfb productCisfb = new ProductCisfb();
                            productCisfb.CisfbId = cisfbs.First(a => a.Code == cisfbCode).Id;
                            productCisfb.ProductId = product.Id;
                            productCisfb.CreatedById = userId;
                            productCisfb.CreatedDate = DateTime.UtcNow;
                            unitOfWork.ProductCisfbRepository.Insert(productCisfb);
                        }
                    }

#if DEBUG
                    System.Diagnostics.Debug.WriteLine($"Passed cisfbs add for product with row index {rowIndex}");
#endif

                    //Add product masterformat
                    if (parsedProduct.ProductExternalMasterformatCodes != null)
                    {
                        foreach (var masterformatCode in parsedProduct.ProductExternalMasterformatCodes)
                        {
                            var masterformat = masterformats.FirstOrDefault(x => x.Code.Trim().ToUpper() == masterformatCode.Trim().ToUpper());
                            if (masterformat != null)
                            {
                                ProductMasterformat productMasterformat = new ProductMasterformat();
                                productMasterformat.ExternalMasterformatId = masterformat.Id;
                                productMasterformat.ProductId = product.Id;
                                productMasterformat.CreatedById = userId;
                                productMasterformat.CreatedDate = DateTime.UtcNow;
                                unitOfWork.ProductMasterformatRepository.Insert(productMasterformat);
                            }
                        }
                    }

#if DEBUG
                    System.Diagnostics.Debug.WriteLine($"Passed masterformats add for product with row index {rowIndex}");
#endif

                    //Add product omniclass
                    if (parsedProduct.ProductOmniclasses != null)
                    {
                        foreach (var omniclassCode in parsedProduct.ProductOmniclasses)
                        {
                            ProductOmniclass productOmniclass = new ProductOmniclass();
                            productOmniclass.OmniclassId = omniclasses.First(a => a.Code == omniclassCode).Id;
                            productOmniclass.ProductId = product.Id;
                            productOmniclass.CreatedById = userId;
                            productOmniclass.CreatedDate = DateTime.UtcNow;
                            unitOfWork.ProductOmniclassRepository.Insert(productOmniclass);
                        }
                    }

#if DEBUG
                    System.Diagnostics.Debug.WriteLine($"Passed omniclasses add for product with row index {rowIndex}");
#endif

                    if (parsedProduct.ProductUniclasses != null)
                    {
                        foreach (var uniclassCode in parsedProduct.ProductUniclasses)
                        {
                            ProductUniclass productUniclass = new ProductUniclass();
                            productUniclass.UniclassId = uniclasses.First(a => a.Code == uniclassCode).Id;
                            productUniclass.ProductId = product.Id;
                            productUniclass.CreatedById = userId;
                            productUniclass.CreatedDate = DateTime.UtcNow;
                            unitOfWork.ProductUniclassRepository.Insert(productUniclass);
                        }
                    }

#if DEBUG
                    System.Diagnostics.Debug.WriteLine($"Passed uniclasses add for product with row index {rowIndex}");
#endif

                    if (parsedProduct.ProductUniformats != null)
                    {
                        foreach (var uniclassCode in parsedProduct.ProductUniformats)
                        {
                            ProductUniformat productUniformat = new ProductUniformat();
                            productUniformat.UniformatId = uniformats.First(a => a.Code == uniclassCode).Id;
                            productUniformat.ProductId = product.Id;
                            productUniformat.CreatedById = userId;
                            productUniformat.CreatedDate = DateTime.UtcNow;
                            unitOfWork.ProductUniformatRepository.Insert(productUniformat);
                        }
                    }

#if DEBUG
                    System.Diagnostics.Debug.WriteLine($"Passed uniformats add for product with row index {rowIndex}");
#endif

                    var categoryKeyStats = unitOfWork.CategoryKeyStatRepository.GetAll().Where(ck => ck.CategoryId == product.CategoryId).ToList();

                    //Add product productStats
                    if (parsedProduct.ProductStats != null)
                    {
                        foreach (var parsedProductStat in parsedProduct.ProductStats)
                        {
                            List<KeyStatValueList> keyStatValueList = new List<KeyStatValueList>();
                            var keyStat = keyStats.First(ks => ks.Name.ToLower() == parsedProductStat.KeyStatName.ToLower());

                            var keyStatUnit = parsedProductStat.KeyStatUnit != null ?
                                    keyStatUnits.First(ks => ks.GroupName.Trim().ToLower() == parsedProductStat.KeyStatUnit.GroupName.ToLower()
                                    && ks.BUnitName.Trim().ToLower() == parsedProductStat.KeyStatUnit.BUnitName.ToLower()) : null;

                            ProductStats newProductStat = new ProductStats();
                            var productStatsComparer = new ProductStatModelComparer();
                            if (newProduct)
                            {
                                var categoryKeyStat = categoryKeyStats.OrderBy(ck => ck.Order).FirstOrDefault(ck => ck.KeyStatId == keyStat.Id);
                                newProductStat.Order = categoryKeyStat != null ? categoryKeyStat.Order : categoryKeyStats.Count + 1;
                            }
                            else
                            {
                                var prevKeyStatOrders = keyStatsOrders.Where(a => productStatsComparer.Equals(a.Key, parsedProductStat)).ToList();
                                newProductStat.Order = prevKeyStatOrders.Any() ? prevKeyStatOrders.First().Value : categoryKeyStats.Count + 1;
                            }
                            newProductStat.ProductId = product.Id;
                            newProductStat.KeyStatId = keyStat.Id;
                            newProductStat.KeyStatType = parsedProductStat.KeyStatType;
                            newProductStat.Note = parsedProductStat.Note;

                            if (parsedProductStat.KeyStatValueList != null && parsedProductStat.KeyStatValueList.Any())
                            {
                                newProductStat.KeyStatUnitId = null;
                            }
                            else
                            {
                                newProductStat.KeyStatUnitId = keyStatUnit?.Id;
                            }

                            newProductStat.CreatedById = userId;
                            newProductStat.CreatedDate = DateTime.UtcNow;

                            if (parsedProductStat.KeyStatType == KeyStatType.SingleNumeric || parsedProductStat.KeyStatType == KeyStatType.TextValue)
                            {
                                newProductStat.Value = parsedProductStat.Value;
                            }
                            else if (parsedProductStat.KeyStatType == KeyStatType.NumericRange)
                            {
                                newProductStat.MinRangeValue = parsedProductStat.MinRangeValue;
                                newProductStat.MaxRangeValue = parsedProductStat.MaxRangeValue;
                            }
                            else if (parsedProductStat.KeyStatType == KeyStatType.TextMultivalue)
                            {
                                foreach (var item in parsedProductStat.KeyStatValueList)
                                {
                                    KeyStatValueList keyStatValueListItem = new KeyStatValueList();

                                    keyStatValueListItem.Value = item.Value;
                                    keyStatValueListItem.MinRangeValue = item.MinRangeValue;
                                    keyStatValueListItem.MaxRangeValue = item.MaxRangeValue;
                                    keyStatValueListItem.KeyStatUnitId = keyStatUnit?.Id;
                                    keyStatValueListItem.Note = item.Note;
                                    keyStatValueListItem.CreatedById = userId;
                                    keyStatValueListItem.CreatedDate = DateTime.UtcNow;

                                    keyStatValueList.Add(keyStatValueListItem);
                                }
                            }
                            else if (parsedProductStat.KeyStatType == KeyStatType.MultivalueNumeric)
                            {
                                foreach (var item in parsedProductStat.KeyStatValueList)
                                {
                                    var valueListKeyStatUnit = item.KeyStatUnit != null ?
                                        keyStatUnits.First(ks => ks.GroupName.Trim().ToLower() == item.KeyStatUnit.GroupName.ToLower()
                                        && ks.BUnitName.Trim().ToLower() == item.KeyStatUnit.BUnitName.ToLower()) : null;

                                    KeyStatValueList keyStatValueListItem = new KeyStatValueList();

                                    keyStatValueListItem.Value = item.Value;
                                    keyStatValueListItem.MinRangeValue = item.MinRangeValue;
                                    keyStatValueListItem.MaxRangeValue = item.MaxRangeValue;
                                    keyStatValueListItem.KeyStatUnitId = valueListKeyStatUnit?.Id;
                                    keyStatValueListItem.Note = item.Note;
                                    keyStatValueListItem.CreatedById = userId;
                                    keyStatValueListItem.CreatedDate = DateTime.UtcNow;

                                    keyStatValueList.Add(keyStatValueListItem);
                                }
                            }
                            else if (parsedProductStat.KeyStatType == KeyStatType.None)
                            {
                                // empty key stat
                            }
                            else
                            {
                                throw new NotImplementedException("Passed not implemented KeyStatType");
                            }

                            unitOfWork.ProductStatsRepository.Insert(newProductStat);
                            unitOfWork.Save();

                            foreach (var item in keyStatValueList)
                            {
                                item.ProductStatsId = newProductStat.Id;
                                unitOfWork.KeyStatValueListRepository.Insert(item);
                            }
                        }
                    }

#if DEBUG
                    System.Diagnostics.Debug.WriteLine($"Passed keystats add for product with row index {rowIndex}");
#endif

                    unitOfWork.Save();

                    productIdUpdateDictionary.Add(product.Id, !newProduct);
                }
                catch (Exception e)
                {
                    excelParseData.Errors.Add($"Error to create product or update with Row Index {rowIndex}: {e.Message}");
                    Log.Error(e.Message, e);
#if DEBUG
                    System.Diagnostics.Debug.WriteLine($"Error to create product or update with Row Index {rowIndex}: {e.Message}");
#endif
                }

                rowIndex++;
            }

            if (!excelParseData.Errors.Any())
            {
                unitOfWork.CommitTransaction();

                foreach (KeyValuePair<int, bool> item in productIdUpdateDictionary)
                    await _productService.SaveProductToMongoAsync(item.Key, item.Value, unitOfWork);

                foreach (KeyValuePair<int, bool> item in productIdUpdateDictionary)
                    await _productService.SaveProductToMongoAsync(item.Key, item.Value, unitOfWork);

                AzureStorageService azureBlobProvider = new AzureStorageService(
                    ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                    ConfigurationHelper.GetValue("Environment"),
                    bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                    false);
                var revitsQueue = azureBlobProvider.GetQueueByName(AzureStorageConstants.ProductRevitsQueue);

                foreach (var fileIdString in revitFileIdsQueue)
                {
                    revitsQueue.SendMessage(fileIdString);
                }

                return excelParseData;
            }
            else
            {
                unitOfWork.RollbackTransaction();
            }

            return excelParseData;
        }

        public static async Task<ProductModel> ImportSingleProductAsync(
            ProductModel parsedProduct,
            List<int> manufacturerIds,
            List<StaticExcelManufacturerModel> manufacturers,
            List<StaticExcelCategoryModel> categories,
            string userId,
            IEnumerable<StaticExcelCacheQualityItemModel> qualityItems,
            IEnumerable<StaticExcelCacheCisfbModel> cisfbs,
            IEnumerable<StaticExcelCacheProjectTypeModel> projectDataTypes,
            List<MasterformatModel> masterformats,
            IEnumerable<StaticExcelCacheOmniclassModel> omniclasses,
            IEnumerable<StaticExcelCacheUniclassModel> uniclasses,
            IEnumerable<StaticExcelCacheUniformatModel> uniformats,
            IEnumerable<StaticExcelCacheKeystatModel> keyStats,
            IEnumerable<StaticExcelCacheKeystatUnitModel> keyStatUnits,
            IUnitOfWork unitOfWork
            )
        {
            try
            {
#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Product with row index {parsedProduct.RowIndex} processing started");
#endif
                if (parsedProduct.ExcelParseErrors.Any())
                {
#if DEBUG
                    System.Diagnostics.Debug.WriteLine($"This product has errors");
#endif
                    return parsedProduct;
                }

                StringValidatorHelper.ValidateStrings(parsedProduct);

                unitOfWork.BeginTransaction();

                var productLineIdsByManufacturer = unitOfWork.ProductLineRepository.GetAll()
                    .Where(a => a.ManufacturerId != null && manufacturerIds.Contains(a.ManufacturerId.Value))
                    .Select(a => new { a.Id, a.ManufacturerId })
                    .AsEnumerable()
                    .GroupBy(a => a.ManufacturerId.Value)
                    .ToDictionary(a => a.Key, a => a.Select(b => b.Id));

                var attachmentOrdersByManufacturer = unitOfWork.AttachmentOrderRepository.GetAll()
                     .Where(a => manufacturerIds.Contains(a.ManufacturerId))
                     .AsEnumerable()
                     .GroupBy(a => a.ManufacturerId)
                     .ToDictionary(a => a.Key, a => a.OrderBy(b => b.Order)
                     .Select(b => b.Type)
                     .ToList());

                var processedProductsWithSamePhoto = new List<int>();

                StaticExcelManufacturerModel currentManufacturer = manufacturers.FirstOrDefault(a => a.Id == parsedProduct.ManufacturerId);
                if (parsedProduct.ProductLineId.HasValue && !productLineIdsByManufacturer[parsedProduct.ManufacturerId].Contains(parsedProduct.ProductLineId.Value))
                {
                    parsedProduct.ExcelParseErrors.Add($"Can't find product line id '{parsedProduct.ProductLineId}' for this manufacturer");
#if DEBUG
                    System.Diagnostics.Debug.WriteLine($"Can't find product line id '{parsedProduct.ProductLineId}' for this manufacturer");
#endif
                    unitOfWork.RollbackTransaction();

                    return parsedProduct;
                }

                bool newProduct = false;
                Product product = null;
                var attachmentsOrder = new Dictionary<int, string>();
                var mainPhotoUrl = string.Empty;
                if (parsedProduct.Id == null || parsedProduct.Id == 0)
                {
                    product = new Product(); // create new product 
                    newProduct = true;
                }
                else
                {
                    product = unitOfWork.ProductRepository.GetAll().Include(a => a.ProductFiles).ThenInclude(b => b.File).FirstOrDefault(a => a.Id == parsedProduct.Id.Value);
                    if (product == null)
                    {
                        parsedProduct.ExcelParseErrors.Add($"Can't find product with Id '{parsedProduct.Id}'");

#if DEBUG
                        System.Diagnostics.Debug.WriteLine($"Can't find product with Id '{parsedProduct.Id}'");
#endif
                        unitOfWork.RollbackTransaction();

                        return parsedProduct;
                    }
                }

                product.Name = parsedProduct.Name;
                if (product.CategoryId != parsedProduct.CategoryId && product.Category != null)
                {
                    var detachedCategory = product.Category;
                    unitOfWork.CategoryRepository.Detached(detachedCategory);
                    product.Category = null;
                }

                product.CategoryId = parsedProduct.CategoryId;
                product.ManufacturerId = parsedProduct.ManufacturerId;
                product.ProductUrl = parsedProduct.ProductUrl;
                product.ULUrl = parsedProduct.ULUrl;
                product.ExternalId = parsedProduct.ExternalId;
                product.ProductLineId = parsedProduct.ProductLineId;
                product.PhotoId = parsedProduct.PhotoId;
                product.Description = parsedProduct.Description;
                product.VideoUrl = parsedProduct.VideoUrl;
                product.VanityURL = parsedProduct.VanityURL;
                product.MetaTitle = parsedProduct.MetaTitle;
                product.MetaDescription = parsedProduct.MetaDescription;
                product.MetaKeywords = parsedProduct.MetaKeywords;
                product.Keywords = parsedProduct.Keywords;
                product.Published = parsedProduct.Published;
                product.PublishedOnCustomMicrosite = parsedProduct.PublishedOnCustomMicrosite;
                product.PublishToPartner = parsedProduct.PublishToPartner;
                product.Staging = parsedProduct.Staging;

                product.IsImperialDefault = parsedProduct.ProductStats != null &&
                    parsedProduct.ProductStats.Any(a => (a.KeyStatUnit != null && (a.KeyStatUnit.BUnitName.ToLowerInvariant() == "in" || a.KeyStatUnit.BUnitName.ToLowerInvariant() == "ft")) ||
                    (a.KeyStatValueList != null && (a.KeyStatValueList.Any(b => b.KeyStatUnit != null && b.KeyStatUnit.BUnitName.ToLowerInvariant() == "in") ||
                                                    a.KeyStatValueList.Any(b => b.KeyStatUnit != null && b.KeyStatUnit.BUnitName.ToLowerInvariant() == "ft"))));

                product.ForgeWallURL = parsedProduct.ForgeWallURL;
                product.ForgeFloorURL = parsedProduct.ForgeFloorURL;
                product.ForgeCeilingURL = parsedProduct.ForgeCeilingURL;
                product.ForgeRoofURL = parsedProduct.ForgeRoofURL;
                product.Weight = parsedProduct.Weight;

                var mainCategoryName = categories.Where(a => a.Id == parsedProduct.CategoryId).Select(a => a.Name).FirstOrDefault();
                if (mainCategoryName == null)
                {
                    parsedProduct.ExcelParseErrors.Add($"Category with Id {parsedProduct.CategoryId} not exist");

#if DEBUG
                    System.Diagnostics.Debug.WriteLine($"Category with Id {parsedProduct.CategoryId} not exist");
#endif
                    unitOfWork.RollbackTransaction();

                    return parsedProduct;
                }

                var metaData = MetaDataHelper.GetProductMetaData(product.Name, mainCategoryName, currentManufacturer.Name);
                if (string.IsNullOrWhiteSpace(product.MetaTitle))
                {
                    product.MetaTitle = metaData.Title;
                }
                if (string.IsNullOrWhiteSpace(product.MetaDescription))
                {
                    product.MetaDescription = metaData.Description;
                }
                if (string.IsNullOrWhiteSpace(product.MetaKeywords))
                {
                    product.MetaKeywords = metaData.Keywords;
                }

                var keyStatsOrders = new List<KeyValuePair<ProductProductStatModel, int>>();
                if (newProduct)
                {
                    product.CreatedById = userId;
                    product.CreatedDate = DateTime.UtcNow;

                    var photoFromDb = unitOfWork.PhotoRepository.GetAll().FirstOrDefault(x => x.Id == parsedProduct.PhotoId);
                    product.PhotoId = photoFromDb?.Id;
                    product.Photo = photoFromDb;

                    unitOfWork.ProductRepository.Insert(product);
                    unitOfWork.Save();
                    parsedProduct.Id = product.Id;

#if DEBUG
                    System.Diagnostics.Debug.WriteLine($"Passed product creation with row index {parsedProduct.RowIndex}");
#endif
                }
                else
                {
                    product.ModifiedById = userId;
                    product.ModifiedDate = DateTime.UtcNow;

                    var photoFromDb = unitOfWork.PhotoRepository.GetAll().FirstOrDefault(x => x.Id == parsedProduct.PhotoId);
                    product.PhotoId = photoFromDb?.Id;
                    product.Photo = photoFromDb;

                    unitOfWork.ProductRepository.Edit(product);
                    unitOfWork.Save();

#if DEBUG
                    System.Diagnostics.Debug.WriteLine($"Passed product save with row index {parsedProduct.RowIndex}");
#endif

                    //Delete old data 
                    var productCertificates = product.ProductCertificates.ToList();
                    unitOfWork.ProductCertificateRepository.Delete(productCertificates);

                    var productCategories = product.ProductCategories.ToList();
                    unitOfWork.ProductCategoryRepository.Delete(productCategories);

                    var productQualityItems = product.ProductQualityItems.ToList();
                    unitOfWork.ProductQualityItemRepository.Delete(productQualityItems);

                    var productsWithSamePhotoId = unitOfWork.ProductRepository.GetAll().Where(x => x.Id != product.Id && x.PhotoId != null && x.PhotoId == product.PhotoId).ToList();

                    if (productsWithSamePhotoId.Any())
                    {
                        foreach (var productWithSamePhotoId in productsWithSamePhotoId)
                        {
                            if (!processedProductsWithSamePhoto.Contains(productWithSamePhotoId.Id))
                            {
                                productWithSamePhotoId.PhotoId = null;
                                productWithSamePhotoId.Photo = null;
                                unitOfWork.ProductRepository.Edit(productWithSamePhotoId);
                            }
                        }
                        processedProductsWithSamePhoto.Add(product.Id);
                    }

                    var productPhotos = product.ProductPhotos.Where(pf => pf.Photo != null && pf.Photo.UploadUrl != null).ToList();

                    mainPhotoUrl = productPhotos.Where(pf => pf.PhotoId == product.PhotoId).Select(x => x.Photo?.UploadUrl).FirstOrDefault();
                    var photos = productPhotos.Select(pp => pp.Photo).ToList();
                    unitOfWork.ProductPhotoRepository.Delete(productPhotos);
                    unitOfWork.PhotoRepository.Delete(photos);

                    var productCisfbs = product.ProductCisfbs.ToList();
                    unitOfWork.ProductCisfbRepository.Delete(productCisfbs);

                    var productMasterformats = product.ProductMasterformats.ToList();
                    unitOfWork.ProductMasterformatRepository.Delete(productMasterformats);

                    var productOmniclasses = product.ProductOmniclasses.ToList();
                    unitOfWork.ProductOmniclassRepository.Delete(productOmniclasses);

                    var productUniclasses = product.ProductUniclasses.ToList();
                    unitOfWork.ProductUniclassRepository.Delete(productUniclasses);

                    var productUniformats = product.ProductUniformats.ToList();
                    unitOfWork.ProductUniformatRepository.Delete(productUniformats);

                    var productStatsToDelete = product.ProductStats.ToList();
                    foreach (var productStatToDelete in productStatsToDelete)
                    {
                        keyStatsOrders.Add(new KeyValuePair<ProductProductStatModel, int>(new ProductProductStatModel
                        {
                            KeyStatName = productStatToDelete.KeyStat.Name,
                            KeyStatType = productStatToDelete.KeyStatType,
                            KeyStatUnit = productStatToDelete.KeyStatUnit == null ? null : new ProductStatKeyStatUnitModel
                            {
                                GroupName = productStatToDelete.KeyStatUnit.GroupName,
                                BUnitName = productStatToDelete.KeyStatUnit.BUnitName
                            },
                            Note = productStatToDelete.Note,
                            Value = productStatToDelete.Value,
                            MinRangeValue = productStatToDelete.MinRangeValue,
                            MaxRangeValue = productStatToDelete.MaxRangeValue,
                            KeyStatValueList = productStatToDelete.KeyStatValueList.Select(ksvl => new ProductStatKeyStatValueListModel
                            {
                                Value = ksvl.Value,
                                MinRangeValue = ksvl.MinRangeValue,
                                MaxRangeValue = ksvl.MaxRangeValue,
                                KeyStatUnit = ksvl.KeyStatUnit == null ? null : new ProductStatKeyStatUnitModel
                                {
                                    BUnitName = ksvl.KeyStatUnit.BUnitName,
                                    GroupName = ksvl.KeyStatUnit.GroupName
                                }
                            }).ToList()
                        }, productStatToDelete.Order));
                        unitOfWork.KeyStatValueListRepository.Delete(productStatToDelete.KeyStatValueList.ToList());
                    }
                    unitOfWork.ProductStatsRepository.Delete(productStatsToDelete);
                }

                unitOfWork.Save();
#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Passed old data deleted for product with row index {parsedProduct.RowIndex}");
#endif

                //Add hero shots
                var addedPhotoIds = new Dictionary<int, string>();
                if (parsedProduct.PhotoURLs != null)
                {
                    foreach (var photoUrl in parsedProduct.PhotoURLs.Where(a => !string.IsNullOrEmpty(a)).ToList())
                    {
                        var attachUrl = photoUrl.Trim();
                        if (!attachUrl.StartsWith("http"))
                        {
                            attachUrl = "http://" + attachUrl;
                        }

                        var fileName = Path.GetFileName(attachUrl);
                        Photo photo = new Photo();
                        photo.CreatedById = userId;
                        photo.CreatedDate = DateTime.UtcNow;
                        photo.Name = fileName;
                        photo.UploadUrl = attachUrl;
                        unitOfWork.PhotoRepository.Insert(photo);

                        unitOfWork.Save();

                        int redirectsCount = 0;
                    StartRedirect:

                        if (attachUrl.StartsWith("https://"))
                        {
                            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;
                            ServicePointManager.ServerCertificateValidationCallback += (sender, certificate, chain, sslPolicyErrors) => true;
                        }

                        HttpClient httpClient = HttpClientFactory.GetClient();
                        using (var photoResponse = await httpClient.GetAsync(attachUrl))
                        {
                            if (photoResponse.StatusCode == HttpStatusCode.OK || photoResponse.StatusCode == HttpStatusCode.Accepted)
                            {
                                var originalImage = new MemoryStream();
                                using (Stream photoResponseStream = await photoResponse.Content.ReadAsStreamAsync())
                                {
                                    photoResponseStream.CopyTo(originalImage);
                                    var imageName = $"{currentManufacturer.Name}-{product.Name}-revit-";
                                    await PhotoProvider.CreateProductPhotosAsync(photo, originalImage, Path.GetExtension(attachUrl), imageName, false);
                                    photo.SyncStatusCode = (int)HttpStatusCode.OK;
                                    photo.UpdatesCount = 0;
                                    unitOfWork.PhotoRepository.Edit(photo);
                                    unitOfWork.Save();
                                }
                            }
                            else if ((photoResponse.StatusCode == HttpStatusCode.Moved || photoResponse.StatusCode == HttpStatusCode.MovedPermanently || photoResponse.StatusCode == HttpStatusCode.Found) &&
                                     !string.IsNullOrWhiteSpace(GetHeaderValue(photoResponse.Headers, "Location")) &&
                                     redirectsCount < 2)
                            {
                                redirectsCount++;
                                attachUrl = GetHeaderValue(photoResponse.Headers, "Location");
                                photoResponse.Dispose();
                                goto StartRedirect;
                            }
                        }

                        ProductPhoto productPhoto = new ProductPhoto();
                        productPhoto.ProductId = product.Id;
                        productPhoto.PhotoId = photo.Id;
                        productPhoto.CreatedById = userId;
                        productPhoto.CreatedDate = DateTime.UtcNow;
                        unitOfWork.ProductPhotoRepository.Insert(productPhoto);
                        addedPhotoIds.Add(photo.Id, photo.UploadUrl);
                    }
                }
                unitOfWork.Save();

#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Passed hero shots add for product with row index {parsedProduct.RowIndex}");
#endif

                if (parsedProduct.PhotoId != null)
                {
                    var mainPhoto = unitOfWork.PhotoRepository.GetById(parsedProduct.PhotoId.Value);
                    if (mainPhoto != null)
                    {
                        product.PhotoId = parsedProduct.PhotoId.Value;
                        unitOfWork.ProductRepository.Edit(product);

                        var productPhotos = unitOfWork.ProductPhotoRepository.GetAll()
                            .Where(a => a.ProductId == product.Id && (a.PhotoId == mainPhoto.Id || (a.Photo.UploadUrl != null && a.Photo.UploadUrl == mainPhoto.UploadUrl))).ToList();

                        unitOfWork.ProductPhotoRepository.Delete(productPhotos);
                        unitOfWork.Save();

                        ProductPhoto productPhoto = new ProductPhoto();
                        productPhoto.ProductId = product.Id;
                        productPhoto.PhotoId = mainPhoto.Id;
                        productPhoto.CreatedById = userId;
                        productPhoto.CreatedDate = DateTime.UtcNow;

                        unitOfWork.ProductPhotoRepository.Insert(productPhoto);
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(mainPhotoUrl))
                        {
                            int? mainPhotoFromURLId = addedPhotoIds.Where(x => x.Value.ToLower() == mainPhotoUrl.ToLower()).Select(x => x.Key).FirstOrDefault();
                            if (mainPhotoFromURLId != null && mainPhotoFromURLId != 0) product.PhotoId = mainPhotoFromURLId;
                            else product.PhotoId = product.ProductPhotos.Any() ? product.ProductPhotos.First().PhotoId : default(int?);
                            unitOfWork.ProductRepository.Edit(product);
                        }
                        else
                        {
                            product.PhotoId = product.ProductPhotos.Any() ? product.ProductPhotos.First().PhotoId : default(int?);
                            unitOfWork.ProductRepository.Edit(product);
                        }
                    }
                }
                else
                {
                    product.PhotoId = addedPhotoIds.Any() ? addedPhotoIds.First().Key : default(int?);
                    unitOfWork.ProductRepository.Edit(product);
                }
                unitOfWork.Save();
#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Passed photo add for product with row index {parsedProduct.RowIndex}");
#endif

                //Add product certificate
                if (parsedProduct.ProductCertificates != null)
                {
                    foreach (var certificateId in parsedProduct.ProductCertificates)
                    {
                        ProductCertificate productCertificate = new ProductCertificate();
                        productCertificate.ProductId = product.Id;
                        productCertificate.ExternalCertificateId = certificateId;
                        productCertificate.CreatedById = userId;
                        productCertificate.CreatedDate = DateTime.UtcNow;
                        unitOfWork.ProductCertificateRepository.Insert(productCertificate);
                    }
                }

#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Passed certificates add for product with row index {parsedProduct.RowIndex}");
#endif

                if (parsedProduct.ProductCategoryIds != null)
                {
                    foreach (var productCategoryId in parsedProduct.ProductCategoryIds)
                    {
                        ProductCategory productCategory = new ProductCategory();
                        productCategory.ProductId = product.Id;
                        productCategory.CategoryId = productCategoryId;
                        unitOfWork.ProductCategoryRepository.Insert(productCategory);
                    }
                }

#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Passed categories add for product with row index {parsedProduct.RowIndex}");
#endif

                //Add product Quality Items
                if (parsedProduct.ProductQualityItems != null)
                {
                    foreach (var qualityItemName in parsedProduct.ProductQualityItems)
                    {
                        ProductQualityItem productQualityItem = new ProductQualityItem();
                        productQualityItem.ProductId = product.Id;
                        productQualityItem.QualityItemId = qualityItems.First(a => a.Name == qualityItemName).Id;
                        productQualityItem.CreatedById = userId;
                        productQualityItem.CreatedDate = DateTime.UtcNow;
                        unitOfWork.ProductQualityItemRepository.Insert(productQualityItem);
                    }
                }

#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Passed quality items add for product with row index {parsedProduct.RowIndex}");
#endif

                //Add product files
                if (parsedProduct.Attachments != null && parsedProduct.Attachments.Any())
                {
                    var existingAttachmentsAddedByUrl = product.ProductFiles.Where(x => x.IsAttachment && x.File.SyncUrl != null).ToList();
                    foreach (var attachment in existingAttachmentsAddedByUrl)
                    {
                        attachmentsOrder.Add(attachment.Id, $"{attachment.File.Title.ToLowerInvariant()}|{attachment.File.SyncUrl.ToLowerInvariant()}");
                    }

                    var attachments = parsedProduct.Attachments.GroupBy(a => new { a.FileTitle, a.FileSyncUrl, a.CustomFileId })
                                                   .Select(a => new ProductFileModel
                                                   {
                                                       FileSyncUrl = a.Key.FileSyncUrl,
                                                       FileTitle = a.Key.FileTitle,
                                                       CustomFileId = a.Key.CustomFileId
                                                   }).ToList();

                    if (newProduct)
                    {
                        if (attachmentOrdersByManufacturer != null && attachmentOrdersByManufacturer.Any())
                        {
                            attachments = parsedProduct.Attachments.OrderBy(a => attachmentOrdersByManufacturer[parsedProduct.ManufacturerId].IndexOf(a.FileTitle)).ToList();
                        }
                        else
                        {
                            attachments = parsedProduct.Attachments.OrderBy(a => AttachmentOrderProvider.DefaultAttachmentsOrder.IndexOf(a.FileTitle)).ToList();
                        }
                    }

                    var existingAttachmentUrls = existingAttachmentsAddedByUrl.Select(x => x.File.SyncUrl).ToList();
                    var excelAttachmentUrls = attachments.Select(x => x.FileSyncUrl).ToList();
                    var newAttachmentUrls = excelAttachmentUrls.Except(existingAttachmentUrls).ToList();
                    var productFileWeight = 0;
                    foreach (var url in newAttachmentUrls)
                    {
                        var parsedProductFile = attachments.FirstOrDefault(x => x.FileSyncUrl == url);
                        var attachUrl = parsedProductFile.FileSyncUrl.Trim();
                        if (attachUrl[attachUrl.Length - 1] == '/')
                        {
                            attachUrl = attachUrl.Remove(attachUrl.Length - 1);
                        }
                        if (!attachUrl.StartsWith("http"))
                        {
                            attachUrl = "http://" + attachUrl;
                        }

                        HttpResponseHeadersInfo headers = await WepApiProvider.GetHeadersAsync(attachUrl);
                        string fileName = headers.FileName ?? FileHelper.RemoveQueryPath(Path.GetFileName(attachUrl));
                        string mediaType = MimeTypeProvider.GetMimeType(fileName);

                        var dbFile = new Domain.DBModels.File();
                        dbFile.FileName = fileName;
                        dbFile.MediaType = mediaType;
                        dbFile.CreatedById = userId;
                        dbFile.Title = parsedProductFile.FileTitle;
                        dbFile.CreatedDate = DateTime.UtcNow;
                        dbFile.PreviewUrl = new Flurl.Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment("/assets/img/default_preview.png");
                        dbFile.NextSyncDateTime = DateTime.UtcNow.AddDays(-1);
                        dbFile.SyncStatusCode = (int)HttpStatusCode.OK;
                        dbFile.UpdatesCount = 0;
                        dbFile.SyncUrl = attachUrl;

                        dbFile.Url = attachUrl;
                        dbFile.FileSize = 0;
                        dbFile.CheckSum = string.Empty;

                        unitOfWork.FileRepository.Insert(dbFile);
                        unitOfWork.Save();

                        ProductFile productFile = new ProductFile();
                        productFile.ProductId = product.Id;
                        productFile.CustomFileId = parsedProductFile.CustomFileId;
                        productFile.FileId = dbFile.Id;
                        productFile.IsAttachment = true;
                        productFile.Weight = productFileWeight;
                        productFile.CreatedById = userId;
                        productFile.CreatedDate = DateTime.UtcNow;
                        unitOfWork.ProductFileRepository.Insert(productFile);

                        if (newProduct)
                        {
                            productFileWeight++;
                        }
                        else
                        {
                            if (attachmentsOrder.ContainsValue($"{parsedProductFile.FileTitle.ToLowerInvariant()}|{attachUrl.ToLowerInvariant()}"))
                            {
                                productFileWeight = attachmentsOrder.First(a => a.Value == $"{parsedProductFile.FileTitle.ToLowerInvariant()}|{attachUrl.ToLowerInvariant()}").Key;
                            }
                            else
                            {
                                productFileWeight = attachments.Count + 2;
                            }
                        }
                    }
                }
#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Passed attachments add for product with row index {parsedProduct.RowIndex}");
#endif

                //Add project files
                if (parsedProduct.ProjectFiles != null && parsedProduct.ProjectFiles.Any())
                {
                    var existingProjectFileUrls = product.ProductFiles.Where(x => !x.IsAttachment && x.File.SyncUrl != null).Select(x => x.File.SyncUrl).ToList();
                    var excelProjectFileUrls = parsedProduct.ProjectFiles.Select(x => x.FileSyncUrl).ToList();
                    var newProjectFileUrls = excelProjectFileUrls.Except(existingProjectFileUrls).ToList();

                    foreach (var url in newProjectFileUrls)
                    {
                        var parsedProductFile = parsedProduct.ProjectFiles.FirstOrDefault(x => x.FileSyncUrl == url);
                        var attachUrl = parsedProductFile.FileSyncUrl.Trim();
                        if (attachUrl[attachUrl.Length - 1] == '/')
                        {
                            attachUrl = attachUrl.Remove(attachUrl.Length - 1);
                        }
                        if (!attachUrl.StartsWith("http"))
                        {
                            attachUrl = "http://" + attachUrl;
                        }

                        HttpResponseHeadersInfo headers = await WepApiProvider.GetHeadersAsync(attachUrl);
                        string fileName = headers.FileName ?? FileHelper.RemoveQueryPath(Path.GetFileName(attachUrl));
                        string mediaType = MimeTypeProvider.GetMimeType(fileName);

                        var dbFile = new Domain.DBModels.File();
                        dbFile.FileName = fileName;
                        dbFile.MediaType = mediaType;
                        dbFile.CreatedById = userId;
                        dbFile.Title = parsedProductFile.FileTitle;
                        dbFile.CreatedDate = DateTime.UtcNow;
                        dbFile.PreviewUrl = new Flurl.Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl")).AppendPathSegment("/assets/img/default_preview.png");
                        dbFile.NextSyncDateTime = DateTime.UtcNow.AddDays(-1);
                        dbFile.SyncStatusCode = (int)HttpStatusCode.OK;
                        dbFile.UpdatesCount = 0;
                        dbFile.SyncUrl = attachUrl;

                        dbFile.Url = attachUrl;
                        dbFile.FileSize = 0;
                        dbFile.CheckSum = string.Empty;

                        unitOfWork.FileRepository.Insert(dbFile);
                        unitOfWork.Save();

                        ProductFile productFile = new ProductFile();
                        productFile.ProductId = product.Id;
                        productFile.CustomFileId = parsedProductFile.CustomFileId;
                        productFile.FileId = dbFile.Id;
                        productFile.CreatedById = userId;
                        productFile.CreatedDate = DateTime.UtcNow;
                        var projectDataType = projectDataTypes.FirstOrDefault(a => a.Title == parsedProductFile.FileType.Title);
                        if (projectDataType.ParentId.HasValue)
                        {
                            productFile.ProjectDataTypeId = projectDataType.ParentId;
                            productFile.SoftwareVersionId = projectDataType.Id;
                        }
                        else
                        {
                            productFile.ProjectDataTypeId = projectDataType.Id;
                        }
                        unitOfWork.ProductFileRepository.Insert(productFile);
                    }
                }

#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Passed project files add for product with row index {parsedProduct.RowIndex}");
#endif

                //Add product cisfb
                if (parsedProduct.ProductCisfbs != null)
                {
                    foreach (var cisfbCode in parsedProduct.ProductCisfbs)
                    {
                        ProductCisfb productCisfb = new ProductCisfb();
                        productCisfb.CisfbId = cisfbs.First(a => a.Code == cisfbCode).Id;
                        productCisfb.ProductId = product.Id;
                        productCisfb.CreatedById = userId;
                        productCisfb.CreatedDate = DateTime.UtcNow;
                        unitOfWork.ProductCisfbRepository.Insert(productCisfb);
                    }
                }

#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Passed cisfbs add for product with row index {parsedProduct.RowIndex}");
#endif

                //Add product masterformat
                if (parsedProduct.ProductExternalMasterformatCodes != null)
                {
                    foreach (var masterformatCode in parsedProduct.ProductExternalMasterformatCodes)
                    {
                        var masterformat = masterformats.FirstOrDefault(x => x.Code.Trim().ToUpper() == masterformatCode.Trim().ToUpper());
                        if (masterformat != null)
                        {
                            ProductMasterformat productMasterformat = new ProductMasterformat();
                            productMasterformat.ExternalMasterformatId = masterformat.Id;
                            productMasterformat.ProductId = product.Id;
                            productMasterformat.CreatedById = userId;
                            productMasterformat.CreatedDate = DateTime.UtcNow;
                            unitOfWork.ProductMasterformatRepository.Insert(productMasterformat);
                        }
                    }
                }

#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Passed masterformats add for product with row index {parsedProduct.RowIndex}");
#endif

                //Add product omniclass
                if (parsedProduct.ProductOmniclasses != null)
                {
                    foreach (var omniclassCode in parsedProduct.ProductOmniclasses)
                    {
                        ProductOmniclass productOmniclass = new ProductOmniclass();
                        productOmniclass.OmniclassId = omniclasses.First(a => a.Code == omniclassCode).Id;
                        productOmniclass.ProductId = product.Id;
                        productOmniclass.CreatedById = userId;
                        productOmniclass.CreatedDate = DateTime.UtcNow;
                        unitOfWork.ProductOmniclassRepository.Insert(productOmniclass);
                    }
                }

#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Passed omniclasses add for product with row index {parsedProduct.RowIndex}");
#endif

                if (parsedProduct.ProductUniclasses != null)
                {
                    foreach (var uniclassCode in parsedProduct.ProductUniclasses)
                    {
                        ProductUniclass productUniclass = new ProductUniclass();
                        productUniclass.UniclassId = uniclasses.First(a => a.Code == uniclassCode).Id;
                        productUniclass.ProductId = product.Id;
                        productUniclass.CreatedById = userId;
                        productUniclass.CreatedDate = DateTime.UtcNow;
                        unitOfWork.ProductUniclassRepository.Insert(productUniclass);
                    }
                }

#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Passed uniclasses add for product with row index {parsedProduct.RowIndex}");
#endif

                if (parsedProduct.ProductUniformats != null)
                {
                    foreach (var uniclassCode in parsedProduct.ProductUniformats)
                    {
                        ProductUniformat productUniformat = new ProductUniformat();
                        productUniformat.UniformatId = uniformats.First(a => a.Code == uniclassCode).Id;
                        productUniformat.ProductId = product.Id;
                        productUniformat.CreatedById = userId;
                        productUniformat.CreatedDate = DateTime.UtcNow;
                        unitOfWork.ProductUniformatRepository.Insert(productUniformat);
                    }
                }

#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Passed uniformats add for product with row index {parsedProduct.RowIndex}");
#endif

                var categoryKeyStats = unitOfWork.CategoryKeyStatRepository.GetAll().Where(ck => ck.CategoryId == product.CategoryId).ToList();

                //Add product productStats
                if (parsedProduct.ProductStats != null)
                {
                    foreach (var parsedProductStat in parsedProduct.ProductStats)
                    {
                        List<KeyStatValueList> keyStatValueList = new List<KeyStatValueList>();
                        var keyStat = keyStats.First(ks => ks.Name.ToLower() == parsedProductStat.KeyStatName.ToLower());

                        var keyStatUnit = parsedProductStat.KeyStatUnit != null ?
                                keyStatUnits.First(ks => ks.GroupName.Trim().ToLower() == parsedProductStat.KeyStatUnit.GroupName.ToLower()
                                && ks.BUnitName.Trim().ToLower() == parsedProductStat.KeyStatUnit.BUnitName.ToLower()) : null;

                        ProductStats newProductStat = new ProductStats();
                        var productStatsComparer = new ProductStatModelComparer();
                        if (newProduct)
                        {
                            var categoryKeyStat = categoryKeyStats.OrderBy(ck => ck.Order).FirstOrDefault(ck => ck.KeyStatId == keyStat.Id);
                            newProductStat.Order = categoryKeyStat != null ? categoryKeyStat.Order : categoryKeyStats.Count + 1;
                        }
                        else
                        {
                            var prevKeyStatOrders = keyStatsOrders.Where(a => productStatsComparer.Equals(a.Key, parsedProductStat)).ToList();
                            newProductStat.Order = prevKeyStatOrders.Any() ? prevKeyStatOrders.First().Value : categoryKeyStats.Count + 1;
                        }
                        newProductStat.ProductId = product.Id;
                        newProductStat.KeyStatId = keyStat.Id;
                        newProductStat.KeyStatType = parsedProductStat.KeyStatType;
                        newProductStat.Note = parsedProductStat.Note;

                        if (parsedProductStat.KeyStatValueList != null && parsedProductStat.KeyStatValueList.Any())
                        {
                            newProductStat.KeyStatUnitId = null;
                        }
                        else
                        {
                            newProductStat.KeyStatUnitId = keyStatUnit?.Id;
                        }

                        newProductStat.CreatedById = userId;
                        newProductStat.CreatedDate = DateTime.UtcNow;

                        if (parsedProductStat.KeyStatType == KeyStatType.SingleNumeric || parsedProductStat.KeyStatType == KeyStatType.TextValue)
                        {
                            newProductStat.Value = parsedProductStat.Value;
                        }
                        else if (parsedProductStat.KeyStatType == KeyStatType.NumericRange)
                        {
                            newProductStat.MinRangeValue = parsedProductStat.MinRangeValue;
                            newProductStat.MaxRangeValue = parsedProductStat.MaxRangeValue;
                        }
                        else if (parsedProductStat.KeyStatType == KeyStatType.TextMultivalue)
                        {
                            foreach (var item in parsedProductStat.KeyStatValueList)
                            {
                                KeyStatValueList keyStatValueListItem = new KeyStatValueList();

                                keyStatValueListItem.Value = item.Value;
                                keyStatValueListItem.MinRangeValue = item.MinRangeValue;
                                keyStatValueListItem.MaxRangeValue = item.MaxRangeValue;
                                keyStatValueListItem.KeyStatUnitId = keyStatUnit?.Id;
                                keyStatValueListItem.Note = item.Note;
                                keyStatValueListItem.CreatedById = userId;
                                keyStatValueListItem.CreatedDate = DateTime.UtcNow;

                                keyStatValueList.Add(keyStatValueListItem);
                            }
                        }
                        else if (parsedProductStat.KeyStatType == KeyStatType.MultivalueNumeric)
                        {
                            foreach (var item in parsedProductStat.KeyStatValueList)
                            {
                                var valueListKeyStatUnit = item.KeyStatUnit != null ?
                                    keyStatUnits.FirstOrDefault(ks => ks.GroupName.Trim().ToLower() == item.KeyStatUnit.GroupName.ToLower()
                                    && ks.BUnitName.Trim().ToLower() == item.KeyStatUnit.BUnitName.ToLower()) : null;

                                KeyStatValueList keyStatValueListItem = new KeyStatValueList();

                                keyStatValueListItem.Value = item.Value;
                                keyStatValueListItem.MinRangeValue = item.MinRangeValue;
                                keyStatValueListItem.MaxRangeValue = item.MaxRangeValue;
                                keyStatValueListItem.KeyStatUnitId = valueListKeyStatUnit?.Id;
                                keyStatValueListItem.Note = item.Note;
                                keyStatValueListItem.CreatedById = userId;
                                keyStatValueListItem.CreatedDate = DateTime.UtcNow;

                                keyStatValueList.Add(keyStatValueListItem);
                            }
                        }
                        else if (parsedProductStat.KeyStatType == KeyStatType.None)
                        {
                            // empty key stat
                        }
                        else
                        {
                            throw new NotImplementedException("Passed not implemented KeyStatType");
                        }

                        unitOfWork.ProductStatsRepository.Insert(newProductStat);
                        unitOfWork.Save();

                        foreach (var item in keyStatValueList)
                        {
                            item.ProductStatsId = newProductStat.Id;
                            unitOfWork.KeyStatValueListRepository.Insert(item);
                        }
                    }
                }

#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Passed keystats add for product with row index {parsedProduct.RowIndex}");
#endif

                unitOfWork.Save();

                if (product.Price == null)
                    await _priceService.SetDefaultPriceToProductAsync(product.Id, unitOfWork);

                unitOfWork.CommitTransaction();

                if (!newProduct && product.Price != null)
                    await _priceService.MoveProductFilesBetweenPaidAndFreeContainerAsync(product.Id, product.Price.PriceType, product.Price.PriceType, unitOfWork, true);

                await _productService.SaveProductToMongoAsync(product.Id, !newProduct, unitOfWork);
                _cacheService.RemoveAllByPattern("*api/Product/List*");
                _cacheService.RemoveAllByPattern("*api/Product/AdminList*");
            }
            catch (Exception e)
            {
                try
                {
                    unitOfWork.RollbackTransaction();
                }
                catch (Exception ex)
                {
                    parsedProduct.ExcelParseErrors.Add($"Error to create product or update with Row Index {parsedProduct.RowIndex}: {ex.Message}");
                }
                parsedProduct.ExcelParseErrors.Add($"Error to create product or update with Row Index {parsedProduct.RowIndex}: {e.Message}");
                Log.Error(e.Message, e);
#if DEBUG
                System.Diagnostics.Debug.WriteLine($"Error to create product or update with Row Index {parsedProduct.RowIndex}: {e.Message}");
#endif
            }

            return parsedProduct;
        }

        private const int CONNECTION_LIMIT = 5;
        private static HttpClient GetHttpClient()
        {
            ServicePointManager.DefaultConnectionLimit = CONNECTION_LIMIT;
            return new HttpClient();
        }

        private static string GetHeaderValue(HttpHeaders headers, string headerKey)
        {
            headers.TryGetValues(headerKey, out var values);
            return values.FirstOrDefault();
        }

        private static async Task ValidateProductUrlsAsync(ProductModel product)
        {
            if (product.Attachments != null)
            {
                foreach (ProductFileModel attachment in product.Attachments)
                {
                    if (!string.IsNullOrEmpty(attachment.FileSyncUrl))
                    {
                        if (attachment.FileTitle.Equals("Image") && !await attachment.FileSyncUrl.IsActiveImageUrlAsync())
                        {
                            product.ExcelParseErrors.Add($"Product Id {product.Id}. Invalid image url: {attachment.FileSyncUrl}");
                        }
                        else if (!await attachment.FileSyncUrl.IsActiveUrlAsync())
                        {
                            product.ExcelParseErrors.Add($"Product Id {product.Id}. Invalid attachment url: {attachment.FileSyncUrl}");
                        }
                    }
                }
            }

            if (product.PhotoURLs != null)
            {
                foreach (var photoUrl in product.PhotoURLs)
                {
                    if (!string.IsNullOrEmpty(photoUrl) && !await photoUrl.IsActiveImageUrlAsync())
                    {
                        product.ExcelParseErrors.Add($"Product Id {product.Id}. Invalid Photo url: {photoUrl}");
                    }
                }
            }

            if (!string.IsNullOrEmpty(product.VideoUrl) && !await product.VideoUrl.IsActiveUrlAsync())
            {
                product.ExcelParseErrors.Add($"Product Id {product.Id}. Invalid Video url: {product.VideoUrl}");
            }

            if (!string.IsNullOrEmpty(product.ProductUrl) && !await product.ProductUrl.IsActiveUrlAsync())
            {
                product.ExcelParseErrors.Add($"Product Id {product.Id}. Invalid Product url: {product.ProductUrl}");
            }

            if (!string.IsNullOrEmpty(product.ULUrl) && !await product.ULUrl.IsActiveUrlAsync())
            {
                product.ExcelParseErrors.Add($"Product Id {product.Id}. Invalid UL SPOT URL: {product.ULUrl}");
            }

            if (!string.IsNullOrEmpty(product.ForgeWallURL) && !await product.ForgeWallURL.IsActiveUrlAsync())
            {
                product.ExcelParseErrors.Add($"Product Id {product.Id}. Invalid Forge Wall url: {product.ForgeWallURL}");
            }

            if (!string.IsNullOrEmpty(product.ForgeFloorURL) && !await product.ForgeFloorURL.IsActiveUrlAsync())
            {
                product.ExcelParseErrors.Add($"Product Id {product.Id}. Invalid Forge Floor url: {product.ForgeFloorURL}");
            }

            if (!string.IsNullOrEmpty(product.ForgeCeilingURL) && !await product.ForgeCeilingURL.IsActiveUrlAsync())
            {
                product.ExcelParseErrors.Add($"Product Id {product.Id}. Invalid Forge Ceiling url: {product.ForgeCeilingURL}");
            }

            if (!string.IsNullOrEmpty(product.ForgeRoofURL) && !await product.ForgeRoofURL.IsActiveUrlAsync())
            {
                product.ExcelParseErrors.Add($"Product Id {product.Id}. Invalid Forge Roof url: {product.ForgeRoofURL}");
            }
        }
    }
}