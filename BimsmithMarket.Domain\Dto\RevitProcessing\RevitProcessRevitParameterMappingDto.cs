﻿using BIMsmithMarket.Domain.Enums;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Dto.RevitProcessing
{
    public class RevitProcessRevitParameterMappingDto
    {
        [StringLength(100)]
        public string RevitParameter { get; set; }

        public MarketField? MarketField { get; set; }

        [StringLength(100)]
        public string CustomValue { get; set; }
    }
}