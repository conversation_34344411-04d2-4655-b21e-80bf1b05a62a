﻿using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Dto.SketchupDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services;
using Microsoft.AspNetCore.Mvc;
using System.Net.Http;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// The controller for interaction with Sketchup Warehouse
    /// Site: https://3dwarehouse.sketchup.com/
    /// Documentation: https://3dwarehouse.sketchup.com/apidoc/integration/index.html#/
    /// </summary>
    [Route("api/[controller]/[action]")]
    public class SketchupController : BaseApiController
    {
        private readonly IHttpClientFactory _httpClientFactory;

        public SketchupController(IHttpClientFactory httpClientFactory)
        {
            _httpClientFactory = httpClientFactory;
        }

        /// <summary>
        /// Pushes products with Sketchup files to Sketchup warehouse
        /// Returns products related to Swatchbox products and options 
        /// </summary>
        /// <param name="model">The request model</param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        public async Task<IActionResult> AddProducts(SketchupFilterProductsDto model)
        {
            LogHelper.LogInfo(ConfigurationHelper.GetValue("SketchupAPI:LogFilePath"), $"Start adding products for manufacturer {model.ManufacturerId}");

            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            SketchupService _sketchupService = new SketchupService(model.ManufacturerId, _httpClientFactory);
            SketchupProductDto[] products = await _sketchupService.GetProductsWithSketchup(model, false, unitOfWork);
            await _sketchupService.AddProductsAsync(products, unitOfWork);

            LogHelper.LogInfo(ConfigurationHelper.GetValue("SketchupAPI:LogFilePath"), $"{products.Length} have been added for manufacturer {model.ManufacturerId}");
            return Ok();
        }

        /// <summary>
        /// Pushes products with Sketchup files to Sketchup warehouse
        /// Returns products related to Swatchbox products and options 
        /// </summary>
        /// <param name="model">The request model</param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        public async Task<IActionResult> UpdateProducts(SketchupFilterProductsDto model)
        {
            LogHelper.LogInfo(ConfigurationHelper.GetValue("SketchupAPI:LogFilePath"), $"Start updating products for manufacturer {model.ManufacturerId}");

            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            SketchupService _sketchupService = new SketchupService(model.ManufacturerId, _httpClientFactory);
            SketchupProductDto[] products = await _sketchupService.GetProductsWithSketchup(model, true, unitOfWork);
            await _sketchupService.UpdateProductsAsync(products);

            LogHelper.LogInfo(ConfigurationHelper.GetValue("SketchupAPI:LogFilePath"), $"{products.Length} have been updated for manufacturer {model.ManufacturerId}");
            return Ok();
        }
    }
}