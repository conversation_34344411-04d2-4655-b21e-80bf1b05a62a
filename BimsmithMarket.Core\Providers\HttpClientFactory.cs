﻿using BIMsmithMarket.Core.Helpers;
using Microsoft.Extensions.Http;
using Polly;
using System;
using System.Net.Http;
using System.Threading.Tasks;

namespace BIMsmithMarket.Core.Providers
{
    public static class HttpClientFactory
    {
        private static readonly Lazy<HttpClient> _factory = new Lazy<HttpClient>(CreateClient);

        public static HttpClient GetClient()
        {
            return _factory.Value;
        }

        #region private methods
        private static HttpClient CreateClient()
        {
            IAsyncPolicy<HttpResponseMessage> retryPolicy = GetRetryPolicy();
            PolicyHttpMessageHandler policyHandler = new PolicyHttpMessageHandler(retryPolicy)
            {
                InnerHandler = new SocketsHttpHandler
                {
                    PooledConnectionLifetime = TimeSpan.FromMinutes(15)
                }
            };

            return new HttpClient(policyHandler);
        }

        private static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy()
        {
            return Policy<HttpResponseMessage>
                .Handle<HttpRequestException>()
                .Or<TaskCanceledException>()
                .OrResult(NetworkHelper.IsResponseInvalid)
                .WaitAndRetryAsync(
                    retryCount: 3,
                    sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)) + TimeSpan.FromMilliseconds(new Random().Next(0, 1000)),
                    onRetry: async (outcome, timespan, retryAttempt, context) =>
                    {
                        await LogHelper.LogHttpClientRetryAsync(outcome?.Result, outcome?.Exception);
                    }
                );
        }
        #endregion
    }
}