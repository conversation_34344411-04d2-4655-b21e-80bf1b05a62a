﻿using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    public class MasterformatController : BaseApiController
    {
        private readonly IMasterformatService _masterformatService;

        public MasterformatController(
            IMasterformatService masterformatService)
        {
            _masterformatService = masterformatService;
        }

        [HttpGet]
        public async Task<IActionResult> PublicList()
        {
            return Ok(await _masterformatService.GetBackofficeMasterformatsAsync());
        }
    }
}