﻿using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;

using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Services
{
    public class StyleFileService : IStyleFileService
    {
        private readonly IHttpClientFactory _httpClientFactory;

        public StyleFileService(IHttpClientFactory httpClientFactory)
        {
            _httpClientFactory = httpClientFactory;
        }

        public async Task<List<string>> GetManufacturerStyleFilesContentAsync(int manufacturerId, StyleFileType type)
        {
            using (var unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var manufacturerExists = await unitOfWork.ManufacturerRepository.GetAll()
                                                         .AnyAsync(x => x.Id == manufacturerId);

                if (!manufacturerExists) throw new Exception("Manufacturer not found");

                var styleFileUrls = await unitOfWork.ManufacturerStyleFileRepository.GetAll()
                                                    .Where(x => x.ManufacturerId == manufacturerId
                                                             && x.Type == type)
                                                    .Select(x => x.File.Url)
                                                    .AsNoTracking()
                                                    .ToListAsync();

                if (!styleFileUrls.Any())
                    return new List<string>();

                var styleFilesContent = new List<string>();
                foreach (var url in styleFileUrls)
                {
                    var httpClient = _httpClientFactory.CreateClient();
                    {
                        var result = await httpClient.GetAsync(url);
                        if (result.IsSuccessStatusCode)
                        {
                            var stream = await result.Content.ReadAsStreamAsync();
                            var streamReader = new StreamReader(stream);
                            styleFilesContent.Add(streamReader.ReadToEnd());
                        }
                    }
                }

                return styleFilesContent;
            }
        }

        public ICollection<StyleFileTypeViewModel> GetManufacturerStyleFileTypes()
        {
            var list = new List<StyleFileTypeViewModel>();
            var enumValueArray = Enum.GetValues(typeof(StyleFileType));
            foreach (int enumValue in enumValueArray)
            {
                list.Add(new StyleFileTypeViewModel { Value = enumValue, Title = GetStyleFileTypeTitle((StyleFileType)enumValue) });
            }
            return list;
        }

        #region private methods
        private bool ValidateStyleContent(string content, List<string> detailedErrors)
        {
            var scriptTagTemplate = new Regex(@"<script([\s\S]*?)script>");
            var scriptTagMatches = scriptTagTemplate.Matches(content);
            foreach (Match match in scriptTagMatches)
            {
                detailedErrors.Add(match.Value);
            }
            var blockedExtensions = new List<string> { ".js", ".php", ".jsp", ".asp", ".aspx" };
            var urlTagTemplate = new Regex(@"url\(([\s\S]*?)\)");
            var urlTagMatches = urlTagTemplate.Matches(content);
            foreach (Match match in urlTagMatches)
            {
                var extension = GetExtension(match.Value);
                if (blockedExtensions.Contains(extension))
                {
                    detailedErrors.Add(match.Value);
                }
            }
            if (detailedErrors.Any())
            {
                return false;
            }
            return true;
        }

        private string GetExtension(string urlString)
        {
            if (urlString.ToLower().Contains("data:image/jpeg;base64"))
            {
                return string.Empty;
            }
            if (urlString.Contains("."))
            {
                urlString = urlString.Substring(urlString.LastIndexOf('.'));
            }
            if (urlString.Contains("?"))
            {
                urlString = urlString.Substring(0, urlString.LastIndexOf('?'));
            }
            urlString = urlString.Replace(@"'", string.Empty).Replace("(", string.Empty).Replace(")", string.Empty);
            return urlString;
        }

        private string GetStyleFileTypeTitle(StyleFileType type)
        {
            var result = string.Empty;
            switch (type)
            {
                case StyleFileType.Microsite: result = "Microsite"; break;
                case StyleFileType.ProductPageViaMicrosite: result = "Product Page Within Microsite"; break;
            }
            return result;
        }
        #endregion
    }
}