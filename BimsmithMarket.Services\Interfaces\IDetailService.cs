﻿using BIMsmithMarket.Domain.Dto.DetailDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IDetailService
    {
        Task<dynamic> AddAsync(AddDetailDto model, string userId);

        Task<int> AddDetailAsync(
            AddDetailDto model,
            string userId,
            IUnitOfWork unitOfWork,
            bool isRestoredFromBackup = false,
            int? detailId = null);

        Task<int> EditDetailAsync(EditDetailDto model, string userId, IUnitOfWork unitOfWork);

        Task<dynamic> EditAsync(EditDetailDto model, string userId);

        Task<bool> DeleteAsync(int id);

        Task<bool> DeleteDetailAsync(int id, IUnitOfWork unitOfWork);

        Task<dynamic> GetAsync(int id);

        Task<dynamic> ListAsync(DetailFilterDto model);

        Task<dynamic> GetFiltersAsync(DetailFilterDto model);

        Task<dynamic> PostRating(PostDetailRatingDto model, string userId);

        Task<bool> PostContentComment(DetailContentCommentDto model, string userId, string hostUrl, string emailsPath);
    }
}