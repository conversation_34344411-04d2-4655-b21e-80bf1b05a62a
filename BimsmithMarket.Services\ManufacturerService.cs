﻿using Azure.Storage.Blobs.Specialized;
using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Core.Extentions;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.Emails;
using BIMsmithMarket.Domain.Dto.Manufacturer;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Helpers;
using BIMsmithMarket.Services.Interfaces;
using BIMsmithMarket.Services.Search.FullTextSearch;
using Flurl;
using Mapster;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    /// <summary>
    /// Manufacturer service
    /// </summary>
    public class ManufacturerService : IManufacturerService
    {
        private readonly IFileService _fileService;
        private readonly IUploadFileService _uploadFileService;

        public ManufacturerService(IFileService fileService,
            IUploadFileService uploadFileService)
        {
            _fileService = fileService;
            _uploadFileService = uploadFileService;
        }

        public ManufacturerService()
        {
            _fileService = new FileService();
        }

        /// <summary>
        /// Update documents previews and images
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier</param>
        /// <returns></returns>
        public async Task<bool> UpdateThumbnailAsync(int manufacturerId)
        {
            var imageSuccesfullyStatusCode = new List<int> { 200, 202, 301, 307, 308 };

            try
            {
                AzureStorageService azureBlobProvider = new AzureStorageService(
                    ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                    ConfigurationHelper.GetValue("Environment"),
                    bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                    false);
                var filesContainer = azureBlobProvider.GetContainerByName(AzureStorageConstants.FilesContainer);
                string folderFiles = Path.Combine(Path.GetTempPath(), ConfigurationHelper.GetValue("Environment"), "Files");
                if (!Directory.Exists(folderFiles))
                {
                    Directory.CreateDirectory(folderFiles);
                }
                using (IUnitOfWork unitOfWork = UnitOfWork.Create())
                {
                    Domain.DBModels.Manufacturer manufacturer = await unitOfWork.ManufacturerRepository.GetByIdAsync(manufacturerId);

                    List<Domain.DBModels.File> files = unitOfWork.ProductFileRepository.GetAll().Where(a => a.Product.ManufacturerId == manufacturerId).Select(a => a.File).ToList();
                    files.AddRange(unitOfWork.ProductLineFileRepository.GetAll().Where(a => a.ProductLine.ManufacturerId == manufacturerId).Select(a => a.File).ToList());
                    files.AddRange(unitOfWork.ManufacturerFileRepository.GetAll().Where(a => a.ManufacturerId == manufacturerId).Select(a => a.File).ToList());
                    foreach (Domain.DBModels.File file in files.Distinct().ToList())
                    {
                        string fileName = file.FileName != null && !string.IsNullOrWhiteSpace(file.FileName) ? file.FileName : file.Id.ToString();
                        string filePath = Path.Combine(folderFiles, fileName);
                        file.FileName = fileName;
                        try
                        {
                            var updateUrl = file.SyncUrl ?? file.Url;
                            //check if pdf is inside of iframe and if it is parse pdf document link
                            var isUrlWithPdfFrames = _fileService.IsUrlWithPdfIframe(updateUrl);
                            if (isUrlWithPdfFrames)
                                updateUrl = _fileService.FindFileUrlForPdfFrame(updateUrl);

                            if (!updateUrl.StartsWith("http"))
                            {
                                updateUrl = "http://" + updateUrl;
                            }

                            int redirectsCount = 0;
                        StartRedirect:

                            if (!updateUrl.StartsWith("http"))
                            {
                                var baseUrl = file.SyncUrl ?? file.Url;
                                updateUrl = new Uri(baseUrl).GetLeftPart(UriPartial.Authority) + updateUrl;
                            }

                            if (updateUrl.StartsWith("https"))
                            {
                                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;
                                ServicePointManager.ServerCertificateValidationCallback += (sender, certificate, chain, sslPolicyErrors) => true;
                            }

                            using (HttpClient httpClient = new())
                            {
                                httpClient.Timeout = TimeSpan.FromMinutes(5);

                                if (updateUrl[updateUrl.Length - 1] == '/')
                                {
                                    updateUrl = updateUrl.Remove(updateUrl.Length - 1);
                                }

                                using (var fileResponse = await httpClient.GetAsync(updateUrl))
                                {
                                    file.SyncStatusCode = (int)fileResponse.StatusCode;

                                    if (fileResponse.StatusCode == HttpStatusCode.OK || fileResponse.StatusCode == HttpStatusCode.Accepted)
                                    {
                                        if (!string.IsNullOrWhiteSpace(file.SyncUrl) && file.SyncUrl != updateUrl && !isUrlWithPdfFrames)
                                        {
                                            file.SyncUrl = updateUrl;
                                            unitOfWork.FileRepository.Edit(file);
                                        }

                                        using (Stream fileStream = await fileResponse.Content.ReadAsStreamAsync())
                                        {
                                            using (var localFile = System.IO.File.Create(filePath))
                                            {
                                                await fileStream.CopyToAsync(localFile);
                                            }
                                        }

                                        var blobName = Path.GetFileName(file.Url);
                                        BlockBlobClient blobFile = filesContainer.GetBlockBlobClient(blobName);

                                        FileInfo fi = new FileInfo(filePath);
                                        using (StreamReader streamReader = new StreamReader(filePath))
                                        {
                                            file.CheckSum = HashProvider.CalculateMD5Hash(streamReader.BaseStream);
                                            file.FileSize = fi.Length;
                                        }
                                        using (FileStream fs = System.IO.File.OpenRead(filePath))
                                            await blobFile.UploadAsync(fs);
                                    }
                                    else if ((fileResponse.StatusCode == HttpStatusCode.Moved || fileResponse.StatusCode == HttpStatusCode.MovedPermanently || fileResponse.StatusCode == HttpStatusCode.Found) &&
                                        !string.IsNullOrWhiteSpace(fileResponse.Headers.Location?.ToString()) &&
                                        redirectsCount < 3)
                                    {
                                        redirectsCount++;
                                        updateUrl = fileResponse.Headers.Location.ToString();
                                        fileResponse.Dispose();
                                        goto StartRedirect;
                                    }
                                }
                            }
                        }
                        catch (Exception e)
                        {
                            Log.Error("Thumbnails" + e.Message, e);
                            continue;
                        }

                        if (file.MediaType.StartsWith("image") || Constants.ThumbnailMediaTypes.Contains(file.MediaType))
                        {
                            var thumbnail = ThumbnailProvider.GetAndSaveThumbnail(filePath, file.MediaType, file.Id);
                            if (!thumbnail.HasError) //Upalod preview on file server
                            {
                                var previewBlobName = Path.GetFileName(thumbnail.FilePath);
                                BlockBlobClient previewBlobUpload = filesContainer.GetBlockBlobClient(previewBlobName);
                                using (FileStream fs = System.IO.File.OpenRead(thumbnail.FilePath))
                                    await previewBlobUpload.UploadAsync(fs);
                                file.PreviewUrl = previewBlobUpload.Uri.ToString();
                                System.IO.File.Delete(thumbnail.FilePath);//delete local file
                                unitOfWork.FileRepository.Edit(file);
                            }
                            else
                            {
                                Log.Error("Thumbnails" + thumbnail.Error);
                            }
                        }

                        manufacturer.HealthCheckStatus = files.Any(x => !imageSuccesfullyStatusCode.Contains(x.SyncStatusCode))
                            ? HealthCheckStatus.Error
                            : HealthCheckStatus.Ok;

                        unitOfWork.ManufacturerRepository.Edit(manufacturer);

                        System.IO.File.Delete(filePath);//delete local file
                        unitOfWork.Save();
                    }

                    return true;
                }
            }
            catch (Exception ex)
            {
                Log.Error("Thumbnails" + ex.Message);
                throw;
            }
        }

        public ManufacturerInfoModel GetBySwatchboxId(MarketManufacturerInfoRequestModel model, string swatchboxMarketAccessKey)
        {
            if (model.AccessKey.ToUpper() != swatchboxMarketAccessKey.ToUpper())
                throw new InvalidInputException("Invalid access key");

            using (var unitOfWork = UnitOfWork.Create())
            {
                var manufacturer = unitOfWork.ManufacturerRepository.GetAll().FirstOrDefault(x => x.SwatchboxManufacturerId == model.SwatchboxId);
                if (manufacturer == null)
                    throw new DbItemNotFoundException($"Manufacturer not found {model.SwatchboxId}");

                return manufacturer.Adapt<ManufacturerInfoModel>();
            }
        }

        public async Task<bool> UpdateRegionIdsAsync(int manufacturerId, string regionIds, string userId, IUnitOfWork unitOfWork)
        {
            bool manufacturerExists = await unitOfWork.ManufacturerRepository.GetAll()
                    .AnyAsync(x => x.Id == manufacturerId);

            if (!manufacturerExists)
                throw new DbItemNotFoundException($"Manufacturer not found {manufacturerId}");

            await unitOfWork.ManufacturerRepository.GetAll()
                .Where(x => x.Id == manufacturerId)
                .ExecuteUpdateAsync(x => x.SetProperty(p => p.RegionIds, regionIds)
                .SetProperty(p => p.ModifiedById, userId)
                .SetProperty(p => p.ModifiedDate, DateTime.UtcNow));

            return true;
        }

        public async Task<bool> UpdateStateIdsAsync(int manufacturerId, string stateIds, string userId, IUnitOfWork unitOfWork)
        {
            bool manufacturerExists = await unitOfWork.ManufacturerRepository.GetAll()
                    .AnyAsync(x => x.Id == manufacturerId);

            if (!manufacturerExists)
                throw new DbItemNotFoundException($"Manufacturer not found {manufacturerId}");

            await unitOfWork.ManufacturerRepository.GetAll()
                .Where(x => x.Id == manufacturerId)
                .ExecuteUpdateAsync(x => x.SetProperty(p => p.StateIds, stateIds)
                .SetProperty(p => p.ModifiedById, userId)
                .SetProperty(p => p.ModifiedDate, DateTime.UtcNow));

            return true;
        }

        public async Task<string[]> VanityUrlListAsync(IUnitOfWork unitOfWork)
        {
            return await unitOfWork.ManufacturerRepository.GetAll()
                .AsNoTracking()
                .Select(x => x.HubVanityURL)
                .ToArrayAsync();
        }

        public async Task<FollowManufacturerDto[]> FollowListAsync(string userId, IUnitOfWork unitOfWork)
        {
            return await unitOfWork.UserBIMsmithManufacturerRepository.GetAll()
                .Where(x => x.AddedById == userId)
                .Select(x => new FollowManufacturerDto
                {
                    Id = x.ManufacturerId
                })
                .ToArrayAsync();
        }

        public async Task<OperationResultDto> UpdateWeightAsync(ManufacturerWeightDto model, string userId, IUnitOfWork unitOfWork)
        {
            Manufacturer manufacturer = await unitOfWork.ManufacturerRepository.GetByIdAsync(model.ManufacturerId);

            if (manufacturer == null)
                throw new InvalidInputException("Manufacturer not found");

            manufacturer.Weight = model.Weight;
            manufacturer.ModifiedById = userId;
            manufacturer.ModifiedDate = DateTime.UtcNow;
            unitOfWork.ManufacturerRepository.Edit(manufacturer);
            await unitOfWork.SaveAsync();

            return new OperationResultDto
            {
                Status = OperationResultStatus.Succeed
            };
        }

        public async Task<PaginationListDto<ManufacturerPopularListDto>> PopularListAsync(
            IUnitOfWork unitOfWork,
            string regionId = null,
            string stateId = null,
            int? categoryId = null,
            string categoryVanityUrl = null,
            int? externalMasterformatId = null,
            string masterformatCode = null,
            string masterformatName = null,
            int offset = 0,
            int count = 10)
        {
            IQueryable<Manufacturer> dbQuery = unitOfWork.ManufacturerRepository.GetAll()
                .Where(x => x.Published)
                .OrderByDescending(x => x.Weight);

            Dictionary<int, int> manufacturerProductsCount = [];

            if (!string.IsNullOrWhiteSpace(regionId))
            {
                dbQuery = dbQuery.Where(x => x.RegionIds == regionId
                        || x.RegionIds.StartsWith(regionId + "_")
                        || x.RegionIds.EndsWith("_" + regionId)
                        || x.RegionIds.Contains("_" + regionId + "_")
                        || x.RegionIds == null
                        || x.RegionIds == string.Empty);
            }

            if (!string.IsNullOrWhiteSpace(stateId))
            {
                dbQuery = dbQuery.Where(x => x.StateIds == stateId
                        || x.StateIds.StartsWith(stateId + "_")
                        || x.StateIds.EndsWith("_" + stateId)
                        || x.StateIds.Contains("_" + stateId + "_")
                        || x.StateIds == null
                || x.StateIds == string.Empty);
            }

            if (categoryId.HasValue || !string.IsNullOrWhiteSpace(categoryVanityUrl))
            {
                SearchCache searchCache = SearchCache.Get(unitOfWork);

                if (!categoryId.HasValue
                    && !string.IsNullOrWhiteSpace(categoryVanityUrl)
                    && searchCache.CategoriesByVanityUrl.ContainsKey(categoryVanityUrl.Trim().ToLower()))
                {
                    categoryId = searchCache.CategoriesByVanityUrl[categoryVanityUrl.Trim().ToLower()];
                }

                if (categoryId.HasValue)
                {
                    List<int> childCategories = searchCache.ChildCategories[categoryId.Value];
                    dbQuery = dbQuery.OrderByDescending(x => x.Products.Count(p => p.Published && (p.CategoryId == categoryId || childCategories.Contains(p.CategoryId) || p.ProductCategories.Any(pc => childCategories.Contains(pc.CategoryId) || pc.CategoryId == categoryId))));
                    manufacturerProductsCount = await dbQuery.Select(x => new ManufacturerPopularInternalListDto
                    {
                        Id = x.Id,
                        ProductsCount = x.Products.Count(p => p.Published && (p.CategoryId == categoryId || childCategories.Contains(p.CategoryId) || p.ProductCategories.Any(pc => childCategories.Contains(pc.CategoryId) || pc.CategoryId == categoryId)))
                    }).ToDictionaryAsync(x => x.Id, x => x.ProductsCount);
                }
            }
            else if (externalMasterformatId.HasValue || !string.IsNullOrWhiteSpace(masterformatCode) || !string.IsNullOrWhiteSpace(masterformatName))
            {
                SearchCache searchCache = SearchCache.Get(unitOfWork);

                if (!externalMasterformatId.HasValue && !string.IsNullOrWhiteSpace(masterformatCode))
                {
                    externalMasterformatId = searchCache.ExternalMasterformats.FirstOrDefault(m => m.Code.ToLower() == masterformatCode.Trim().ToLower())?.Id;
                }
                if (!externalMasterformatId.HasValue && !string.IsNullOrWhiteSpace(masterformatName))
                {
                    externalMasterformatId = searchCache.ExternalMasterformats.FirstOrDefault(m => m.Title.ToLower() == masterformatName.Trim().ToLower())?.Id;
                }

                if (externalMasterformatId.HasValue)
                {
                    IEnumerable<int> masterformatIds = searchCache.GetChildrenMasterformatIds(externalMasterformatId.Value);
                    dbQuery = dbQuery.OrderByDescending(x => x.Products.Count(p => p.Published && p.ProductMasterformats.Any(c => masterformatIds.Contains(c.ExternalMasterformatId))));
                    manufacturerProductsCount = await dbQuery.Select(x => new ManufacturerPopularInternalListDto
                    {
                        Id = x.Id,
                        ProductsCount = x.Products.Count(p => p.Published && p.ProductMasterformats.Any(c => masterformatIds.Contains(c.ExternalMasterformatId)))
                    }).ToDictionaryAsync(x => x.Id, x => x.ProductsCount);
                }
            }

            int totalCount = await dbQuery.CountAsync();
            ManufacturerPopularListDto[] items = await dbQuery
                .ProjectToType<ManufacturerPopularListDto>()
                .Skip(offset)
                .Take(count)
                .ToArrayAsync();

            if (!manufacturerProductsCount.Any())
            {
                manufacturerProductsCount = await dbQuery.Select(x => new ManufacturerPopularInternalListDto
                {
                    Id = x.Id,
                    ProductsCount = x.Products.Where(p => p.Published).Count()
                }).ToDictionaryAsync(x => x.Id, x => x.ProductsCount);
            }

            foreach (ManufacturerPopularListDto item in items)
            {
                item.ProductsCount = manufacturerProductsCount[item.Id];
            }

            return new PaginationListDto<ManufacturerPopularListDto>
            {
                Count = totalCount,
                Data = items
            };
        }

        public async Task<OperationResultDto> RequestPricingAsync(
            RequestPricingDto model,
            string templatePath,
            string userId,
            string userEmail,
            string ip,
            string referer,
            IUserService userService,
            IAnalyticsService analyticsService,
            SlackWebHook slackWebHook,
            IUnitOfWork unitOfWork)
        {
            var manufacturer = await unitOfWork.ManufacturerRepository.GetAll()
                .Where(x => x.Id == model.ManufacturerId)
                .Select(x => new
                {
                    x.Id,
                    x.Name,
                    x.RequestPricingEnabled,
                    x.RequestPricingEmail,
                    x.RequestPricingEmailCC
                })
                .FirstOrDefaultAsync();

            if (manufacturer == null)
                throw new InvalidInputException("Manufacturer not found");

            if (!manufacturer.RequestPricingEnabled)
                throw new InvalidInputException("Request Pricing is not enabled for this manufacturer");

            if (string.IsNullOrWhiteSpace(manufacturer.RequestPricingEmail))
                throw new InvalidInputException("Request Pricing email is not enabled for this manufacturer");

            var product = await unitOfWork.ProductRepository.GetAll()
                .Where(x => x.Id == model.ProductId)
                .Select(x => new
                {
                    x.Id,
                    x.Name
                })
                .FirstOrDefaultAsync();

            if (product == null)
                throw new InvalidInputException("Product not found");

            RequestPricingUser requestPricingUser = new RequestPricingUser
            {
                ManufacturerId = manufacturer.Id,
                ProductId = product.Id,
                CreatedById = userId
            };

            unitOfWork.RequestPricingUserRepository.Insert(requestPricingUser);
            await unitOfWork.SaveAsync();

            string productLink = new Url(ConfigurationHelper.GetValue("MarketFrontBaseUrl"))
                .AppendPathSegment("product")
                .AppendPathSegment(product.Id.ToString());

            BIMsmithUserInfoDto userInfo = await userService.GetBIMsmithUserInfoByEmailAsync(userEmail);
            RequestPricingEmailDto emailModel = new RequestPricingEmailDto
            {
                City = userInfo.City,
                Country = userInfo.Country,
                FirstName = userInfo.FirstName,
                LastName = userInfo.LastName,
                ManufacturerEmail = manufacturer.RequestPricingEmail,
                ManufacturerEmailCC = manufacturer.RequestPricingEmailCC,
                Message = model.Message,
                ProductLink = productLink,
                ProductName = product.Name,
                RequestURL = model.RequestUrl,
                State = userInfo.State,
                UserEmail = userInfo.Email,
                UserType = userInfo.UserType
            };

            await EmailNotificationHelper.Create(templatePath).SendRequestPricingEmailAsync(emailModel);

            await slackWebHook.SendRequestPricingMessageAsync(userInfo.Email, manufacturer.Name, product.Name);

            AnalyticsLocationModel location = await analyticsService.GetLocationByIpAsync(ip);
            SalesForceUserEventViewModel salesForceUserEventViewModel = new SalesForceUserEventViewModel
            {
                ActionType = UserEventType.RequestPricing,
                Country = userInfo.Country,
                State = userInfo.State ?? location?.Region,
                City = userInfo.City ?? location?.City,
                Province = string.Empty,
                Zip = string.Empty,
                CompanyName = userInfo.Company,
                Email = userInfo.Email,
                EventDate = DateTime.UtcNow,
                FirstName = userInfo.FirstName,
                LastName = userInfo.LastName,
                ManufacturerId = manufacturer.Id,
                Notes = model.Message,
                ManufacturerName = manufacturer.Name,
                ContactId = userId,
                PageOrigination = referer,
                ProductName = product.Name
            };
            await analyticsService.SaveSalesForceEventToAnalyticsAsync(salesForceUserEventViewModel);

            return new OperationResultDto
            {
                Status = OperationResultStatus.Succeed
            };
        }

        public async Task<PaginationListDto<RequestPricingUserListDto>> RequestPricingUserListAsync(
            int manufacturerId,
            IUnitOfWork unitOfWork,
            int offset = 0,
            int count = 10)
        {
            IQueryable<RequestPricingUser> dbQuery = unitOfWork.RequestPricingUserRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId);

            int totalCount = await dbQuery.CountAsync();
            RequestPricingUserListDto[] items = await dbQuery
                .OrderBy(x => x.CreatedDate)
                .Skip(offset)
                .Take(count)
                .ProjectToType<RequestPricingUserListDto>()
                .ToArrayAsync();

            return new PaginationListDto<RequestPricingUserListDto>
            {
                Count = totalCount,
                Data = items
            };
        }

        public async Task<OperationResultDto> DeleteRequestPricingUserAsync(
            int id,
            IUnitOfWork unitOfWork)
        {
            RequestPricingUser requestPricingUser = await unitOfWork.RequestPricingUserRepository.GetByIdAsync(id);

            if (requestPricingUser == null)
                throw new InvalidInputException("Request Pricing User not found");

            unitOfWork.RequestPricingUserRepository.Delete(requestPricingUser);
            await unitOfWork.SaveAsync();

            return new OperationResultDto
            {
                Status = OperationResultStatus.Succeed
            };
        }

        public async Task<PaginationListDto<ManufacturerRevitMicrositesPublicListDto>> RevitMicrositesPublicListAsync(
            IUnitOfWork unitOfWork,
            string query = null,
            string regionId = null,
            string stateId = null)
        {
            IQueryable<Manufacturer> dbQuery = unitOfWork.ManufacturerRepository.GetAll()
                .Where(x => x.Published);

            if (!string.IsNullOrWhiteSpace(query))
            {
                dbQuery.Where(x => x.Name.ToUpper().Contains(query.Trim().ToUpper()));
            }

            if (!string.IsNullOrWhiteSpace(regionId))
            {
                dbQuery = dbQuery.Where(x => x.RegionIds == regionId
                        || x.RegionIds.StartsWith(regionId + "_")
                        || x.RegionIds.EndsWith("_" + regionId)
                        || x.RegionIds.Contains("_" + regionId + "_")
                        || x.RegionIds == null
                        || x.RegionIds == string.Empty);
            }

            if (!string.IsNullOrWhiteSpace(stateId))
            {
                dbQuery = dbQuery.Where(x => x.StateIds == stateId
                        || x.StateIds.StartsWith(stateId + "_")
                        || x.StateIds.EndsWith("_" + stateId)
                        || x.StateIds.Contains("_" + stateId + "_")
                        || x.StateIds == null
                || x.StateIds == string.Empty);
            }

            int totalCount = await dbQuery.CountAsync();
            ManufacturerRevitMicrositesPublicListDto[] data = await dbQuery.ProjectToType<ManufacturerRevitMicrositesPublicListDto>().ToArrayAsync();

            return new PaginationListDto<ManufacturerRevitMicrositesPublicListDto>
            {
                Count = totalCount,
                Data = data
            };
        }



        public async Task<AddManufacturerResult> AddAsync(AddManufacturerDto model, string userId, IUnitOfWork unitOfWork)
        {
            if (!string.IsNullOrEmpty(model.OwnerId))
            {
                var owner = await unitOfWork.UserRepository.GetAll().Where(a => a.Id == model.OwnerId).FirstOrDefaultAsync();
                if (owner == null)
                {
                    throw new InvalidInputException($"Parameter OwnerId {model.OwnerId} is invalid");
                }
            }
            else
            {
                model.OwnerId = null;
            }

            //validate VanityUrl 
            if (!string.IsNullOrEmpty(model.HubVanityURL))
            {
                if (await unitOfWork.ManufacturerRepository.GetAll().AnyAsync(a => a.HubVanityURL.ToLower() == model.HubVanityURL.ToLower()) ||
                    await unitOfWork.VanityHistoryRepository.GetAll().AnyAsync(a => a.EntityType == "manufacturer" && a.VanityUrl.ToLower() == model.HubVanityURL.ToLower()))
                {
                    throw new InvalidInputException($"This Vanity Url {model.HubVanityURL} occupied by another manufacturer, please enter another url");
                }

                if (!Regex.IsMatch(model.HubVanityURL, Domain.Constants.Constants.UrlVanityRegax))
                {
                    throw new InvalidInputException($"This Vanity Url {model.HubVanityURL} contains not accessible characters. Available next characters '[A-Za-z]|[0-9]|_|-|()'");
                }
            }

            unitOfWork.BeginTransaction();

            Address manufacturerAddress = null;

            if (model.Address != null)
            {
                manufacturerAddress = new Address();
                manufacturerAddress.Address1 = model.Address.Address1;
                manufacturerAddress.Address2 = model.Address.Address2;
                manufacturerAddress.Country = model.Address.Country;
                manufacturerAddress.State = model.Address.State;
                manufacturerAddress.City = model.Address.City;
                manufacturerAddress.Province = model.Address.Province;
                manufacturerAddress.Zip = model.Address.Zip;
                manufacturerAddress.CreatedById = userId;
                manufacturerAddress.CreatedDate = DateTime.UtcNow;

                unitOfWork.AddressRepository.Insert(manufacturerAddress);

                await unitOfWork.SaveAsync();
            }

            string keywords = null;
            if (!string.IsNullOrWhiteSpace(model.Keywords))
            {
                keywords = " " + model.Keywords.Trim(' ', ',').Replace(",", ", ").Replace("  ", " ") + ", ";
            }

            Manufacturer manufacturer = new Manufacturer();
            manufacturer.Name = model.Name.Trim();
            manufacturer.ManufacturerHubTitle = !string.IsNullOrEmpty(model.ManufacturerHubTitle) ? model.ManufacturerHubTitle : model.Name.Trim();
            manufacturer.ManufacturerHubSubtitle = !string.IsNullOrEmpty(model.ManufacturerHubSubtitle) ? model.ManufacturerHubSubtitle : $"{model.Name.Trim()} Revit Families and BIM Content";
            manufacturer.ForgeManufacturerId = model.ForgeManufacturerId?.Trim();
            manufacturer.AddressId = manufacturerAddress == null ? default(int?) : manufacturerAddress.Id;
            manufacturer.Description = model.Description ?? "";
            manufacturer.Site = model.Site ?? "";
            manufacturer.ManualUrl = model.ManualUrl;
            manufacturer.Type = model.Type;
            manufacturer.Note = model.Note;
            manufacturer.SwatchboxManufacturerId = model.SwatchboxManufacturerId;
            manufacturer.PhotoId = model.PhotoId;
            manufacturer.ImageId = model.ImageId;
            manufacturer.OriginalImageId = model.OriginalImageId;
            manufacturer.IncludeRevitPlugin = model.IncludeRevitPlugin;
            manufacturer.RevitPluginPhotoId = model.RevitPluginPhotoId;
            manufacturer.Staging = model.Staging;
            manufacturer.DetailsMicrositeSettings = model.DetailsMicrositeSettings;
            var uploadResponseModel = new { id = 0, small = "", big = "" };
            if (model.IncludeRevitPlugin && model.RevitPluginPhotoId == null && model.PhotoId != null)
            {
                try
                {
                    var photo = await unitOfWork.PhotoRepository.GetByIdAsync(model.PhotoId.Value);
                    var uploadResponseResult = await _uploadFileService.PhotoAsync(null, userId, true, photo.OriginalImgUrl, true);
                    manufacturer.RevitPluginPhotoId = uploadResponseResult.Id;
                }
                catch (Exception ex)
                {
                    Log.Error(ex.GetAllMessages(), ex);
                }
            }
            manufacturer.RegionIds = model.RegionIds;
            manufacturer.StateIds = model.StateIds;
            manufacturer.PhoneMask = model.PhoneMask;
            manufacturer.PhoneNumber = model.PhoneNumber;
            manufacturer.PageSetting = model.PageSetting;
            manufacturer.Synonyms = model.Synonyms;
            manufacturer.AnalyticsSetting = model.AnalyticsSetting;
            manufacturer.NodeSetting = model.NodeSetting;
            manufacturer.IsOnForge = model.IsOnForge;
            manufacturer.IsOnMarket = model.IsOnMarket;
            manufacturer.LAndL = model.LAndL;
            manufacturer.SendULInfo = model.SendULInfo;
            manufacturer.LetsTalk = model.LetsTalk;
            manufacturer.LetsTalkSettings = model.LetsTalkSettings;
            manufacturer.EmailLetsTalkCC = model.EmailLetsTalkCC;
            manufacturer.EmailLandLCC = model.EmailLandLCC;
            manufacturer.EmailLandL = model.EmailLandL;
            manufacturer.EmailLetsTalk = model.EmailLetsTalk;
            manufacturer.SubscribeButton = model.SubscribeButton;
            manufacturer.MetaDescription = model.MetaDescription;
            manufacturer.MetaKeywords = model.MetaKeywords;
            manufacturer.MetaTitle = model.MetaTitle;
            manufacturer.Keywords = keywords;
            manufacturer.VideoUrl = model.VideoUrl;
            manufacturer.ForgeUrl = model.ForgeUrl;
            manufacturer.MarketUrl = model.MarketUrl;
            manufacturer.Published = model.Published;
            manufacturer.PublishToPartner = model.PublishToPartner;
            manufacturer.CreatedById = userId;
            manufacturer.CreatedDate = DateTime.UtcNow;
            manufacturer.OwnerId = model.OwnerId;
            manufacturer.HubVanityURL = model.HubVanityURL.AsVanityUrl();
            manufacturer.FooterAdImageId = model.FooterAdImageId;
            manufacturer.FooterAdUrl = model.FooterAdUrl;
            manufacturer.ShowFooterAd = model.ShowFooterAd;
            manufacturer.UseCustomLoginRegisterScreen = model.UseCustomLoginRegisterScreen;
            manufacturer.CustomSignInBackgroundColor = model.CustomSignInBackgroundColor;
            manufacturer.CustomSignInTextColor = model.CustomSignInTextColor;
            manufacturer.CustomSignInDescription = model.CustomSignInDescription;
            manufacturer.CustomSignInLogoImageId = model.CustomSignInLogoImageId;
            manufacturer.IsParentManufacturer = model.IsParentManufacturer;
            manufacturer.CustomMicrositeName = model.CustomMicrositeName;
            manufacturer.RequestPricingEnabled = model.RequestPricingEnabled;
            manufacturer.RequestPricingEmail = model.RequestPricingEmail;
            manufacturer.RequestPricingEmailCC = model.RequestPricingEmailCC;

            var metaData = MetaDataHelper.GetManufacturerMetaData(manufacturer.Name);
            if (string.IsNullOrWhiteSpace(manufacturer.MetaTitle))
            {
                manufacturer.MetaTitle = metaData.Title;
            }
            if (string.IsNullOrWhiteSpace(manufacturer.MetaDescription))
            {
                manufacturer.MetaDescription = metaData.Description;
            }
            if (string.IsNullOrWhiteSpace(manufacturer.MetaKeywords))
            {
                manufacturer.MetaKeywords = metaData.Keywords;
            }

            unitOfWork.ManufacturerRepository.Insert(manufacturer);

            await unitOfWork.SaveAsync();

            if (model.ChildIds.Any())
            {
                foreach (var childId in model.ChildIds)
                {
                    var child = await unitOfWork.ManufacturerRepository.GetByIdAsync(childId);
                    child.Parent = manufacturer;
                    child.ParentId = manufacturer.Id;
                    unitOfWork.ManufacturerRepository.Edit(child);
                }
                await unitOfWork.SaveAsync();
            }

            if (model.Files != null && model.Files.Any())
            {
                foreach (var fileModel in model.Files)
                {
                    var manufacturerFile = new ManufacturerFile();
                    manufacturerFile.ManufacturerId = manufacturer.Id;
                    manufacturerFile.CustomFileId = fileModel.CustomFileId;
                    manufacturerFile.FileId = fileModel.FileId;
                    manufacturerFile.IsAttachment = true;
                    manufacturerFile.CreatedById = userId;
                    manufacturerFile.CreatedDate = DateTime.UtcNow;
                    manufacturerFile.WasChanged = true;
                    unitOfWork.ManufacturerFileRepository.Insert(manufacturerFile);

                    var file = await unitOfWork.FileRepository.GetByIdAsync(fileModel.FileId);
                    if (file.Title != fileModel.Title)
                    {
                        file.Title = fileModel.Title;
                        unitOfWork.FileRepository.Edit(file);
                    }
                }

                await unitOfWork.SaveAsync();
            }

            if (model.StyleFiles != null && model.StyleFiles.Any())
            {
                foreach (var fileModel in model.StyleFiles)
                {
                    var manufacturerStyleFile = new ManufacturerStyleFile();
                    manufacturerStyleFile.Title = fileModel.Title;
                    manufacturerStyleFile.ManufacturerId = manufacturer.Id;
                    manufacturerStyleFile.FileId = fileModel.FileId;
                    manufacturerStyleFile.FileVersion = fileModel.FileVersion;
                    manufacturerStyleFile.Type = fileModel.Type;
                    manufacturerStyleFile.CreatedById = userId;
                    manufacturerStyleFile.CreatedDate = DateTime.UtcNow;
                    unitOfWork.ManufacturerStyleFileRepository.Insert(manufacturerStyleFile);

                    var file = await unitOfWork.FileRepository.GetByIdAsync(fileModel.FileId);
                    if (file.Title != fileModel.Title)
                    {
                        file.Title = fileModel.Title;
                        unitOfWork.FileRepository.Edit(file);
                    }
                }

                await unitOfWork.SaveAsync();
            }

            if (model.AdditionalFiles != null && model.AdditionalFiles.Any())
            {
                foreach (FileDto fileModel in model.AdditionalFiles)
                {
                    ManufacturerAdditionalFile manufacturerAdditionalFile = new ManufacturerAdditionalFile();
                    manufacturerAdditionalFile.ManufacturerId = manufacturer.Id;
                    manufacturerAdditionalFile.CustomFileId = fileModel.CustomFileId;
                    manufacturerAdditionalFile.FileId = fileModel.FileId;
                    manufacturerAdditionalFile.IsAttachment = true;
                    manufacturerAdditionalFile.CreatedById = userId;
                    manufacturerAdditionalFile.CreatedDate = DateTime.UtcNow;
                    unitOfWork.ManufacturerAdditionalFileRepository.Insert(manufacturerAdditionalFile);

                    var file = await unitOfWork.FileRepository.GetByIdAsync(fileModel.FileId);
                    if (file.Title != fileModel.Title)
                    {
                        file.Title = fileModel.Title;
                        unitOfWork.FileRepository.Edit(file);
                    }
                }

                await unitOfWork.SaveAsync();
            }

            unitOfWork.CommitTransaction();

            return await unitOfWork.ManufacturerRepository.GetAll()
                .Where(x => x.Id == manufacturer.Id)
                .ProjectToType<AddManufacturerResult>()
                .FirstAsync();
        }

        public async Task<EditManufacturerResult> EditAsync(EditManufacturerDto model, string userId, IUnitOfWork unitOfWork, bool useExternalTransaction = false)
        {
            if (!string.IsNullOrEmpty(model.OwnerId))
            {
                var owner = await unitOfWork.UserRepository.GetAll().Where(a => a.Id == model.OwnerId).FirstOrDefaultAsync();
                if (owner == null)
                {
                    throw new InvalidInputException($"Parameter OwnerId {model.OwnerId} is invalid");
                }
            }
            else
            {
                model.OwnerId = null;
            }

            //validate VanityUrl 
            if (!string.IsNullOrEmpty(model.HubVanityURL))
            {
                if (await unitOfWork.ManufacturerRepository.GetAll().AnyAsync(a => a.Id != model.Id && a.HubVanityURL.ToLower() == model.HubVanityURL.ToLower()) ||
                    await unitOfWork.VanityHistoryRepository.GetAll().AnyAsync(a => a.EntityType == "manufacturer" && a.VanityUrl.ToLower() == model.HubVanityURL.ToLower() && a.EntityId != model.Id))
                {
                    throw new InvalidInputException($"This Vanity Url {model.HubVanityURL} occupied by another manufacturer, please enter another url");
                }

                if (!Regex.IsMatch(model.HubVanityURL, Domain.Constants.Constants.UrlVanityRegax))
                {
                    throw new InvalidInputException($"This Vanity Url {model.HubVanityURL} contains not accessible characters. Available next characters '[A-Za-z]|[0-9]|_|-|()'");
                }
            }

            if (!useExternalTransaction)
            {
                unitOfWork.BeginTransaction();
            }

            Manufacturer manufacturer = await unitOfWork.ManufacturerRepository.GetByIdAsync(model.Id);

            Address manufacturerAddress = manufacturer.Address;
            if (manufacturerAddress != null)
            {
                unitOfWork.AddressRepository.Delete(manufacturerAddress);
            }

            await unitOfWork.SaveAsync();

            if (model.Address != null)
            {
                manufacturerAddress = new Address();
                manufacturerAddress.Address1 = model.Address.Address1;
                manufacturerAddress.Address2 = model.Address.Address2;
                manufacturerAddress.Country = model.Address.Country;
                manufacturerAddress.State = model.Address.State;
                manufacturerAddress.City = model.Address.City;
                manufacturerAddress.Province = model.Address.Province;
                manufacturerAddress.Zip = model.Address.Zip;
                manufacturerAddress.CreatedById = userId;
                manufacturerAddress.CreatedDate = DateTime.UtcNow;

                unitOfWork.AddressRepository.Insert(manufacturerAddress);

                await unitOfWork.SaveAsync();
            }

            string keywords = null;
            if (!string.IsNullOrWhiteSpace(model.Keywords))
            {
                keywords = " " + model.Keywords.Trim(' ', ',').Replace(",", ", ").Replace("  ", " ") + ", ";
            }

            manufacturer.Name = model.Name.Trim();
            manufacturer.ManufacturerHubTitle = !string.IsNullOrEmpty(model.ManufacturerHubTitle) ? model.ManufacturerHubTitle : model.Name.Trim();
            manufacturer.ManufacturerHubSubtitle = !string.IsNullOrEmpty(model.ManufacturerHubSubtitle) ? model.ManufacturerHubSubtitle : $"{model.Name.Trim()} Revit Families and BIM Content";
            manufacturer.ForgeManufacturerId = model.ForgeManufacturerId?.Trim();
            manufacturer.AddressId = manufacturerAddress == null ? default(int?) : manufacturerAddress.Id;
            manufacturer.Description = model.Description ?? "";
            manufacturer.Site = model.Site ?? "";
            manufacturer.ManualUrl = model.ManualUrl;
            manufacturer.Type = model.Type;
            manufacturer.PhotoId = model.PhotoId;
            manufacturer.Note = model.Note;
            manufacturer.ImageId = model.ImageId;
            manufacturer.SwatchboxManufacturerId = model.SwatchboxManufacturerId;
            manufacturer.OriginalImageId = model.OriginalImageId;
            manufacturer.IncludeRevitPlugin = model.IncludeRevitPlugin;
            manufacturer.RevitPluginPhotoId = model.RevitPluginPhotoId;
            manufacturer.Staging = model.Staging;
            manufacturer.DetailsMicrositeSettings = model.DetailsMicrositeSettings;
            manufacturer.IsParentManufacturer = model.IsParentManufacturer;
            manufacturer.CustomMicrositeName = model.CustomMicrositeName;
            manufacturer.RequestPricingEnabled = model.RequestPricingEnabled;
            manufacturer.RequestPricingEmail = model.RequestPricingEmail;
            manufacturer.RequestPricingEmailCC = model.RequestPricingEmailCC;

            var big = string.Empty;
            if (model.IncludeRevitPlugin && model.RevitPluginPhotoId == null && model.PhotoId != null)
            {
                try
                {
                    var photo = await unitOfWork.PhotoRepository.GetByIdAsync(model.PhotoId.Value);
                    var uploadResponseResult = await _uploadFileService.PhotoAsync(null, userId, false, photo.OriginalImgUrl, true);
                    manufacturer.RevitPluginPhotoId = uploadResponseResult.Id;
                    big = uploadResponseResult.OriginalImgUrl;
                }
                catch (Exception ex)
                {
                    Log.Error(ex.GetAllMessages(), ex);
                }
            }
            manufacturer.RegionIds = model.RegionIds;
            manufacturer.StateIds = model.StateIds;
            manufacturer.PhoneMask = model.PhoneMask;
            manufacturer.PhoneNumber = model.PhoneNumber;
            manufacturer.PageSetting = model.PageSetting;
            manufacturer.Synonyms = model.Synonyms;
            manufacturer.AnalyticsSetting = model.AnalyticsSetting;
            manufacturer.NodeSetting = model.NodeSetting;
            manufacturer.IsOnForge = model.IsOnForge;
            manufacturer.IsOnMarket = model.IsOnMarket;
            manufacturer.LAndL = model.LAndL;
            manufacturer.SendULInfo = model.SendULInfo;
            manufacturer.LetsTalk = model.LetsTalk;
            manufacturer.LetsTalkSettings = model.LetsTalkSettings;
            manufacturer.EmailLandL = model.EmailLandL;
            manufacturer.EmailLetsTalk = model.EmailLetsTalk;
            manufacturer.EmailLetsTalkCC = model.EmailLetsTalkCC;
            manufacturer.EmailLandLCC = model.EmailLandLCC;
            manufacturer.SubscribeButton = model.SubscribeButton;
            manufacturer.MetaDescription = model.MetaDescription;
            manufacturer.MetaKeywords = model.MetaKeywords;
            manufacturer.MetaTitle = model.MetaTitle;
            manufacturer.Keywords = keywords;
            manufacturer.VideoUrl = model.VideoUrl;
            manufacturer.ForgeUrl = model.ForgeUrl;
            manufacturer.MarketUrl = model.MarketUrl;
            manufacturer.Published = model.Published;
            manufacturer.PublishToPartner = model.PublishToPartner;
            manufacturer.ModifiedById = userId;
            manufacturer.ModifiedDate = DateTime.UtcNow;
            manufacturer.OwnerId = model.OwnerId;
            manufacturer.FooterAdImageId = model.FooterAdImageId;
            manufacturer.FooterAdUrl = model.FooterAdUrl;
            manufacturer.ShowFooterAd = model.ShowFooterAd;
            manufacturer.UseCustomLoginRegisterScreen = model.UseCustomLoginRegisterScreen;
            manufacturer.CustomSignInBackgroundColor = model.CustomSignInBackgroundColor;
            manufacturer.CustomSignInTextColor = model.CustomSignInTextColor;
            manufacturer.CustomSignInDescription = model.CustomSignInDescription;
            manufacturer.CustomSignInLogoImageId = model.CustomSignInLogoImageId;

            if (manufacturer.HubVanityURL != model.HubVanityURL.AsVanityUrl())
            {
                var oldVanityHistory = await unitOfWork.VanityHistoryRepository.GetAll().Where(a => a.VanityUrl == manufacturer.HubVanityURL).FirstOrDefaultAsync();
                if (oldVanityHistory == null)
                {
                    oldVanityHistory = new VanityHistory
                    {
                        EntityId = manufacturer.Id,
                        EntityType = "manufacturer",
                        VanityUrl = manufacturer.HubVanityURL,
                        CreatedById = userId,
                        CreatedDate = DateTime.UtcNow
                    };
                    unitOfWork.VanityHistoryRepository.Insert(oldVanityHistory);
                    await unitOfWork.SaveAsync();
                }

                var vanityHistory = new VanityHistory
                {
                    EntityId = manufacturer.Id,
                    ParentId = oldVanityHistory.Id,
                    EntityType = "manufacturer",
                    VanityUrl = model.HubVanityURL.AsVanityUrl(),
                    CreatedById = userId,
                    CreatedDate = DateTime.UtcNow
                };
                unitOfWork.VanityHistoryRepository.Insert(vanityHistory);
                await unitOfWork.SaveAsync();
            }
            manufacturer.HubVanityURL = model.HubVanityURL.AsVanityUrl();

            var metaData = MetaDataHelper.GetManufacturerMetaData(manufacturer.Name);
            if (string.IsNullOrWhiteSpace(manufacturer.MetaTitle))
            {
                manufacturer.MetaTitle = metaData.Title;
            }
            if (string.IsNullOrWhiteSpace(manufacturer.MetaDescription))
            {
                manufacturer.MetaDescription = metaData.Description;
            }
            if (string.IsNullOrWhiteSpace(manufacturer.MetaKeywords))
            {
                manufacturer.MetaKeywords = metaData.Keywords;
            }

            unitOfWork.ManufacturerRepository.Edit(manufacturer);

            await unitOfWork.SaveAsync();

            if (model.IsParentManufacturer)
            {
                var childManufacturers = await unitOfWork.ManufacturerRepository.GetAll().Where(x => x.ParentId == manufacturer.Id).Select(x => x.Id).ToListAsync();
                if (model.ChildIds.Any())
                {
                    var manufacturersToAdd = model.ChildIds.Except(childManufacturers);
                    var manufacturersToRemove = childManufacturers.Except(model.ChildIds);
                    foreach (var a in manufacturersToAdd)
                    {
                        var childManufacturer = await unitOfWork.ManufacturerRepository.GetByIdAsync(a);
                        if (childManufacturer != null)
                        {
                            childManufacturer.ParentId = manufacturer.Id;
                            childManufacturer.Parent = manufacturer;
                        }
                    }

                    foreach (var a in manufacturersToRemove)
                    {
                        var childManufacturer = await unitOfWork.ManufacturerRepository.GetByIdAsync(a);
                        if (childManufacturer != null)
                        {
                            childManufacturer.ParentId = null;
                            childManufacturer.Parent = null;
                        }
                    }

                    await unitOfWork.SaveAsync();
                }
                else if (!model.ChildIds.Any() && childManufacturers.Any())
                {
                    foreach (var a in childManufacturers)
                    {
                        var childManufacturer = await unitOfWork.ManufacturerRepository.GetByIdAsync(a);
                        if (childManufacturer != null)
                        {
                            childManufacturer.ParentId = null;
                            childManufacturer.Parent = null;
                        }
                    }
                    await unitOfWork.SaveAsync();
                }

            }

            if (model.Files != null && model.Files.Any())
            {
                var dbManufacturerFilesId = manufacturer.ManufacturerFiles.Select(x => x.FileId).ToList();
                var modelFilesId = model.Files.Select(x => x.FileId).ToList();

                var filesToUpdate = modelFilesId.Intersect(dbManufacturerFilesId).ToList();
                foreach (var fileId in filesToUpdate)
                {
                    var dbFile = await unitOfWork.ManufacturerFileRepository.GetAll().Where(x => x.FileId == fileId).FirstOrDefaultAsync();
                    var modelFile = model.Files.FirstOrDefault(x => x.FileId == fileId);
                    if (dbFile != null && modelFile != null)
                    {
                        dbFile.CustomFileId = modelFile.CustomFileId;
                        dbFile.ModifiedById = userId;
                        dbFile.ModifiedDate = DateTime.UtcNow;
                    }
                    unitOfWork.ManufacturerFileRepository.Edit(dbFile);
                }

                var filesToAdd = modelFilesId.Except(dbManufacturerFilesId).ToList();
                foreach (var fileId in filesToAdd)
                {
                    var fileModelToAdd = model.Files.FirstOrDefault(x => x.FileId == fileId);
                    if (fileModelToAdd != null)
                    {
                        var manufacturerFile = new ManufacturerFile();
                        manufacturerFile.ManufacturerId = manufacturer.Id;
                        manufacturerFile.CustomFileId = fileModelToAdd.CustomFileId;
                        manufacturerFile.FileId = fileModelToAdd.FileId;
                        manufacturerFile.IsAttachment = true;
                        manufacturerFile.CreatedById = userId;
                        manufacturerFile.CreatedDate = DateTime.UtcNow;
                        manufacturerFile.WasChanged = true;
                        unitOfWork.ManufacturerFileRepository.Insert(manufacturerFile);

                        var file = await unitOfWork.FileRepository.GetByIdAsync(fileModelToAdd.FileId);
                        if (file.Title != fileModelToAdd.Title)
                        {
                            file.Title = fileModelToAdd.Title;
                            unitOfWork.FileRepository.Edit(file);
                        }
                    }
                }

                var filesToDelete = dbManufacturerFilesId.Except(modelFilesId).ToList();
                foreach (var fileId in filesToDelete)
                {
                    var fileModelToDelete = await unitOfWork.ManufacturerFileRepository.GetAll().Where(x => x.FileId == fileId).FirstOrDefaultAsync();
                    if (fileModelToDelete != null)
                    {
                        unitOfWork.ManufacturerFileRepository.Delete(fileModelToDelete);
                    }
                }
                await unitOfWork.SaveAsync();
            }
            else
            {
                var dbManufacturerFiles = manufacturer.ManufacturerFiles.ToList();
                if (dbManufacturerFiles.Any())
                {
                    dbManufacturerFiles.ForEach(x => unitOfWork.ManufacturerFileRepository.Delete(x));
                    await unitOfWork.SaveAsync();
                }
            }

            if (model.AdditionalFiles != null && model.AdditionalFiles.Any())
            {
                var dbManufacturerAdditionalFilesId = manufacturer.ManufacturerAdditionalFiles.Select(x => x.FileId).ToList();
                var modelFilesId = model.AdditionalFiles.Select(x => x.FileId).ToList();

                var filesToUpdate = modelFilesId.Intersect(dbManufacturerAdditionalFilesId).ToList();
                foreach (var fileId in filesToUpdate)
                {
                    var dbFile = await unitOfWork.ManufacturerAdditionalFileRepository.GetAll().Where(x => x.FileId == fileId).FirstOrDefaultAsync();
                    var modelFile = model.AdditionalFiles.FirstOrDefault(x => x.FileId == fileId);
                    if (dbFile != null && modelFile != null)
                    {
                        dbFile.CustomFileId = modelFile.CustomFileId;
                        dbFile.ModifiedById = userId;
                        dbFile.ModifiedDate = DateTime.UtcNow;
                    }
                    unitOfWork.ManufacturerAdditionalFileRepository.Edit(dbFile);
                }

                var filesToAdd = modelFilesId.Except(dbManufacturerAdditionalFilesId).ToList();
                foreach (var fileId in filesToAdd)
                {
                    var fileModelToAdd = model.AdditionalFiles.FirstOrDefault(x => x.FileId == fileId);
                    if (fileModelToAdd != null)
                    {
                        var manufacturerAdditionalFile = new ManufacturerAdditionalFile
                        {
                            ManufacturerId = manufacturer.Id,
                            CustomFileId = fileModelToAdd.CustomFileId,
                            FileId = fileModelToAdd.FileId,
                            IsAttachment = true,
                            CreatedById = userId,
                            CreatedDate = DateTime.UtcNow
                        };
                        unitOfWork.ManufacturerAdditionalFileRepository.Insert(manufacturerAdditionalFile);

                        var file = await unitOfWork.FileRepository.GetByIdAsync(fileModelToAdd.FileId);
                        if (file.Title != fileModelToAdd.Title)
                        {
                            file.Title = fileModelToAdd.Title;
                            unitOfWork.FileRepository.Edit(file);
                        }
                    }
                }

                var filesToDelete = dbManufacturerAdditionalFilesId.Except(modelFilesId).ToList();
                foreach (var fileId in filesToDelete)
                {
                    var fileModelToDelete = unitOfWork.ManufacturerAdditionalFileRepository.GetAll().Where(x => x.FileId == fileId).FirstOrDefault();
                    if (fileModelToDelete != null)
                        unitOfWork.ManufacturerAdditionalFileRepository.Delete(fileModelToDelete);
                }
                await unitOfWork.SaveAsync();
            }
            else
            {
                var dbManufacturerFiles = manufacturer.ManufacturerAdditionalFiles.ToList();
                dbManufacturerFiles.ForEach(x => unitOfWork.ManufacturerAdditionalFileRepository.Delete(x));
                await unitOfWork.SaveAsync();
            }

            if (model.StyleFiles != null && model.StyleFiles.Any())
            {
                var dbManufacturerStyleFilesIds = manufacturer.ManufacturerStyleFiles.Select(x => x.FileId).ToList();
                var modelStyleFileIds = model.StyleFiles.Select(x => x.FileId).ToList();

                var filesToUpdate = modelStyleFileIds.Intersect(dbManufacturerStyleFilesIds).ToList();
                foreach (var fileId in filesToUpdate)
                {
                    var dbFile = await unitOfWork.ManufacturerStyleFileRepository.GetAll().Where(x => x.FileId == fileId).FirstOrDefaultAsync();
                    var modelFile = model.StyleFiles.FirstOrDefault(x => x.FileId == fileId);
                    if (dbFile != null && modelFile != null)
                    {
                        dbFile.Title = modelFile.Title;
                        dbFile.FileVersion = modelFile.FileVersion;
                        dbFile.Type = modelFile.Type;
                        dbFile.ModifiedById = userId;
                        dbFile.ModifiedDate = DateTime.UtcNow;
                    }
                    unitOfWork.ManufacturerStyleFileRepository.Edit(dbFile);
                }

                var filesToAdd = modelStyleFileIds.Except(dbManufacturerStyleFilesIds).ToList();
                foreach (var fileId in filesToAdd)
                {
                    var fileModelToAdd = model.StyleFiles.FirstOrDefault(x => x.FileId == fileId);
                    if (fileModelToAdd != null)
                    {
                        var manufacturerStyleFile = new ManufacturerStyleFile();
                        manufacturerStyleFile.Title = fileModelToAdd.Title;
                        manufacturerStyleFile.ManufacturerId = manufacturer.Id;
                        manufacturerStyleFile.FileId = fileModelToAdd.FileId;
                        manufacturerStyleFile.FileVersion = fileModelToAdd.FileVersion;
                        manufacturerStyleFile.CreatedById = userId;
                        manufacturerStyleFile.CreatedDate = DateTime.UtcNow;
                        manufacturerStyleFile.Type = fileModelToAdd.Type;
                        unitOfWork.ManufacturerStyleFileRepository.Insert(manufacturerStyleFile);

                        var file = await unitOfWork.FileRepository.GetByIdAsync(fileModelToAdd.FileId);
                        if (file.Title != fileModelToAdd.Title)
                        {
                            file.Title = fileModelToAdd.Title;
                            unitOfWork.FileRepository.Edit(file);
                        }
                    }
                }

                var filesToDelete = dbManufacturerStyleFilesIds.Except(modelStyleFileIds).ToList();
                foreach (var fileId in filesToDelete)
                {
                    var fileModelToDelete = await unitOfWork.ManufacturerStyleFileRepository.GetAll().FirstOrDefaultAsync(x => x.FileId == fileId);
                    if (fileModelToDelete != null)
                    {
                        unitOfWork.ManufacturerStyleFileRepository.Delete(fileModelToDelete);
                    }
                }
                await unitOfWork.SaveAsync();
            }
            else
            {
                var dbManufacturerStyleFiles = manufacturer.ManufacturerStyleFiles.ToList();
                dbManufacturerStyleFiles.ForEach(x => unitOfWork.ManufacturerStyleFileRepository.Delete(x));
                await unitOfWork.SaveAsync();
            }

            if (!useExternalTransaction)
            {
                unitOfWork.CommitTransaction();
            }

            return new EditManufacturerResult
            {
                RevitIconUrl = big
            };
        }
    }
}