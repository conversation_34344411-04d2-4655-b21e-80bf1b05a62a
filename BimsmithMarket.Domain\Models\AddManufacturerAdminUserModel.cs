﻿using BIMsmithMarket.Domain.DBModels;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Models
{
    public class AddManufacturerAdminUserModel
    {
        [Required]
        public string AdminUserEmail { get; set; }

        [Required]
        public int ManufacturerId { get; set; }

        public ManufacturerAdminRole Roles { get; set; }

        public int Status { get; set; }
    }

    public class EditManufacturerAdminUserModel
    {
        [Required]
        public int Id { get; set; }

        public ManufacturerAdminRole Roles { get; set; }

        public int Status { get; set; }
    }
}