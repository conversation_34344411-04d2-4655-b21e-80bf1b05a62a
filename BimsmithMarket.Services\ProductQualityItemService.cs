﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Domain.Models.MassTransit.Events;
using BIMsmithMarket.Services.Interfaces;
using MassTransit;
using MassTransit.Transports;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class ProductQualityItemService : IProductQualityItemService
    {
        private readonly IPublishEndpoint _publishEndpoint;

        public ProductQualityItemService(IPublishEndpoint publishEndpoint)
        {
            _publishEndpoint = publishEndpoint;
        }

        public async Task<object> ListAsync(IUnitOfWork unitOfWork)
        {
            var query = unitOfWork.QualityItemRepository.GetAll();

            return await query
                .Select(a => new
                {
                    id = a.Id,
                    name = a.Name,
                    description = a.Description,
                    iconUrl = a.IconUrl
                })
                .AsNoTracking()
                .ToListAsync();
        }

        public async Task<object> GetAsync(IUnitOfWork unitOfWork, int id)
        {
            var item = await unitOfWork.QualityItemRepository.GetAll()
                    .Where(a => a.Id == id)
                    .Select(a => new
                    {
                        id = a.Id,
                        name = a.Name,
                        description = a.Description,
                        iconUrl = a.IconUrl
                    })
                    .FirstOrDefaultAsync();

            if (item == null)
            {
                throw new DbItemNotFoundException("Not found the Certificate");
            }

            return item;
        }

        public async Task<object> AddAsync(IUnitOfWork unitOfWork, string userId, AddProductQualityModel model)
        {
            QualityItem qualityItem = new QualityItem();
            qualityItem.Name = model.Name.Trim();
            qualityItem.Description = model.Description;
            qualityItem.IconUrl = model.IconUrl;
            qualityItem.CreatedById = userId;
            qualityItem.CreatedDate = DateTime.UtcNow;

            unitOfWork.QualityItemRepository.Insert(qualityItem);

            await unitOfWork.SaveAsync();

            return new
            {
                id = qualityItem.Id,
                name = qualityItem.Name,
                description = qualityItem.Description,
                iconUrl = qualityItem.IconUrl
            };
        }

        public async Task EditAsync(IUnitOfWork unitOfWork, string userId, EditProductQualityModel model)
        {
            QualityItem qualityItem = unitOfWork.QualityItemRepository.GetById(model.Id);
            qualityItem.Name = model.Name.Trim();
            qualityItem.Description = model.Description;
            qualityItem.IconUrl = model.IconUrl;
            qualityItem.ModifiedById = userId;
            qualityItem.ModifiedDate = DateTime.UtcNow;

            unitOfWork.QualityItemRepository.Edit(qualityItem);

            await unitOfWork.SaveAsync();

            await _publishEndpoint.Publish(new QualityItemChangedEvent
            {
                QualityItemId = qualityItem.Id
            });
        }

        public async Task DeleteAsync(IUnitOfWork unitOfWork, int id)
        {
            unitOfWork.BeginTransaction();

            QualityItem qualityItem = await unitOfWork.QualityItemRepository.GetByIdAsync(id);

            var productQualityItems = await unitOfWork.ProductQualityItemRepository.GetAll().Where(a => a.QualityItemId == id).ToListAsync();
            unitOfWork.ProductQualityItemRepository.Delete(productQualityItems);

            unitOfWork.QualityItemRepository.Delete(qualityItem);

            await unitOfWork.SaveAsync();

            unitOfWork.CommitTransaction();
        }
    }
}
