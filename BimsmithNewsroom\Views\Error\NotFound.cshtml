﻿@using BIMsmithMarket.Core.Helpers
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

    <link rel="icon" type="image/x-icon" href="~/favicon.png">
    <meta name="viewport" content="width=device-width">
    <meta name="description" content="Building Product Data for Revit and beyond is at your fingertips. Revit Families, AutoCAD Details, 3 Part Specs.">
    <meta name="keywords" content="Keywords: revit families, BIM, revit content, BIM content, BIM objects, building product data">


    <meta name="google-site-verification" content="S931UZF3IQ9ehCHUtrDeFGsTowM-hC7cX-ar3sRWYVI" />

    <meta name="msvalidate.01" content="5533F7259691FCA7EF63EFB3CDED200F" />
    <title>Page Not Found</title>



    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=***********-2"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', '***********-2');
    </script>

    <script type='text/javascript' src='https://cdnjs.cloudflare.com/ajax/libs/js-cookie/2.1.4/js.cookie.min.js'></script>

    <title>@ViewBag.Title</title>
    <link rel="stylesheet" href="~/Bundles/contentcssbundle.min.css" />

</head>
<body class="post-template-default single single-post postid-451 single-format-standard">
    <div id="page" class="hfeed site details">
        <header id="masthead" role="banner">
            <nav class="navbar navbar-default navbar-fixed-top navbar-left" role="navigation">
                <!-- Brand and toggle get grouped for better mobile display -->
                <div class="container" id="navigation_menu">
                    <div class="navbar-header">
                        <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-ex1-collapse">
                            <span class="sr-only">Toggle navigation</span>
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                        </button>
                        <a class="navbar-brand" href="@ConfigurationHelper.GetValue("BIMsmithUrl")">Newsroom</a>
                    </div>
                    <div class="sign-in">
                        <div style="display: none;" id="user-name"></div>
                        <div style="display: none;" id="head-drop" class="head-drop">
                            <div class="av">
                                <img id="profile-image" alt="" src="/images/img01.jpg" height="383" width="383">
                                <div id="settings-name" class="name"></div>
                            </div>
                            <div class="links">
                                <a href="@ConfigurationHelper.GetValue("MarketUrl")/">Market</a>
                                <a href="@ConfigurationHelper.GetValue("ForgeUrl")/">forge</a>
                                <a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/NewMyBIMSmith/settings">MyBIMsmith</a>
                            </div>
                        </div>
                        <a id="logout-button" style="display: none;" class="def-btn logout-button" href="javascript:void(0)">Log out</a>
                        <a id="login-button" style="display: inline-block;" class="def-btn" href="@ConfigurationHelper.GetValue("BIMsmithUrl")/Account/Login/">Log in</a>
                        <a id="signup-button" style="display: inline-block;" class="g-btn" href="@ConfigurationHelper.GetValue("BIMsmithUrl")/Account/Register">Sign up</a>
                    </div>
                    <div class="collapse navbar-collapse navbar-ex1-collapse">
                        <ul id="menu-menu-1" class="nav navbar-nav">
                            <li id="menu-item-101" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-101">
                                <a title="NewsRoom" href="/">NewsRoom</a>
                            </li>
                            <li id="menu-item-151" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-151">
                                <a title="Contact" href="@ConfigurationHelper.GetValue("BIMsmithUrl")/Contact">Contact</a>
                            </li>
                            <li id="mob-login"><a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/Account/Login/">Sign In</a></li>
                            <li id="mob-regis"><a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/Account/Register">Sign Up</a></li>
                        </ul>
                    </div>
                </div>
            </nav>
            <div id="cc_spacer" style="height: 45px;"></div>
        </header>
        <div id="content" class="site-content">
            <div class="container">
                <div class="main">
                    <div style="height:300px; margin-top: 25%; text-align: center">
                        <h3>404 - Page Not Found</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <footer id="colophon" class="site-footer" role="contentinfo">
        <div class="row site-info">
            <div class="brands-block">
                <p>Chosen by: </p>
                <ul>
                    <li>
                        <a href="http://market.bimsmith.com/Canon">
                            <img src="images/brand-img01.png" alt="">
                        </a>
                    </li>
                    <li>
                        <a href="http://market.bimsmith.com/SherwinWilliams">
                            <img src="images/brand-img02.png" alt="">
                        </a>
                    </li>
                    <li>
                        <a href="http://market.bimsmith.com/Moen">
                            <img src="images/brand-img03.png" alt="">
                        </a>
                    </li>
                    <li>
                        <a href="http://market.bimsmith.com/Dupont-na">
                            <img src="images/brand-img05.png" alt="">
                        </a>
                    </li>
                </ul>
            </div>
            <div class="footer-holder">
                <footer class="footer">
                    <div class="info">                        
                        <div class="address">
                            <span class="white">BIMsmith Headquarters</span>
                            <span>68 S. Grove Avenue, Elgin, IL 60120</span>
                        </div>
                        <div class="contacts">
                            <div>
                                <a class="tel" href="tel:+12244848896">+1 (224) 484 - 8896</a>
                            </div>
                            <div>
                                <div>
                                    <a class="mail" href="mailto:<EMAIL>"><EMAIL></a>
                                </div>
                                <div>
                                    <a href="mailto:<EMAIL>"><EMAIL></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="links">
                        <div class="col">
                            <p>Products</p>
                            <ul>
                                <li>
                                    <a href="@ConfigurationHelper.GetValue("MarketUrl")/">Market</a>
                                </li>
                                <li>
                                    <a href="@ConfigurationHelper.GetValue("ForgeUrl")/">Forge</a>
                                </li>
                                <li>
                                    <a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/">MyBIMsmith</a>
                                </li>
                                <li>
                                    <a href="https://anguleris.com/bim-strategy/bim-content-creation">BIM Content Creation</a>
                                </li>
                            </ul>
                        </div>
                        <div class="col">
                            <p>Community</p>
                            <ul>
                                <li>
                                    <a href="@ConfigurationHelper.GetValue("NewsroomUrl")/">Newsroom</a>
                                </li>
                                <li>
                                    <a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/Contact">Support</a>
                                </li>
                            </ul>
                        </div>
                        <div class="col">
                            <p>Legal</p>
                            <ul>
                                <li><a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/legal/terms-and-conditions">Terms and conditions</a></li>
                                <li><a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/legal/privacy-policy">Privacy Policy</a></li>
                                <li><a href="@ConfigurationHelper.GetValue("BIMsmithUrl")/legal/patents-and-intellectual-property">Patents and Intellectual Property</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="f-bot">
                        <ul class="social">
                            <li>
                                <a href="https://www.facebook.com/thebimsmith/">
                                    <img src="images/soc-fb.png" alt="">
                                </a>
                            </li>
                            <li>
                                <a href="https://twitter.com/thebimsmith">
                                    <img src="images/soc-tw.png" alt="">
                                </a>
                            </li>
                            <li>
                                <a href="https://www.youtube.com/channel/UCAPJyryHrdzN5nTxYSvFqNQ">
                                    <img src="images/soc-youtube.png" alt="">
                                </a>
                            </li>
                            <li>
                                <a href="https://www.linkedin.com/company/10901027?trk=tyah&amp;trkInfo=clickedVertical%3Ashowcase%2CclickedEntityId%3A10901027%2Cidx%3A1-1-1%2CtarId%3A1478971756516%2Ctas%3ABIMsmi">
                                    <img src="images/soc-in.png" alt="">
                                </a>
                            </li>
                            <li>
                                <a href="https://www.pinterest.com/thebimsmith/">
                                    <img src="images/soc-p.png" alt="">
                                </a>
                            </li>
                        </ul>
                        <div class="copy">© @(DateTime.UtcNow.Year) Anguleris Technologies</div>
                    </div>
                </footer>
            </div>
        </div>
    </footer>
    </div>

    <script src="~/Bundles/jquerybundle.min.js"></script>
    <script src="~/Bundles/bimsmithbundle.min.js"></script>
    <script src="~/Bundles/bootstrapjsbundle.min.js"></script>

</body>