﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    /// <summary>
    /// Lunch and Learn request to manufacturer
    /// </summary>
    public class UserBIMsmithLLManufacturer
    {
        public int Id { get; set; }

        public int ManufacturerId { get; set; }

        public int Status { get; set; }

        public string AddedById { get; set; }

        public DateTime AddedDate { get; set; }

        /// ------------------------------------------
        [ForeignKey("AddedById")]
        public virtual ApplicationUser AddedBy { get; set; }

        [ForeignKey("ManufacturerId")]
        public virtual Manufacturer Manufacturer { get; set; }
    }
}