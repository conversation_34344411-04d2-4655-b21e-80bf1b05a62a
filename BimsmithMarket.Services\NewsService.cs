﻿using Azure.Storage.Blobs.Specialized;
using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Helpers;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class NewsService : INewsService
    {
        public async Task<object> ListAsync(IUnitOfWork unitOfWork, string q, int offset, int count)
        {
            var query = unitOfWork.NewsRepository.GetAll();

            if (!string.IsNullOrEmpty(q))
            {
                query = query.Where(e =>
                           e.Title.Contains(q));
            }

            var coundOfData = await query.CountAsync();

            var list = await query
                .OrderByDescending(a => a.CreatedDate)
                .Skip(offset)
                .Take(count)
                .Select(a => new
                {
                    id = a.Id,
                    vanityId = a.VanityId,
                    title = a.Title,
                    imageUrlSmall = a.ImageUrlSmall,
                    publishOption = a.PublishOption,
                    createdDate = a.CreatedDate,
                    createdBy = new
                    {
                        id = a.CreatedById,
                        firstName = a.CreatedBy.FirstName,
                        lastName = a.CreatedBy.LastName
                    }
                })
                .AsNoTracking()
                .ToListAsync();

            return new
            {
                count = coundOfData,
                data = list,
            };
        }

        public async Task<object> GetAsync(IUnitOfWork unitOfWork, int id, string vanityId)
        {
            var query = unitOfWork.NewsRepository.GetAll();

            if (id != -1)
            {
                query = query.Where(c => c.Id == id);
            }
            else
            {
                query = query.Where(c => c.VanityId == vanityId);
            }

            var item = await query
                .Select(a => new
                {
                    id = a.Id,
                    vanityId = a.VanityId,
                    title = a.Title,
                    imageUrlSmall = a.ImageUrlSmall,
                    imageUrlBig = a.ImageUrlBig,
                    htmlBody = a.HtmlBody,
                    publishOption = a.PublishOption,
                    authorTitle = a.AuthorTitle,
                    tags = a.Tags,
                    metaDescription = a.MetaDescription,
                    metaKeywords = a.MetaKeywords,
                    viewCount = a.ViewCount,
                    publishedDate = a.PublishedDate,
                    createdDate = a.CreatedDate,
                    modifiedDate = a.ModifiedDate,
                    createdBy = new
                    {
                        id = a.CreatedById,
                        firstName = a.CreatedBy.FirstName,
                        lastName = a.CreatedBy.LastName
                    },
                    modifiedBy = a.ModifiedById == null ? null : new
                    {
                        id = a.ModifiedById,
                        firstName = a.ModifiedBy.FirstName,
                        lastName = a.ModifiedBy.LastName
                    },
                    targets = a.NewsTargets.Select(x => x.Site),
                    schedulePublishDate = a.SchedulePublishDate
                })
                .FirstOrDefaultAsync();

            if (item == null)
            {
                throw new DbItemNotFoundException("Not found the News");
            }

            return item;
        }

        public async Task<object> AddAsync(IUnitOfWork unitOfWork, string userId, AddNewsModel model)
        {
            if (await unitOfWork.NewsRepository.GetAll().AnyAsync(a => a.VanityId == model.VanityId))
            {
                throw new InvalidInputException("This VanityId is occupied");
            }

            News news = new News();
            news.VanityId = model.VanityId;
            news.NewsCategoryId = model.NewsCategoryId;
            news.Title = model.Title.Trim();
            news.HtmlBody = model.HtmlBody;
            news.ImageUrlBig = model.ImageUrlBig;
            news.ImageUrlSmall = model.ImageUrlSmall;
            news.Tags = model.Tags;
            news.MetaDescription = model.MetaDescription;
            news.MetaKeywords = model.MetaKeywords;
            news.PublishOption = model.PublishOption;
            news.AuthorTitle = model.AuthorTitle;
            news.PublishedDate = model.PublishedDate;
            news.CreatedById = userId;
            news.CreatedDate = DateTime.UtcNow; 
            news.SchedulePublishDate = model.SchedulePublishDate;

            if (news.PublishOption == PublishOption.Published && news.PublishedDate == null)
            {
                news.PublishedDate = DateTime.UtcNow;
            }

            var plainText = HtmlToText.TryConvertHtml(model.HtmlBody).Trim();
            news.Descriptions = plainText.Length > 250 ? plainText.Substring(0, 250) + "..." : plainText;

            unitOfWork.NewsRepository.Insert(news);

            await unitOfWork.SaveAsync();

            if (model.Targets != null && model.Targets.Any())
            {
                foreach (var target in model.Targets.Distinct())
                {
                    var newsTarget = new NewsTarget
                    {
                        CreatedById = userId,
                        CreatedDate = DateTime.UtcNow,
                        NewsId = news.Id,
                        Site = target
                    };

                    unitOfWork.NewsTargetRepository.Insert(newsTarget);
                }
                await unitOfWork.SaveAsync();
            }

            return new
            {
                id = news.Id
            };
        }

        public async Task<object> EditAsync(IUnitOfWork unitOfWork, string userId, EditNewsModel model)
        {
            if (await unitOfWork.NewsRepository.GetAll().AnyAsync(a => a.Id != model.Id && a.VanityId == model.VanityId))
                throw new InvalidInputException("This VanityId is occupied");
                
            News news = await unitOfWork.NewsRepository.GetByIdAsync(model.Id);

            if (news == null)
            {
                throw new DbItemNotFoundException("The news not found");
            }

            news.VanityId = model.VanityId;
            news.NewsCategoryId = model.NewsCategoryId;
            news.Title = model.Title.Trim();
            news.HtmlBody = model.HtmlBody;
            news.ImageUrlBig = model.ImageUrlBig;
            news.ImageUrlSmall = model.ImageUrlSmall;
            news.Tags = model.Tags;
            news.MetaDescription = model.MetaDescription;
            news.MetaKeywords = model.MetaKeywords;
            news.PublishOption = model.PublishOption;
            news.AuthorTitle = model.AuthorTitle;
            news.PublishedDate = model.PublishedDate;
            news.ModifiedById = userId;
            news.ModifiedDate = DateTime.UtcNow;
            news.SchedulePublishDate = model.SchedulePublishDate;

            if (news.PublishOption == PublishOption.Published && news.PublishedDate == null)
            {
                news.PublishedDate = DateTime.UtcNow;
            }

            var plainText = HtmlToText.TryConvertHtml(model.HtmlBody).Trim();
            news.Descriptions = plainText.Length > 250 ? plainText.Substring(0, 250) + "..." : plainText;

            unitOfWork.NewsRepository.Edit(news);

            var existingTargets = await unitOfWork.NewsTargetRepository.GetAll()
                .Where(x => x.NewsId == news.Id)
                .Select(x => x.Site)
                .ToListAsync();

            var targetsToAdd = model.Targets.Except(existingTargets).Distinct().ToList();
            var targetsToDelete = existingTargets.Except(model.Targets).Distinct().ToList();

            if (targetsToAdd.Any())
            {
                foreach (var target in targetsToAdd)
                {
                    var newsTarget = new NewsTarget
                    {
                        CreatedById = userId,
                        CreatedDate = DateTime.UtcNow,
                        NewsId = news.Id,
                        Site = target
                    };

                    unitOfWork.NewsTargetRepository.Insert(newsTarget);
                }
            }

            if (targetsToDelete.Any())
            {
                foreach (var target in targetsToDelete)
                {
                    var newsTarget = unitOfWork.NewsTargetRepository.GetAll()
                        .First(x => x.NewsId == news.Id
                        && x.Site == target);

                    unitOfWork.NewsTargetRepository.Delete(newsTarget);
                }
            }

            await unitOfWork.SaveAsync();

            return new
            {
                id = news.Id
            };
        }

        public async Task ChangePublishStateAsync(IUnitOfWork unitOfWork, ChangeNewsPublishStateModel model)
        {
            News news = await unitOfWork.NewsRepository.GetByIdAsync(model.Id);

            news.PublishOption = model.PublishOption;

            unitOfWork.NewsRepository.Edit(news);

            await unitOfWork.SaveAsync();
        }

        public async Task DeleteAsync(IUnitOfWork unitOfWork, int id)
        {
            unitOfWork.BeginTransaction();

            News news = await unitOfWork.NewsRepository.GetByIdAsync(id);

            if (news == null)
            {
                throw new DbItemNotFoundException("The news not found");
            }

            var newsTargets = await unitOfWork.NewsTargetRepository.GetAll().Where(x => x.NewsId == id).ToListAsync();
            unitOfWork.NewsTargetRepository.Delete(newsTargets);

            unitOfWork.NewsRepository.Delete(news);

            await unitOfWork.SaveAsync();

            unitOfWork.CommitTransaction();
        }

        public async Task<object> UploadImageAsync(IFormFile formFile, bool suppressAlphaChannel)
        {
            AzureStorageService azureBlobProvider = new AzureStorageService(
                ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                ConfigurationHelper.GetValue("Environment"),
                bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                false);
            var newsContentContainer = azureBlobProvider.GetContainerByName(AzureStorageConstants.NewsContentContainer);

            using (var fileStream = formFile.OpenReadStream())
            {
                using (Image originalImage = Image.FromStream(fileStream))
                {
                    string bigImageUrl = null;
                    string smallImageUrl = null;

                    double scaleBig = Math.Max(originalImage.Height, originalImage.Width) / 1000d; //max side is 1000px for full image
                    double scaleSmall = Math.Max(originalImage.Height, originalImage.Width) / 300d; //max side is 250px for preview image

                    Image bigImage = null;

                    if (scaleBig > 1) // need to reduce size of image
                    {
                        bigImage = new Bitmap(originalImage, new Size((int)(originalImage.Width / scaleBig), (int)(originalImage.Height / scaleBig)));
                    }
                    else
                    {
                        bigImage = new Bitmap(originalImage);
                    }

                    Image smallImage = new Bitmap(originalImage, new Size((int)(originalImage.Width / scaleSmall), (int)(originalImage.Height / scaleSmall)));

                    ImageCodecInfo jgpEncoder = PhotoProvider.GetEncoder(ImageFormat.Jpeg);

                    // Create an Encoder object based on the GUID
                    // for the Quality parameter category.
                    System.Drawing.Imaging.Encoder myEncoder = System.Drawing.Imaging.Encoder.Quality;

                    // Create an EncoderParameters object.
                    // An EncoderParameters object has an array of EncoderParameter
                    // objects. In this case, there is only one
                    // EncoderParameter object in the array.
                    EncoderParameter myEncoderParameter = new EncoderParameter(myEncoder, 90L);
                    EncoderParameters myEncoderParameters = new EncoderParameters(1);
                    myEncoderParameters.Param[0] = myEncoderParameter;

                    string fileName = formFile.FileName.Replace("\"", string.Empty);

                    var bigImageBlobName = $"{DateTime.UtcNow.Year}{DateTime.UtcNow.Month}{DateTime.UtcNow.Day}_b_{fileName}";
                    var smallImageBlobName = $"{DateTime.UtcNow.Year}{DateTime.UtcNow.Month}{DateTime.UtcNow.Day}_m_{fileName}";

                    using (var b = new Bitmap(bigImage.Width, bigImage.Height))
                    {
                        b.SetResolution(bigImage.HorizontalResolution, bigImage.VerticalResolution);

                        using (var g = Graphics.FromImage(b))
                        {
                            g.Clear(Color.White);
                            g.DrawImageUnscaled(bigImage, 0, 0);
                        }
                        BlockBlobClient bigImageBlobUpload = newsContentContainer.GetBlockBlobClient(bigImageBlobName);
                        using (var stream = await bigImageBlobUpload.OpenWriteAsync(true))
                        {
                            if (suppressAlphaChannel)
                            {
                                b.Save(stream, jgpEncoder, myEncoderParameters);
                            }
                            else
                            {
                                b.Save(stream, ImageFormat.Png);
                            }

                        }
                        bigImageUrl = bigImageBlobUpload.Uri.ToString();
                    }

                    using (var b = new Bitmap(smallImage.Width, smallImage.Height))
                    {
                        b.SetResolution(smallImage.HorizontalResolution, smallImage.VerticalResolution);

                        using (var g = Graphics.FromImage(b))
                        {
                            g.Clear(Color.White);
                            g.DrawImageUnscaled(smallImage, 0, 0);
                        }
                        BlockBlobClient smallImageBlobUpload = newsContentContainer.GetBlockBlobClient(smallImageBlobName);
                        using (var stream = await smallImageBlobUpload.OpenWriteAsync(true))
                        {
                            if (suppressAlphaChannel)
                            {
                                b.Save(stream, jgpEncoder, myEncoderParameters);
                            }
                            else
                            {
                                b.Save(stream, ImageFormat.Png);
                            }
                        }
                        smallImageUrl = smallImageBlobUpload.Uri.ToString();
                    }

                    bigImage.Dispose();
                    smallImage.Dispose();

                    return new
                    {
                        smallImageUrl = smallImageUrl,
                        bigImageUrl = bigImageUrl
                    };
                }
            }
        }

        public async Task<object> UploadContentAsync(IFormFile formFile, bool suppressAlphaChannel)
        {
            AzureStorageService azureBlobProvider = new AzureStorageService(
                ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                ConfigurationHelper.GetValue("Environment"),
                bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                false);
            var newsContentContainer = azureBlobProvider.GetContainerByName(AzureStorageConstants.NewsContentContainer);

            using (var fileStream = formFile.OpenReadStream())
            {
                string fileName = formFile.FileName.Replace("\"", string.Empty);
                var blobName = $"{DateTime.UtcNow.Year}{DateTime.UtcNow.Month}{DateTime.UtcNow.Day}_{fileName}";

                BlockBlobClient blobUpload = newsContentContainer.GetBlockBlobClient(blobName);
                using (var stream = await blobUpload.OpenWriteAsync(true))
                {
                    await fileStream.CopyToAsync(stream);
                }

                return new
                {
                    url = blobUpload.Uri.ToString()
                };
            }
        }

        public async Task<object> PublicListAsync(IUnitOfWork unitOfWork, NewsTargetSite target, string q, int offset, int count, string tag)
        {
            var query = unitOfWork.NewsRepository.GetAll().Where(a => a.PublishOption == PublishOption.Published && a.NewsTargets.Any(x => x.Site == target));

            if (!string.IsNullOrWhiteSpace(q))
            {
                query = query.Where(x => x.Title.ToUpper().Contains(q.ToUpper())
                || x.Descriptions.ToUpper().Contains(q.ToUpper()));
            }

            if (!string.IsNullOrWhiteSpace(tag))
            {
                query = query.Where(x => x.Tags.ToUpper().Contains(tag.ToUpper()));
            }

            var totalCount = await query.CountAsync();

            var list = await query.OrderByDescending(a => a.PublishedDate)
                .Skip(offset)
                .Take(count)
                .Select(a => new NewsViewModel
                {
                    VanityId = a.VanityId,
                    ImageUrl = a.ImageUrlBig,
                    Title = a.Title,
                    Descriptions = a.Descriptions,
                    PublishedDate = a.PublishedDate
                })
                .AsNoTracking()
                .ToListAsync();

            var allTags = (await unitOfWork.NewsRepository.GetAll().Where(a => a.PublishOption == PublishOption.Published && a.NewsTargets.Any(x => x.Site == target)).Select(a => a.Tags).AsNoTracking().ToListAsync())
                                                          .Select(a => new
                                                          {
                                                              tags = a != null ? a.Split(',', ';').Select(t => t.Trim()).ToList() : new List<string>(),
                                                          })
                                                          .SelectMany(a => a.tags)
                                                          .Distinct()
                                                          .ToList();

            return new NewsListViewModel
            {
                News = list,
                Tags = allTags,
                TotalCount = totalCount
            };
        }

        public async Task<MetaInfoDto> GetNewsMetaInfoAsync(string vanityId, IUnitOfWork unitOfWork)
        {
            var news = await unitOfWork.NewsRepository.GetAll()
                                       .Where(x => x.PublishOption == PublishOption.Published
                                                && x.VanityId.ToLower() == vanityId.ToLower())
                                       .FirstOrDefaultAsync();

            if (news == null)
                throw new DbItemNotFoundException();

            return news.Adapt<MetaInfoDto>();
        }
    }
}
