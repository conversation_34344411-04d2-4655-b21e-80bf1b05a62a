﻿using System.Net;
using System.Security.Claims;

namespace BIMsmithMarket.Domain.Dto.Authorization
{
    public class LoginIdentityResultDto
    {
        public ClaimsIdentity Identity { get; set; }

        public string FirstName { get; set; }

        public string LastName { get; set; }

        public string Roles { get; set; }

        public string U { get; set; }

        public string UserId { get; set; }

        public string UserName { get; set; }

        public CookieCollection BIMsmithCookies { get; set; }

        public string Error { get; set; }
    }
}