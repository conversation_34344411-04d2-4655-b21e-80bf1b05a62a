﻿namespace BIMsmithMarket.Domain.Dto
{
    public class PhotoProductDto
    {
        public int? PhotoId { get; set; }

        public string UploadUrl { get; set; }

        public string Small { get; set; }

        public string Middle { get; set; }

        public string Big { get; set; }
    }

    public class PhotoProductWithStatusDto : PhotoProductDto
    {
        public int UpdatesCount { get; set; }

        public int FileSyncStatusCode { get; set; }

    }

    public class PhotoProductWithMainParamDto : PhotoProductWithStatusDto
    {
        public bool IsMainPhoto { get; set; }
    }
}
