﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Configurations>Debug;Release;Dev;UAT</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BIMsmith.AzureStorageProvider" Version="8.0.4" />
    <PackageReference Include="FileSignatures" Version="5.0.2" />
    <PackageReference Include="FreeSpire.Doc" Version="12.2.0" />
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="MassTransit" Version="8.2.3" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.7" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="8.0.7" />
    <PackageReference Include="Microsoft.Extensions.Http.Resilience" Version="8.7.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Serilog" Version="4.0.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.5" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.7" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BIMsmithMarket.Domain\BIMsmithMarket.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="gsdll32.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="gsdll64.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="urlmon.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
