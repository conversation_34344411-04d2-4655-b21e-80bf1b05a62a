﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <urlCompression doStaticCompression="true" doDynamicCompression="true" />
    <httpCompression>
      <dynamicTypes>
        <clear />
        <add enabled="true" mimeType="text/*" />
        <add enabled="true" mimeType="message/*" />
        <add enabled="true" mimeType="application/x-javascript" />
        <add enabled="true" mimeType="application/javascript" />
        <add enabled="true" mimeType="application/json" />
        <add enabled="false" mimeType="*/*" />
        <add enabled="true" mimeType="application/atom+xml" />
        <add enabled="true" mimeType="application/atom+xml;charset=utf-8" />
      </dynamicTypes>
      <staticTypes>
        <clear />
        <add enabled="true" mimeType="text/*" />
        <add enabled="true" mimeType="message/*" />
        <add enabled="true" mimeType="application/javascript" />
        <add enabled="true" mimeType="application/atom+xml" />
        <add enabled="true" mimeType="application/xaml+xml" />
        <add enabled="true" mimeType="application/json" />
        <add enabled="false" mimeType="*/*" />
      </staticTypes>
    </httpCompression>
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="524288000" />
        <!-- Max size of upload request is 500 Mib-->
      </requestFiltering>
    </security>
	<modules>
	    <remove name="WebDAVModule" />
	</modules>
    <rewrite>
      <rules>
        <clear />
        <rule name="Redirect to https" stopProcessing="true">
          <match url="(.*)" />
          <conditions>
            <add input="{HTTPS}" pattern="^OFF$" />
          </conditions>
          <action type="Redirect" url="https://{HTTP_HOST}{REQUEST_URI}" appendQueryString="false" redirectType="Permanent" />
        </rule>
        <rule name="all domains www to non-www" stopProcessing="true">
          <match url="(.*)" />
          <conditions logicalGrouping="MatchAny" trackAllCaptures="false">
            <add input="{HTTP_HOST}" pattern="^(www\.)(.*)$" />
          </conditions>
          <action type="Redirect" url="https://{C:2}{REQUEST_URI}" redirectType="Permanent" />
        </rule>       
      </rules>
    </rewrite>    
    <httpProtocol>
      <customHeaders>
        <add name="Referrer-Policy" value="no-referrer-when-downgrade" />
      </customHeaders>
    </httpProtocol>
  </system.webServer>
</configuration>