﻿using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Constants
{
    public static class ProjectFileConstants
    {
        public static readonly IReadOnlyList<string> ProjectFileTitles = new List<string>
        {
            RevitFileTitle,
            AutoCADFileTitle,
            SketchupFileTitle,
            IFCFileTitle,
            ARCHICADFileTitle,
            ThreeDsMaxFileTitle,
            IESFileTitle,
            BentleyFileTitle,
            VectorworksFileTitle,
            ChiefArchitectFileTitle,
            STEPFileTitle,
            SATFileTitle,
            PDFFileTitle,
            FabricationFileTitle
        };

        public const string RevitFileTitle = "Revit";

        public const string AutoCADFileTitle = "AutoCAD";

        public const string SketchupFileTitle = "Sketchup";

        public const string IFCFileTitle = "IFC";

        public const string ARCHICADFileTitle = "ARCHICAD";

        public const string ThreeDsMaxFileTitle = "3ds Max";

        public const string IESFileTitle = "IES";

        public const string BentleyFileTitle = "Bentley";

        public const string VectorworksFileTitle = "Vectorworks";

        public const string ChiefArchitectFileTitle = "Chief Architect";

        public const string STEPFileTitle = "STEP Files";

        public const string SATFileTitle = "SAT File";

        public const string PDFFileTitle = "PDF";

        public const string FabricationFileTitle = "Fabrication";
    }
}