﻿using BIMsmithMarket.Domain.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels.RevitProcessing
{
    public class RevitProcessRevitParameterMapping : BaseEntity
    {
        [StringLength(100)]
        public string RevitParameter { get; set; }

        public MarketField? MarketField { get; set; }

        [StringLength(100)]
        public string CustomValue { get; set; }

        public int RevitProcessId { get; set; }

        [ForeignKey("RevitProcessId")]
        public virtual RevitProcess RevitProcess { get; set; }
    }
}