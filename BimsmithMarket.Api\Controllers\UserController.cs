﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Api.Providers;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Flurl;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Json;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// 
    /// </summary>    
    [Route("api/[controller]/[action]")]
    public class UserController : BaseApiController
    {
        private readonly IUserService _userService;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly IHttpClientFactory _httpClientFactory;

        public UserController(
            UserManager<ApplicationUser> userManager,
            IUserService userService,
            IHttpClientFactory httpClientFactory)
        {
            _userManager = userManager;
            _userService = userService;
            _httpClientFactory = httpClientFactory;
        }

        [HttpGet]
#if !DEBUG
        [Authorize]
#endif
        public async Task<IActionResult> Subscribed()
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            return Ok(new { subscribed = await _userService.IsUserSubscribedAsync(userId, unitOfWork) });
        }

        /// <summary>
        /// Get list of all users in system: For Role - ADMIN
        /// </summary>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <param name="q"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("List")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> List(int offset = 0, int count = 10, string q = null)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var superAdminEmails = ConfigurationHelper.GetValue("AdministratorEmails").Split(',').Select(a => a.ToLower()).ToList();

                IQueryable<ApplicationUser> query = unitOfWork.UserRepository.GetAll().Where(a => a.UserName != "System_User");

                if (!string.IsNullOrEmpty(q))
                {
                    query = query.Where(e => e.FirstName.StartsWith(q)
                            || e.Id == q
                            || e.LastName.StartsWith(q)
                            || e.Email == q);
                }

                var countOfUsers = await query.CountAsync();

                var roles = await unitOfWork.RoleRepository.GetAll().AsNoTracking().ToArrayAsync();
                var adminRoleId = roles.FirstOrDefault(x => x.Name == DbConstants.AdminRole)?.Id;
                var users = await query
                    .OrderByDescending(a => a.CreatedDate)
                    .Select(a => new UserListModel
                    {
                        Id = a.Id,
                        UserName = a.UserName,
                        FirstName = a.FirstName,
                        LastName = a.LastName,
                        Email = a.Email,
                        CreatedDate = a.CreatedDate,
                        RoleIds = a.Roles.Select(x => x.RoleId)
                    })
                    .Skip(offset)
                    .Take(count)
                    .ToArrayAsync();

                foreach (UserListModel user in users)
                {
                    user.Roles = roles.Where(x => user.RoleIds.Contains(x.Id)).Select(x => x.Name).ToList();
                    user.IsAdmin = user.RoleIds.Contains(adminRoleId);
                    user.IsSuperAdmin = superAdminEmails.Contains(user.Email.ToLower());
                }

                try
                {
                    var httpClient = _httpClientFactory.CreateClient();
                    {
                        var bimsmithWebsiteUrl = ConfigurationHelper.GetValue("BimsmithApiUrl");
                        var userCompaniesUrl = string.Concat(bimsmithWebsiteUrl, "/Company/GetUserCompanies");
                        var model = new
                        {
                            UserIds = users.Select(x => x.Id).ToList(),
                            Token = ConfigurationHelper.GetValue("BimsmithAccessToken")
                        };
                        var json = JsonConvert.SerializeObject(model);
                        var body = new StringContent(json, Encoding.UTF8, "application/json");
                        using var userCompaniesResult = await httpClient.PostAsync(userCompaniesUrl, body);
                        if (userCompaniesResult.IsSuccessStatusCode)
                        {
                            var resultString = await userCompaniesResult.Content.ReadAsStringAsync();
                            var userCompanies = JsonConvert.DeserializeObject<ICollection<UserCompaniesModel>>(resultString);
                            foreach (var userCompany in userCompanies)
                            {
                                var company = users.FirstOrDefault(x => x.Id == userCompany.UserId);
                                if (company != null) company.Company = userCompany.Company;
                            }
                        }
                    }
                }
                catch (Exception)
                {
                    Log.Error("Error while gettin user companies");
                }

                var result = new
                {
                    count = countOfUsers,
                    data = users
                };

                return Ok(result);
            }
        }

        /// <summary>
        /// Check uses access to analytics
        /// </summary>
        /// <param name="authToken"></param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        [ActionName("HasAcsessToAnalytics")]
        public async Task<IActionResult> HasAcsessToAnalytics(string authToken)
        {
            var userToken = AuthenticationService.GetUserToken(authToken);
            if (userToken != null && userToken.UserId != null)
            {
                var data = await CheckAnalyticsAsync(authToken, userToken.UserId, userToken.UserEmail);
                if (data.fullAccess || data.manufacturers != null)
                    return Ok(true);
            }
            return Ok(false);
        }

        [HttpGet]
        [AllowAnonymous]
        [ActionName("AllowedTaxonomyUser")]
        public async Task<IActionResult> AllowedTaxonomyUser(string email)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var adminRoleId = (await unitOfWork.RoleRepository.GetAll().FirstOrDefaultAsync(x => x.Name == DbConstants.AdminRole))?.Id;
                var allowedUser = await unitOfWork.UserRepository.GetAll().FirstOrDefaultAsync(x => x.Email == email && x.Roles.Any(r => r.RoleId == adminRoleId));
                return Ok(allowedUser != null);
            }
        }

        /// <summary>
        /// Edit Permissions for user
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("EditPermissions")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> EditPermissions([FromBody] EditPermissionsModel model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var user = await unitOfWork.UserRepository.GetAll().FirstAsync(a => a.Id == model.UserId);

                var superAdminEmails = ConfigurationHelper.GetValue("AdministratorEmails").Split(',').Select(a => a.ToLower()).ToList();

                if (superAdminEmails.Contains(user.Email.ToLower()))
                    return BadRequest("You can not edit the user, because the user has Admin role in Web.Config file");

                var userRoles = await unitOfWork.UserRoleRepository.GetAll().Where(a => a.UserId == user.Id).ToListAsync();
                var roles = await unitOfWork.RoleRepository.GetAll().ToListAsync();
                var adminRoleId = (await unitOfWork.RoleRepository.GetAll().FirstOrDefaultAsync(x => x.Name == DbConstants.AdminRole))?.Id;
                var adminUserRole = userRoles.FirstOrDefault(a => a.RoleId == adminRoleId);

                if (model.IsAdmin && adminUserRole == null)
                {
                    ApplicationUserRole userRole = new ApplicationUserRole();
                    userRole.RoleId = roles.First(a => a.Name == DbConstants.AdminRole).Id;
                    userRole.UserId = user.Id;
                    unitOfWork.UserRoleRepository.Insert(userRole);
                }
                else if (!model.IsAdmin && adminUserRole != null)
                {
                    unitOfWork.UserRoleRepository.Delete(adminUserRole);
                }

                CacheHelper.ClearSpecificCache("*/api/User/List*");

                await unitOfWork.SaveAsync();
                return Ok();
            }
        }

        /// <summary>
        /// Check user access to salesforce
        /// </summary>
        /// <param name="email"></param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        [ActionName("HasAccessToSalesforce")]
        public async Task<IActionResult> HasAccessToSalesforce(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return Ok(false);

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var hasAccess = await unitOfWork.ManufacturerAdminUserRepository.GetAll()
                    .AnyAsync(x => x.Email.ToUpper() == email.ToUpper()
                    && ((x.Roles & ManufacturerAdminRole.SalesforceAccess) == ManufacturerAdminRole.SalesforceAccess
                    || (x.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess));
                return Ok(hasAccess);
            }
        }

        /// <summary>
        /// Makes copy of BIMsmithMarket user on Market
        /// </summary>
        /// <param name="model">BIMsmithMarket user model</param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        [ActionName("AddBimsmithUser")]
        public async Task<IActionResult> AddBimsmithUser(AddBimsmithUserModel model)
        {
            var result = await _userService.AddBimsmithUser(model);
            if (result)
            {
                CacheHelper.ClearSpecificCache("*/api/User/List*");
                return Ok();
            }
            return BadRequest();
        }

        /// <summary>
        /// Deletes user account from Market
        /// </summary>
        /// <param name="model">Deletion account model</param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        [ActionName("DeleteAccount")]
        public async Task<IActionResult> DeleteAccount([FromBody] DeleteAccountModel model)
        {
            bool result = await _userService.DeleteAccountAsync(model);
            if (result)
            {
                CacheHelper.ClearSpecificCache("*/api/User/List*");
                return Ok();
            }
            return BadRequest();
        }

        [HttpDelete]
        [ActionName("Delete")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Delete([FromQuery] string deletionUserEmail)
        {
            if (string.IsNullOrWhiteSpace(deletionUserEmail))
                return BadRequest("Invalid email");

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var userExists = await unitOfWork.UserRepository.GetAll().AnyAsync(x => x.Email.ToLower() == deletionUserEmail.ToLower());
                if (!userExists)
                {
                    return BadRequest("User does not exist");
                }
                var userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
                var userEmail = (await unitOfWork.UserRepository.GetAll().FirstOrDefaultAsync(a => a.Id == userId))?.Email;
                if (userEmail == deletionUserEmail)
                {
                    return BadRequest("You can't drop yourself");
                }

                var httpClient = _httpClientFactory.CreateClient();
                {
                    var deleteModel = new DeleteAccountModel
                    {
                        Email = deletionUserEmail,
                        SecretKey = ConfigurationHelper.GetValue("BimsmithAccessToken")
                    };
                    var url = new Url(ConfigurationHelper.GetValue("BimsmithApiUrl"))
                        .AppendPathSegment("Auth/DeleteAccountByEmail")
                        .SetQueryParam("email", deletionUserEmail);
                    using var response = await httpClient.PostAsJsonAsync(url, deleteModel);

                    var bimsmithDeletionResult = false;
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        return BadRequest("Could not check BIMsmith user");
                    }
                    else if (response.IsSuccessStatusCode)
                    {
                        bimsmithDeletionResult = JsonConvert.DeserializeObject<bool>(await response.Content.ReadAsStringAsync());
                    }
                    else if (response.StatusCode == HttpStatusCode.NotFound)
                    {
                        bimsmithDeletionResult = true;
                    }

                    if (bimsmithDeletionResult)
                    {
                        var manufacturerAdmin = await unitOfWork.ManufacturerAdminUserRepository.GetAll().FirstOrDefaultAsync(x => x.Email == deletionUserEmail);
                        if (manufacturerAdmin != null)
                        {
                            unitOfWork.ManufacturerAdminUserRepository.Delete(manufacturerAdmin);
                            await unitOfWork.SaveAsync();
                        }
                        var user = await _userManager.FindByEmailAsync(deletionUserEmail);
                        var deletionResult = (await _userManager.DeleteAsync(user)).Succeeded;
                        if (deletionResult)
                        {
                            CacheHelper.ClearSpecificCache("*/api/User/List*");
                            return Ok();
                        }
                    }
                }

                return BadRequest();
            }
        }

        /// <summary>
        /// Checks if user account exists with email specified
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        [ActionName("CheckAccount")]
        public async Task<IActionResult> CheckAccount(CheckAccountDto model)
        {
            return Ok(await _userService.CheckAccountAsync(model, _userManager));
        }

        /// <summary>
        /// Checks if users has admin permissions
        /// </summary>
        /// <param name="email">The user email</param>
        /// <returns></returns>
        [ActionName("IsUserAdmin")]
        [HttpGet]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> IsUserAdmin(string email)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _userService.IsUserAdminAsync(email, unitOfWork));
        }

        #region private methods
        private async Task<dynamic> CheckAnalyticsAsync(string token, string userId, string email = null)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var query = unitOfWork.ManufacturerRepository.GetAll()
                    .Select(a => new
                    {
                        Manufacturer = a,
                        AccessToMarketData = true,
                        AccessToMarketManufacturerActionsData = true,
                        AccessToForgeData = true,
                        AccessToNanolumensData = true,
                    });

                bool fullAccess = true;

                var user = await unitOfWork.UserRepository.GetAll().FirstOrDefaultAsync(a => a.Id == userId || a.Email.ToLower() == email.ToLower());

                if (user != null)
                {
                    var adminEmails = ConfigurationHelper.GetValue("AdministratorEmails").Split(',').Select(a => a.ToLower()).ToList();

                    if (adminEmails.Contains(user.Email.ToLower()) == false && !await _userManager.IsInRoleAsync(user, DbConstants.AdminRole))
                    {
                        fullAccess = false;

                        var owners = unitOfWork.ManufacturerRepository.GetAll().Where(a => a.OwnerId == user.Id)
                                    .Select(a => new
                                    {
                                        Manufacturer = a,
                                        AccessToMarketData = true,
                                        AccessToMarketManufacturerActionsData = true,
                                        AccessToForgeData = true,
                                        AccessToNanolumensData = true
                                    });

                        query = unitOfWork.ManufacturerAdminUserRepository.GetAll().Where(a =>
                                            a.AdminUserId == userId &&
                                            ((a.Roles & ManufacturerAdminRole.Analytics) == ManufacturerAdminRole.Analytics
                                            ||
                                            (a.Roles & ManufacturerAdminRole.AnalyticsForge) == ManufacturerAdminRole.AnalyticsForge
                                            ||
                                            (a.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess)
                                            ).Select(a => new
                                            {
                                                Manufacturer = a.Manufacturer,
                                                AccessToMarketData = (a.Roles & ManufacturerAdminRole.Analytics) == ManufacturerAdminRole.Analytics || (a.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess,
                                                AccessToMarketManufacturerActionsData = (a.Roles & ManufacturerAdminRole.Analytics) == ManufacturerAdminRole.Analytics && (a.Roles & ManufacturerAdminRole.AnalyticsManufacturerActionsData) == ManufacturerAdminRole.AnalyticsManufacturerActionsData || (a.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess,
                                                AccessToForgeData = (a.Roles & ManufacturerAdminRole.AnalyticsForge) == ManufacturerAdminRole.AnalyticsForge || (a.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess,
                                                AccessToNanolumensData = ((a.Roles & ManufacturerAdminRole.NanolumensAnalytics) == ManufacturerAdminRole.NanolumensAnalytics || (a.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess) && a.ManufacturerId == AnalyticsConstants.NanolumensManufacturerId
                                            });

                        query = query.Concat(owners).Distinct();
                    }
                }
                else
                {
                    fullAccess = false;
                    query = unitOfWork.ManufacturerAdminUserRepository.GetAll().Where(a =>
                                      a.Email == email &&
                                      ((a.Roles & ManufacturerAdminRole.Analytics) == ManufacturerAdminRole.Analytics
                                      ||
                                      (a.Roles & ManufacturerAdminRole.AnalyticsForge) == ManufacturerAdminRole.AnalyticsForge
                                      ||
                                      (a.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess)
                                        ).Select(a => new
                                        {
                                            Manufacturer = a.Manufacturer,
                                            AccessToMarketData = (a.Roles & ManufacturerAdminRole.Analytics) == ManufacturerAdminRole.Analytics || (a.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess,
                                            AccessToMarketManufacturerActionsData = (a.Roles & ManufacturerAdminRole.Analytics) == ManufacturerAdminRole.Analytics && (a.Roles & ManufacturerAdminRole.AnalyticsManufacturerActionsData) == ManufacturerAdminRole.AnalyticsManufacturerActionsData || (a.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess,
                                            AccessToForgeData = (a.Roles & ManufacturerAdminRole.AnalyticsForge) == ManufacturerAdminRole.AnalyticsForge || (a.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess,
                                            AccessToNanolumensData = ((a.Roles & ManufacturerAdminRole.NanolumensAnalytics) == ManufacturerAdminRole.NanolumensAnalytics || (a.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess) && a.ManufacturerId == AnalyticsConstants.NanolumensManufacturerId
                                        });
                }

                string userEmail = user?.Email ?? email;
                bool salesforceAccess = await unitOfWork.ManufacturerAdminUserRepository.GetAll()
                    .AnyAsync(x => x.Email.ToUpper() == userEmail.ToUpper()
                                && ((x.Roles & ManufacturerAdminRole.SalesforceAccess) == ManufacturerAdminRole.SalesforceAccess
                                || (x.Roles & ManufacturerAdminRole.FullAccess) == ManufacturerAdminRole.FullAccess));

                var manufacturers = await query
                    .Select(a => new
                    {
                        id = a.Manufacturer.Id,
                        name = a.Manufacturer.Name,
                        site = a.Manufacturer.Site,
                        analyticsSettings = a.Manufacturer.AnalyticsSetting,
                        logo = new
                        {
                            id = a.Manufacturer.PhotoId,
                            small = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.SmallImgUrl : null,
                            big = a.Manufacturer.PhotoId != null ? a.Manufacturer.Photo.OriginalImgUrl : null
                        },
                        published = a.Manufacturer.Published,
                        accessToMarketData = a.AccessToMarketData,
                        accessToMarketManufacturerActionsData = a.AccessToMarketManufacturerActionsData,
                        accessToForgeData = a.AccessToForgeData,
                        accessToNanolumensData = a.AccessToNanolumensData,
                        swatchboxManufacturerId = a.Manufacturer.SwatchboxManufacturerId
                    })
                    .ToListAsync();

                dynamic result = new
                {
                    fullAccess = fullAccess,
                    salesforceAccess = salesforceAccess,
                    manufacturers = manufacturers.Any() ? manufacturers : null
                };

                return result;

            }
        }
        #endregion
    }
}