﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class ManufacturerAdminUserService : IManufacturerAdminUserService
    {
        public async Task<object> AddAsync(IUnitOfWork unitOfWork, string userId, AddManufacturerAdminUserModel model)
        {
            var user = await unitOfWork.UserRepository.GetAll().FirstOrDefaultAsync(a => a.Email == model.AdminUserEmail);
            if (user == null)
            {

            }
            else
            {
                var existingAdminUser = await unitOfWork.ManufacturerAdminUserRepository.GetAll()
                                        .FirstOrDefaultAsync(d => d.AdminUserId == user.Id && d.ManufacturerId == model.ManufacturerId);
                if (existingAdminUser != null)
                {
                    string message = string.Format("The user with email - '{0}' was alredy added for manufacturer with ID - '{1}'", model.AdminUserEmail, model.ManufacturerId);
                    throw new InvalidInputException(message);
                }
            }

            var manAdminUser = new Domain.DBModels.ManufacturerAdminUser
            {
                ManufacturerId = model.ManufacturerId,
                AddedById = userId,
                AddedDate = DateTime.UtcNow,
                AdminUserId = user?.Id,
                Email = model.AdminUserEmail,
                Roles = model.Roles,
                Status = model.Status
            };

            unitOfWork.ManufacturerAdminUserRepository.Insert(manAdminUser);
            await unitOfWork.SaveAsync();

            return new { id = manAdminUser.Id };
        }

        public async Task EditAsync(IUnitOfWork unitOfWork, string userId, EditManufacturerAdminUserModel model)
        {
            var manAdminUser = await unitOfWork.ManufacturerAdminUserRepository.GetAll().FirstOrDefaultAsync(a => a.Id == model.Id);

            if (manAdminUser == null)
            {
                throw new DbItemNotFoundException("Not Found");
            }

            manAdminUser.ModifiedById = userId;
            manAdminUser.ModifiedDate = DateTime.UtcNow;
            manAdminUser.Roles = model.Roles;
            manAdminUser.Status = model.Status;

            unitOfWork.ManufacturerAdminUserRepository.Edit(manAdminUser);
            await unitOfWork.SaveAsync();
        }

        public async Task DeleteAsync(IUnitOfWork unitOfWork, int id)
        {
            var manAdminUser = await unitOfWork.ManufacturerAdminUserRepository.GetAll().FirstOrDefaultAsync(a => a.Id == id);

            if (manAdminUser == null)
            {
                throw new DbItemNotFoundException("Not Found");
            }

            unitOfWork.ManufacturerAdminUserRepository.Delete(manAdminUser);
            await unitOfWork.SaveAsync();
        }

        public async Task<object> ListAsync(IUnitOfWork unitOfWork, int manufacturerId, DateTime? dateFrom, DateTime? dateTo, int count, int offset)
        {
            var query = unitOfWork.ManufacturerAdminUserRepository.GetAll().Where(a => a.ManufacturerId == manufacturerId);

            if (dateFrom.HasValue && dateTo.HasValue)
            {
                var from = dateFrom.Value.Date;
                var to = dateTo.Value.Date.AddDays(1).AddMilliseconds(-1);

                query = query.Where(a => a.AddedDate >= from && a.AddedDate <= to);
            }

            return await query
                                .OrderBy(a => a.Id)
                                .Skip(offset)
                                .Take(count)
                                .Select(a => new
                                {
                                    id = a.Id,
                                    firstName = a.AdminUserId != null ? a.AdminUser.FirstName : "-",
                                    lastName = a.AdminUserId != null ? a.AdminUser.LastName : "-",
                                    email = a.AdminUserId != null ? a.AdminUser.Email : a.Email,
                                    userId = a.AdminUserId,
                                    permissions = a.Roles,
                                    addedDate = a.AddedDate
                                })
                                .AsNoTracking()
                                .ToListAsync();
        }
    }
}
