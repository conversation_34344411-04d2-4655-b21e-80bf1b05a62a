﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class HelperService : IHelperService
    {
        public async Task<object> GetStatsFromFileAsync(IUnitOfWork unitOfWork, string path)
        {
            return IISLogsParser.GetStatsFromFile(path);
        }
    }
}
