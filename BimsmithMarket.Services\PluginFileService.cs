﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto.PluginFileDto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class PluginFileService : IPluginFileService
    {
        public async Task<GetPluginFileDto> GetAsync(IUnitOfWork unitOfWork, int id)
        {
            var pluginFile = await unitOfWork.PluginFileRepository.GetAll()
                .Include(a => a.File)
                .SingleOrDefaultAsync(a => a.Id == id);

            if (pluginFile != null)
            {
                return new GetPluginFileDto
                {
                    Id = pluginFile.Id,
                    Info = pluginFile.Info,
                    File = new GetPluginFileInfoDto
                    {
                        Id = pluginFile.FileId,
                        Name = pluginFile.File.Url,
                    },
                };
            }
            else
            {
                throw new DbItemNotFoundException($"PluginFile with id: {id} not found");
            }
        }

        public async Task<List<GetPluginFileDto>> ListAsync(PluginType pluginType, IUnitOfWork unitOfWork)
        {
            var countries = unitOfWork.PluginFileRepository.GetAll().Where(x => x.PluginType == pluginType);
            return await countries.Select(pluginFile => new GetPluginFileDto
            {
                Id = pluginFile.Id,
                Info = pluginFile.Info,
                File = new GetPluginFileInfoDto
                {
                    Id = pluginFile.FileId,
                    Name = pluginFile.File.Url,
                },
            })
            .AsNoTracking()
            .ToListAsync();
        }

        public async Task<EditPluginFileDto> AddAsync(IUnitOfWork unitOfWork, string userId, AddPluginFileDto model)
        {
            var pluginFile = model.Adapt<PluginFile>();
            pluginFile.CreatedById = userId;
            pluginFile.CreatedDate = DateTime.UtcNow;
            unitOfWork.PluginFileRepository.Insert(pluginFile);
            await unitOfWork.SaveAsync();

            return pluginFile.Adapt<EditPluginFileDto>();
        }

        public async Task<EditPluginFileDto> EditAsync(IUnitOfWork unitOfWork, string userId, EditPluginFileDto model)
        {
            var pluginFile = await unitOfWork.PluginFileRepository.GetByIdAsync(model.Id);
            model.Adapt(pluginFile);
            pluginFile.ModifiedById = userId;
            pluginFile.ModifiedDate = DateTime.UtcNow;
            unitOfWork.PluginFileRepository.Edit(pluginFile);
            await unitOfWork.SaveAsync();

            return pluginFile.Adapt<EditPluginFileDto>();
        }

        public async Task DeleteAsync(IUnitOfWork unitOfWork, int id)
        {
            var pluginFile = await unitOfWork.PluginFileRepository.GetByIdAsync(id);
            if (pluginFile != null)
            {
                unitOfWork.PluginFileRepository.Delete(pluginFile);
                await unitOfWork.SaveAsync();
            }
            else
            {
                throw new DbItemNotFoundException($"PluginFile with id: {id} not found");
            }
        }

        public async Task<List<PluginFileVersionDto>> VersionListAsync(bool orderByDate, PluginType pluginType, IUnitOfWork unitOfWork)
        {
            IQueryable<PluginFile> pluginFilesQuery = unitOfWork.PluginFileRepository.GetAll()
                .Where(x => pluginType == PluginType.None || x.PluginType == pluginType)
                .Include(x => x.File)
                .AsQueryable();

            if (orderByDate)
                pluginFilesQuery = pluginFilesQuery.OrderByDescending(x => x.CreatedDate);
            else
                pluginFilesQuery = pluginFilesQuery.OrderByDescending(x => x.Version);

            List<PluginFile> pluginFiles = await pluginFilesQuery.ToListAsync();

            return pluginFiles.Adapt<List<PluginFileVersionDto>>();
        }

        public async Task<List<PluginFileVersionUpdateDto>> VersionUpdatesAsync(string version, PluginType pluginType, IUnitOfWork unitOfWork)
        {
            if (string.IsNullOrWhiteSpace(version))
                throw new InvalidInputException("Please specify version");

            version = version.ToLower().Trim();

            List<PluginFileVersionUpdateInternalDto> latestVersions = await unitOfWork.PluginFileRepository.GetAll()
                .Where(x => x.PluginType == pluginType && !(x.Version == null || x.Version == string.Empty) && x.Version.ToLower().Trim().CompareTo(version) > 0)
                .OrderByDescending(x => x.Id)
                .ProjectToType<PluginFileVersionUpdateInternalDto>()
                .ToListAsync();

            return latestVersions.Adapt<List<PluginFileVersionUpdateDto>>();
        }
    }
}