{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"MarketDBConnection": "Data Source=*************;Initial Catalog=bimsmith-market-uat;User ID=sa;Password=****************;Connect Timeout=15;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultipleActiveResultSets=true;MultiSubnetFailover=False"}, "BaseLogPath": "C:\\Work Folder\\", "MailChimpAPIKey": "************************************", "MailChimpListId": "f150d11cd0", "BimsmithApiToken": "c1R3C79Z9dVl7Ienc3fF2A9pc", "BimsmithApiUrl": "http://dev.bimsmith.com/api", "GoogleRecaptchaSecretKey": "6Lf52q8ZAAAAAIjwa0c0gKEIuxKIynM6ZhtSxiJD", "GoogleRecaptchaPublicKey": "6Lf52q8ZAAAAACkC_uWLqH28KyE-rXgfN0LMigxx", "GoogleRecaptchaApiUrl": "https://www.google.com/recaptcha/api/siteverify", "commentAdminEmail": "<EMAIL>,<EMAIL>", "emailAccountInfo": "<EMAIL>", "emailAccountDisplayName": "Market Info", "MarketUrl": "https://market-dev.bimsmith.com", "BimsmithUrl": "https://dev.bimsmith.com", "ForgeUrl": "https://forge-uat.bimsmith.com", "BlogUrl": "https://localhost:7147", "CBOBaseAddress": "https://bimsmith-translatorapi-dev.bimsmith.com/", "AnalyticsWebApiBaseAddress": "https://analytics-web-api-dev.bimsmith.com/", "IsDevEnvironment": "true", "Smtp": {"Host": "smtp.office365.com", "Port": "587", "EnableSsl": "true", "UserName": "<EMAIL>", "Password": "vcxwvrpwlkdwjxwf", "ChecksEmail": "<EMAIL>"}, "DevelopmentAllowedEmails": "<EMAIL>,pav<PERSON><EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,sba<PERSON><PERSON><PERSON><EMAIL>,<EMAIL>,r<PERSON><EMAIL>,pap<PERSON><PERSON>@qarea.com,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,matsy<PERSON><PERSON>@qarea.us,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>", "RobotsContent": "User-agent: *;Disallow: /"}