﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// Starter controller
    /// </summary>
    [Route("api/[controller]/[action]")]
    public class StarterController : BaseApiController
    {
        /// <summary>
        /// Starter service
        /// </summary>
        private readonly IStarterService _starterService;

        public StarterController(IStarterService starterService)
        {
            _starterService = starterService;
        }

        /// <summary>
        /// Get list of Forge's starters
        /// </summary>
        /// <param name="manufacturerId"></param>
        /// <param name="q"></param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("List")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> List(int manufacturerId = -1, string q = null, int offset = 0, int count = 10)
        {
            return Ok(await _starterService.ListAsync(manufacturerId, null, null, q, offset, count));
        }

        /// <summary>
        /// Get list of Forge's starters for public
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier</param>
        /// <param name="staging">The staging flag</param>
        /// <param name="q">The query string</param>
        /// <param name="offset">The count to skip</param>
        /// <param name="count">The count to take</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("PublicList")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> PublicList(
            int manufacturerId = -1,
            bool? staging = null,
            string q = null,
            int offset = 0,
            int count = 10)
        {
            return Ok(await _starterService.ListAsync(manufacturerId, true, staging, q, offset, count));
        }

        /// <summary>
        /// Updates the weights.
        /// </summary>
        /// <param name="model">The model.</param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("UpdateWeights")]
#if (!DEBUG)
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> UpdateWeights([FromBody] UpdateWeightsDto model)
        {
            await _starterService.UpdateWeightsAsync(model);

            CacheHelper.ClearSpecificCache("*/api/Starter/List*");
            CacheHelper.ClearSpecificCache("*/api/Starter/PublicList*");

            return Ok();
        }

        /// <summary>
        /// Refresh starters by forgeManufacturerId
        /// </summary>
        /// <param name="forgeManufacturerId"></param>
        /// <returns></returns>
        [HttpGet]
#if (!DEBUG)
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [ActionName("UpdateManufacturerStarters")]
        public async Task<IActionResult> UpdateManufacturerStarters(string forgeManufacturerId = null)
        {
            await _starterService.RefreshStartersFromForgeAsync(forgeManufacturerId);

            CacheHelper.ClearSpecificCache("*/api/Starter/List*");
            CacheHelper.ClearSpecificCache("*/api/Starter/PublicList*");

            return Ok();
        }

        /// <summary>
        /// Sets the specified starter published status
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
#if (!DEBUG)
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [ActionName("SetPublishStatus")]
        public async Task<IActionResult> SetPublishStatus(SetStarterStatusDto model)
        {
            await _starterService.SetPublishStatusAsync(model);

            CacheHelper.ClearSpecificCache("*/api/Starter/List*");
            CacheHelper.ClearSpecificCache("*/api/Starter/PublicList*");

            return Ok();
        }

        /// <summary>
        /// Sets the specified starter staging status
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
#if (!DEBUG)
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [ActionName("SetStagingStatus")]
        public async Task<IActionResult> SetStagingStatus(SetStarterStatusDto model)
        {
            await _starterService.SetStagingStatusAsync(model);

            CacheHelper.ClearSpecificCache("*/api/Starter/List*");
            CacheHelper.ClearSpecificCache("*/api/Starter/PublicList*");

            return Ok();
        }
    }
}