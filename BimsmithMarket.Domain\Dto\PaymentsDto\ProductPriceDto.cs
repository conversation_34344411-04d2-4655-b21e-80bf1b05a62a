﻿namespace BIMsmithMarket.Domain.Dto.PaymentsDto
{
    public class ProductPriceDto : ProductPriceCreateDto
    {
        public int? Id { get; set; }
        public decimal CurrentProductPrice { get => GetCurrentPrice(); }
        public PaymentPlanDto PaymentPlan { get; set; }
        public decimal ProductPrice { get; set; }

        private decimal GetCurrentPrice()
        {
            return ProductPrice - ProductPrice * PaymentPlan.PaymentDiscount / 100;
        }
    }

    public class ProductPriceCreateDto
    {
        public int PaymentPlanId { get; set; }
        public int ProductId { get; set; }
    }
}
