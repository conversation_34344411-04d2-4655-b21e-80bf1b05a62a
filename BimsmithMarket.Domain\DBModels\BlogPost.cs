﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{

    [Index(nameof(VanityId), Name = "IX_BlogPost_VanityId")]
    public class BlogPost : BaseEntity
    {
        [StringLength(300)]
        public string VanityId { get; set; }

        public int BlogCategoryId { get; set; }

        public string Title { get; set; }

        public string Descriptions { get; set; }

        public string HtmlBody { get; set; }

        public string ImageUrlBig { get; set; }

        public string ImageUrlSmall { get; set; }

        public string AuthorTitle { get; set; }

        public string AuthorEmail { get; set; }

        public string AuthorImage { get; set; }

        public string Tags { get; set; }

        public string MetaDescription { get; set; }

        public string MetaKeywords { get; set; }

        public string MetaTitle { get; set; }

        public int ViewCount { get; set; }

        public PublishOption PublishOption { get; set; }

        public DateTime? PublishedDate { get; set; }

        public bool IsFeatured { get; set; }

        public int Status { get; set; }
        
        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        public DateTime? SchedulePublishDate { get; set; }

        /// ------------------------------------------
        [ForeignKey("BlogCategoryId")]
        public virtual BlogCategory BlogCategory { get; set; }

        public virtual ICollection<BlogComment> BlogComments { get; set; }

        public virtual ICollection<BlogMentionedEntry> BlogMentionedEntries { get; set; }

        public virtual ICollection<BlogTargetType> TargetTypes { get; set; }

        public BlogPost()
        {
            BlogComments = new List<BlogComment>();
            BlogMentionedEntries = new List<BlogMentionedEntry>();
            TargetTypes = new List<BlogTargetType>();
        }
    }

    public enum PublishOption
    {
        Private = 1,
        Hidden = 2,
        Published = 3,
        Scheduled = 4
    }
}