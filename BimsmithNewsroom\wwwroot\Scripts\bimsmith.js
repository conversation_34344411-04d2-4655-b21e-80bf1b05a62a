/**
 * Makes the top level navigation menu item clickable
 */


(function($){

    //handles top level menu item without children
    $( '.navbar-nav > li.menu-item > a' ).click( function(){
    	if($(this).attr('target') != '_blank' && $(this).attr('class') != 'dropdown-toggle')
          window.location = $( this ).attr( 'href' );
    });

    //handles top level menu item having children
    $( '.navbar-nav > li.menu-item > .dropdown-toggle' ).click( function(){
    	if($(this).attr('target') == '_blank')
          window.open(this.href); // $( this ).attr( 'href' );
        else
          window.location = $( this ).attr( 'href' );
    });

    $('.dropdown').hover(function() {
        $(this).addClass('open');
    },
    function() {
        $(this).removeClass('open');
    });

    var setHeight = function (h) {

	height = h;

	$("#cc_spacer").css("height", height + "px");
	}

	$(window).resize(function(){
		setHeight($("#navigation_menu").height());
	})

	$(window).ready(function(){
		setHeight($("#navigation_menu").height());
	})

    jQuery('.entry-content .pt-cv-href-thumbnail').each(function() {
        jQuery(this).css('background-image', 'url(' + jQuery(this).find('> img').attr('src') + ')').find('> img').hide();
    });
    jQuery('.single-post .post-content .featured-image').each(function() {
        jQuery(this).css('background-image', 'url(' + jQuery(this).find('> img').attr('src') + ')').find('> img').hide();
    });

    /*jQuery('#user-name').click(function(){
        jQuery('.head-drop').css('display', 'block');
    });*/


   $('#user-name').click(function(e){
        e.stopPropagation();
        $('.head-drop').toggleClass('show-menu');
    });
        $('.head-drop').click(function(e){
        e.stopPropagation();
    });
    $('body,html').click(function(e){
       $('.head-drop').removeClass('show-menu');
    });


    document.querySelector('#login-button').href = "https://bimsmith.com/NewMyBIMSmith/login?returnURL=" + window.location.href;
    document.querySelector('#signup-button').href = "https://bimsmith.com/NewMyBIMSmith/register?returnURL=" + window.location.href;
    document.querySelector('#mob-login a').href = "https://bimsmith.com/NewMyBIMSmith/login?returnURL=" + window.location.href;
    document.querySelector('#mob-regis a').href = "https://bimsmith.com/NewMyBIMSmith/register?returnURL=" + window.location.href;

    // authentication logic
    document.querySelector('#logout-button').onclick = function() {
        document.querySelector('#login-button').style.display = 'inline-block';
        document.querySelector('#signup-button').style.display = 'inline-block';

        document.querySelector('#logout-button').style.display = 'none';
        document.querySelector('#user-name').style.display = 'none';

        document.cookie = 'authToken=;expires=Thu, 01 Jan 1970 00:00:01 GMT;domain=.bimsmith.com;';
    };

    const authToken = Cookies.get('authToken');

    if (authToken) {
        jQuery.get("https://bimsmith.com/api/Auth/UserInfo/?authToken=" + authToken )
        .then(
            function (data) {

                document.querySelector('#login-button').style.display = 'none';
                document.querySelector('#signup-button').style.display = 'none';
                document.querySelector('#mob-login').style.display = 'none';
                document.querySelector('#mob-regis').style.display = 'none';
		        document.querySelector('#profile-image').src = data['Image'] ? data['Image'] : '/images/img01.jpg';

                document.querySelector('#logout-button').style.display = 'inline-block';
                document.querySelector('#user-name').style.display = 'inline-block';

                if (data['lastName'].length == 0 && data['firstName'] == 0) {
                    document.querySelector('#user-name, #settings-name').innerHTML='Anonymous User';
                    document.querySelector('#settings-name').innerHTML='Anonymous User';
                }
                else {
                    document.querySelector('#user-name, #settings-name').innerHTML = data['lastName'] + ' ' + data['firstName'];
                    document.querySelector('#settings-name').innerHTML = data['lastName'] + ' ' + data['firstName'];
                }

            },
            function (err) {
                console.log('error');
                //TODO logout show Login log out buttons
                document.cookie = 'authToken=;expires=Thu, 01 Jan 1970 00:00:01 GMT;domain=.bimsmith.com;';

                document.querySelector('#login-button').style.display = 'inline-block';
                document.querySelector('#signup-button').style.display = 'inline-block';
            }
        )
    } else {
        document.cookie = 'authToken=;expires=Thu, 01 Jan 1970 00:00:01 GMT;domain=.bimsmith.com;';
        document.querySelector('#login-button').style.display = 'inline-block';
        document.querySelector('#signup-button').style.display = 'inline-block';
    }

        //<authentication logic

})(jQuery);
