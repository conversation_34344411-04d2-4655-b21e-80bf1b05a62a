﻿using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Dto.DetailDto
{
    public class DetailListDto
    {
        public int Id { get; set; }

        public string Name { get; set; }

        public int? ManufacturerId { get; set; }

        public bool Published { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        public int? PhotoId { get; set; }

        public int? DetailPhotoId { get; set; }

        public int? DetailScaleId { get; set; }

        public DetailListManufacturerDto Manufacturer { get; set; }

        public DetailListPhotoDto Photo { get; set; }

        public DetailListDetailScaleDto DetailScale { get; set; }

        public ICollection<DetailListProjectFileDto> ProjectFiles { get; set; }

        public ICollection<DetailListApplicationDto> Applications { get; set; }
    }

    public class DetailListManufacturerDto
    {
        public int Id { get; set; }

        public string Name { get; set; }

        public string Site { get; set; }
    }

    public class DetailListPhotoDto
    {
        public int? PhotoId { get; set; }

        public string UploadUrl { get; set; }

        public string Small { get; set; }

        public string Middle { get; set; }

        public string Big { get; set; }
    }

    public class DetailListDetailScaleDto
    {
        public int Id { get; set; }

        public string Name { get; set; }
    }

    public class DetailListProjectFileDto
    {
        public int FileId { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        public string SoftwareRelease { get; set; }

        public string ContentCreatedby { get; set; }

        public string ContentCheckedBy { get; set; }

        public string FileVersion { get; set; }

        public DetailListProjectDataTypeDto ProjectDataType { get; set; }

        public int? ProjectDataTypeId { get; set; }

        public string Title { get; set; }

        public string FileName { get; set; }

        public long FileSize { get; set; }

        public string MimeType { get; set; }

        public int UpdatesCount { get; set; }

        public int FileSyncStatusCode { get; set; }

        public string FileSyncUrl { get; set; }

        public string Url { get; set; }

        public string Preview { get; set; }
    }

    public class DetailListProjectDataTypeDto
    {
        public int Id { get; set; }

        public string Title { get; set; }

        public string Header { get; set; }
    }

    public class DetailListApplicationDto
    {
        public int Id { get; set; }

        public string Name { get; set; }
    }

    public class DetailManufacturerPairDto
    {
        public int DetailId { get; set; }

        public int ManufacturerId { get; set; }
    }
}