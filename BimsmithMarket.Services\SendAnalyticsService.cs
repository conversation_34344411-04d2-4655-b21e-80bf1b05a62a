﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto.NewAnalyticsDto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Helpers;
using Flurl;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Services
{
    public class SendAnalyticsService
    {
        private readonly IHttpClientFactory _httpClientFactory;

        public SendAnalyticsService(IHttpClientFactory httpClientFactory)
        {
            _httpClientFactory = httpClientFactory;
        }

        public async Task SendEventsToNewAnalytics(ICollection<EventDto> events)
        {
            if (events != null && events.Any())
            {
                var httpClient = _httpClientFactory.CreateClient();
                {
                    var url = new Url(ConfigurationHelper.GetValue("AnalyticsWriterBaseAddress"))
                        .AppendPathSegment("api/Data/AddEvents");
                    var result = await httpClient.PostAsJsonAsync(url, events);
                    if (!result.IsSuccessStatusCode)
                        LogHelper.LogInfo(ConfigurationHelper.GetValue("DownloadFileLogPath"), $"ERROR on Analytics Event Send Status Code: {result.StatusCode} Url {url} Model: {JsonConvert.SerializeObject(events)}");
                }
            }
        }

        public async Task<ICollection<EventDto>> GenerateProjectTypeNewAnalyticsEvents(int productId, int[] projectDataTypes, string fileIds, HttpRequest httpRequest, IUnitOfWork unitOfWork)
        {
            var downloadStatisticsUsername = ConfigurationHelper.GetValue("DownloadStatisticsUsername");
            var downloadStaticticsUserId = ConfigurationHelper.GetValue("DownloadStatisticsUserId");
            var downloadStatisticsUser = await unitOfWork.UserRepository.GetAll().FirstOrDefaultAsync(x => x.UserName == downloadStatisticsUsername);
            var chosenLanguage = "unknown";
            var languages = httpRequest?.GetTypedHeaders().AcceptLanguage;
            if (languages != null && languages.Any()) chosenLanguage = languages[0].Value.ToString();

            Guid? sessionId = null;
            sessionId = await GetSessionAsync(httpRequest, sessionId);
            if (sessionId == null)
            {
                sessionId = Guid.NewGuid();
                await StartSessionAsync(httpRequest, downloadStaticticsUserId, downloadStatisticsUser, chosenLanguage, sessionId);
            }

            var events = new List<EventDto>();
            var product = await unitOfWork.ProductRepository.GetByIdAsync(productId);
            var manufacturer = await unitOfWork.ManufacturerRepository.GetByIdAsync(product?.ManufacturerId ?? -1);

            foreach (var projectDataType in projectDataTypes)
            {
                var downloadFileEventAction = "Download";
                var projectDataTypeTitle = (await unitOfWork.ProjectDataTypeRepository.GetByIdAsync(projectDataType))?.Title;
                if (!string.IsNullOrWhiteSpace(projectDataTypeTitle)) downloadFileEventAction = "Download_" + projectDataTypeTitle;
                var hostUrl = httpRequest?.Host.ToString();

                var downloadProjectTypeEvent = new EventDto
                {
                    SessionId = sessionId,
                    EventData = new Dictionary<NewAnalyticsEventKeys, string>
                    {
                        { NewAnalyticsEventKeys.ManufacturerId, manufacturer?.Id.ToString() },
                        { NewAnalyticsEventKeys.EventCategory, "Download_File" },
                        { NewAnalyticsEventKeys.EventAction, downloadFileEventAction },
                        { NewAnalyticsEventKeys.EventTitle, product?.Name },
                        { NewAnalyticsEventKeys.EventValue, $"{hostUrl}/api/file/getzip?id={fileIds}&fileName={product?.Name}" },
                        { NewAnalyticsEventKeys.UrlLocation, "External" },
                        { NewAnalyticsEventKeys.PageCode, product?.Category?.Name }
                    }
                };

                var productCategoryProjectTypeEvent = new EventDto
                {
                    SessionId = sessionId,
                    EventData = new Dictionary<NewAnalyticsEventKeys, string>
                    {
                        { NewAnalyticsEventKeys.ManufacturerId, manufacturer?.Id.ToString() },
                        { NewAnalyticsEventKeys.EventCategory, "Product" },
                        { NewAnalyticsEventKeys.EventAction, "Download" },
                        { NewAnalyticsEventKeys.EventTitle, "ProductCategory" },
                        { NewAnalyticsEventKeys.EventValue, product?.Category?.Name  },
                        { NewAnalyticsEventKeys.UrlLocation, "External" },
                    }
                };

                var productLineProjectTypeEvent = new EventDto
                {
                    SessionId = sessionId,
                    EventData = new Dictionary<NewAnalyticsEventKeys, string>
                    {
                        { NewAnalyticsEventKeys.ManufacturerId, manufacturer?.Id.ToString() },
                        { NewAnalyticsEventKeys.EventCategory, "Product" },
                        { NewAnalyticsEventKeys.EventAction, "Download" },
                        { NewAnalyticsEventKeys.EventTitle, "ProductLine" },
                        { NewAnalyticsEventKeys.EventValue, product?.ProductLine?.Name },
                        { NewAnalyticsEventKeys.UrlLocation, "External" }
                    }
                };

                events.Add(downloadProjectTypeEvent);
                events.Add(productCategoryProjectTypeEvent);
                events.Add(productLineProjectTypeEvent);
            }
            return events;
        }

        public async Task<ICollection<EventDto>> GenerateProductTypeNewAnalyticsEvents(int fileId, int productId, int manufacturerId, HttpRequest httpRequest, IUnitOfWork unitOfWork)
        {
            var downloadStatisticsUsername = ConfigurationHelper.GetValue("DownloadStatisticsUsername");
            var downloadStaticticsUserId = ConfigurationHelper.GetValue("DownloadStatisticsUserId");
            var downloadStatisticsUser = await unitOfWork.UserRepository.GetAll().FirstOrDefaultAsync(x => x.UserName == downloadStatisticsUsername);
            var chosenLanguage = "unknown";
            var languages = httpRequest?.GetTypedHeaders().AcceptLanguage;
            if (languages != null && languages.Any()) chosenLanguage = languages[0].Value.ToString();

            Guid? sessionId = null;
            sessionId = await GetSessionAsync(httpRequest, sessionId);
            if (sessionId == null)
            {
                sessionId = Guid.NewGuid();
                await StartSessionAsync(httpRequest, downloadStaticticsUserId, downloadStatisticsUser, chosenLanguage, sessionId);
            }

            var events = new List<EventDto>();
            var file = await unitOfWork.FileRepository.GetByIdAsync(fileId);
            if (file == null)
                throw new DbItemNotFoundException($"file with Id {fileId} not found. (Send analytics event)");

            var product = await unitOfWork.ProductRepository.GetByIdAsync(productId);
            var manufacturer = await unitOfWork.ManufacturerRepository.GetByIdAsync(product?.ManufacturerId ?? -1);

            if (IsAttachmentFile(file, product))
            {
                //select event action
                var downloadFileEventAction = "no action";
                if (file?.SyncUrl != null && file?.SyncUrl.ToLower().IndexOf(AttachmentConstants.MasterspecUrl) != -1)
                {
                    if (file?.Title.ToLower() == "3-part specification") downloadFileEventAction = AttachmentConstants.MasterspecFileTitle;
                    else downloadFileEventAction = "3-Part Specification";
                }
                else
                {
                    if (!string.IsNullOrEmpty(file?.Title)) downloadFileEventAction = file?.Title;
                    else downloadFileEventAction = file?.FileName;
                }

                //set event title
                var downloadFileEventTitle = string.Empty;
                if (file?.SyncUrl != null && file?.SyncUrl.ToLower().IndexOf(AttachmentConstants.MasterspecUrl) != -1)
                    downloadFileEventTitle = "masterspec";
                else if (IsULFile(file, product))
                    downloadFileEventTitle = "ul";
                else if (file?.Title != null && file?.Title.ToLower().IndexOf("hpd") != -1)
                    downloadFileEventTitle = "hpd";
                else downloadFileEventTitle = "default";

                var downloadFileAttachmentEvent = new EventDto
                {
                    SessionId = sessionId,
                    EventData = new Dictionary<NewAnalyticsEventKeys, string>
                    {
                        { NewAnalyticsEventKeys.ManufacturerId, manufacturer?.Id.ToString() },
                        { NewAnalyticsEventKeys.EventCategory, "Download_TechDocFile" },
                        { NewAnalyticsEventKeys.EventAction, downloadFileEventAction },
                        { NewAnalyticsEventKeys.EventTitle, downloadFileEventTitle },
                        { NewAnalyticsEventKeys.EventValue, file?.FileName },
                        { NewAnalyticsEventKeys.UrlLocation, "External" },
                        { NewAnalyticsEventKeys.PageCode, product?.Category?.Name ?? "Unknown" }
                    }
                };

                var productCategoryAttachentEvent = new EventDto
                {
                    SessionId = sessionId,
                    EventData = new Dictionary<NewAnalyticsEventKeys, string>
                    {
                        { NewAnalyticsEventKeys.ManufacturerId, manufacturer?.Id.ToString() },
                        { NewAnalyticsEventKeys.EventCategory, "Product" },
                        { NewAnalyticsEventKeys.EventAction, "Download" },
                        { NewAnalyticsEventKeys.EventTitle, "ProductCategory" },
                        { NewAnalyticsEventKeys.EventValue, product?.Category?.Name ?? "Unknown" },
                        { NewAnalyticsEventKeys.UrlLocation, "External" }
                    }
                };

                var productLineAttachmentEvent = new EventDto
                {
                    SessionId = sessionId,
                    EventData = new Dictionary<NewAnalyticsEventKeys, string>
                    {
                        { NewAnalyticsEventKeys.ManufacturerId, manufacturer?.Id.ToString() },
                        { NewAnalyticsEventKeys.EventCategory, "Product" },
                        { NewAnalyticsEventKeys.EventAction, "Download" },
                        { NewAnalyticsEventKeys.EventTitle, "ProductLine" },
                        { NewAnalyticsEventKeys.EventValue, product?.ProductLine?.Name ?? "Unknown"},
                        { NewAnalyticsEventKeys.UrlLocation, "External" }
                    }
                };
                events.Add(downloadFileAttachmentEvent);
                events.Add(productCategoryAttachentEvent);
                events.Add(productLineAttachmentEvent);
            }
            else
            {
                var downloadFileEventAction = "Download";
                var projectDataType = product?.ProductFiles?.FirstOrDefault(x => x.FileId == file.Id)?.ProjectDataType?.Title;
                if (!string.IsNullOrWhiteSpace(projectDataType)) downloadFileEventAction = "Download_" + projectDataType;
                var hostUrl = httpRequest?.Host.ToString();

                var downloadProjectFileEvent = new EventDto
                {
                    SessionId = sessionId,
                    EventData = new Dictionary<NewAnalyticsEventKeys, string>
                    {
                        { NewAnalyticsEventKeys.ManufacturerId, manufacturer?.Id.ToString() },
                        { NewAnalyticsEventKeys.EventCategory, "Download_File" },
                        { NewAnalyticsEventKeys.EventAction, downloadFileEventAction },
                        { NewAnalyticsEventKeys.EventTitle, product?.Name ?? "Unknown" },
                        { NewAnalyticsEventKeys.EventValue, $"{hostUrl}/api/file/getzip?id={file?.Id}&fileName={file?.FileName}" },
                        { NewAnalyticsEventKeys.UrlLocation, "External" },
                        { NewAnalyticsEventKeys.PageCode, product?.Category?.Name ?? "Unknown"}
                    }
                };

                var productCategoryProjectFileEvent = new EventDto
                {
                    SessionId = sessionId,
                    EventData = new Dictionary<NewAnalyticsEventKeys, string>
                    {
                        { NewAnalyticsEventKeys.ManufacturerId, manufacturer?.Id.ToString() },
                        { NewAnalyticsEventKeys.EventCategory, "Product" },
                        { NewAnalyticsEventKeys.EventAction, "Download" },
                        { NewAnalyticsEventKeys.EventTitle, "ProductCategory" },
                        { NewAnalyticsEventKeys.EventValue, product?.Category?.Name ?? "Unknown"},
                        { NewAnalyticsEventKeys.UrlLocation, "External" }
                    }
                };

                var productLineProjectFileEvent = new EventDto
                {
                    SessionId = sessionId,
                    EventData = new Dictionary<NewAnalyticsEventKeys, string>
                    {
                        { NewAnalyticsEventKeys.ManufacturerId, manufacturer?.Id.ToString() },
                        { NewAnalyticsEventKeys.EventCategory, "Product" },
                        { NewAnalyticsEventKeys.EventAction, "Download" },
                        { NewAnalyticsEventKeys.EventTitle, "ProductLine" },
                        { NewAnalyticsEventKeys.EventValue, product?.ProductLine?.Name ?? "Unknown"},
                        { NewAnalyticsEventKeys.UrlLocation, "External" }
                    }
                };

                events.Add(downloadProjectFileEvent);
                events.Add(productCategoryProjectFileEvent);
                events.Add(productLineProjectFileEvent);
            }
            return events;
        }

        public async Task SendEventToGoogleAnalytics(string uid)
        {
            var httpClient = _httpClientFactory.CreateClient();
            {
                var protocolVersion = "1";
                var googleAnalyticsEndpoint = ConfigurationHelper.GetValue("GoogleAnalyticsApiCollectEndpoint");
                var parameters = new List<KeyValuePair<string, string>>();
                parameters.Add(new KeyValuePair<string, string>("v", protocolVersion));
                parameters.Add(new KeyValuePair<string, string>("tid", ConfigurationHelper.GetValue("GoogleAnalyticsApiResourceId")));
                parameters.Add(new KeyValuePair<string, string>("cid", uid));
                parameters.Add(new KeyValuePair<string, string>("t", "event"));
                parameters.Add(new KeyValuePair<string, string>("ec", "Social"));
                parameters.Add(new KeyValuePair<string, string>("ea", "Download_File"));
                parameters.Add(new KeyValuePair<string, string>("el", "Product"));
                var paramString = RequestHelper.BuildQueryString(parameters);
                var body = RequestHelper.BuildEncodedBody(parameters);
                var result = await httpClient.PostAsync(googleAnalyticsEndpoint, new StringContent(body));
                if (!result.IsSuccessStatusCode) Log.Error("Could not push data to Google Analytics");
            }
        }

        #region private methods
        private string GetIpAddress(HttpRequest httpRequest)
        {
            return httpRequest?.HttpContext?.Connection?.RemoteIpAddress?.ToString();
        }

        private bool IsAttachmentFile(File file, Product product)
        {
            var productFile = product?.ProductFiles?.FirstOrDefault(x => x.FileId == file.Id);
            if (productFile == null)
                return true; //Cratch for file download                             
            return productFile.IsAttachment;
        }

        private bool IsULFile(File file, Product product)
        {
            var productFile = product?.ProductFiles?.FirstOrDefault(x => x.FileId == file?.Id);
            return productFile?.IsULPartnership ?? false;
        }

        private async Task StartSessionAsync(HttpRequest httpRequest, string downloadStaticticsUserId, ApplicationUser downloadStatisticsUser, string chosenLanguage, Guid? sessionId)
        {
            SessionDto sessionDto = new SessionDto
            {
                Id = sessionId,
                Company = "External",
                Ip = GetIpAddress(httpRequest),
                SessionDuration = 0,
                UrlStartLocation = "External",
                UserType = "External",
                Email = downloadStatisticsUser?.Email,
                ProjectId = (int)ProjectType.Market,
                UID = Guid.Parse(downloadStaticticsUserId),
                Cult = chosenLanguage,
                CultName = "unknown"
            };

            try
            {
                sessionDto.CultName = CultureInfo.GetCultureInfo(sessionDto.Cult).EnglishName;
            }
            catch { }

            HttpClient httpClient = _httpClientFactory.CreateClient();
            {
                Url url = new Url(ConfigurationHelper.GetValue("AnalyticsWriterBaseAddress"))
                    .AppendPathSegment("api/Data/StartSession");

                await httpClient.PostAsJsonAsync(url, sessionDto);
            }
        }

        private async Task<Guid?> GetSessionAsync(HttpRequest httpRequest, Guid? sessionId)
        {
            HttpClient httpClient = _httpClientFactory.CreateClient();
            {
                Url url = new Url(ConfigurationHelper.GetValue("AnalyticsWebApiBaseAddress"))
                    .AppendPathSegment("api/Session/GetLastSessionByIp")
                    .SetQueryParam("ip", GetIpAddress(httpRequest))
                    .SetQueryParam("projectType", ProjectType.Market);

                HttpResponseMessage sessionResult = await httpClient.GetAsync(url);
                if (sessionResult.IsSuccessStatusCode)
                    sessionId = JsonConvert.DeserializeObject<Guid>(await sessionResult.Content.ReadAsStringAsync());
            }

            return sessionId;
        }
        #endregion
    }
}