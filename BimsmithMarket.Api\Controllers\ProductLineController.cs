﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.ProductLineExcelDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// Product Line Controller
    /// </summary>
    [Route("api/[controller]/[action]/{id?}")]
    public class ProductLineController : BaseApiController
    {
        private readonly IProductLineService _productLineService;
        private readonly IProductLineExcelService _productLineExcelService;

        public ProductLineController(
            IProductLineService productLineService,
            IProductLineExcelService productLineExcelService
            )
        {
            _productLineService = productLineService;
            _productLineExcelService = productLineExcelService;
        }

        /// <summary>
        /// Get list of all Product Line from database
        /// </summary>
        /// <param name="q">Search query</param>
        /// <param name="manufacturerId">Optional: Filter by manufacturer</param>
        /// <param name="manufacturerName">Optional: Filter by manufacturer</param>
        /// <param name="categoryId">Optional: Filter by category</param>
        /// <param name="onlyWithPublishedProducts">if set to <c>true</c> [only with published products].</param>
        /// <param name="isStaging">if set to <c>true</c> [is staging].</param>
        /// <param name="regionId">The region identifier</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("List")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> List(
            string q = null,
            int manufacturerId = -1,
            string manufacturerName = null,
            int categoryId = -1,
            bool onlyWithPublishedProducts = false,
            bool isStaging = false,
            string regionId = null
        )
        {
            if (regionId == "null")
                regionId = null;

            using IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true);
            return Ok(await _productLineService.ListAsync(unitOfWork, q, manufacturerId, manufacturerName, categoryId, onlyWithPublishedProducts, isStaging, regionId));
        }

        /// <summary>
        /// Get detailed information about Product Line by Id
        /// </summary>
        /// <param name="id">Id of manufacturer</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("Get")]
        public async Task<IActionResult> Get(int id)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _productLineService.GetAsync(id, unitOfWork));
        }

        [HttpPost]
        [ActionName("GetNamesList")]
        public async Task<IActionResult> GetNamesList(EntityIdsDto model)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _productLineService.GetNamesListAsync(model, unitOfWork));
        }

        /// <summary>
        /// Add new Product Line: Role-ADMIN
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Add")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Add([FromBody] AddProductLineModel model)
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            ProductLineAddEditResultDto result = await _productLineService.AddAsync(model, userId, unitOfWork);
            CacheHelper.ClearSpecificCache("*/api/ProductLine/List*");
            return Ok(result);
        }

        /// <summary>
        /// Edit Product Line: Role-ADMIN
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Edit")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Edit([FromBody] EditProductLineModel model)
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            ProductLineAddEditResultDto result = await _productLineService.EditAsync(model, userId, unitOfWork);
            CacheHelper.ClearSpecificCache("*/api/ProductLine/List*");
            CacheHelper.ClearSpecificCache("*/api/Product/AdminList*");
            return Ok(result);
        }

        /// <summary>
        /// Update product line Region Ids
        /// </summary>
        /// <param name="id">Id of ProductLine</param>
        /// <param name="regionIds">Ids of Regions (id1_id2_id3_..._idN) </param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("UpdateRegionIds")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> UpdateRegionIds(int id, string regionIds)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            var result = await _productLineService.UpdateRegionIdsAsync(id, regionIds, userId, unitOfWork);
            CacheHelper.ClearSpecificCache("*/api/ProductLine/List*");
            return Ok(result);
        }

        /// <summary>
        /// Update product line State Ids
        /// </summary>
        /// <param name="id">Id of ProductLine</param>
        /// <param name="stateIds">Ids of states (id1_id2_id3_..._idN) </param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("UpdateStateIds")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> UpdateStateIds(int id, string stateIds)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            var result = await _productLineService.UpdateStateIds(id, stateIds, userId, unitOfWork);
            CacheHelper.ClearSpecificCache("*/api/ProductLine/List*");
            return Ok(result);
        }

        /// <summary>
        /// Delete Product Line: Role-ADMIN
        /// </summary>
        /// <param name="id">Product Line Id</param>
        /// <returns></returns>
        [HttpDelete]
        [ActionName("Delete")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Delete(int id)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            OperationResultDto result = await _productLineService.DeleteAsync(id, userId, unitOfWork);
            CacheHelper.ClearSpecificCache("*/api/ProductLine/List*");
            return Ok(result);
        }

        /// <summary>
        /// Gets the product line product files.
        /// </summary>
        /// <param name="productLineId">The product line identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("GetProductFiles")]
        public async Task<IActionResult> GetProductFiles(int productLineId)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true);
            return Ok(await _productLineService.GetProductFilesAsync(productLineId, unitOfWork));
        }

        /// <summary>
        /// Exports all product lines for manufacturer specified
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier</param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        [ActionName("ExcelExport")]
        public async Task<IActionResult> ExcelExport(int manufacturerId)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                string excelPath = await _productLineExcelService.GetProductLineExcelAsync(manufacturerId, unitOfWork);
                return PhysicalFile(excelPath, "text/csv", $"Product Lines Template.xlsx");
            }
        }

        /// <summary>
        /// Exports all product lines for manufacturer specified
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier</param>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        [ActionName("ExcelImport")]
        public async Task<IActionResult> ExcelImport(int manufacturerId)
        {
            if (!(Request.HasFormContentType && Request.Form.Files.Any()))
                return BadRequest("Mime Multipart Content missing");

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
#if DEBUG
                if (string.IsNullOrWhiteSpace(userId))
                    userId = DbConstants.AdminUserId;
#endif
                ProductLineExcelImportResultDto result = await _productLineExcelService.ImportProductLineExcelAsync(manufacturerId, Request.Form.Files[0], userId, unitOfWork);
                WebApiOutputCacheAttribute.ClearCacheKeysByPattern("*api/ProductLine/List*");
                return Ok(result);
            }
        }
    }
}