﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class ProductLineFilesService : IProductLineFilesService
    {
        public async Task<object> SetWeightAsync(IUnitOfWork unitOfWork, string userId, WeightSetModel model)
        {
            unitOfWork.BeginTransaction();
            var editTime = DateTime.UtcNow;
            //Set ProductLineFiles
            foreach (var item in model.ProductLineFiles)
            {
                var productLineFile = await unitOfWork.ProductLineFileRepository.GetAll().FirstOrDefaultAsync(x => x.FileId == item.id);
                if (productLineFile == null)
                {
                    throw new DbItemNotFoundException("Can't find productLineFile by id {item.id}");
                }
#if !DEBUG
                    productLineFile.ModifiedById = userId;
#endif
                productLineFile.Weight = item.weight;
                productLineFile.ModifiedDate = editTime;
                unitOfWork.ProductLineFileRepository.Edit(productLineFile);
                try
                {
                    await unitOfWork.SaveAsync();
                }
                catch (Exception e)
                {
                    unitOfWork.RollbackTransaction();
                    throw new InvalidInputException(e.Message);
                }
            }


            //set ProductFiles
            foreach (var productFileSet in model.ProductFiles)
            {
                var productFile = await unitOfWork.ProductFileRepository.GetAll().FirstOrDefaultAsync(x => x.FileId == productFileSet.id);
                if (productFile == null)
                {
                    throw new DbItemNotFoundException($"Can't find productLineFile by id {productFileSet.id}");
                }
#if !DEBUG
                    productFile.ModifiedById = userId;
#endif
                productFile.Weight = productFileSet.weight;
                productFile.ModifiedDate = editTime;
                unitOfWork.ProductFileRepository.Edit(productFile);
            }

            try
            {
                await unitOfWork.SaveAsync();
                unitOfWork.CommitTransaction();
            }
            catch (Exception e)
            {
                unitOfWork.RollbackTransaction();
                throw new InvalidInputException(e.Message);
            }

            return $"Weight saved.";
        }

        public class WeightFileSetModel
        {
            public int id { get; set; }
            public int weight { get; set; }
        }

        public class WeightSetModel
        {
            public List<WeightFileSetModel> ProductFiles { get; set; }
            public List<WeightFileSetModel> ProductLineFiles { get; set; }
        }
    }
}
