﻿using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{

    [Index(nameof(Code), Name = "IX_Omniclass_Code")]
    [Index(nameof(Title), Name = "IX_Omniclass_Title")]
    public class Omniclass
    {
        public int Id { get; set; }

        public int? ParentId { get; set; }

        [StringLength(200)]
        public string Code { get; set; }

        [StringLength(200)]
        public string Title { get; set; }

        [ForeignKey("ParentId")]
        public virtual Omniclass Parent { get; set; }

        public virtual ICollection<Omniclass> Children { get; set; }

        public Omniclass()
        {
            Children = new List<Omniclass>();
        }
    }
}