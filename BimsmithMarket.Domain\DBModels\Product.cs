﻿using BIMsmithMarket.Domain.DBModels.PaymentDbModels;
using BIMsmithMarket.Domain.DBModels.RevitProcessing;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{

    [Index(nameof(Name), Name = "IX_Product_Name")]
    public class Product : BaseEntity
    {
        public int CategoryId { get; set; }

        public string Note { get; set; }

        public int ManufacturerId { get; set; }

        public int? ProductLineId { get; set; }

        public int? PhotoId { get; set; }

        public bool Published { get; set; }

        public bool PublishToPartner { get; set; }

        public bool Staging { get; set; }

        public bool HideOnMicrosite { get; set; }

        [StringLength(200)]
        public string Name { get; set; }

        public string Description { get; set; }

        public string ProductUrl { get; set; }

        public string VideoUrl { get; set; }

        public float ProductRating { get; set; }

        public float ContentRating { get; set; }

        public bool IsFeatured { get; set; }

        public string VanityURL { get; set; }

        public string MetaTitle { get; set; }

        public string MetaDescription { get; set; }

        public string MetaKeywords { get; set; }

        public string Keywords { get; set; }

        public float Weight { get; set; }

        public bool IsImperialDefault { get; set; }

        public string ForgeWallURL { get; set; }

        public string ForgeFloorURL { get; set; }

        public string ForgeCeilingURL { get; set; }

        public string ForgeRoofURL { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        public string SwatchboxProductId { get; set; }

        public string SwatchboxOptionId { get; set; }

        public bool DisplaySwatchboxProductOnProductPage { get; set; }

        public bool DisplaySwatchboxProductOnMicrosite { get; set; }

        public int Status { get; set; }

        public ULSyncStatus ULSyncStatus { get; set; }

        public string ULUrl { get; set; }

        public string AssemblyCode { get; set; }

        public string FooterAdUrl { get; set; }

        public int? FooterAdImageId { get; set; }

        public string SketchupId { get; set; }

        public bool PublishedOnCustomMicrosite { get; set; }


        /// <summary>
        /// If product was update from External endpoint
        /// </summary>
        public DateTime? ExternalModifiedDate { get; set; }
        /// ------------------------------------------

        [ForeignKey("CategoryId")]
        public virtual Category Category { get; set; }

        [ForeignKey("ManufacturerId")]
        public virtual Manufacturer Manufacturer { get; set; }

        [ForeignKey("ProductLineId")]
        public virtual ProductLine ProductLine { get; set; }

        [ForeignKey("PhotoId")]
        public virtual Photo Photo { get; set; }

        public string ExternalId { get; set; }

        [ForeignKey("FooterAdImageId")]
        public virtual Photo FooterAdImage { get; set; }

        public virtual Price Price { get; set; }

        public virtual ICollection<ProductPrice> ProductPrices { get; set; }

        public virtual ICollection<ProductPhoto> ProductPhotos { get; set; }

        public virtual ICollection<ProductSample> ProductSamples { get; set; }

        public virtual ICollection<ProductFile> ProductFiles { get; set; }

        public virtual ICollection<RelatedProduct> RelatedProducts { get; set; }

        public virtual ICollection<ProductStats> ProductStats { get; set; }

        public virtual ICollection<ProductCertificate> ProductCertificates { get; set; }

        public virtual ICollection<ProductQualityItem> ProductQualityItems { get; set; }

        public virtual ICollection<ProductMasterformat> ProductMasterformats { get; set; }

        public virtual ICollection<ProductOmniclass> ProductOmniclasses { get; set; }

        public virtual ICollection<ProductUniformat> ProductUniformats { get; set; }

        public virtual ICollection<ProductUniclass> ProductUniclasses { get; set; }

        public virtual ICollection<ProductCategory> ProductCategories { get; set; }

        public virtual ICollection<ProductCisfb> ProductCisfbs { get; set; }

        public virtual ICollection<ProductRating> ProductRatings { get; set; }

        public virtual ICollection<ULCertificate> ULCertificates { get; set; }

        public virtual ICollection<ULRatingSystemSustainableCredit> ULRatingSystemsSustainableCredits { get; set; }

        public virtual ICollection<ULStandardNumber> ULStandardNumbers { get; set; }

        public virtual ICollection<ProductDetail> ProductDetails { get; set; }

        public virtual ICollection<RequestPricingUser> RequestPricingUsers { get; set; }

        public virtual ICollection<UserBIMsmithLTManufacturer> UserBIMsmithLTManufacturers { get; set; }

        public virtual ICollection<RevitProcessProduct> RevitProcessProducts { get; set; }

        public Product()
        {
            ProductPrices = new List<ProductPrice>();
            ProductPhotos = new List<ProductPhoto>();
            ProductSamples = new List<ProductSample>();
            ProductFiles = new List<ProductFile>();
            RelatedProducts = new List<RelatedProduct>();
            ProductStats = new List<ProductStats>();
            ProductCertificates = new List<ProductCertificate>();
            ProductQualityItems = new List<ProductQualityItem>();
            ProductMasterformats = new List<ProductMasterformat>();
            ProductOmniclasses = new List<ProductOmniclass>();
            ProductUniformats = new List<ProductUniformat>();
            ProductUniclasses = new List<ProductUniclass>();
            ProductCategories = new List<ProductCategory>();
            ProductCisfbs = new List<ProductCisfb>();
            ProductRatings = new List<ProductRating>();
            ULCertificates = new List<ULCertificate>();
            ULRatingSystemsSustainableCredits = new List<ULRatingSystemSustainableCredit>();
            ULStandardNumbers = new List<ULStandardNumber>();
            ProductDetails = new List<ProductDetail>();
            RequestPricingUsers = new List<RequestPricingUser>();
            UserBIMsmithLTManufacturers = new List<UserBIMsmithLTManufacturer>();
            RevitProcessProducts = new List<RevitProcessProduct>();
        }
    }

    public enum ULSyncStatus
    {
        Free = 0,
        WaitForSync = 1
    }
}