﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Swashbuckle.AspNetCore.Annotations;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// 
    /// </summary>
    [Route("api/[controller]/[action]/{id?}")]
    public class CategoryController : BaseApiController
    {
        private readonly ICategoryService _categoryService;
        private readonly IFeatureSettingService _featureSettingService;
        private readonly IProductService _productService;
        private readonly IUploadService _uploadService;
        private readonly IWebHostEnvironment _webHostEnvironment;

        public CategoryController(
            IProductService productService,
            ICategoryService categoryService,
            IFeatureSettingService featureSettingService,
            IUploadService uploadService,
            IWebHostEnvironment webHostEnvironment)
        {
            _productService = productService;
            _categoryService = categoryService;
            _featureSettingService = featureSettingService;
            _uploadService = uploadService;
            _webHostEnvironment = webHostEnvironment;
        }

        /// <summary>
        /// Get list of all categories from database
        /// </summary>
        /// <param name="onlyRoot"></param>
        /// <param name="q">Search query</param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        [ActionName("List")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> List(bool onlyRoot, string q = null)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var query = unitOfWork.CategoryRepository.GetAll();

                if (onlyRoot)
                {
                    query = query.Where(a => a.ParentCategoryId == null);
                }

                if (!string.IsNullOrEmpty(q))
                {
                    query = query.Where(e => e.Name.Contains(q));
                }

                var list = await query
                    .Select(a => new
                    {
                        id = a.Id,
                        name = a.Name,
                        fullName = string.Concat(
                            (a.ParentCategoryId != null && a.ParentCategory.ParentCategoryId != null ? a.ParentCategory.ParentCategory.Name + " - " : ""),
                            (a.ParentCategoryId != null ? a.ParentCategory.Name + " - " : ""),
                            a.Name
                        ),
                        isRoot = a.ParentCategoryId == null,
                        iconUrl = a.IconUrl,
                        includeRevitPlugin = a.IncludeRevitPlugin,
                        revitIcon = a.RevitPluginPhotoId == null ? null : new
                        {
                            id = a.RevitPluginPhotoId,
                            smallUrl = a.RevitPluginPhoto.SmallImgUrl,
                            originalUrl = a.RevitPluginPhoto.OriginalImgUrl
                        },
                        parentCategoryId = a.ParentCategoryId,
                        productsCount = a.Products.Count(),
                        updateDate = a.ModifiedDate ?? a.CreatedDate,
                        vanityUrl = a.VanityUrl,
                        bimsmithVanityUrl = a.BimsmithVanityUrl,
                        weight = a.Weight,
                        description = a.Description
                    })
                    .OrderBy(a => a.fullName)
                    .AsNoTracking()
                    .ToListAsync();

                return Ok(list);
            }
        }

        /// <summary>
        /// Get Tree of all categories from database
        /// </summary>
        /// <param name="manufacturerId">Optional: Filter by manufacturer</param>
        /// <param name="manufacturerName">Optional: Filter by manufacturer</param>
        /// <param name="isStaging">if set to <c>true</c> [is staging].</param>
        /// <param name="langCode">The language code</param>
        /// <returns></returns>
        [HttpGet]
#if !DEBUG
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
#endif
        [ActionName("Tree")]
        public async Task<IActionResult> Tree(int manufacturerId = -1, string manufacturerName = null, bool isStaging = false, string langCode = null)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            unitOfWork.CurrentDbContext.ChangeTracker.LazyLoadingEnabled = false;
            return Ok(await _categoryService.TreeAsync(unitOfWork, manufacturerId, manufacturerName, isStaging, langCode));
        }

        /// <summary>
        /// Get detailed information about Category by Id
        /// </summary>
        /// <param name="id">Id of Category</param>
        /// <param name="vanityUrl"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("Get")]
        public async Task<IActionResult> Get(int id = -1, string vanityUrl = null)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var query = unitOfWork.CategoryRepository.GetAll();
                if (id != -1)
                {
                    query = query.Where(c => c.Id == id);
                }
                else
                {
                    query = query.Where(c => c.VanityUrl == vanityUrl);
                }

                var item = await query
                    .Select(a => new
                    {
                        id = a.Id,
                        name = a.Name,
                        isRoot = a.ParentCategoryId == null,
                        iconUrl = a.IconUrl,
                        synonyms = a.Synonyms,
                        keywords = a.Keywords,
                        parentCategoryId = a.ParentCategoryId,
                        updateDate = a.ModifiedDate ?? a.CreatedDate,
                        metaTitle = a.MetaTitle,
                        metaKeywords = a.MetaKeywords,
                        metaDescription = a.MetaDescription,
                        description = a.Description,
                        includeRevitPlugin = a.IncludeRevitPlugin,
                        revitIcon = a.RevitPluginPhotoId == null ? null : new
                        {
                            id = a.RevitPluginPhotoId,
                            smallUrl = a.RevitPluginPhoto.SmallImgUrl,
                            originalUrl = a.RevitPluginPhoto.OriginalImgUrl
                        },
                        vanityUrl = a.VanityUrl,
                        weight = a.Weight,
                        keyStats = a.CategoryKeyStats.OrderBy(f => f.Order).Select(c => new
                        {
                            keyStatId = c.KeyStatId,
                            name = c.KeyStat.Name,
                            note = c.KeyStat.Note,
                            //value = c.KeyStat.DefaultValue
                        }),
                        parentKeyStats = a.ParentCategory.CategoryKeyStats.OrderBy(b => b.Order).Select(d => new
                        {
                            keyStatId = d.KeyStatId,
                            name = d.KeyStat.Name,
                            note = d.KeyStat.Note,
                            //value = c.KeyStat.DefaultValue
                        }),
                        subcategories = a.Subcategories.Select(s => new
                        {
                            id = s.Id,
                            name = s.Name,
                            isRoot = s.ParentCategoryId == null,
                            iconUrl = s.IconUrl,
                            parentCategoryId = s.ParentCategoryId,
                            subcategories = s.Subcategories.Select(ss => new
                            {
                                id = ss.Id,
                                name = ss.Name,
                                isRoot = ss.ParentCategoryId == null,
                                iconUrl = ss.IconUrl,
                                parentCategoryId = ss.ParentCategoryId
                            })
                        }),
                        bimsmithVanityUrl = a.BimsmithVanityUrl
                    })
                    .FirstOrDefaultAsync();

                if (item == null)
                    return NotFound("Not found the Category");

                return Ok(item);
            }
        }

        [HttpPost]
        [ActionName("GetNamesList")]
        public async Task<IActionResult> GetNamesList(EntityIdsDto model)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _categoryService.GetNamesListAsync(model, unitOfWork));
        }

        /// <summary>
        /// Add new Category to Group: Role-ADMIN
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Add")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> Add([FromBody] AddCategoryModel model)
        {
            if (!await _featureSettingService.GetSettingStatusAsync(DbConstants.FeatureAddCategoryViaAdminPanel))
                return BadRequest("The feature is disabled");

            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            unitOfWork.BeginTransaction();
            var result = await _categoryService.AddAsync(model, _webHostEnvironment.WebRootPath, userId, unitOfWork);
            unitOfWork.CommitTransaction();

            CacheHelper.ClearSpecificCache("*api/Category/List*");
            CacheHelper.ClearSpecificCache("*api/Category/Tree*");

            return Ok(result);
        }

        /// <summary>
        /// Edit Category: Role-ADMIN
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Edit")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> Edit([FromBody] EditCategoryModel model)
        {
            if (!await _featureSettingService.GetSettingStatusAsync(DbConstants.FeatureUpdateCategoryViaAdminPanel))
                return BadRequest("The feature is disabled");

            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            unitOfWork.BeginTransaction();
            var result = await _categoryService.EditAsync(model, _webHostEnvironment.WebRootPath, userId, unitOfWork);
            unitOfWork.CommitTransaction();

            CacheHelper.ClearSpecificCache("*api/Category/List*");
            CacheHelper.ClearSpecificCache("*api/Category/Tree*");

            return Ok(result);
        }

        /// <summary>
        /// Get info for delete
        /// </summary>
        /// <param name="id">Id of Category</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("GetDeleteInfo")]
        public async Task<IActionResult> GetDeleteInfo(int id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var allCategoryIds = new List<int>();
                var category = await unitOfWork.CategoryRepository.GetByIdAsync(id);
                allCategoryIds.Add(category.Id);
                var subcategoryIds = category.Subcategories.Select(a => a.Id);
                allCategoryIds.AddRange(subcategoryIds);

                var countOfProducts = await unitOfWork.ProductRepository.GetAll().CountAsync(a => allCategoryIds.Contains(a.CategoryId));
                var countOfSubcategories = subcategoryIds.Count();

                var result = new
                {
                    countOfProducts = countOfProducts,
                    countOfSubcategories = countOfSubcategories
                };

                return Ok(result);
            }
        }

        /// <summary>
        ///  Delete Category and subcategory and all products from the category: Role-ADMIN
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpDelete]
        [ActionName("Delete")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> Delete([FromBody] DeleteCategoryModel model)
        {
            if (!await _featureSettingService.GetSettingStatusAsync(DbConstants.FeatureDeleteCategoryViaAdminPanel))
            {
                return BadRequest("The feature is disabled");
            }

            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            IUnitOfWork unitOfWork = UnitOfWork.Create();
            OperationResultDto result = await _categoryService.DeleteAsync(model, userId, _productService, unitOfWork);

            CacheHelper.ClearSpecificCache("*api/Category/List*");
            CacheHelper.ClearSpecificCache("*api/Category/Tree*");

            return Ok(result);
        }

        /// <summary>
        /// Get list of category icon urls
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("CategoryIcons")]
        public IActionResult CategoryIcons()
        {
            string folderImages = Path.Combine(_webHostEnvironment.WebRootPath, "assets/img/categoryIcons");

            var list = Directory.GetFiles(folderImages).Select(a => "/assets/img/categoryIcons/" + Path.GetFileName(a));

            return Ok(list);
        }

        /// <summary>
        /// Upload Category icon
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ActionName("UploadIcon")]
        [SwaggerOperation("FileUpload")]
        public async Task<IActionResult> UploadIcon()
        {
            if (!(Request.HasFormContentType && Request.Form.Files.Any()))
                return BadRequest("Mime Multipart Content missing");

            var iconUrl = await _uploadService.SaveIcoToBlobAsync(Request.Form.Files[0]);
            if (string.IsNullOrEmpty(iconUrl))
            {
                return BadRequest("Something wrong ");
            }

            return Ok(new { iconUrl });
        }


        /// <summary>
        /// Gets mappings for category vanity url and bimsmith vanity url
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("GetBimsmithVanityUrlPairs")]
        public async Task<IActionResult> GetBimsmithVanityUrlPairs()
        {
            var result = await _categoryService.GetBimsmithVanityUrlPairs();
            return Ok(result);
        }

        /// <summary>
        /// Gets category vanity url by bimsmith vanity url. If it does not exist return empty string
        /// </summary>
        /// <param name="bimsmithUrl">BIMsmithMarket vanity url</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("GetVanityUrlByBimsmithUrl")]
        public async Task<IActionResult> GetVanityUrlByBimsmithUrl(string bimsmithUrl)
        {
            return Ok(await _categoryService.GetVanityUrlByBimsmithUrl(bimsmithUrl));
        }

        /// <summary>
        /// Exports all categories
        /// </summary>
        /// <returns></returns>
        [HttpGet]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> ExcelExport()
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            string excelPath = await _categoryService.ExcelExportAsync(unitOfWork);
            return PhysicalFile(excelPath, "text/csv", $"Categories Template.xlsx");
        }

        /// <summary>
        /// Imports all categories
        /// </summary>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpPost]
        [ActionName("ExcelImport")]
        public async Task<IActionResult> ExcelImport()
        {
            if (!(Request.HasFormContentType && Request.Form.Files.Any()))
                return BadRequest("Mime Multipart Content missing");

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
#if DEBUG
                if (string.IsNullOrWhiteSpace(userId))
                    userId = DbConstants.AdminUserId;
#endif
                CategoryExcelImportResultDto result = await _categoryService.ImportExcelAsync(Request.Form.Files[0], userId, unitOfWork);
                CacheHelper.ClearSpecificCache("*api/Category/List*");
                CacheHelper.ClearSpecificCache("*api/Category/Tree*");
                return Ok(result);
            }
        }
    }
}