﻿using System;

namespace BIMsmithMarket.Domain.Dto
{
    public class ProductGetFileDto
    {
        public int Id { get; set; }

        public string CustomFileId { get; set; }

        public string Title { get; set; }

        public string FileName { get; set; }

        public long FileSize { get; set; }

        public string MimeType { get; set; }

        public string Url { get; set; }

        public string FileSyncUrl { get; set; }

        public int FileSyncStatusCode { get; set; }

        public int UpdatesCount { get; set; }

        public string Preview { get; set; }

        public DateTime ModifiedDate { get; set; }
    }

    public class ProductGetFileWithRegionDto : ProductGetFileWithWeightDto
    {
        public string RegionIds { get; set; }

        public string StateIds { get; set; }
    }

    public class ProductGetFileWithWeightDto : ProductGetFileDto
    {
        public int Weight { get; set; }
    }
}
