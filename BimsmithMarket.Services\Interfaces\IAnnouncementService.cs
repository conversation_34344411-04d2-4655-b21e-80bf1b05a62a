﻿using BIMsmithMarket.Domain.Dto.AnnouncementDto;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IAnnouncementService
    {
        Task<GetAnnouncementDto> AddAsync(AddAnnouncementDto model, string userId);

        Task<GetAnnouncementDto> EditAsync(EditAnnouncementDto model, string userId);

        Task<GetAnnouncementDto> GetAsync(int id);

        Task<GetAnnouncementDto[]> ListAsync(int count, int offset, bool? isActive, string regionId = null, string stateId = null);

        Task DeleteAsync(int id);
    }
}