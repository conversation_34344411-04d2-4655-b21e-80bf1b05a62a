﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;

using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Services
{
    public class FeatureSettingService : IFeatureSettingService
    {
        public async Task<bool> GetSettingStatusAsync(string name)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var setting = await unitOfWork.FeatureSettingRepository.GetAll().FirstOrDefaultAsync(x => x.Name.ToUpper() == name.ToUpper());
                if (setting == null)
                    return false;
                else
                    return setting.Status;
            }
        }

        public async Task<FeatureSetting> SetStatus(FeatureSettingSetStatusModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var setting = unitOfWork.FeatureSettingRepository.GetById(model.FeatureId);
                if (setting == null) throw new Exception("Setting not found");
                setting.Status = model.Status;
                await unitOfWork.SaveAsync();

                CacheHelper.ClearSpecificCache("*/api/FeatureSetting/List*");
                CacheHelper.ClearSpecificCache("*/api/FeatureSetting/PublicList*");

                return setting;
            }
        }

        public async Task<List<FeatureSettingListDto>> ListAsync(FeatureSettingTarget? target = null)
        {
            using (var unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                IQueryable<FeatureSetting> query = unitOfWork.FeatureSettingRepository.GetAllAsNoTracking();

                if (target.HasValue)
                    query = query.Where(x => x.Target == target);

                List<FeatureSettingListDto> list = await query.Select(x => new FeatureSettingListDto
                {
                    Id = x.Id,
                    Name = x.Name,
                    Status = x.Status
                })
                .ToListAsync();

                return list;
            }
        }
    }
}