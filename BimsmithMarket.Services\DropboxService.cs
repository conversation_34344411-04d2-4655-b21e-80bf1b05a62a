﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto.DropboxDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using Dropbox.Api;
using Dropbox.Api.Check;
using Dropbox.Api.Files;
using Dropbox.Api.Sharing;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class DropboxService : IDropboxService
    {
        public async Task UpdateDropboxFilesAsync(UpdateDropboxFilesDto model, IUnitOfWork unitOfWork)
        {
            if (!model.ProductId.HasValue && !model.ManufacturerId.HasValue)
                throw new InvalidInputException("Please specify product or manufacturer identifier");

            IQueryable<ProductFile> productFileQuery = unitOfWork.ProductFileRepository.GetAllAsNoTracking()
                .Where(x => !(x.File.SyncUrl == null || x.File.SyncUrl == string.Empty) && x.File.SyncUrl.Contains("dropbox.com"));

            if (model.ProductId.HasValue)
                productFileQuery = productFileQuery.Where(x => x.ProductId == model.ProductId);

            if (model.ManufacturerId.HasValue)
                productFileQuery = productFileQuery.Where(x => x.Product.ManufacturerId == model.ManufacturerId);

            List<int> fileIds = await productFileQuery.Select(x => x.FileId).Distinct().ToListAsync();
            if (!fileIds.Any())
                return;

            int take = 100;
            for (int i = 0; i < fileIds.Count / take + 1; i++)
            {
                List<int> fileIdsPart = fileIds.Skip(i * take).Take(take).ToList();
                List<Domain.DBModels.File> files = await unitOfWork.FileRepository.GetAll().Where(x => fileIdsPart.Contains(x.Id)).ToListAsync();
                foreach (Domain.DBModels.File file in files)
                {
                    file.NextSyncDateTime = DateTime.UtcNow.AddDays(-1);
                    file.UpdatesCount = 0;
                    file.SyncStatus = SyncFileStatus.Free;
                    unitOfWork.FileRepository.Edit(file);
                }
                await unitOfWork.SaveAsync();
            }
        }

        public async Task<Stream> ExportDropboxLinksAsync(string dropboxPath, string userId, IUnitOfWork unitOfWork)
        {
            if (string.IsNullOrWhiteSpace(dropboxPath))
                throw new Exception("Path can not be empty");

            string accessToken = await GetAccessTokenAsync(userId, unitOfWork);
            List<DropboxFileDto> dropboxFiles = await GetDropboxFilesAsync(dropboxPath, accessToken);

            //excel file
            Stream excelStream = new MemoryStream();
            SpreadsheetDocument spreadsheetDocument = SpreadsheetDocument.Create(excelStream, SpreadsheetDocumentType.Workbook);
            WorkbookPart workbookpart = spreadsheetDocument.AddWorkbookPart();
            workbookpart.Workbook = new Workbook();
            WorksheetPart worksheetPart = workbookpart.AddNewPart<WorksheetPart>();
            worksheetPart.Worksheet = new Worksheet();
            Fonts fonts = new Fonts(
                new Font(
                    new FontSize() { Val = 10 }
                ));
            Fills fills = new Fills(
                    new Fill(new PatternFill() { PatternType = PatternValues.None }),
                    new Fill(new PatternFill(new ForegroundColor { Rgb = new HexBinaryValue() { Value = "FFE7E6E6" } }) { PatternType = PatternValues.Solid }), // Index 1 - default
                    new Fill(new PatternFill(new ForegroundColor { Rgb = new HexBinaryValue() { Value = "FFBDD7EE" } }) { PatternType = PatternValues.Solid }) // Index 2 - header
                );
            Borders borders = new Borders(
                    new Border()
                );
            CellFormats cellFormats = new CellFormats(
                    new CellFormat(),
                    new CellFormat { FontId = 0, FillId = 1, BorderId = 0, ApplyFill = true },
                    new CellFormat { FontId = 0, FillId = 2, BorderId = 0, ApplyFill = true },
                    new CellFormat { FontId = 0, FillId = 2, BorderId = 0, ApplyFill = true, Alignment = new Alignment { TextRotation = 90, WrapText = true } } // header for certificate
                );
            Stylesheet styleSheet = new Stylesheet(fonts, fills, borders, cellFormats);
            WorkbookStylesPart stylePart = workbookpart.AddNewPart<WorkbookStylesPart>();
            stylePart.Stylesheet = styleSheet;
            stylePart.Stylesheet.Save();

            //columns
            Columns columns = new Columns();

            uint headerColumnIndex = 1;
            int columnsCount = 3;
            for (int i = 0; i < columnsCount; i++)
                columns.AppendChild(new Column { Min = headerColumnIndex, Max = headerColumnIndex++, Width = 25, CustomWidth = true });

            worksheetPart.Worksheet.AppendChild(columns);
            workbookpart.Workbook.Save();

            //Sheets
            Sheets sheets = workbookpart.Workbook.AppendChild(new Sheets());

            //Append a new worksheet and associate it with the workbook.
            Sheet sheet = new Sheet()
            {
                Id = spreadsheetDocument.WorkbookPart.
                GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = "Dropbox links"
            };
            sheets.Append(sheet);
            workbookpart.Workbook.Save();

            // Get the sheetData cell table.
            SheetData sheetData = worksheetPart.Worksheet.AppendChild(new SheetData());

            int rowIndex = 1;
            Row headerRow = new Row();
            sheetData.AppendChild(headerRow);
            int columnIndex = 1;

            List<string> headerColumnCaptions = new List<string>
            {
                "File Name",
                "File Path",
                "File Link"
            };
            foreach (string caption in headerColumnCaptions)
                headerRow.AppendChild(CommonExcelProvider.ConstructCell(caption, CellValues.String, columnIndex++, rowIndex, 2));

            rowIndex++;
            foreach (DropboxFileDto dropboxFile in dropboxFiles)
            {
                Row row = new Row();
                sheetData.AppendChild(row);

                columnIndex = 1;

                row.AppendChild(CommonExcelProvider.ConstructCell(dropboxFile.Name, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(dropboxFile.Path, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(dropboxFile.Url, CellValues.String, columnIndex++, rowIndex));

                rowIndex++;
            }

            workbookpart.Workbook.Save();

            spreadsheetDocument.Dispose();

            excelStream.Position = 0;
            return excelStream;
        }

        public async Task CheckAuthorizationAsync(string userId, IUnitOfWork unitOfWork)
        {
            string accessToken = await GetAccessTokenAsync(userId, unitOfWork);
            DropboxClient dropboxClient = new DropboxClient(accessToken);
            await dropboxClient.Check.UserAsync();
        }

        public async Task CompleteOAuthAsync(DropboxOAuthResponseDto model, string userId, IUnitOfWork unitOfWork)
        {
            DropboxSetting dropboxSetting = await unitOfWork.DropboxSettingRepository.GetAll()
                .FirstOrDefaultAsync(x => x.CreatedById == userId);

            if (dropboxSetting == null)
            {
                dropboxSetting = model.Adapt<DropboxSetting>();
                dropboxSetting.CreatedById = userId;
                unitOfWork.DropboxSettingRepository.Insert(dropboxSetting);
            }
            else
            {
                model.Adapt(dropboxSetting);
                dropboxSetting.ModifiedById = userId;
                dropboxSetting.ModifiedDate = DateTime.UtcNow;
                unitOfWork.DropboxSettingRepository.Edit(dropboxSetting);
            }

            await unitOfWork.SaveAsync();
        }

        #region
        public async Task<List<DropboxFileDto>> GetDropboxFilesAsync(string dropboxFolder, string dropboxAccessToken)
        {
            DropboxClient dropboxClient = new DropboxClient(dropboxAccessToken);
            List<DropboxFileDto> dropboxFiles = new List<DropboxFileDto>();

            ListFolderResult listFolderResult = await dropboxClient.Files.ListFolderAsync(dropboxFolder);
            dropboxFiles.AddRange(GetFilesFromListFolderResult(listFolderResult));

            while (listFolderResult.HasMore)
            {
                listFolderResult = await dropboxClient.Files.ListFolderContinueAsync(listFolderResult.Cursor);
                dropboxFiles.AddRange(GetFilesFromListFolderResult(listFolderResult));
            }

            foreach (DropboxFileDto dropboxFile in dropboxFiles)
            {
                ListSharedLinksResult listSharedLinksResult = await dropboxClient.Sharing.ListSharedLinksAsync(dropboxFile.Path);
                dropboxFile.Url = listSharedLinksResult.Links.FirstOrDefault(x => x.IsFile)?.Url;

                if (string.IsNullOrWhiteSpace(dropboxFile.Url))
                {
                    SharedLinkMetadata sharedLinkMetadata = await dropboxClient.Sharing.CreateSharedLinkWithSettingsAsync(dropboxFile.Path);
                    dropboxFile.Url = sharedLinkMetadata.Url;
                }

                dropboxFile.Url = dropboxFile.Url.Replace("dl=0", "dl=1");
            }

            return dropboxFiles;
        }

        private IEnumerable<DropboxFileDto> GetFilesFromListFolderResult(ListFolderResult listFolderResult)
        {
            return listFolderResult.Entries
                .Where(x => x.IsFile)
                .Select(x => new DropboxFileDto
                {
                    Name = x.Name,
                    Path = x.PathLower
                });
        }

        private async Task<string> GetAccessTokenAsync(string userId, IUnitOfWork unitOfWork)
        {
            DropboxSetting dropboxSetting = await unitOfWork.DropboxSettingRepository.GetAll()
                .FirstOrDefaultAsync(x => x.CreatedById == userId);

            if (dropboxSetting == null)
                throw new InvalidInputException("User does not have Dropbox settings");

            return dropboxSetting?.AccessToken;
        }
        #endregion
    }
}