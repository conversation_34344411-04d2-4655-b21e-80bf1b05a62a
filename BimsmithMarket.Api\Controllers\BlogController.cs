using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// Newsroom administration controller
    /// </summary>
    [Route("api/[controller]/[action]/{id?}")]
    public class BlogController : BaseApiController
    {
        private readonly IBlogService _blogService;
        private readonly IWebHostEnvironment _webHostEnvironment;

        public BlogController(
            IBlogService blogService,
            IWebHostEnvironment webHostEnvironment)
        {
            _blogService = blogService;
            _webHostEnvironment = webHostEnvironment;
        }

        /// <summary>
        /// Get piblic list of blog posts
        /// </summary>
        /// <param name="categoryId"></param>
        /// <param name="q"></param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <param name="isFeaturedFirst"></param>
        /// <param name="targetType"> string parameter that include target type, that shoud be include to result</param>
        /// <param name="author"></param>
        /// <param name="vanityCategoryid"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("PublicList")]
        public async Task<IActionResult> PublicListAsync(int categoryId = -1,
            string q = null,
            int offset = 0,
            int count = 10,
            bool isFeaturedFirst = false,
            TargetBlogType targetType = TargetBlogType.All,
            string author = null,
            string vanityCategoryid = null)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _blogService.PublicListAsync(unitOfWork, categoryId, q, offset, count, isFeaturedFirst, targetType, author, vanityCategoryid, HttpContext?.Connection?.RemoteIpAddress?.ToString()));
            }
        }

        /// <summary>
        /// Refresh Authors Photo
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        [ActionName("RefreshAuthorsPhoto")]
        public async Task<IActionResult> RefreshAuthorsPhotos()
        {
            int count = 0;
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var emails = await unitOfWork.BlogPostRepository.GetAll()
                                       .Where(x => !string.IsNullOrEmpty(x.AuthorEmail) &&
                                                    x.AuthorEmail.Contains("@"))
                                       .Select(x => x.AuthorEmail).Distinct().ToListAsync();

                if (emails.Any())
                {
                    unitOfWork.BeginTransaction();
                    foreach (var email in emails)
                    {
                        var img = await _blogService.GetUserPhoto(email);

                        if (!string.IsNullOrEmpty(img))
                        {
                            List<BlogPost> posts = await unitOfWork.BlogPostRepository.GetAll()
                                                             .Where(x => x.AuthorEmail == email)
                                                             .Distinct().ToListAsync();
                            foreach (var post in posts)
                            {
                                post.AuthorImage = img ?? "";
                                unitOfWork.BlogPostRepository.Edit(post);
                                count++;
                            }
                        }
                    }
                    await unitOfWork.SaveAsync();
                    unitOfWork.CommitTransaction();
                }
            }

            CacheHelper.ClearSpecificCache("*/api/AttachmentOrder/List*");

            return Ok(new
            {
                updated = count
            });
        }

        [AllowAnonymous]
        [HttpPost]
        [ActionName("UpdateAuthorPhoto")]
        public async Task<IActionResult> UpdateAuthorPhoto(UpdateAuthorPhotoModel model)
        {
            if (model.Token != ConfigurationHelper.GetValue("SwatchboxMarketAccessKey"))
                return NotFound();

            using (var unitOfWork = UnitOfWork.Create())
            {
                await _blogService.UpdateAuthorPhotoAsync(unitOfWork, model);
                return Ok();
            }
        }

        /// <summary>
        /// Get blog information by id
        /// </summary>
        /// <param name="id"></param>
        /// <param name="vanityId"></param>
        /// <param name="targetType"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("PublicGet")]
        public async Task<IActionResult> PublicGetAsync(int id = -1, string vanityId = null, TargetBlogType targetType = TargetBlogType.All)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var query = unitOfWork.BlogPostRepository.GetAll();

                if (id != -1)
                {
                    query = query.Where(c => c.Id == id);
                }
                else
                {
                    query = query.Where(c => c.VanityId == vanityId);
                }

                if (targetType != TargetBlogType.All)
                {
                    query = query.Where(x => x.TargetTypes.Any(a => a.TargetBlogType == targetType));
                }

                var email = await query.Select(x => x.AuthorEmail).FirstOrDefaultAsync();

                var item = await query
                    .Select(a => new
                    {
                        id = a.Id,
                        vanityId = a.VanityId,
                        title = a.Title,
                        category = new
                        {
                            id = a.BlogCategory.Id,
                            name = a.BlogCategory.Name,
                            vanityId = a.BlogCategory.VanityId
                        },
                        authorTitle = a.AuthorTitle,
                        authorImage = a.AuthorImage,
                        htmlBody = a.HtmlBody,
                        publishedDate = a.PublishedDate,
                        imageUrlBig = a.ImageUrlBig,
                        imageUrlSmall = a.ImageUrlSmall,
                        metaTitle = a.MetaTitle,
                        metaDescription = a.MetaDescription,
                        metaKeywords = a.MetaKeywords,
                        blogTags = a.Tags,
                        blogComments = a.BlogComments.Where(x => x.Targettype == TargetBlogType.SwatchBox).Select(c => new
                        {
                            id = c.Id,
                            ownerName = c.OwnerName,
                            comment = c.Comment,
                            date = c.CreatedDate
                        })
                    })
                    .FirstOrDefaultAsync();

                if (item == null)
                {
                    return NotFound("Not found the Blog post");
                }

                return Ok(item);
            }
        }

        /// <summary>
        /// Get list of all blog publications
        /// </summary>
        /// <param name="q"></param>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("List")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> List(string q = null, int offset = 0, int count = 10)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _blogService.ListAsync(unitOfWork, q, offset, count));
            }
        }

        /// <summary>
        /// Get detailed blog post 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="vanityId"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("Get")]
#if !DEBUG
    [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Get(int id = -1, string vanityId = null)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _blogService.GetAsync(unitOfWork, id, vanityId));
            }
        }

        /// <summary>
        /// Create new blog post
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Add")]
#if !DEBUG
    [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Add([FromBody] AddBlogPostModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var result = await _blogService.AddAsync(unitOfWork, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), model);

                CacheHelper.ClearSpecificCache("*/api/Blog/List*");

                return Ok(result);
            }
        }

        /// <summary>
        /// Create new news
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Edit")]
#if !DEBUG
    [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Edit([FromBody] EditBlogPostModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var result = await _blogService.EditAsync(unitOfWork, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), model);

                CacheHelper.ClearSpecificCache("*/api/Blog/List*");
                CacheHelper.ClearSpecificCache("*/api/Blog/PublicList*");

                return Ok(result);
            }
        }
        
        /// <summary>
        /// Edits the blog region ids.
        /// </summary>
        /// <param name="model">The model.</param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("EditBlogRegionIds")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> EditBlogRegionIds([FromBody] EditBlog model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var blog = unitOfWork.BlogPostRepository.GetAll().SingleOrDefault(b => b.Id == model.BlogId);
                if (blog == null)
                    return NotFound("Not found the Blog");

                blog.RegionIds = model.RegionIds;
                blog.StateIds = model.StateIds;
                unitOfWork.BlogPostRepository.Edit(blog);
                await unitOfWork.SaveAsync();
                
                CacheHelper.ClearSpecificCache("*/api/Blog/List*");
                CacheHelper.ClearSpecificCache("*/api/Blog/PublicList*");
                
                return Ok();
            }
        }

        /// <summary>
        /// delete news
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [ActionName("Delete")]
#if !DEBUG
    [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> Delete(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                await _blogService.DeleteAsync(unitOfWork, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), id);

                CacheHelper.ClearSpecificCache("*/api/Blog/List*");

                return Ok();
            }
        }

        /// <summary>
        /// Get all available categories
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("/api/blog/category/list")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> Categories()
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                var items = await _blogService.CategoriesAsync(unitOfWork);

                CacheHelper.ClearSpecificCache("*/api/Blog/PublicList*");
                CacheHelper.ClearSpecificCache("*/api/blog/category/list*");

                return Ok(items);
            }
        }

        /// <summary>
        /// Add new blog category
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("/api/blog/category/add")]
#if !DEBUG
    [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> AddCategory([FromBody] AddBlogCategoryModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var result = await _blogService.AddCategoryAsync(unitOfWork, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), model);

                CacheHelper.ClearSpecificCache("*/api/Blog/PublicList*");
                CacheHelper.ClearSpecificCache("*/api/blog/category/list*");

                return Ok(result);
            }
        }

        /// <summary>
        /// Edit blog category items
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("/api/blog/category/edit")]
#if !DEBUG
    [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> EditCategory([FromBody] EditBlogCategoryModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var result = await _blogService.EditCategoryAsync(unitOfWork, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), model);

                CacheHelper.ClearSpecificCache("*/api/Blog/PublicList*");
                CacheHelper.ClearSpecificCache("*/api/blog/category/list*");

                return Ok(result);
            }
        }

        /// <summary>
        /// Commentses the list.
        /// </summary>
        /// <param name="blogPostId">The blog post identifier.</param>
        /// <param name="q">The q.</param>
        /// <param name="offset">The offset.</param>
        /// <param name="count">The count.</param>
        /// <returns></returns>
        [HttpGet]
        [Route("/api/blog/comment/list")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> CommentsList(int? blogPostId = null, string q = null, int offset = 0, int count = 10)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _blogService.CommentsListAsync(unitOfWork, blogPostId, q, offset, count));
            }
        }

        /// <summary>
        /// Adds the swatchbox comment.
        /// </summary>
        /// <param name="model">The model.</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost]
        public async Task<IActionResult> AddSwatchboxComment(AddSwatchboxBlogCommentModel model)
        {
            string AngularisSiteAccessToken = ConfigurationHelper.GetValue("AngularisSiteAccessToken");
            if (model.AngularisToken != AngularisSiteAccessToken)
            {
                return BadRequest("Invalid Angularis access token");
            }

            if (!await IsReCaptchValidAsync(model.CaptchaResponse))
            {
                return BadRequest("Invalid captcha response");
            }

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var emailsPath = Path.Combine(_webHostEnvironment.WebRootPath, "Emails");
                await _blogService.AddSwatchboxComment(unitOfWork, emailsPath, model);
            }

            CacheHelper.ClearSpecificCache("*/api/blog/comment/list*");

            return Ok("Comment was add");
        }


        /// <summary>
        /// Deletes the comment.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [Route("/api/blog/comment/delete")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> DeleteComment(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                await _blogService.DeleteCommentAsync(unitOfWork, id);

                CacheHelper.ClearSpecificCache("*/api/blog/comment/list*");

                return Ok();
            }
        }

        /// <summary>
        /// Gets meta info for specified blog
        /// </summary>
        /// <param name="vanityId">The vanity identifier</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        [ActionName("GetNewsMetaInfo")]
        public async Task<IActionResult> GetNewsMetaInfo(string vanityId)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _blogService.GetNewsMetaInfoAsync(vanityId, unitOfWork));
            }
        }

        private async Task<bool> IsReCaptchValidAsync(string captchaResponse)
        {
            var result = false;
            var secretKey = ConfigurationHelper.GetValue("GoogleRecaptchaSecretKey");
            var apiUrl = ConfigurationHelper.GetValue("GoogleRecaptchaApiUrl") + "?secret={0}&response={1}";
            var requestUri = string.Format(apiUrl, secretKey, captchaResponse);
            var request = (HttpWebRequest)WebRequest.Create(requestUri);

            using (WebResponse response = await request.GetResponseAsync())
            {
                result = await _blogService.IsReCaptchValidAsync(response);
            }

            return result;
        }
    }
}