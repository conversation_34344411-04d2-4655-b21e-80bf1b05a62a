﻿using System;
using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Dto
{
    public class ProductLineDto
    {
        public int? Id { get; set; }

        public string Name { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        public IEnumerable<int?> ExternalCertificates { get; set; }

        public IEnumerable<QualityItemDto> QualityItems { get; set; }

        public virtual IEnumerable<ProjectGetFileWithTypeDto> ProjectFiles { get; set; }

        public virtual IEnumerable<ProductGetFileDto> ProductFiles { get; set; }
    }

    public class ProductLineAddEditResultDto
    {
        public int Id { get; set; }

        public string Name { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        public string Description { get; set; }
    }

    public class ProductLineGetDto
    {
        public int Id { get; set; }

        public string Name { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        public int? ManifacturerId { get; set; }

        public string ManufacturerName { get; set; }

        public string Note { get; set; }

        public string Description { get; set; }

        public IEnumerable<int?> ExternalCertificates { get; set; }

        public IEnumerable<string> ForgeProductLineIds { get; set; }

        public IEnumerable<ProductLineGetQualityItemDto> QualityItems { get; set; }

        public IEnumerable<ProductLineGetProductFileDto> ProductFiles { get; set; }

        public IEnumerable<ProductLineGetProductLineFileDto> ProjectFiles { get; set; }

        public DateTime UpdateDate { get; set; }
    }

    public class ProductLineGetQualityItemDto
    {
        public int Id { get; set; }

        public string Name { get; set; }

        public string IconUrl { get; set; }
    }

    public class ProductLineGetProductFileDto
    {
        public int Id { get; set; }

        public string CustomFileId { get; set; }

        public string Title { get; set; }

        public string FileName { get; set; }

        public long FileSize { get; set; }

        public string MimeType { get; set; }

        public int UpdatesCount { get; set; }

        public int FileSyncStatusCode { get; set; }

        public string FileSyncUrl { get; set; }

        public string Url { get; set; }

        public string Preview { get; set; }

        public int Weight { get; set; }
    }

    public class ProductLineGetProductLineFileDto
    {
        public int Id { get; set; }

        public string CustomFileId { get; set; }

        public string SoftwareRelease { get; set; }

        public string ContentCreatedby { get; set; }

        public string ContentCheckedBy { get; set; }

        public string FileVersion { get; set; }

        public ProductLineGetProjectTypeDto ProjectType { get; set; }

        public string Title { get; set; }

        public string FileName { get; set; }

        public long FileSize { get; set; }

        public string MimeType { get; set; }

        public int UpdatesCount { get; set; }

        public int FileSyncStatusCode { get; set; }

        public string FileSyncUrl { get; set; }

        public string Url { get; set; }

        public string Preview { get; set; }
    }

    public class ProductLineGetProjectTypeDto
    {
        public int? Id { get; set; }

        public string Title { get; set; }

        public string Header { get; set; }
    }
}