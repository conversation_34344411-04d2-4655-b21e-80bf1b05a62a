﻿using System.Collections.Generic;

namespace BIMsmithMarket.Domain.Models.ExcelModels.DynamicExcel
{
    public class DynamicExcelProjectFileModel
    {
        public string ProjectDataTypeTitle { get; set; }

        public string SyncUrl { get; set; }
    }

    public class DynamicExcelProjectFileModelComparer : IEqualityComparer<DynamicExcelProjectFileModel>
    {
        public bool Equals(DynamicExcelProjectFileModel x, DynamicExcelProjectFileModel y)
        {
            return x.ProjectDataTypeTitle.ToUpperInvariant() == y.ProjectDataTypeTitle.ToUpperInvariant()
                && x.SyncUrl.ToUpperInvariant() == y.SyncUrl.ToUpperInvariant();
        }

        public int GetHashCode(DynamicExcelProjectFileModel obj)
        {
            return obj.ProjectDataTypeTitle.GetHashCode() + obj.SyncUrl.GetHashCode();
        }
    }
}