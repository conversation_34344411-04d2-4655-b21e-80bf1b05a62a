﻿using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.HealthDashboardDto;
using BIMsmithMarket.Domain.Enums;
using Microsoft.AspNetCore.Http;
using System.Threading;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IHealthDashboardExcelService
    {
        Task<string> GetBrokenLinksExcelAsync(HealthDashboardFilterDto model, CancellationToken cancellationToken = default);

        Task<string> GetDashboardExcelAsync(HealthDashboardFilterDto model, CancellationToken cancellationToken = default);

        Task<ExcelImportResultDto> RepairBrokenLinksAsync(IFormFile formFile, CancellationToken cancellationToken = default);

        Task<ExcelImportResultDto> RepairProductsAsync(IFormFile formFile, HealthDashboardType dashboardType, string userId, CancellationToken cancellationToken = default);
    }
}