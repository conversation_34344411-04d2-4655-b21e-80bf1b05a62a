﻿using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using Dapper;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;

namespace BIMsmithMarket.Services.Search.FullTextSearch
{
    public class GenericEntity
    {
        public int Id;
        public string Name;
        public string Synonyms;

        public GenericEntity(Category c)
        {
            Id = c.Id;
            Name = c.Name;
            Synonyms = c.Synonyms;
        }

        public GenericEntity(Manufacturer m)
        {
            Id = m.Id;
            Name = m.Name;
            Synonyms = m.Synonyms;
        }

        public GenericEntity(int id, string name, string synonyms)
        {
            Id = id;
            Name = name;
            Synonyms = synonyms;
        }
    }

    public class MultilingualDictionary<TKey, TValue>
    {
        public Dictionary<string, Dictionary<TKey, TValue>> Languages { get; set; }

        public MultilingualDictionary()
        {
            Languages = new Dictionary<string, Dictionary<TKey, TValue>>();
        }

        public MultilingualDictionary(IEnumerable<(string, Dictionary<TKey, TValue>)> languages)
        {
            Languages = languages.ToDictionary(l => l.Item1, l => l.Item2);
        }

        public bool Includes(string languageCode)
        {
            return Languages.TryGetValue(languageCode, out var language) && language.Any();
        }

        public void Add(string languageCode, Dictionary<TKey, TValue> language)
        {
            Languages[languageCode] = language;
        }

        public Dictionary<TKey, TValue> Get(string languageCode)
        {
            return Languages[languageCode];
        }
    }

    public class SearchCache
    {
        public const string DefaultLanguage = "";
        public static readonly int MaxAgeInMinutes = 60;

        private static volatile object _sync = new object();
        private static SearchCache _searchCache;
        private static DateTime? _lastRefresh;

        private static readonly SortedSet<string> _noiseWords = new SortedSet<string> { "and" };
        private static readonly SortedSet<char> _irregularPlurals = new SortedSet<char> { 'z', 's', 'h', 'x' };
        private static readonly SortedSet<char> _vowels = new SortedSet<char> { 'a', 'i', 'u', 'e', 'o' };
        private static readonly int _maxVariantLength = 15;
        private static readonly MasterformatService _masterformatService = new MasterformatService(new CacheService());
        private static readonly CertificateService _certificateService = new CertificateService(new CacheService());

        public SortedSet<string> HyphenedPhrases { get; set; }
        public Dictionary<string, string> ConjoinedPhrases { get; set; }
        public MultilingualDictionary<string, MatchableTokens> ManufacturerTokens { get; set; }
        public MultilingualDictionary<string, MatchableTokens> ManufacturerSynonymTokens { get; set; }
        public MultilingualDictionary<string, MatchableTokens> CategoryNameTokens { get; set; }
        public MultilingualDictionary<string, MatchableTokens> CategorySynonymTokens { get; set; }
        public Dictionary<string, MatchableTokens> DetailTokens { get; set; }
        public Dictionary<string, int> CategoriesByVanityUrl { get; set; }
        public Dictionary<int, string> CategoryNames { get; set; }
        public Dictionary<string, SortedSet<int>> CategoryIds { get; set; }
        public Dictionary<int, string> ManufacturersToForgeId { get; set; }
        public Dictionary<int, List<int>> ChildCategories { get; set; }
        public Dictionary<int, Category> Categories { get; set; }
        public Dictionary<string, int> ProjectTypes { get; set; }
        public List<KeyStatUnit> KeyStatUnits { get; set; }
        public Dictionary<int, Manufacturer> Manufacturers { get; set; }
        public Dictionary<string, HashSet<int>> DetailApplications { get; set; }
        public List<Cisfb> Cisfbs { get; set; }
        public List<int> ExternalMasterformatIds { get; set; }
        public List<MasterformatModel> ExternalMasterformats { get; set; }
        public List<CertificateModel> ExternalCertificates { get; set; }
        public List<Omniclass> Omniclasses { get; set; }
        public List<Uniclass> Uniclasses { get; set; }
        public List<Uniformat> Uniformats { get; set; }
        public List<SearchCacheQualityItem> QualityItems { get; set; }
        public Dictionary<string, string> Settings { get; set; }
        public Dictionary<string, List<Synonym>> Synonyms { get; set; }
        public Dictionary<int, int> ManufacturerOrder { get; set; }
        public Dictionary<int, int> CategoryOrder { get; set; }

        public List<SearchCacheProductCisfb> ProductCisfbs { get; set; }

        public List<SearchCacheProductOmniclass> ProductOmniclasses { get; set; }

        public List<SearchCacheProductUniclass> ProductUniclasses { get; set; }

        public List<SearchCacheProductUniformat> ProductUniformats { get; set; }

        public List<SearchCacheProductCertificate> ProductCertificates { get; set; }

        public List<SearchCacheProductMasterformat> ProductMasterformats { get; set; }

        public List<SearchCacheProductQualityItem> ProductQualityItems { get; set; }

        public List<int> ProductSwatchboxEnabledIds { get; set; }

        public List<int> ProductMasterspecEnabledIds { get; set; }

        public List<SearchCacheProductProjectDataType> ProductProjectDataTypes { get; set; }

        public WeightsTable WeightsTable { get; set; }

        public Dictionary<int, float> TryMatchWithConfidence(List<QueryToken> queryTokens, MultilingualDictionary<string, MatchableTokens> matchableTokens, string language, QueryTokenType tokenType, int totalQueryTokens)
        {
            if (language != DefaultLanguage && matchableTokens.Includes(language))
            {
                // try specified language first
                var languageResults = TryMatchWithConfidence(queryTokens, matchableTokens.Get(language), tokenType, totalQueryTokens);
                if (languageResults.Any())
                    return languageResults;
            }

            // search english if no results
            return TryMatchWithConfidence(queryTokens, matchableTokens.Get(DefaultLanguage), tokenType, totalQueryTokens);
        }

        // try to prefix-match tokens with varying match confidence
        public Dictionary<int, float> TryMatchWithConfidence(List<QueryToken> queryTokens, Dictionary<string, MatchableTokens> matchableTokens, QueryTokenType tokenType, int totalQueryTokens)
        {
            var matches = new Dictionary<int, float>();
            try
            {
                for (int i = 0; i < queryTokens.Count; i++)
                {
                    var token = queryTokens[i].Original.Replace("-", "");
                    var corrected = queryTokens[i].Corrected.Replace("-", "");

                    // if prefix matches
                    MatchableTokens matchableToken;
                    if (matchableTokens.TryGetValue(token, out matchableToken) || matchableTokens.TryGetValue(corrected, out matchableToken))
                    {
                        // calculate match % on following tokens
                        queryTokens[i].Type = tokenType;
                        if (!matchableTokens.ContainsKey(corrected))
                        {
                            // ignore autocorrection if we matched the original
                            queryTokens[i].Corrected = queryTokens[i].Original;
                        }
                        SearchResults.Merge(matches, matchableToken.MatchWithConfidence(queryTokens.Skip(i + 1), tokenType, totalQueryTokens));
                    }
                }
            }
            catch (Exception e)
            {
                Trace.TraceError("Error looking up tokens for {0}: {1}", tokenType, e.Message);
                throw;
            }
            return matches;
        }

        public Dictionary<int, float> TryMatchExact(List<QueryToken> queryTokens, MultilingualDictionary<string, MatchableTokens> matchableTokens, string language, QueryTokenType tokenType, int totalQueryTokens, SearchResults results)
        {
            if (language != DefaultLanguage && matchableTokens.Includes(language))
            {
                // try specified language first
                var languageResults = TryMatchExact(queryTokens, matchableTokens.Get(language), tokenType, totalQueryTokens, results);
                if (languageResults.Any())
                    return languageResults;
            }

            // search english if no results
            return TryMatchExact(queryTokens, matchableTokens.Get(DefaultLanguage), tokenType, totalQueryTokens, results);
        }

        // try to exact match and return longest match
        public Dictionary<int, float> TryMatchExact(List<QueryToken> queryTokens, Dictionary<string, MatchableTokens> matchableTokens, QueryTokenType tokenType, int totalQueryTokens, SearchResults results)
        {
            var matches = new Dictionary<int, QueryToken[]>();
            try
            {
                for (int i = 0; i < queryTokens.Count; i++)
                {
                    var token = queryTokens[i].Original.Replace("-", "");
                    var corrected = queryTokens[i].Corrected.Replace("-", "");

                    // if prefix matches
                    MatchableTokens matchableToken;
                    if (matchableTokens.TryGetValue(token, out matchableToken) || matchableTokens.TryGetValue(corrected, out matchableToken))
                    {
                        // calculate match lengths
                        if (!matchableTokens.ContainsKey(corrected))
                        {
                            // ignore autocorrection if we matched the original
                            queryTokens[i].Corrected = queryTokens[i].Original;
                        }
                        var matchedItems = matchableToken.MatchExact(queryTokens.Skip(i + 1), tokenType, results);
                        foreach (var mi in matchedItems)
                        {
                            var matchedTokens = new QueryToken[] { queryTokens[i] }.Concat(mi.Value).ToArray(); // add prefix token
                            if (!matches.TryGetValue(mi.Key, out var existingMatch))
                            {
                                matches.Add(mi.Key, matchedTokens);
                            }
                            else if (existingMatch.Length < matchedTokens.Length)
                            {
                                matchedItems[mi.Key] = matchedTokens;
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Trace.TraceError("Error looking up tokens for {0}: {1}", tokenType, e.Message);
                throw;
            }
            if (!matches.Any())
            {
                return new Dictionary<int, float>();
            }
            var longestMatch = matches.Values.Select(v => v.Length).Max();
            var longestMatches = matches.Where(m => m.Value.Length == longestMatch).ToList();
            foreach (var lm in longestMatches)
            {
                // mark tokens belonging to longest match
                foreach (var qt in lm.Value)
                {
                    qt.Type = tokenType;
                    qt.Matches.Add(lm.Key);
                }
            }
            // if we matched all tokens in query, weight is +0.1f (will bee-line), otherwise -0.1f (not beeline but strong match)
            return longestMatches.ToDictionary(m => m.Key, m => m.Value.Length == totalQueryTokens ? WeightsTable.CategoryBeelineThreshold + 0.1f : WeightsTable.CategoryBeelineThreshold - 0.1f);
        }

        // split input word into matchable tokens by alternating spaces, dots, hyphens etc.
        protected static List<string> GenerateVariants(string input, bool includePrefix, bool includeWords)
        {
            var variants = new List<string>();
            try
            {
                if (!string.IsNullOrWhiteSpace(input))
                {
                    // check for camel case ("ClarkDietrich") and separate by spaces and hyphens
                    input = input.Trim();
                    if (input.Any(c => char.IsUpper(c)) && input.Any(c => char.IsLower(c)))
                    {
                        var hasCamelCase = false;
                        var spacedInput = new List<char> { input[0] };
                        var hyphenedInput = new List<char> { input[0] };
                        for (int i = 1; i < input.Length; i++)
                        {
                            if (char.IsUpper(input[i]) && char.IsLower(input[i - 1]))
                            {
                                spacedInput.Add(' ');
                                hyphenedInput.Add('-');
                                hasCamelCase = true;
                            }
                            spacedInput.Add(input[i]);
                            hyphenedInput.Add(input[i]);
                        }
                        if (hasCamelCase)
                        {
                            variants.AddRange(GenerateVariants(new string(spacedInput.ToArray()).ToLower(), includePrefix, includeWords));
                            variants.AddRange(GenerateVariants(new string(hyphenedInput.ToArray()).ToLower(), includePrefix, includeWords));
                        }
                    }

                    if (input.Any(c => IsSymbolOrPunct(c)))
                    {
                        // strip (R), (TM) etc.
                        variants.AddRange(GenerateVariants(new string(input.Where(c => !IsSymbolOrPunct(c)).ToArray()), includePrefix, includeWords));
                    }

                    input = input.ToLower();
                    variants.Add(input);
                    input = input.Trim('.', ',');
                    variants.Add(input);
                    for (int i = 0; i < 2; i++)
                    {
                        if (input.EndsWith(" inc") && input.Length > 5)
                        {
                            input = input.Substring(0, input.Length - 4).Trim(' ', '.', ',');
                        }
                        if (input.EndsWith(" llc") && input.Length > 5)
                        {
                            input = input.Substring(0, input.Length - 4).Trim(' ', '.', ',');
                        }
                        if (input.EndsWith(" co") && input.Length > 5)
                        {
                            input = input.Substring(0, input.Length - 3).Trim(' ', '.', ',');
                        }
                    }

                    // check for initials
                    try
                    {
                        if (input.Contains('.') || input.Contains(' '))
                        {
                            var tokens = input.Split(new char[] { ' ', '.' }, StringSplitOptions.RemoveEmptyEntries);
                            variants.Add(string.Join(" ", tokens));

                            var initialsTokens = tokens.Where(t => t.Length == 1 && char.IsLetter(t[0]));
                            if (initialsTokens.Any())
                            {
                                var dottedInput = string.Join(" ", tokens.Select(t => initialsTokens.Contains(t) ? t + "." : t));
                                variants.Add(dottedInput);

                                if (initialsTokens.Count() > 1)
                                {
                                    // if they are consecutive, merge them (W R Meadows -> WR Meadows & W.R. Meadows)
                                    var mergedInput = new List<string>();
                                    var mergedDottedInput = new List<string>();
                                    for (int i = 0; i < tokens.Length; i++)
                                    {
                                        if (i < tokens.Length - 1 && initialsTokens.Contains(tokens[i]) && initialsTokens.Contains(tokens[i + 1]))
                                        {
                                            mergedInput.Add(tokens[i] + tokens[i + 1]);
                                            mergedDottedInput.Add(tokens[i] + "." + tokens[i + 1] + ".");
                                            i++;
                                        }
                                        else
                                        {
                                            mergedInput.Add(tokens[i]);
                                            mergedDottedInput.Add(tokens[i]);
                                        }
                                    }

                                    variants.Add(string.Join(" ", mergedInput));
                                    variants.Add(string.Join(" ", mergedDottedInput));
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Trace.TraceError("Error deriving initials cache: " + ex.Message);
                    }

                    variants.Add(input.Replace(" ", ""));
                    variants.Add(input.Replace(".", " ").Replace("  ", " ").Trim());
                    variants.Add(input.Replace(",", " ").Replace("  ", " ").Trim());
                    variants.Add(input.Replace("-", " ").Replace("  ", " ").Trim());
                    variants.Add(input.Replace(".", " ").Replace(" ", ""));
                    variants.Add(input.Replace(",", " ").Replace(" ", ""));
                    variants.Add(input.Replace("-", " ").Replace(" ", ""));

                    var individualTokens = input.Split(' ').Where(t => !string.IsNullOrWhiteSpace(t)).Select(t => t.Trim()).ToList();
                    if (individualTokens.Any())
                    {
                        variants.Add(string.Join(" ", individualTokens));
                        if (includePrefix && individualTokens.First().Length > 1)
                        {
                            variants.Add(individualTokens.First());
                        }
                    }
                }
                if (includeWords)
                {
                    var singleWords = variants.Where(s => s.Contains(' ')).SelectMany(s => s.Split(' ')).Where(s => s.Length > 1 && !_noiseWords.Contains(s)).ToList();
                    variants.AddRange(singleWords.SelectMany(w => GenerateVariants(new string(w.Where(c => !IsSymbolOrPunct(c)).ToArray()), false, false)));
                    variants.AddRange(singleWords.Select(w => Alternate(w)).Where(w => w != null));

                    var tripleWords = variants.Where(s => s.Count(c => c == ' ') == 2).ToList();
                    foreach (var tripleWord in tripleWords)
                    {
                        var threeTokens = tripleWord.Split(' ').Where(w => !string.IsNullOrWhiteSpace(w)).Select(w => new string(w.Where(c => !IsSymbolOrPunct(c)).ToArray())).ToArray();
                        if (threeTokens.Length == 3)
                        {
                            variants.Add(threeTokens[0] + " " + threeTokens[1]);
                            if (Alternate(threeTokens[1]) != null)
                            {
                                variants.Add(threeTokens[0] + " " + Alternate(threeTokens[1]));
                            }
                            variants.Add(threeTokens[1] + " " + threeTokens[2]);
                            variants.Add(threeTokens[0] + " " + threeTokens[2]);
                            if (Alternate(threeTokens[2]) != null)
                            {
                                variants.Add(threeTokens[1] + " " + Alternate(threeTokens[2]));
                                variants.Add(threeTokens[0] + " " + Alternate(threeTokens[2]));
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Trace.TraceError("Error generating variants: {0}", e.Message);
                throw;
            }
            return variants;
        }

        /// <summary>
        /// Ensure the cache will be reloaded on next query
        /// </summary>
        public static void Invalidate()
        {
            lock (_sync)
            {
                _searchCache = null;
            }
        }

        public static bool IsVowel(char c)
        {
            return _vowels.Contains(c);
        }

        public static string Plural(string s)
        {
            if (s.Length > 2 && s.EndsWith("y"))
            {
                if (IsVowel(s[s.Length - 2]))
                {
                    return s + "s";
                }
                return s.Substring(0, s.Length - 1) + "ies";
            }
            else if (s.Length > 2 && s.EndsWith("o"))
            {
                return s + "es";
            }
            else if (s.Length > 2 && !s.EndsWith("s") && char.IsLetter(s[s.Length - 1]))
            {
                return s + (_irregularPlurals.Contains(s[s.Length - 1]) ? "es" : "s");
            }
            return null;
        }

        public static string Singular(string s)
        {
            if (s.EndsWith("ies"))
            {
                return s.Substring(0, s.Length - 3) + "y";
            }
            else if (s.Length > 3 && s.EndsWith("es") && _irregularPlurals.Contains(s[s.Length - 3]))
            {
                return s.Substring(0, s.Length - 2);
            }
            else if (s.Length > 2 && s.EndsWith("s") && !s.EndsWith("ss"))
            {
                return s.Substring(0, s.Length - 1);
            }
            return null;
        }

        public static string Alternate(string s)
        {
            return Singular(s) ?? Plural(s);
        }

        public static void AddFixedSynonyms(string s, ref HashSet<string> synonyms)
        {
            if (s.Contains("lighting"))
            {
                synonyms.Add(s.Replace("lighting", "light"));
                synonyms.Add(s.Replace("lighting", "lights"));
            }
            if (s.Contains("flooring"))
            {
                synonyms.Add(s.Replace("flooring", "floor"));
                synonyms.Add(s.Replace("flooring", "floors"));
            }
            if (s.Contains("coatings"))
            {
                synonyms.Add(s.Replace("coatings", "coat"));
                synonyms.Add(s.Replace("coatings", "coats"));
            }
            if (s.Contains("railings"))
            {
                synonyms.Add(s.Replace("railings", "rail"));
                synonyms.Add(s.Replace("railings", "rails"));
            }
        }

        public bool IsStopWord(string word)
        {
            return word != null && Synonyms.TryGetValue(word.ToLower(), out var synonyms) && synonyms.Any(s => s.Alternate == "STOP");
        }

        protected static Dictionary<string, List<int>> GenerateCategoryVariants(IEnumerable<GenericEntity> categories, bool includePrefix, bool includeWords)
        {
            var categoryVariants = new Dictionary<string, List<int>>();
            foreach (var c in categories)
            {
                try
                {
                    var categoryName = c.Name.ToLower();
                    var synonyms = new HashSet<string> { categoryName };

                    // separate out parentheses and slashes
                    if (categoryName.Contains('(') && categoryName.Contains(')'))
                    {
                        var tokens = categoryName.Split('(', ')');
                        synonyms.UnionWith(tokens.Select(t => t.Trim()));
                    }
                    if (categoryName.Contains('/') || categoryName.Contains('&'))
                    {
                        var tokens = categoryName.Split('/', '&');
                        synonyms.UnionWith(tokens.Select(t => t.Trim()));
                    }
                    if (categoryName.Contains(" and "))
                    {
                        var tokens = categoryName.Split(new string[] { " and " }, StringSplitOptions.RemoveEmptyEntries);
                        synonyms.UnionWith(tokens.Select(t => t.Trim()));
                    }

                    // add singular forms and common alternate forms
                    foreach (var s in synonyms.ToArray())
                    {
                        var plural = Alternate(s);
                        if (plural != null)
                        {
                            synonyms.Add(plural);
                        }
                        AddFixedSynonyms(s, ref synonyms);
                    }

                    foreach (var synonym in synonyms)
                    {
                        foreach (var v in GenerateVariants(synonym, includePrefix, includeWords))
                        {
                            if (!string.IsNullOrWhiteSpace(v) && v.Length > 1)
                            {
                                if (!categoryVariants.ContainsKey(v))
                                {
                                    categoryVariants.Add(v, new List<int> { c.Id });
                                }
                                else if (!categoryVariants[v].Contains(c.Id))
                                {
                                    categoryVariants[v].Add(c.Id);
                                }
                            }
                        }
                    }
                }
                catch (Exception)
                {
                    // TODO: log
                }
            }
            return categoryVariants;
        }

        protected static Dictionary<string, List<int>> GenerateManufacturerVariants(IEnumerable<GenericEntity> manufacturers, bool includePrefix, bool includeWords)
        {
            var manufacturerVariants = new Dictionary<string, List<int>>();
            foreach (var m in manufacturers)
            {
                try
                {
                    foreach (var v in GenerateVariants(m.Name, includePrefix, includeWords))
                    {
                        if (!string.IsNullOrWhiteSpace(v) && v.Length > 1)
                        {
                            if (!manufacturerVariants.ContainsKey(v))
                            {
                                manufacturerVariants.Add(v, new List<int> { m.Id });
                            }
                            else if (!manufacturerVariants[v].Contains(m.Id))
                            {
                                manufacturerVariants[v].Add(m.Id);
                            }
                        }
                    }
                }
                catch (Exception)
                {
                    // TODO: log
                }
            }
            return manufacturerVariants;
        }

        protected static Dictionary<string, List<int>> GenerateDetailVariants(IEnumerable<Detail> details)
        {
            var detailVariants = new Dictionary<string, List<int>>();
            foreach (var m in details)
            {
                try
                {
                    var variants = new HashSet<string>();
                    var name = m.Name.ToLower();
                    variants.Add(name);
                    if (name.Contains(' '))
                    {
                        variants.Add(name.Substring(0, name.IndexOf(' ')));
                    }
                    // add all prefixes with and without dashes, only up until first space
                    foreach (var v in variants.ToList())
                    {
                        var code = v;
                        if (code.Contains(' '))
                        {
                            code = code.Substring(0, code.IndexOf(' '));
                        }
                        if (code.Contains('-'))
                        {
                            var tokens = code.Split('-');
                            for (int i = 1; i <= tokens.Length; i++)
                            {
                                variants.Add(string.Join("-", tokens.Take(i)));
                                variants.Add(string.Join("", tokens.Take(i)));
                            }
                        }
                    }

                    foreach (var v in variants)
                    {
                        if (!string.IsNullOrWhiteSpace(v) && v.Length > 1)
                        {
                            if (!detailVariants.ContainsKey(v))
                            {
                                detailVariants.Add(v, new List<int> { m.Id });
                            }
                            else if (!detailVariants[v].Contains(m.Id))
                            {
                                detailVariants[v].Add(m.Id);
                            }
                        }
                    }
                }
                catch (Exception)
                {
                    // TODO: log
                }
            }
            return detailVariants;
        }

        // synonyms are applied on top of automatically generated variants, and can be negative (-door) to remove an auto-generated variant, or positive (to add)
        private static Dictionary<string, List<int>> OverlaySynonyms<T>(Dictionary<string, List<int>> variants, IEnumerable<T> items, Func<T, int> idSelector, Func<T, IEnumerable<string>> synonymSelector)
        {
            try
            {
                foreach (var i in items)
                {
                    foreach (var sn in synonymSelector(i).Where(s => !string.IsNullOrWhiteSpace(s)).SelectMany(s => s.Split(',').Where(t => !string.IsNullOrWhiteSpace(t)).Select(t => t.Trim().ToLower())))
                    {
                        var id = idSelector(i);
                        if (sn.StartsWith("-"))
                        {
                            // negative synonym - remove from variants list
                            var sns = sn.Substring(1).Trim();
                            {
                                if (variants.TryGetValue(sns, out var iSyn))
                                {
                                    iSyn.RemoveAll(x => x == id);
                                }
                            }

                            // also alternate form (singular/plural/fixed)
                            var alternates = new HashSet<string> { Alternate(sns) };
                            AddFixedSynonyms(sn, ref alternates);
                            foreach (var alt in alternates)
                            {
                                if (alt != null && alt != sns)
                                {
                                    if (variants.TryGetValue(alt, out var iSyn))
                                    {
                                        iSyn.RemoveAll(x => x == id);
                                    }
                                }
                            }
                        }
                        else
                        {
                            // positive synonym - add
                            {
                                if (!variants.TryGetValue(sn, out var iSyn))
                                {
                                    iSyn = new List<int>();
                                    variants.Add(sn, iSyn);
                                }
                                iSyn.Add(id);
                            }

                            // also alternate form (singular/plural/fixed)
                            var alternates = new HashSet<string> { Alternate(sn) };
                            AddFixedSynonyms(sn, ref alternates);
                            foreach (var alt in alternates)
                            {
                                if (alt != null && alt != sn)
                                {
                                    if (!variants.TryGetValue(alt, out var iSyn))
                                    {
                                        iSyn = new List<int>();
                                        variants.Add(alt, iSyn);
                                    }
                                    iSyn.Add(id);
                                }
                            }
                        }
                    }
                }
                foreach (var tooLongVariant in variants.Keys.Where(v => !v.Contains(' ') && v.Length > _maxVariantLength).ToList())
                {
                    variants.Remove(tooLongVariant);
                }
            }
            catch (Exception)
            {
                // TODO: log
            }
            return variants;
        }

        private static Dictionary<string, List<int>> GetCategoriesBySynonym(IEnumerable<GenericEntity> categories)
        {
            try
            {
                return OverlaySynonyms(GenerateCategoryVariants(categories, false, true), categories.Where(c => c.Name != null), c => c.Id, c => new string[] { c.Name.ToLower(), c.Synonyms });
            }
            catch (Exception)
            {
                // TODO: log
                return new Dictionary<string, List<int>>();
            }
        }

        private static Dictionary<string, List<int>> GetManufacturersBySynonym(IEnumerable<GenericEntity> manufacturers)
        {
            try
            {
                return OverlaySynonyms(GenerateManufacturerVariants(manufacturers, true, false), manufacturers.Where(m => m.Name != null), m => m.Id, m => new string[] { m.Name.ToLower(), m.Synonyms });
            }
            catch (Exception)
            {
                // TODO: log
                return new Dictionary<string, List<int>>();
            }
        }

        private static Dictionary<string, HashSet<int>> GetDetailApplications(IUnitOfWork unitOfWork)
        {
            try
            {
                var applications =
                unitOfWork.DetailDetailApplicationRepository.GetAll()
                                                            .AsNoTracking()
                                                            .Select(d => new { Name = d.DetailApplication.Name.ToLower(), d.DetailId }).ToList().GroupBy(d => d.Name).ToDictionary(d => d.Key, d => new HashSet<int>(d.Select(x => x.DetailId)));

                foreach (var brackets in applications.Keys.Where(a => a.Contains('(')).ToList())
                {
                    // duplicate applications that have alt name in brackets
                    foreach (var token in brackets.Split(new char[] { '(', ')' }).Where(t => !string.IsNullOrWhiteSpace(t)))
                    {
                        if (!applications.ContainsKey(token))
                        {
                            applications.Add(token, applications[brackets]);
                        }
                    }
                }

                return applications;
            }
            catch (Exception)
            {
                return new Dictionary<string, HashSet<int>>();
            }
        }

        private static void AddCategoryTranslations(SearchCache searchCache, IUnitOfWork unitOfWork)
        {
            var categoryTranslations = unitOfWork.DynamicTranslationRepository.GetAll().Where(t => (t.TranslatableEntityField.FieldName == "Name" || t.TranslatableEntityField.FieldName == "Synonyms")
            && t.TranslatableEntityField.TranslatableEntity.TypeName == "Categories" && t.Value != null && t.Value != "")
                .Select(t => new { t.EntityId, t.TranslatableEntityField.FieldName, t.Value, t.LanguageCode }).ToList()
                .GroupBy(t => t.LanguageCode)
                .ToDictionary(l => l.Key, l =>
                    l.GroupBy(t => t.EntityId).Select(t => new GenericEntity(t.Key, t.FirstOrDefault(c => c.FieldName == "Name")?.Value ?? string.Empty, t.FirstOrDefault(c => c.FieldName == "Synonyms")?.Value ?? string.Empty)));

            foreach (var language in categoryTranslations)
            {
                searchCache.CategoryNameTokens.Add(language.Key, MatchableTokens.SplitIntoMatchableTokens(GenerateCategoryVariants(language.Value, false, false)));
                searchCache.CategorySynonymTokens.Add(language.Key, MatchableTokens.SplitIntoMatchableTokens(GetCategoriesBySynonym(language.Value)));
            }
        }

        private static void AddManufacturerTranslations(SearchCache searchCache, IUnitOfWork unitOfWork)
        {
            var manufacturerTranslations = unitOfWork.DynamicTranslationRepository.GetAll().Where(t => (t.TranslatableEntityField.FieldName == "Name" || t.TranslatableEntityField.FieldName == "Synonyms")
            && t.TranslatableEntityField.TranslatableEntity.TypeName == "Manufacturers" && t.Value != null && t.Value != "")
                .Select(t => new { t.EntityId, t.TranslatableEntityField.FieldName, t.Value, t.LanguageCode }).ToList()
                .GroupBy(t => t.LanguageCode)
                .ToDictionary(l => l.Key, l =>
                    l.GroupBy(t => t.EntityId).Select(t => new GenericEntity(t.Key, t.FirstOrDefault(c => c.FieldName == "Name")?.Value ?? string.Empty, t.FirstOrDefault(c => c.FieldName == "Synonyms")?.Value ?? string.Empty)));

            foreach (var language in manufacturerTranslations)
            {
                searchCache.ManufacturerTokens.Add(language.Key, MatchableTokens.SplitIntoMatchableTokens(GenerateManufacturerVariants(language.Value, false, false)));
                searchCache.ManufacturerSynonymTokens.Add(language.Key, MatchableTokens.SplitIntoMatchableTokens(GetManufacturersBySynonym(language.Value)));
            }
        }

        public static SearchCache Get(IUnitOfWork unitOfWork)
        {
            lock (_sync)
            {
                try
                {
                    if (_searchCache == null || _lastRefresh.HasValue && _lastRefresh < DateTime.UtcNow.AddMinutes(-MaxAgeInMinutes))
                    {
#if (DEBUG)
                        var sw = Stopwatch.StartNew();
#endif
                        var categories = unitOfWork.CategoryRepository.GetAll().AsNoTracking().ToDictionary(c => c.Id, c => c);
                        categories.Add(0, new Category { Name = "(none)" });
                        var manufacturers = unitOfWork.ManufacturerRepository.GetAll().AsNoTracking().ToDictionary(m => m.Id, m => m);
                        manufacturers.Add(0, new Manufacturer { Name = "(none)" });
                        var details = unitOfWork.DetailRepository.GetAll().AsNoTracking().ToDictionary(m => m.Id, m => m);
                        details.Add(0, new Detail { Name = "(none)" });
                        var hyphenedPhrases = new SortedSet<string>();
                        var sql = "SELECT IndexWord FROM dbo.FTSIndexWords WHERE CHARINDEX('-', IndexWord) > 0 AND PATINDEX('%[0-9]%', IndexWord) = 0";
                        hyphenedPhrases = new SortedSet<string>(unitOfWork.CurrentDbContext.Database.GetDbConnection().Query<string>(sql).ToList());

                        _searchCache = new SearchCache
                        {
                            CategoriesByVanityUrl = categories.Values.Where(c => c.VanityUrl != null).AsEnumerable().GroupBy(c => c.VanityUrl.ToLower()).ToDictionary(c => c.Key, c => c.First().Id),
                            ManufacturersToForgeId = manufacturers.Where(c => c.Value.ForgeManufacturerId != null).ToDictionary(m => m.Key, m => m.Value.ForgeManufacturerId),
                            Manufacturers = manufacturers,
                            KeyStatUnits = unitOfWork.KeyStatUnitRepository.GetAll().AsNoTracking().ToList(),
                            Categories = categories,
                            CategoryNames = categories.ToDictionary(c => c.Key, c => c.Value.Name),
                            ChildCategories = categories.ToDictionary(c => c.Key, c => categories.Values.Where(q =>
                                q.ParentCategoryId == c.Value.Id ||
                                q.ParentCategoryId.HasValue && categories[q.ParentCategoryId.Value].ParentCategoryId == c.Value.Id ||
                                q.ParentCategoryId.HasValue && categories[q.ParentCategoryId.Value].ParentCategoryId.HasValue && categories[categories[q.ParentCategoryId.Value].ParentCategoryId.Value].ParentCategoryId == c.Value.Id
                            ).Select(q => q.Id).Distinct().ToList()),
                            Omniclasses = unitOfWork.OmniclassRepository.GetAll().AsNoTracking().ToList(),
                            Uniclasses = unitOfWork.UniclassRepository.GetAll().AsNoTracking().ToList(),
                            Uniformats = unitOfWork.UniformatRepository.GetAll().AsNoTracking().ToList(),
                            ExternalMasterformatIds = unitOfWork.ProductMasterformatRepository.GetAll().AsNoTracking().Select(b => b.ExternalMasterformatId).Distinct().ToList(),
                            Cisfbs = unitOfWork.CisfbRepository.GetAll().AsNoTracking().ToList(),
                            QualityItems = unitOfWork.QualityItemRepository.GetAll().ProjectToType<SearchCacheQualityItem>().ToList(),
                            HyphenedPhrases = hyphenedPhrases,
                            ConjoinedPhrases = hyphenedPhrases.GroupBy(p => p.Replace("-", "")).ToDictionary(p => p.Key, p => p.First()),
                            ProjectTypes = unitOfWork.ProjectDataTypeRepository.GetAll().Where(t => t.Title != null && t.Title != "").AsNoTracking().Select(t => new { t.Title, t.Id }).ToList()
                                    .GroupBy(g => g.Title.ToLower().Split(' ')[0]).ToDictionary(g => g.Key, g => g.First().Id),
                            DetailTokens = MatchableTokens.SplitIntoMatchableTokens(GenerateDetailVariants(details.Values)),
                            DetailApplications = GetDetailApplications(unitOfWork),
                            Settings = unitOfWork.SettingRepository.GetAll().AsNoTracking().ToList().GroupBy(s => s.Name).ToDictionary(s => s.Key, s => s.First().Value),
                            Synonyms = unitOfWork.SynonymRepository.GetAll().AsNoTracking().AsEnumerable().GroupBy(s => s.Keyword.ToLower()).ToDictionary(s => s.Key, s => s.ToList()),
                            CategoryNameTokens = new MultilingualDictionary<string, MatchableTokens>(),
                            CategorySynonymTokens = new MultilingualDictionary<string, MatchableTokens>(),
                            ManufacturerTokens = new MultilingualDictionary<string, MatchableTokens>(),
                            ManufacturerSynonymTokens = new MultilingualDictionary<string, MatchableTokens>(),
                            ProductCisfbs = unitOfWork.ProductCisfbRepository.GetAll().ProjectToType<SearchCacheProductCisfb>().ToList(),
                            ProductOmniclasses = unitOfWork.ProductOmniclassRepository.GetAll().ProjectToType<SearchCacheProductOmniclass>().ToList(),
                            ProductUniclasses = unitOfWork.ProductUniclassRepository.GetAll().ProjectToType<SearchCacheProductUniclass>().ToList(),
                            ProductUniformats = unitOfWork.ProductUniformatRepository.GetAll().ProjectToType<SearchCacheProductUniformat>().ToList(),
                            ProductCertificates = unitOfWork.ProductCertificateRepository.GetAll().ProjectToType<SearchCacheProductCertificate>().ToList(),
                            ProductMasterformats = unitOfWork.ProductMasterformatRepository.GetAll().ProjectToType<SearchCacheProductMasterformat>().ToList(),
                            ProductQualityItems = unitOfWork.ProductQualityItemRepository.GetAll().ProjectToType<SearchCacheProductQualityItem>().ToList(),
                            ProductSwatchboxEnabledIds = unitOfWork.ProductRepository.GetAll().Where(x => x.SwatchboxProductId != null).Select(x => x.Id).ToList(),
                            ProductMasterspecEnabledIds = unitOfWork.ProductRepository.GetAll()
                                .Where(a => (a.ProductFiles.Any(pf => pf.IsAttachment && (pf.File.SyncUrl.Contains(AttachmentConstants.MasterspecUrl) || pf.File.Title == AttachmentConstants.MasterspecFileTitle)))
                                         || (a.ProductLine.ProductLineFiles.Any(pf => pf.IsAttachment && (pf.File.SyncUrl.Contains(AttachmentConstants.MasterspecUrl) || pf.File.Title == AttachmentConstants.MasterspecFileTitle))))
                                .Select(x => x.Id)
                                .ToList(),
                            ProductProjectDataTypes = unitOfWork.ProductFileRepository.GetAll().Where(x => x.ProjectDataTypeId != null).Select(x => new SearchCacheProductProjectDataTypeItem
                            {
                                ProductId = x.ProductId,
                                ProjectDataTypeId = x.ProjectDataTypeId.Value
                            }).GroupBy(x => x.ProductId)
                            .Select(x => new SearchCacheProductProjectDataType
                            {
                                ProductId = x.Key,
                                ProjectDataTypeIds = x.Select(x => x.ProjectDataTypeId).Distinct().ToList()
                            })
                            .ToList()
                        };

                        // default language
                        var categoryEntities = categories.Values.Select(c => new GenericEntity(c)).ToList();
                        var manufacturerEntities = manufacturers.Values.Select(m => new GenericEntity(m)).ToList();
                        _searchCache.CategoryNameTokens.Add(DefaultLanguage, MatchableTokens.SplitIntoMatchableTokens(GenerateCategoryVariants(categoryEntities, false, false)));
                        _searchCache.CategorySynonymTokens.Add(DefaultLanguage, MatchableTokens.SplitIntoMatchableTokens(GetCategoriesBySynonym(categoryEntities)));
                        _searchCache.ManufacturerTokens.Add(DefaultLanguage, MatchableTokens.SplitIntoMatchableTokens(GenerateManufacturerVariants(manufacturerEntities, false, false)));
                        _searchCache.ManufacturerSynonymTokens.Add(DefaultLanguage, MatchableTokens.SplitIntoMatchableTokens(GetManufacturersBySynonym(manufacturerEntities)));

                        // translations
                        AddCategoryTranslations(_searchCache, unitOfWork);
                        AddManufacturerTranslations(_searchCache, unitOfWork);

                        _searchCache.ExternalMasterformats = _masterformatService.GetBackofficeMasterformatsAsync().GetAwaiter().GetResult()?.OrderBy(x => x.Code).ToList() ?? new List<MasterformatModel>();
                        _searchCache.ExternalCertificates = _certificateService.GetBackofficeCertificatesAsync().GetAwaiter().GetResult()?.OrderBy(x => x.Id).ToList() ?? new List<CertificateModel>();

                        // fixed order of manufacturers and categories for rotating views
                        _searchCache.ManufacturerOrder = unitOfWork.ProductRepository.GetAll()
                            // get manufacturers ordered by how many products they have in the whole database
                            .GroupBy(p => p.ManufacturerId).Select(p => new { ManufacturerId = p.Key, Count = p.Count() }).OrderByDescending(c => c.Count).ToList()
                            // calculate index of each
                            .Select((m, i) => new { m.ManufacturerId, i }).ToDictionary(m => m.ManufacturerId, m => m.i + 1);
                        // get all categories ordered by parent then name
                        _searchCache.CategoryOrder = unitOfWork.CategoryRepository.GetAll().OrderBy(c => c.ParentCategoryId).ThenBy(c => c.Name).ToList()
                            // calculate index of each
                            .Select((c, i) => new { c.Id, i }).ToDictionary(c => c.Id, c => c.i + 1);

                        _searchCache.BuildCertificateSynonyms();
                        _searchCache.BuildWeightsTable();

                        if (!_lastRefresh.HasValue)
                        {
                            Debug.WriteLine("FTSProductSearch::SearchCache built the first time");
                        }
                        else
                        {
                            Debug.WriteLine(string.Format("FTSProductSearch::SearchCache rebuilt (was {0} minutes old)", (DateTime.UtcNow - _lastRefresh.Value).TotalMinutes));
                        }
                        _lastRefresh = DateTime.UtcNow;
#if (DEBUG)
                        Debug.WriteLine(string.Format("FTSProductSearch::SearchCache built in {0}ms", sw.ElapsedMilliseconds));
#endif
                    }

                }
                catch (Exception e)
                {
                    Trace.TraceError("FTS Cache error: {0}", e.Message);
                    throw;
                }
                return _searchCache;
            }
        }

        public IEnumerable<int> GetChildrenMasterformatIds(int externalMasterformatId)
        {
            List<int> masterformatIds = new List<int>() { externalMasterformatId };
            List<int> retriveChildrenMasterformatIds = new List<int>() { externalMasterformatId };

            while (true)
            {
                var childrenIds = ExternalMasterformats.Where(a => a.Parent != null && retriveChildrenMasterformatIds.Contains(a.Parent.Id)).Select(a => a.Id).ToList();
                if (childrenIds.Count == 0)
                {
                    break;
                }
                masterformatIds.AddRange(childrenIds);
                retriveChildrenMasterformatIds = childrenIds;
            }

            return masterformatIds;
        }

        private static readonly HashSet<char> symbolChars = new HashSet<char> { '.', '-', ':' };

        public bool IsAcronym(string word)
        {
            return ExternalCertificates.Any(c => c.Synonyms.Contains(word.ToLower())); // means do not autocorrect
        }

        private static List<string> ExtractCertificateSymbols(string str)
        {
            // try to find suspected codes, i.e. words with capital letters, numbers, dashes and dots
            var sb = new List<string>();

            // split into words
            foreach (var ow in str.Split(' '))
            {
                var w = ow.StartsWith("Mil-") ? ow.ToUpper() : ow;
                var wb = new StringBuilder();
                int wc = 0;
                foreach (var l in w)
                {
                    if (char.IsUpper(l) || char.IsDigit(l) || symbolChars.Contains(l) && wc > 0)
                    {
                        wb.Append(l);
                        wc++;
                    }
                    else
                    {
                        wb.Clear();
                        break;
                    }
                }
                if (!string.IsNullOrWhiteSpace(wb.ToString()))
                {
                    sb.Add(wb.ToString());
                }
            }

            return sb;
        }

        private void BuildCertificateSynonyms()
        {
            foreach (var c in _searchCache.ExternalCertificates)
            {
                try
                {
                    c.Synonyms = new HashSet<string>();
                    c.Synonyms.Add(c.Name.ToLower().Trim());
                    if (c.Name.Contains('('))
                    {
                        var prefix = c.Name.Substring(0, c.Name.IndexOf('(')).ToLower().Trim();
                        c.Synonyms.Add(prefix);

                        if (c.Name.IndexOf(')', c.Name.IndexOf('(')) > 0)
                        {
                            // has closing bracket? search for symbol in brackets
                            var content = c.Name.Substring(c.Name.IndexOf('(') + 1, c.Name.IndexOf(')', c.Name.IndexOf('(')) - c.Name.IndexOf('(') - 1).Trim();
                            c.Synonyms.Add(content.ToLower());

                            var bracketSymbol = ExtractCertificateSymbols(content);
                            c.Synonyms.Add(string.Join(" ", bracketSymbol).ToLower()); // add symbol with spaces
                            c.Synonyms.Add(string.Join("", bracketSymbol).ToLower()); // without spaces
                        }
                    }

                    // special case, symbol after comma at the end
                    if (c.Name.Contains(", "))
                    {
                        var afterComma = c.Name.Substring(c.Name.IndexOf(", ") + 1);
                        var bracketSymbol = ExtractCertificateSymbols(afterComma);
                        c.Synonyms.Add(string.Join(" ", bracketSymbol).ToLower()); // add symbol with spaces
                        c.Synonyms.Add(string.Join("", bracketSymbol).ToLower()); // without spaces
                    }

                    // search for symbol at the start
                    var sb = ExtractCertificateSymbols(c.Name);
                    c.Synonyms.Add(string.Join(" ", sb).ToLower()); // add symbol with spaces
                    c.Synonyms.Add(string.Join("", sb).ToLower()); // without spaces

                    // add any all-capital words themselves, but only if name had some lowercase letters (not an all caps entry)
                    if (c.Name.Any(x => char.IsLower(x)))
                    {
                        foreach (var w in c.Name.Split(' ').Select(w => w.Trim()))
                        {
                            if (w.Length > 1 && w.All(x => char.IsUpper(x) || char.IsDigit(x) || x == '-'))
                            {
                                c.Synonyms.Add(w.ToLower());
                            }
                        }
                    }

                    // fix double spaces and such
                    foreach (var s in c.Synonyms.ToArray())
                    {
                        c.Synonyms.Add(s.Replace("  ", " ").Trim());
                        if (!s.Contains(' '))
                        {
                            c.Synonyms.Add(s.Replace("-", ""));
                        }
                    }

                    c.Synonyms.RemoveWhere(s => s.Length < 3 || s.Length > 20); // CE is too short
                    c.Synonyms.RemoveWhere(s => s.All(x => char.IsDigit(x))); // no pure numbers

                    if (c.Name.ToLower().StartsWith("intertek"))
                    {
                        c.Synonyms.Add("intertek");
                    }
                    if (c.Name.ToLower().StartsWith("federal"))
                    {
                        c.Synonyms.Add("federal");
                    }
                    if (c.Name.ToLower().StartsWith("ansi"))
                    {
                        c.Synonyms.Add("ansi");
                    }
                }
                catch (Exception)
                {
                }
            }
        }

        private static Dictionary<int, SortedSet<string>> InverseSynonyms(Dictionary<string, MatchableTokens> synonyms)
        {
            var itemsBySynonym = new Dictionary<int, SortedSet<string>>();
            foreach (var c in synonyms)
            {
                foreach (var t in c.Value.Tokens)
                {
                    var phrase = string.Join(" ", new string[] { c.Value.Prefix }.Concat(t.Item1).Where(s => !string.IsNullOrWhiteSpace(s)));
                    foreach (var catId in t.Item2)
                    {
                        if (!itemsBySynonym.TryGetValue(catId, out var catSynonyms))
                        {
                            catSynonyms = new SortedSet<string>();
                            itemsBySynonym.Add(catId, catSynonyms);
                        }
                        catSynonyms.Add(phrase);
                    }
                }
            }
            return itemsBySynonym;
        }

        private static SortedSet<string> Singular(SortedSet<string> synonyms)
        {
            var result = new SortedSet<string>(synonyms);
            foreach (var s in synonyms)
            {
                var p = Plural(s);
                if (p != null && result.Contains(p))
                {
                    result.Remove(p);
                }
            }
            return result;
        }

        private static Dictionary<string, List<string>> DumpSynonyms(Dictionary<string, MatchableTokens> synonyms, Dictionary<int, string> itemMap)
        {
            var sl = new Dictionary<string, List<string>>();
            var inverseSynonyms = InverseSynonyms(synonyms);
            foreach (var s in inverseSynonyms.OrderBy(s => itemMap[s.Key]))
            {
                var itemName = $"{itemMap[s.Key]} (#{s.Key})";
                sl.Add(itemName, Singular(s.Value).ToList());
            }
            return sl;
        }

        public dynamic Dump()
        {
            var categoriesBySynonym = DumpSynonyms(CategorySynonymTokens.Get(DefaultLanguage), Categories.ToDictionary(c => c.Key, c => c.Value.ParentCategoryId.HasValue ? Categories[c.Value.ParentCategoryId.Value].Name + " \\ " + c.Value.Name : c.Value.Name));
            var manufacturersBySynonym = DumpSynonyms(ManufacturerSynonymTokens.Get(DefaultLanguage), Manufacturers.ToDictionary(c => c.Key, c => c.Value.Name));

            return new
            {
                Categories = categoriesBySynonym,
                Manufacturers = manufacturersBySynonym
            };
        }

        public static bool IsSymbolOrPunct(char c)
        {
            return char.IsSymbol(c) || char.IsPunctuation(c);
        }

        public int GetSetting(string settingName, int defaultValue)
        {
            try
            {
                var setting = GetSetting(settingName, null);
                return setting == null || string.IsNullOrWhiteSpace(setting) || !int.TryParse(setting, out var iv) ? defaultValue : iv;
            }
            catch (Exception ex)
            {
                Trace.TraceError(ex.Message);
                return defaultValue;
            }
        }

        public float GetSetting(string settingName, float defaultValue)
        {
            try
            {
                var setting = GetSetting(settingName, null);
                return setting == null || string.IsNullOrWhiteSpace(setting) || !float.TryParse(setting, out var iv) ? defaultValue : iv;
            }
            catch (Exception ex)
            {
                Trace.TraceError(ex.Message);
                return defaultValue;
            }
        }

        public string GetSetting(string settingName, string defaultValue)
        {
            try
            {
                if (Settings.TryGetValue(settingName, out var settingValue) && !string.IsNullOrWhiteSpace(settingValue))
                {
                    return settingValue;
                }
                return defaultValue;
            }
            catch (Exception ex)
            {
                Trace.TraceError(ex.Message);
                return defaultValue;
            }
        }

        private void LoadWeights(float[] array, string name)
        {
            for (int i = 1; i < WeightsTable.Matches.Length; i++)
            {
                var key = $"{ProductSearch.sSettingPrefix}Score.{name}.{WeightsTable.Matches[i]}";
                array[i] = GetSetting(key, array[i]);
            }
        }

        public void BuildWeightsTable()
        {
            WeightsTable = new WeightsTable();
            LoadWeights(WeightsTable.PrdName, "PrdName");
            LoadWeights(WeightsTable.PrdModelNum, "PrdModelNum");
            LoadWeights(WeightsTable.PrdDesc, "PrdDesc");
            LoadWeights(WeightsTable.PrdKwrd, "PrdKwrd");
            LoadWeights(WeightsTable.CatName, "CatName");
            LoadWeights(WeightsTable.CatKwrd, "CatKwrd");
            LoadWeights(WeightsTable.ParCatName, "ParCatName");
            LoadWeights(WeightsTable.ParCatKwrd, "ParCatKwrd");
            LoadWeights(WeightsTable.PrdLineName, "PrdLineName");
            LoadWeights(WeightsTable.MfnName, "MfnName");
            LoadWeights(WeightsTable.MfnKwrd, "MfnKwrd");
            LoadWeights(WeightsTable.PrdStat, "PrdStat");
            LoadWeights(WeightsTable.Omncls, "Omncls");
            LoadWeights(WeightsTable.MulCatName, "MulCatName");
            LoadWeights(WeightsTable.MulCatKwrd, "MulCatKwrd");
            WeightsTable.UnitMatchScore = GetSetting(ProductSearch.sSettingPrefix + "Score.UnitMatch", WeightsTable.UnitMatchScore);
            WeightsTable.WeightScore = GetSetting(ProductSearch.sSettingPrefix + "Score.Weight", WeightsTable.WeightScore);
        }
    }

    public class SearchCacheQualityItem
    {
        public int Id { get; set; }

        public string Name { get; set; }

        public string IconUrl { get; set; }
    }

    public class SearchCacheProductCisfb
    {
        public int ProductId { get; set; }

        public int CisfbId { get; set; }
    }

    public class SearchCacheProductOmniclass
    {
        public int ProductId { get; set; }

        public int OmniclassId { get; set; }
    }

    public class SearchCacheProductUniclass
    {
        public int ProductId { get; set; }

        public int UniclassId { get; set; }
    }

    public class SearchCacheProductUniformat
    {
        public int ProductId { get; set; }

        public int UniformatId { get; set; }
    }

    public class SearchCacheProductCertificate
    {
        public int ProductId { get; set; }

        public int ExternalCertificateId { get; set; }
    }

    public class SearchCacheProductMasterformat
    {
        public int ProductId { get; set; }

        public int ExternalMasterformatId { get; set; }
    }

    public class SearchCacheProductQualityItem
    {
        public int ProductId { get; set; }

        public int QualityItemId { get; set; }
    }

    public class SearchCacheProductProjectDataTypeItem
    {
        public int ProductId { get; set; }

        public int ProjectDataTypeId { get; set; }
    }

    public class SearchCacheProductProjectDataType
    {
        public int ProductId { get; set; }

        public List<int> ProjectDataTypeIds { get; set; }
    }
}