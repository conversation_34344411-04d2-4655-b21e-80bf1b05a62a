﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Models
{
    public class AddProductModel
    {
        [Required]
        public bool? Published { get; set; }

        [Required]
        public bool? PublishedOnCustomMicrosite { get; set; }

        [Required]
        public bool? PublishToPartner { get; set; }

        [Required]
        public bool? Staging { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; }

        [Required]
        public string Description { get; set; }

        public float Weight { get; set; }

        [Required]
        public int CategoryId { get; set; }

        [Required]
        public int ManufacturerId { get; set; }

        public int? ProductLineId { get; set; }
        public int? PriceId { get; set; }

        [Required]
        public bool? IsFeatured { get; set; }

        [Required]
        public List<int> RelatedProductIds { get; set; }

        [Required]
        public List<FileDto> Files { get; set; }

        [Required]
        public List<ProjectFileModel> ProjectFiles { get; set; }

        [Required]
        public List<PhotoModel> Photos { get; set; }

        public string Note { get; set; }

        public string ProductUrl { get; set; }

        public string VanityURL { get; set; }

        public string ULUrl { get; set; }

        public string VideoUrl { get; set; }

        public string MetaTitle { get; set; }

        public string MetaDescription { get; set; }

        public string MetaKeywords { get; set; }

        public string Keywords { get; set; }

        public bool IsImperialDefault { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        public string ForgeWallURL { get; set; }

        public string ForgeFloorURL { get; set; }

        public string ForgeCeilingURL { get; set; }

        public string ForgeRoofURL { get; set; }

        public bool AddToULQueue { get; set; }

        public string ExternalProductId { get; set; }

        [Required]
        public List<ProductStatsModel> ProductStats { get; set; }

        public List<int> CategoryIds { get; set; }

        public List<int> ExternalCertificateIds { get; set; }

        public List<int> SampleIds { get; set; }

        public List<int> QualityItemIds { get; set; }

        public List<int> OmniclassIds { get; set; }

        public List<int> UniformatIds { get; set; }

        public List<int> UniclassIds { get; set; }

        public List<int> CisfbIds { get; set; }

        public string SwatchboxProductId { get; set; }

        public string SwatchboxOptionId { get; set; }

        public bool DisplaySwatchboxProductOnProductPage { get; set; }

        public bool DisplaySwatchboxProductOnMicrosite { get; set; }

        public bool HideOnMicrosite { get; set; }

        public string AssemblyCode { get; set; }

        public List<int> DetailIds { get; set; }

        public List<int> ExternalMasterformatIds { get; set; }

        public string FooterAdUrl { get; set; }

        public int? FooterAdImageId { get; set; }
    }

    public class ProductStatsModel
    {
        [Required]
        public int KeyStatId { get; set; }

        public KeyStatType KeyStatType { get; set; }

        public int? KeyStatUnitId { get; set; }

        public string SingleValue { get; set; }

        public string MinRangeValue { get; set; }

        public string MaxRangeValue { get; set; }


        public int? ConvertKeyStatUnitId { get; set; }

        public string ConvertSingleValue { get; set; }

        public string ConvertMinRangeValue { get; set; }

        public string ConvertMaxRangeValue { get; set; }

        public string Note { get; set; }

        public List<ProductStatsMultipleValueModel> MultipleValues { get; set; }
    }

    public class ProductStatsMultipleValueModel
    {
        public int? KeyStatUnitId { get; set; }

        public string MinValue { get; set; }

        public string Value { get; set; }

        public string MaxValue { get; set; }

        public int? ConvertKeyStatUnitId { get; set; }

        public string ConvertValue { get; set; }

        public string ConvertMaxValue { get; set; }

        public string Note { get; set; }
    }

    public class EditProductModel : AddProductModel
    {
        public int Id { get; set; }
    }

    public class EditProduct
    {
        public int ProductId { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }
    }


    public class ProjectFileModel
    {
        [Required]
        public int FileId { get; set; }
        public string CustomFileId { get; set; }

        public string Title { get; set; }

        public int? ProjectDataTypeTypeId { get; set; }
        /// Id of children ProjectDataTypeType that set software version
        public int? SoftwareVersionId { get; set; }

        public string RegionIds { get; set; }

        public string StateIds { get; set; }

        /// <summary>
        /// Obsolete field don't use 
        /// </summary>
        public string SoftwareRelease { get; set; }

        public string ContentCreatedby { get; set; }

        public string FileVersion { get; set; }

        public string ContentCheckedBy { get; set; }
    }

    public class PhotoModel
    {
        [Required]
        public int PhotoId { get; set; }

        //[Required]
        //public string Title { get; set; }
    }

    public class AddExternalProduct
    {
        public int? ProductId { get; set; }

        public bool Published { get; set; }

        public bool PublishedOnCustomMicrosite { get; set; }

        public bool Staging { get; set; }

        public string ProductLine { get; set; }

        public string ExternalProductId { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; }

        [Required]
        public string Description { get; set; }

        public List<ExternalNewProductPhoto> Photos { get; set; }

        public List<ExternalNewProductDocuments> Documents { get; set; }

        public string ProductUrl { get; set; }

        public string ULUrl { get; set; }

        public string VideoUrl { get; set; }

        public List<ExternalNewProductStat> ProductStats { get; set; }

        [Required]
        public List<ExternalNewProductName> CategoryNames { get; set; }

        public List<ExternalNewProductCode> OmniclassIds { get; set; }

        public List<ExternalNewProductCode> UniformatIds { get; set; }

        public List<ExternalNewProductCode> UniclassIds { get; set; }

        public List<ExternalNewProductCode> MasterformatIds { get; set; }

        public List<ExternalNewProductCode> CisfbIds { get; set; }

        public List<int> ExternalMasterformatIds { get; set; }
    }

    public class ExternalNewProductStat : ExternalNewProductName
    {
        public string KeyStatType { get; set; }

        public ExternalNewProductStatUnit KeyStatUnit { get; set; }

        public string Value { get; set; }

        public string MinRangeValue { get; set; }

        public string MaxRangeValue { get; set; }

        public string Note { get; set; }

        public List<ExternalNewProductStatMultiple> MultipleValues { get; set; }
    }

    public class ExternalNewProductStatMultiple
    {
        public ExternalNewProductStatUnit KeyStatUnit { get; set; }

        public string Value { get; set; }

        public string MaxRangeValue { get; set; }

        public string Note { get; set; }
    }

    public class ExternalNewProductStatUnit
    {
        public string BUnitName { get; set; }

        public string GroupName { get; set; }
    }

    public class ExternalNewProductPhoto
    {
        [Required]
        public string PhotoURL { get; set; }
    }

    public class ExternalNewProductName
    {
        [Required]
        public string Name { get; set; }
        public string CustomId { get; set; }
    }

    public class ExternalNewProductDocuments : ExternalNewProductName
    {
        [Required]
        public string Url { get; set; }

        public string Type { get; set; }
    }

    public class ExternalNewProductCode
    {
        [Required]
        public string Code { get; set; }
    }

    public class GetAddExternalProduct : AddExternalProduct
    {
        public List<ExternalNewProductDocuments> RevitFiles { get; set; }

        public List<ExternalNewProductDocuments> AutocadFiles { get; set; }

        public List<ExternalNewProductDocuments> IESFiles { get; set; }
    }
}