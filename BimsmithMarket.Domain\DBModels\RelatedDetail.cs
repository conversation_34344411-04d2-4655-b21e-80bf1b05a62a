﻿using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class RelatedDetail : BaseEntity
    {
        public int DetailId { get; set; }

        public int? RelatedDetailId { get; set; }

        [ForeignKey("DetailId")]
        public virtual Detail Detail { get; set; }

        [ForeignKey("RelatedDetailId")]
        public virtual Detail RelatedDetailItem { get; set; }
    }
}