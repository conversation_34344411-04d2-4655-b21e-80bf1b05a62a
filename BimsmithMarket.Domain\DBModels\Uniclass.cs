﻿using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    [Index(nameof(Code), Name = "IX_Uniclass_Code")]
    [Index(nameof(Title), Name = "IX_Uniclass_Title")]
    public class Uniclass
    {
        public int Id { get; set; }

        public int? ParentId { get; set; }

        [StringLength(200)]
        public string Code { get; set; }

        [StringLength(200)]
        public string Title { get; set; }

        [ForeignKey("ParentId")]
        public virtual Uniclass Parent { get; set; }

        public virtual ICollection<Uniclass> Children { get; set; }

        public Uniclass()
        {
            Children = new List<Uniclass>();
        }
    }
}