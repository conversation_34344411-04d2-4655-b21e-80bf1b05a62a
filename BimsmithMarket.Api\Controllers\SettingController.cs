﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// Controller for managing settings
    /// </summary>
#if !DEBUG
    [Authorize(Roles = DbConstants.AdminRole)]
#endif
    [Route("api/[controller]/[action]")]
    public class SettingController : BaseApiController
    {
        private readonly ISettingService _settingService;

        public SettingController(ISettingService settingService)
        {
            _settingService = settingService;
        }

        /// <summary>
        /// Get list of all settings in system: For Role - ADMIN
        /// </summary>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("List")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> List(int offset = 0, int count = 9999)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _settingService.ListAsync(unitOfWork, offset, count));
            }
        }

        /// <summary>
        /// Edit setting
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Edit")]
        public async Task<IActionResult> Edit([FromBody] SettingModel model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                await _settingService.EditAsync(unitOfWork, model);

                Log.Information($"Updating setting to value: {model.Value}");

                CacheHelper.ClearSpecificCache("*/api/Setting/List*");

                return Ok();
            }
        }
    }
}
