﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels.DynamicTranslationDbModels
{
    public class TranslatableEntityField : BaseEntity
    {
        public string FieldName { get; set; }

        public string Caption { get; set; }

        public int TranslatableEntityId { get; set; }

        [ForeignKey("TranslatableEntityId")]
        public virtual TranslatableEntity TranslatableEntity { get; set; }

        public virtual ICollection<DynamicTranslation> DynamicTranslations { get; set; }
    }
}