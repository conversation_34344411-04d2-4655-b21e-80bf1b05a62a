using BIMsmith.AzureStorageProvider;
using BIMsmith.ExcelProvider;
using BIMsmithMarket.Api.Jobs;
using BIMsmithMarket.Api.Services;
using BIMsmithMarket.Api.Services.ManufacturerBackup;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Core.MapsterConfig.Map;
using BIMsmithMarket.Core.Middelewares;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.DataLayer.Context;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services;
using BIMsmithMarket.Services.HealthDashboardServices;
using BIMsmithMarket.Services.Helpers;
using BIMsmithMarket.Services.Interfaces;
using BIMsmithMarket.Services.Interfaces.RevitProcessing;
using BIMsmithMarket.Services.MassTransit.Consumers;
using BIMsmithMarket.Services.PaymentServices;
using BIMsmithMarket.Services.ProductServices;
using BIMsmithMarket.Services.RevitProcessing;
using Flurl;
using Mapster;
using MassTransit;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Http.Resilience;
using Microsoft.IdentityModel.Tokens;
using Polly;
using Quartz;
using Quartz.AspNetCore;
using Serilog;
using System;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            // Add services to the container.
            builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
                   .AddCookie(options =>
                   {
                       options.LoginPath = new Microsoft.AspNetCore.Http.PathString("/token");
                   });

            builder.Services.AddControllers();
            // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
            builder.Services.AddEndpointsApiExplorer();
            builder.Services.AddSwaggerGen();

            builder.Services
                   //.AddEntityFrameworkSqlServer() //Remove this call for Throttle attribute to work
                   .AddDbContextPool<ApplicationDbContext>((serviceProvider, options)
                    => options.UseLazyLoadingProxies()
                              .UseSqlServer(ConfigurationHelper.GetValue("ConnectionStrings:MarketDBConnection"))
                              .UseInternalServiceProvider(serviceProvider));

            builder.Services.AddIdentityCore<ApplicationUser>()
                    .AddRoles<ApplicationRole>()
                    .AddEntityFrameworkStores<ApplicationDbContext>()
                    .AddDefaultTokenProviders();

            builder.Services.AddAuthentication(x =>
            {
                x.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                x.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(x =>
            {
                x.TokenValidationParameters = new TokenValidationParameters();
            });

            builder.Services.AddMemoryCache();
            builder.Services.AddHttpClient(string.Empty).AddStandardResilienceHandler(options =>
            {
                options.Retry = new HttpRetryStrategyOptions
                {
                    OnRetry = async onRetryContext =>
                    {
                        await LogHelper.LogHttpClientRetryAsync(onRetryContext.Outcome.Result, onRetryContext.Outcome.Exception);
                    },
                    Delay = TimeSpan.FromSeconds(5),
                    MaxRetryAttempts = 3,
                    BackoffType = DelayBackoffType.Exponential,
                    UseJitter = true,
                    ShouldHandle = async shouldHandleContext =>
                    {
                        Exception exception = shouldHandleContext.Outcome.Exception;
                        return NetworkHelper.IsResponseInvalid(shouldHandleContext.Outcome.Result)
                            || (exception != null && (exception is HttpRequestException || exception is TaskCanceledException));
                    }
                };
            });

            builder.Services.AddMassTransit(busConfigurator =>
            {
                busConfigurator.SetKebabCaseEndpointNameFormatter();

                busConfigurator.AddConsumer<ProductLineChangedConsumer>();
                busConfigurator.AddConsumer<ManufacturerChangedConsumer>();
                busConfigurator.AddConsumer<QualityItemChangedConsumer>();

                busConfigurator.UsingInMemory((context, configurator) =>
                {
                    configurator.ConfigureEndpoints(context);
                });
            });

            builder.Services.AddScoped<IExcelHelper, ExcelHelper>();
            builder.Services.AddScoped<IChangeLogService, ChangeLogService>();
            builder.Services.AddScoped<SlackWebHook>();
            builder.Services.AddScoped<IMongoRepository<ProductMongoDto>, MongoRepository<ProductMongoDto>>();
            builder.Services.AddScoped<IAccountService, AccountService>();
            builder.Services.AddScoped<IAnalyticsService, AnalyticsService>();
            builder.Services.AddScoped<IAttachmentOrderService, AttachmentOrderService>();
            builder.Services.AddScoped<IBIMsmithService, BIMsmithService>();
            builder.Services.AddScoped<IBlogService, BlogService>();
            builder.Services.AddScoped<ICacheService, CacheService>();
            builder.Services.AddScoped<ICategoryKeyStatService, CategoryKeyStatService>();
            builder.Services.AddScoped<ICboCertificateService, CboCertificateService>();
            builder.Services.AddScoped<ICertificateService, CertificateService>();
            builder.Services.AddScoped<ICisfbService, CisfbService>();
            builder.Services.AddScoped<ICompanyService, CompanyService>();
            builder.Services.AddScoped<IEventService, EventService>();
            builder.Services.AddScoped<IFileService, FileService>();
            builder.Services.AddScoped<IHelperService, HelperService>();
            builder.Services.AddScoped<IHelpService, HelpService>();
            builder.Services.AddScoped<IKeyStatUnitService, KeyStatUnitService>();
            builder.Services.AddScoped<IManufacturerAdminUserService, ManufacturerAdminUserService>();
            builder.Services.AddScoped<IMasterformatService, MasterformatService>();
            builder.Services.AddScoped<INewsService, NewsService>();
            builder.Services.AddScoped<IOmniclassService, OmniclassService>();
            builder.Services.AddScoped<IPluginFileService, PluginFileService>();
            builder.Services.AddScoped<IProductLineFilesService, ProductLineFilesService>();
            builder.Services.AddScoped<IProductQualityItemService, ProductQualityItemService>();
            builder.Services.AddScoped<IProjectDataTypeService, ProjectDataTypeService>();
            builder.Services.AddScoped<IRevitPluginService, RevitPluginService>();
            builder.Services.AddScoped<ISettingService, SettingService>();
            builder.Services.AddScoped<IStarterService, StarterService>();
            builder.Services.AddScoped<IUniclassService, UniclassService>();
            builder.Services.AddScoped<IUniformatService, UniformatService>();
            builder.Services.AddScoped<IUploadFileService, UploadFileService>();
            builder.Services.AddScoped<IManufacturerService, ManufacturerService>();
            builder.Services.AddScoped<IVanityHistoryService, VanityHistoryService>();
            builder.Services.AddScoped<IPaymentPlanService, PaymentPlanService>();
            builder.Services.AddScoped<IPriceService, PriceService>();
            builder.Services.AddScoped<IProductPriceService, ProductPriceService>();
            builder.Services.AddScoped<IUserPaidProductService, UserPaidProductService>();
            builder.Services.AddScoped<IHealthDashboardService, HealthDashboardService>();
            builder.Services.AddScoped<IHealthDashboardExcelService, HealthDashboardExcelService>();
            builder.Services.AddScoped<IProductService, ProductService>();
            builder.Services.AddScoped<IAnnouncementService, AnnouncementService>();
            builder.Services.AddScoped<ICategoryService, CategoryService>();
            builder.Services.AddScoped<IDetailApplicationService, DetailApplicationService>();
            builder.Services.AddScoped<IDetailScaleService, DetailScaleService>();
            builder.Services.AddScoped<IDetailService, DetailService>();
            builder.Services.AddScoped<IManufacturerBackupFileService, ManufacturerBackupFileService>();
            builder.Services.AddScoped<IManufacturerBackupRestoreService, ManufacturerBackupRestoreService>();
            builder.Services.AddScoped<IManufacturerBackupService, ManufacturerBackupService>();
            builder.Services.AddScoped<IDynamicTranslationService, DynamicTranslationService>();
            builder.Services.AddScoped<IFeatureSettingService, FeatureSettingService>();
            builder.Services.AddScoped<IProductLineService, ProductLineService>();
            builder.Services.AddScoped<IStyleFileService, StyleFileService>();
            builder.Services.AddScoped<IUploadService, UploadService>();
            builder.Services.AddScoped<IUserService, UserService>();
            builder.Services.AddScoped<IMetadataService, MetadataService>();
            builder.Services.AddScoped<IAzureStorageService, AzureStorageService>(provider =>
            {
                return ActivatorUtilities.CreateInstance<AzureStorageService>(
                    provider,
                    ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                    ConfigurationHelper.GetValue("Environment"),
                    bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                    false);
            });
            builder.Services.AddScoped<IStaticExcelImportService, StaticExcelImportService>();
            builder.Services.AddScoped<IProductLineExcelService, ProductLineExcelService>();
            builder.Services.AddScoped<IDropboxService, DropboxService>();
            builder.Services.AddScoped<IAuthorizationService, AuthorizationService>();
            builder.Services.AddScoped<SendAnalyticsService>();
            builder.Services.AddScoped<ICustomCategoryIconService, CustomCategoryIconService>();
            builder.Services.AddScoped<ISalesRepresentativeService, SalesRepresentativeService>();
            builder.Services.AddScoped<IChangeLogExcelService, ChangeLogExcelService>();
            builder.Services.AddScoped<INoteService, NoteService>();
            builder.Services.AddScoped<IManufacturerExcelService, ManufacturerExcelService>();
            builder.Services.AddScoped<IAnalyticsService, AnalyticsService>();
            builder.Services.AddScoped<ICheckService, CheckService>();
            builder.Services.AddScoped<IExternalApiService, ExternalApiService>();
            builder.Services.AddScoped<IPaymentService, PaymentService>(provider =>
            {
                string successUrl = new Url(ConfigurationHelper.GetValue("MarketApiBaseUrl")) + "api/Payment/StripeSuccess?sessionid={CHECKOUT_SESSION_ID}";
                string cancelUrl = new Url(ConfigurationHelper.GetValue("MarketApiBaseUrl")) + "api/Payment/StripeCancelation?sessionid={CHECKOUT_SESSION_ID}";
                string apiKey = ConfigurationHelper.GetValue("StripePrivateKey");

                return ActivatorUtilities.CreateInstance<PaymentService>(
                    provider,
                    apiKey,
                    successUrl,
                    cancelUrl,
                    provider.GetService<IProductPriceService>(),
                    provider.GetService<IUserPaidProductService>());
            });
            builder.Services.AddScoped<IRevitProcessingService, RevitProcessingService>();
            builder.Services.AddScoped<SitemapGenerator>();

            //Do not run jobs on local debug
#if !DEBUG
            builder.Services.AddQuartz(options =>
            {
                options.ScheduleJob<ClearSearchCacheJob>(trigger => trigger
                    .WithIdentity("clearSearchCacheJobTrigger")
                    .StartAt(DateBuilder.EvenHourDate(null))
                    .WithSimpleSchedule(x => x.WithIntervalInHours(1).RepeatForever()));

                options.ScheduleJob<RefreshAutocorrectCacheJob>(trigger => trigger
                    .WithIdentity("refreshAutocorrectCacheJobTrigger")
                    .StartAt(DateBuilder.EvenHourDate(null))
                    .WithSimpleSchedule(x => x.WithIntervalInHours(1).RepeatForever()));

                options.ScheduleJob<CheckHealthStatusJob>(trigger => trigger
                    .WithIdentity("checkHealthStatusJobTrigger")
                    .StartAt(DateBuilder.EvenHourDate(null))
                    .WithSimpleSchedule(x => x.WithIntervalInHours(1).RepeatForever()));

                options.ScheduleJob<StaticExcelFileParseJob>(trigger => trigger
                    .WithIdentity("staticExcelParseFileTrigger")
                    .StartNow()
                    .WithSimpleSchedule(x => x.WithIntervalInMinutes(1).RepeatForever()));

                options.ScheduleJob<StaticExcelProductProcessingJob>(trigger => trigger
                    .WithIdentity("staticExcelProcessingProductTrigger")
                    .StartNow()
                    .WithSimpleSchedule(x => x.WithIntervalInMinutes(1).RepeatForever()));

                options.ScheduleJob<DeleteUnusedBlobsJob>(trigger => trigger
                    .WithIdentity("deleteUnusedBlobsJobTrigger")
                    .StartNow()
                    .WithSchedule(CronScheduleBuilder.MonthlyOnDayAndHourAndMinute(1, 0, 0).WithMisfireHandlingInstructionFireAndProceed()));

                options.ScheduleJob<DownloadFilesJob>(trigger => trigger
                    .WithIdentity("downloadFilesJobTrigger")
                    .StartNow()
                    .WithSimpleSchedule(s => s.WithIntervalInMinutes(1).RepeatForever()));

                options.ScheduleJob<DownloadRevitsJob>(trigger => trigger
                    .WithIdentity("downloadRevitsJobTrigger")
                    .StartNow()
                    .WithSimpleSchedule(s => s.WithIntervalInMinutes(1).RepeatForever()));

                options.ScheduleJob<ULPartnershipJob>(trigger => trigger
                    .WithIdentity("ulPartnershipJobTrigger")
                    .StartNow()
                    .WithSimpleSchedule(s => s.WithIntervalInMinutes(1).RepeatForever()));

                options.ScheduleJob<UpdateThumbnailsJob>(trigger => trigger
                    .WithIdentity("updateThumbnailsJobTrigger")
                    .StartNow()
                    .WithSimpleSchedule(s => s.WithIntervalInMinutes(1).RepeatForever()));

                options.ScheduleJob<PublishNewsAndBlogsByScheduleJob>(trigger => trigger
                    .WithIdentity("publishNewsAndBlogsByScheduleJobTrigger")
                    .StartNow()
                    .WithSimpleSchedule(s => s.WithIntervalInMinutes(1).RepeatForever()));

                options.ScheduleJob<UpdateMongoProductJob>(trigger => trigger
                    .WithIdentity("updateMongoProductJobTrigger")
                    .StartNow()
                    .WithSchedule(CronScheduleBuilder.DailyAtHourAndMinute(0, 0).WithMisfireHandlingInstructionFireAndProceed()));

                options.ScheduleJob<NoteNotificationJob>(trigger => trigger
                    .WithIdentity("noteNotificationJobTrigger")
                    .StartNow()
                    .WithSchedule(CronScheduleBuilder.DailyAtHourAndMinute(0, 0).WithMisfireHandlingInstructionFireAndProceed()));

                options.ScheduleJob<ExternalApiUpdateProductsJob>(trigger => trigger
                    .WithIdentity("externalApiUpdateProductsJobTrigger")
                    .StartNow()
                    .WithSimpleSchedule(s => s.WithIntervalInMinutes(1).RepeatForever()));
            });

            builder.Services.AddQuartzServer(options =>
            {
                options.WaitForJobsToComplete = true;
            });
#endif

            // Mapster registration
            var config = TypeAdapterConfig.GlobalSettings;
            MapsterConfig.RegisterAllProfiles(config);

            Log.Logger = LogHelper.Initialize("Market Api");

            var app = builder.Build();

            app.Use((context, next) =>
            {
                if (context.Request.Path.Value.StartsWith("//"))
                {
                    context.Request.Path = new PathString(context.Request.Path.Value.Replace("//", "/"));
                }
                return next();
            });

            app.Use(async (context, next) =>
            {
                string origin = context.Request.Headers.Origin.ToString();
                context.Response.Headers.AccessControlAllowOrigin = !string.IsNullOrWhiteSpace(origin) ? origin : "*";
                context.Response.Headers.AccessControlAllowCredentials = "true";
                context.Response.Headers.AccessControlAllowHeaders = "Origin, Content-Type, Accept, rememberMe, Authorization";
                context.Response.Headers.AccessControlAllowMethods = "GET, POST, PUT, DELETE, PATCH";
                context.Response.Headers.AccessControlMaxAge = "3600";

                if (context.Request.Method.ToLower() == "options")
                {
                    context.Response.StatusCode = (int)HttpStatusCode.OK;
                }
                else await next(context);
            });

            app.UseSwagger();
            app.UseSwaggerUI();

            app.UseMiddleware<BrowserCheckMiddleware>();
            app.UseMiddleware<ExceptionsMiddleware>();

            app.UseHttpsRedirection();

            app.UseStaticFiles();

            app.UseRouting();

            app.UseAuthorization();

            app.Use((context, next) =>
            {
                context.Request.EnableBuffering();
                return next();
            });

            app.MapControllers();

            app.Run();
        }
    }
}