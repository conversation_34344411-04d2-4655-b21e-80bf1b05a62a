﻿using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.DynamicTranslationDto;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IDynamicTranslationService
    {
        Task<PaginationListDto<TranslatableEntityListDto>> TranslatableEntityListAsync(int offset = 0, int count = 10);

        Task<PaginationListDto<TranslatableEntityFieldListDto>> TranslatableEntityFieldListAsync(int translatableEntityId, int offset = 0, int count = 10);

        Task<PaginationListDto<ListDynamicTranslationDto>> DynamicTranslationListAsync(int translatableEntityId, int entityId, int offset = 0, int count = 10);

        Task<EditDynamicTranslationDto> AddDynamicTranslationAsync(AddDynamicTranslationDto model, string userId);

        Task AddDynamicTranslationsAsync(AddDynamicTranslationListDto model, string userId);

        Task<EditDynamicTranslationDto> EditDynamicTranslationAsync(EditDynamicTranslationDto model, string userId);

        Task<EditDynamicTranslationDto> GetDynamicTranslationAsync(int id);

        Task DeleteDynamicTranslationAsync(int id);
    }
}