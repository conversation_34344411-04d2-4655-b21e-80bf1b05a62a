﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class NoteService : INoteService
    {
        private IUserService _userService;

        public NoteService(IUserService userService)
        {
            _userService = userService;
        }

        public async Task<AdminGetNoteDto> AddAsync(AddNoteDto model, string userId, IUnitOfWork unitOfWork)
        {
            unitOfWork.BeginTransaction();
            Note note = model.Adapt<Note>();
            note.CreatedById = userId;
            unitOfWork.NoteRepository.Insert(note);
            await unitOfWork.SaveAsync();

            foreach (string email in model.NotificationList)
            {
                if (!await _userService.IsUserAdminAsync(email, unitOfWork))
                    throw new InvalidInputException($"User with email {email} is not admin");

                NoteNotificationUser noteNotificationUser = new NoteNotificationUser
                {
                    Email = email,
                    CreatedById = userId,
                    NoteId = note.Id
                };

                unitOfWork.NoteNotificationUserRepository.Insert(noteNotificationUser);
            }

            await unitOfWork.SaveAsync();
            unitOfWork.CommitTransaction();

            return await AdminGetAsync(note.Id, unitOfWork);
        }

        public async Task<AdminGetNoteDto> EditAsync(EditNoteDto model, string userId, IUnitOfWork unitOfWork)
        {
            Note note = await unitOfWork.NoteRepository.GetByIdAsync(model.Id);

            if (note == null)
                throw new InvalidInputException("Note does not exist");

            unitOfWork.BeginTransaction();
            model.Adapt(note);
            note.ModifiedById = userId;
            note.ModifiedDate = DateTime.UtcNow;

            unitOfWork.NoteRepository.Edit(note);
            await unitOfWork.SaveAsync();

            NoteNotificationUser[] noteNotificationUsers = await unitOfWork.NoteNotificationUserRepository.GetAll()
                .Where(x => x.NoteId == note.Id)
                .ToArrayAsync();

            string[] existingEmails = noteNotificationUsers.Select(x => x.Email.ToUpper()).ToArray();
            string[] modelEmails = model.NotificationList.Select(x => x.ToUpper()).ToArray();
            string[] emailsToAdd = modelEmails.Except(existingEmails).ToArray();
            string[] emailsToDelete = existingEmails.Except(modelEmails).ToArray();

            foreach (string email in emailsToAdd)
            {
                if (!await _userService.IsUserAdminAsync(email, unitOfWork))
                    throw new InvalidInputException($"User with email {email} is not admin");

                NoteNotificationUser noteNotificationUser = new NoteNotificationUser
                {
                    Email = email.ToLower(),
                    CreatedById = userId,
                    NoteId = note.Id
                };

                unitOfWork.NoteNotificationUserRepository.Insert(noteNotificationUser);
            }

            foreach (string email in emailsToDelete)
            {
                NoteNotificationUser noteNotificationUser = noteNotificationUsers.FirstOrDefault(x => x.Email.ToUpper() == email);
                unitOfWork.NoteNotificationUserRepository.Delete(noteNotificationUser);
            }

            await unitOfWork.SaveAsync();
            unitOfWork.CommitTransaction();

            return await AdminGetAsync(note.Id, unitOfWork);
        }

        public async Task<AdminGetNoteDto> AdminGetAsync(int id, IUnitOfWork unitOfWork)
        {
            AdminGetNoteDto note = await unitOfWork.NoteRepository.GetAll()
                .Where(x => x.Id == id)
                .ProjectToType<AdminGetNoteDto>()
                .FirstOrDefaultAsync();

            if (note == null)
                throw new DbItemNotFoundException("Note does not exist");

            return note;
        }

        public async Task<PaginationListDto<AdminListNoteDto>> AdminListAsync(
            IUnitOfWork unitOfWork,
            EntityType entityType,
            int entityId,
            bool? isActive = null,
            string query = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int offset = 0,
            int count = 10)
        {
            IQueryable<Note> dbQuery = unitOfWork.NoteRepository.GetAll()
                .Where(x => x.EntityType == entityType
                         && x.EntityId == entityId);

            if (isActive.HasValue)
            {
                dbQuery = dbQuery
                    .Where(x => x.DateIsEffectiveTill == null
                             || (x.DateIsEffectiveTill != null && x.DateIsEffectiveTill >= DateTime.UtcNow));
            }

            if (!string.IsNullOrWhiteSpace(query))
            {
                string normalizedQuery = query.ToUpper();
                dbQuery = dbQuery.Where(x => x.Name.ToUpper().Contains(normalizedQuery)
                    || x.CreatedBy.Email.ToUpper().Contains(normalizedQuery)
                    || x.NotificationList.Any(x => x.Email.ToUpper().Contains(normalizedQuery)));
            }

            if (startDate != null)
            {
                dbQuery = dbQuery.Where(x => x.CreatedDate.Date >= startDate.Value.Date);
            }

            if (endDate != null)
            {
                dbQuery = dbQuery.Where(x => x.CreatedDate.Date <= endDate.Value.Date);
            }

            int totalCount = await dbQuery.CountAsync();

            AdminListNoteInternalDto[] dbItems = await dbQuery
                .OrderByDescending(x => x.Id)
                .ProjectToType<AdminListNoteInternalDto>()
                .Skip(offset)
                .Take(count)
                .ToArrayAsync();

            AdminListNoteDto[] items = dbItems.Adapt<AdminListNoteDto[]>();

            return new PaginationListDto<AdminListNoteDto>
            {
                Count = totalCount,
                Data = items
            };
        }

        public async Task<OperationResultDto> DeleteAsync(int id, IUnitOfWork unitOfWork)
        {
            Note note = await unitOfWork.NoteRepository.GetByIdAsync(id);

            if (note == null)
                throw new InvalidInputException("Note does not exist");

            unitOfWork.NoteRepository.Delete(note);
            await unitOfWork.SaveAsync();

            return new OperationResultDto
            {
                Status = OperationResultStatus.Succeed
            };
        }
    }
}