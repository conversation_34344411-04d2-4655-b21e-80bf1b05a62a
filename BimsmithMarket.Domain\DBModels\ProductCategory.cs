﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class ProductCategory
    {
        [Key]
        [Column(Order = 1)]
        public int ProductId { get; set; }

        [Key]
        [Column(Order = 2)]
        public int CategoryId { get; set; }

        public bool MainCategory { get; set; }

        public float Weight { get; set; }
        /// ------------------------------------------

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; }

        [ForeignKey("CategoryId")]
        public virtual Category Category { get; set; }
    }
}