﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace BIMsmithMarket.Services.Search.FullTextSearch
{
    public class CategoryResult
    {
        public int ProductId { get; set; }
        public int CategoryId { get; set; }
        public int? ParentCategoryId { get; set; }
        public int ManufacturerId { get; set; }
        public long PRn { get; set; } // product rank
        public long CRn { get; set; } // category rank
        public long MRn { get; set; } // manufacturer rank
        public long? PCRn { get; set; } // parent category rank
        public float Weight { get; set; } // product weight
        public int ResultBlock { get; set; }
    }

    public static class StaticViews
    {
        /// <summary>
        /// Returns product list when there is no text search query (rotated by manufacturer and category - two products per manufacturer)
        /// </summary>
        public static ProductSearchResults DefaultView(
            ProductSearchOptions options,
            List<int> categoryIds,
            IQueryable<Product> commonResults,
            IQueryable<Product> input,
            SearchResults results,
            IUnitOfWork unitOfWork,
            SearchCache searchCache,
            ProductSearch.SearchAlgorithm algo)
        {
            // if we have identified categories based on query text, narrow down potential results to them
            if (categoryIds.Any(c => c > 0) && categoryIds.Any(c => c != options.CategoryId))
            {
                commonResults = commonResults.Where(p =>
                    categoryIds.Contains(p.CategoryId) ||
                    (p.Category.ParentCategoryId != null && categoryIds.Contains(p.Category.ParentCategoryId.Value)) ||
                    (p.Category.ParentCategoryId != null && p.Category.ParentCategory.ParentCategoryId != null && categoryIds.Contains(p.Category.ParentCategory.ParentCategoryId.Value)) ||
                    p.ProductCategories.Any(pc => categoryIds.Contains(pc.CategoryId)));
            }

            if (options.SortType == ProductSortType.Relevant)
            {
                List<CategoryResult> orderedProducts = new List<CategoryResult>();
                if (options.SearchManufacturerChildProducts == false && (options.ManufacturerIds != null && options.ManufacturerIds.Any()))
                {
                    // only allow results from main manufacturer (child manufacturers are included by common filters)
                    commonResults = commonResults.Where(r => options.ManufacturerIds.Contains(r.ManufacturerId));
                }

#if (DEBUG)
                var sw = Stopwatch.StartNew();
#endif

                orderedProducts = GetProductDisplayOrderViews(commonResults, unitOfWork.ProductCategoryRepository.GetAll(), searchCache);

                IEnumerable<KeyValuePair<int, float>> reorderedProducts;
                if (options.ManufacturerIds != null && options.ManufacturerIds.Any())
                {
                    // we are looking at a single manufacturer, reorder products by weight first
                    reorderedProducts = orderedProducts
                        .OrderByDescending(p => p.Weight)
                        .ThenBy(p => p.ResultBlock)
                        .ThenBy(p => p.PRn)
                        .ThenBy(p => p.CRn)
                        .ThenBy(p => p.MRn)
                        .ThenBy(p => p.ProductId)
                        .Select(p => new KeyValuePair<int, float>(p.ProductId, p.Weight));
                    results.Explain(c => $"Returning reordered default view for manufacturers {string.Join(",", c.Manufacturers.Where(x => options.ManufacturerIds.Contains(x.Key)).Select(x => x.Value.Name))}");
                }
                else
                {
                    reorderedProducts = orderedProducts.Select(p => new KeyValuePair<int, float>(p.ProductId, 0));
                    results.Explain(c => $"Returning reordered default view for categories {string.Join(", ", categoryIds.Select(i => c.Categories[i].Name))}");
                }

#if (DEBUG)
                System.Diagnostics.Debug.WriteLine(String.Format("FTSProductSearch::DefaultView for categories {0} took {1}ms", string.Join(",", categoryIds), sw.ElapsedMilliseconds));
#endif
                return new ProductSearchResults(options, commonResults, reorderedProducts.Select(p => new RankedResults.ProductRank { Id = p.Key, Weight = p.Value }), null, input, results);
            }
            else
            {
                return new ProductSearchResults(options, commonResults, false, results);
            }
        }

        /// <summary>
        /// Produces a default view with interleaved products from multiple manufacturers
        /// </summary>
        public static List<CategoryResult> GetProductDisplayOrderViews(IQueryable<Product> Products, IQueryable<ProductCategory> ProductCategories, SearchCache searchCache)
        {
            // results by primary category (result block 0)
            var results = Products.Select(p => new
            {
                ProductId = p.Id,
                CategoryId = p.CategoryId,
                ManufacturerId = p.ManufacturerId,
                ParentCategoryId = p.Category.ParentCategoryId,
                ParentParentCategoryId = p.Category.ParentCategory != null ? (int?)p.Category.ParentCategory.Id : null,
                Weight = p.Weight
            }).ToList().Select(r => new ProductDisplayOrderView
            {
                ProductId = r.ProductId,
                CategoryId = r.CategoryId,
                ManufacturerId = r.ManufacturerId,
                ParentCategoryId = r.ParentCategoryId,
                ParentParentCategoryId = r.ParentParentCategoryId,
                Weight = r.Weight,
                ResultBlock = 0
            }).ToList();

            // results by secondary category (result block 1)
            results.AddRange(Products.Join(ProductCategories, p => p.Id, pc => pc.ProductId, (p, pc) => new
            {
                ProductId = p.Id,
                CategoryId = pc.CategoryId,
                ManufacturerId = p.ManufacturerId,
                ParentCategoryId = pc.Category.ParentCategoryId,
                ParentParentCategoryId = pc.Category.ParentCategory != null ? (int?)pc.Category.ParentCategory.Id : null,
                Weight = p.Weight
            }).ToList().Select(r => new ProductDisplayOrderView
            {
                ProductId = r.ProductId,
                CategoryId = r.CategoryId,
                ManufacturerId = r.ManufacturerId,
                ParentCategoryId = r.ParentCategoryId,
                ParentParentCategoryId = r.ParentParentCategoryId,
                Weight = r.Weight,
                ResultBlock = 1
            }));

            foreach (var result in results)
            {
                // assign rank to each result
                // manufacturers ranked by number of products in the database (stored in cache)
                result.MRn = searchCache.ManufacturerOrder.TryGetValue(result.ManufacturerId, out var manufacturerRank) ? manufacturerRank : 0;
                // categories ordered by name
                result.CRn = searchCache.CategoryOrder.TryGetValue(result.CategoryId, out var categoryRank) ? categoryRank : 0;
                // parent categories ordered by name
                result.PCRn = result.ParentCategoryId.HasValue && searchCache.CategoryOrder.TryGetValue(result.ParentCategoryId.Value, out var parentCategoryRank) ? (long?)parentCategoryRank : null;
            }

            // calculate product rank, i.e. order of products shown for a specific manufacturer in specific category (two at a time per category/manufacturer, ordered by weight)
            foreach (var mc in results.GroupBy(p => new { p.ResultBlock, p.CategoryId, p.ManufacturerId }))
            {
                int p = 2;
                // use weight to decide which products go first within this manufacturer and category
                foreach (var product in mc.OrderByDescending(o => o.Weight).ThenBy(o => o.ProductId))
                {
                    product.PRn = p / 2; // two products at a time
                    p++;
                }
            }

            // calculate final order
            return results
                .OrderBy(r => r.ResultBlock) // first show products using their primary category (block 0), then secondary categories (block 1)
                .ThenBy(r => r.PRn) // product rank, show all products with rank 1 first, then 2 etc.
                .ThenBy(r => r.CRn) // rotate by primary category
                .ThenBy(r => r.PCRn) // rotate by secondary category
                .ThenBy(r => r.MRn) // rotate by manufacturer
                .ThenBy(r => r.ProductId)
                .Select(c => new CategoryResult
                {
                    CategoryId = c.CategoryId,
                    CRn = c.CRn,
                    ManufacturerId = c.ManufacturerId,
                    MRn = c.MRn,
                    ParentCategoryId = c.ParentCategoryId,
                    PCRn = c.PCRn,
                    PRn = c.PRn,
                    ProductId = c.ProductId,
                    ResultBlock = c.ResultBlock,
                    Weight = c.Weight
                }).ToList();
        }
    }
}