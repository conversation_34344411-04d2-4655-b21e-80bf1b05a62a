﻿using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.Extensions.Caching.Distributed;
using Newtonsoft.Json;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;

namespace BIMsmithMarket.Services
{
    public class CacheService : ICacheService
    {
        private readonly IDistributedCache _cache = RedisCacheProvider.GetRedisCacheInstance();
        private readonly RedisOptions _redisOptions = RedisCacheProvider.GetRedisOptions();

        public T Get<T>(string key)
        {
            var value = _cache.GetString(key);

            if (value != null)
            {
                return JsonConvert.DeserializeObject<T>(value);
            }

            return default;
        }

        public T Set<T>(string key, T value, int secondsToExpire)
        {
            var options = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(secondsToExpire),
                SlidingExpiration = TimeSpan.FromSeconds(secondsToExpire)
            };

            _cache.SetString(key, JsonConvert.SerializeObject(value), options);

            return value;
        }

        public bool Contains(string key)
        {
            var value = _cache.Get(key);
            return value != null;
        }

        public void Remove(string key)
        {
            _cache.Remove(key);
        }

        public void RemoveAll()
        {
            ICollection<string> cacheKeys = GetKeys();
            foreach (string cacheKey in cacheKeys)
            {
                Remove(cacheKey);
            }
        }

        public void RemoveAllByPattern(string pattern)
        {
            ICollection<string> cacheKeys = GetKeys(pattern);
            foreach (string cacheKey in cacheKeys)
            {
                Remove(cacheKey);
            }
        }

        public ICollection<string> GetKeys(string pattern = "*")
        {
            using (ConnectionMultiplexer redis = ConnectionMultiplexer.Connect($"{_redisOptions.Host}:{_redisOptions.Port},allowAdmin={_redisOptions.AllowAdmin}"))
            {
                var redisServer = redis.GetServer($"{_redisOptions.Host}:{_redisOptions.Port}");
                var keys = redisServer.Keys(_redisOptions.DefaultDatabase, pattern).Select(key => ((string)key).Remove(0, _redisOptions.Instance.Length)).ToArray();
                return keys;
            }
        }
    }
}