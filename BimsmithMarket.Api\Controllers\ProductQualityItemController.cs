﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// Product Quality Item Controller
    /// </summary>
    [Route("api/[controller]/[action]/{id?}")]
    public class ProductQualityItemController : BaseApiController
    {
        private readonly IProductQualityItemService _productQualityItemService;
        private readonly IUploadService _uploadService;
        private readonly IWebHostEnvironment _webHostEnvironment;

        public ProductQualityItemController(
            IProductQualityItemService productQualityItemService,
            IUploadService uploadService,
            IWebHostEnvironment webHostEnvironment
            )
        {
            _productQualityItemService = productQualityItemService;
            _uploadService = uploadService;
            _webHostEnvironment = webHostEnvironment;
        }

        /// <summary>
        /// Get list of Product Quality icon urls
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("ProductQualityIcons")]
        public IActionResult CertificateIcons()
        {
            string folderImages = Path.Combine(_webHostEnvironment.WebRootPath, "assets/img/quality");
            var list = Directory.GetFiles(folderImages).Select(a => "/assets/img/quality/" + Path.GetFileName(a));
            return Ok(list);
        }


        /// <summary>
        /// Upload Product Quality icon
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ActionName("UploadIcon")]
        public async Task<IActionResult> UploadIcon()
        {
            if (!(Request.HasFormContentType && Request.Form.Files.Any()))
                return BadRequest("Mime Multipart Content missing");

            var iconUrl = await _uploadService.SaveIcoToBlobAsync(Request.Form.Files[0]);
            if (string.IsNullOrEmpty(iconUrl))
            {
                return BadRequest("Something wrong ");
            }

            return Ok(new { iconUrl });
        }


        /// <summary>
        /// Get list of all Product Quality Items from database
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ActionName("List")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> List()
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _productQualityItemService.ListAsync(unitOfWork));
            }
        }



        /// <summary>
        /// Get detailed information about Product Quality by Id
        /// </summary>
        /// <param name="id">Id of Product Quality</param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("Get")]
        public async Task<IActionResult> Get(int id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _productQualityItemService.GetAsync(unitOfWork, id));
            }
        }

        /// <summary>
        /// Add new Product Quality: Role-ADMIN
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Add")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> Add([FromBody] AddProductQualityModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                var result = await _productQualityItemService.AddAsync(unitOfWork, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), model);

                CacheHelper.ClearSpecificCache("*/api/ProductQualityItem/List*");

                return Ok(result);
            }
        }

        /// <summary>
        /// Edit Product Quality: Role-ADMIN
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ActionName("Edit")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> Edit([FromBody] EditProductQualityModel model)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                await _productQualityItemService.EditAsync(unitOfWork, AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier), model);

                CacheHelper.ClearSpecificCache("*/api/ProductQualityItem/List*");

                return Ok();
            }
        }

        /// <summary>
        /// Delete Product Quality: Role-ADMIN
        /// </summary>
        /// <param name="id">Product Quality id</param>
        /// <returns></returns>
        [HttpDelete]
        [ActionName("Delete")]
        [Authorize(Roles = DbConstants.AdminRole)]
        public async Task<IActionResult> Delete(int id)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                await _productQualityItemService.DeleteAsync(unitOfWork, id);

                CacheHelper.ClearSpecificCache("*/api/ProductQualityItem/List*");

                return Ok();
            }
        }
    }
}
