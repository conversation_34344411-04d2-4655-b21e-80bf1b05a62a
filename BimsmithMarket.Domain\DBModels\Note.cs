﻿using BIMsmithMarket.Domain.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.DBModels
{
    public class Note : BaseEntity
    {
        [StringLength(100)]
        public string Name { get; set; }

        public DateTime? DateToNotifyUsers { get; set; }

        public DateTime? DateIsEffectiveTill { get; set; }

        public EntityType EntityType { get; set; }

        public int EntityId { get; set; }

        public virtual List<NoteNotificationUser> NotificationList { get; set; }

        public Note()
        {
            NotificationList = new();
        }
    }
}