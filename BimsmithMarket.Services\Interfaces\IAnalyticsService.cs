﻿using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.Analytics;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IAnalyticsService
    {
        Task<bool> HasAnalyticsManufacturerAccessAsync(string userId, string email, int manufactuterId, ProjectType projectType);

        Task<List<DetailManufacturerDictionaryDto>> GetDetailManufacturerDictionaryAsync(IUnitOfWork unitOfWork);

        Task<List<ProductManufacturerDictionaryDto>> GetProductManufacturerDictionaryAsync(IUnitOfWork unitOfWork);

        Task<AnalyticsLocationModel> GetLocationByIpAsync(string ip);

        Task<bool> SaveSalesForceEventToAnalyticsAsync(SalesForceUserEventViewModel model);

        Task<AnalyticsPaginationListDto<AnalyticsRequestPricingUserListDto>> RequestPricingUserListAsync(
            int manufacturerId,
            IUnitOfWork unitOfWork,
            int offset = 0,
            int count = 10);

        Task<AnalyticsUserDataManufacturerDto[]> UserDataManufacturersAsync(IUnitOfWork unitOfWork);
    }
}