﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto.DropboxDto;
using Mapster;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public static class DropboxMap
    {
        public static void Register(TypeAdapterConfig config)
        {
            config.ForType<DropboxOAuthResponseDto, DropboxSetting>()
               .Map(d => d.AccessToken, s => s.Access_token)
               .Map(d => d.AccountId, s => s.Account_id)
               .Map(d => d.ExpiresIn, s => s.Expires_in)
               .Map(d => d.Scope, s => s.Scope)
               .Map(d => d.TokenType, s => s.Token_type);
        }
    }
}