﻿using Azure.Storage.Blobs;
using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.Domain.Constants;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Core.Helpers
{
    public static class ImageHelper
    {
        public static async Task<string> GetStripeImagesAsync(
            List<string> imageUrls,
            string serverFolder,
            string apiUrl,
            int orderId)
        {
            AzureStorageService azureBlobProvider = new AzureStorageService(
                ConfigurationHelper.GetValue("BimsmithStorageConnectionString"),
                ConfigurationHelper.GetValue("Environment"),
                bool.Parse(ConfigurationHelper.GetValue("IsDevEnvironment")),
                false);
            var filesContainer = azureBlobProvider.GetContainerByName(AzureStorageConstants.PhotosContainer);
            List<Image> imgs = new List<Image>();
            foreach (var url in imageUrls)
            {
                var filename = Path.GetFileName(url);
                BlobClient blobFile = filesContainer.GetBlobClient(filename);
                if (await blobFile.ExistsAsync())
                {
                    using (var ms = new MemoryStream())
                    {
                        await blobFile.DownloadToAsync(ms);
                        ms.Flush();
                        Image img = Image.FromStream(ms);
                        imgs.Add(img);
                    }
                }
            }

            if (!imgs.Any())
                return null;
            var actualHeight = imgs.Sum(x => x.Height) / imgs.Count;
            var actualWidth = imgs.Sum(x => x.Width) / imgs.Count;
            var size = new Size(actualWidth, actualHeight);

            List<Image> imgsResized = new List<Image>();
            foreach (var img in imgs)
            {
                imgsResized.Add(resizeImage(img, size));
                img.Dispose();
            }


            int width = imgsResized.Sum(x => x.Width);
            int height = imgsResized.Max(x => x.Height);

            Bitmap resultImg = new Bitmap(width, height);
            Graphics g = Graphics.FromImage(resultImg);
            g.Clear(ColorTranslator.FromHtml("#F9B61E"));
            int startx = 0;
            foreach (var img in imgsResized)
            {
                g.DrawImage(img, new Point(startx, 0));
                startx += img.Width;
                img.Dispose();
            }

            var path = serverFolder + orderId + ".jpg";
            if (!Directory.Exists(serverFolder))
                Directory.CreateDirectory(serverFolder);

            if (File.Exists(path))
                File.Delete(path);

            resultImg.Save(path, System.Drawing.Imaging.ImageFormat.Jpeg);
            resultImg.Dispose();
            return apiUrl + @"assets/img/StripeImgs/" + orderId + ".jpg";
        }


        private static Image resizeImage(Image imgToResize, Size size)
        {
            //Get the image current width  
            int sourceWidth = imgToResize.Width;
            //Get the image current height  
            int sourceHeight = imgToResize.Height;
            float nPercentW = 0;
            float nPercentH = 0;
            //Calulate  width with new desired size  
            nPercentW = (size.Width / (float)sourceWidth);
            //Calculate height with new desired size  
            nPercentH = (size.Height / (float)sourceHeight);

            //New Width  
            int destWidth = (int)(sourceWidth * nPercentW);
            //New Height  
            int destHeight = (int)(sourceHeight * nPercentH);
            Bitmap b = new Bitmap(destWidth, destHeight);
            Graphics g = Graphics.FromImage(b);
            g.InterpolationMode = InterpolationMode.HighQualityBicubic;
            // Draw image with new width and height  
            g.DrawImage(imgToResize, 0, 0, destWidth, destHeight);
            g.Dispose();
            return b;
        }
    }
}
