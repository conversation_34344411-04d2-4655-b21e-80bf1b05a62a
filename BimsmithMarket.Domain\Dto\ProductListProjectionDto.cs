﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Enums;
using System;

namespace BIMsmithMarket.Domain.Dto
{
    public class ProductListProjectionDto
    {
        public int id { get; set; }

        public string name { get; set; }

        public string externalProductId { get; set; }

        public string description { get; set; }

        public float weight { get; set; }

        public float productRating { get; set; }

        public float contentRating { get; set; }

        public ULSyncStatus ulSyncStatus { get; set; }

        public string ulURL { get; set; }

        public bool isFeatured { get; set; }

        public bool published { get; set; }

        public bool publishToPartner { get; set; }

        public bool staging { get; set; }

        public string regionIds { get; set; }

        public string stateId { get; set; }

        public dynamic productLine { get; set; }

        public dynamic category { get; set; }

        public dynamic categories { get; set; }

        public dynamic externalCertificates { get; set; }

        public dynamic qualityItems { get; set; }

        public string photoUrl { get; set; }

        public string manufacture { get; set; }

        public int manufacturerId { get; set; }

        public string manufacturerVanityURL { get; set; }

        public string manufactureLogo { get; set; }

        public DateTime? updateDate { get; set; }

        public bool displaySwatchboxProductOnProductPage { get; set; }

        public bool displaySwatchboxProductOnMicrosite { get; set; }

        public dynamic externalMasterformatIds { get; set; }

        public HealthCheckStatus healthCheckStatus { get; set; }

        public string userName { get; set; }
    }
}