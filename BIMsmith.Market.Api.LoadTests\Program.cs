﻿using BIMsmithMarketApi.LoadTests.Models;
using NBomber.Contracts;
using NBomber.CSharp;
using NBomber.Plugins.Http;
using NBomber.Plugins.Http.CSharp;
using System;
using System.Collections.Generic;

namespace BIMsmithMarketApi.LoadTests
{
    class Program
    {
        static void Main(string[] args)
        {
            string marketApiBaseUrl = "https://api-market-dev.bimsmith.com/";
            int warmUpDurationInSeconds = 5;
            int rate = 50;
            int simulationDurationInSeconds = 300;

            List<ScenarioConfiguration> scenarioConfigurations = new List<ScenarioConfiguration>
            {
                new ScenarioConfiguration { Name = "General Search Without Query", Endpoint = $"{marketApiBaseUrl}api/Common/GeneralSearch?count=24&offset=0&published=true&regionId=93&returnFilters=false&sortType=4&startersPosition=3" },
                new ScenarioConfiguration { Name = "General Search With Query", Endpoint = $"{marketApiBaseUrl}api/Common/GeneralSearch?count=24&offset=0&published=true&q=door&regionId=93&returnFilters=false&sortType=4&startersPosition=3" },
                new ScenarioConfiguration { Name = "Category Tree", Endpoint = $"{marketApiBaseUrl}api/Category/Tree" },
                new ScenarioConfiguration { Name = "Feature Settings List", Endpoint = $"{marketApiBaseUrl}api/FeatureSetting/PublicList" },
                new ScenarioConfiguration { Name = "Product Get", Endpoint = $"{marketApiBaseUrl}api/Product/PublicGet/46625?langCode=en-US" },
                new ScenarioConfiguration { Name = "Microsite List", Endpoint = $"{marketApiBaseUrl}/api/ProductAndStarter/Microsite/List?published=true&includeHideOnMicrosite=false&manufacturerName=USG&startersPosition=1&offset=0&count=32" }
            };

            foreach (ScenarioConfiguration scenarioConfiguration in scenarioConfigurations)
            {
                IStep step = Step.Create(
                    scenarioConfiguration.Name,
                    clientFactory: HttpClientFactory.Create(scenarioConfiguration.Name),
                    execute: context =>
                    {
                        HttpRequest request = Http.CreateRequest("GET", scenarioConfiguration.Endpoint);
                        return Http.Send(request, context);
                    });

                Scenario scenario = ScenarioBuilder
                    .CreateScenario($"Market API Test {scenarioConfiguration.Name}", step)
                    .WithWarmUpDuration(TimeSpan.FromSeconds(warmUpDurationInSeconds))
                    .WithLoadSimulations(
                        Simulation.InjectPerSec(rate, TimeSpan.FromSeconds(simulationDurationInSeconds))
                );

                NBomberRunner
                    .RegisterScenarios(scenario)
                    .Run();
            }
        }
    }
}