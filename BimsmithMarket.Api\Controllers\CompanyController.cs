﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
#if !DEBUG
    [Authorize(Roles = DbConstants.AdminRole)]
#endif
    /// <summary>
    /// 
    /// </summary>
    [Route("api/[controller]/[action]")]
    public class CompanyController : BaseApiController
    {
        private readonly ICompanyService _companyService;

        public CompanyController(ICompanyService companyService)
        {
            _companyService = companyService;
        }

        /// <summary>
        /// Return companies List
        /// </summary>
        /// <param name="offset"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        [HttpGet]
        [ActionName("List")]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> List(int offset = 0, int count = 20)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _companyService.ListAsync(unitOfWork, offset, count));
            }
        }

        /// <summary>
        /// Delete company by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost("{id}")]
        [ActionName("Delete")]
        public async Task<IActionResult> Delete(int id)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var company = await unitOfWork.CompanyRepository.GetByIdAsync(id);
                if (company != null)
                {
                    unitOfWork.CompanyRepository.Delete(company);
                    await unitOfWork.SaveAsync();

                    CacheHelper.ClearSpecificCache("*/api/Company/List*");

                    return Ok();
                }
                return BadRequest("Can't find company or something went wrong.");
            }
        }


        /// <summary>
        /// Add new company
        /// </summary>
        /// <param name="model">The model</param>
        /// <returns>company model</returns>
        [HttpPost]
        [ActionName("Add")]
        public async Task<IActionResult> Add([FromBody] AddCompanyModel model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var result = await _companyService.AddAsync(unitOfWork, model);

                CacheHelper.ClearSpecificCache("*/api/Company/List*");

                return Ok(result);
            }
        }

        /// <summary>
        /// Edit Company
        /// </summary>
        /// <param name="model">the model</param>
        /// <returns> company model</returns>
        [HttpPost]
        [ActionName("Edit")]
        public async Task<IActionResult> Edit([FromBody] EditCompanyModel model)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                var result = await _companyService.EditAsync(unitOfWork, model);

                CacheHelper.ClearSpecificCache("*/api/Company/List*");

                return Ok(result);
            }
        }


        [HttpGet]
        [ActionName("GetExcelData")]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> GetExcelData(int fileId)
        {
            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _companyService.GetExcelDataAsync(unitOfWork, fileId));
            }
        }

        /// <summary>
        /// Sync selected company users
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        [Authorize(Roles = DbConstants.AdminRole)]
        [HttpGet]
        [ActionName("Sync")]
        public async Task<IActionResult> SyncAsync(int id)
        {
            var documentDBAuthKey = ConfigurationHelper.GetValue("DocumentDBAuthKey");
            var bimsmith = ConfigurationHelper.GetValue("LoginDomain");
            var controller = ConfigurationHelper.GetValue("CompaniesSyncEndPoint");

            using (IUnitOfWork unitOfWork = UnitOfWork.Create())
            {
                return Ok(await _companyService.SyncAsync(unitOfWork, documentDBAuthKey, bimsmith, controller, id));
            }
        }

        /// <summary>
        /// Companies the information.
        /// </summary>
        /// <param name="model">The model.</param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost]
        [ActionName("CompanyInfo")]
        public async Task<IActionResult> CompanyInfo(CompanyQueryParams model)
        {
            var documentDBAuthKey = ConfigurationHelper.GetValue("DocumentDBAuthKey");
            if (documentDBAuthKey != model.DocumentDBAuthKey)
            {
                return Unauthorized();
            }

            using (IUnitOfWork unitOfWork = UnitOfWork.Create(disableAllTracking: true))
            {
                return Ok(await _companyService.CompanyInfoAsync(unitOfWork));
            }
        }
    }
}