﻿using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Enums;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Search.FullTextSearch;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;

namespace BIMsmithMarket.Services.Search
{
    /// <summary>
    /// Options for product search
    /// </summary>
    public class ProductSearchOptions
    {
        public List<int> ManufacturerIds { get; set; }

        [DefaultValue(-1)]
        public int CategoryId { get; set; } = -1;

        [DefaultValue(-1)]
        public int ProductLineId { get; set; } = -1;

        [DefaultValue(-1)]
        public int CisfbId { get; set; } = -1;

        [DefaultValue(-1)]
        public int ExternalMasterformatId { get; set; } = -1;

        [DefaultValue(-1)]
        public int OmniclassId { get; set; } = -1;

        [DefaultValue(-1)]
        public int UniclassId { get; set; } = -1;

        [DefaultValue(-1)]
        public int UniformatId { get; set; } = -1;

        [DefaultValue(-1)]
        public int DetailId { get; set; } = -1;

        public List<int> ProductFileTypeIds { get; set; }

        public List<int> ExternalCertificateIds { get; set; }

        public List<int> QualityItemIds { get; set; }

        public string RegionId { get; set; }

        public string StateId { get; set; }

        public string Query { get; set; }

        public string Keyword { get; set; }

        public bool Featured { get; set; }

        public bool Published { get; set; }

        public bool PublishedOnCustomMicrosite { get; set; }

        public bool Staging { get; set; }

        [DefaultValue(true)]
        public bool IncludeHideOnMicrosite { get; set; }

        public bool SamplesAvailable { get; set; }

        public StartersPosition StartersPosition { get; set; }

        public string CategoryVanityUrl { get; set; }

        public string AttachUrl { get; set; }

        public ProductSortType SortType { get; set; }

        [DefaultValue(0)]
        public int Skip { get; set; }

        [DefaultValue(24)]
        public int Take { get; set; }

        [DefaultValue(-1)]
        public int RotationMethod { get; set; } = -1;

        [DefaultValue(SearchCache.DefaultLanguage)]
        public string LangCode { get; set; } = SearchCache.DefaultLanguage;

        public bool? SearchManufacturerChildProducts { get; set; }

        //This parameter used only for Starters filtering on Microsites
        public int ForgeProductLineId { get; set; }

        // internal use only
        public bool GenerateExplain { get; set; }

        public ICollection<MicrositeListWithStarterFiltersKeyStatDto> KeyStats { get; set; }

        public SearchSource SearchSource { get; set; } = SearchSource.Market;

        public bool OnlyFree { get; set; }

        public bool IsSpecialized()
        {
            return ProductLineId > 0 ||
                    CisfbId > 0 ||
                    ExternalMasterformatId > 0 ||
                    OmniclassId > 0 ||
                    UniclassId > 0 ||
                    UniformatId > 0 ||
                    DetailId > 0 ||
                    ProductFileTypeIds != null && ProductFileTypeIds.Any() ||
                    ExternalCertificateIds != null && ExternalCertificateIds.Any() ||
                    QualityItemIds != null && QualityItemIds.Any() ||
                    RegionId != null && RegionId.Any() ||
                    StateId != null && StateId.Any() ||
                    !string.IsNullOrWhiteSpace(AttachUrl);
        }

        public void Normalize()
        {
            if (string.IsNullOrWhiteSpace(LangCode))
            {
                LangCode = SearchCache.DefaultLanguage;
            }
        }
    }

    /// <summary>
    /// Class containing a projection of a search result
    /// </summary>
    /// <typeparam name="P">Type of projection (use object for dynamic types)</typeparam>
    public class ProductSearchResult<P> where P : class
    {
        public int ProductId { get; set; }

        public string StarterId { get; set; }

        public string ItemType { get; set; }

        public P Projection { get; set; }

        public int ManufacturerId { get; set; }
    }

    public interface IProductSearchResults
    {
        List<int> AllResultsIds { get; }
        int CountOfProducts { get; }
        int CountOfSecondaryResults { get; }
        int CountOfStarters { get; }
        long SearchTime { get; }

        IQueryable<Product> AllResults(IUnitOfWork unitOfWork);

        IEnumerable<ProductSearchResult<P>> PrimaryResults<P>(
            int skip, int take, IUnitOfWork unitOfWork,
            System.Linq.Expressions.Expression<Func<Product, ProductSearchResult<P>>> productProjection,
            System.Linq.Expressions.Expression<Func<Starter, ProductSearchResult<P>>> starterProjection) where P : class;

        IEnumerable<ProductSearchResult<P>> SecondaryResults<P>(
            System.Linq.Expressions.Expression<Func<Product, ProductSearchResult<P>>> productProjection,
            int secondarySkip, int secondaryTake, IUnitOfWork unitOfWork) where P : class;

        Dictionary<string, List<int>> SecondaryResultsByPhrase();
    }

    public class ProductSearchResultCached
    {
        public int ProductId { get; set; }
        public string StarterId { get; set; }
        public string ItemType { get; set; }
    }

    public class ProductSearchResultsCached : IProductSearchResults
    {
        public List<int> AllResultsIds { get; set; }
        public List<ProductSearchResultCached> PrimaryResultsIds { get; set; }
        public List<int> SecondaryResultsIds { get; set; }
        public Dictionary<string, List<int>> SecondaryResultsIdsByPhrase { get; set; }
        public int CountOfProducts { get; set; }
        public int CountOfSecondaryResults { get; set; }
        public int CountOfStarters { get; set; }

        public long SearchTime { get; set; }

        public IQueryable<Product> AllResults(IUnitOfWork unitOfWork)
        {
            return unitOfWork.ProductRepository.GetAll().Where(p => AllResultsIds.Contains(p.Id));
        }

        public IEnumerable<ProductSearchResult<P>> PrimaryResults<P>(
            int skip, int take, IUnitOfWork unitOfWork,
            System.Linq.Expressions.Expression<Func<Product, ProductSearchResult<P>>> productProjection,
            System.Linq.Expressions.Expression<Func<Starter, ProductSearchResult<P>>> starterProjection) where P : class
        {
            // take portion of results we're interested in
            var results = PrimaryResultsIds.Skip(skip).Take(take).ToList();

            // lookup IDs of products and starters
            var productsInResults = results.Where(r => r.ProductId != 0).Select(p => p.ProductId).ToList();
            var startersInResults = results.Where(r => r.StarterId != null).Select(s => s.StarterId).ToList();

            // retrieve from database and apply projection to products and starters
            var products = productProjection != null ? unitOfWork.ProductRepository.GetAll().AsSplitQuery().Where(p => productsInResults.Contains(p.Id)).Select(productProjection).ToDictionary(p => p.ProductId, p => p) : null;
            var starters = starterProjection != null ? unitOfWork.StarterRepository.GetAll().AsSplitQuery().Where(s => startersInResults.Contains(s.Id)).Select(starterProjection).ToDictionary(p => p.StarterId, p => p) : null;

            // return in order of results
            var finalResults = new List<ProductSearchResult<P>>();
            foreach (var r in results)
            {
                if (r.ProductId != 0 && products != null && products.TryGetValue(r.ProductId, out var projectedProduct))
                {
                    finalResults.Add(projectedProduct);
                }
                else if (r.StarterId != null && starters != null && starters.TryGetValue(r.StarterId, out var projectedStarter))
                {
                    finalResults.Add(projectedStarter);
                }
            }
            return finalResults;
        }

        public IEnumerable<ProductSearchResult<P>> SecondaryResults<P>(
            System.Linq.Expressions.Expression<Func<Product, ProductSearchResult<P>>> productProjection,
            int secondarySkip, int secondaryTake, IUnitOfWork unitOfWork) where P : class
        {
            if (SecondaryResultsIds != null && SecondaryResultsIds.Any())
            {
                var scopeResults = SecondaryResultsIds.Skip(secondarySkip).Take(secondaryTake).ToList();
                return unitOfWork.ProductRepository.GetAll().Where(p => SecondaryResultsIds.Contains(p.Id)).Select(productProjection).ToList();
            }
            else
            {
                return new List<ProductSearchResult<P>>();
            }
        }

        public Dictionary<string, List<int>> SecondaryResultsByPhrase()
        {
            return SecondaryResultsIdsByPhrase;
        }
    }

    /// <summary>
    /// Represents results of product search
    /// </summary>
    public class ProductSearchResults : IProductSearchResults
    {
        /// <summary>
        /// Constructor for results which can be pre-sorted on the database side (by rating, created date etc.)
        /// </summary>
        /// <param name="options">Search options</param>
        /// <param name="allResults">All results of the query</param>
        /// <param name="preSorted">If set general sort will not be applied</param>
        public ProductSearchResults(ProductSearchOptions options, IQueryable<Product> allResults, bool preSorted, SearchResults searchResults)
        {
            _skip = options.Skip;
            _take = options.Take;
            _sortType = options.SortType;
            _preSorted = preSorted;
            _allResults = allResults;
            SearchResults = searchResults;
        }

        /// <summary>
        /// Constructor for results which have to be sorted on the application side by calculated rank
        /// </summary>
        /// <param name="options">Search options</param>
        /// <param name="allResults">All results of the query, used to derive filters</param>
        /// <param name="primaryResults">Ordered list of ProductIds to be shown in results</param>
        /// <param name="originalInput">Original collection (unfiltered) for data retrieval</param>
        public ProductSearchResults(
            ProductSearchOptions options,
            IQueryable<Product> allResults,
            IEnumerable<RankedResults.ProductRank> primaryResults,
            IEnumerable<RankedResults.ProductRank> secondaryResults,
            IQueryable<Product> originalInput, SearchResults searchResults
        ) : this(options, allResults, false, searchResults)
        {
            _originalInput = originalInput;
            _primaryResults = primaryResults;
            _secondaryResults = secondaryResults;
        }

        /// <summary>
        /// Queryable set of ALL results (in arbitrary order and including outside of bounds)
        /// </summary>
        private IQueryable<Product> _allResults { get; }

        public IQueryable<Product> AllResults(IUnitOfWork _)
        {
            return _allResults;
        }

        private List<int> _allResultsIds;

        public List<int> AllResultsIds
        {
            get
            {
                if (_allResultsIds == null)
                {
                    _allResultsIds = _allResults.Select(r => r.Id).ToList();
                }
                return _allResultsIds;
            }
        }

        /// <summary>
        /// Class containing information about the query and results for troubleshooting
        /// </summary>
        public SearchResults SearchResults { get; }

        /// <summary>
        /// Retrieves a custom projection of results for display (ordered and bound)
        /// </summary>
        /// <param name="productProjection">Function projecting each Product result into desired representation (will be called over EF)</param>
        /// <returns></returns>
        public IEnumerable<ProductSearchResult<P>> PrimaryResults<P>(int skip, int take, IUnitOfWork unitOfWork,
            System.Linq.Expressions.Expression<Func<Product, ProductSearchResult<P>>> productProjection,
            System.Linq.Expressions.Expression<Func<Starter, ProductSearchResult<P>>> starterProjection) where P : class
        {
#if(DEBUG)
            if (SearchResults?.Options != null && (skip != SearchResults.Options.Skip || take != SearchResults.Options.Take))
            {
                Debug.WriteLine("Incompatible result objects");
            }
#endif
            CountOfProducts = _allResults.Count();

            // _starterPosition says on which places in FINAL results should the starters be, _skip and _take also refer to FINAL result order (i.e. products and starters combined)
            // if there are not enough products we have to shift starters after the end so that there are no gaps in position indexes
            if (_starterPositions != null && _starterPositions.Any())
            {
                var lastValidPosition = CountOfProducts + _starterPositions.Count(s => s.Key <= CountOfProducts);
                var startersAfterEnd = _starterPositions.Where(s => s.Key >= lastValidPosition).OrderBy(s => s.Key).ToList();
                foreach (var starterAfterEnd in startersAfterEnd)
                {
                    // remove and reinsert starter at the last valid end position
                    _starterPositions.Remove(starterAfterEnd.Key);
                    _starterPositions.Add(lastValidPosition++, starterAfterEnd.Value);
                }
                /*
                #if (DEBUG)
                                var totalResults = CountOfProducts + CountOfStarters;
                                if (_starterPositions.Any(s => s.Key >= totalResults))
                                {
                                    throw new Exception("Invalid starter locations");
                                }
                #endif
                */
            }

            var startersBefore = _starterPositions == null ? 0 : _starterPositions.Count(s => s.Key < _skip);
            var startersWithin = _starterPositions == null ? 0 : _starterPositions.Count(s => s.Key >= _skip && s.Key < _skip + _take);
            var productsSkip = _skip - startersBefore;
            var productsTake = _take - startersWithin;

            List<ProductSearchResult<P>> fullResults = null;

            if (_primaryResults != null)
            {
                fullResults = ProjectResults(_primaryResults, productsSkip, productsTake, productProjection);
            }
            else if (_preSorted)
            {
                // pre-sorted query
                fullResults = _allResults.AsSplitQuery().Skip(productsSkip).Take(productsTake).Select(productProjection).ToList();
            }
            else
            {
                // database-level sorting
                fullResults = QueryableSorting(_sortType, _allResults).AsSplitQuery().Skip(productsSkip).Take(productsTake).Select(productProjection).ToList();
            }

            if (_sortType == ProductSortType.Relevant)
                fullResults = RotateResults(fullResults);

            if (startersWithin != 0)
            {
                // insert starters at positions specified in _starterPositions
                var compiledProjection = starterProjection.Compile();
                foreach (var starter in _starterPositions.Where(s => s.Key >= _skip && s.Key < _skip + _take).OrderBy(s => s.Key))
                {
                    fullResults.Insert(Math.Min(fullResults.Count, starter.Key - _skip), compiledProjection(starter.Value));
                }
            }

            return fullResults;
        }
       
        /// <summary>
        /// Retrieves low quality results to include in a separate section at the bottom
        /// </summary>
        /// <param name="productProjection">Function projecting each Product result into desired representation (will be called over EF)</param>
        /// <returns></returns>
        public IEnumerable<ProductSearchResult<P>> SecondaryResults<P>(
            System.Linq.Expressions.Expression<Func<Product, ProductSearchResult<P>>> productProjection,
            int secondarySkip, int secondaryTake, IUnitOfWork unitOfWork) where P : class
        {
            if (_secondaryResults != null && _secondaryResults.Any(s => string.IsNullOrWhiteSpace(s.Query)))
            {
                return ProjectResults(_secondaryResults.Where(s => string.IsNullOrWhiteSpace(s.Query)), secondarySkip, secondaryTake, productProjection);
            }
            else
            {
                return new List<ProductSearchResult<P>>();
            }
        }

        /// <summary>
        /// Return list of sub-phrases for secondary results
        /// </summary>
        /// <returns></returns>
        public Dictionary<string, List<int>> SecondaryResultsByPhrase()
        {
            if (_secondaryResults != null)
            {
                return _secondaryResults.Where(s => !string.IsNullOrWhiteSpace(s.Query)).GroupBy(s => s.Query).ToDictionary(s => s.Key, s => s.OrderByDescending(q => q.FinalScore).Select(t => t.Id).ToList());
            }
            else
            {
                return new Dictionary<string, List<int>>();
            }
        }

        private List<ProductSearchResult<P>> ProjectResults<P>(IEnumerable<RankedResults.ProductRank> orderedResults, int skip, int take,
            System.Linq.Expressions.Expression<Func<Product, ProductSearchResult<P>>> productProjection) where P : class
        {
            // application-level ranking, remove duplicates if any
            var uniqueResults = orderedResults.Select((id, pos) => new KeyValuePair<int, int>(id.Id, pos)).GroupBy(v => v.Key).Select(v => v.OrderBy(x => x.Value).First().Key);
            var displayedResults = uniqueResults;
            if (skip != -1 && take != -1)
                displayedResults = uniqueResults.Skip(skip).Take(take);
            var displayOrder = displayedResults.Select((id, pos) => new KeyValuePair<int, int>(id, pos)).ToDictionary(v => v.Key, v => v.Value);
            return _originalInput.AsSplitQuery().Where(r => displayedResults.Contains(r.Id)).Select(productProjection).ToList().OrderBy(r => displayOrder[r.ProductId]).ToList();
        }

        public long SearchTime { get; protected set; }

        public ProductSearchResults LogTime(Stopwatch sw)
        {
            SearchTime = sw.ElapsedMilliseconds;
            return this;
        }

        public int CountOfProducts { get; private set; }
        public int CountOfSecondaryResults => _secondaryResults != null ? _secondaryResults.Count(s => !string.IsNullOrWhiteSpace(s.Query)) : 0;
        public int CountOfStarters => _starterPositions != null ? _starterPositions.Count : 0;

        protected readonly int _skip;
        protected readonly int _take;
        protected readonly ProductSortType _sortType;
        protected readonly IEnumerable<RankedResults.ProductRank> _primaryResults;
        protected readonly IEnumerable<RankedResults.ProductRank> _secondaryResults;
        protected IQueryable<Product> _originalInput;
        protected Dictionary<int, Starter> _starterPositions;
        protected bool _preSorted;

        public void IncludeStarters(ProductSearchOptions options,
                                    IQueryable<Starter> starters,
                                    IEnumerable<int> foundManufacturers,
                                    IUnitOfWork unitOfWork)
        {
            if (starters != null && ((options.ManufacturerIds != null && options.ManufacturerIds.Any()) ||
                                    !string.IsNullOrWhiteSpace(options.Query) ||
                                    options.CategoryId > 0 ||
                                    options.ProductLineId > 0)
                                 && string.IsNullOrWhiteSpace(options.AttachUrl))
            {
                var foundStarters = starters;
                bool hasFilter = false;
                var cache = SearchCache.Get(unitOfWork);

                if (options.ProductLineId > 0)
                {
                    foundStarters = foundStarters.Where(x => x.ForgeManufacturerId != null &&
                                                             x.ProductLines.Select(a => a.ProductLineId.ToString()).Contains(options.ProductLineId.ToString()));
                    hasFilter = true;
                }

                if (options.ManufacturerIds != null && options.ManufacturerIds.Any())
                {
                    string[] manufacturerForgeIds = cache.ManufacturersToForgeId.Where(x => options.ManufacturerIds.Contains(x.Key)).Select(x => x.Value).ToArray();
                    foundStarters = foundStarters.Where(s => s.ForgeManufacturerId != "" && manufacturerForgeIds.Contains(s.ForgeManufacturerId));
                    hasFilter = true;
                }

                if (!string.IsNullOrWhiteSpace(options.Query))
                {
                    var foundForgeIds = foundManufacturers.Where(f => cache.ManufacturersToForgeId.ContainsKey(f)).Select(f => cache.ManufacturersToForgeId[f]).ToList();

                    if (foundManufacturers.Any() && cache.GetSetting(ProductSearch.sFilterStartersByManufacturers, 1) == 1)
                    {
                        // only return starters from found manufacturers, even if they are not on forge (as otherwise we have poor results for "lg")
                        foundStarters = foundStarters.Where(a => foundForgeIds.Contains(a.ForgeManufacturerId));
                    }

                    foundStarters = foundStarters.Where(a => a.Name.Contains(options.Query) ||
                                                             a.ManufacturerName.Contains(options.Query) ||
                                                             a.Brand.Contains(options.Query) ||
                                                             foundForgeIds.Contains(a.ForgeManufacturerId));
                    hasFilter = true;
                }

                if (options.CategoryId > 0 && cache.CategoryNames.ContainsKey(options.CategoryId))
                {
                    var categoryName = cache.CategoryNames[options.CategoryId];
                    if (!Enum.TryParse(categoryName, true, out StarterOrientation orientation))
                        orientation = StarterOrientation.Undefined;
                    foundStarters = foundStarters.Where(a => a.Orientation == orientation);
                    hasFilter = true;
                }

                if (options.Published)
                {
                    foundStarters = foundStarters.Where(x => x.Published);
                }

                if (options.Staging)
                {
                    foundStarters = foundStarters.Where(x => x.Published || x.Staging);
                }

                switch (options.SortType)
                {
                    case ProductSortType.Alphabetically:
                        {
                            foundStarters = foundStarters.OrderBy(x => x.Name);
                            break;
                        }
                    default:
                        {
                            foundStarters = foundStarters.OrderByDescending(x => x.Weight);
                            break;
                        }
                }

                if (hasFilter)
                {
                    // this dictionary indicates on which positions in the final results should starters be put in
                    _starterPositions = new Dictionary<int, Starter>();
                    var i = 0;

                    switch (options.StartersPosition)
                    {
                        case StartersPosition.StartWithStarters:
                            {
                                foreach (var starter in foundStarters.ToList())
                                {
                                    _starterPositions.Add(i++, starter);
                                }
                                break;
                            }

                        case StartersPosition.TwoProductsTwoStarters:
                            {
                                foreach (var starter in foundStarters.ToList())
                                {
                                    _starterPositions.Add(i % 2 + 4 * (i / 2) + 2, starter); // 2, 3, 6, 7, 10, 11, 14, 15, 18, 19, ...
                                    i++;
                                }
                                break;
                            }

                        case StartersPosition.TwoProductsOneStarter:
                            {
                                int pos = 0;
                                foreach (var starter in foundStarters.ToList())
                                {
                                    i++;
                                    pos = 2 + (i - 1) * 3;
                                    _starterPositions.Add(pos, starter); // 2, 5, 8, 11, 14, ...
                                }
                                break;
                            }
                        case StartersPosition.Weighted:
                            {
                                var sortedArray = foundStarters.Select(a => new { a.Weight, a.Id }).ToList();
                                if (_primaryResults != null)
                                {
                                    sortedArray.AddRange(_primaryResults.Select(a => new { a.Weight, Id = a.Id.ToString() }).ToList());
                                }
                                else
                                {
                                    sortedArray.AddRange(_allResults.Select(a => new { a.Weight, Id = a.Id.ToString() }).ToList());
                                }

                                int pos = 0;
                                sortedArray = sortedArray.OrderByDescending(a => a.Weight).ToList();
                                foreach (var starter in foundStarters.ToList())
                                {
                                    pos = sortedArray.FindIndex(a => a.Id == starter.Id);
                                    _starterPositions.Add(pos, starter);
                                }
                                break;
                            }
                        default:
                            {
                                CountOfProducts = _allResults.Count();
                                i = CountOfProducts;
                                foreach (var starter in foundStarters.ToList())
                                {
                                    _starterPositions.Add(i++, starter);
                                }
                                break;
                            }

                    }
                }
            }
        }

        protected static IQueryable<Product> QueryableSorting(ProductSortType sortType, IQueryable<Product> results)
        {
            switch (sortType)
            {
                case ProductSortType.Relevant:
                    return results.OrderByDescending(a => a.Weight);
                case ProductSortType.ContentRating:
                    return results.OrderByDescending(a => a.ContentRating);
                case ProductSortType.ProductRating:
                    return results.OrderByDescending(a => a.ProductRating);
                case ProductSortType.LatestProducts:
                    return results.OrderByDescending(a => a.CreatedDate);
                case ProductSortType.LatestModified:
                    return results.OrderByDescending(a => a.ModifiedDate ?? a.CreatedDate);
                case ProductSortType.Id:
                    return results.OrderBy(a => a.Id);
                case ProductSortType.Alphabetically:
                    return results.OrderBy(a => a.Name);
                case ProductSortType.AlphabeticallyDistinct:
                    return results.OrderByDescending(a => a.Name);
                default:
                    throw new NotImplementedException();
            }
        }

        /// <summary>
        /// Return a plain object with results which can be serialized in cache
        /// </summary>
        public ProductSearchResultsCached GetForCache()
        {
            return new ProductSearchResultsCached
            {
                AllResultsIds = _allResults.Select(r => r.Id).ToList(),
                PrimaryResultsIds = PrimaryResults(SearchResults?.Options?.Skip ?? 0, SearchResults?.Options?.Take ?? 0, null,
                    p => new ProductSearchResult<ProductSearchResultCached>
                    {
                        ProductId = p.Id,
                        ManufacturerId = p.ManufacturerId,
                        Projection = new ProductSearchResultCached
                        {
                            ProductId = p.Id,
                            ItemType = "product"
                        }
                    }, p => new ProductSearchResult<ProductSearchResultCached>
                    {
                        StarterId = p.Id,
                        Projection = new ProductSearchResultCached
                        {
                            StarterId = p.Id,
                            ItemType = "starter"
                        }
                    }).Select(p => p.Projection).ToList(),
                SecondaryResultsIds = _secondaryResults != null && _secondaryResults.Any(s => string.IsNullOrWhiteSpace(s.Query)) ? _secondaryResults.Where(s => string.IsNullOrWhiteSpace(s.Query)).Select(s => s.Id).ToList() : null,
                SecondaryResultsIdsByPhrase = SecondaryResultsByPhrase(),
                CountOfProducts = CountOfProducts,
                CountOfStarters = CountOfStarters,
                CountOfSecondaryResults = CountOfSecondaryResults
            };
        }

        public List<ProductSearchResult<P>> RotateResults<P>(List<ProductSearchResult<P>> inputResults) where P : class
        {
            List<ProductSearchResult<P>> results = new();
            List<IGrouping<int, ProductSearchResult<P>>> grouppedResults = inputResults.GroupBy(x => x.ManufacturerId).ToList();
            int rotatedResultsCount = 0;
            int rotationTakePortion = 2;
            int elementsInGroupToSkip = 0;
            int rotationLimit = inputResults.Count;

            while (rotatedResultsCount < rotationLimit)
            {
                foreach (var group in grouppedResults)
                {
                    ProductSearchResult<P>[] groupElements = group.Skip(elementsInGroupToSkip).Take(rotationTakePortion).ToArray();
                    if (groupElements.Any())
                    {
                        results.AddRange(groupElements);
                        rotatedResultsCount += groupElements.Length;
                    }
                    if (rotatedResultsCount >= rotationLimit)
                        break;
                }
                elementsInGroupToSkip += rotationTakePortion;
            }

            return results;
        }
    }

    /// <summary>
    /// Utility class for performing product search
    /// </summary>
    public class ProductSearch
    {
        public static readonly string sSettingPrefix = "Product Search.";

        public static readonly string sPrimaryAlgorithmSetting = sSettingPrefix + "Primary Algorithm Version";
        public static readonly string sSecondaryAlgorithmSetting = sSettingPrefix + "Secondary Algorithm Version";
        public static readonly string sTertiaryAlgorithmSetting = sSettingPrefix + "Tertiary Algorithm Version";
        public static readonly string sAutocorrectSensitivitySetting = sSettingPrefix + "Autocorrect Sensitivity";
        public static readonly string sModelNumberKeyStatSetting = sSettingPrefix + "Model Number Key Stat";
        public static readonly string sSearchTextForUnitsSetting = sSettingPrefix + "Search Text for Units";
        public static readonly string sFreetextSearchCategory = sSettingPrefix + "Include Category in Freetext";
        public static readonly string sFreetextSearchManufacturer = sSettingPrefix + "Include Manufacturer in Freetext";
        public static readonly string sRequireAllTokensToMatch = sSettingPrefix + "Require All Tokens to Match";
        public static readonly string sFilterStartersByManufacturers = sSettingPrefix + "Filter Starters by Manufacturers";
        public static readonly string sUseLegacyDefaultView = sSettingPrefix + "Use Legacy Default View";

        public enum SearchAlgorithm
        {
            V7,
            V8,
            V9
        }

        private static readonly SearchAlgorithm _defaultPrimaryAlgorithm = SearchAlgorithm.V7;
        private static readonly SearchAlgorithm _defaultSecondaryAlgorithm = SearchAlgorithm.V8;
        private static readonly SearchAlgorithm _defaultTertiaryAlgorithm = SearchAlgorithm.V9;

        /// <summary>
        /// Execute a search for products
        /// </summary>
        /// <param name="options">Options for search</param>
        /// <param name="products">Collection to search</param>
        /// <param name="algorithm">Optional override algorithm</param>
        public static ProductSearchResults Search(ProductSearchOptions options, IQueryable<Product> products, IQueryable<Starter> starters, SearchCache searchCache, IUnitOfWork unitOfWork)
        {
            if (!Enum.TryParse<SearchAlgorithm>(searchCache.GetSetting(sPrimaryAlgorithmSetting, null), true, out var selectedAlgorithm))
            {
                selectedAlgorithm = _defaultPrimaryAlgorithm;
            }
            if (options.Query != null)
            {
                if (options.Query.StartsWith("!!"))
                {
                    if (!Enum.TryParse(searchCache.GetSetting(sTertiaryAlgorithmSetting, null), true, out selectedAlgorithm))
                    {
                        selectedAlgorithm = _defaultTertiaryAlgorithm;
                    }
                    options.Query = options.Query.Substring(2);
                }
                else if (options.Query.StartsWith("!"))
                {
                    if (!Enum.TryParse(searchCache.GetSetting(sSecondaryAlgorithmSetting, null), true, out selectedAlgorithm))
                    {
                        selectedAlgorithm = _defaultSecondaryAlgorithm;
                    }
                    options.Query = options.Query.Substring(1);
                }
            }
            var sw = Stopwatch.StartNew();
            ProductSearchResults results;

            switch (selectedAlgorithm)
            {
                case SearchAlgorithm.V7: // current
                case SearchAlgorithm.V8: // using synonyms 
                case SearchAlgorithm.V9:
                    results = FullTextSearch.SearchAlgorithm.SearchProducts(options, products.AsNoTracking(), unitOfWork, searchCache, selectedAlgorithm);
                    break;
                default:
                    throw new NotImplementedException();
            }

            if (starters != null)
            {
                results.IncludeStarters(options,
                                        starters,
                                        results.SearchResults != null && results.SearchResults.FoundManufacturers != null ? results.SearchResults.FoundManufacturers.Where(m => m.Value >= WeightsTable.ManufacturerBeelineThreshold).Select(m => m.Key) : new List<int>(),
                                        unitOfWork);
            }

            results.LogTime(sw);

            return results;
        }

        public static IQueryable<Product> FilterByCategory(IQueryable<Product> input, int categoryId, IUnitOfWork unitOfWork)
        {
            if (categoryId > 0)
            {
                var cache = SearchCache.Get(unitOfWork);
                var childCategories = cache.ChildCategories[categoryId];

                return input.Where(e => e.CategoryId == categoryId || childCategories.Contains(e.CategoryId) || e.ProductCategories.Any(pc => childCategories.Contains(pc.CategoryId) || pc.CategoryId == categoryId));
            }
            return input;
        }

        public static IQueryable<Product> FilterByCategories(IQueryable<Product> input, IEnumerable<int> categoryIds, IUnitOfWork unitOfWork)
        {
            if (categoryIds.Any())
            {
                var cache = SearchCache.Get(unitOfWork);
                var childCategories = categoryIds.ToList();
                foreach (var categoryId in categoryIds)
                {
                    childCategories.AddRange(cache.ChildCategories[categoryId]);
                }
                childCategories = childCategories.Distinct().ToList();

                return input.Where(e => childCategories.Contains(e.CategoryId) || e.ProductCategories.Any(pc => childCategories.Contains(pc.CategoryId)));
            }
            return input;
        }

        public static IQueryable<Product> FilterByManufacturers(
            IQueryable<Product> input,
            IEnumerable<int> manufacturerIds,
            IUnitOfWork unitOfWork)
        {
            if (manufacturerIds.Any())
            {
                return input.Where(e => manufacturerIds.Contains(e.ManufacturerId));
            }
            return input;
        }

        public static IQueryable<Product> ApplyCommonFilters(
            ProductSearchOptions options,
            IQueryable<Product> input,
            IUnitOfWork unitOfWork)
        {
            var cache = SearchCache.Get(unitOfWork);

            var results = input;

            // Return only published products
            if (options.Staging && !options.Published)
            {
                results = results.Where(a => a.Staging);
            }
            else if (options.Published && !options.Staging)
            {
                results = results.Where(a => a.Published && a.Manufacturer.Published);
            }

            if (options.PublishedOnCustomMicrosite)
            {
                results = results.Where(a => a.PublishedOnCustomMicrosite);
            }

            if (!options.IncludeHideOnMicrosite)
            {
                results = results.Where(a => !a.HideOnMicrosite);
            }

            // Return published and staging products
            if (options.Staging)
            {
                results = results.Where(x => x.Staging || x.Published);
            }

            if (options.CategoryId <= 0 && !string.IsNullOrWhiteSpace(options.CategoryVanityUrl))
            {
                if (cache.CategoriesByVanityUrl.ContainsKey(options.CategoryVanityUrl.ToLower()))
                {
                    options.CategoryId = cache.CategoriesByVanityUrl[options.CategoryVanityUrl.ToLower()];
                }
            }

            if (options.CategoryId > 0)
            {
                // "hard" category specified in options or identified by vanityurl
                results = FilterByCategory(results, options.CategoryId, unitOfWork);
            }

            if (options.ManufacturerIds != null && options.ManufacturerIds.Any())
            {
                var childManufacturerIds = unitOfWork.ManufacturerRepository.GetAll().Where(x => options.ManufacturerIds.Contains(x.ParentId.Value)).Select(x => x.Id).ToArray();
                results = results.Where(e => options.ManufacturerIds.Contains(e.ManufacturerId) || childManufacturerIds.Contains(e.ManufacturerId));
            }

            if (options.ProductLineId > 0)
            {
                results = results.Where(e => e.ProductLineId == options.ProductLineId);
            }

            if (options.DetailId > 0)
            {
                results = results.Where(e => e.ProductDetails.Any(d => d.DetailId == options.DetailId));
            }

            if (options.OmniclassId > 0)
            {
                List<int> omniclassIds = new List<int>() { options.OmniclassId };
                List<int> retriveChildrenOmniclassIds = new List<int>() { options.OmniclassId };

                while (true)
                {
                    var childrenIds = cache.Omniclasses.Where(a => a.ParentId != null && retriveChildrenOmniclassIds.Contains(a.ParentId.Value)).Select(a => a.Id).ToList();
                    if (childrenIds.Count == 0)
                    {
                        break;
                    }
                    omniclassIds.AddRange(childrenIds);
                    retriveChildrenOmniclassIds = childrenIds;
                }

                results = results.Where(a => a.ProductOmniclasses.Any(c => omniclassIds.Contains(c.OmniclassId)));
            }

            if (options.UniclassId > 0)
            {
                List<int> uniclassIds = new List<int>() { options.UniclassId };

                //If the Uniclasse is root
                if (cache.Uniclasses.Where(a => a.Id == options.UniclassId).Select(a => a.ParentId).FirstOrDefault() == null)
                {
                    List<int> retriveChildrenUniclassIds = new List<int>() { options.UniclassId };
                    while (true)
                    {
                        var childrenIds = cache.Uniclasses.Where(a => a.ParentId != null && retriveChildrenUniclassIds.Contains(a.ParentId.Value)).Select(a => a.Id).ToList();
                        if (childrenIds.Count == 0)
                        {
                            break;
                        }
                        uniclassIds.AddRange(childrenIds);
                        retriveChildrenUniclassIds = childrenIds;
                    }
                }

                results = results.Where(a => a.ProductUniclasses.Any(c => uniclassIds.Contains(c.UniclassId)));
            }

            if (options.UniformatId > 0)
            {
                List<int> uniformatIds = new List<int>() { options.UniformatId };

                //If the Uniformat is root
                if (cache.Uniformats.Where(a => a.Id == options.UniformatId).Select(a => a.ParentId).FirstOrDefault() == null)
                {
                    List<int> retriveChildrenUniformatIds = new List<int>() { options.UniformatId };
                    while (true)
                    {
                        var childrenIds = cache.Uniformats.Where(a => a.ParentId != null && retriveChildrenUniformatIds.Contains(a.ParentId.Value)).Select(a => a.Id).ToList();
                        if (childrenIds.Count == 0)
                        {
                            break;
                        }
                        uniformatIds.AddRange(childrenIds);
                        retriveChildrenUniformatIds = childrenIds;
                    }
                }

                results = results.Where(a => a.ProductUniformats.Any(c => uniformatIds.Contains(c.UniformatId)));
            }

            if (options.ExternalMasterformatId > 0)
            {
                IEnumerable<int> masterformatIds = cache.GetChildrenMasterformatIds(options.ExternalMasterformatId);
                results = results.Where(a => a.ProductMasterformats.Any(c => masterformatIds.Contains(c.ExternalMasterformatId)));
            }

            if (options.CisfbId > 0)
            {
                List<int> cisfbIds = new List<int>() { options.CisfbId };

                //If the masterformat is root
                if (cache.Cisfbs.Where(a => a.Id == options.CisfbId).Select(a => a.ParentId).FirstOrDefault() == null)
                {
                    List<int> retriveChildrenCisfbIds = new List<int>() { options.CisfbId };
                    while (true)
                    {
                        var childrenIds = cache.Cisfbs.Where(a => a.ParentId != null && retriveChildrenCisfbIds.Contains(a.ParentId.Value)).Select(a => a.Id).ToList();
                        if (childrenIds.Count == 0)
                        {
                            break;
                        }
                        cisfbIds.AddRange(childrenIds);
                        retriveChildrenCisfbIds = childrenIds;
                    }
                }

                results = results.Where(a => a.ProductCisfbs.Any(c => cisfbIds.Contains(c.CisfbId)));
            }

            if (options.ExternalCertificateIds != null && options.ExternalCertificateIds.Any())
            {
                IQueryable<Product> productExternalCertificateQuery = results;
                foreach (int certificateId in options.ExternalCertificateIds)
                    productExternalCertificateQuery = productExternalCertificateQuery.Where(x => x.ProductCertificates.Select(c => c.ExternalCertificateId.Value).Contains(certificateId));
                List<int> productIds = productExternalCertificateQuery.Select(x => x.Id).ToList();
                results = results.Where(a => productIds.Contains(a.Id));
            }

            if (!string.IsNullOrWhiteSpace(options.RegionId))
            {
                results = results.Where(a =>
                    a.RegionIds.ToLower() == options.RegionId.ToLower()
                    || a.RegionIds.ToLower().StartsWith(options.RegionId.ToLower() + "_")
                    || a.RegionIds.ToLower().EndsWith("_" + options.RegionId.ToLower())
                    || a.RegionIds.ToLower().Contains("_" + options.RegionId.ToLower() + "_") ||
                    //---------------------------------------
                    (a.RegionIds == null || a.RegionIds == "") &&
                     (a.ProductLine.RegionIds == null || a.ProductLine.RegionIds == "") &&
                     (a.Manufacturer.RegionIds == null || a.Manufacturer.RegionIds == "") ||
                    //-----------------------------------------------------------------------
                    (a.RegionIds == null || a.RegionIds == "") &&
                    (a.ProductLine.RegionIds.ToLower() == options.RegionId.ToLower()
                    || a.ProductLine.RegionIds.ToLower().StartsWith(options.RegionId.ToLower() + "_")
                    || a.ProductLine.RegionIds.ToLower().EndsWith("_" + options.RegionId.ToLower())
                    || a.ProductLine.RegionIds.ToLower().Contains("_" + options.RegionId.ToLower() + "_")
                    ||
                    a.Manufacturer.RegionIds.ToLower() == options.RegionId.ToLower()
                    || a.Manufacturer.RegionIds.ToLower().StartsWith(options.RegionId.ToLower() + "_")
                    || a.Manufacturer.RegionIds.ToLower().EndsWith("_" + options.RegionId.ToLower())
                    || a.Manufacturer.RegionIds.ToLower().Contains("_" + options.RegionId.ToLower() + "_")
                    )
                );
            }

            if (!string.IsNullOrWhiteSpace(options.StateId))
            {
                results = results.Where(a =>
                    a.StateIds.ToLower() == options.StateId.ToLower()
                    || a.StateIds.ToLower().StartsWith(options.StateId.ToLower() + "_")
                    || a.StateIds.ToLower().EndsWith("_" + options.StateId.ToLower())
                    || a.StateIds.ToLower().Contains("_" + options.StateId.ToLower() + "_") ||
                    //---------------------------------------
                    (a.StateIds == null || a.StateIds == "") &&
                     (a.ProductLine.StateIds == null || a.ProductLine.StateIds == "") &&
                     (a.Manufacturer.StateIds == null || a.Manufacturer.StateIds == "") ||
                    //-----------------------------------------------------------------------
                    (a.StateIds == null || a.StateIds == "") &&
                    (a.ProductLine.StateIds.ToLower() == options.StateId.ToLower()
                    || a.ProductLine.StateIds.ToLower().StartsWith(options.StateId.ToLower() + "_")
                    || a.ProductLine.StateIds.ToLower().EndsWith("_" + options.StateId.ToLower())
                    || a.ProductLine.StateIds.ToLower().Contains("_" + options.StateId.ToLower() + "_")
                    ||
                    a.Manufacturer.StateIds.ToLower() == options.StateId.ToLower()
                    || a.Manufacturer.StateIds.ToLower().StartsWith(options.StateId.ToLower() + "_")
                    || a.Manufacturer.StateIds.ToLower().EndsWith("_" + options.StateId.ToLower())
                    || a.Manufacturer.StateIds.ToLower().Contains("_" + options.StateId.ToLower() + "_")
                    )
                );
            }

            if (options.ProductFileTypeIds != null && options.ProductFileTypeIds.Any())
            {
                results = results.Where(a => a.ProductFiles.Any(c => c.ProjectDataTypeId != null && options.ProductFileTypeIds.Contains(c.ProjectDataTypeId.Value)));
            }

            if (options.QualityItemIds != null && options.QualityItemIds.Any())
            {
                results = results.Where(a => a.ProductQualityItems.Any(c => options.QualityItemIds.Contains(c.QualityItemId)));
            }

            if (options.Featured)
            {
                results = results.Where(a => a.IsFeatured);
            }

            if (!string.IsNullOrWhiteSpace(options.AttachUrl))
            {
                if (options.AttachUrl.ToLower() == AttachmentConstants.MasterspecUrl)
                {
                    results = results.Where(a => (a.ProductFiles.Any(pf => pf.IsAttachment && (pf.File.SyncUrl.Contains(AttachmentConstants.MasterspecUrl) || pf.File.Title == AttachmentConstants.MasterspecFileTitle)))
                                              || (a.ProductLine.ProductLineFiles.Any(pf => pf.IsAttachment && (pf.File.SyncUrl.Contains(AttachmentConstants.MasterspecUrl) || pf.File.Title == AttachmentConstants.MasterspecFileTitle))));
                }
                else
                {
                    results = results.Where(a => a.ProductFiles.Any(pf => pf.IsAttachment && pf.File.SyncUrl.Contains(options.AttachUrl))
                    || a.ProductLine.ProductLineFiles.Any(pf => pf.IsAttachment && pf.File.SyncUrl.Contains(options.AttachUrl)));
                }
            }

            if (options.SamplesAvailable)
            {
                results = results.Where(x => x.SwatchboxProductId != null);
                switch (options.SearchSource)
                {
                    case SearchSource.Market: results = results.Where(x => x.DisplaySwatchboxProductOnProductPage); break;
                    case SearchSource.Microsites: results = results.Where(x => x.DisplaySwatchboxProductOnMicrosite); break;
                }
            }

            if (options.KeyStats != null && options.KeyStats.Any())
            {
                foreach (MicrositeListWithStarterFiltersKeyStatDto keyStat in options.KeyStats)
                {
                    List<int> productIdsWithKeystat = new List<int>();

                    foreach (MicrositeListWithStarterFiltersKeyStatValueDto value in keyStat.Values)
                    {
                        switch (value.KeyStatType)
                        {
                            case KeyStatType.SingleNumeric:
                            case KeyStatType.NumericRange:
                            case KeyStatType.TextValue:
                                productIdsWithKeystat.AddRange(
                                    results.Where(x => x.ProductStats.Any(p => p.KeyStatId == keyStat.KeyStatId
                                                                            && p.KeyStatType == value.KeyStatType
                                                                            && p.KeyStatUnitId == value.KeyStatUnitId
                                                                            && (p.Value == value.Value || p.MinRangeValue == value.Value)
                                                                            && p.MaxRangeValue == value.MaxRangeValue))
                                           .Select(x => x.Id)
                                           .ToList());
                                break;
                            case KeyStatType.MultivalueNumeric:
                            case KeyStatType.TextMultivalue:
                                productIdsWithKeystat.AddRange(
                                    results.Where(x => x.ProductStats.Any(p => p.KeyStatId == keyStat.KeyStatId
                                                                            && p.KeyStatType == value.KeyStatType
                                                                            && p.KeyStatValueList.Any(k => k.KeyStatUnitId == value.KeyStatUnitId
                                                                                                        && (k.Value == value.Value || k.MinRangeValue == value.Value)
                                                                                                        && k.MaxRangeValue == value.MaxRangeValue)))
                                            .Select(x => x.Id)
                                            .ToList());
                                break;
                        }
                    }

                    productIdsWithKeystat = productIdsWithKeystat.Distinct().ToList();
                    results = results.Where(x => productIdsWithKeystat.Contains(x.Id));
                }
            }

            if (options.OnlyFree)
            {
                results = results.Where(x => x.Price == null || x.Price.PriceType == PriceType.Free);
            }

            //if search query is number look up by id and clear search query
            if (!string.IsNullOrWhiteSpace(options.Query) && int.TryParse(options.Query.Trim(), out int id))
            {
                string normalizedQuery = options.Query.ToLower().Trim();
                results = results.Where(x => x.Id.ToString().Contains(id.ToString())
                    || x.Name.ToLower().Contains(normalizedQuery)
                    || x.Description.ToLower().Contains(normalizedQuery)
                    || x.Keywords.ToLower().Contains(normalizedQuery));
                options.Query = string.Empty;
            }

            return results;
        }
    }
}