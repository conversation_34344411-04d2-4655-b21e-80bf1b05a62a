﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.CustomAttributes
{
    public class EmailAddressListAttribute : ValidationAttribute
    {
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            IEnumerable<string> emailAddresses = value as IEnumerable<string>;

            if (emailAddresses == null)
            {
                return new ValidationResult("Invalid email list");
            }

            EmailAddressAttribute emailAddressAttribute = new EmailAddressAttribute();
            foreach (var email in emailAddresses)
            {
                if (!emailAddressAttribute.IsValid(email))
                {
                    return new ValidationResult($"Invalid email address: {email}");
                }
            }

            return ValidationResult.Success;
        }
    }
}