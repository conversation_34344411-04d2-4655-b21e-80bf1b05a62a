﻿using BIMsmithMarket.Domain.Dto.PaymentsDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IProductPriceService
    {
        Task<ProductPriceDto> AddProductAsync(ProductPriceCreateDto model, IUnitOfWork unitOfWork);

        Task SetDefaultPaymentPlan(int id, IUnitOfWork unitOfWork);

        Task MoveProductAsync(int pymentPlanId, int productId, IUnitOfWork unitOfWork);

        Task<List<ProductPriceDto>> GetProductPricesAsync(
            int productId,
            IUnitOfWork unitOfWork,
            bool onlyAvaliable = false);

        Task<StripeProductInfoDto> GetCurrentProductPriceAsync(
            int id,
            IUnitOfWork unitOfWork);
    }
}