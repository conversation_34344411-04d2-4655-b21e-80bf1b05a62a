﻿using BIMsmithMarket.Domain.Dto.SketchupDto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface ISketchupService
    {
        Task AddProductsAsync(SketchupProductDto[] products, IUnitOfWork unitOfWork);
        Task UpdateProductsAsync(SketchupProductDto[] products);
        Task<SketchupProductDto[]> GetProductsWithSketchup(SketchupFilterProductsDto model, bool isUpdate, IUnitOfWork unitOfWork);
    }
}
