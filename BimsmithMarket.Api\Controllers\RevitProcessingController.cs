﻿using BIMsmithMarket.Api.ActionFilters;
using BIMsmithMarket.Api.Attributes;
using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.RevitProcessing;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces.RevitProcessing;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Controllers
{
    /// <summary>
    /// Revit Processes
    /// </summary>
    [Route("api/[controller]/[action]")]
    public class RevitProcessingController : BaseApiController
    {
        private readonly IRevitProcessingService _revitProcessingService;

        public RevitProcessingController(IRevitProcessingService revitProcessService)
        {
            _revitProcessingService = revitProcessService;
        }

        /// <summary>
        /// List of Revit Process types
        /// </summary>
        /// <returns></returns>
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [HttpGet]
        [Produces<RevitProcessTypeDto[]>]
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> RevitProcessTypeList()
        {
            return Ok(_revitProcessingService.RevitProcessTypeList());
        }

        /// <summary>
        /// List of Market Fields available for mappings
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Produces<MarketFieldDto[]>]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> MarketFieldList()
        {
            return Ok(_revitProcessingService.MarketFieldList());
        }

        /// <summary>
        /// Start new Revit Process
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Produces<OperationResultDto>]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> StartRevitProcess(StartRevitProcessDto model)
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _revitProcessingService.StartRevitProcessAsync(model, userId, unitOfWork));
        }

        /// <summary>
        /// List of revit processes
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier</param>
        /// <param name="offset">The offset to skip</param>
        /// <param name="count">The count to take</param>
        /// <returns></returns>
        [HttpGet]
        [Produces<PaginationListDto<RevitProcessListDto>>]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> RevitProcessList(int? manufacturerId, int offset = 0, int count = 10)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _revitProcessingService.RevitProcessListAsync(unitOfWork, manufacturerId, offset, count));
        }

        /// <summary>
        /// Cancel Revit Process
        /// </summary>
        /// <param name="id">The Revit Process identifier</param>
        /// <returns></returns>
        [HttpGet]
        [Produces<OperationResultDto>]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> CancelRevitProcess(int id)
        {
            string userId = AuthHelper.GetUserInfo(Request, ClaimTypes.NameIdentifier);
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _revitProcessingService.CancelRevitProcessAsync(id, userId, unitOfWork));
        }

        /// <summary>
        /// Generate report for Revit Process
        /// </summary>
        /// <param name="id">The Revit Process identifier</param>
        /// <returns></returns>
        [HttpGet]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        public async Task<IActionResult> GenerateRevitProcessReport(int id)
        {
            using IUnitOfWork unitOfWork = UnitOfWork.Create();
            byte[] bytes = await _revitProcessingService.GenerateReportAsync(id, unitOfWork);
            return File(bytes, "text/plain", "Revit Processing Report.txt");
        }

        /// <summary>
        /// Save result after job finished
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Produces<OperationResultDto>]
#if !DEBUG
        [RevitProcessingAuthorize]
#endif
        public async Task<IActionResult> RevitJobFinished(RevitJobResultDto model)
        {
            IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _revitProcessingService.HandleRevitJobResultAsync(model, unitOfWork));
        }

        [HttpGet]
#if !DEBUG
        [Authorize(Roles = DbConstants.AdminRole)]
#endif
        [WebApiOutputCache(CacheConstants.ServerExpiration)]
        public async Task<IActionResult> GetAssemblyCodes()
        {
            IUnitOfWork unitOfWork = UnitOfWork.Create();
            return Ok(await _revitProcessingService.GetAssemblyCodesAsync(unitOfWork));
        }
    }
}