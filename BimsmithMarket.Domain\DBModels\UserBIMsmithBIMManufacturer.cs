﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    /// <summary>
    /// BIM request to manufacturer
    /// </summary>
    public class UserBIMsmithBIMManufacturer
    {
        public int Id { get; set; }

        public int ManufacturerId { get; set; }

        public string Message { get; set; }

        public string ProductName { get; set; }

        public string ProductLink { get; set; }

        public int TimeZone { get; set; }

        public int Status { get; set; }

        public string AddedById { get; set; }

        public DateTime AddedDate { get; set; }

        /// ------------------------------------------
        [<PERSON><PERSON><PERSON>("AddedById")]
        public virtual ApplicationUser AddedBy { get; set; }

        [ForeignKey("ManufacturerId")]
        public virtual Manufacturer Manufacturer { get; set; }
    }
}