﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BIMsmithMarket.DataLayer.Migrations
{
    /// <inheritdoc />
    public partial class Add_RevitProcessing_Tables : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DELETE RevitProcesses");

            migrationBuilder.DropColumn(
                name: "ProcessName",
                table: "RevitProcesses");

            migrationBuilder.DropColumn(
                name: "RevitParameter",
                table: "RevitProcesses");

            migrationBuilder.RenameColumn(
                name: "MarketField",
                table: "RevitProcesses",
                newName: "Type");

            migrationBuilder.RenameColumn(
                name: "DateModefided",
                table: "RevitProcesses",
                newName: "ModifiedDate");

            migrationBuilder.RenameColumn(
                name: "DateCreated",
                table: "RevitProcesses",
                newName: "CreatedDate");

            migrationBuilder.AddColumn<string>(
                name: "CreatedById",
                table: "RevitProcesses",
                type: "nvarchar(128)",
                maxLength: 128,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "FinishDate",
                table: "RevitProcesses",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ManufacturerId",
                table: "RevitProcesses",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "ModifiedById",
                table: "RevitProcesses",
                type: "nvarchar(128)",
                maxLength: 128,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Status",
                table: "RevitProcesses",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "RevitJobs",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Status = table.Column<int>(type: "int", nullable: false),
                    FinishDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Report = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ReportInternal = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    RevitProcessId = table.Column<int>(type: "int", nullable: false),
                    ProductFileId = table.Column<int>(type: "int", nullable: false),
                    ProjectDataTypeId = table.Column<int>(type: "int", nullable: true),
                    CreatedById = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ModifiedById = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RevitJobs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RevitJobs_ProductFiles_ProductFileId",
                        column: x => x.ProductFileId,
                        principalTable: "ProductFiles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RevitJobs_ProjectDataTypes_ProjectDataTypeId",
                        column: x => x.ProjectDataTypeId,
                        principalTable: "ProjectDataTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RevitJobs_RevitProcesses_RevitProcessId",
                        column: x => x.RevitProcessId,
                        principalTable: "RevitProcesses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RevitJobs_Users_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "Users",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RevitJobs_Users_ModifiedById",
                        column: x => x.ModifiedById,
                        principalTable: "Users",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "RevitProcessProducts",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    RevitProcessId = table.Column<int>(type: "int", nullable: false),
                    ProductId = table.Column<int>(type: "int", nullable: false),
                    CreatedById = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ModifiedById = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RevitProcessProducts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RevitProcessProducts_Products_ProductId",
                        column: x => x.ProductId,
                        principalTable: "Products",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RevitProcessProducts_RevitProcesses_RevitProcessId",
                        column: x => x.RevitProcessId,
                        principalTable: "RevitProcesses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RevitProcessProducts_Users_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "Users",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RevitProcessProducts_Users_ModifiedById",
                        column: x => x.ModifiedById,
                        principalTable: "Users",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "RevitProcessProjectDataTypes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    RevitProcessId = table.Column<int>(type: "int", nullable: false),
                    ProjectDataTypeId = table.Column<int>(type: "int", nullable: false),
                    CreatedById = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ModifiedById = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RevitProcessProjectDataTypes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RevitProcessProjectDataTypes_ProjectDataTypes_ProjectDataTypeId",
                        column: x => x.ProjectDataTypeId,
                        principalTable: "ProjectDataTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RevitProcessProjectDataTypes_RevitProcesses_RevitProcessId",
                        column: x => x.RevitProcessId,
                        principalTable: "RevitProcesses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RevitProcessProjectDataTypes_Users_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "Users",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RevitProcessProjectDataTypes_Users_ModifiedById",
                        column: x => x.ModifiedById,
                        principalTable: "Users",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "RevitProcessRevitParameterMappings",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    RevitParameter = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    MarketField = table.Column<int>(type: "int", nullable: true),
                    CustomValue = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    RevitProcessId = table.Column<int>(type: "int", nullable: false),
                    CreatedById = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ModifiedById = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RevitProcessRevitParameterMappings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RevitProcessRevitParameterMappings_RevitProcesses_RevitProcessId",
                        column: x => x.RevitProcessId,
                        principalTable: "RevitProcesses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RevitProcessRevitParameterMappings_Users_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "Users",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RevitProcessRevitParameterMappings_Users_ModifiedById",
                        column: x => x.ModifiedById,
                        principalTable: "Users",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "RevitJobRevitParameterMappings",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    RevitParameter = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    MarketValue = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    RevitJobId = table.Column<int>(type: "int", nullable: false),
                    CreatedById = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ModifiedById = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    ModifiedDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RevitJobRevitParameterMappings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RevitJobRevitParameterMappings_RevitJobs_RevitJobId",
                        column: x => x.RevitJobId,
                        principalTable: "RevitJobs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_RevitJobRevitParameterMappings_Users_CreatedById",
                        column: x => x.CreatedById,
                        principalTable: "Users",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_RevitJobRevitParameterMappings_Users_ModifiedById",
                        column: x => x.ModifiedById,
                        principalTable: "Users",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_RevitProcesses_CreatedById",
                table: "RevitProcesses",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RevitProcesses_ManufacturerId",
                table: "RevitProcesses",
                column: "ManufacturerId");

            migrationBuilder.CreateIndex(
                name: "IX_RevitProcesses_ModifiedById",
                table: "RevitProcesses",
                column: "ModifiedById");

            migrationBuilder.CreateIndex(
                name: "IX_RevitJobRevitParameterMappings_CreatedById",
                table: "RevitJobRevitParameterMappings",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RevitJobRevitParameterMappings_ModifiedById",
                table: "RevitJobRevitParameterMappings",
                column: "ModifiedById");

            migrationBuilder.CreateIndex(
                name: "IX_RevitJobRevitParameterMappings_RevitJobId",
                table: "RevitJobRevitParameterMappings",
                column: "RevitJobId");

            migrationBuilder.CreateIndex(
                name: "IX_RevitJobs_CreatedById",
                table: "RevitJobs",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RevitJobs_ModifiedById",
                table: "RevitJobs",
                column: "ModifiedById");

            migrationBuilder.CreateIndex(
                name: "IX_RevitJobs_ProductFileId",
                table: "RevitJobs",
                column: "ProductFileId");

            migrationBuilder.CreateIndex(
                name: "IX_RevitJobs_ProjectDataTypeId",
                table: "RevitJobs",
                column: "ProjectDataTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_RevitJobs_RevitProcessId",
                table: "RevitJobs",
                column: "RevitProcessId");

            migrationBuilder.CreateIndex(
                name: "IX_RevitProcessProducts_CreatedById",
                table: "RevitProcessProducts",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RevitProcessProducts_ModifiedById",
                table: "RevitProcessProducts",
                column: "ModifiedById");

            migrationBuilder.CreateIndex(
                name: "IX_RevitProcessProducts_ProductId",
                table: "RevitProcessProducts",
                column: "ProductId");

            migrationBuilder.CreateIndex(
                name: "IX_RevitProcessProducts_RevitProcessId",
                table: "RevitProcessProducts",
                column: "RevitProcessId");

            migrationBuilder.CreateIndex(
                name: "IX_RevitProcessProjectDataTypes_CreatedById",
                table: "RevitProcessProjectDataTypes",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RevitProcessProjectDataTypes_ModifiedById",
                table: "RevitProcessProjectDataTypes",
                column: "ModifiedById");

            migrationBuilder.CreateIndex(
                name: "IX_RevitProcessProjectDataTypes_ProjectDataTypeId",
                table: "RevitProcessProjectDataTypes",
                column: "ProjectDataTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_RevitProcessProjectDataTypes_RevitProcessId",
                table: "RevitProcessProjectDataTypes",
                column: "RevitProcessId");

            migrationBuilder.CreateIndex(
                name: "IX_RevitProcessRevitParameterMappings_CreatedById",
                table: "RevitProcessRevitParameterMappings",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_RevitProcessRevitParameterMappings_ModifiedById",
                table: "RevitProcessRevitParameterMappings",
                column: "ModifiedById");

            migrationBuilder.CreateIndex(
                name: "IX_RevitProcessRevitParameterMappings_RevitProcessId",
                table: "RevitProcessRevitParameterMappings",
                column: "RevitProcessId");

            migrationBuilder.AddForeignKey(
                name: "FK_RevitProcesses_Manufacturers_ManufacturerId",
                table: "RevitProcesses",
                column: "ManufacturerId",
                principalTable: "Manufacturers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_RevitProcesses_Users_CreatedById",
                table: "RevitProcesses",
                column: "CreatedById",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_RevitProcesses_Users_ModifiedById",
                table: "RevitProcesses",
                column: "ModifiedById",
                principalTable: "Users",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_RevitProcesses_Manufacturers_ManufacturerId",
                table: "RevitProcesses");

            migrationBuilder.DropForeignKey(
                name: "FK_RevitProcesses_Users_CreatedById",
                table: "RevitProcesses");

            migrationBuilder.DropForeignKey(
                name: "FK_RevitProcesses_Users_ModifiedById",
                table: "RevitProcesses");

            migrationBuilder.DropTable(
                name: "RevitJobRevitParameterMappings");

            migrationBuilder.DropTable(
                name: "RevitProcessProducts");

            migrationBuilder.DropTable(
                name: "RevitProcessProjectDataTypes");

            migrationBuilder.DropTable(
                name: "RevitProcessRevitParameterMappings");

            migrationBuilder.DropTable(
                name: "RevitJobs");

            migrationBuilder.DropIndex(
                name: "IX_RevitProcesses_CreatedById",
                table: "RevitProcesses");

            migrationBuilder.DropIndex(
                name: "IX_RevitProcesses_ManufacturerId",
                table: "RevitProcesses");

            migrationBuilder.DropIndex(
                name: "IX_RevitProcesses_ModifiedById",
                table: "RevitProcesses");

            migrationBuilder.DropColumn(
                name: "CreatedById",
                table: "RevitProcesses");

            migrationBuilder.DropColumn(
                name: "FinishDate",
                table: "RevitProcesses");

            migrationBuilder.DropColumn(
                name: "ManufacturerId",
                table: "RevitProcesses");

            migrationBuilder.DropColumn(
                name: "ModifiedById",
                table: "RevitProcesses");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "RevitProcesses");

            migrationBuilder.RenameColumn(
                name: "Type",
                table: "RevitProcesses",
                newName: "MarketField");

            migrationBuilder.RenameColumn(
                name: "ModifiedDate",
                table: "RevitProcesses",
                newName: "DateModefided");

            migrationBuilder.RenameColumn(
                name: "CreatedDate",
                table: "RevitProcesses",
                newName: "DateCreated");

            migrationBuilder.AddColumn<string>(
                name: "ProcessName",
                table: "RevitProcesses",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RevitParameter",
                table: "RevitProcesses",
                type: "nvarchar(max)",
                nullable: true);
        }
    }
}
