﻿using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Models.ApplicationDetailModels
{
    public class AddDetailApplicationViewModel
    {
        [Required]
        [StringLength(200)]
        public string Name { get; set; }

        public string Header { get; set; }

        public string PageTitle { get; set; }

        public string MetaDescription { get; set; }

        public string Description { get; set; }

        [Required]
        public string VanityUrl { get; set; }

        public string Keywords { get; set; }

        public string Synonyms { get; set; }
    }
}