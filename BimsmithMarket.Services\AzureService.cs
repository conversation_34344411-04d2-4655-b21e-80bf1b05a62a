﻿using Azure.Storage.Blobs;
using BIMsmithMarket.Core.Helpers;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class AzureService
    {
        private readonly bool IsDevBlob;

        public AzureService()
        {
            IsDevBlob = Convert.ToBoolean(ConfigurationHelper.GetValue("IsDevEnvironment"));
        }

        public async Task<bool> DeleteBlob(string blobUrl)
        {
            BlobClient blobClient = new BlobClient(new Uri(blobUrl));
            var response = await blobClient.DeleteAsync(Azure.Storage.Blobs.Models.DeleteSnapshotsOption.IncludeSnapshots);
            return response.Status == 200;
        }

        public async void DeleteUnusedBlobs()
        {
            string[] blobContainerNames = new string[] {
                "archives",
                "attachments",
                "files",
                "icons",
                "locked-archive",
                "locked-product-files",
                "news-content",
                "photos",
                "temp-files"
            };

            if (IsDevEnvironment())
            {
                string environment = ConfigurationHelper.GetValue("Environment");
                blobContainerNames = blobContainerNames.Select(x => x + $"-{environment}").ToArray();
            }
        }

        private bool IsDevEnvironment()
        {
            return IsDevBlob;
        }
    }
}