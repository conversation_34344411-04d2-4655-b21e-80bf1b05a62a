﻿using BIMsmithMarket.Core.Providers;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Domain.Dto.Manufacturer;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Interfaces;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class ManufacturerExcelService : IManufacturerExcelService
    {
        public async Task<string> GetRequestPricingUserExcelAsync(int manufacturerId, IUnitOfWork unitOfWork)
        {
            RequestPricingUserExcelDto[] users = await GetRequestPricingUserExcelListAsync(manufacturerId, unitOfWork);
            string filePath = Path.Combine(Path.GetTempPath(), $"RequestPricingUserExport_{Guid.NewGuid()}.xlsx");
            SpreadsheetDocument spreadsheetDocument;
            WorkbookPart workbookpart;
            WorksheetPart worksheetPart;
            CommonExcelProvider.CreateDocument(filePath, out spreadsheetDocument, out workbookpart, out worksheetPart);

            //columns
            Columns columns = new Columns();
            uint headerColumnIndex = 1;
            int columnsCount = 5;
            for (int i = 0; i < columnsCount; i++)
                columns.AppendChild(new Column { Min = headerColumnIndex, Max = headerColumnIndex++, Width = 25, CustomWidth = true });
            worksheetPart.Worksheet.AppendChild(columns);
            workbookpart.Workbook.Save();

            CommonExcelProvider.CreateSheet(spreadsheetDocument, workbookpart, worksheetPart, "Pricing Requests");

            // Get the sheetData cell table.
            SheetData sheetData = worksheetPart.Worksheet.AppendChild(new SheetData());
            int rowIndex = 1;
            Row rowHeader = new Row();
            rowHeader.Height = 90;
            rowHeader.CustomHeight = true;
            sheetData.AppendChild(rowHeader);

            int columnIndex = 1;

            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(ManufacturerExcelConstants.RequestPricingUserEmailCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(ManufacturerExcelConstants.RequestPricingUserFirstNameCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(ManufacturerExcelConstants.RequestPricingUserLastNameCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(ManufacturerExcelConstants.RequestPricingProductNameCaption, CellValues.String, columnIndex++, rowIndex, 2));
            rowHeader.AppendChild(CommonExcelProvider.ConstructCell(ManufacturerExcelConstants.RequestPricingCreatedDateCaption, CellValues.String, columnIndex++, rowIndex, 2));

            rowIndex++;

            foreach (RequestPricingUserExcelDto user in users)
            {
                Row row = new Row();
                sheetData.AppendChild(row);

                columnIndex = 1;

                row.AppendChild(CommonExcelProvider.ConstructCell(user.UserEmail, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(user.UserFirstName, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(user.UserLastName, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(user.ProductName, CellValues.String, columnIndex++, rowIndex));
                row.AppendChild(CommonExcelProvider.ConstructCell(user.CreatedDate.ToString(), CellValues.Date, columnIndex++, rowIndex));

                rowIndex++;
            }

            workbookpart.Workbook.Save();

            spreadsheetDocument.Dispose();

            return filePath;
        }

        #region private methods
        private async Task<RequestPricingUserExcelDto[]> GetRequestPricingUserExcelListAsync(int manufacturerId, IUnitOfWork unitOfWork)
        {
            return await unitOfWork.RequestPricingUserRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId)
                .OrderBy(x => x.CreatedDate)
                .ProjectToType<RequestPricingUserExcelDto>()
                .ToArrayAsync();
        }
        #endregion
    }
}