﻿using BIMsmithMarket.Domain.Models;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IDetailScaleService
    {
        Task<dynamic> AddAsync(AddDetailScaleViewModel model, string userId);

        Task<dynamic> EditAsync(EditDetailScaleViewModel model, string userId);

        Task<bool> DeleteAsync(int id);

        Task<dynamic> GetAsync(int id);

        Task<dynamic> ListAsync(string query, int count, int offset);
    }
}