﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIMsmithMarket.Domain.DBModels
{
    public class BlogComment
    {
        public int Id { get; set; }

        public int BlogPostId { get; set; }

        public string OwnerName { get; set; }

        public string Comment { get; set; }

        public string CreatedById { get; set; }

        public DateTime CreatedDate { get; set; }

        public TargetBlogType Targettype { get; set; }

        /// ------------------------------------------
        [<PERSON><PERSON><PERSON>("BlogPostId")]
        public virtual BlogPost BlogPost { get; set; }

        [ForeignKey("CreatedById")]
        public virtual ApplicationUser CreatedBy { get; set; }

    }
}