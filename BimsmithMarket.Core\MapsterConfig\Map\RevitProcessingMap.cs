﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.DBModels.RevitProcessing;
using BIMsmithMarket.Domain.Dto.RevitProcessing;
using BIMsmithMarket.Domain.Enums.RevitProcessing;
using Mapster;
using System.Linq;

namespace BIMsmithMarket.Core.MapsterConfig.Map
{
    public static class RevitProcessingMap
    {
        public static void Register(TypeAdapterConfig config)
        {
            config.ForType<RevitProcess, RevitProcessListDto>()
                .Map(d => d.ManufacturerName, s => s.Manufacturer.Name)
                .Map(d => d.ProductsCount, s => s.RevitProcessProducts.Count)
                .Map(d => d.FilesCount, s => s.RevitJobs.Count)
                .Map(d => d.FilesInProgress, s => s.RevitJobs.Count(r => r.Status == RevitJobStatus.InProgress))
                .Map(d => d.FilesCompleted, s => s.RevitJobs.Count(r => r.Status == RevitJobStatus.Completed))
                .Map(d => d.FilesCompletedWithErrors, s => s.RevitJobs.Count(r => r.Status == RevitJobStatus.CompletedWithErrors))
                .Map(d => d.FilesCancelled, s => s.RevitJobs.Count(r => r.Status == RevitJobStatus.Cancelled))
                .Map(d => d.FilesFailed, s => s.RevitJobs.Count(r => r.Status == RevitJobStatus.Failed));

            config.ForType<RevitJob, RevitJobDto>()
                .Map(d => d.RevitProcessType, s => s.RevitProcess.Type)
                .Map(d => d.FileId, s => s.ProductFile.FileId)
                .Map(d => d.FileName, s => s.ProductFile.File.Title)
                .Map(d => d.ProductId, s => s.ProductFile.ProductId)
                .Map(d => d.ProductName, s => s.ProductFile.Product.Name)
                .Map(d => d.ManufacturerId, s => s.ProductFile.Product.ManufacturerId)
                .Map(d => d.ManufacturerName, s => s.ProductFile.Product.Manufacturer.Name)
                .Map(d => d.RevitVersion, s => s.ProductFile.SoftwareVersion.Title)
                .Map(d => d.RevitParameterMappings, s => s.RevitJobRevitParameterMappings);

            config.ForType<ProductFile, RevitProcessingProductFileDto>()
                .Map(d => d.ProductFileId, s => s.Id)
                .Map(d => d.SoftwareVersionId, s => s.SoftwareVersionId)
                .Map(d => d.RevitVersionString, s => s.SoftwareVersion.Title.Replace("Revit ", string.Empty))
                .Map(d => d.FileUrl, s => s.File.Url)
                .Map(d => d.FileName, s => s.File.FileName);

            config.ForType<RevitJob, RevitJobQueueDto>()
                .Map(d => d.ProcessType, s => s.RevitProcess.Type)
                .Map(d => d.FileUrl, s => s.ProductFile.File.Url)
                .Map(d => d.RevitVersion, s => s.ProjectDataType.Title.Replace("Revit ", string.Empty))
                .Map(d => d.ParameterMapping, s => s.RevitJobRevitParameterMappings);

            config.ForType<RevitProcess, RevitProcessSlackNotificationDto>()
                .Map(d => d.CreatorUserEmail, s => s.CreatedBy.Email)
                .Map(d => d.ModifierUserEmail, s => s.ModifiedBy.Email)
                .Map(d => d.ManufacturerName, s => s.Manufacturer.Name)
                .Map(d => d.ProductsCount, s => s.RevitProcessProducts.Count)
                .Map(d => d.FilesCount, s => s.RevitJobs.Count);
        }
    }
}