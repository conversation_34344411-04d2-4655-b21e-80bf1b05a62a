﻿using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Specialized;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Dto.File;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using System;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services.Interfaces
{
    public interface IFileService
    {
        Task<InitDownloadZipResult> InitDownloadZipAsync(int[] fileIds, int? productId = null, int? fileType = null, string localFileFolder = null, bool downloadConfirmation = false);

        Task MoveProductFilesToLockedBlob(int productId, IUnitOfWork unitOfWork);

        Task MoveProductFilesToUnLockedBlob(int productId, IUnitOfWork unitOfWork);

        string FixFileName(string fileName, string defaultName = "archive");

        string FindFileUrlForPdfFrame(string fileUrl);

        bool IsUrlWithPdfIframe(string fileUrl);

        Task DownloadFileFromUrlAsync(Domain.DBModels.File file, string attachUrl, BlobContainerClient filesContainer, IUnitOfWork unitOfWork, bool forceUpdate = false);

        void CheckPdfMetadataAndSave(string filePath);

        string GenerateSasTokenForFile(string fileName, string extension, BlockBlobClient blobZipFile);

        string GenerateSasTokenForArchive(string fileName, BlockBlobClient blobZipFile);

        Task<BlockBlobClient> GetManufacturerProjectFilesZipAsync(
            DownloadManufacturerProjectFilesDto model,
            IUnitOfWork unitOfWork);

        Task<string> GenerateSharedAccessSignatureForFileAsync(int fileId, string containerName, TimeSpan liveWindow, IUnitOfWork unitOfWork);
    }
}