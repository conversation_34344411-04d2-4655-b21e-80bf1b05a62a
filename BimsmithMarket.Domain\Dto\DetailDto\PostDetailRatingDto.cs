﻿using BIMsmithMarket.Domain.DBModels;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Dto.DetailDto
{
    public class PostDetailRatingDto
    {
        [Required]
        public int DetailId { get; set; }

        [Required]
        public DetailRatingType Type { get; set; }

        [Required]
        public int Rating { get; set; }

        public string Comment { get; set; }
    }
}