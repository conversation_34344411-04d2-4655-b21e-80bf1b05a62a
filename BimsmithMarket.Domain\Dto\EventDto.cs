﻿using System;
using System.ComponentModel.DataAnnotations;

namespace BIMsmithMarket.Domain.Dto
{

    public class AddEditEventDto
    {
        public int Id { get; set; }

        [Required]
        public string Title { get; set; }

        public string HtmlBody { get; set; }

        [Required]
        public string ImageUrlBig { get; set; }

        [Required]
        public string ImageUrlSmall { get; set; }

        public string AuthorTitle { get; set; }

        public string AuthorEmail { get; set; }

        public string Tags { get; set; }

        public bool Published { get; set; }

        public DateTime? PublishedDate { get; set; }

        public string MetaDescription { get; set; }

        public string MetaKeywords { get; set; }

        public string MetaTitle { get; set; }

        public string VanityUrl { get; set; }

        [Required]
        public DateTime Date { get; set; }

        [Required]
        public string Time { get; set; }

        [Required]
        public string Link { get; set; }

        public string CEUCredits { get; set; }

        public bool UnpublishAfterDateHasPassed { get; set; }
    }

    public class AdminListEventDto
    {
        public int Id { get; set; }

        public string ImageUrlSmall { get; set; }

        public string VanityUrl { get; set; }

        public string UserName { get; set; }

        public DateTime CreatedDate { get; set; }
    }

    public class PublicListEventDto
    {
        public string Title { get; set; }

        public string Date { get; set; }

        public string Time { get; set; }

        public string CEUCredits { get; set; }

        public string Link { get; set; }
    }

    public class AdminGetEventDto
    {
        public int Id { get; set; }

        public DateTime CreatedDate { get; set; }

        public DateTime? ModifiedDate { get; set; }

        public string Title { get; set; }

        public string Description { get; set; }

        public string HtmlBody { get; set; }

        public string ImageUrlBig { get; set; }

        public string ImageUrlSmall { get; set; }

        public string AuthorTitle { get; set; }

        public string AuthorEmail { get; set; }

        public string AuthorImage { get; set; }

        public string Tags { get; set; }

        public string MetaDescription { get; set; }

        public string MetaKeywords { get; set; }

        public string MetaTitle { get; set; }

        public int ViewCount { get; set; }

        public bool Published { get; set; }

        public DateTime? PublishedDate { get; set; }

        public bool IsFeatured { get; set; }

        public int Status { get; set; }

        public string VanityUrl { get; set; }

        public DateTime Date { get; set; }

        public string Time { get; set; }

        public string Link { get; set; }

        public string CEUCredits { get; set; }

        public bool UnpublishAfterDateHasPassed { get; set; }

        public EventUserDto CreatedBy { get; set; }

        public EventUserDto ModifiedBy { get; set; }
    }

    public class EventUserDto
    {
        public string Id { get; set; }

        public string FirstName { get; set; }

        public string LastName { get; set; }
    }
}