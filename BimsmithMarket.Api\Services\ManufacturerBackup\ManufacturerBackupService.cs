﻿using BIMsmith.AzureStorageProvider;
using BIMsmithMarket.DataLayer;
using BIMsmithMarket.Domain.Dto;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Mapster;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;

using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Api.Services.ManufacturerBackup
{
    public class ManufacturerBackupService : IManufacturerBackupService
    {
        private readonly IManufacturerBackupFileService _manufacturerBackupFileService;
        private readonly IAzureStorageService _azureStorageService;

        public ManufacturerBackupService(
            IManufacturerBackupFileService manufacturerBackupFileService,
            IAzureStorageService azureStorageService)
        {
            _manufacturerBackupFileService = manufacturerBackupFileService;
            _azureStorageService = azureStorageService;
        }

        /// <summary>
        /// Creates manufacturer backup
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier</param>
        /// <returns></returns>
        public async Task<bool> CreateManufacturerBackupAsync(int manufacturerId)
        {
            using (var unitOfWork = UnitOfWork.Create())
            {
                if (!unitOfWork.ManufacturerRepository.GetAll().Any(x => x.Id == manufacturerId)) throw new Exception("Manufacturer not found");
                var manufacturerBackupModel = await GetManufacturerBackupModelAsync(manufacturerId, unitOfWork);
                await _manufacturerBackupFileService.SaveManufacturerBackupToFileAsync(manufacturerBackupModel);
            }
            return true;
        }

        public async Task<List<ManufacturerBackupsListModel>> GetManufacturerBackupsListAsync(int manufacturerId)
        {
            List<string> manufacturerBackupFiles = await _manufacturerBackupFileService.GetManufacturerBackupFilesAsync(manufacturerId);
            var files = manufacturerBackupFiles.Select(x => new ManufacturerBackupsListModel
            {
                SystemName = Path.GetFileNameWithoutExtension(x),
                DateString = _manufacturerBackupFileService.ConvertBackupFileNameToDateString(Path.GetFileNameWithoutExtension(x))
            }).ToList();

            return files;
        }

        /// <summary>
        /// Checks if manufacturer has backup for current data
        /// </summary>
        /// <param name="manufacturerId">The manufacturer identifier</param>
        /// <returns></returns>
        public async Task<bool> ManufacturerHasBackupForCurrentDataAsync(int manufacturerId)
        {
            List<string> manufacturerBackupFiles = await _manufacturerBackupFileService.GetManufacturerBackupFilesAsync(manufacturerId);
            var currentDateString = DateTime.UtcNow.ToString("yyyy MM dd");
            return manufacturerBackupFiles.Any(x => x.Contains(currentDateString));
        }

        #region private methods

        private async Task<ManufacturerBackupModel> GetManufacturerBackupModelAsync(int manufacturerId, IUnitOfWork unitOfWork)
        {
            ManufacturerBackupModel manufacturerBackupModel = await unitOfWork.ManufacturerRepository.GetAll()
                .Where(x => x.Id == manufacturerId)
                .ProjectToType<ManufacturerBackupModel>()
                .FirstOrDefaultAsync();
            manufacturerBackupModel.Products = await GetManufacturerBackupProductsAsync(manufacturerId, unitOfWork);
            manufacturerBackupModel.ProductLines = await GetManufacturerBackupProductLinesAsync(manufacturerId, unitOfWork);
            manufacturerBackupModel.ManufacturerPhotoIds = await GetManufacturerBackupPhotoIdsAsync(manufacturerId, unitOfWork);
            manufacturerBackupModel.ManufacturerFiles = await GetManufacturerBackupFilesAsync(manufacturerId, unitOfWork);
            manufacturerBackupModel.Details = await GetManufacturerBackupDetailsAsync(manufacturerId, unitOfWork);
            manufacturerBackupModel.SubscribedUsers = await GetManufacturerBackupSubscribedUsersAsync(manufacturerId, unitOfWork);
            manufacturerBackupModel.LetsTalkUsers = await GetManufacturerBackupLetsTalkUsersAsync(manufacturerId, unitOfWork);
            manufacturerBackupModel.LunchAndLearnUsers = await GetManufacturerBackupLunchAndLearnUsersAsync(manufacturerId, unitOfWork);
            manufacturerBackupModel.BimRequests = await GetManufacturerBackupBimRequestsAsync(manufacturerId, unitOfWork);
            manufacturerBackupModel.ManufacturerAdminUsers = await GetManufacturerBackupAdminsAsync(manufacturerId, unitOfWork);
            manufacturerBackupModel.AttachmentOrders = await GetManufacturerBackupAttachmentOrdersAsync(manufacturerId, unitOfWork);
            manufacturerBackupModel.RequestPricingUsers = await GetManufacturerBackupRequestPricingAsync(manufacturerId, unitOfWork);
            manufacturerBackupModel.SalesRepresentatives = await GetManufacturerBackupSalesRepresentativesAsync(manufacturerId, unitOfWork);

            return manufacturerBackupModel;
        }

        private async Task<List<ProductBackupModel>> GetManufacturerBackupProductsAsync(int manufacturerId, IUnitOfWork unitOfWork)
        {
            var manufacturerBackupProducts = await unitOfWork.ProductRepository.GetAll()
                .AsSplitQuery()
                .Where(x => x.ManufacturerId == manufacturerId)
                .ProjectToType<ProductBackupModel>()
                .ToListAsync();

            return manufacturerBackupProducts;
        }

        private async Task<List<ProductLineBackupModel>> GetManufacturerBackupProductLinesAsync(int manufacturerId, IUnitOfWork unitOfWork)
        {
            var manufacturerBackupProductLines = await unitOfWork.ProductLineRepository.GetAll()
                .AsSplitQuery()
                .Where(x => x.ManufacturerId == manufacturerId)
                .ProjectToType<ProductLineBackupModel>()
                .ToListAsync();

            return manufacturerBackupProductLines;
        }

        private async Task<List<int>> GetManufacturerBackupPhotoIdsAsync(int manufacturerId, IUnitOfWork unitOfWork)
        {
            var manufacturerBackupPhotoIds = await unitOfWork.ManufacturerPhotoRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId)
                .Select(x => x.PhotoId)
                .ToListAsync();

            return manufacturerBackupPhotoIds;
        }

        private async Task<List<FileDto>> GetManufacturerBackupFilesAsync(int manufacturerId, IUnitOfWork unitOfWork)
        {
            var manufacturerBackupFiles = await unitOfWork.ManufacturerFileRepository.GetAll()
                .AsSplitQuery()
                .Where(x => x.ManufacturerId == manufacturerId)
                .ProjectToType<FileDto>()
                .ToListAsync();

            return manufacturerBackupFiles;
        }

        private async Task<List<DetailBackupModel>> GetManufacturerBackupDetailsAsync(int manufacturerId, IUnitOfWork unitOfWork)
        {
            var manufacturerBackupDetails = await unitOfWork.DetailRepository.GetAll()
                .AsSplitQuery()
                .Where(x => x.ManufacturerId == manufacturerId)
                .ProjectToType<DetailBackupModel>()
                .ToListAsync();

            return manufacturerBackupDetails;
        }

        private async Task<List<SubscriberBackupModel>> GetManufacturerBackupSubscribedUsersAsync(int manufacturerId, IUnitOfWork unitOfWork)
        {
            var manufacturerBackupSubscribedUsers = await unitOfWork.UserBIMsmithManufacturerRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId)
                .ProjectToType<SubscriberBackupModel>()
                .ToListAsync();

            return manufacturerBackupSubscribedUsers;
        }

        private async Task<List<LetsTalkUserBackupModel>> GetManufacturerBackupLetsTalkUsersAsync(int manufacturerId, IUnitOfWork unitOfWork)
        {
            var manufacturerBackupLetsTalkUsers = await unitOfWork.UserBIMsmithLTManufacturerRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId)
                .ProjectToType<LetsTalkUserBackupModel>()
                .ToListAsync();

            return manufacturerBackupLetsTalkUsers;
        }

        private async Task<List<LunchAndLearnUserBackupModel>> GetManufacturerBackupLunchAndLearnUsersAsync(int manufacturerId, IUnitOfWork unitOfWork)
        {
            var manufacturerBackupLunchAndLearnUsers = await unitOfWork.UserBIMsmithLLManufacturerRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId)
                .ProjectToType<LunchAndLearnUserBackupModel>()
                .ToListAsync();

            return manufacturerBackupLunchAndLearnUsers;
        }

        private async Task<List<BimRequestBackupModel>> GetManufacturerBackupBimRequestsAsync(int manufacturerId, IUnitOfWork unitOfWork)
        {
            var manufacturerBackupBimRequests = await unitOfWork.UserBIMsmithBIMManufacturerRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId)
                .ProjectToType<BimRequestBackupModel>()
                .ToListAsync();

            return manufacturerBackupBimRequests;
        }

        private async Task<List<ManufacturerAdminUserBackupModel>> GetManufacturerBackupAdminsAsync(int manufacturerId, IUnitOfWork unitOfWork)
        {
            var manufacturerBackupAdmins = await unitOfWork.ManufacturerAdminUserRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId)
                .ProjectToType<ManufacturerAdminUserBackupModel>()
                .ToListAsync();

            return manufacturerBackupAdmins;
        }

        private async Task<List<AttachmentOrderBackupModel>> GetManufacturerBackupAttachmentOrdersAsync(int manufacturerId, IUnitOfWork unitOfWork)
        {
            var manufacturerBackupAttachmentOrders = await unitOfWork.AttachmentOrderRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId)
                .ProjectToType<AttachmentOrderBackupModel>()
                .ToListAsync();

            return manufacturerBackupAttachmentOrders;
        }

        private async Task<List<RequestPricingUserBackupModel>> GetManufacturerBackupRequestPricingAsync(int manufacturerId, IUnitOfWork unitOfWork)
        {
            return await unitOfWork.RequestPricingUserRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId)
                .ProjectToType<RequestPricingUserBackupModel>()
                .ToListAsync();
        }

        private async Task<List<SalesRepresentativeBackupModel>> GetManufacturerBackupSalesRepresentativesAsync(int manufacturerId, IUnitOfWork unitOfWork)
        {
            return await unitOfWork.SalesRepresentativeRepository.GetAll()
                .Where(x => x.ManufacturerId == manufacturerId)
                .ProjectToType<SalesRepresentativeBackupModel>()
                .ToListAsync();
        }
        #endregion
    }
}