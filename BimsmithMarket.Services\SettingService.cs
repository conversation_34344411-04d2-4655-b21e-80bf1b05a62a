﻿using BIMsmithMarket.Core.CustomExceptions;
using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Domain.Models;
using BIMsmithMarket.Services.Interfaces;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Threading.Tasks;

namespace BIMsmithMarket.Services
{
    public class SettingService : ISettingService
    {
        public async Task<object> ListAsync(IUnitOfWork unitOfWork, int offset, int count)
        {
            IQueryable<Setting> query = unitOfWork.SettingRepository.GetAll();

            var countOfSettings = await query.CountAsync();

            var settings = await query
                        .OrderBy(a => a.Name)
                        .Skip(offset)
                        .Take(count)
                        .Select(a => new SettingModel
                        {
                            Name = a.Name,
                            Description = a.Description,
                            Value = a.Value
                        })
                        .AsNoTracking()
                        .ToListAsync();

            return new
            {
                count = countOfSettings,
                data = settings
            };
        }

        public async Task EditAsync(IUnitOfWork unitOfWork, SettingModel model)
        {
            var setting = await unitOfWork.SettingRepository.GetAll().FirstOrDefaultAsync(a => a.Name == model.Name);
            if (setting == null)
                throw new InvalidInputException("Setting not found");

            setting.Value = model.Value;
            await unitOfWork.SaveAsync();
        }
    }
}
