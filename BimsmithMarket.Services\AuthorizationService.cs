﻿using BIMsmithMarket.Core.Helpers;
using BIMsmithMarket.Domain.Constants;
using BIMsmithMarket.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;

namespace BIMsmithMarket.Services
{
    public class AuthorizationService : IAuthorizationService
    {
        private readonly ICacheService _cacheService;

        public AuthorizationService(ICacheService cacheService)
        {
            _cacheService = cacheService;
        }

        public void InvalidateToken(string token)
        {
            if (string.IsNullOrWhiteSpace(token))
                return;

            JwtSecurityToken securityToken = AuthHelper.ParseSecurityToken(token);
            int secondsToExpire = (int)(securityToken.ValidTo - DateTime.UtcNow).TotalSeconds;

            List<string> invalidTokens = GetInvalidTokens();

            if (!invalidTokens.Contains(token))
                invalidTokens.Add(token);

            _cacheService.Set(AuthorizationConstants.InvalidTokensListRedisKey, invalidTokens, secondsToExpire);
        }

        public bool IsTokenInvalid(string token)
        {
            if (string.IsNullOrWhiteSpace(token))
                return false;

            List<string> invalidTokens = GetInvalidTokens();
            return invalidTokens.Contains(token);
        }

        #region private methods
        private List<string> GetInvalidTokens()
        {
            return _cacheService.Get<List<string>>(AuthorizationConstants.InvalidTokensListRedisKey) ?? new();
        }
        #endregion
    }
}