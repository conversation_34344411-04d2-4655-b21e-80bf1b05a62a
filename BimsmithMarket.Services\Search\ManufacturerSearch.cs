﻿using BIMsmithMarket.Domain.DBModels;
using BIMsmithMarket.Domain.Interfaces.DataInterfaces;
using BIMsmithMarket.Services.Search.FullTextSearch;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;

namespace BIMsmithMarket.Services.Search
{
    public class ManufacturerSearchOptions
    {
        public List<int> ManufacturerIds { get; set; }

        public string Query { get; set; }

        public string ListQuery { get; set; }

        public string ManufacturerName { get; set; }

        public string HubVanityURL { get; set; }

        public bool Published { get; set; }

        public string RegionId { get; set; }

        public string StateId { get; set; }

        public string LangCode { get; set; }

        public void Normalize()
        {
            if (string.IsNullOrWhiteSpace(LangCode))
            {
                LangCode = SearchCache.DefaultLanguage;
            }
        }
    }

    /// <summary>
    /// Utility class for performing manufacturer search
    /// </summary>
    public class ManufacturerSearch
    {
        public static IQueryable<Manufacturer> Search(
            ManufacturerSearchOptions options,
            IQueryable<Manufacturer> input,
            IUnitOfWork unitOfWork)
        {
            return SearchAlgoV1(options, input.AsNoTracking(), unitOfWork);
        }

        protected static IQueryable<Manufacturer> SearchAlgoV1(ManufacturerSearchOptions options, IQueryable<Manufacturer> input, IUnitOfWork unitOfWork)
        {
            var results = input;

            options.Normalize();

            if (options.Published)
            {
                results = results.Where(a => a.Published);
            }

            if (!string.IsNullOrWhiteSpace(options.RegionId))
            {
                results = results.Where(m => m.RegionIds == null || m.RegionIds == "" || m.RegionIds == options.RegionId || m.RegionIds.StartsWith(options.RegionId + "_") || m.RegionIds.EndsWith("_" + options.RegionId) || m.RegionIds.Contains("_" + options.RegionId + "_")); //do not take product and product lines into account
                //|| m.Products.Any(p => p.RegionIds == null || p.RegionIds == "" || p.RegionIds.StartsWith(options.RegionId + "_") || p.RegionIds.EndsWith("_" + options.RegionId) || p.RegionIds.Contains("_" + options.RegionId + "_")));
                //|| m.ProductLines.Any(p => p.RegionIds == null || p.RegionIds == "" || p.RegionIds.StartsWith(options.RegionId + "_") || p.RegionIds.EndsWith("_" + options.RegionId) || p.RegionIds.Contains("_" + options.RegionId + "_"))); 
            }

            if (!string.IsNullOrWhiteSpace(options.StateId))
            {
                results = results.Where(m => m.StateIds == null || m.StateIds == "" || m.StateIds == options.StateId || m.StateIds.StartsWith(options.StateId + "_") || m.StateIds.EndsWith("_" + options.StateId) || m.StateIds.Contains("_" + options.StateId + "_"));//do not take product and product lines into account
                //|| m.Products.Any(p => p.StateIds == null || p.StateIds == "" || p.StateIds.StartsWith(options.StateId + "_") || p.StateIds.EndsWith("_" + options.StateId) || p.StateIds.Contains("_" + options.StateId + "_"))); 
                //|| m.ProductLines.Any(p => p.StateIds == null || p.StateIds == "" || p.StateIds.StartsWith(options.StateId + "_") || p.StateIds.EndsWith("_" + options.StateId) || p.StateIds.Contains("_" + options.StateId + "_")));
            }

            // if we have high confidence matches return just those, otherwise all
            var cache = SearchCache.Get(unitOfWork);
            var tokens = SearchResults.SplitIntoTokens(NormalizeQueries(options));
            var queryMatchResults = cache.TryMatchWithConfidence(tokens.ToList(), cache.ManufacturerTokens, options.LangCode, QueryTokenType.Manufacturer, tokens.Count);
            var ftsManufacturerIds = new List<int>();
            if (queryMatchResults.Any(m => m.Value >= WeightsTable.ManufacturerBeelineThreshold))
            {
                ftsManufacturerIds.AddRange(queryMatchResults.Where(m => m.Value >= WeightsTable.ManufacturerBeelineThreshold).Select(m => m.Key));
            }
            else
            {
                ftsManufacturerIds.AddRange(queryMatchResults.Keys);
            }

            if (!string.IsNullOrWhiteSpace(options.Query))
            {
                var searchQuery = options.Query.TrimStart('!').Trim().ToLower();

                var ignoredKeywords = new string[] { "free", "revit", "files", "bim", "bimsmith" };

                if (!ignoredKeywords.Contains(searchQuery))
                {
                    // keywords are really unstructured in the database, improve this once they are cleaned up
                    var midKeywordQuery1 = ", " + searchQuery + ",";
                    var midKeywordQuery2 = "," + searchQuery + ",";
                    var startKeywordQuery = searchQuery + ",";
                    var endKeywordQuery1 = ", " + searchQuery + ",";
                    var endKeywordQuery2 = ", " + searchQuery;
                    var endKeywordQuery3 = "," + searchQuery + ",";
                    var endKeywordQuery4 = "," + searchQuery;

                    results = results.Where(a =>
                        a.Name.ToLower() == searchQuery ||
                        a.Name.ToLower().StartsWith(searchQuery + " ") ||
                        ftsManufacturerIds.Contains(a.Id) ||
                        a.Keywords.ToLower().Trim().StartsWith(startKeywordQuery) ||
                        a.Keywords.ToLower().Trim().Contains(midKeywordQuery1) ||
                        a.Keywords.ToLower().Trim().Contains(midKeywordQuery2) ||
                        a.Keywords.ToLower().Trim().EndsWith(endKeywordQuery1) ||
                        a.Keywords.ToLower().Trim().EndsWith(endKeywordQuery2) ||
                        a.Keywords.ToLower().Trim().EndsWith(endKeywordQuery3) ||
                        a.Keywords.ToLower().Trim().EndsWith(endKeywordQuery4));
                }
                else
                {
                    results = results.Where(a =>
                        a.Name.ToLower() == searchQuery ||
                        a.Name.ToLower().StartsWith(searchQuery + " ") ||
                        ftsManufacturerIds.Contains(a.Id));
                }
            }
            else if (!string.IsNullOrWhiteSpace(options.ListQuery))
            {
                var listQuery = options.ListQuery.Trim().ToLower();
                results = results.Where(a => a.Name.StartsWith(listQuery) || ftsManufacturerIds.Contains(a.Id) || a.Id.ToString().Contains(listQuery));
            }
            else if (!string.IsNullOrWhiteSpace(options.ManufacturerName) || !string.IsNullOrWhiteSpace(options.HubVanityURL))
            {
                results = results.Where(m => m.Name.ToLower() == options.ManufacturerName || m.HubVanityURL.ToLower() == options.HubVanityURL);
            }

            if (options.ManufacturerIds != null && options.ManufacturerIds.Any())
            {
                //get manufacturer by Ids (include children manufacturers)
                results = input.Where(e => (options.ManufacturerIds.Contains(e.Id) || options.ManufacturerIds.Contains(e.ParentId.Value)) && e.Published == options.Published); // (show all on admin side) carried over: use original input, i.e. ignore keyword filter?
            }

            return results.OrderByDescending(r => r.Weight).ThenBy(m => m.Id);
        }

        protected static string NormalizeQueries(ManufacturerSearchOptions options)
        {
            var searchQuery = (options.Query ?? string.Empty).TrimStart('!').Trim().ToLower();
            var listQuery = (options.ListQuery ?? string.Empty).TrimStart('!').Trim().ToLower();

            // ignore single character query
            if (searchQuery.Length < 2)
            {
                searchQuery = string.Empty;
            }
            if (listQuery.Length < 2)
            {
                listQuery = string.Empty;
            }

            if (string.IsNullOrWhiteSpace(searchQuery) && !string.IsNullOrWhiteSpace(listQuery))
            {
                searchQuery = listQuery;
            }

            while (searchQuery.Contains("  "))
            {
                searchQuery = searchQuery.Replace("  ", " ");
            }

            if (searchQuery.Any(q => char.IsSymbol(q)))
            {
                // strip (R), (TM) etc. special characters
                searchQuery = new string(searchQuery.Where(c => !char.IsSymbol(c)).ToArray());
            }

            return searchQuery;
        }
    }
}